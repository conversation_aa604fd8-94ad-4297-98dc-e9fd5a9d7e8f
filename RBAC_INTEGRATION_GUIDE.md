# RBAC Integration Guide for IES NextJS Admin Panel

## Overview

This guide shows how to integrate the Role-Based Access Control (RBAC) system into your NextJS admin panel. The RBAC system provides granular permissions for modules and membership types.

## Quick Start

### 1. Basic Setup (Already Done)

The RBAC provider is already added to your main layout:

```tsx
// src/app/layout.tsx
import { RBACProvider } from "@/contexts/rbac-context";

export default function RootLayout({ children }) {
  return (
    <RBACProvider>
      {children}
    </RBACProvider>
  );
}
```

### 2. Basic Usage

```tsx
import { 
  PermissionGuard, 
  CreateButton, 
  useRBACContext,
  MODULES,
  ACTIONS 
} from '@/components/rbac'

export default function MyPage() {
  const { hasPermission } = useRBACContext()

  return (
    <PermissionGuard module="MEMBERSHIP" action="READ">
      <h1>Membership Management</h1>
      
      <CreateButton 
        module="MEMBERSHIP"
        onClick={() => console.log('Create')}
      >
        Add Member
      </CreateButton>
    </PermissionGuard>
  )
}
```

## Available Components

### Permission Guards

#### `PermissionGuard`
Conditionally renders content based on permissions:

```tsx
<PermissionGuard 
  module="MEMBERSHIP" 
  action="READ" 
  membershipTypeId="optional-type-id"
  fallback={<div>Access Denied</div>}
>
  <YourProtectedContent />
</PermissionGuard>
```

#### `ModuleGuard`
Guards entire modules:

```tsx
<ModuleGuard module="FINANCE" fallback={<AccessDenied />}>
  <FinanceContent />
</ModuleGuard>
```

#### `ActionGuard`
Guards specific actions:

```tsx
<ActionGuard module="MEMBERSHIP" action="DELETE">
  <DeleteButton />
</ActionGuard>
```

### Permission Buttons

Pre-built buttons with permission checking:

```tsx
// Automatically disabled if user lacks permission
<CreateButton module="MEMBERSHIP" onClick={handleCreate} />
<EditButton module="MEMBERSHIP" onClick={handleEdit} />
<DeleteButton module="MEMBERSHIP" onClick={handleDelete} />
<ExportButton module="MEMBERSHIP" onClick={handleExport} />
<ApproveButton module="APPLICATION" onClick={handleApprove} />
<ReviewButton module="APPLICATION" onClick={handleReview} />
```

### Membership Type Filtering

**Important**: Membership type filtering only applies to MEMBERSHIP and APPLICATION modules.

```tsx
import { MembershipTypeFilter } from '@/components/rbac'

const [selectedType, setSelectedType] = useState('')

// Only use for MEMBERSHIP and APPLICATION modules
<MembershipTypeFilter
  value={selectedType}
  onValueChange={setSelectedType}
  placeholder="Select membership type"
  module="MEMBERSHIP" // or "APPLICATION"
/>
```

## Hooks

### `useRBACContext`

Main hook for accessing RBAC functionality:

```tsx
const { 
  hasPermission,
  getAccessibleMemberships,
  permissions,
  loading,
  user,
  isAuthenticated,
  isAdmin
} = useRBACContext()

// Check permission programmatically
const canEdit = await hasPermission('MEMBERSHIP', 'UPDATE', membershipTypeId)

// Get accessible membership types
const accessibleTypes = await getAccessibleMemberships()
```

### `usePermissions`

Quick access to permission state:

```tsx
const { permissions, loading } = usePermissions()
```

### `usePermissionCheck`

Access to permission checking functions:

```tsx
const { hasPermission, getCachedPermission } = usePermissionCheck()
```

## Server-Side Usage

For API routes and Server Components:

```tsx
import { checkServerPermission, getServerAccessibleMembershipTypes } from '@/lib/rbac-utils'

// In API route
export async function GET(request: Request) {
  const userId = getUserFromRequest(request)
  
  const canRead = await checkServerPermission(userId, 'MEMBERSHIP', 'READ')
  if (!canRead) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
  }
  
  // Get accessible membership types for filtering
  const accessibleTypes = await getServerAccessibleMembershipTypes(userId, 'MEMBERSHIP')
  
  // Filter data based on accessible types
  const filteredData = data.filter(item => 
    accessibleTypes.includes(item.membership_type)
  )
  
  return NextResponse.json(filteredData)
}
```

## Available Modules and Actions

### Modules
- `MEMBERSHIP` - Member management (✅ Membership type filtering)
- `APPLICATION` - Application processing (✅ Membership type filtering)
- `PROGRAMME` - Programme management
- `FINANCE` - Financial operations
- `SYSTEM` - System administration
- `CALENDAR` - Appointment and scheduling
- `NOTIFICATION` - System notifications
- `BUDGET` - Budget management
- `REPORTS` - Reporting and analytics

### Actions
- `CREATE` - Create new records
- `READ` - View/read records
- `UPDATE` - Edit existing records
- `DELETE` - Remove records
- `REVIEW` - Review submissions
- `APPROVE` - Approve applications/requests
- `EXPORT` - Export data

### Available Roles
- `SUPER_ADMIN` - Full system access with all permissions
- `ADMIN` - Administrative access to specific modules
- `MANAGER` - Departmental management with limited admin functions
- `STAFF` - Operational staff with create/update permissions
- `REVIEWER` - Review and approval permissions for applications

### Important: Membership Type Filtering
**Only MEMBERSHIP and APPLICATION modules** require membership type filtering. All other modules (PROGRAMME, FINANCE, SYSTEM, CALENDAR, etc.) do not filter by membership types.

## Integration Examples

### 1. Updating Existing Pages

Replace your existing pages with RBAC-protected versions:

```tsx
// Before
export default function MembersPage() {
  return (
    <div>
      <h1>Members</h1>
      <button onClick={handleCreate}>Add Member</button>
      <MemberTable />
    </div>
  )
}

// After
import { ModuleGuard, CreateButton } from '@/components/rbac'

export default function MembersPage() {
  return (
    <ModuleGuard module="MEMBERSHIP">
      <h1>Members</h1>
      <CreateButton module="MEMBERSHIP" onClick={handleCreate}>
        Add Member
      </CreateButton>
      <MemberTable />
    </ModuleGuard>
  )
}
```

### 2. Table Row Actions

```tsx
// In your table component
import { EditButton, DeleteButton } from '@/components/rbac'

const MemberTableRow = ({ member }) => (
  <tr>
    <td>{member.name}</td>
    <td>{member.email}</td>
    <td>
      <EditButton 
        module="MEMBERSHIP"
        membershipTypeId={member.membership_type}
        onClick={() => editMember(member.id)}
        size="sm"
      />
      <DeleteButton
        module="MEMBERSHIP" 
        membershipTypeId={member.membership_type}
        onClick={() => deleteMember(member.id)}
        size="sm"
        hideIfNoPermission={true}
      />
    </td>
  </tr>
)
```

### 3. Navigation Menu Integration

```tsx
import { useRBACContext } from '@/contexts/rbac-context'

export const NavigationMenu = () => {
  const { getCachedPermission } = useRBACContext()
  
  return (
    <nav>
      {getCachedPermission('MEMBERSHIP', 'READ') && (
        <NavLink href="/members">Members</NavLink>
      )}
      {getCachedPermission('APPLICATION', 'READ') && (
        <NavLink href="/applications">Applications</NavLink>
      )}
      {getCachedPermission('FINANCE', 'READ') && (
        <NavLink href="/finance">Finance</NavLink>
      )}
    </nav>
  )
}
```

## Error Handling

The RBAC system includes built-in error handling:

```tsx
const { error, loading } = useRBACContext()

if (error) {
  return <div>Error loading permissions: {error}</div>
}

if (loading) {
  return <LoadingSpinner />
}
```

## Performance Considerations

1. **Permission Caching**: Permissions are cached for performance
2. **Lazy Loading**: Permission checks are done on-demand
3. **Server-Side Filtering**: Use server-side functions for data filtering
4. **Batch Checks**: Use `checkMultiplePermissions` for multiple checks

## Troubleshooting

### Common Issues

1. **"useRBACContext must be used within RBACProvider"**
   - Ensure RBACProvider wraps your component tree

2. **Permissions not loading**
   - Check network tab for RPC function calls
   - Verify user authentication
   - Check Supabase connection

3. **Permission checks returning false**
   - Verify user has been assigned roles
   - Check role permissions in database
   - Ensure membership type IDs match

### Debug Mode

Enable debug logging:

```tsx
// Add to your component
useEffect(() => {
  console.log('RBAC Debug:', { permissions, user, loading })
}, [permissions, user, loading])
```

## RBAC Management Pages

For complete RBAC administration, you'll need these management interfaces:

### Critical Admin Pages
1. **User Role Management** (`/rbac/user-roles`) - Assign roles to users
2. **Role Permissions Matrix** (`/rbac/role-permissions`) - Configure role permissions
3. **RBAC Overview Dashboard** (`/rbac/overview`) - System overview and stats
4. **Permission Audit Logs** (`/rbac/audit-logs`) - Track all permission changes

### Integration Examples for Management Pages

```tsx
// User Role Management Page
import { useRBACContext } from '@/contexts/rbac-context'

export default function UserRoleManagement() {
  const { user } = useRBACContext()
  
  return (
    <PermissionGuard module="SYSTEM" action="UPDATE">
      <UserRoleTable />
      <RoleAssignmentForm />
    </PermissionGuard>
  )
}
```

## Next Steps

### Phase 1: Core Protection
1. **Create RBAC management pages** (/rbac/user-roles, /rbac/role-permissions)
2. **Update main pages** with RBAC guards (members, applications, programmes)
3. **Add permission-based table actions** (edit/delete buttons)

### Phase 2: Enhanced Integration  
4. **Add membership type filtering** to MEMBERSHIP and APPLICATION tables
5. **Implement server-side permission checking** in API routes
6. **Add navigation protection** based on permissions

### Phase 3: Advanced Features
7. **Create audit logging interface** for permission changes
8. **Add bulk operations** for role assignments
9. **Test with different user roles** and membership types

## Support

For questions about RBAC integration:
1. Check the database RBAC test files for examples
2. Review the Supabase RPC functions 
3. Test permissions in the database first before frontend integration