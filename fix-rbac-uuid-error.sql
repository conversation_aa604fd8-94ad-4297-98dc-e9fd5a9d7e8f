-- ============================================================================
-- Fix: Update get_system_modules function to return actual module_id
-- Purpose: Fix UUID error when saving permissions
-- Issue: Function was returning module_name as module_id causing "invalid input syntax for type uuid"
-- Date: 2024-12-06
-- ============================================================================

-- Drop the existing function
DROP FUNCTION IF EXISTS access_control.get_system_modules();

-- Recreate the function with correct module_id return
CREATE OR REPLACE FUNCTION access_control.get_system_modules()
RETURNS TABLE (
    module_id TEXT,
    module_name TEXT,
    module_description TEXT,
    module_category TEXT,
    is_active BOOLEAN,
    created_at TEXT,
    updated_at TEXT
)
SECURITY DEFINER
SET search_path = public, access_control
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return modules with field names matching TypeScript interface
    RETURN QUERY
    SELECT 
        m.module_id::TEXT,  -- Return actual module_id instead of module_name
        m.module_name::TEXT,
        m.module_description::TEXT,
        m.module_category::TEXT,
        m.is_active,
        m.created_at::TEXT,
        m.updated_at::TEXT
    FROM access_control.modules m
    WHERE m.is_active = true
    ORDER BY 
        CASE m.module_category
            WHEN 'CORE' THEN 1
            WHEN 'ADMIN' THEN 2
            WHEN 'OPERATIONS' THEN 3
            WHEN 'ANALYTICS' THEN 4
            WHEN 'FINANCE' THEN 5
            ELSE 6
        END,
        m.module_name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION access_control.get_system_modules() TO authenticated;

-- Add function comment
COMMENT ON FUNCTION access_control.get_system_modules() IS 
'Returns all active system modules with field names matching TypeScript interface. Uses SECURITY DEFINER to bypass RLS policies. Fixed to return actual module_id instead of module_name.';