{"name": "ies-nextjs-admin-panel", "version": "1.0.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@shadcn/ui": "^0.0.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.50.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-character-count": "^2.23.0", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-font-family": "^2.23.0", "@tiptap/extension-highlight": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/extension-placeholder": "^2.23.0", "@tiptap/extension-table": "^2.23.0", "@tiptap/extension-table-cell": "^2.23.0", "@tiptap/extension-table-header": "^2.23.0", "@tiptap/extension-table-row": "^2.23.0", "@tiptap/extension-text-align": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/pm": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@types/lodash": "^4.17.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "dotenv": "^17.0.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.451.0", "next": "^14.2.30", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.11", "react-router-dom": "^6.27.0", "recharts": "^3.0.2", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@types/file-saver": "^2.0.7", "@types/jest": "^30.0.0", "@types/jspdf": "^1.3.3", "@types/node": "^20.19.2", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-router-dom": "^5.3.3", "eslint": "^8", "eslint-config-next": "14.2.15", "glob": "^11.0.3", "jest": "^30.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.0", "typescript": "^5.6.3"}}