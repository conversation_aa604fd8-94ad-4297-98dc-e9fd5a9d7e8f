#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to create storage buckets in Supabase
 * Usage: npx tsx scripts/create-storage-bucket.ts
 * 
 * This script can be used to create the 'purchase-order-attachments' bucket
 * if you want to use a separate bucket for purchase order attachments.
 */

import { createClient } from '@supabase/supabase-js';

// You'll need to set these environment variables or update the values here
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createStorageBucket() {
  try {
    // Create the bucket
    const { data, error } = await supabase.storage.createBucket('purchase-order-attachments', {
      public: false, // Set to true if you want public access
      fileSizeLimit: 10485760, // 10MB limit
      allowedMimeTypes: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png'
      ]
    });

    if (error) {
      if (error.message.includes('already exists')) {
        console.log('Bucket "purchase-order-attachments" already exists');
      } else {
        console.error('Error creating bucket:', error);
      }
    } else {
      console.log('Successfully created bucket "purchase-order-attachments"');
      
      // Set up RLS policies for the bucket
      console.log('\nTo set up RLS policies, run these SQL commands in Supabase SQL editor:');
      console.log(`
-- Allow authenticated users to upload files
CREATE POLICY "Allow authenticated uploads" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'purchase-order-attachments');

-- Allow authenticated users to view their own files
CREATE POLICY "Allow authenticated downloads" ON storage.objects
FOR SELECT TO authenticated
USING (bucket_id = 'purchase-order-attachments');

-- Allow authenticated users to delete their own files
CREATE POLICY "Allow authenticated deletes" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'purchase-order-attachments');
      `);
    }

    // List existing buckets
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    if (!listError && buckets) {
      console.log('\nExisting storage buckets:');
      buckets.forEach(bucket => {
        console.log(`- ${bucket.name} (public: ${bucket.public})`);
      });
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createStorageBucket();