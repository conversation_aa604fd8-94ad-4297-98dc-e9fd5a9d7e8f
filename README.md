# IES NextJS Admin Panel

This is a modern admin panel built with [Next.js](https://nextjs.org) 14, utilizing the App Router and Server Components. The project is bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Tech Stack

- **Framework:** Next.js 14 with TypeScript
- **Styling:** Tailwind CSS with shadcn/ui components
- **Database:** Supabase
- **Authentication:** Supabase Auth
- **State Management:** React Hook Form
- **Data Tables:** TanStack Table
- **Form Validation:** Zod
- **Icons:** Lucide React & React Icons
- **UI Components:** Radix UI primitives
- **Date Handling:** date-fns
- **File Management:** JSZip & File Saver

## Project Structure

```
src/
├── app/           # Next.js app router pages and layouts
├── components/    # Reusable UI components
├── config/        # Configuration files
├── data/         # Static data and constants
├── hooks/        # Custom React hooks
├── lib/          # Utility libraries and setup
├── services/     # API and external service integrations
├── types/        # TypeScript type definitions
└── utils/        # Helper functions and utilities
```

## Getting Started

1. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

2. Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build production bundle
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Learn More

To learn more about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs) - Next.js features and API
- [Supabase Documentation](https://supabase.com/docs) - Supabase features and API
- [shadcn/ui](https://ui.shadcn.com/) - Re-usable components built with Radix UI and Tailwind CSS
- [Tailwind CSS](https://tailwindcss.com/docs) - Utility-first CSS framework

## Deployment

The project is configured for deployment on [Vercel](https://vercel.com). Simply connect your repository to Vercel for automatic deployments on every push to the main branch.

For other deployment options, check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying).

## Trigger vercel deployment