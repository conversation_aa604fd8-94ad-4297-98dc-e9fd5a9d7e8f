-- ============================================================================
-- Function: access_control.get_system_modules()
-- Purpose: Returns all system modules with their details
-- Security: SECURITY DEFINER to bypass RLS policies
-- ============================================================================

CREATE OR REPLACE FUNCTION access_control.get_system_modules()
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    category TEXT,
    icon TEXT,
    status TEXT,
    display_order INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
SECURITY DEFINER
SET search_path = public, access_control
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return all modules from the access_control.modules table
    RETURN QUERY
    SELECT 
        m.id,
        m.name,
        m.description,
        m.category,
        m.icon,
        m.status,
        m.display_order,
        m.created_at,
        m.updated_at
    FROM access_control.modules m
    WHERE m.status = 'active'
    ORDER BY 
        CASE m.category
            WHEN 'CORE' THEN 1
            WHEN 'ADMIN' THEN 2
            WHEN 'ANALYTICS' THEN 3
            WHEN 'FINANCE' THEN 4
            ELSE 5
        END,
        m.display_order,
        m.name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION access_control.get_system_modules() TO authenticated;

-- Add function comment
COMMENT ON FUNCTION access_control.get_system_modules() IS 
'Returns all active system modules. Uses SECURITY DEFINER to bypass RLS policies on the modules table.';

-- ============================================================================
-- Function: access_control.get_role_permissions()
-- Purpose: Returns all permissions for a specific role
-- Security: SECURITY DEFINER to bypass RLS policies
-- ============================================================================

CREATE OR REPLACE FUNCTION access_control.get_role_permissions(p_role_id UUID)
RETURNS TABLE (
    id UUID,
    role_id UUID,
    module_id UUID,
    module_name TEXT,
    actions JSONB,
    membership_type_restrictions UUID[],
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
SECURITY DEFINER
SET search_path = public, access_control
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return all permissions for the specified role
    RETURN QUERY
    SELECT 
        mp.id,
        mp.role_id,
        mp.module_id,
        m.name as module_name,
        mp.actions,
        mp.membership_type_restrictions,
        mp.created_at,
        mp.updated_at
    FROM access_control.module_permissions mp
    INNER JOIN access_control.modules m ON m.id = mp.module_id
    WHERE mp.role_id = p_role_id
    ORDER BY m.name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION access_control.get_role_permissions(UUID) TO authenticated;

-- Add function comment
COMMENT ON FUNCTION access_control.get_role_permissions(UUID) IS 
'Returns all module permissions for a specific role. Uses SECURITY DEFINER to bypass RLS policies.';

-- ============================================================================
-- Function: access_control.upsert_role_permission()
-- Purpose: Insert or update a role permission
-- Security: Checks if user has permission to manage roles
-- ============================================================================

CREATE OR REPLACE FUNCTION access_control.upsert_role_permission(
    p_role_id UUID,
    p_module_id UUID,
    p_actions JSONB,
    p_membership_type_restrictions UUID[] DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
SET search_path = public, access_control
LANGUAGE plpgsql
AS $$
DECLARE
    v_permission_id UUID;
    v_user_id UUID;
    v_has_permission BOOLEAN;
BEGIN
    -- Get the current user ID
    v_user_id := auth.uid();
    
    -- Check if user has permission to manage roles (must have SYSTEM module UPDATE permission)
    SELECT EXISTS (
        SELECT 1
        FROM access_control.user_roles ur
        INNER JOIN access_control.module_permissions mp ON mp.role_id = ur.role_id
        INNER JOIN access_control.modules m ON m.id = mp.module_id
        WHERE ur.user_id = v_user_id
        AND m.name = 'SYSTEM'
        AND mp.actions ? 'UPDATE'
    ) INTO v_has_permission;
    
    IF NOT v_has_permission THEN
        RAISE EXCEPTION 'Permission denied: User does not have permission to manage role permissions';
    END IF;
    
    -- Insert or update the permission
    INSERT INTO access_control.module_permissions (
        role_id,
        module_id,
        actions,
        membership_type_restrictions
    ) VALUES (
        p_role_id,
        p_module_id,
        p_actions,
        p_membership_type_restrictions
    )
    ON CONFLICT (role_id, module_id) DO UPDATE
    SET 
        actions = EXCLUDED.actions,
        membership_type_restrictions = EXCLUDED.membership_type_restrictions,
        updated_at = CURRENT_TIMESTAMP
    RETURNING id INTO v_permission_id;
    
    -- Log the action
    INSERT INTO access_control.permission_audit (
        user_id,
        action,
        table_name,
        record_id,
        changes
    ) VALUES (
        v_user_id,
        CASE 
            WHEN EXISTS (SELECT 1 FROM access_control.module_permissions WHERE id = v_permission_id AND created_at < updated_at) 
            THEN 'UPDATE' 
            ELSE 'INSERT' 
        END,
        'module_permissions',
        v_permission_id,
        jsonb_build_object(
            'role_id', p_role_id,
            'module_id', p_module_id,
            'actions', p_actions,
            'membership_type_restrictions', p_membership_type_restrictions
        )
    );
    
    RETURN v_permission_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION access_control.upsert_role_permission(UUID, UUID, JSONB, UUID[]) TO authenticated;

-- Add function comment
COMMENT ON FUNCTION access_control.upsert_role_permission(UUID, UUID, JSONB, UUID[]) IS 
'Insert or update a role permission. Requires SYSTEM module UPDATE permission.';

-- ============================================================================
-- Function: access_control.delete_role_permission()
-- Purpose: Delete a role permission
-- Security: Checks if user has permission to manage roles
-- ============================================================================

CREATE OR REPLACE FUNCTION access_control.delete_role_permission(
    p_role_id UUID,
    p_module_id UUID
)
RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public, access_control
LANGUAGE plpgsql
AS $$
DECLARE
    v_user_id UUID;
    v_has_permission BOOLEAN;
    v_deleted_id UUID;
BEGIN
    -- Get the current user ID
    v_user_id := auth.uid();
    
    -- Check if user has permission to manage roles
    SELECT EXISTS (
        SELECT 1
        FROM access_control.user_roles ur
        INNER JOIN access_control.module_permissions mp ON mp.role_id = ur.role_id
        INNER JOIN access_control.modules m ON m.id = mp.module_id
        WHERE ur.user_id = v_user_id
        AND m.name = 'SYSTEM'
        AND mp.actions ? 'DELETE'
    ) INTO v_has_permission;
    
    IF NOT v_has_permission THEN
        RAISE EXCEPTION 'Permission denied: User does not have permission to delete role permissions';
    END IF;
    
    -- Delete the permission and get its ID for audit
    DELETE FROM access_control.module_permissions
    WHERE role_id = p_role_id AND module_id = p_module_id
    RETURNING id INTO v_deleted_id;
    
    IF v_deleted_id IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Log the action
    INSERT INTO access_control.permission_audit (
        user_id,
        action,
        table_name,
        record_id,
        changes
    ) VALUES (
        v_user_id,
        'DELETE',
        'module_permissions',
        v_deleted_id,
        jsonb_build_object(
            'role_id', p_role_id,
            'module_id', p_module_id
        )
    );
    
    RETURN TRUE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION access_control.delete_role_permission(UUID, UUID) TO authenticated;

-- Add function comment
COMMENT ON FUNCTION access_control.delete_role_permission(UUID, UUID) IS 
'Delete a role permission. Requires SYSTEM module DELETE permission.';