/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: 'http',
                hostname: '127.0.0.1',
                port: '54321',
                pathname: '/storage/v1/object/**'
            },
            {
                protocol: 'https',
                hostname: 'ieqpwmhaqovwnktznpes.supabase.co',
                pathname: '/storage/v1/object/**'
            },
            {
                protocol: 'https',
                hostname: 'phqlqxxuqolpughivcib.supabase.co',
                pathname: '/storage/v1/object/**'
            },
            {
                protocol: 'https',
                hostname: 'cmcduemcksyqkfcfcqfh.supabase.co',
                pathname: '/storage/v1/object/**'
            }
        ]
    },
    eslint: {
        // Warning: This allows production builds to successfully complete even if
        // your project has ESLint errors.
        ignoreDuringBuilds: true,
    }
};

export default nextConfig;
