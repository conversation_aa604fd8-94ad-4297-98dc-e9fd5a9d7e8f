import { RecipientList, MembershipStatus, RecipientFilters } from '@/types/email-blast';
import { mockMembers, getMembersByStatus, getMembersByType, getExpiringMembers, getSubscribedMembers } from './members.mock';

const calculateRecipientCount = (filters: RecipientFilters): number => {
  let filteredMembers = [...mockMembers];
  
  // Filter by membership status
  if (filters.membershipStatus && filters.membershipStatus.length > 0) {
    filteredMembers = filteredMembers.filter(member => 
      filters.membershipStatus!.includes(member.membershipStatus)
    );
  }
  
  // Filter by membership types
  if (filters.membershipTypes && filters.membershipTypes.length > 0) {
    filteredMembers = filteredMembers.filter(member => 
      filters.membershipTypes!.includes(member.membershipType)
    );
  }
  
  // Filter by expiry date range
  if (filters.expiryDateRange) {
    filteredMembers = filteredMembers.filter(member => {
      if (!member.expiryDate) return false;
      const expiryTime = member.expiryDate.getTime();
      const fromTime = filters.expiryDateRange!.from ? filters.expiryDateRange!.from.getTime() : 0;
      const toTime = filters.expiryDateRange!.to ? filters.expiryDateRange!.to.getTime() : Infinity;
      return expiryTime >= fromTime && expiryTime <= toTime;
    });
  }
  
  // Filter by locations
  if (filters.locations && filters.locations.length > 0) {
    filteredMembers = filteredMembers.filter(member => 
      filters.locations!.includes(member.location)
    );
  }
  
  // Filter by age range
  if (filters.ageRange) {
    filteredMembers = filteredMembers.filter(member => {
      const age = member.age;
      const minAge = filters.ageRange!.min || 0;
      const maxAge = filters.ageRange!.max || Infinity;
      return age >= minAge && age <= maxAge;
    });
  }
  
  // Filter by tags
  if (filters.tags && filters.tags.length > 0) {
    filteredMembers = filteredMembers.filter(member => 
      filters.tags!.some(tag => member.tags.includes(tag))
    );
  }
  
  // Exclude unsubscribed
  if (filters.excludeUnsubscribed) {
    filteredMembers = filteredMembers.filter(member => member.isSubscribed);
  }
  
  return filteredMembers.length;
};

const now = new Date();
const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
const sixtyDaysFromNow = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000);
const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

export const mockRecipientLists: RecipientList[] = [
  {
    id: 'list-1',
    name: 'All Active Members',
    description: 'All members with active membership status',
    filters: {
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'admin-user-1',
    tags: ['all-members', 'active']
  },
  {
    id: 'list-2',
    name: 'Fellow and Life Members',
    description: 'Members with Fellow, Honorary Fellow, or Life membership',
    filters: {
      membershipTypes: ['Fellow Member', 'Honorary Fellow', 'Life Member', 'LIFE MEMBER (Fellow)'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipTypes: ['Fellow Member', 'Honorary Fellow', 'Life Member', 'LIFE MEMBER (Fellow)'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-02-20'),
    updatedAt: new Date('2024-02-20'),
    createdBy: 'admin-user-1',
    tags: ['premium', 'high-value']
  },
  {
    id: 'list-3',
    name: 'Expiring Soon (30 Days)',
    description: 'Active members whose membership expires within 30 days',
    filters: {
      membershipStatus: [MembershipStatus.ACTIVE],
      expiryDateRange: {
        from: now,
        to: thirtyDaysFromNow
      },
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipStatus: [MembershipStatus.ACTIVE],
      expiryDateRange: {
        from: now,
        to: thirtyDaysFromNow
      },
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-03-10'),
    updatedAt: new Date(),
    createdBy: 'admin-user-2',
    tags: ['renewal', 'urgent']
  },
  {
    id: 'list-4',
    name: 'Recently Expired Members',
    description: 'Members whose membership expired in the last 90 days',
    filters: {
      membershipStatus: [MembershipStatus.EXPIRED],
      expiryDateRange: {
        from: ninetyDaysAgo,
        to: now
      },
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipStatus: [MembershipStatus.EXPIRED],
      expiryDateRange: {
        from: ninetyDaysAgo,
        to: now
      },
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-04-05'),
    updatedAt: new Date(),
    createdBy: 'admin-user-2',
    tags: ['win-back', 'expired']
  },
  {
    id: 'list-5',
    name: 'New York Members',
    description: 'All members located in New York',
    filters: {
      locations: ['New York'],
      membershipStatus: [MembershipStatus.ACTIVE, MembershipStatus.PENDING],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      locations: ['New York'],
      membershipStatus: [MembershipStatus.ACTIVE, MembershipStatus.PENDING],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-05-12'),
    updatedAt: new Date('2024-01-10'),
    createdBy: 'admin-user-3',
    tags: ['location-based', 'new-york']
  },
  {
    id: 'list-6',
    name: 'Senior Members (60+)',
    description: 'Members aged 60 and above',
    filters: {
      ageRange: { min: 60 },
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      ageRange: { min: 60 },
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-06-18'),
    updatedAt: new Date('2024-02-01'),
    createdBy: 'admin-user-3',
    tags: ['demographics', 'seniors']
  },
  {
    id: 'list-7',
    name: 'Young Professionals (25-40)',
    description: 'Active members aged between 25 and 40',
    filters: {
      ageRange: { min: 25, max: 40 },
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      ageRange: { min: 25, max: 40 },
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-07-22'),
    updatedAt: new Date('2024-02-15'),
    createdBy: 'admin-user-1',
    tags: ['demographics', 'young-professionals']
  },
  {
    id: 'list-8',
    name: 'Event Attendees',
    description: 'Members who have attended events',
    filters: {
      tags: ['event-attendee'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      tags: ['event-attendee'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-08-14'),
    updatedAt: new Date(),
    createdBy: 'admin-user-2',
    tags: ['engagement', 'events']
  },
  {
    id: 'list-9',
    name: 'Life Members and Major Donors',
    description: 'Life members and high-value donors',
    filters: {
      tags: ['donor', 'high-value'],
      membershipTypes: ['Life Member', 'LIFE MEMBER (Fellow)', 'Honorary Fellow'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      tags: ['donor', 'high-value'],
      membershipTypes: ['Life Member', 'LIFE MEMBER (Fellow)', 'Honorary Fellow'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-09-05'),
    updatedAt: new Date('2024-01-25'),
    createdBy: 'admin-user-1',
    tags: ['vip', 'donors']
  },
  {
    id: 'list-10',
    name: 'Newsletter Subscribers Only',
    description: 'All subscribed members regardless of status',
    filters: {
      excludeUnsubscribed: true
    },
    recipientCount: getSubscribedMembers().length,
    isStatic: false,
    createdAt: new Date('2023-10-01'),
    updatedAt: new Date(),
    createdBy: 'admin-user-3',
    tags: ['newsletter', 'all-subscribers']
  },
  {
    id: 'list-11',
    name: 'At-Risk Members',
    description: 'Members tagged as at-risk or with expired membership',
    filters: {
      tags: ['at-risk'],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      tags: ['at-risk'],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-11-12'),
    updatedAt: new Date(),
    createdBy: 'admin-user-2',
    tags: ['retention', 'at-risk']
  },
  {
    id: 'list-12',
    name: 'Board Members & Staff',
    description: 'Static list of board members and key staff',
    filters: {},
    recipientCount: 25,
    isStatic: true,
    staticRecipientIds: Array.from({ length: 25 }, (_, i) => `member-${i + 1}`),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-01'),
    createdBy: 'admin-user-1',
    tags: ['internal', 'board', 'staff']
  },
  {
    id: 'list-13',
    name: 'California Members',
    description: 'Members from Los Angeles, San Diego, San Jose, and Sacramento',
    filters: {
      locations: ['Los Angeles', 'San Diego', 'San Jose', 'Sacramento'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      locations: ['Los Angeles', 'San Diego', 'San Jose', 'Sacramento'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2023-12-20'),
    updatedAt: new Date(),
    createdBy: 'admin-user-3',
    tags: ['location-based', 'california']
  },
  {
    id: 'list-14',
    name: 'New Members (Joined Last 30 Days)',
    description: 'Members who joined in the last 30 days',
    filters: {
      registrationDateRange: {
        from: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        to: now
      },
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      registrationDateRange: {
        from: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        to: now
      },
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date(),
    createdBy: 'admin-user-1',
    tags: ['new-members', 'onboarding']
  },
  {
    id: 'list-15',
    name: 'Engaged Members',
    description: 'Members with high engagement scores',
    filters: {
      tags: ['engaged', 'frequent-buyer'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      tags: ['engaged', 'frequent-buyer'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date(),
    createdBy: 'admin-user-2',
    tags: ['engagement', 'loyalty']
  },
  {
    id: 'list-16',
    name: 'Student Members',
    description: 'All student members currently enrolled',
    filters: {
      membershipTypes: ['Student Member'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipTypes: ['Student Member'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date(),
    createdBy: 'admin-user-3',
    tags: ['students', 'education']
  },
  {
    id: 'list-17',
    name: 'Members by Age Group',
    description: 'Members categorized by age-based membership types',
    filters: {
      membershipTypes: ['Member above 31', 'Member below 30'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipTypes: ['Member above 31', 'Member below 30'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2024-02-05'),
    updatedAt: new Date(),
    createdBy: 'admin-user-1',
    tags: ['age-based', 'demographics']
  },
  {
    id: 'list-18',
    name: 'Organisational Members',
    description: 'Corporate and organizational memberships',
    filters: {
      membershipTypes: ['Organisational Member'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    },
    recipientCount: calculateRecipientCount({
      membershipTypes: ['Organisational Member'],
      membershipStatus: [MembershipStatus.ACTIVE],
      excludeUnsubscribed: true
    }),
    isStatic: false,
    createdAt: new Date('2024-01-30'),
    updatedAt: new Date(),
    createdBy: 'admin-user-2',
    tags: ['corporate', 'b2b']
  }
];

// Export utility functions
export const getRecipientListById = (id: string): RecipientList | undefined => {
  return mockRecipientLists.find(list => list.id === id);
};

export const getRecipientListsByTag = (tag: string): RecipientList[] => {
  return mockRecipientLists.filter(list => list.tags?.includes(tag));
};

export const getStaticLists = (): RecipientList[] => {
  return mockRecipientLists.filter(list => list.isStatic);
};

export const getDynamicLists = (): RecipientList[] => {
  return mockRecipientLists.filter(list => !list.isStatic);
};