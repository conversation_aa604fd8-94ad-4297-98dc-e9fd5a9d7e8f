import { 
  EmailTemplate, 
  TemplateCategory, 
  TemplateStatus, 
  TemplateVariable,
  TemplateUsageStats 
} from '@/types/email-blast/email-template.types';

// Common template variables
const commonVariables: TemplateVariable[] = [
  {
    id: 'var-1',
    name: 'First Name',
    key: 'firstName',
    description: 'Recipient\'s first name',
    type: 'text',
    required: true,
    defaultValue: 'Valued Customer',
    placeholder: 'Enter first name'
  },
  {
    id: 'var-2', 
    name: 'Last Name',
    key: 'lastName',
    description: 'Recipient\'s last name',
    type: 'text',
    required: false,
    placeholder: 'Enter last name'
  },
  {
    id: 'var-3',
    name: 'Company Name',
    key: 'companyName',
    description: 'Company or organization name',
    type: 'text',
    required: false,
    placeholder: 'Enter company name'
  },
  {
    id: 'var-4',
    name: 'Email Address',
    key: 'email',
    description: 'Recipient\'s email address',
    type: 'email',
    required: true,
    placeholder: '<EMAIL>'
  }
];

const eventVariables: TemplateVariable[] = [
  ...commonVariables,
  {
    id: 'var-5',
    name: 'Event Name',
    key: 'eventName',
    description: 'Name of the event',
    type: 'text',
    required: true,
    placeholder: 'Enter event name'
  },
  {
    id: 'var-6',
    name: 'Event Date',
    key: 'eventDate',
    description: 'Date of the event',
    type: 'date',
    required: true,
    placeholder: 'Select event date'
  },
  {
    id: 'var-7',
    name: 'Event Location',
    key: 'eventLocation',
    description: 'Location or venue of the event',
    type: 'text',
    required: false,
    placeholder: 'Enter location'
  }
];

const promotionalVariables: TemplateVariable[] = [
  ...commonVariables,
  {
    id: 'var-8',
    name: 'Discount Percentage',
    key: 'discountPercent',
    description: 'Discount percentage for the offer',
    type: 'number',
    required: true,
    defaultValue: '10',
    validation: {
      minLength: 1,
      maxLength: 2
    }
  },
  {
    id: 'var-9',
    name: 'Offer Valid Until',
    key: 'offerValidUntil',
    description: 'Expiration date of the offer',
    type: 'date',
    required: true
  },
  {
    id: 'var-10',
    name: 'Promo Code',
    key: 'promoCode',
    description: 'Promotional code for the discount',
    type: 'text',
    required: false,
    placeholder: 'SAVE20'
  }
];

// Mock usage statistics
const createMockUsageStats = (templateId: string): TemplateUsageStats => ({
  templateId,
  totalUsage: Math.floor(Math.random() * 100) + 10,
  campaignsUsed: Math.floor(Math.random() * 20) + 3,
  lastUsedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  successRate: Math.floor(Math.random() * 30) + 70,
  avgOpenRate: Math.floor(Math.random() * 40) + 15,
  avgClickRate: Math.floor(Math.random() * 15) + 3,
  popularVariables: ['firstName', 'companyName', 'email'],
  monthlyUsage: [
    { month: '2024-01', count: Math.floor(Math.random() * 20) + 5 },
    { month: '2024-02', count: Math.floor(Math.random() * 20) + 5 },
    { month: '2024-03', count: Math.floor(Math.random() * 20) + 5 },
    { month: '2024-04', count: Math.floor(Math.random() * 20) + 5 },
    { month: '2024-05', count: Math.floor(Math.random() * 20) + 5 },
    { month: '2024-06', count: Math.floor(Math.random() * 20) + 5 }
  ]
});

export const mockEmailTemplates: EmailTemplate[] = [
  {
    id: 'template-1',
    name: 'Welcome Email - New Customer',
    description: 'A warm welcome email for new customers joining our platform',
    category: TemplateCategory.WELCOME,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: 'Welcome to {{companyName}}, {{firstName}}!',
      body: `Hi {{firstName}},

Welcome to {{companyName}}! We're thrilled to have you on board.

Here's what you can expect:
• Personalized service tailored to your needs
• 24/7 customer support
• Exclusive member benefits

Get started by logging into your account and exploring our features.

Best regards,
The {{companyName}} Team`,
      bodyHtml: `<html><body>
<h1>Welcome to {{companyName}}, {{firstName}}!</h1>
<p>We're thrilled to have you on board.</p>
<h3>Here's what you can expect:</h3>
<ul>
<li>Personalized service tailored to your needs</li>
<li>24/7 customer support</li>
<li>Exclusive member benefits</li>
</ul>
<p>Get started by logging into your account and exploring our features.</p>
<p>Best regards,<br/>The {{companyName}} Team</p>
</body></html>`,
      previewText: 'Welcome to our platform! Here\'s what you can expect...'
    },
    variables: commonVariables,
    thumbnail: '/templates/thumbnails/welcome-new-customer.png',
    tags: ['welcome', 'onboarding', 'new-customer'],
    isSystem: true,
    version: 1,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-20'),
    createdBy: 'system',
    updatedBy: '<EMAIL>',
    usageStats: createMockUsageStats('template-1')
  },
  {
    id: 'template-2',
    name: 'Monthly Newsletter Template',
    description: 'Professional newsletter template for monthly updates',
    category: TemplateCategory.NEWSLETTER,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: '{{companyName}} Monthly Newsletter - {{monthYear}}',
      body: `Hi {{firstName}},

Here's your monthly update from {{companyName}}.

## This Month's Highlights
• Feature updates and improvements
• Customer success stories
• Upcoming events and webinars

## What's New
[Add your monthly content here]

Stay tuned for more updates!

Best regards,
{{companyName}} Team`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto;">
<h1 style="color: #333;">{{companyName}} Monthly Newsletter</h1>
<p>Hi {{firstName}},</p>
<p>Here's your monthly update from {{companyName}}.</p>
<h2 style="color: #0066cc;">This Month's Highlights</h2>
<ul>
<li>Feature updates and improvements</li>
<li>Customer success stories</li>
<li>Upcoming events and webinars</li>
</ul>
<h2 style="color: #0066cc;">What's New</h2>
<p>[Add your monthly content here]</p>
<p>Stay tuned for more updates!</p>
<p>Best regards,<br/>{{companyName}} Team</p>
</div>
</body></html>`,
      previewText: 'Your monthly update is here with highlights and new features...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-newsletter-1',
        name: 'Month Year',
        key: 'monthYear',
        description: 'Current month and year',
        type: 'text',
        required: true,
        defaultValue: new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
        placeholder: 'January 2024'
      }
    ],
    thumbnail: '/templates/thumbnails/monthly-newsletter.png',
    tags: ['newsletter', 'monthly', 'updates'],
    isSystem: true,
    version: 3,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-03-15'),
    createdBy: 'system',
    updatedBy: '<EMAIL>',
    usageStats: createMockUsageStats('template-2')
  },
  {
    id: 'template-3',
    name: 'Flash Sale Promotion',
    description: 'Urgent promotional email template for flash sales',
    category: TemplateCategory.PROMOTIONAL,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: '🔥 Flash Sale: {{discountPercent}}% OFF Everything - Limited Time!',
      body: `Hi {{firstName}},

🚨 FLASH SALE ALERT! 🚨

Get {{discountPercent}}% OFF everything in our store!

⏰ This offer expires on {{offerValidUntil}}
🎯 Use code: {{promoCode}}
💰 No minimum purchase required

Don't miss out on this incredible deal!

Shop now: [Shop Link]

Hurry, sale ends soon!

Best regards,
{{companyName}} Team`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif; background-color: #ff4444; color: white;">
<div style="max-width: 600px; margin: 0 auto; padding: 20px;">
<h1 style="text-align: center; font-size: 32px;">🔥 FLASH SALE 🔥</h1>
<h2 style="text-align: center; color: #ffff00;">{{discountPercent}}% OFF EVERYTHING!</h2>
<p style="font-size: 18px;">Hi {{firstName}},</p>
<div style="background-color: white; color: #333; padding: 20px; border-radius: 10px; margin: 20px 0;">
<ul style="font-size: 16px;">
<li>⏰ Expires: {{offerValidUntil}}</li>
<li>🎯 Code: <strong>{{promoCode}}</strong></li>
<li>💰 No minimum purchase</li>
</ul>
</div>
<div style="text-align: center; margin: 30px 0;">
<a href="#" style="background-color: #ffff00; color: #333; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 18px;">SHOP NOW</a>
</div>
<p style="text-align: center; font-size: 14px;">Hurry, sale ends soon!</p>
</div>
</body></html>`,
      previewText: 'Flash Sale Alert! Get huge discounts on everything...'
    },
    variables: promotionalVariables,
    thumbnail: '/templates/thumbnails/flash-sale.png',
    tags: ['promotion', 'sale', 'discount', 'urgent'],
    isSystem: true,
    version: 2,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-04-10'),
    createdBy: 'system',
    updatedBy: '<EMAIL>',
    usageStats: createMockUsageStats('template-3')
  },
  {
    id: 'template-4',
    name: 'Event Invitation',
    description: 'Professional event invitation template',
    category: TemplateCategory.EVENT,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: 'You\'re Invited: {{eventName}} - {{eventDate}}',
      body: `Dear {{firstName}},

You're cordially invited to {{eventName}}!

📅 Date: {{eventDate}}
📍 Location: {{eventLocation}}
🕒 Time: [Event Time]

This exclusive event will feature:
• Industry expert speakers
• Networking opportunities
• Refreshments and door prizes

RSVP by [RSVP Date] to secure your spot.

We look forward to seeing you there!

Best regards,
{{companyName}} Team`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto; border: 2px solid #0066cc; border-radius: 10px; padding: 30px;">
<h1 style="color: #0066cc; text-align: center;">You're Invited!</h1>
<h2 style="text-align: center; color: #333;">{{eventName}}</h2>
<p>Dear {{firstName}},</p>
<div style="background-color: #f0f8ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
<p><strong>📅 Date:</strong> {{eventDate}}</p>
<p><strong>📍 Location:</strong> {{eventLocation}}</p>
<p><strong>🕒 Time:</strong> [Event Time]</p>
</div>
<h3 style="color: #0066cc;">This exclusive event will feature:</h3>
<ul>
<li>Industry expert speakers</li>
<li>Networking opportunities</li>
<li>Refreshments and door prizes</li>
</ul>
<div style="text-align: center; margin: 30px 0;">
<a href="#" style="background-color: #0066cc; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">RSVP NOW</a>
</div>
<p>We look forward to seeing you there!</p>
<p>Best regards,<br/>{{companyName}} Team</p>
</div>
</body></html>`,
      previewText: 'You\'re invited to our exclusive event! Join us for...'
    },
    variables: eventVariables,
    thumbnail: '/templates/thumbnails/event-invitation.png',
    tags: ['event', 'invitation', 'networking'],
    isSystem: true,
    version: 1,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    createdBy: 'system',
    updatedBy: 'system',
    usageStats: createMockUsageStats('template-4')
  },
  {
    id: 'template-5',
    name: 'Follow-up After Meeting',
    description: 'Professional follow-up email after business meetings',
    category: TemplateCategory.FOLLOWUP,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: 'Following up on our meeting - {{meetingDate}}',
      body: `Hi {{firstName}},

Thank you for taking the time to meet with us on {{meetingDate}}.

I wanted to follow up on our discussion about:
• [Key point 1]
• [Key point 2]
• [Key point 3]

Next Steps:
• [Action item 1]
• [Action item 2]

Please let me know if you have any questions or if there's anything else I can help you with.

Looking forward to our continued collaboration.

Best regards,
{{senderName}}
{{companyName}}`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto;">
<p>Hi {{firstName}},</p>
<p>Thank you for taking the time to meet with us on {{meetingDate}}.</p>
<h3 style="color: #0066cc;">Discussion Points:</h3>
<ul>
<li>[Key point 1]</li>
<li>[Key point 2]</li>
<li>[Key point 3]</li>
</ul>
<h3 style="color: #0066cc;">Next Steps:</h3>
<ul>
<li>[Action item 1]</li>
<li>[Action item 2]</li>
</ul>
<p>Please let me know if you have any questions or if there's anything else I can help you with.</p>
<p>Looking forward to our continued collaboration.</p>
<p>Best regards,<br/>{{senderName}}<br/>{{companyName}}</p>
</div>
</body></html>`,
      previewText: 'Thank you for taking the time to meet with us...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-followup-1',
        name: 'Meeting Date',
        key: 'meetingDate',
        description: 'Date of the meeting',
        type: 'date',
        required: true
      },
      {
        id: 'var-followup-2',
        name: 'Sender Name',
        key: 'senderName',
        description: 'Name of the person sending the follow-up',
        type: 'text',
        required: true,
        placeholder: 'Your name'
      }
    ],
    thumbnail: '/templates/thumbnails/follow-up-meeting.png',
    tags: ['follow-up', 'meeting', 'business'],
    isSystem: false,
    version: 1,
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2024-02-15'),
    createdBy: '<EMAIL>',
    updatedBy: '<EMAIL>',
    usageStats: createMockUsageStats('template-5')
  },
  {
    id: 'template-6',
    name: 'System Notification',
    description: 'Template for system-generated notifications',
    category: TemplateCategory.NOTIFICATION,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: '{{notificationType}}: {{systemName}} Alert',
      body: `Hello {{firstName}},

This is an automated notification from {{systemName}}.

{{notificationMessage}}

Notification Details:
• Type: {{notificationType}}
• Time: {{timestamp}}
• Severity: {{severity}}

If this requires immediate attention, please contact our support team.

This is an automated message. Please do not reply to this email.

Best regards,
{{systemName}} Team`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto; border-left: 4px solid #ff9800; padding-left: 20px;">
<h2 style="color: #ff9800;">{{systemName}} Alert</h2>
<p>Hello {{firstName}},</p>
<p>This is an automated notification from {{systemName}}.</p>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
<p><strong>{{notificationMessage}}</strong></p>
</div>
<h3>Notification Details:</h3>
<ul>
<li><strong>Type:</strong> {{notificationType}}</li>
<li><strong>Time:</strong> {{timestamp}}</li>
<li><strong>Severity:</strong> {{severity}}</li>
</ul>
<p>If this requires immediate attention, please contact our support team.</p>
<p style="font-size: 12px; color: #666;">This is an automated message. Please do not reply to this email.</p>
</div>
</body></html>`,
      previewText: 'System notification alert - please review...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-notif-1',
        name: 'Notification Type',
        key: 'notificationType',
        description: 'Type of notification',
        type: 'text',
        required: true,
        validation: {
          options: ['Alert', 'Warning', 'Info', 'Error']
        }
      },
      {
        id: 'var-notif-2',
        name: 'System Name',
        key: 'systemName',
        description: 'Name of the system generating the notification',
        type: 'text',
        required: true,
        defaultValue: 'IES System'
      },
      {
        id: 'var-notif-3',
        name: 'Notification Message',
        key: 'notificationMessage',
        description: 'Main notification message',
        type: 'text',
        required: true
      },
      {
        id: 'var-notif-4',
        name: 'Timestamp',
        key: 'timestamp',
        description: 'When the notification was generated',
        type: 'date',
        required: true,
        defaultValue: new Date().toLocaleString()
      },
      {
        id: 'var-notif-5',
        name: 'Severity',
        key: 'severity',
        description: 'Severity level of the notification',
        type: 'text',
        required: true,
        validation: {
          options: ['Low', 'Medium', 'High', 'Critical']
        }
      }
    ],
    thumbnail: '/templates/thumbnails/system-notification.png',
    tags: ['system', 'notification', 'alert', 'automated'],
    isSystem: true,
    version: 1,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05'),
    createdBy: 'system',
    updatedBy: 'system',
    usageStats: createMockUsageStats('template-6')
  },
  {
    id: 'template-7',
    name: 'Customer Survey Request',
    description: 'Template for requesting customer feedback surveys',
    category: TemplateCategory.SURVEY,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: 'We\'d love your feedback, {{firstName}}!',
      body: `Hi {{firstName}},

Hope you're having a great day!

We're always looking to improve our services, and your opinion matters to us.

Would you mind taking a few minutes to share your thoughts in our quick survey?

Survey Details:
• Takes only 3-5 minutes to complete
• Your responses are completely anonymous
• Helps us serve you better

[Survey Link]

As a thank you, you'll be entered into our monthly drawing for a {{incentive}}.

Thank you for helping us improve!

Best regards,
{{companyName}} Team`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto;">
<h2 style="color: #0066cc;">We'd love your feedback!</h2>
<p>Hi {{firstName}},</p>
<p>Hope you're having a great day!</p>
<p>We're always looking to improve our services, and your opinion matters to us.</p>
<p>Would you mind taking a few minutes to share your thoughts in our quick survey?</p>
<div style="background-color: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
<h3>Survey Details:</h3>
<ul>
<li>✅ Takes only 3-5 minutes to complete</li>
<li>🔒 Your responses are completely anonymous</li>
<li>📈 Helps us serve you better</li>
</ul>
</div>
<div style="text-align: center; margin: 30px 0;">
<a href="#" style="background-color: #0066cc; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">TAKE SURVEY</a>
</div>
<p>As a thank you, you'll be entered into our monthly drawing for a {{incentive}}.</p>
<p>Thank you for helping us improve!</p>
<p>Best regards,<br/>{{companyName}} Team</p>
</div>
</body></html>`,
      previewText: 'We\'d love your feedback! Take our quick survey...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-survey-1',
        name: 'Incentive',
        key: 'incentive',
        description: 'Incentive offered for completing the survey',
        type: 'text',
        required: false,
        defaultValue: '$50 gift card',
        placeholder: 'Gift card, discount, etc.'
      }
    ],
    thumbnail: '/templates/thumbnails/customer-survey.png',
    tags: ['survey', 'feedback', 'customer-satisfaction'],
    isSystem: true,
    version: 1,
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25'),
    createdBy: 'system',
    updatedBy: 'system',
    usageStats: createMockUsageStats('template-7')
  },
  {
    id: 'template-8',
    name: 'Product Launch Announcement',
    description: 'Template for announcing new product launches',
    category: TemplateCategory.ANNOUNCEMENT,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: '🚀 Introducing {{productName}} - Available Now!',
      body: `Hi {{firstName}},

Exciting news! We're thrilled to announce the launch of {{productName}}.

{{productDescription}}

Key Features:
• {{feature1}}
• {{feature2}}
• {{feature3}}

Special Launch Offer:
Get {{launchDiscount}}% off for the first {{offerDuration}} with code {{launchCode}}.

[Learn More] [Get Started]

Don't miss out on this limited-time offer!

Best regards,
{{companyName}} Team`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto;">
<h1 style="color: #0066cc; text-align: center;">🚀 New Product Launch!</h1>
<h2 style="text-align: center; color: #333;">{{productName}}</h2>
<p>Hi {{firstName}},</p>
<p>Exciting news! We're thrilled to announce the launch of {{productName}}.</p>
<div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
<p><strong>{{productDescription}}</strong></p>
</div>
<h3 style="color: #0066cc;">Key Features:</h3>
<ul>
<li>{{feature1}}</li>
<li>{{feature2}}</li>
<li>{{feature3}}</li>
</ul>
<div style="background-color: #e8f5e8; border: 2px solid #4caf50; padding: 20px; border-radius: 10px; margin: 20px 0;">
<h3 style="color: #2e7d32;">Special Launch Offer:</h3>
<p>Get <strong>{{launchDiscount}}%</strong> off for the first <strong>{{offerDuration}}</strong> with code <strong>{{launchCode}}</strong>.</p>
</div>
<div style="text-align: center; margin: 30px 0;">
<a href="#" style="background-color: #0066cc; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 0 10px;">LEARN MORE</a>
<a href="#" style="background-color: #4caf50; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 0 10px;">GET STARTED</a>
</div>
<p>Don't miss out on this limited-time offer!</p>
<p>Best regards,<br/>{{companyName}} Team</p>
</div>
</body></html>`,
      previewText: 'Exciting news! We\'re launching our new product...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-product-1',
        name: 'Product Name',
        key: 'productName',
        description: 'Name of the new product',
        type: 'text',
        required: true,
        placeholder: 'Enter product name'
      },
      {
        id: 'var-product-2',
        name: 'Product Description',
        key: 'productDescription',
        description: 'Brief description of the product',
        type: 'text',
        required: true,
        placeholder: 'Describe the product'
      },
      {
        id: 'var-product-3',
        name: 'Feature 1',
        key: 'feature1',
        description: 'First key feature',
        type: 'text',
        required: true
      },
      {
        id: 'var-product-4',
        name: 'Feature 2',
        key: 'feature2',
        description: 'Second key feature',
        type: 'text',
        required: true
      },
      {
        id: 'var-product-5',
        name: 'Feature 3',
        key: 'feature3',
        description: 'Third key feature',
        type: 'text',
        required: true
      },
      {
        id: 'var-product-6',
        name: 'Launch Discount',
        key: 'launchDiscount',
        description: 'Launch discount percentage',
        type: 'number',
        required: false,
        defaultValue: '20'
      },
      {
        id: 'var-product-7',
        name: 'Offer Duration',
        key: 'offerDuration',
        description: 'How long the launch offer lasts',
        type: 'text',
        required: false,
        defaultValue: '30 days'
      },
      {
        id: 'var-product-8',
        name: 'Launch Code',
        key: 'launchCode',
        description: 'Promotional code for launch discount',
        type: 'text',
        required: false,
        defaultValue: 'LAUNCH2024'
      }
    ],
    thumbnail: '/templates/thumbnails/product-launch.png',
    tags: ['product-launch', 'announcement', 'new-feature'],
    isSystem: false,
    version: 1,
    createdAt: new Date('2024-03-01'),
    updatedAt: new Date('2024-03-01'),
    createdBy: '<EMAIL>',
    updatedBy: '<EMAIL>',
    usageStats: createMockUsageStats('template-8')
  },
  {
    id: 'template-9',
    name: 'Thank You Note',
    description: 'Professional thank you email template',
    category: TemplateCategory.FOLLOWUP,
    status: TemplateStatus.ACTIVE,
    content: {
      subject: 'Thank you, {{firstName}}!',
      body: `Dear {{firstName}},

Thank you so much for {{reasonForThanks}}.

Your {{contribution}} means a lot to us and helps us continue to {{impact}}.

We truly appreciate your support and look forward to {{futureEngagement}}.

With gratitude,
{{senderName}}
{{companyName}}`,
      bodyHtml: `<html><body style="font-family: Arial, sans-serif;">
<div style="max-width: 600px; margin: 0 auto; text-align: center;">
<h1 style="color: #0066cc; font-size: 36px;">Thank You!</h1>
<p style="font-size: 18px;">Dear {{firstName}},</p>
<p style="font-size: 16px; line-height: 1.6;">Thank you so much for {{reasonForThanks}}.</p>
<div style="background-color: #f0f8ff; padding: 30px; border-radius: 10px; margin: 30px 0;">
<p style="font-size: 16px; line-height: 1.6;">Your {{contribution}} means a lot to us and helps us continue to {{impact}}.</p>
</div>
<p style="font-size: 16px; line-height: 1.6;">We truly appreciate your support and look forward to {{futureEngagement}}.</p>
<p style="margin-top: 40px;">With gratitude,<br/>{{senderName}}<br/>{{companyName}}</p>
</div>
</body></html>`,
      previewText: 'Thank you so much for your support and contribution...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-thanks-1',
        name: 'Reason for Thanks',
        key: 'reasonForThanks',
        description: 'What you\'re thanking them for',
        type: 'text',
        required: true,
        placeholder: 'your recent purchase'
      },
      {
        id: 'var-thanks-2',
        name: 'Contribution',
        key: 'contribution',
        description: 'Their specific contribution',
        type: 'text',
        required: true,
        placeholder: 'support, business, feedback'
      },
      {
        id: 'var-thanks-3',
        name: 'Impact',
        key: 'impact',
        description: 'How their contribution helps',
        type: 'text',
        required: true,
        placeholder: 'improve our services'
      },
      {
        id: 'var-thanks-4',
        name: 'Future Engagement',
        key: 'futureEngagement',
        description: 'What you look forward to',
        type: 'text',
        required: true,
        placeholder: 'working with you again'
      },
      {
        id: 'var-thanks-5',
        name: 'Sender Name',
        key: 'senderName',
        description: 'Name of the person sending thanks',
        type: 'text',
        required: true,
        placeholder: 'Your name'
      }
    ],
    thumbnail: '/templates/thumbnails/thank-you.png',
    tags: ['thank-you', 'appreciation', 'gratitude'],
    isSystem: true,
    version: 1,
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-02-10'),
    createdBy: 'system',
    updatedBy: 'system',
    usageStats: createMockUsageStats('template-9')
  },
  {
    id: 'template-10',
    name: 'Custom HTML Template',
    description: 'Advanced HTML template for custom designs',
    category: TemplateCategory.CUSTOM,
    status: TemplateStatus.DRAFT,
    content: {
      subject: '{{customSubject}}',
      body: `Hi {{firstName}},

This is a custom template that can be fully customized to meet your specific needs.

{{customContent}}

Best regards,
{{companyName}}`,
      bodyHtml: `<html>
<head>
<style>
.container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
.content { padding: 30px; background-color: #ffffff; }
.footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
</style>
</head>
<body>
<div class="container">
<div class="header">
<h1>{{headerTitle}}</h1>
</div>
<div class="content">
<p>Hi {{firstName}},</p>
<div>{{customContent}}</div>
</div>
<div class="footer">
<p>{{companyName}} | {{contactInfo}}</p>
</div>
</div>
</body>
</html>`,
      previewText: 'Custom template with your personalized content...'
    },
    variables: [
      ...commonVariables,
      {
        id: 'var-custom-1',
        name: 'Custom Subject',
        key: 'customSubject',
        description: 'Custom email subject line',
        type: 'text',
        required: true,
        placeholder: 'Enter custom subject'
      },
      {
        id: 'var-custom-2',
        name: 'Header Title',
        key: 'headerTitle',
        description: 'Title in the header section',
        type: 'text',
        required: true,
        placeholder: 'Enter header title'
      },
      {
        id: 'var-custom-3',
        name: 'Custom Content',
        key: 'customContent',
        description: 'Main content area',
        type: 'text',
        required: true,
        placeholder: 'Enter your custom content'
      },
      {
        id: 'var-custom-4',
        name: 'Contact Info',
        key: 'contactInfo',
        description: 'Contact information for footer',
        type: 'text',
        required: false,
        defaultValue: '<EMAIL> | (555) 123-4567'
      }
    ],
    thumbnail: '/templates/thumbnails/custom-html.png',
    tags: ['custom', 'html', 'advanced', 'flexible'],
    isSystem: false,
    version: 1,
    createdAt: new Date('2024-03-20'),
    updatedAt: new Date('2024-03-25'),
    createdBy: '<EMAIL>',
    updatedBy: '<EMAIL>',
    usageStats: createMockUsageStats('template-10')
  }
];

// Additional mock data for template categories
export const templateCategoryStats = {
  [TemplateCategory.NEWSLETTER]: { count: 2, lastUsed: new Date('2024-03-15') },
  [TemplateCategory.PROMOTIONAL]: { count: 1, lastUsed: new Date('2024-04-10') },
  [TemplateCategory.ANNOUNCEMENT]: { count: 1, lastUsed: new Date('2024-03-01') },
  [TemplateCategory.WELCOME]: { count: 1, lastUsed: new Date('2024-02-20') },
  [TemplateCategory.FOLLOWUP]: { count: 2, lastUsed: new Date('2024-02-15') },
  [TemplateCategory.EVENT]: { count: 1, lastUsed: new Date('2024-01-20') },
  [TemplateCategory.SURVEY]: { count: 1, lastUsed: new Date('2024-01-25') },
  [TemplateCategory.NOTIFICATION]: { count: 1, lastUsed: new Date('2024-01-05') },
  [TemplateCategory.CUSTOM]: { count: 1, lastUsed: new Date('2024-03-25') }
};

// Popular template tags
export const popularTemplateTags = [
  'welcome', 'promotion', 'newsletter', 'event', 'follow-up',
  'announcement', 'survey', 'notification', 'thank-you', 'custom'
];