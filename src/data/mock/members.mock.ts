import { MembershipStatus } from '@/types/email-blast';

export interface MockMember {
  id: string;
  name: string;
  email: string;
  phoneNumber: string;
  membershipType: string;
  membershipStatus: MembershipStatus;
  joinDate: Date;
  expiryDate?: Date;
  location: string;
  age: number;
  gender: 'male' | 'female' | 'other';
  tags: string[];
  lastActivityDate: Date;
  isSubscribed: boolean;
  customFields: Record<string, any>;
}

const firstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const lastNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Flores',
  'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'
];

const locations = [
  'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego',
  'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte', 'Seattle',
  'Denver', 'Washington DC', 'Boston', 'Detroit', 'Nashville', 'Portland', 'Las Vegas', 'Oklahoma City',
  'Baltimore', 'Louisville', 'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento', 'Kansas City'
];

const membershipTypes = [
  'Associate Member',
  'Fellow Member',
  'Honorary Fellow',
  'Life Member',
  'LIFE MEMBER (Fellow)',
  'Member above 31',
  'Member below 30',
  'Organisational Member',
  'Senior Member',
  'Student Member'
];

const interests = [
  'Sports', 'Music', 'Arts', 'Technology', 'Travel', 'Food', 'Fashion', 'Health', 'Business', 'Education',
  'Entertainment', 'Gaming', 'Photography', 'Reading', 'Cooking', 'Fitness', 'Nature', 'Science', 'Politics', 'History'
];

const tags = [
  'early-adopter', 'frequent-buyer', 'newsletter-subscriber', 'event-attendee', 'volunteer',
  'donor', 'premium-member', 'influencer', 'ambassador', 'new-member', 'returning-member',
  'at-risk', 'high-value', 'engaged', 'inactive', 'referrer', 'survey-respondent'
];

function randomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function generatePhoneNumber(): string {
  const areaCode = Math.floor(Math.random() * 900) + 100;
  const firstPart = Math.floor(Math.random() * 900) + 100;
  const secondPart = Math.floor(Math.random() * 9000) + 1000;
  return `+1 (${areaCode}) ${firstPart}-${secondPart}`;
}

function generateMemberStatus(joinDate: Date): { status: MembershipStatus; expiryDate?: Date } {
  const now = new Date();
  const random = Math.random();
  
  if (random < 0.7) {
    // 70% active members
    const expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() + Math.floor(Math.random() * 12) + 1);
    return { status: MembershipStatus.ACTIVE, expiryDate };
  } else if (random < 0.85) {
    // 15% expired members
    const expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() - Math.floor(Math.random() * 12) - 1);
    return { status: MembershipStatus.EXPIRED, expiryDate };
  } else if (random < 0.95) {
    // 10% pending members
    return { status: MembershipStatus.PENDING };
  } else {
    // 5% suspended members
    const expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() + Math.floor(Math.random() * 6));
    return { status: MembershipStatus.SUSPENDED, expiryDate };
  }
}

function generateMockMember(index: number): MockMember {
  const firstName = randomElement(firstNames);
  const lastName = randomElement(lastNames);
  const name = `${firstName} ${lastName}`;
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@example.com`;
  
  const joinDate = randomDate(new Date('2020-01-01'), new Date());
  const { status, expiryDate } = generateMemberStatus(joinDate);
  
  const age = Math.floor(Math.random() * 50) + 18;
  const memberInterests = Array.from(
    { length: Math.floor(Math.random() * 5) + 1 },
    () => randomElement(interests)
  );
  const memberTags = Array.from(
    { length: Math.floor(Math.random() * 4) },
    () => randomElement(tags)
  ).filter((tag, index, self) => self.indexOf(tag) === index);
  
  // Add status-based tags
  if (status === MembershipStatus.EXPIRED) {
    memberTags.push('at-risk');
  } else if (status === MembershipStatus.ACTIVE && expiryDate) {
    const daysUntilExpiry = Math.floor((expiryDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    if (daysUntilExpiry < 30) {
      memberTags.push('renewal-due');
    }
  }
  
  const lastActivityDate = randomDate(joinDate, new Date());
  const isSubscribed = Math.random() > 0.1; // 90% subscribed
  
  return {
    id: `member-${index + 1}`,
    name,
    email,
    phoneNumber: generatePhoneNumber(),
    membershipType: randomElement(membershipTypes),
    membershipStatus: status,
    joinDate,
    expiryDate,
    location: randomElement(locations),
    age,
    gender: randomElement(['male', 'female', 'other']),
    tags: memberTags,
    lastActivityDate,
    isSubscribed,
    customFields: {
      interests: memberInterests,
      preferredCommunication: randomElement(['email', 'sms', 'both']),
      languagePreference: randomElement(['en', 'es', 'fr', 'de', 'zh']),
      referralSource: randomElement(['website', 'social-media', 'friend', 'event', 'advertisement', 'other']),
      lifetimeValue: Math.floor(Math.random() * 10000) + 100,
      engagementScore: Math.floor(Math.random() * 100),
      lastPurchaseDate: Math.random() > 0.3 ? randomDate(joinDate, new Date()) : null,
      totalPurchases: Math.floor(Math.random() * 50),
      notes: Math.random() > 0.7 ? `Special notes about ${firstName}` : null
    }
  };
}

// Generate 550 mock members
export const mockMembers: MockMember[] = Array.from({ length: 550 }, (_, index) => generateMockMember(index));

// Export some utility functions for filtering
export const getMembersByStatus = (status: MembershipStatus): MockMember[] => {
  return mockMembers.filter(member => member.membershipStatus === status);
};

export const getMembersByType = (type: string): MockMember[] => {
  return mockMembers.filter(member => member.membershipType === type);
};

export const getMembersByLocation = (location: string): MockMember[] => {
  return mockMembers.filter(member => member.location === location);
};

export const getExpiringMembers = (daysFromNow: number = 30): MockMember[] => {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + daysFromNow);
  
  return mockMembers.filter(member => {
    if (!member.expiryDate || member.membershipStatus !== MembershipStatus.ACTIVE) return false;
    return member.expiryDate <= futureDate && member.expiryDate >= new Date();
  });
};

export const getSubscribedMembers = (): MockMember[] => {
  return mockMembers.filter(member => member.isSubscribed);
};

// Export member statistics
export const memberStatistics = {
  total: mockMembers.length,
  byStatus: {
    active: getMembersByStatus(MembershipStatus.ACTIVE).length,
    expired: getMembersByStatus(MembershipStatus.EXPIRED).length,
    pending: getMembersByStatus(MembershipStatus.PENDING).length,
    suspended: getMembersByStatus(MembershipStatus.SUSPENDED).length
  },
  byType: membershipTypes.reduce((acc, type) => ({
    ...acc,
    [type]: getMembersByType(type).length
  }), {} as Record<string, number>),
  byLocation: locations.reduce((acc, location) => ({
    ...acc,
    [location]: getMembersByLocation(location).length
  }), {} as Record<string, number>),
  subscribed: getSubscribedMembers().length,
  unsubscribed: mockMembers.length - getSubscribedMembers().length,
  expiringIn30Days: getExpiringMembers(30).length
};