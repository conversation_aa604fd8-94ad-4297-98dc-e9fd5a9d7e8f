import { EmailBlast, EmailCampaignStatus, EmailContent } from '@/types/email-blast';
import { mockRecipientLists } from './recipient-lists.mock';

const emailTemplates: Record<string, EmailContent> = {
  welcome: {
    subject: 'Welcome to Our Community, {{name}}!',
    body: `Dear {{name}},

Welcome to our community! We're thrilled to have you as a {{membershipType}} member.

Your membership benefits include:
- Exclusive access to member events
- Monthly newsletter with industry insights
- Special discounts on products and services
- Priority customer support

If you have any questions, please don't hesitate to reach out to our support team.

Best regards,
The Membership Team`,
    bodyHtml: `<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #4a90e2; color: white; padding: 20px; text-align: center; }
    .content { padding: 20px; background-color: #f9f9f9; }
    .button { display: inline-block; padding: 10px 20px; background-color: #4a90e2; color: white; text-decoration: none; border-radius: 5px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Welcome to Our Community!</h1>
    </div>
    <div class="content">
      <p>Dear {{name}},</p>
      <p>Welcome to our community! We're thrilled to have you as a <strong>{{membershipType}}</strong> member.</p>
      <h3>Your membership benefits include:</h3>
      <ul>
        <li>Exclusive access to member events</li>
        <li>Monthly newsletter with industry insights</li>
        <li>Special discounts on products and services</li>
        <li>Priority customer support</li>
      </ul>
      <p style="text-align: center; margin-top: 30px;">
        <a href="{{memberPortalUrl}}" class="button">Access Member Portal</a>
      </p>
      <p>If you have any questions, please don't hesitate to reach out to our support team.</p>
      <p>Best regards,<br>The Membership Team</p>
    </div>
  </div>
</body>
</html>`,
    variables: { name: '', membershipType: '', memberPortalUrl: 'https://members.example.com' }
  },
  renewal: {
    subject: 'Your Membership Expires Soon - Renew Today!',
    body: `Dear {{name}},

We hope you've been enjoying your {{membershipType}} membership benefits. We wanted to remind you that your membership will expire on {{expiryDate}}.

Don't miss out on your exclusive member benefits! Renew today to continue enjoying:
- Member-only events and workshops
- Exclusive discounts and offers
- Access to our resource library
- And much more!

Renew now and save 10% with code RENEW10.

Best regards,
The Membership Team`,
    bodyHtml: `<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #e74c3c; color: white; padding: 20px; text-align: center; }
    .content { padding: 20px; background-color: #f9f9f9; }
    .urgent { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px; }
    .button { display: inline-block; padding: 12px 30px; background-color: #27ae60; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Your Membership Expires Soon!</h1>
    </div>
    <div class="content">
      <p>Dear {{name}},</p>
      <p>We hope you've been enjoying your <strong>{{membershipType}}</strong> membership benefits.</p>
      <div class="urgent">
        <strong>⚠️ Important:</strong> Your membership will expire on <strong>{{expiryDate}}</strong>.
      </div>
      <h3>Don't miss out on your exclusive member benefits!</h3>
      <ul>
        <li>Member-only events and workshops</li>
        <li>Exclusive discounts and offers</li>
        <li>Access to our resource library</li>
        <li>And much more!</li>
      </ul>
      <p style="text-align: center; margin: 30px 0;">
        <a href="{{renewalUrl}}" class="button">Renew Now & Save 10%</a>
      </p>
      <p style="text-align: center; color: #666;">Use code: <strong>RENEW10</strong></p>
      <p>Best regards,<br>The Membership Team</p>
    </div>
  </div>
</body>
</html>`,
    variables: { name: '', membershipType: '', expiryDate: '', renewalUrl: 'https://renew.example.com' }
  },
  newsletter: {
    subject: 'Monthly Newsletter - {{month}} {{year}} Edition',
    body: `Dear {{name}},

Welcome to our {{month}} newsletter! Here's what's new this month:

UPCOMING EVENTS
- Annual Conference: March 15-17
- Networking Mixer: March 22
- Workshop Series: Starting April 1

MEMBER SPOTLIGHT
This month we feature Sarah Johnson, who has been making waves in the industry...

INDUSTRY NEWS
- New regulations affecting our sector
- Market trends and analysis
- Technology updates

Thank you for being a valued member of our community!

Best regards,
The Newsletter Team`,
    bodyHtml: `<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Georgia, serif; line-height: 1.8; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #2c3e50; color: white; padding: 30px; text-align: center; }
    .content { padding: 30px; background-color: #ffffff; }
    .section { margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #eee; }
    .section:last-child { border-bottom: none; }
    h2 { color: #2c3e50; margin-top: 0; }
    .event { background-color: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
    .spotlight { background-color: #f8f9fa; padding: 20px; border-left: 4px solid #3498db; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Monthly Newsletter</h1>
      <p style="margin: 0; font-size: 18px;">{{month}} {{year}} Edition</p>
    </div>
    <div class="content">
      <p>Dear {{name}},</p>
      <p>Welcome to our {{month}} newsletter! Here's what's new this month:</p>
      
      <div class="section">
        <h2>📅 Upcoming Events</h2>
        <div class="event">
          <strong>Annual Conference</strong><br>
          March 15-17 | Convention Center
        </div>
        <div class="event">
          <strong>Networking Mixer</strong><br>
          March 22 | 6:00 PM - 8:00 PM
        </div>
        <div class="event">
          <strong>Workshop Series</strong><br>
          Starting April 1 | Register Now
        </div>
      </div>
      
      <div class="section">
        <h2>⭐ Member Spotlight</h2>
        <div class="spotlight">
          <p>This month we feature <strong>Sarah Johnson</strong>, who has been making waves in the industry with her innovative approach to sustainable business practices...</p>
          <a href="#">Read Full Story →</a>
        </div>
      </div>
      
      <div class="section">
        <h2>📰 Industry News</h2>
        <ul>
          <li><a href="#">New regulations affecting our sector</a></li>
          <li><a href="#">Market trends and analysis for Q1</a></li>
          <li><a href="#">Technology updates and digital transformation</a></li>
        </ul>
      </div>
      
      <p>Thank you for being a valued member of our community!</p>
      <p>Best regards,<br>The Newsletter Team</p>
    </div>
  </div>
</body>
</html>`,
    variables: { name: '', month: 'March', year: '2024' }
  },
  event: {
    subject: 'You\'re Invited: {{eventName}} - {{eventDate}}',
    body: `Dear {{name}},

You're cordially invited to attend {{eventName}}!

Event Details:
Date: {{eventDate}}
Time: {{eventTime}}
Location: {{eventLocation}}

{{eventDescription}}

Space is limited, so please RSVP by {{rsvpDeadline}}.

We look forward to seeing you there!

Best regards,
The Events Team`,
    bodyHtml: `<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px; text-align: center; }
    .content { padding: 30px; background-color: #f9f9f9; }
    .event-details { background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .detail-row { margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #eee; }
    .detail-row:last-child { border-bottom: none; }
    .button { display: inline-block; padding: 15px 40px; background-color: #667eea; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 style="margin: 0;">You're Invited!</h1>
      <h2 style="margin: 10px 0; font-weight: normal;">{{eventName}}</h2>
    </div>
    <div class="content">
      <p>Dear {{name}},</p>
      <p>You're cordially invited to attend our upcoming event!</p>
      
      <div class="event-details">
        <div class="detail-row">
          <strong>📅 Date:</strong> {{eventDate}}
        </div>
        <div class="detail-row">
          <strong>🕐 Time:</strong> {{eventTime}}
        </div>
        <div class="detail-row">
          <strong>📍 Location:</strong> {{eventLocation}}
        </div>
      </div>
      
      <p>{{eventDescription}}</p>
      
      <p style="text-align: center; margin: 30px 0;">
        <a href="{{rsvpUrl}}" class="button">RSVP Now</a>
      </p>
      
      <p style="text-align: center; color: #666;">
        Space is limited. Please RSVP by <strong>{{rsvpDeadline}}</strong>
      </p>
      
      <p>We look forward to seeing you there!</p>
      <p>Best regards,<br>The Events Team</p>
    </div>
  </div>
</body>
</html>`,
    variables: {
      name: '',
      eventName: '',
      eventDate: '',
      eventTime: '',
      eventLocation: '',
      eventDescription: '',
      rsvpDeadline: '',
      rsvpUrl: 'https://events.example.com/rsvp'
    }
  }
};

const generateCampaignContent = (type: string): EmailContent => {
  const templates = Object.keys(emailTemplates);
  const template = emailTemplates[type] || emailTemplates.newsletter;
  return { ...template };
};

export const mockEmailCampaigns: EmailBlast[] = [
  {
    id: 'campaign-1',
    name: 'March 2024 Newsletter',
    description: 'Monthly newsletter featuring upcoming events and member spotlight',
    status: EmailCampaignStatus.SENT,
    content: generateCampaignContent('newsletter'),
    recipientListId: 'list-10',
    recipientCount: 485,
    sentCount: 485,
    failedCount: 3,
    openCount: 312,
    clickCount: 89,
    sentAt: new Date('2024-03-01T10:00:00Z'),
    createdAt: new Date('2024-02-28T14:30:00Z'),
    updatedAt: new Date('2024-03-01T10:15:00Z'),
    createdBy: 'admin-user-1',
    updatedBy: 'admin-user-1',
    tags: ['newsletter', 'monthly']
  },
  {
    id: 'campaign-2',
    name: 'Membership Renewal Reminder - 30 Days',
    description: 'Automated renewal reminder for members expiring in 30 days',
    status: EmailCampaignStatus.SENDING,
    content: generateCampaignContent('renewal'),
    recipientListId: 'list-3',
    recipientCount: 67,
    sentCount: 45,
    failedCount: 1,
    openCount: 38,
    clickCount: 22,
    scheduledAt: new Date('2024-03-15T09:00:00Z'),
    createdAt: new Date('2024-03-14T16:00:00Z'),
    updatedAt: new Date('2024-03-15T09:05:00Z'),
    createdBy: 'admin-user-2',
    updatedBy: 'system',
    tags: ['renewal', 'automated']
  },
  {
    id: 'campaign-3',
    name: 'Annual Conference Invitation',
    description: 'Save the date for our annual conference',
    status: EmailCampaignStatus.SCHEDULED,
    content: {
      ...generateCampaignContent('event'),
      variables: {
        eventName: 'Annual Industry Conference 2024',
        eventDate: 'April 15-17, 2024',
        eventTime: '9:00 AM - 5:00 PM',
        eventLocation: 'Grand Convention Center, New York',
        eventDescription: 'Join us for three days of inspiring keynotes, interactive workshops, and unparalleled networking opportunities.',
        rsvpDeadline: 'April 1, 2024',
        rsvpUrl: 'https://events.example.com/annual-conference-2024'
      }
    },
    recipientListId: 'list-1',
    recipientCount: 380,
    scheduledAt: new Date('2024-03-20T14:00:00Z'),
    createdAt: new Date('2024-03-10T11:00:00Z'),
    updatedAt: new Date('2024-03-10T11:30:00Z'),
    createdBy: 'admin-user-3',
    updatedBy: 'admin-user-3',
    tags: ['event', 'conference']
  },
  {
    id: 'campaign-4',
    name: 'Welcome Series - New Members',
    description: 'Automated welcome email for new members',
    status: EmailCampaignStatus.SENT,
    content: generateCampaignContent('welcome'),
    recipientListId: 'list-14',
    recipientCount: 28,
    sentCount: 28,
    failedCount: 0,
    openCount: 26,
    clickCount: 18,
    sentAt: new Date('2024-03-05T08:00:00Z'),
    createdAt: new Date('2024-03-05T07:30:00Z'),
    updatedAt: new Date('2024-03-05T08:05:00Z'),
    createdBy: 'system',
    updatedBy: 'system',
    tags: ['welcome', 'automated', 'onboarding']
  },
  {
    id: 'campaign-5',
    name: 'VIP Member Exclusive Event',
    description: 'Invitation to VIP-only networking dinner',
    status: EmailCampaignStatus.DRAFT,
    content: {
      ...generateCampaignContent('event'),
      variables: {
        eventName: 'VIP Networking Dinner',
        eventDate: 'March 30, 2024',
        eventTime: '6:30 PM - 9:00 PM',
        eventLocation: 'The Grand Ballroom, Fifth Avenue',
        eventDescription: 'An exclusive evening of fine dining and networking with fellow VIP members and industry leaders.',
        rsvpDeadline: 'March 25, 2024',
        rsvpUrl: 'https://events.example.com/vip-dinner-march'
      }
    },
    recipientListId: 'list-9',
    recipientCount: 42,
    createdAt: new Date('2024-03-12T10:00:00Z'),
    updatedAt: new Date('2024-03-12T15:45:00Z'),
    createdBy: 'admin-user-1',
    updatedBy: 'admin-user-1',
    tags: ['vip', 'exclusive', 'event']
  },
  {
    id: 'campaign-6',
    name: 'Win-Back Campaign - Expired Members',
    description: 'Special offer for recently expired members',
    status: EmailCampaignStatus.SENT,
    content: {
      subject: 'We Miss You! Come Back with 25% Off',
      body: 'Special renewal offer for valued past members...',
      bodyHtml: '<html>...</html>'
    },
    recipientListId: 'list-4',
    recipientCount: 89,
    sentCount: 89,
    failedCount: 2,
    openCount: 54,
    clickCount: 12,
    sentAt: new Date('2024-02-20T11:00:00Z'),
    createdAt: new Date('2024-02-19T14:00:00Z'),
    updatedAt: new Date('2024-02-20T11:10:00Z'),
    createdBy: 'admin-user-2',
    updatedBy: 'admin-user-2',
    tags: ['win-back', 'promotion']
  },
  {
    id: 'campaign-7',
    name: 'California Chapter Update',
    description: 'Regional update for California members',
    status: EmailCampaignStatus.SENT,
    content: {
      subject: 'California Chapter News & Updates',
      body: 'Updates specific to our California members...',
      bodyHtml: '<html>...</html>'
    },
    recipientListId: 'list-13',
    recipientCount: 124,
    sentCount: 124,
    failedCount: 1,
    openCount: 98,
    clickCount: 34,
    sentAt: new Date('2024-02-15T09:30:00Z'),
    createdAt: new Date('2024-02-14T16:00:00Z'),
    updatedAt: new Date('2024-02-15T09:35:00Z'),
    createdBy: 'admin-user-3',
    updatedBy: 'admin-user-3',
    tags: ['regional', 'california']
  },
  {
    id: 'campaign-8',
    name: 'Senior Member Benefits Update',
    description: 'New benefits announcement for senior members',
    status: EmailCampaignStatus.FAILED,
    content: {
      subject: 'Enhanced Benefits for Our Senior Members',
      body: 'Exciting new benefits for members 60+...',
      bodyHtml: '<html>...</html>'
    },
    recipientListId: 'list-6',
    recipientCount: 73,
    sentCount: 0,
    failedCount: 73,
    scheduledAt: new Date('2024-03-08T10:00:00Z'),
    createdAt: new Date('2024-03-07T14:30:00Z'),
    updatedAt: new Date('2024-03-08T10:05:00Z'),
    createdBy: 'admin-user-1',
    updatedBy: 'system',
    tags: ['seniors', 'benefits'],
    metadata: {
      errorMessage: 'SMTP server connection failed'
    }
  },
  {
    id: 'campaign-9',
    name: 'Board Meeting Minutes - February',
    description: 'Monthly board meeting summary',
    status: EmailCampaignStatus.SENT,
    content: {
      subject: 'Board Meeting Minutes - February 2024',
      body: 'Summary of key decisions and discussions...',
      bodyHtml: '<html>...</html>'
    },
    recipientListId: 'list-12',
    recipientCount: 25,
    sentCount: 25,
    failedCount: 0,
    openCount: 25,
    clickCount: 8,
    sentAt: new Date('2024-02-25T16:00:00Z'),
    createdAt: new Date('2024-02-25T15:00:00Z'),
    updatedAt: new Date('2024-02-25T16:02:00Z'),
    createdBy: 'admin-user-1',
    updatedBy: 'admin-user-1',
    tags: ['internal', 'board']
  },
  {
    id: 'campaign-10',
    name: 'Young Professionals Meetup',
    description: 'Networking event for members aged 25-40',
    status: EmailCampaignStatus.SCHEDULED,
    content: {
      ...generateCampaignContent('event'),
      variables: {
        eventName: 'Young Professionals Happy Hour',
        eventDate: 'March 28, 2024',
        eventTime: '5:30 PM - 7:30 PM',
        eventLocation: 'The Rooftop Bar, Downtown',
        eventDescription: 'Connect with fellow young professionals in a relaxed atmosphere. Complimentary appetizers and drink specials.',
        rsvpDeadline: 'March 26, 2024',
        rsvpUrl: 'https://events.example.com/yp-meetup-march'
      }
    },
    recipientListId: 'list-7',
    recipientCount: 156,
    scheduledAt: new Date('2024-03-18T10:00:00Z'),
    createdAt: new Date('2024-03-11T09:00:00Z'),
    updatedAt: new Date('2024-03-11T09:30:00Z'),
    createdBy: 'admin-user-2',
    updatedBy: 'admin-user-2',
    tags: ['event', 'networking', 'young-professionals']
  }
];

// Export utility functions
export const getCampaignById = (id: string): EmailBlast | undefined => {
  return mockEmailCampaigns.find(campaign => campaign.id === id);
};

export const getCampaignsByStatus = (status: EmailCampaignStatus): EmailBlast[] => {
  return mockEmailCampaigns.filter(campaign => campaign.status === status);
};

export const getCampaignsByRecipientList = (listId: string): EmailBlast[] => {
  return mockEmailCampaigns.filter(campaign => campaign.recipientListId === listId);
};

export const getRecentCampaigns = (days: number = 30): EmailBlast[] => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return mockEmailCampaigns.filter(campaign => 
    campaign.createdAt >= cutoffDate || 
    (campaign.sentAt && campaign.sentAt >= cutoffDate)
  );
};

// Campaign statistics
export const campaignStatistics = {
  total: mockEmailCampaigns.length,
  byStatus: {
    draft: getCampaignsByStatus(EmailCampaignStatus.DRAFT).length,
    scheduled: getCampaignsByStatus(EmailCampaignStatus.SCHEDULED).length,
    sending: getCampaignsByStatus(EmailCampaignStatus.SENDING).length,
    sent: getCampaignsByStatus(EmailCampaignStatus.SENT).length,
    failed: getCampaignsByStatus(EmailCampaignStatus.FAILED).length
  },
  totalEmailsSent: mockEmailCampaigns.reduce((sum, campaign) => sum + (campaign.sentCount || 0), 0),
  totalOpens: mockEmailCampaigns.reduce((sum, campaign) => sum + (campaign.openCount || 0), 0),
  totalClicks: mockEmailCampaigns.reduce((sum, campaign) => sum + (campaign.clickCount || 0), 0),
  averageOpenRate: (mockEmailCampaigns.reduce((sum, campaign) => {
    if (campaign.sentCount && campaign.openCount) {
      return sum + (campaign.openCount / campaign.sentCount);
    }
    return sum;
  }, 0) / mockEmailCampaigns.filter(c => c.sentCount && c.openCount).length * 100).toFixed(1)
};