import { z } from 'zod';

// Attendance configuration schema (for database operations)
export const attendanceConfigurationSchema = z.object({
  id: z.string().uuid(),
  programme_id: z.string().uuid(),
  minimum_attendance_percentage: z.number().min(0).max(100).default(80),
  late_arrival_threshold_minutes: z.number().int().min(0).default(15),
  early_departure_threshold_minutes: z.number().int().min(0).default(15),
  certificate_minimum_attendance: z.number().min(0).max(100).default(80),
  certificate_minimum_sessions: z.number().int().min(0).optional(),
  allow_self_checkin: z.boolean().default(true),
  require_checkout: z.boolean().default(false),
  checkin_starts_minutes_before: z.number().int().min(0).default(30),
  checkin_ends_minutes_after: z.number().int().min(0).default(30),
  qr_code_enabled: z.boolean().default(true),
  qr_code_refresh_interval_minutes: z.number().int().min(1).default(60),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Form schema for UI (only shows editable fields)
export const attendanceConfigFormSchema = z.object({
  programme_id: z.string().uuid('Invalid programme ID'),
  allow_self_checkin: z.boolean().default(true),
  qr_code_enabled: z.boolean().default(true),
});

// Update schema for configuration (only visible fields can be updated)
export const updateAttendanceConfigSchema = z.object({
  allow_self_checkin: z.boolean().optional(),
  qr_code_enabled: z.boolean().optional(),
});

// Schema for creating new configuration with defaults
export const createAttendanceConfigSchema = z.object({
  programme_id: z.string().uuid('Invalid programme ID'),
  // Visible fields
  allow_self_checkin: z.boolean().default(true),
  qr_code_enabled: z.boolean().default(true),
  // Hidden fields with defaults (will be set in the service layer)
  minimum_attendance_percentage: z.number().min(0).max(100).default(80),
  late_arrival_threshold_minutes: z.number().int().min(0).default(15),
  early_departure_threshold_minutes: z.number().int().min(0).default(15),
  certificate_minimum_attendance: z.number().min(0).max(100).default(80),
  certificate_minimum_sessions: z.number().int().min(0).optional(),
  require_checkout: z.boolean().default(false),
  checkin_starts_minutes_before: z.number().int().min(0).default(30),
  checkin_ends_minutes_after: z.number().int().min(0).default(30),
  qr_code_refresh_interval_minutes: z.number().int().min(1).default(60),
});

// Type exports for use in components
export type AttendanceConfiguration = z.infer<typeof attendanceConfigurationSchema>;
export type AttendanceConfigForm = z.infer<typeof attendanceConfigFormSchema>;
export type UpdateAttendanceConfig = z.infer<typeof updateAttendanceConfigSchema>;
export type CreateAttendanceConfig = z.infer<typeof createAttendanceConfigSchema>;