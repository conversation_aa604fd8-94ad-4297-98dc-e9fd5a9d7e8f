import { z } from 'zod';

// Enum schemas
export const attendanceStatusSchema = z.enum(['present', 'absent', 'late', 'excused', 'partial']);
export const checkInMethodSchema = z.enum(['qr_code', 'manual', 'auto', 'facial_recognition']);
export const sessionTypeSchema = z.enum(['regular', 'workshop', 'breakout', 'keynote', 'networking']);
export const completionStatusSchema = z.enum(['incomplete', 'completed', 'partially_completed', 'withdrawn']);

// Check-in request schema
export const checkInRequestSchema = z.object({
  participant_id: z.string().uuid('Invalid participant ID'),
  programme_id: z.string().uuid('Invalid programme ID'),
  schedule_id: z.string().uuid('Invalid schedule ID').optional(),
  qr_code_id: z.string().uuid('Invalid QR code ID').optional(),
  check_in_method: checkInMethodSchema.default('manual'),
  check_in_location: z.string().max(255).optional(),
  check_in_device_info: z.record(z.any()).optional(),
  notes: z.string().max(1000).optional(),
});

// Check-out request schema
export const checkOutRequestSchema = z.object({
  attendance_record_id: z.string().uuid('Invalid attendance record ID'),
  check_out_time: z.string().datetime().optional(),
  notes: z.string().max(1000).optional(),
});

// Bulk check-in request schema
export const bulkCheckInRequestSchema = z.object({
  participant_ids: z.array(z.string().uuid('Invalid participant ID')).min(1, 'At least one participant is required'),
  programme_id: z.string().uuid('Invalid programme ID'),
  schedule_id: z.string().uuid('Invalid schedule ID').optional(),
  check_in_method: checkInMethodSchema.default('manual'),
  check_in_location: z.string().max(255).optional(),
  notes: z.string().max(1000).optional(),
});

// Export alias for backward compatibility
export const bulkCheckInFormSchema = bulkCheckInRequestSchema;

// Attendance record schema (for database operations)
export const attendanceRecordSchema = z.object({
  id: z.string().uuid(),
  participant_id: z.string().uuid(),
  programme_id: z.string().uuid(),
  schedule_id: z.string().uuid().optional(),
  qr_code_id: z.string().uuid().optional(),
  check_in_time: z.string().datetime(),
  check_out_time: z.string().datetime().optional(),
  attendance_status: attendanceStatusSchema,
  check_in_method: checkInMethodSchema,
  check_in_location: z.string().optional(),
  check_in_device_info: z.record(z.any()).optional(),
  checked_in_by: z.string().uuid().optional(),
  checked_out_by: z.string().uuid().optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Session attendance schema
export const sessionAttendanceSchema = z.object({
  id: z.string().uuid(),
  attendance_record_id: z.string().uuid(),
  participant_id: z.string().uuid(),
  programme_id: z.string().uuid(),
  schedule_id: z.string().uuid(),
  session_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
  session_name: z.string().max(255).optional(),
  session_type: sessionTypeSchema,
  is_present: z.boolean(),
  arrival_time: z.string().datetime().optional(),
  departure_time: z.string().datetime().optional(),
  duration_minutes: z.number().int().min(0).optional(),
  participation_score: z.number().int().min(0).max(100).optional(),
  engagement_notes: z.string().optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Attendance summary schema
export const attendanceSummarySchema = z.object({
  id: z.string().uuid(),
  participant_id: z.string().uuid(),
  programme_id: z.string().uuid(),
  registration_id: z.string().uuid(),
  total_sessions: z.number().int().min(0),
  attended_sessions: z.number().int().min(0),
  attendance_percentage: z.number().min(0).max(100).optional(),
  total_duration_minutes: z.number().int().min(0),
  first_attendance: z.string().datetime().optional(),
  last_attendance: z.string().datetime().optional(),
  completion_status: completionStatusSchema,
  completion_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  certificate_eligible: z.boolean(),
  certificate_issued: z.boolean(),
  certificate_issued_date: z.string().datetime().optional(),
  certificate_number: z.string().max(100).optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Export options schema
export const attendanceExportOptionsSchema = z.object({
  programme_id: z.string().uuid('Invalid programme ID'),
  participant_ids: z.array(z.string().uuid()).optional(),
  include_summary: z.boolean().default(true),
  include_sessions: z.boolean().default(false),
  include_certificate_status: z.boolean().default(true),
  format: z.enum(['csv', 'excel', 'pdf']).default('csv'),
  date_range: z.object({
    start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  }).optional(),
});

// Form validation schemas for UI components
export const attendanceFilterSchema = z.object({
  programme_id: z.string().uuid().optional(),
  attendance_status: z.array(attendanceStatusSchema).optional(),
  check_in_method: z.array(checkInMethodSchema).optional(),
  date_from: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  date_to: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  certificate_eligible: z.boolean().optional(),
});

// Quick check-in form schema
export const quickCheckInSchema = z.object({
  participant_id: z.string().uuid('Invalid participant'),
  programme_id: z.string().uuid('Invalid programme'),
  schedule_id: z.string().uuid().optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
});

// Type exports for use in components
export type CheckInRequest = z.infer<typeof checkInRequestSchema>;
export type CheckOutRequest = z.infer<typeof checkOutRequestSchema>;
export type BulkCheckInRequest = z.infer<typeof bulkCheckInRequestSchema>;
export type BulkCheckInFormData = z.infer<typeof bulkCheckInFormSchema>;
export type AttendanceRecord = z.infer<typeof attendanceRecordSchema>;
export type SessionAttendance = z.infer<typeof sessionAttendanceSchema>;
export type AttendanceSummary = z.infer<typeof attendanceSummarySchema>;
export type AttendanceExportOptions = z.infer<typeof attendanceExportOptionsSchema>;
export type AttendanceFilter = z.infer<typeof attendanceFilterSchema>;
export type QuickCheckIn = z.infer<typeof quickCheckInSchema>;