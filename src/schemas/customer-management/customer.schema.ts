import { z } from "zod";
import type { CustomerType, CustomerStatus, CustomerCategory } from "@/types/customer-management";

// Address schema for customers
export const customerAddressSchema = z.object({
  street_address: z.string().min(1, "Street address is required"),
  city: z.string().min(1, "City is required"),
  postal_code: z.string().min(1, "Postal code is required"),
  country: z.string().min(1, "Country is required"),
});

// Optional address schema
export const optionalCustomerAddressSchema = z.object({
  street_address: z.string().optional(),
  city: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
});

// Contact Person schema for customers
export const customerContactPersonSchema = z.object({
  id: z.string().optional(), // Optional for new records, auto-generated
  name: z.string().min(1, "Contact name is required"),
  title: z.string().optional(),
  email: z.string().email("Invalid email format"),
  phone: z.string().optional(),
  is_primary: z.boolean().default(false),
});

// Transform empty strings to undefined for optional fields
const emptyStringToUndefined = z.literal('').transform(() => undefined);

// Base customer schema without refinements
const baseCustomerSchema = z.object({
  customer_code: z.union([z.string(), emptyStringToUndefined]).optional(),
  name: z.string().min(1, "Name is required"),
  display_name: z.union([z.string(), emptyStringToUndefined]).optional(),
  registration_number: z.union([z.string(), emptyStringToUndefined]).optional(),
  tax_registration_number: z.union([z.string(), emptyStringToUndefined]).optional(),
  customer_type: z.enum(["INDIVIDUAL", "COMPANY", "GOVERNMENT", "NON_PROFIT", "EDUCATIONAL"], {
    required_error: "Customer type is required",
  }),
  category: z.enum(["REGULAR", "VIP", "CORPORATE", "RESELLER", "DISTRIBUTOR"]).optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED"], {
    required_error: "Status is required",
  }).default("ACTIVE"),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email format"),
  phone: z.union([z.string(), emptyStringToUndefined]).optional(),
  website: z.union([z.string().url("Invalid URL format"), emptyStringToUndefined, z.literal("")]).optional(),
  billing_address: customerAddressSchema,
  shipping_addresses: z.array(customerAddressSchema).optional().default([]),
  payment_terms: z.union([z.string(), emptyStringToUndefined]).optional(),
  credit_limit: z.number().min(0, "Credit limit must be 0 or greater").nullable().optional(),
  is_gst_registered: z.boolean().default(false),
  contact_persons: z.array(customerContactPersonSchema).optional().default([]),
});

// Customer schema with validation
export const customerSchema = baseCustomerSchema.refine(
  (data) => {
    // For Individual customers, contact persons are optional
    if (data.customer_type === "INDIVIDUAL") {
      // If contact persons exist, ensure exactly one primary contact
      if (data.contact_persons && data.contact_persons.length > 0) {
        const primaryContacts = data.contact_persons.filter(cp => cp.is_primary);
        return primaryContacts.length === 1;
      }
      return true; // No contact persons required for individuals
    }
    
    // For non-Individual customers, ensure at least one contact person exists
    if (!data.contact_persons || data.contact_persons.length === 0) {
      return false;
    }
    
    // Ensure exactly one primary contact
    const primaryContacts = data.contact_persons.filter(cp => cp.is_primary);
    return primaryContacts.length === 1;
  },
  {
    message: "Non-individual customers must have exactly one primary contact person",
    path: ["contact_persons"],
  }
);

// Form data types
export type CustomerFormData = z.infer<typeof customerSchema>;
export type CustomerContactPersonFormData = z.infer<typeof customerContactPersonSchema>;
export type CustomerAddressFormData = z.infer<typeof customerAddressSchema>;

// Validation for editing customers - same as create
export const customerEditSchema = customerSchema;

export type CustomerEditFormData = z.infer<typeof customerEditSchema>;