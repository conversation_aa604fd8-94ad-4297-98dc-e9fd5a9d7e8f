import { FileWithPreview } from "@/types/general.types";
import { z } from "zod";

// Goods Receipt Item schema
export const goodsReceiptItemSchema = z.object({
  // Use po_item_id to match database schema
  po_item_id: z.string({ required_error: "Purchase order item is required" }),
  quantity_received: z.number()
    .min(0, "Quantity received must be 0 or greater")
    .max(99999999999.9999, "Quantity exceeds maximum allowed"),
  quantity_accepted: z.number()
    .min(0, "Quantity accepted must be 0 or greater")
    .max(99999999999.9999, "Quantity exceeds maximum allowed"),
  quantity_rejected: z.number()
    .min(0, "Quantity rejected must be 0 or greater")
    .max(99999999999.9999, "Quantity exceeds maximum allowed")
    .default(0),
  rejection_reason: z.string().optional(),
  rejection_reason_type: z.enum(["damaged", "wrong_item", "quality_issue", "expired", "other"]).optional(),
  rejection_notes: z.string().max(500, "Rejection notes too long").optional(),
}).refine(
  (data) => {
    // Allow for floating point precision issues
    const diff = Math.abs(data.quantity_received - (data.quantity_accepted + data.quantity_rejected));
    return diff < 0.0001;
  },
  {
    message: "Quantity received must equal quantity accepted plus quantity rejected",
    path: ["quantity_received"],
  }
).refine(
  (data) => {
    if (data.quantity_rejected > 0) {
      return data.rejection_reason_type && data.rejection_reason_type.length > 0;
    }
    return true;
  },
  {
    message: "Rejection reason is required when quantity rejected is greater than 0",
    path: ["rejection_reason_type"],
  }
).refine(
  (data) => {
    if (data.rejection_reason_type === "other") {
      return data.rejection_notes && data.rejection_notes.trim().length > 0;
    }
    return true;
  },
  {
    message: "Please provide details when 'Other' is selected",
    path: ["rejection_notes"],
  }
);

// Main Goods Receipt schema
export const goodsReceiptSchema = z.object({
  purchase_order_id: z.string({ required_error: "Purchase order is required" }).uuid("Invalid purchase order ID"),
  receipt_date: z.date({ required_error: "Receipt date is required" }),
  received_by: z.string({ required_error: "Received by is required" }).min(1, "Received by is required"),
  notes: z.string().max(1000, "Notes too long").optional().nullable(),

  // Quality inspection fields
  inspection_required: z.boolean().optional().default(false),
  inspector_name: z.string().max(100, "Inspector name too long").optional().nullable(),
  inspection_date: z.date().optional().nullable(),
  inspection_report_number: z.string().max(50, "Report number too long").optional().nullable(),

  // Line items
  items: z.array(goodsReceiptItemSchema).min(1, "At least one receipt item is required").refine(
    (items) => items.some(item => item.quantity_received > 0),
    "At least one item must have quantity received greater than 0"
  ),
  files: z.array(z.custom<FileWithPreview>()).optional(),
}).refine(
  (data) => {
    // If inspection is required, inspector name must be provided
    if (data.inspection_required && (!data.inspector_name || data.inspector_name.trim().length === 0)) {
      return false;
    }
    return true;
  },
  {
    message: "Inspector name is required when inspection is enabled",
    path: ["inspector_name"],
  }
).refine(
  (data) => {
    // If inspection is required, inspection date must be provided
    if (data.inspection_required && !data.inspection_date) {
      return false;
    }
    return true;
  },
  {
    message: "Inspection date is required when inspection is enabled",
    path: ["inspection_date"],
  }
);

// Form data types - these match the schema structure
export type GoodsReceiptFormData = z.infer<typeof goodsReceiptSchema>;
export type GoodsReceiptItemFormData = z.infer<typeof goodsReceiptItemSchema>;