import { z } from "zod";
import { FileWithPreview } from "@/types/general.types";

const addressSchema = z.object({
  street_address: z.string().optional(),
  city: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
});

export const purchaseOrderItemSchema = z.object({
  field_id: z.string(),// for form handle only
  description: z.string({ required_error: "Item description is required" }).min(1, "Item description is required"),
  quantity: z.number().min(0.01, "Quantity must be greater than 0"),
  unit_price: z.number().min(0, "Unit price must be greater than or equal to 0"),
  item_code: z.string().optional(),
  uom: z.string({ required_error: "Unit of measure is required" }).min(1, "Unit of measure is required"),
  discount_percentage: z.number().min(0).max(100, "Discount must be between 0 and 100"),
  tax_code: z.string().optional(),
  tax_rate: z.number().min(0).max(100, "Tax rate must be between 0 and 100"),
  gl_account_code: z.string().optional(),
  budget_item_id: z.string().optional(),
});

export const purchaseOrderSchema = z.object({
  supplier_id: z.string({ required_error: "Supplier is required" }).min(1, "Supplier is required"),
  title: z.string({ required_error: "Title is required" }).min(1, "Title is required").max(255, "Title must be less than 255 characters"),
  po_date: z.date({ required_error: "PO date is required" }),
  entity_id: z.string({ required_error: "Entity is required" }).min(1, "Entity is required"),
  description: z.string().optional(),
  expected_delivery_date: z.date().optional(),
  currency_code: z.string({ required_error: "Currency is required" }).min(1, "Currency is required"),
  exchange_rate: z.number().min(0.01, "Exchange rate must be greater than 0"),
  delivery_address: addressSchema.optional(),
  delivery_instructions: z.string().optional(),
  budget_revision_id: z.string().optional(),
  project_reference: z.string().optional(),
  department_code: z.string().optional(),
  items: z.array(purchaseOrderItemSchema).min(1, "At least one line item is required"),
  files: z.array(z.custom<FileWithPreview>()).optional(),
}).refine(
  (data) =>
    !data.expected_delivery_date || data.expected_delivery_date >= data.po_date,
  {
    message: "Expected delivery date must after PO date",
    path: ["expected_delivery_date"], // Shows the error on this field
  }
);

// Purchase Order form data type
export type PurchaseOrderFormData = z.infer<typeof purchaseOrderSchema>;
export type PurchaseOrderItemFormData = z.infer<typeof purchaseOrderItemSchema>;