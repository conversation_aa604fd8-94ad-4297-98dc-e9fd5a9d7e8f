import { z } from "zod";
import { isValidPhoneNumber } from "react-phone-number-input";

// Basic Info schema
const basicInfoSchema = z.object({
  supplier_code: z.string().optional(),
  name: z.string().min(1, "Name is required"),
  display_name: z.string().optional(),
  registration_number: z.string().optional(),
  tax_registration_number: z.string().optional(),
  supplier_type: z.string().min(1, "Supplier Type is required"),
  category: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "BLACKLISTED"], {
    required_error: "Status is required",
  }),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email format"),
  phone_number: z
    .string()
    .optional()
    .superRefine((val, ctx) => {
      if (val && val.trim() !== "" && !isValidPhoneNumber(val)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid phone number format",
        });
      }
    }),
  website: z.string().url("Invalid URL format").optional().or(z.literal("")),
});

// Address sub-schema
const singleAddressSchema = z.object({
  address_line_1: z.string().optional(),
  address_line_2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postal_code: z.string().optional(),
});

// Address group schema
const addressGroupSchema = z.object({
  billing_address: singleAddressSchema.optional(),
  shipping_address: singleAddressSchema.optional(),
  same_as_billing: z.boolean().optional(),
});

// Contact Person schema
const contactPersonSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Contact name is required"),
  title: z.string().optional(),
  email: z.string().email("Invalid email format"),
  phone_number: z
    .string()
    .optional()
    .superRefine((val, ctx) => {
      if (val && val.trim() !== "" && !isValidPhoneNumber(val)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid phone number format",
        });
      }
    }),
  is_primary: z.boolean().default(false),
});

// Bank Details schema
const bankDetailsSchema = z.object({
  id: z.string().optional(),
  bank_name: z.string().min(1, "Bank name is required"),
  account_number: z.string().min(1, "Account number is required"),
  swift_code: z.string().optional(),
  account_type: z.string().min(1, "Account type is required"),
});

// Financial schema
const financialSchema = z.object({
  currency_code: z.string({ required_error: "Currency code is required" }),
  payment_terms: z.number().min(0, "Payment terms must be 0 or greater").optional(),
  credit_limit: z.number().min(0, "Credit limit must be 0 or greater").optional(),
  is_gst_registered: z.boolean().default(false),
});

// Final supplier schema grouped into 5 sections
export const supplierSchema = z.object({
  basic_info: basicInfoSchema,
  address: addressGroupSchema,
  contact_persons: z.array(contactPersonSchema).optional(),
  bank_details: z.array(bankDetailsSchema).optional(),
  financial: financialSchema,
});

// Form data types
export type SupplierFormData = z.infer<typeof supplierSchema>;
export type ContactPersonFormData = z.infer<typeof contactPersonSchema>;
export type BankDetailsFormData = z.infer<typeof bankDetailsSchema>;