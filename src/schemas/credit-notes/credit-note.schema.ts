import { z } from "zod";

// Credit Note Item schema
export const creditNoteItemSchema = z.object({
  line_number: z.number().min(1, "Line number is required"),
  description: z.string().min(1, "Item description is required"),
  quantity: z.number().min(0.01, "Quantity must be greater than 0"),
  unit_price: z.number().min(0, "Unit price must be greater than or equal to 0"),
  discount_amount: z.number().min(0).optional(),
  discount_percentage: z.number().min(0).max(100, "Discount must be between 0 and 100").optional(),
  tax_percentage: z.number().min(0).max(100, "Tax rate must be between 0 and 100"),
  item_code: z.string().optional(),
  gl_account_code: z.string().optional(),
});

// Main Credit Note schema
export const creditNoteSchema = z.object({
  customer_id: z.string({ required_error: "Customer is required" }),
  entity_id: z.string({ required_error: "Entity is required" }),
  invoice_date: z.string({ required_error: "Credit note date is required" }),
  due_date: z.string().optional(),
  currency_code: z.string({ required_error: "Currency is required" }),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
  credit_reason: z.enum(["RETURN", "PRICING_ADJUSTMENT", "SERVICE_ISSUE", "PROMOTIONAL", "OTHER"], {
    required_error: "Credit reason is required"
  }),
  original_invoice_id: z.string().optional(),
  original_order_id: z.string().optional(),
  expiry_date: z.string().optional(),
  application_method: z.enum(["AUTO_APPLY", "MANUAL", "HOLD"], {
    required_error: "Application method is required"
  }),
  application_restrictions: z.string().optional(),
  items: z
    .array(creditNoteItemSchema)
    .min(1, "At least one line item is required"),
});

// Credit Note form data type
export type CreditNoteFormData = z.infer<typeof creditNoteSchema>;
export type CreditNoteItemFormData = z.infer<typeof creditNoteItemSchema>;