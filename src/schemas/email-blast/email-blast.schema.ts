import { z } from 'zod';
import { EmailCampaignStatus } from '@/types/email-blast';

// Email attachment schema
export const emailAttachmentSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  url: z.string().url('Invalid URL'),
  size: z.number().positive('Size must be positive'),
  mimeType: z.string().min(1, 'MIME type is required')
});

// Email content schema
export const emailContentSchema = z.object({
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject too long'),
  body: z.string().min(1, 'Body content is required'),
  bodyHtml: z.string().optional(),
  attachments: z.array(emailAttachmentSchema).optional(),
  templateId: z.string().optional(),
  variables: z.record(z.any()).optional()
});

// Email blast base schema (for creation)
export const emailBlastCreateSchema = z.object({
  name: z.string().min(1, 'Campaign name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  content: emailContentSchema,
  recipientListId: z.string().min(1, 'Recipient list is required'),
  scheduledAt: z.date().optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
});

// Email blast update schema
export const emailBlastUpdateSchema = emailBlastCreateSchema.partial().extend({
  status: z.nativeEnum(EmailCampaignStatus).optional()
});

// Email blast full schema (from database)
export const emailBlastSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  status: z.nativeEnum(EmailCampaignStatus),
  content: emailContentSchema,
  recipientListId: z.string(),
  recipientCount: z.number().optional(),
  sentCount: z.number().optional(),
  failedCount: z.number().optional(),
  openCount: z.number().optional(),
  clickCount: z.number().optional(),
  scheduledAt: z.date().optional(),
  sentAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  createdBy: z.string(),
  updatedBy: z.string(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
});

// Email statistics schema
export const emailBlastStatisticsSchema = z.object({
  campaignId: z.string(),
  totalRecipients: z.number().nonnegative(),
  sentCount: z.number().nonnegative(),
  deliveredCount: z.number().nonnegative(),
  openCount: z.number().nonnegative(),
  uniqueOpenCount: z.number().nonnegative(),
  clickCount: z.number().nonnegative(),
  uniqueClickCount: z.number().nonnegative(),
  bounceCount: z.number().nonnegative(),
  unsubscribeCount: z.number().nonnegative(),
  spamReportCount: z.number().nonnegative(),
  failedCount: z.number().nonnegative(),
  lastUpdated: z.date()
});

// Email recipient status
export const recipientStatusSchema = z.enum([
  'pending',
  'sent',
  'delivered',
  'opened',
  'clicked',
  'bounced',
  'failed',
  'unsubscribed'
]);

// Email blast recipient schema
export const emailBlastRecipientSchema = z.object({
  id: z.string(),
  campaignId: z.string(),
  recipientId: z.string(),
  email: z.string().email('Invalid email address'),
  name: z.string(),
  status: recipientStatusSchema,
  sentAt: z.date().optional(),
  deliveredAt: z.date().optional(),
  openedAt: z.date().optional(),
  clickedAt: z.date().optional(),
  bouncedAt: z.date().optional(),
  failedAt: z.date().optional(),
  unsubscribedAt: z.date().optional(),
  errorMessage: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

// Email schedule schema
export const emailScheduleSchema = z.object({
  type: z.enum(['immediate', 'scheduled', 'recurring']),
  scheduledAt: z.date().optional(),
  timezone: z.string().optional(),
  recurrence: z.object({
    frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
    interval: z.number().positive('Interval must be positive'),
    daysOfWeek: z.array(z.number().min(0).max(6)).optional(),
    dayOfMonth: z.number().min(1).max(31).optional(),
    endDate: z.date().optional(),
    occurrences: z.number().positive().optional()
  }).optional()
});

// Email settings schema
export const emailSettingsSchema = z.object({
  fromName: z.string().min(1, 'From name is required'),
  fromEmail: z.string().email('Invalid from email'),
  replyToEmail: z.string().email('Invalid reply-to email').optional(),
  unsubscribeUrl: z.string().url('Invalid unsubscribe URL').optional(),
  trackOpens: z.boolean().optional(),
  trackClicks: z.boolean().optional(),
  includeUnsubscribeLink: z.boolean().optional(),
  includeFooter: z.boolean().optional(),
  footerContent: z.string().optional()
});

// Email personalization schema
export const emailPersonalizationSchema = z.object({
  recipientId: z.string(),
  variables: z.record(z.any()),
  customSubject: z.string().optional(),
  customContent: z.string().optional()
});

// Campaign filter schema (for searching/filtering)
export const campaignFilterSchema = z.object({
  status: z.nativeEnum(EmailCampaignStatus).optional(),
  recipientListId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  dateRange: z.object({
    from: z.date().optional(),
    to: z.date().optional()
  }).optional(),
  searchTerm: z.string().optional()
});

// Campaign send request schema
export const campaignSendSchema = z.object({
  campaignId: z.string(),
  sendImmediately: z.boolean().default(true),
  scheduledAt: z.date().optional(),
  testRecipients: z.array(z.string().email()).optional(),
  settings: emailSettingsSchema.optional()
});

// Form data schema - for UI forms with flattened structure
export const emailBlastFormSchema = z.object({
  name: z.string().min(1, 'Campaign name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject too long'),
  from_name: z.string().min(1, 'From name is required'),
  from_email: z.string().email('Invalid from email'),
  reply_to_email: z.string().email('Invalid reply-to email').optional().or(z.literal('')),
  content_html: z.string().min(1, 'Email content is required'),
  content_text: z.string().optional(),
  recipientListId: z.string().min(1, 'Recipient list is required'),
  templateId: z.string().optional(),
  attachments: z.array(emailAttachmentSchema).optional(),
  variables: z.record(z.any()).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
});

// Type exports
export type EmailBlastCreate = z.infer<typeof emailBlastCreateSchema>;
export type EmailBlastUpdate = z.infer<typeof emailBlastUpdateSchema>;
export type EmailBlast = z.infer<typeof emailBlastSchema>;
export type EmailBlastFormData = z.infer<typeof emailBlastFormSchema>;
export type EmailBlastStatistics = z.infer<typeof emailBlastStatisticsSchema>;
export type EmailBlastRecipient = z.infer<typeof emailBlastRecipientSchema>;
export type EmailSchedule = z.infer<typeof emailScheduleSchema>;
export type EmailSettings = z.infer<typeof emailSettingsSchema>;
export type EmailPersonalization = z.infer<typeof emailPersonalizationSchema>;
export type CampaignFilter = z.infer<typeof campaignFilterSchema>;
export type CampaignSend = z.infer<typeof campaignSendSchema>;