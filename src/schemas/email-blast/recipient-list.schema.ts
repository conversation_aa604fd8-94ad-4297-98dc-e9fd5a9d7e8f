import { z } from 'zod';
import { MembershipStatus, FilterOperator } from '@/types/email-blast';

// Recipient filters schema
export const recipientFiltersSchema = z.object({
  membershipStatus: z.array(z.nativeEnum(MembershipStatus)).optional(),
  expiryDateRange: z.object({
    from: z.date().optional(),
    to: z.date().optional()
  }).optional(),
  membershipTypes: z.array(z.string()).optional(),
  customFields: z.array(z.object({
    fieldName: z.string(),
    operator: z.nativeEnum(FilterOperator),
    value: z.any()
  })).optional(),
  tags: z.array(z.string()).optional(),
  locations: z.array(z.string()).optional(),
  ageRange: z.object({
    min: z.number().min(0).optional(),
    max: z.number().max(150).optional()
  }).optional(),
  registrationDateRange: z.object({
    from: z.date().optional(),
    to: z.date().optional()
  }).optional(),
  lastActivityDateRange: z.object({
    from: z.date().optional(),
    to: z.date().optional()
  }).optional(),
  excludeUnsubscribed: z.boolean().optional(),
  excludeBounced: z.boolean().optional()
});

// Recipient list create schema
export const recipientListCreateSchema = z.object({
  name: z.string().min(1, 'List name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  filters: recipientFiltersSchema,
  isStatic: z.boolean().default(false),
  staticRecipientIds: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
});

// Recipient list update schema
export const recipientListUpdateSchema = recipientListCreateSchema.partial();

// Recipient list full schema (from database)
export const recipientListSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  filters: recipientFiltersSchema,
  recipientCount: z.number().nonnegative(),
  isStatic: z.boolean().optional(),
  staticRecipientIds: z.array(z.string()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  createdBy: z.string(),
  updatedBy: z.string().optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
});

// Recipient preview schema
export const recipientPreviewSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email('Invalid email address'),
  membershipType: z.string(),
  status: z.nativeEnum(MembershipStatus),
  expiryDate: z.date().optional(),
  location: z.string().optional(),
  phoneNumber: z.string().optional(),
  tags: z.array(z.string()).optional(),
  customFields: z.record(z.any()).optional()
});

// Recipient list statistics schema
export const recipientListStatisticsSchema = z.object({
  listId: z.string(),
  totalCount: z.number().nonnegative(),
  activeCount: z.number().nonnegative(),
  expiredCount: z.number().nonnegative(),
  pendingCount: z.number().nonnegative(),
  suspendedCount: z.number().nonnegative(),
  byMembershipType: z.record(z.number()),
  byLocation: z.record(z.number()).optional(),
  lastUpdated: z.date()
});

// Recipient import schema
export const recipientImportSchema = z.object({
  listId: z.string(),
  file: z.any(), // File will be handled separately
  mappings: z.record(z.string()), // Column mappings
  options: z.object({
    skipDuplicates: z.boolean().default(true),
    updateExisting: z.boolean().default(false),
    validateEmails: z.boolean().default(true),
    sendWelcomeEmail: z.boolean().default(false)
  }).optional()
});

// Recipient import result schema
export const recipientImportResultSchema = z.object({
  listId: z.string(),
  successCount: z.number().nonnegative(),
  failedCount: z.number().nonnegative(),
  duplicateCount: z.number().nonnegative(),
  errors: z.array(z.object({
    row: z.number(),
    email: z.string(),
    error: z.string()
  })),
  importedAt: z.date(),
  importedBy: z.string()
});

// Recipient add/remove schema (for static lists)
export const recipientManageSchema = z.object({
  listId: z.string(),
  recipientIds: z.array(z.string()).min(1, 'At least one recipient is required'),
  action: z.enum(['add', 'remove'])
});

// Recipient search schema
export const recipientSearchSchema = z.object({
  query: z.string().optional(),
  filters: recipientFiltersSchema.optional(),
  limit: z.number().positive().max(1000).default(100),
  offset: z.number().nonnegative().default(0),
  sortBy: z.enum(['name', 'email', 'joinDate', 'expiryDate', 'status']).optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

// List duplication schema
export const listDuplicateSchema = z.object({
  sourceListId: z.string(),
  newName: z.string().min(1, 'New list name is required'),
  includeRecipients: z.boolean().default(true)
});

// List merge schema
export const listMergeSchema = z.object({
  sourceListIds: z.array(z.string()).min(2, 'At least two lists required to merge'),
  newName: z.string().min(1, 'New list name is required'),
  mergeStrategy: z.enum(['union', 'intersection']).default('union'),
  removeDuplicates: z.boolean().default(true)
});

// Type exports
export type RecipientListCreate = z.infer<typeof recipientListCreateSchema>;
export type RecipientListUpdate = z.infer<typeof recipientListUpdateSchema>;
export type RecipientList = z.infer<typeof recipientListSchema>;
export type RecipientPreview = z.infer<typeof recipientPreviewSchema>;
export type RecipientListStatistics = z.infer<typeof recipientListStatisticsSchema>;
export type RecipientImport = z.infer<typeof recipientImportSchema>;
export type RecipientImportResult = z.infer<typeof recipientImportResultSchema>;
export type RecipientManage = z.infer<typeof recipientManageSchema>;
export type RecipientSearch = z.infer<typeof recipientSearchSchema>;
export type ListDuplicate = z.infer<typeof listDuplicateSchema>;
export type ListMerge = z.infer<typeof listMergeSchema>;