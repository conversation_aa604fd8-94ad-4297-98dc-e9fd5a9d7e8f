import { z } from 'zod';
import { Template<PERSON>ategory, TemplateStatus } from '@/types/email-blast/email-template.types';

// Template Variable Schema
export const templateVariableSchema = z.object({
  id: z.string().min(1, 'Variable ID is required'),
  name: z.string().min(1, 'Variable name is required').max(100, 'Variable name too long'),
  key: z.string()
    .min(1, 'Variable key is required')
    .max(50, 'Variable key too long')
    .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, 'Variable key must start with a letter and contain only letters, numbers, and underscores'),
  description: z.string().max(200, 'Description too long').optional(),
  type: z.enum(['text', 'number', 'date', 'boolean', 'url', 'email'], {
    required_error: 'Variable type is required'
  }),
  required: z.boolean().default(false),
  defaultValue: z.string().max(500, 'Default value too long').optional(),
  placeholder: z.string().max(100, 'Placeholder too long').optional(),
  validation: z.object({
    minLength: z.number().min(0).optional(),
    maxLength: z.number().min(1).optional(),
    pattern: z.string().optional(),
    options: z.array(z.string()).optional()
  }).optional()
}).refine((data) => {
  // If maxLength is provided, it should be greater than minLength
  if (data.validation?.minLength !== undefined && data.validation?.maxLength !== undefined) {
    return data.validation.maxLength >= data.validation.minLength;
  }
  return true;
}, {
  message: 'Maximum length must be greater than or equal to minimum length',
  path: ['validation', 'maxLength']
});

// Email Content Schema
export const emailContentSchema = z.object({
  subject: z.string()
    .min(1, 'Subject is required')
    .max(200, 'Subject too long'),
  body: z.string()
    .min(1, 'Email body is required')
    .max(500000, 'Email body too long'),
  bodyHtml: z.string()
    .max(1000000, 'HTML body too long')
    .optional(),
  previewText: z.string()
    .max(150, 'Preview text too long')
    .optional(),
  attachments: z.array(z.object({
    filename: z.string().min(1, 'Filename is required'),
    url: z.string().url('Invalid attachment URL'),
    size: z.number().min(0, 'File size must be positive'),
    mimeType: z.string().min(1, 'MIME type is required')
  })).optional(),
  variables: z.record(z.any()).optional()
});

// Template Form Schema
export const templateFormSchema = z.object({
  name: z.string()
    .min(1, 'Template name is required')
    .max(100, 'Template name too long')
    .trim(),
  description: z.string()
    .max(500, 'Description too long')
    .trim()
    .optional(),
  category: z.nativeEnum(TemplateCategory, {
    required_error: 'Category is required'
  }),
  status: z.nativeEnum(TemplateStatus, {
    required_error: 'Status is required'
  }),
  subject: z.string()
    .min(1, 'Subject is required')
    .max(200, 'Subject too long')
    .trim(),
  body: z.string()
    .min(1, 'Email body is required')
    .max(500000, 'Email body too long'),
  bodyHtml: z.string()
    .max(1000000, 'HTML body too long')
    .optional(),
  previewText: z.string()
    .max(150, 'Preview text too long')
    .trim()
    .optional(),
  variables: z.array(templateVariableSchema).default([]),
  tags: z.array(z.string().min(1).max(30).trim()).max(10, 'Too many tags').optional(),
  metadata: z.record(z.any()).optional()
}).refine((data) => {
  // Validate that all variables referenced in content exist
  const variableKeys = data.variables.map(v => v.key);
  const subjectVars = extractVariables(data.subject);
  const bodyVars = extractVariables(data.body);
  const htmlVars = data.bodyHtml ? extractVariables(data.bodyHtml) : [];
  
  const allContentVars = Array.from(new Set([...subjectVars, ...bodyVars, ...htmlVars]));
  const missingVars = allContentVars.filter(varKey => !variableKeys.includes(varKey));
  
  return missingVars.length === 0;
}, {
  message: 'All variables used in content must be defined in the variables array',
  path: ['variables']
});

// Template Duplicate Schema
export const templateDuplicateSchema = z.object({
  name: z.string()
    .min(1, 'New template name is required')
    .max(100, 'Template name too long')
    .trim(),
  description: z.string()
    .max(500, 'Description too long')
    .trim()
    .optional(),
  preserveVariables: z.boolean().default(true),
  copyTags: z.boolean().default(true)
});

// Template Preview Schema
export const templatePreviewSchema = z.object({
  templateId: z.string().min(1, 'Template ID is required'),
  variableValues: z.record(z.any()).default({}),
  recipientEmail: z.string().email('Invalid email address').optional()
});

// Template Filters Schema
export const templateFiltersSchema = z.object({
  category: z.nativeEnum(TemplateCategory).optional(),
  status: z.nativeEnum(TemplateStatus).optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().max(100).trim().optional(),
  createdBy: z.string().max(100).optional(),
  isSystem: z.boolean().optional()
});

// Variable Value Schema (for form validation when using templates)
export const variableValueSchema = z.object({
  key: z.string().min(1, 'Variable key is required'),
  value: z.any(),
  type: z.enum(['text', 'number', 'date', 'boolean', 'url', 'email'])
}).superRefine((data, ctx) => {
  const { value, type, key } = data;
  
  // Skip validation if value is empty and not required (handled elsewhere)
  if (value === '' || value === null || value === undefined) {
    return;
  }
  
  switch (type) {
    case 'text':
      if (typeof value !== 'string') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${key} must be text`,
          path: ['value']
        });
      }
      break;
      
    case 'number':
      const numValue = Number(value);
      if (isNaN(numValue)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${key} must be a valid number`,
          path: ['value']
        });
      }
      break;
      
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (typeof value !== 'string' || !emailRegex.test(value)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${key} must be a valid email address`,
          path: ['value']
        });
      }
      break;
      
    case 'url':
      try {
        new URL(value);
      } catch {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${key} must be a valid URL`,
          path: ['value']
        });
      }
      break;
      
    case 'date':
      const dateValue = new Date(value);
      if (isNaN(dateValue.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${key} must be a valid date`,
          path: ['value']
        });
      }
      break;
      
    case 'boolean':
      if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${key} must be true or false`,
          path: ['value']
        });
      }
      break;
  }
});

// Template Variable Values Schema (for validating all variables at once)
export const templateVariableValuesSchema = z.record(z.any()).superRefine((data, ctx) => {
  // This will be used with template-specific variable definitions
  // Individual variable validation will be handled by the component
  return true;
});

// Utility function to extract variables from content
function extractVariables(content: string): string[] {
  const variableRegex = /\{\{(\w+)\}\}/g;
  const variables: string[] = [];
  let match;
  
  while ((match = variableRegex.exec(content)) !== null) {
    if (!variables.includes(match[1])) {
      variables.push(match[1]);
    }
  }
  
  return variables;
}

// Export derived types
export type TemplateFormData = z.infer<typeof templateFormSchema>;
export type TemplateDuplicateData = z.infer<typeof templateDuplicateSchema>;
export type TemplatePreviewData = z.infer<typeof templatePreviewSchema>;
export type TemplateFiltersData = z.infer<typeof templateFiltersSchema>;
export type TemplateVariableData = z.infer<typeof templateVariableSchema>;
export type VariableValueData = z.infer<typeof variableValueSchema>;

// Validation helper functions
export const validateTemplateVariables = (
  variables: TemplateVariableData[],
  values: Record<string, any>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  for (const variable of variables) {
    const value = values[variable.key];
    
    // Check required variables
    if (variable.required && (value === undefined || value === null || value === '')) {
      errors.push(`${variable.name} is required`);
      continue;
    }
    
    // Skip further validation if value is empty and not required
    if (!value && !variable.required) {
      continue;
    }
    
    // Validate based on type
    try {
      variableValueSchema.parse({
        key: variable.key,
        value,
        type: variable.type
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        errors.push(...error.errors.map(e => e.message));
      }
    }
    
    // Additional validation rules
    if (variable.validation) {
      const val = String(value);
      
      if (variable.validation.minLength && val.length < variable.validation.minLength) {
        errors.push(`${variable.name} must be at least ${variable.validation.minLength} characters`);
      }
      
      if (variable.validation.maxLength && val.length > variable.validation.maxLength) {
        errors.push(`${variable.name} must be no more than ${variable.validation.maxLength} characters`);
      }
      
      if (variable.validation.pattern && !new RegExp(variable.validation.pattern).test(val)) {
        errors.push(`${variable.name} format is invalid`);
      }
      
      if (variable.validation.options && !variable.validation.options.includes(val)) {
        errors.push(`${variable.name} must be one of: ${variable.validation.options.join(', ')}`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateTemplateContent = (
  content: string,
  variables: TemplateVariableData[]
): { isValid: boolean; missingVariables: string[]; unusedVariables: string[] } => {
  const contentVars = extractVariables(content);
  const definedVars = variables.map(v => v.key);
  
  const missingVariables = contentVars.filter(varKey => !definedVars.includes(varKey));
  const unusedVariables = definedVars.filter(varKey => !contentVars.includes(varKey));
  
  return {
    isValid: missingVariables.length === 0,
    missingVariables,
    unusedVariables
  };
};