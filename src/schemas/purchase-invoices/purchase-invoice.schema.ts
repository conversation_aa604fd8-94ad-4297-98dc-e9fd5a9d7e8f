import { z } from "zod";

// Purchase Invoice Item schema
export const purchaseInvoiceItemSchema = z.object({
  po_item_id: z.string().optional(),
  line_number: z.number().min(1, "Line number is required"),
  description: z.string().min(1, "Item description is required"),
  quantity: z.number().min(0.01, "Quantity must be greater than 0"),
  unit_price: z.number().min(0, "Unit price must be greater than or equal to 0"),
  discount_amount: z.number().min(0).optional(),
  discount_percentage: z.number().min(0).max(100, "Discount must be between 0 and 100").optional(),
  tax_percentage: z.number().min(0).max(100, "Tax rate must be between 0 and 100"),
  item_code: z.string().optional(),
  gl_account_code: z.string().optional(),
  cost_center: z.string().optional(),
  budget_item_id: z.string().optional(),
});

// Main Purchase Invoice schema
export const purchaseInvoiceSchema = z.object({
  supplier_id: z.string({ required_error: "Supplier is required" }),
  entity_id: z.string({ required_error: "Entity is required" }),
  purchase_order_id: z.string().optional(),
  invoice_number: z.string().min(1, "Invoice number is required"),
  supplier_reference: z.string().optional(),
  invoice_date: z.string({ required_error: "Invoice date is required" }),
  due_date: z.string().optional(),
  currency_code: z.string({ required_error: "Currency is required" }),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
  perform_three_way_match: z.boolean().default(false),
  items: z
    .array(purchaseInvoiceItemSchema)
    .min(1, "At least one line item is required"),
});

// Purchase Invoice form data type
export type PurchaseInvoiceFormData = z.infer<typeof purchaseInvoiceSchema>;
export type PurchaseInvoiceItemFormData = z.infer<typeof purchaseInvoiceItemSchema>;