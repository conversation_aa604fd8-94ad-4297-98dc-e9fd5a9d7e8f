import { createClient } from '@/utils/supabase/Client';
import { Database } from '@/types/email_system.types';
import { PostgrestError } from '@supabase/supabase-js';

// Type aliases for better readability
type EmailSystemTables = Database['email_system']['Tables'];
type QueryResult<T> = { data: T | null; error: PostgrestError | null };

// ==================== SCHEMA HELPER ====================

/**
 * Creates a Supabase client with email_system schema
 * CRITICAL: Always use this helper for email system queries
 */
export const emailDB = async () => {
  const supabase = await createClient();
  return supabase.schema('email_system');
};

// ==================== ERROR HANDLING ====================

export class EmailSystemError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'EmailSystemError';
  }
}

/**
 * Wraps a database query with error handling
 */
export async function withErrorHandling<T>(
  queryFn: () => Promise<QueryResult<T>>,
  errorMessage: string
): Promise<T> {
  try {
    const { data, error } = await queryFn();
    
    if (error) {
      throw new EmailSystemError(
        `${errorMessage}: ${error.message}`,
        error.code,
        error.details
      );
    }
    
    if (!data) {
      throw new EmailSystemError(`${errorMessage}: No data returned`);
    }
    
    return data;
  } catch (error) {
    if (error instanceof EmailSystemError) {
      throw error;
    }
    throw new EmailSystemError(
      `${errorMessage}: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

// ==================== PAGINATION ====================

export interface PaginationParams {
  page?: number;
  limit?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Applies pagination to a query
 */
export function applyPagination(
  query: any,
  params: PaginationParams
): any {
  const { page = 1, limit = 10, orderBy = 'created_at', orderDirection = 'desc' } = params;
  const offset = (page - 1) * limit;
  
  return query
    .order(orderBy, { ascending: orderDirection === 'asc' })
    .range(offset, offset + limit - 1);
}

/**
 * Gets total count for pagination
 */
export async function getTotalCount(
  tableName: keyof EmailSystemTables,
  filters?: Record<string, any>
): Promise<number> {
  const db = await emailDB();
  let query = db.from(tableName).select('*', { count: 'exact', head: true });
  
  // Apply filters
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });
  }
  
  const { count, error } = await query;
  
  if (error) {
    throw new EmailSystemError(`Failed to get count: ${error.message}`, error.code);
  }
  
  return count || 0;
}

// ==================== FILTER BUILDERS ====================

export interface FilterParams {
  search?: string;
  searchFields?: string[];
  filters?: Record<string, any>;
  dateRange?: {
    field: string;
    from?: Date;
    to?: Date;
  };
  inFilters?: Record<string, any[]>;
}

/**
 * Applies filters to a query
 */
export function applyFilters(
  query: any,
  params: FilterParams
): any {
  let filteredQuery = query;
  
  // Apply search
  if (params.search && params.searchFields?.length) {
    const searchConditions = params.searchFields
      .map(field => `${field}.ilike.%${params.search}%`)
      .join(',');
    filteredQuery = filteredQuery.or(searchConditions);
  }
  
  // Apply exact filters
  if (params.filters) {
    Object.entries(params.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        filteredQuery = filteredQuery.eq(key, value);
      }
    });
  }
  
  // Apply date range filter
  if (params.dateRange) {
    const { field, from, to } = params.dateRange;
    if (from) {
      filteredQuery = filteredQuery.gte(field, from.toISOString());
    }
    if (to) {
      filteredQuery = filteredQuery.lte(field, to.toISOString());
    }
  }
  
  // Apply IN filters
  if (params.inFilters) {
    Object.entries(params.inFilters).forEach(([key, values]) => {
      if (values && values.length > 0) {
        filteredQuery = filteredQuery.in(key, values);
      }
    });
  }
  
  return filteredQuery;
}

// ==================== QUERY BUILDERS ====================

/**
 * Base query builder class
 */
export class QueryBuilder<T> {
  private query: any;
  private tableName: keyof EmailSystemTables;
  
  constructor(tableName: keyof EmailSystemTables, initialQuery?: any) {
    this.tableName = tableName;
    this.query = initialQuery;
  }
  
  static async from<T>(tableName: keyof EmailSystemTables) {
    const db = await emailDB();
    const query = db.from(tableName);
    return new QueryBuilder<T>(tableName, query);
  }
  
  select(columns: string = '*') {
    this.query = this.query.select(columns);
    return this;
  }
  
  filter(params: FilterParams) {
    this.query = applyFilters(this.query, params);
    return this;
  }
  
  paginate(params: PaginationParams) {
    this.query = applyPagination(this.query, params);
    return this;
  }
  
  eq(column: string, value: any) {
    this.query = this.query.eq(column, value);
    return this;
  }
  
  neq(column: string, value: any) {
    this.query = this.query.neq(column, value);
    return this;
  }
  
  gt(column: string, value: any) {
    this.query = this.query.gt(column, value);
    return this;
  }
  
  gte(column: string, value: any) {
    this.query = this.query.gte(column, value);
    return this;
  }
  
  lt(column: string, value: any) {
    this.query = this.query.lt(column, value);
    return this;
  }
  
  lte(column: string, value: any) {
    this.query = this.query.lte(column, value);
    return this;
  }
  
  in(column: string, values: any[]) {
    this.query = this.query.in(column, values);
    return this;
  }
  
  isNull(column: string) {
    this.query = this.query.is(column, null);
    return this;
  }
  
  order(column: string, ascending: boolean = true) {
    this.query = this.query.order(column, { ascending });
    return this;
  }
  
  limit(count: number) {
    this.query = this.query.limit(count);
    return this;
  }
  
  async execute(): Promise<T[]> {
    return withErrorHandling(
      async () => await this.query,
      `Failed to execute query on ${String(this.tableName)}`
    );
  }
  
  async single(): Promise<T> {
    return withErrorHandling(
      async () => await this.query.single(),
      `Failed to get single record from ${String(this.tableName)}`
    );
  }
  
  async maybeSingle(): Promise<T | null> {
    const { data, error } = await this.query.maybeSingle();
    if (error) {
      throw new EmailSystemError(
        `Failed to get record from ${String(this.tableName)}: ${error.message}`,
        error.code
      );
    }
    return data;
  }
  
  async count(): Promise<number> {
    const { count, error } = await this.query.select('*', { count: 'exact', head: true });
    if (error) {
      throw new EmailSystemError(
        `Failed to get count from ${String(this.tableName)}: ${error.message}`,
        error.code
      );
    }
    return count || 0;
  }
}

// ==================== SPECIFIC QUERY HELPERS ====================

/**
 * Email Campaign specific queries
 */
export const CampaignQueries = {
  async getWithStatistics(campaignId: string) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => await db
        .from('email_campaigns')
        .select(`
          *,
          campaign_statistics(*)
        `)
        .eq('id', campaignId)
        .single(),
      'Failed to get campaign with statistics'
    );
  },
  
  async getWithRecipients(campaignId: string, pagination?: PaginationParams) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => {
        let query = db
          .from('campaign_recipients')
          .select('*')
          .eq('campaign_id', campaignId);
        
        if (pagination) {
          query = applyPagination(query, pagination);
        }
        
        return await query;
      },
      'Failed to get campaign recipients'
    );
  },
  
  async updateStatistics(campaignId: string) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => await db.rpc('update_campaign_statistics', { p_campaign_id: campaignId }),
      'Failed to update campaign statistics'
    );
  },
};

/**
 * Recipient List specific queries
 */
export const RecipientListQueries = {
  async calculateRecipients(listId: string) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => await db.rpc('calculate_recipient_list', { p_list_id: listId }),
      'Failed to calculate recipient list'
    );
  },
  
  async getMembers(listId: string, pagination?: PaginationParams) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => {
        let query = db
          .from('recipient_list_members')
          .select('*')
          .eq('list_id', listId)
          .eq('is_active', true);
        
        if (pagination) {
          query = applyPagination(query, pagination);
        }
        
        return await query;
      },
      'Failed to get list members'
    );
  },
};

/**
 * Email Template specific queries
 */
export const TemplateQueries = {
  async getActiveTemplates(category?: string) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => {
        let query = db
          .from('email_templates')
          .select('*')
          .eq('active', true)
          .eq('is_blast_template', true);
        
        if (category) {
          query = query.eq('category', category);
        }
        
        return await query.order('name');
      },
      'Failed to get active templates'
    );
  },
  
  async getWithUsageStats(templateId: string) {
    const db = await emailDB();
    
    return withErrorHandling(
      async () => await db
        .from('email_templates')
        .select(`
          *,
          template_usage_stats(*)
        `)
        .eq('id', templateId)
        .single(),
      'Failed to get template with usage stats'
    );
  },
};

// ==================== TRANSACTION HELPERS ====================

/**
 * Executes multiple queries in a transaction-like manner
 * Note: Supabase doesn't support true transactions, so this provides
 * a way to batch operations and rollback on failure
 */
export async function executeTransaction<T>(
  operations: Array<() => Promise<any>>,
  rollbackOperations?: Array<() => Promise<any>>
): Promise<T[]> {
  const results: T[] = [];
  const completedOperations: number[] = [];
  
  try {
    for (let i = 0; i < operations.length; i++) {
      const result = await operations[i]();
      results.push(result);
      completedOperations.push(i);
    }
    return results;
  } catch (error) {
    // Attempt rollback if provided
    if (rollbackOperations && completedOperations.length > 0) {
      for (let i = completedOperations.length - 1; i >= 0; i--) {
        try {
          await rollbackOperations[completedOperations[i]]();
        } catch (rollbackError) {
          console.error('Rollback failed:', rollbackError);
        }
      }
    }
    
    throw error;
  }
}

// ==================== BATCH OPERATIONS ====================

/**
 * Performs batch insert with chunking
 */
export async function batchInsert<T>(
  tableName: keyof EmailSystemTables,
  records: T[],
  chunkSize: number = 100
): Promise<void> {
  const db = await emailDB();
  
  for (let i = 0; i < records.length; i += chunkSize) {
    const chunk = records.slice(i, i + chunkSize);
    
    await withErrorHandling(
      async () => await db.from(tableName).insert(chunk),
      `Failed to batch insert into ${String(tableName)}`
    );
  }
}

/**
 * Performs batch update with chunking
 */
export async function batchUpdate<T extends { id: string }>(
  tableName: keyof EmailSystemTables,
  records: T[],
  chunkSize: number = 100
): Promise<void> {
  const db = await emailDB();
  
  for (let i = 0; i < records.length; i += chunkSize) {
    const chunk = records.slice(i, i + chunkSize);
    
    // Supabase doesn't support bulk updates, so we need to do them individually
    await Promise.all(
      chunk.map(record =>
        withErrorHandling(
          async () => await db.from(tableName).update(record).eq('id', record.id),
          `Failed to update record ${record.id} in ${String(tableName)}`
        )
      )
    );
  }
}