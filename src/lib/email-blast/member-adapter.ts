import { RecipientPreview, RecipientFilters, MembershipStatus } from '@/types/email-blast/recipient-list.types';
import { emailDB, withErrorHandling, QueryBuilder, applyFilters, FilterParams } from './db-queries';
import { mapUserToRecipientPreview } from './type-mappers';

// ==================== MEMBER TABLE INTERFACE ====================

/**
 * Interface defining the expected structure of member/user tables
 * This allows the adapter to work with different table schemas
 */
export interface MemberTableConfig {
  tableName: string;
  schema?: string; // Default is 'public'
  fields: {
    id: string;
    email: string;
    name?: string; // Could be 'name', 'full_name', or separate first/last
    firstName?: string;
    lastName?: string;
    status?: string;
    membershipType?: string;
    membershipExpiry?: string;
    location?: string;
    phone?: string;
    tags?: string;
    createdAt?: string;
    updatedAt?: string;
    // Any additional custom fields
    [key: string]: string | undefined;
  };
}

// Default configuration for common user/member tables
export const DEFAULT_USER_TABLE_CONFIG: MemberTableConfig = {
  tableName: 'users',
  schema: 'public',
  fields: {
    id: 'id',
    email: 'email',
    name: 'name',
    status: 'status',
    membershipType: 'membership_type',
    membershipExpiry: 'membership_expiry',
    location: 'location',
    phone: 'phone',
    tags: 'tags',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
};

export const DEFAULT_MEMBER_TABLE_CONFIG: MemberTableConfig = {
  tableName: 'members',
  schema: 'public',
  fields: {
    id: 'id',
    email: 'email',
    firstName: 'first_name',
    lastName: 'last_name',
    status: 'membership_status',
    membershipType: 'membership_type',
    membershipExpiry: 'expiry_date',
    location: 'address',
    phone: 'phone_number',
    tags: 'tags',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
};

// ==================== MEMBER DATA ADAPTER ====================

export class MemberDataAdapter {
  private config: MemberTableConfig;
  
  constructor(config: MemberTableConfig = DEFAULT_USER_TABLE_CONFIG) {
    this.config = config;
  }
  
  /**
   * Get members based on recipient filters
   */
  async getMembersByFilters(
    filters: RecipientFilters,
    pagination?: { page: number; limit: number }
  ): Promise<{ members: RecipientPreview[]; total: number }> {
    // Get the client first
    const client = await this.getClient();
    
    // Build count query
    let countQuery = client.from(this.config.tableName).select('*', { count: 'exact', head: true });
    countQuery = this.applyFiltersToQuery(countQuery, filters);
    const { count, error: countError } = await countQuery;
    
    if (countError) {
      throw new Error(`Failed to get count: ${countError.message}`);
    }
    
    const total = count || 0;
    
    // Build data query
    let query = client.from(this.config.tableName).select('*');
    query = this.applyFiltersToQuery(query, filters);
    
    // Apply pagination before executing
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Failed to get members: ${error.message}`);
    }
    
    const members = ((data as any[]) || []).map(this.mapToRecipientPreview.bind(this));
    
    return { members, total };
  }
  
  /**
   * Get members by their IDs
   */
  async getMembersByIds(ids: string[]): Promise<RecipientPreview[]> {
    if (!ids.length) return [];
    
    const client = await this.getClient();
    const query = client
      .from(this.config.tableName)
      .select('*')
      .in(this.config.fields.id, ids);
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Failed to get members by IDs: ${error.message}`);
    }
    
    return (data as any[]).map(this.mapToRecipientPreview.bind(this));
  }
  
  /**
   * Search members by email or name
   */
  async searchMembers(
    searchTerm: string,
    limit: number = 10
  ): Promise<RecipientPreview[]> {
    const client = await this.getClient();
    
    // Build search conditions
    const searchFields = [];
    if (this.config.fields.email) {
      searchFields.push(`${this.config.fields.email}.ilike.%${searchTerm}%`);
    }
    if (this.config.fields.name) {
      searchFields.push(`${this.config.fields.name}.ilike.%${searchTerm}%`);
    }
    if (this.config.fields.firstName && this.config.fields.lastName) {
      searchFields.push(`${this.config.fields.firstName}.ilike.%${searchTerm}%`);
      searchFields.push(`${this.config.fields.lastName}.ilike.%${searchTerm}%`);
    }
    
    const query = client
      .from(this.config.tableName)
      .select('*')
      .or(searchFields.join(','))
      .limit(limit);
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Failed to search members: ${error.message}`);
    }
    
    return (data as any[]).map(this.mapToRecipientPreview.bind(this));
  }
  
  /**
   * Get member emails for email blast
   */
  async getMemberEmails(filters: RecipientFilters): Promise<string[]> {
    const client = await this.getClient();
    let query = client
      .from(this.config.tableName)
      .select(this.config.fields.email);
    
    // Apply the same filters
    query = this.applyFiltersToQuery(query, filters);
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Failed to get member emails: ${error.message}`);
    }
    
    return ((data as any[]) || []).map(row => row[this.config.fields.email]);
  }
  
  /**
   * Check if email exists and is not suppressed
   */
  async isEmailValid(email: string): Promise<boolean> {
    // Check if email exists in member table
    const client = await this.getClient();
    const memberQuery = client
      .from(this.config.tableName)
      .select(this.config.fields.id)
      .eq(this.config.fields.email, email)
      .single();
    
    const { data: member, error: memberError } = await memberQuery;
    
    if (memberError || !member) {
      return false;
    }
    
    // Check if email is suppressed
    const db = await emailDB();
    const suppressionQuery = db
      .from('suppression_list')
      .select('id')
      .eq('email_address', email)
      .eq('is_active', true)
      .single();
    
    const { data: suppression } = await suppressionQuery;
    
    return !suppression;
  }
  
  // ==================== PRIVATE METHODS ====================
  
  private async getClient() {
    const { createClient } = await import('@/utils/supabase/Client');
    const supabase = await createClient();
    
    if (this.config.schema === 'public') {
      return supabase;
    } else {
      return supabase.schema(this.config.schema!);
    }
  }
  
  
  private applyFiltersToQuery(query: any, filters: RecipientFilters) {
    // Apply membership status filter
    if (filters.membershipStatus?.length && this.config.fields.status) {
      const statusValues = filters.membershipStatus.map(s => this.mapStatusToDb(s));
      query = query.in(this.config.fields.status, statusValues);
    }
    
    // Apply membership type filter
    if (filters.membershipTypes?.length && this.config.fields.membershipType) {
      query = query.in(this.config.fields.membershipType, filters.membershipTypes);
    }
    
    // Apply expiry date range filter
    if (filters.expiryDateRange && this.config.fields.membershipExpiry) {
      if (filters.expiryDateRange.from) {
        query = query.gte(
          this.config.fields.membershipExpiry,
          filters.expiryDateRange.from.toISOString()
        );
      }
      if (filters.expiryDateRange.to) {
        query = query.lte(
          this.config.fields.membershipExpiry,
          filters.expiryDateRange.to.toISOString()
        );
      }
    }
    
    // Apply registration date range filter
    if (filters.registrationDateRange && this.config.fields.createdAt) {
      if (filters.registrationDateRange.from) {
        query = query.gte(
          this.config.fields.createdAt,
          filters.registrationDateRange.from.toISOString()
        );
      }
      if (filters.registrationDateRange.to) {
        query = query.lte(
          this.config.fields.createdAt,
          filters.registrationDateRange.to.toISOString()
        );
      }
    }
    
    // Apply location filter
    if (filters.locations?.length && this.config.fields.location) {
      query = query.in(this.config.fields.location, filters.locations);
    }
    
    // Apply tags filter
    if (filters.tags?.length && this.config.fields.tags) {
      // Assuming tags are stored as an array
      query = query.contains(this.config.fields.tags, filters.tags);
    }
    
    // Apply custom fields filter
    if (filters.customFields) {
      Object.entries(filters.customFields).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Check if the field is mapped
          const fieldName = this.config.fields[key] || key;
          query = query.eq(fieldName, value);
        }
      });
    }
    
    // Exclude unsubscribed if requested
    if (filters.excludeUnsubscribed) {
      // Join with email_suppressions table
      // Note: This requires a proper join or subquery which might need RPC
      // For now, we'll handle this in post-processing
    }
    
    // Exclude bounced if requested
    if (filters.excludeBounced) {
      // Similar to unsubscribed, might need post-processing
    }
    
    return query;
  }
  
  
  private mapToRecipientPreview(data: any): RecipientPreview {
    const fields = this.config.fields;
    
    // Build name from available fields
    let name = '';
    if (fields.name && data[fields.name]) {
      name = data[fields.name];
    } else if (fields.firstName && fields.lastName) {
      const firstName = data[fields.firstName] || '';
      const lastName = data[fields.lastName] || '';
      name = `${firstName} ${lastName}`.trim();
    }
    
    // Map the data using the generic mapper
    const mapped = mapUserToRecipientPreview({
      id: data[fields.id],
      email: data[fields.email],
      name: name || 'Unknown',
      membership_type: fields.membershipType ? data[fields.membershipType] : undefined,
      status: fields.status ? data[fields.status] : 'active',
      expiry_date: fields.membershipExpiry ? data[fields.membershipExpiry] : undefined,
      location: fields.location ? data[fields.location] : undefined,
      phone: fields.phone ? data[fields.phone] : undefined,
      tags: fields.tags ? data[fields.tags] : [],
      ...data, // Include all other fields as custom fields
    });
    
    return mapped;
  }
  
  private mapStatusToDb(status: MembershipStatus): string {
    const statusMap: Record<MembershipStatus, string> = {
      [MembershipStatus.ACTIVE]: 'ACTIVE',
      [MembershipStatus.EXPIRED]: 'EXPIRED',
      [MembershipStatus.PENDING]: 'PENDING',
      [MembershipStatus.SUSPENDED]: 'SUSPENDED',
      [MembershipStatus.INACTIVE]: 'INACTIVE',
      [MembershipStatus.TERMINATED]: 'TERMINATED',
      [MembershipStatus.PENDING_RENEWAL]: 'PENDING_RENEWAL',
      [MembershipStatus.PENDING_RE_APPLICATION]: 'PENDING_RE_APPLICATION',
    };
    return statusMap[status];
  }
}

// ==================== FACTORY FUNCTIONS ====================

/**
 * Creates a member adapter for the default user table
 */
export function createUserAdapter(customConfig?: Partial<MemberTableConfig>): MemberDataAdapter {
  const config = {
    ...DEFAULT_USER_TABLE_CONFIG,
    ...customConfig,
    fields: {
      ...DEFAULT_USER_TABLE_CONFIG.fields,
      ...(customConfig?.fields || {}),
    },
  };
  return new MemberDataAdapter(config);
}

/**
 * Creates a member adapter for the default member table
 */
export function createMemberAdapter(customConfig?: Partial<MemberTableConfig>): MemberDataAdapter {
  const config = {
    ...DEFAULT_MEMBER_TABLE_CONFIG,
    ...customConfig,
    fields: {
      ...DEFAULT_MEMBER_TABLE_CONFIG.fields,
      ...(customConfig?.fields || {}),
    },
  };
  return new MemberDataAdapter(config);
}

/**
 * Auto-detects the member table and creates an appropriate adapter
 */
export async function createAutoAdapter(): Promise<MemberDataAdapter> {
  const { createClient } = await import('@/utils/supabase/Client');
  const supabase = await createClient();
  
  // Try to detect if 'users' table exists
  const { data: usersTable } = await supabase
    .from('users')
    .select('id')
    .limit(1);
  
  if (usersTable !== null) {
    return createUserAdapter();
  }
  
  // Try to detect if 'members' table exists
  const { data: membersTable } = await supabase
    .from('members')
    .select('id')
    .limit(1);
  
  if (membersTable !== null) {
    return createMemberAdapter();
  }
  
  // Default to users table
  return createUserAdapter();
}

// ==================== INTEGRATION HELPERS ====================

/**
 * Syncs members to recipient list members table
 */
export async function syncMembersToRecipientList(
  listId: string,
  filters: RecipientFilters,
  adapter?: MemberDataAdapter
): Promise<number> {
  const memberAdapter = adapter || await createAutoAdapter();
  const { members } = await memberAdapter.getMembersByFilters(filters);
  
  // Clear existing members for dynamic lists
  const db = await emailDB();
  const { error: deleteError } = await db
    .from('recipient_list_members')
    .delete()
    .eq('list_id', listId);
  
  if (deleteError) {
    throw new Error(`Failed to clear existing list members: ${deleteError.message}`);
  }
  
  // Add new members
  const listMembers = members.map(member => ({
    list_id: listId,
    email: member.email,
    name: member.name,
    member_data: {
      id: member.id,
      membershipType: member.membershipType,
      status: member.status,
      location: member.location,
      phoneNumber: member.phoneNumber,
      tags: member.tags,
      customFields: member.customFields,
    },
    is_active: true,
  }));
  
  // Batch insert
  if (listMembers.length > 0) {
    const chunkSize = 100;
    for (let i = 0; i < listMembers.length; i += chunkSize) {
      const chunk = listMembers.slice(i, i + chunkSize);
      const { error: insertError } = await db
        .from('recipient_list_members')
        .insert(chunk);
      
      if (insertError) {
        throw new Error(`Failed to insert list members: ${insertError.message}`);
      }
    }
  }
  
  return listMembers.length;
}