import { RecipientFilters, FilterOperator, CustomFieldFilter } from '@/types/email-blast/recipient-list.types';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Filter Query Builder for Email Blast Recipients
 * Converts RecipientFilters to Supabase queries
 */
export class FilterQueryBuilder {
  private client: SupabaseClient;
  private tableName: string;

  constructor(client: SupabaseClient, tableName: string = 'members') {
    this.client = client;
    this.tableName = tableName;
  }

  /**
   * Build a Supabase query from RecipientFilters
   */
  buildQuery(filters: RecipientFilters): any {
    let query = this.client.from(this.tableName).select('*');

    // Apply membership status filters
    if (filters.membershipStatus?.length) {
      query = query.in('membership_status', filters.membershipStatus);
    }

    // Apply membership type filters
    if (filters.membershipTypes?.length) {
      query = query.in('membership_type', filters.membershipTypes);
    }

    // Apply location filters
    if (filters.locations?.length) {
      query = query.in('location', filters.locations);
    }

    // Apply expiry date range filters
    if (filters.expiryDateRange) {
      if (filters.expiryDateRange.from) {
        query = query.gte('expiry_date', filters.expiryDateRange.from.toISOString());
      }
      if (filters.expiryDateRange.to) {
        query = query.lte('expiry_date', filters.expiryDateRange.to.toISOString());
      }
    }

    // Apply age range filters
    if (filters.ageRange) {
      const currentYear = new Date().getFullYear();
      if (filters.ageRange.min) {
        const maxBirthYear = currentYear - filters.ageRange.min;
        query = query.lte('birth_year', maxBirthYear);
      }
      if (filters.ageRange.max) {
        const minBirthYear = currentYear - filters.ageRange.max;
        query = query.gte('birth_year', minBirthYear);
      }
    }

    // Apply tag filters
    if (filters.tags?.length) {
      // Assuming tags are stored as an array in the database
      query = query.contains('tags', filters.tags);
    }

    // Apply custom fields
    if (filters.customFields?.length) {
      filters.customFields.forEach(field => {
        query = this.applyCustomFieldFilter(query, field);
      });
    }

    // Apply exclude filters
    if (filters.excludeUnsubscribed || filters.excludeBounced) {
      query = this.applySuppressionFilters(query, filters);
    }

    return query;
  }

  /**
   * Apply custom field filter based on operator
   */
  private applyCustomFieldFilter(query: any, field: CustomFieldFilter): any {
    const { fieldName, operator, value } = field;

    switch (operator) {
      case FilterOperator.EQUALS:
        return query.eq(fieldName, value);
      
      case FilterOperator.NOT_EQUALS:
        return query.neq(fieldName, value);
      
      case FilterOperator.CONTAINS:
        return query.ilike(fieldName, `%${value}%`);
      
      case FilterOperator.NOT_CONTAINS:
        return query.not(fieldName, 'ilike', `%${value}%`);
      
      case FilterOperator.STARTS_WITH:
        return query.ilike(fieldName, `${value}%`);
      
      case FilterOperator.ENDS_WITH:
        return query.ilike(fieldName, `%${value}`);
      
      case FilterOperator.GREATER_THAN:
        return query.gt(fieldName, value);
      
      case FilterOperator.LESS_THAN:
        return query.lt(fieldName, value);
      
      case FilterOperator.GREATER_THAN_OR_EQUAL:
        return query.gte(fieldName, value);
      
      case FilterOperator.LESS_THAN_OR_EQUAL:
        return query.lte(fieldName, value);
      
      case FilterOperator.IS_EMPTY:
        return query.or(`${fieldName}.is.null,${fieldName}.eq.`);
      
      case FilterOperator.IS_NOT_EMPTY:
        return query.not(fieldName, 'is', null).not(fieldName, 'eq', '');
      
      case FilterOperator.IN:
        return Array.isArray(value) ? query.in(fieldName, value) : query.eq(fieldName, value);
      
      case FilterOperator.NOT_IN:
        return Array.isArray(value) ? query.not(fieldName, 'in', value) : query.neq(fieldName, value);
      
      default:
        return query;
    }
  }

  /**
   * Apply suppression filters (unsubscribed, bounced)
   */
  private applySuppressionFilters(query: any, filters: RecipientFilters): any {
    // This would typically involve a join or subquery with email_suppressions table
    // For now, we'll add a note that this needs to be handled differently
    
    // In practice, you might need to:
    // 1. First get suppressed emails from email_system.email_suppressions
    // 2. Then exclude them from the main query
    // OR use a more complex query with joins
    
    // Placeholder implementation:
    if (filters.excludeUnsubscribed) {
      // Assuming there's an is_subscribed field
      query = query.neq('is_subscribed', false);
    }

    return query;
  }

  /**
   * Build count query (optimized for counting)
   */
  buildCountQuery(filters: RecipientFilters): any {
    let query = this.buildQuery(filters);
    return query.select('*', { count: 'exact', head: true });
  }

  /**
   * Build SQL WHERE clause from filters (for RPC functions)
   */
  buildWhereClause(filters: RecipientFilters): string {
    const conditions: string[] = [];

    // Membership status
    if (filters.membershipStatus?.length) {
      const values = filters.membershipStatus.map(s => `'${s}'`).join(',');
      conditions.push(`membership_status IN (${values})`);
    }

    // Membership types
    if (filters.membershipTypes?.length) {
      const values = filters.membershipTypes.map(t => `'${t}'`).join(',');
      conditions.push(`membership_type IN (${values})`);
    }

    // Locations
    if (filters.locations?.length) {
      const values = filters.locations.map(l => `'${l}'`).join(',');
      conditions.push(`location IN (${values})`);
    }

    // Expiry date range
    if (filters.expiryDateRange) {
      if (filters.expiryDateRange.from) {
        conditions.push(`expiry_date >= '${filters.expiryDateRange.from.toISOString()}'`);
      }
      if (filters.expiryDateRange.to) {
        conditions.push(`expiry_date <= '${filters.expiryDateRange.to.toISOString()}'`);
      }
    }

    // Age range
    if (filters.ageRange) {
      const currentYear = new Date().getFullYear();
      if (filters.ageRange.min) {
        conditions.push(`birth_year <= ${currentYear - filters.ageRange.min}`);
      }
      if (filters.ageRange.max) {
        conditions.push(`birth_year >= ${currentYear - filters.ageRange.max}`);
      }
    }

    // Tags
    if (filters.tags?.length) {
      const tagConditions = filters.tags.map(tag => `'${tag}' = ANY(tags)`);
      conditions.push(`(${tagConditions.join(' OR ')})`);
    }

    // Custom fields
    if (filters.customFields?.length) {
      filters.customFields.forEach(field => {
        const condition = this.buildCustomFieldCondition(field);
        if (condition) conditions.push(condition);
      });
    }

    // Exclude filters
    if (filters.excludeUnsubscribed) {
      conditions.push(`(is_subscribed IS NULL OR is_subscribed = true)`);
    }

    if (filters.excludeBounced) {
      conditions.push(`
        email NOT IN (
          SELECT email_address 
          FROM email_system.email_suppressions 
          WHERE suppression_type = 'bounce' 
          AND is_active = true
        )
      `);
    }

    return conditions.length > 0 ? conditions.join(' AND ') : '1=1';
  }

  /**
   * Build SQL condition for custom field
   */
  private buildCustomFieldCondition(field: CustomFieldFilter): string | null {
    const { fieldName, operator, value } = field;
    const quotedField = `"${fieldName}"`;

    switch (operator) {
      case FilterOperator.EQUALS:
        return `${quotedField} = '${value}'`;
      
      case FilterOperator.NOT_EQUALS:
        return `${quotedField} != '${value}'`;
      
      case FilterOperator.CONTAINS:
        return `${quotedField} ILIKE '%${value}%'`;
      
      case FilterOperator.NOT_CONTAINS:
        return `${quotedField} NOT ILIKE '%${value}%'`;
      
      case FilterOperator.STARTS_WITH:
        return `${quotedField} ILIKE '${value}%'`;
      
      case FilterOperator.ENDS_WITH:
        return `${quotedField} ILIKE '%${value}'`;
      
      case FilterOperator.GREATER_THAN:
        return `${quotedField} > '${value}'`;
      
      case FilterOperator.LESS_THAN:
        return `${quotedField} < '${value}'`;
      
      case FilterOperator.GREATER_THAN_OR_EQUAL:
        return `${quotedField} >= '${value}'`;
      
      case FilterOperator.LESS_THAN_OR_EQUAL:
        return `${quotedField} <= '${value}'`;
      
      case FilterOperator.IS_EMPTY:
        return `(${quotedField} IS NULL OR ${quotedField} = '')`;
      
      case FilterOperator.IS_NOT_EMPTY:
        return `(${quotedField} IS NOT NULL AND ${quotedField} != '')`;
      
      case FilterOperator.IN:
        if (Array.isArray(value)) {
          const values = value.map(v => `'${v}'`).join(',');
          return `${quotedField} IN (${values})`;
        }
        return `${quotedField} = '${value}'`;
      
      case FilterOperator.NOT_IN:
        if (Array.isArray(value)) {
          const values = value.map(v => `'${v}'`).join(',');
          return `${quotedField} NOT IN (${values})`;
        }
        return `${quotedField} != '${value}'`;
      
      default:
        return null;
    }
  }

  /**
   * Validate filters before building query
   */
  validateFilters(filters: RecipientFilters): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate date ranges
    if (filters.expiryDateRange) {
      if (filters.expiryDateRange.from && filters.expiryDateRange.to) {
        if (filters.expiryDateRange.from > filters.expiryDateRange.to) {
          errors.push('Expiry date "from" must be before "to"');
        }
      }
    }

    // Validate age ranges
    if (filters.ageRange) {
      if (filters.ageRange.min !== undefined && filters.ageRange.min < 0) {
        errors.push('Minimum age cannot be negative');
      }
      if (filters.ageRange.max !== undefined && filters.ageRange.max < 0) {
        errors.push('Maximum age cannot be negative');
      }
      if (filters.ageRange.min !== undefined && filters.ageRange.max !== undefined) {
        if (filters.ageRange.min > filters.ageRange.max) {
          errors.push('Minimum age cannot be greater than maximum age');
        }
      }
    }

    // Validate custom fields
    if (filters.customFields?.length) {
      filters.customFields.forEach((field: CustomFieldFilter, index: number) => {
        if (!field.fieldName) {
          errors.push(`Custom field ${index + 1}: Field name is required`);
        }
        if (!field.operator) {
          errors.push(`Custom field ${index + 1}: Operator is required`);
        }
        if (field.operator !== FilterOperator.IS_EMPTY && 
            field.operator !== FilterOperator.IS_NOT_EMPTY && 
            field.value === undefined) {
          errors.push(`Custom field ${index + 1}: Value is required for this operator`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export helper function for easy use
export function createFilterQueryBuilder(client: SupabaseClient, tableName?: string) {
  return new FilterQueryBuilder(client, tableName);
}