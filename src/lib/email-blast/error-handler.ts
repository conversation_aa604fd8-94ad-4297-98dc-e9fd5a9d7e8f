import { PostgrestError } from '@supabase/supabase-js';

// ==================== ERROR TYPES ====================

export enum EmailBlastErrorCode {
  // Database errors
  DB_CONNECTION_ERROR = 'DB_CONNECTION_ERROR',
  DB_QUERY_ERROR = 'DB_QUERY_ERROR',
  DB_CONSTRAINT_ERROR = 'DB_CONSTRAINT_ERROR',
  DB_NOT_FOUND = 'DB_NOT_FOUND',
  DB_DUPLICATE_ERROR = 'DB_DUPLICATE_ERROR',
  
  // Business logic errors
  INVALID_RECIPIENT_LIST = 'INVALID_RECIPIENT_LIST',
  EMPTY_RECIPIENT_LIST = 'EMPTY_RECIPIENT_LIST',
  INVALID_TEMPLATE = 'INVALID_TEMPLATE',
  CAMPAIGN_ALREADY_SENT = 'CAMPAIGN_ALREADY_SENT',
  CAMPAIGN_IN_PROGRESS = 'CAMPAIGN_IN_PROGRESS',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  
  // Email delivery errors
  EMAIL_SEND_FAILED = 'EMAIL_SEND_FAILED',
  EMAIL_QUOTA_EXCEEDED = 'EMAIL_QUOTA_EXCEEDED',
  EMAIL_RATE_LIMITED = 'EMAIL_RATE_LIMITED',
  EMAIL_INVALID_ADDRESS = 'EMAIL_INVALID_ADDRESS',
  EMAIL_SUPPRESSED = 'EMAIL_SUPPRESSED',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FIELD_TYPE = 'INVALID_FIELD_TYPE',
  INVALID_DATE_RANGE = 'INVALID_DATE_RANGE',
  
  // System errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
}

export class EmailBlastError extends Error {
  constructor(
    message: string,
    public code: EmailBlastErrorCode = EmailBlastErrorCode.UNKNOWN_ERROR,
    public details?: any,
    public userMessage?: string,
    public isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'EmailBlastError';
  }
}

// ==================== ERROR PARSING ====================

/**
 * Parses Supabase/PostgreSQL errors and returns a user-friendly error
 */
export function parseSupabaseError(error: PostgrestError): EmailBlastError {
  const { code, message, details } = error;
  
  // Handle specific PostgreSQL error codes
  switch (code) {
    // Not found
    case 'PGRST116':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.DB_NOT_FOUND,
        details,
        'The requested resource was not found.'
      );
    
    // Foreign key violation
    case '23503':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.DB_CONSTRAINT_ERROR,
        details,
        'Referenced data does not exist. Please check your inputs.'
      );
    
    // Unique violation
    case '23505':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.DB_DUPLICATE_ERROR,
        details,
        'This record already exists. Please use a different value.'
      );
    
    // Check constraint violation
    case '23514':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.VALIDATION_ERROR,
        details,
        'The provided data does not meet validation requirements.'
      );
    
    // Connection error
    case 'PGRST301':
    case '08001':
    case '08006':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.DB_CONNECTION_ERROR,
        details,
        'Unable to connect to the database. Please try again later.',
        true // Retryable
      );
    
    // Timeout
    case '57014':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.TIMEOUT_ERROR,
        details,
        'The operation took too long. Please try again.',
        true // Retryable
      );
    
    // Permission denied
    case '42501':
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.INSUFFICIENT_PERMISSIONS,
        details,
        'You do not have permission to perform this action.'
      );
    
    default:
      // Try to parse the message for more context
      if (message.includes('violates foreign key constraint')) {
        return new EmailBlastError(
          message,
          EmailBlastErrorCode.DB_CONSTRAINT_ERROR,
          details,
          'Invalid reference. Please check that all referenced data exists.'
        );
      }
      
      if (message.includes('duplicate key value')) {
        return new EmailBlastError(
          message,
          EmailBlastErrorCode.DB_DUPLICATE_ERROR,
          details,
          'This value already exists. Please use a different one.'
        );
      }
      
      if (message.includes('permission denied')) {
        return new EmailBlastError(
          message,
          EmailBlastErrorCode.INSUFFICIENT_PERMISSIONS,
          details,
          'You do not have permission to perform this action.'
        );
      }
      
      return new EmailBlastError(
        message,
        EmailBlastErrorCode.DB_QUERY_ERROR,
        details,
        'A database error occurred. Please try again.'
      );
  }
}

// ==================== USER-FRIENDLY MESSAGES ====================

const ERROR_MESSAGES: Record<EmailBlastErrorCode, string> = {
  [EmailBlastErrorCode.DB_CONNECTION_ERROR]: 'Unable to connect to the database. Please check your connection and try again.',
  [EmailBlastErrorCode.DB_QUERY_ERROR]: 'An error occurred while processing your request. Please try again.',
  [EmailBlastErrorCode.DB_CONSTRAINT_ERROR]: 'The operation could not be completed due to data constraints.',
  [EmailBlastErrorCode.DB_NOT_FOUND]: 'The requested item was not found.',
  [EmailBlastErrorCode.DB_DUPLICATE_ERROR]: 'This item already exists. Please use a different identifier.',
  
  [EmailBlastErrorCode.INVALID_RECIPIENT_LIST]: 'The selected recipient list is invalid or does not exist.',
  [EmailBlastErrorCode.EMPTY_RECIPIENT_LIST]: 'The recipient list contains no valid email addresses.',
  [EmailBlastErrorCode.INVALID_TEMPLATE]: 'The selected email template is invalid or does not exist.',
  [EmailBlastErrorCode.CAMPAIGN_ALREADY_SENT]: 'This campaign has already been sent and cannot be modified.',
  [EmailBlastErrorCode.CAMPAIGN_IN_PROGRESS]: 'This campaign is currently being sent. Please wait for it to complete.',
  [EmailBlastErrorCode.INSUFFICIENT_PERMISSIONS]: 'You do not have permission to perform this action.',
  
  [EmailBlastErrorCode.EMAIL_SEND_FAILED]: 'Failed to send the email. Please check your email configuration.',
  [EmailBlastErrorCode.EMAIL_QUOTA_EXCEEDED]: 'Email quota exceeded. Please contact your administrator.',
  [EmailBlastErrorCode.EMAIL_RATE_LIMITED]: 'Too many emails sent. Please wait before sending more.',
  [EmailBlastErrorCode.EMAIL_INVALID_ADDRESS]: 'One or more email addresses are invalid.',
  [EmailBlastErrorCode.EMAIL_SUPPRESSED]: 'One or more recipients have been suppressed from receiving emails.',
  
  [EmailBlastErrorCode.VALIDATION_ERROR]: 'Please check your input and try again.',
  [EmailBlastErrorCode.MISSING_REQUIRED_FIELD]: 'Please fill in all required fields.',
  [EmailBlastErrorCode.INVALID_FIELD_TYPE]: 'Invalid data type provided.',
  [EmailBlastErrorCode.INVALID_DATE_RANGE]: 'Invalid date range. End date must be after start date.',
  
  [EmailBlastErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
  [EmailBlastErrorCode.SERVICE_UNAVAILABLE]: 'The service is temporarily unavailable. Please try again later.',
  [EmailBlastErrorCode.TIMEOUT_ERROR]: 'The operation timed out. Please try again.',
};

/**
 * Gets a user-friendly error message
 */
export function getUserFriendlyMessage(error: EmailBlastError | Error): string {
  if (error instanceof EmailBlastError) {
    return error.userMessage || ERROR_MESSAGES[error.code] || error.message;
  }
  
  // Generic error
  return 'An unexpected error occurred. Please try again.';
}

// ==================== ERROR LOGGING ====================

export interface ErrorLogEntry {
  timestamp: Date;
  code: EmailBlastErrorCode;
  message: string;
  details?: any;
  stack?: string;
  userId?: string;
  context?: Record<string, any>;
}

/**
 * Logs an error for debugging and monitoring
 */
export function logError(
  error: EmailBlastError | Error,
  context?: Record<string, any>
): void {
  const entry: ErrorLogEntry = {
    timestamp: new Date(),
    code: error instanceof EmailBlastError ? error.code : EmailBlastErrorCode.UNKNOWN_ERROR,
    message: error.message,
    stack: error.stack,
    context,
  };
  
  if (error instanceof EmailBlastError) {
    entry.details = error.details;
  }
  
  // In production, send to error tracking service
  if (process.env.NODE_ENV === 'production') {
    // TODO: Send to Sentry, LogRocket, etc.
    console.error('[EmailBlast Error]', entry);
  } else {
    console.error('[EmailBlast Error]', entry);
  }
}

// ==================== RETRY LOGIC ====================

export interface RetryOptions {
  maxAttempts?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  shouldRetry?: (error: Error, attempt: number) => boolean;
}

const DEFAULT_RETRY_OPTIONS: Required<RetryOptions> = {
  maxAttempts: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
  shouldRetry: (error) => {
    if (error instanceof EmailBlastError) {
      return error.isRetryable;
    }
    return false;
  },
};

/**
 * Retries an operation with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const opts = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: Error | undefined;
  
  for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === opts.maxAttempts || !opts.shouldRetry(lastError, attempt)) {
        throw lastError;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        opts.initialDelay * Math.pow(opts.backoffFactor, attempt - 1),
        opts.maxDelay
      );
      
      // Add jitter to prevent thundering herd
      const jitter = Math.random() * 0.1 * delay;
      
      await new Promise(resolve => setTimeout(resolve, delay + jitter));
    }
  }
  
  throw lastError || new Error('Retry failed');
}

// ==================== ERROR RECOVERY ====================

export interface RecoveryStrategy {
  code: EmailBlastErrorCode;
  recover: () => Promise<void>;
}

/**
 * Attempts to recover from known errors
 */
export async function attemptRecovery(
  error: EmailBlastError,
  strategies: RecoveryStrategy[]
): Promise<boolean> {
  const strategy = strategies.find(s => s.code === error.code);
  
  if (!strategy) {
    return false;
  }
  
  try {
    await strategy.recover();
    return true;
  } catch (recoveryError) {
    logError(recoveryError as Error, {
      originalError: error,
      recovery: 'failed',
    });
    return false;
  }
}

// ==================== VALIDATION HELPERS ====================

export class ValidationError extends EmailBlastError {
  constructor(
    public field: string,
    message: string,
    public value?: any
  ) {
    super(
      `Validation error for field '${field}': ${message}`,
      EmailBlastErrorCode.VALIDATION_ERROR,
      { field, value },
      message
    );
  }
}

/**
 * Validates required fields
 */
export function validateRequired<T extends Record<string, any>>(
  data: T,
  requiredFields: (keyof T)[]
): void {
  const errors: ValidationError[] = [];
  
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(
        new ValidationError(
          String(field),
          `${String(field)} is required`,
          data[field]
        )
      );
    }
  }
  
  if (errors.length > 0) {
    throw new EmailBlastError(
      'Validation failed',
      EmailBlastErrorCode.VALIDATION_ERROR,
      errors,
      'Please fill in all required fields.'
    );
  }
}

/**
 * Validates email format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validates date range
 */
export function validateDateRange(from?: Date, to?: Date): void {
  if (from && to && from > to) {
    throw new ValidationError(
      'dateRange',
      'End date must be after start date',
      { from, to }
    );
  }
}

// ==================== ERROR BOUNDARIES ====================

/**
 * Wraps an async function with error handling
 */
export function withErrorBoundary<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  errorHandler?: (error: Error) => void
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      const emailError = error instanceof EmailBlastError
        ? error
        : new EmailBlastError(
            (error as Error).message,
            EmailBlastErrorCode.UNKNOWN_ERROR,
            error
          );
      
      logError(emailError);
      
      if (errorHandler) {
        errorHandler(emailError);
      }
      
      throw emailError;
    }
  }) as T;
}