import { <PERSON>, <PERSON>son } from '@/types/email_system.types';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  EmailContent,
  EmailAttachment,
  EmailCampaignStatus,
  EmailBlastRecipient,
  EmailBlastStatistics,
} from '@/types/email-blast/email-blast.types';
import {
  RecipientList,
  RecipientFilters,
  RecipientPreview,
  MembershipStatus,
} from '@/types/email-blast/recipient-list.types';
import {
  EmailTemplate,
  TemplateCategory,
  TemplateStatus,
  TemplateVariable,
  EmailContent as TemplateEmailContent,
} from '@/types/email-blast/email-template.types';

// Database table types
type EmailCampaignRow = Database['email_system']['Tables']['email_campaigns']['Row'];
type EmailCampaignInsert = Database['email_system']['Tables']['email_campaigns']['Insert'];
type EmailCampaignUpdate = Database['email_system']['Tables']['email_campaigns']['Update'];

type RecipientListRow = Database['email_system']['Tables']['recipient_lists']['Row'];
type RecipientListInsert = Database['email_system']['Tables']['recipient_lists']['Insert'];
type RecipientListUpdate = Database['email_system']['Tables']['recipient_lists']['Update'];

type EmailTemplateRow = Database['email_system']['Tables']['email_templates']['Row'];
type EmailTemplateInsert = Database['email_system']['Tables']['email_templates']['Insert'];
type EmailTemplateUpdate = Database['email_system']['Tables']['email_templates']['Update'];

type CampaignRecipientRow = Database['email_system']['Tables']['campaign_recipients']['Row'];
type CampaignRecipientInsert = Database['email_system']['Tables']['campaign_recipients']['Insert'];
type CampaignRecipientUpdate = Database['email_system']['Tables']['campaign_recipients']['Update'];

type CampaignStatisticsRow = Database['email_system']['Tables']['campaign_statistics']['Row'];

// Helper function to safely parse JSON
function safeJsonParse<T = any>(json: Json | null | undefined, defaultValue: T): T {
  if (!json) return defaultValue;
  try {
    return json as T;
  } catch {
    return defaultValue;
  }
}

// Type guard for checking if metadata has specific properties
function isMetadataWithProperties(metadata: any): metadata is { variables?: Record<string, any>; attachments?: any[]; fromEmail?: string; fromName?: string; replyToEmail?: string } {
  return metadata && typeof metadata === 'object';
}

// Convert EmailAttachment to Json-compatible format
function toJsonAttachment(attachment: EmailAttachment): Json {
  return {
    filename: attachment.filename,
    url: attachment.url,
    size: attachment.size,
    mimeType: attachment.mimeType
  };
}

// Convert TemplateVariable to Json-compatible format
function toJsonTemplateVariable(variable: TemplateVariable): Json {
  return {
    id: variable.id,
    name: variable.name,
    key: variable.key,
    description: variable.description || null,
    type: variable.type,
    required: variable.required,
    defaultValue: variable.defaultValue || null,
    placeholder: variable.placeholder || null,
    validation: variable.validation as Json || null
  };
}

// Helper function to convert date string to Date object
function toDate(dateString: string | null | undefined): Date | undefined {
  if (!dateString) return undefined;
  try {
    return new Date(dateString);
  } catch {
    return undefined;
  }
}

// Helper function to convert Date to ISO string
function toISOString(date: Date | undefined): string | undefined {
  return date?.toISOString();
}

// Map database status to frontend enum
function mapCampaignStatus(status: string): EmailCampaignStatus {
  const statusMap: Record<string, EmailCampaignStatus> = {
    'draft': EmailCampaignStatus.DRAFT,
    'scheduled': EmailCampaignStatus.SCHEDULED,
    'sending': EmailCampaignStatus.SENDING,
    'sent': EmailCampaignStatus.SENT,
    'failed': EmailCampaignStatus.FAILED,
  };
  return statusMap[status] || EmailCampaignStatus.DRAFT;
}

// Map frontend enum to database status
function mapCampaignStatusToDb(status: EmailCampaignStatus): string {
  return status.toLowerCase();
}

// ==================== EMAIL BLAST MAPPERS ====================

export function mapEmailCampaignToEmailBlast(
  campaign: EmailCampaignRow,
  statistics?: CampaignStatisticsRow
): EmailBlast {
  const metadata = safeJsonParse(campaign.metadata, {});
  
  const content: EmailContent = {
    subject: campaign.subject,
    body: campaign.body_text || campaign.body_html,
    bodyHtml: campaign.body_html,
    templateId: campaign.template_id || undefined,
    variables: isMetadataWithProperties(metadata) && metadata.variables ? metadata.variables : {},
    attachments: isMetadataWithProperties(metadata) && metadata.attachments ? metadata.attachments : [],
  };

  return {
    id: campaign.id,
    name: campaign.name,
    description: campaign.description || undefined,
    status: mapCampaignStatus(campaign.status),
    content,
    recipientListId: campaign.recipient_list_id,
    recipientCount: campaign.total_recipients || undefined,
    sentCount: campaign.sent_count || undefined,
    failedCount: campaign.failed_count || undefined,
    openCount: campaign.opened_count || undefined,
    clickCount: campaign.clicked_count || undefined,
    scheduledAt: toDate(campaign.scheduled_at),
    sentAt: toDate(campaign.started_at),
    createdAt: new Date(campaign.created_at!),
    updatedAt: new Date(campaign.updated_at || campaign.created_at!),
    createdBy: campaign.created_by,
    updatedBy: campaign.updated_by || campaign.created_by,
    tags: campaign.tags || undefined,
    metadata: metadata,
  };
}

export function mapEmailBlastToEmailCampaignInsert(
  blast: Partial<EmailBlast>,
  userId: string
): EmailCampaignInsert {
  const attachments = (blast.content?.attachments || []).map(toJsonAttachment);
  
  const metadata: Json = {
    variables: blast.content?.variables || {},
    attachments: attachments,
    ...(blast.metadata || {}),
  };

  return {
    name: blast.name!,
    description: blast.description,
    status: blast.status ? mapCampaignStatusToDb(blast.status) : 'draft',
    subject: blast.content?.subject || '',
    body_html: blast.content?.bodyHtml || blast.content?.body || '',
    body_text: blast.content?.body,
    template_id: blast.content?.templateId,
    recipient_list_id: blast.recipientListId!,
    scheduled_at: toISOString(blast.scheduledAt),
    from_email: (isMetadataWithProperties(metadata) && typeof metadata.fromEmail === 'string' ? metadata.fromEmail : undefined) || '<EMAIL>', // Should come from settings
    from_name: (isMetadataWithProperties(metadata) && typeof metadata.fromName === 'string' ? metadata.fromName : undefined) || 'IES System',
    reply_to_email: isMetadataWithProperties(metadata) && typeof metadata.replyToEmail === 'string' ? metadata.replyToEmail : undefined,
    tags: blast.tags,
    metadata,
    created_by: userId,
    track_opens: true,
    track_clicks: true,
    include_unsubscribe: true,
  };
}

export function mapEmailBlastToEmailCampaignUpdate(
  blast: Partial<EmailBlast>,
  userId: string
): EmailCampaignUpdate {
  const update: EmailCampaignUpdate = {
    updated_by: userId,
    updated_at: new Date().toISOString(),
  };

  if (blast.name !== undefined) update.name = blast.name;
  if (blast.description !== undefined) update.description = blast.description;
  if (blast.status !== undefined) update.status = mapCampaignStatusToDb(blast.status);
  if (blast.scheduledAt !== undefined) update.scheduled_at = toISOString(blast.scheduledAt);
  if (blast.tags !== undefined) update.tags = blast.tags;

  if (blast.content) {
    if (blast.content.subject !== undefined) update.subject = blast.content.subject;
    if (blast.content.bodyHtml !== undefined) update.body_html = blast.content.bodyHtml;
    if (blast.content.body !== undefined) update.body_text = blast.content.body;
    if (blast.content.templateId !== undefined) update.template_id = blast.content.templateId;
    
    const attachments = (blast.content.attachments || []).map(toJsonAttachment);
    
    const metadata: Json = {
      variables: blast.content.variables || {},
      attachments: attachments,
      ...(blast.metadata || {}),
    };
    update.metadata = metadata;
  }

  return update;
}

// ==================== RECIPIENT LIST MAPPERS ====================

export function mapRecipientListFromDb(list: RecipientListRow): RecipientList {
  const filters = safeJsonParse<RecipientFilters>(list.filters, {});
  const metadata = safeJsonParse(list.metadata, {});

  return {
    id: list.id,
    name: list.name,
    description: list.description || undefined,
    filters,
    recipientCount: list.recipient_count || 0,
    isStatic: list.list_type === 'static',
    type: list.list_type as 'dynamic' | 'static',
    status: (list.status || 'active') as 'active' | 'inactive' | 'archived',
    lastUsedAt: toDate(list.last_used_at),
    usageCount: list.usage_count || undefined,
    createdAt: new Date(list.created_at!),
    updatedAt: new Date(list.updated_at || list.created_at!),
    createdBy: list.created_by,
    updatedBy: list.updated_by || undefined,
    tags: list.tags || undefined,
    metadata,
  };
}

export function mapRecipientListToInsert(
  list: Partial<RecipientList>,
  userId: string
): RecipientListInsert {
  return {
    name: list.name!,
    description: list.description,
    list_type: list.type || 'dynamic',
    filters: list.filters as Json || {},
    tags: list.tags,
    metadata: (list.metadata || {}) as Json,
    created_by: userId,
    status: list.status || 'active',
  };
}

export function mapRecipientListToUpdate(
  list: Partial<RecipientList>,
  userId: string
): RecipientListUpdate {
  const update: RecipientListUpdate = {
    updated_by: userId,
    updated_at: new Date().toISOString(),
  };

  if (list.name !== undefined) update.name = list.name;
  if (list.description !== undefined) update.description = list.description;
  if (list.filters !== undefined) update.filters = list.filters as Json;
  if (list.tags !== undefined) update.tags = list.tags;
  if (list.metadata !== undefined) update.metadata = list.metadata as Json;
  if (list.status !== undefined) update.status = list.status;

  return update;
}

// ==================== EMAIL TEMPLATE MAPPERS ====================

export function mapEmailTemplateFromDb(template: EmailTemplateRow): EmailTemplate {
  const variables = safeJsonParse<TemplateVariable[]>(template.variables, []);
  
  const content: TemplateEmailContent = {
    subject: template.subject,
    body: template.body_text || template.body_html,
    bodyHtml: template.body_html,
    previewText: template.description || undefined,
  };

  return {
    id: template.id,
    name: template.name,
    description: template.description || undefined,
    category: (template.category as TemplateCategory) || TemplateCategory.CUSTOM,
    status: template.active ? TemplateStatus.ACTIVE : TemplateStatus.ARCHIVED,
    content,
    variables,
    thumbnail: template.thumbnail_url || undefined,
    tags: [], // Not in database schema
    isSystem: template.is_blast_template || false,
    version: template.version || 1,
    parentTemplateId: template.parent_template_id || undefined,
    createdAt: new Date(template.created_at!),
    updatedAt: new Date(template.updated_at || template.created_at!),
    createdBy: 'system', // Not tracked in database
    updatedBy: 'system', // Not tracked in database
    metadata: {},
  };
}

export function mapEmailTemplateToInsert(
  template: Partial<EmailTemplate>
): EmailTemplateInsert {
  return {
    name: template.name!,
    description: template.description,
    category: template.category || 'custom',
    subject: template.content?.subject || '',
    body_html: template.content?.bodyHtml || template.content?.body || '',
    body_text: template.content?.body,
    active: template.status === TemplateStatus.ACTIVE,
    is_blast_template: true,
    variables: (template.variables || []).map(toJsonTemplateVariable) as Json,
    thumbnail_url: template.thumbnail,
    parent_template_id: template.parentTemplateId,
    version: template.version || 1,
  };
}

export function mapEmailTemplateToUpdate(
  template: Partial<EmailTemplate>
): EmailTemplateUpdate {
  const update: EmailTemplateUpdate = {
    updated_at: new Date().toISOString(),
  };

  if (template.name !== undefined) update.name = template.name;
  if (template.description !== undefined) update.description = template.description;
  if (template.category !== undefined) update.category = template.category;
  if (template.status !== undefined) update.active = template.status === TemplateStatus.ACTIVE;
  if (template.variables !== undefined) {
    update.variables = template.variables.map(toJsonTemplateVariable) as Json;
  }
  if (template.thumbnail !== undefined) update.thumbnail_url = template.thumbnail;
  if (template.version !== undefined) update.version = template.version;

  if (template.content) {
    if (template.content.subject !== undefined) update.subject = template.content.subject;
    if (template.content.bodyHtml !== undefined) update.body_html = template.content.bodyHtml;
    if (template.content.body !== undefined) update.body_text = template.content.body;
  }

  return update;
}

// ==================== CAMPAIGN RECIPIENT MAPPERS ====================

export function mapCampaignRecipientFromDb(recipient: CampaignRecipientRow): EmailBlastRecipient {
  const metadata = safeJsonParse(recipient.recipient_data, {});

  // Map database status to frontend status
  let status: EmailBlastRecipient['status'] = 'pending';
  if (recipient.status === 'unsubscribed' || recipient.unsubscribed_at) {
    status = 'unsubscribed';
  } else if (recipient.failed_at || recipient.status === 'failed') {
    status = 'failed';
  } else if (recipient.bounced_at || recipient.bounce_type) {
    status = 'bounced';
  } else if (recipient.clicked_at || recipient.click_count) {
    status = 'clicked';
  } else if (recipient.opened_at || recipient.open_count) {
    status = 'opened';
  } else if (recipient.delivered_at || recipient.status === 'delivered') {
    status = 'delivered';
  } else if (recipient.sent_at || recipient.status === 'sent') {
    status = 'sent';
  }

  return {
    id: recipient.id,
    campaignId: recipient.campaign_id,
    recipientId: recipient.id, // Using same ID as recipient
    email: recipient.email,
    name: recipient.name || '',
    status,
    sentAt: toDate(recipient.sent_at),
    deliveredAt: toDate(recipient.delivered_at),
    openedAt: toDate(recipient.opened_at || recipient.first_opened_at),
    clickedAt: toDate(recipient.clicked_at || recipient.first_clicked_at),
    bouncedAt: toDate(recipient.bounced_at),
    failedAt: toDate(recipient.failed_at),
    unsubscribedAt: toDate(recipient.unsubscribed_at),
    errorMessage: recipient.error_message || undefined,
    metadata,
  };
}

// ==================== STATISTICS MAPPERS ====================

export function mapCampaignStatisticsFromDb(stats: CampaignStatisticsRow): EmailBlastStatistics {
  return {
    campaignId: stats.campaign_id,
    totalRecipients: stats.total_recipients,
    sentCount: stats.sent_count || 0,
    deliveredCount: stats.delivered_count || 0,
    openCount: stats.total_opens || 0,
    uniqueOpenCount: stats.unique_opens || 0,
    clickCount: stats.total_clicks || 0,
    uniqueClickCount: stats.unique_clicks || 0,
    bounceCount: stats.bounce_count || 0,
    unsubscribeCount: stats.unsubscribe_count || 0,
    spamReportCount: stats.spam_report_count || 0,
    failedCount: stats.failed_count || 0,
    lastUpdated: new Date(stats.updated_at || stats.created_at!),
  };
}

// ==================== MEMBER/USER MAPPERS ====================

// This is a generic mapper that can be used with any user/member table
export function mapUserToRecipientPreview(user: any): RecipientPreview {
  return {
    id: user.id || user.user_id || '',
    name: user.name || user.full_name || `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown',
    email: user.email || '',
    membershipType: user.membership_type || user.role || 'member',
    status: mapUserStatus(user.status || user.membership_status || 'active'),
    expiryDate: toDate(user.expiry_date || user.membership_expiry),
    location: user.location || user.address || user.city || undefined,
    phoneNumber: user.phone || user.phone_number || undefined,
    tags: user.tags || [],
    customFields: {
      ...user,
      // Remove fields we've already mapped
      id: undefined,
      user_id: undefined,
      name: undefined,
      full_name: undefined,
      first_name: undefined,
      last_name: undefined,
      email: undefined,
      membership_type: undefined,
      role: undefined,
      status: undefined,
      membership_status: undefined,
      expiry_date: undefined,
      membership_expiry: undefined,
      location: undefined,
      address: undefined,
      city: undefined,
      phone: undefined,
      phone_number: undefined,
      tags: undefined,
    },
  };
}

function mapUserStatus(status: string): MembershipStatus {
  const statusMap: Record<string, MembershipStatus> = {
    'active': MembershipStatus.ACTIVE,
    'expired': MembershipStatus.EXPIRED,
    'pending': MembershipStatus.PENDING,
    'suspended': MembershipStatus.SUSPENDED,
    'inactive': MembershipStatus.EXPIRED,
    'blocked': MembershipStatus.SUSPENDED,
  };
  return statusMap[status.toLowerCase()] || MembershipStatus.PENDING;
}