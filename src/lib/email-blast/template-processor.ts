/**
 * Template processor for email content
 * Handles Handlebars-style variable replacement {{variable}}
 */

export interface TemplateVariables {
  [key: string]: string | number | boolean | null | undefined;
}

export interface ProcessedTemplate {
  subject: string;
  body: string;
  bodyHtml?: string;
  missingVariables: string[];
}

export class TemplateProcessor {
  private static readonly VARIABLE_PATTERN = /\{\{(\s*[\w.]+\s*)\}\}/g;
  private static readonly DEFAULT_VALUES: Record<string, string> = {
    firstName: 'Member',
    lastName: '',
    email: '',
    membershipType: 'Member',
    companyName: 'Our Company',
    currentYear: new Date().getFullYear().toString(),
    currentDate: new Date().toLocaleDateString(),
  };

  /**
   * Process a template string with variables
   */
  static processTemplate(template: string, variables: TemplateVariables): string {
    if (!template) return '';

    const missingVars = new Set<string>();
    
    const processed = template.replace(
      this.VARIABLE_PATTERN,
      (match, variableName) => {
        const cleanVarName = variableName.trim();
        
        // Handle nested properties (e.g., {{member.firstName}})
        const value = this.getNestedValue(variables, cleanVarName);
        
        if (value === undefined || value === null) {
          missingVars.add(cleanVarName);
          // Use default value if available
          return this.DEFAULT_VALUES[cleanVarName] || match;
        }
        
        return String(value);
      }
    );

    return processed;
  }

  /**
   * Process email content with variables
   */
  static processEmailContent(
    subject: string,
    body: string,
    bodyHtml: string | undefined,
    variables: TemplateVariables
  ): ProcessedTemplate {
    const missingVariables = new Set<string>();
    
    // Process subject
    const processedSubject = this.processTemplateWithTracking(
      subject,
      variables,
      missingVariables
    );
    
    // Process plain text body
    const processedBody = this.processTemplateWithTracking(
      body,
      variables,
      missingVariables
    );
    
    // Process HTML body if present
    const processedBodyHtml = bodyHtml
      ? this.processTemplateWithTracking(bodyHtml, variables, missingVariables)
      : undefined;

    return {
      subject: processedSubject,
      body: processedBody,
      bodyHtml: processedBodyHtml,
      missingVariables: Array.from(missingVariables),
    };
  }

  /**
   * Extract variable names from a template
   */
  static extractVariables(template: string): string[] {
    if (!template) return [];
    
    const variables = new Set<string>();
    let match;
    
    while ((match = this.VARIABLE_PATTERN.exec(template)) !== null) {
      variables.add(match[1].trim());
    }
    
    // Reset lastIndex
    this.VARIABLE_PATTERN.lastIndex = 0;
    
    return Array.from(variables);
  }

  /**
   * Get all variables from email content
   */
  static getEmailVariables(
    subject: string,
    body: string,
    bodyHtml?: string
  ): string[] {
    const variables = new Set<string>();
    
    // Extract from subject
    this.extractVariables(subject).forEach(v => variables.add(v));
    
    // Extract from body
    this.extractVariables(body).forEach(v => variables.add(v));
    
    // Extract from HTML body
    if (bodyHtml) {
      this.extractVariables(bodyHtml).forEach(v => variables.add(v));
    }
    
    return Array.from(variables);
  }

  /**
   * Validate that all required variables are present
   */
  static validateVariables(
    template: string,
    variables: TemplateVariables
  ): { isValid: boolean; missing: string[] } {
    const requiredVars = this.extractVariables(template);
    const missing: string[] = [];
    
    for (const varName of requiredVars) {
      const value = this.getNestedValue(variables, varName);
      if (value === undefined || value === null) {
        missing.push(varName);
      }
    }
    
    return {
      isValid: missing.length === 0,
      missing,
    };
  }

  /**
   * Create a preview of the template with sample data
   */
  static createPreview(template: string): string {
    const sampleVariables: TemplateVariables = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      membershipType: 'Premium',
      expiryDate: '2024-12-31',
      companyName: 'ACME Corp',
      memberNumber: 'MEM-12345',
      ...this.DEFAULT_VALUES,
    };
    
    return this.processTemplate(template, sampleVariables);
  }

  /**
   * Process template with missing variable tracking
   */
  private static processTemplateWithTracking(
    template: string,
    variables: TemplateVariables,
    missingVars: Set<string>
  ): string {
    if (!template) return '';
    
    return template.replace(
      this.VARIABLE_PATTERN,
      (match, variableName) => {
        const cleanVarName = variableName.trim();
        const value = this.getNestedValue(variables, cleanVarName);
        
        if (value === undefined || value === null) {
          missingVars.add(cleanVarName);
          return this.DEFAULT_VALUES[cleanVarName] || match;
        }
        
        return String(value);
      }
    );
  }

  /**
   * Get nested value from object (e.g., "member.firstName")
   */
  private static getNestedValue(
    obj: TemplateVariables,
    path: string
  ): string | number | boolean | null | undefined {
    const parts = path.split('.');
    let current: any = obj;
    
    for (const part of parts) {
      if (current === null || current === undefined) {
        return undefined;
      }
      current = current[part];
    }
    
    return current;
  }

  /**
   * Sanitize HTML content to prevent XSS
   */
  static sanitizeHtml(html: string): string {
    // Basic sanitization - in production, use a proper HTML sanitizer library
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '');
  }

  /**
   * Format variables for display
   */
  static formatVariableList(variables: string[]): string {
    if (variables.length === 0) return 'No variables';
    
    return variables
      .sort()
      .map(v => `{{${v}}}`)
      .join(', ');
  }
}

// Export convenience functions
export const processTemplate = TemplateProcessor.processTemplate.bind(TemplateProcessor);
export const processEmailContent = TemplateProcessor.processEmailContent.bind(TemplateProcessor);
export const extractVariables = TemplateProcessor.extractVariables.bind(TemplateProcessor);
export const getEmailVariables = TemplateProcessor.getEmailVariables.bind(TemplateProcessor);
export const validateVariables = TemplateProcessor.validateVariables.bind(TemplateProcessor);
export const createPreview = TemplateProcessor.createPreview.bind(TemplateProcessor);
export const sanitizeHtml = TemplateProcessor.sanitizeHtml.bind(TemplateProcessor);
export const formatVariableList = TemplateProcessor.formatVariableList.bind(TemplateProcessor);