export const singaporeIdValidation = (idType?: string, value?: string): boolean | string => {
    if (!idType || !value) return "Invalid ID/Identification Type";

    const nricRegex = /^[STFGM]\d{7}[A-Z]$/;
    const workPermitRegex = /^[WS]\d{7}[A-Z]$/;
    const passportRegex = /^[A-Za-z0-9]{6,9}$/;

    switch (idType) {
        case "NRIC":
            return nricRegex.test(value) || "Invalid NRIC format. Example: *********";
        case "WORK_PERMIT":
        //same with S_PASS
        case "S_PASS":
            return workPermitRegex.test(value) || "Invalid Work Permit/S Pass format. Example: W1234567J";
        case "PASSPORT":
            return passportRegex.test(value) || "Invalid Passport format. Should be alphanumeric, 6-9 characters.";
        default:
            return "Invalid ID/Identification Type";
    }
};