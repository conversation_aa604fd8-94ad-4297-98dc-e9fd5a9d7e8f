// Utility functions for RBAC operations

import { createClient } from '@/utils/supabase/Client'

// RBAC Constants
export const MODULES = {
  // Core modules
  MEMBERSHIP: 'MEMBERSHIP',
  APPLICATION: 'APPLICATION',
  PROGRAMMES: 'PROGRAMMES',
  
  // Admin modules
  USER_MANAGEMENT: 'USER_MANAGEMENT',
  SETTINGS: 'SETTINGS',
  
  // Communication modules
  EMAIL_BLAST: 'EMAIL_BLAST',
  
  // Finance modules
  CUSTOMER_MANAGEMENT: 'CUSTOMER_MANAGEMENT',
  SALES_INVOICES: 'SALES_INVOICES',
  CUSTOMER_RECEIPTS: 'CUSTOMER_RECEIPTS',
  CUSTOMER_CREDIT_NOTES: 'CUSTOMER_CREDIT_NOTES',
  
  // Procurement modules
  PURCHASE_INVOICES: 'PURCHASE_INVOICES',
  PURCHASE_ORDERS: 'PURCHASE_ORDERS',
  APPROVALS: 'APPROVALS',
  BUDGET: 'BUDGET'
} as const

export const ACTIONS = {
  CREATE: 'CREATE',
  READ: 'READ', 
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  REVIEW: 'REVIEW',
  APPROVE: 'APPROVE',
  EXPORT: 'EXPORT'
} as const

export type Module = typeof MODULES[keyof typeof MODULES]
export type Action = typeof ACTIONS[keyof typeof ACTIONS]

// Server-side permission checking for API routes and Server Components
export async function checkServerPermission(
  userId: string,
  module: Module,
  action: Action,
  membershipTypeId?: string
): Promise<boolean> {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase.rpc('check_user_permission', {
      module_name: module,
      action: action,
      membership_type_id: membershipTypeId || null
    })
    
    if (error) {
      console.error('Server permission check error:', error)
      return false
    }
    
    return Boolean(data)
  } catch (err) {
    console.error('Error in checkServerPermission:', err)
    return false
  }
}

// Get user's accessible membership types for server-side filtering
export async function getServerAccessibleMembershipTypes(
  userId: string,
  module: Module
): Promise<string[]> {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .rpc('get_user_accessible_membership_types', {
        p_user_id: userId,
        p_module_name: module
      })
    
    if (error) {
      console.error('Error getting server accessible membership types:', error)
      return []
    }
    
    return data || []
  } catch (err) {
    console.error('Error in getServerAccessibleMembershipTypes:', err)
    return []
  }
}

// Server-side function to get accessible memberships
export async function getServerAccessibleMemberships(userId: string) {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase.rpc('get_accessible_memberships')
    
    if (error) {
      console.error('Error getting server accessible memberships:', error)
      return []
    }
    
    return data || []
  } catch (err) {
    console.error('Error in getServerAccessibleMemberships:', err)
    return []
  }
}

// Helper function to check multiple permissions at once
export async function checkMultiplePermissions(
  permissions: Array<{
    module: Module
    action: Action
    membershipTypeId?: string
  }>
): Promise<boolean[]> {
  try {
    const supabase = await createClient()
    
    const checks = permissions.map(async ({ module, action, membershipTypeId }) => {
      const { data, error } = await supabase.rpc('check_user_permission', {
        module_name: module,
        action: action,
        membership_type_id: membershipTypeId || null
      })
      
      return !error && Boolean(data)
    })
    
    return await Promise.all(checks)
  } catch (err) {
    console.error('Error in checkMultiplePermissions:', err)
    return permissions.map(() => false)
  }
}

// Filter data based on accessible membership types
export function filterByAccessibleMembershipTypes<T extends { membership_type?: string }>(
  data: T[],
  accessibleTypes: string[]
): T[] {
  if (accessibleTypes.length === 0) {
    return [] // No access to any types
  }
  
  return data.filter(item => 
    !item.membership_type || accessibleTypes.includes(item.membership_type)
  )
}

// Generate WHERE clause for Supabase queries based on accessible membership types
export function generateMembershipTypeFilter(accessibleTypes: string[]): string {
  if (accessibleTypes.length === 0) {
    return 'membership_type.eq.null' // This will return no results
  }
  
  if (accessibleTypes.length === 1) {
    return `membership_type.eq.${accessibleTypes[0]}`
  }
  
  return `membership_type.in.(${accessibleTypes.join(',')})`
}

// Check if user has any admin-level permissions
export async function isUserAdmin(): Promise<boolean> {
  try {
    const adminPermissions = [
      { module: MODULES.USER_MANAGEMENT, action: ACTIONS.READ },
      { module: MODULES.MEMBERSHIP, action: ACTIONS.DELETE },
      { module: MODULES.APPLICATION, action: ACTIONS.APPROVE }
    ]
    
    const results = await checkMultiplePermissions(adminPermissions)
    return results.some(result => result === true)
  } catch (err) {
    console.error('Error checking admin status:', err)
    return false
  }
}

// Get user's permission summary for dashboard display
export async function getUserPermissionSummary() {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase.rpc('get_user_permissions')
    
    if (error) {
      console.error('Error getting permission summary:', error)
      return null
    }
    
    return data
  } catch (err) {
    console.error('Error in getUserPermissionSummary:', err)
    return null
  }
}