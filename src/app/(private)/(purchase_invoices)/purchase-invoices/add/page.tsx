"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { PurchaseInvoiceForm } from "@/components/purchase-invoices/forms/purchase-invoice-form";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AddPurchaseInvoicePage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Invoices", href: "/purchase-invoices" },
          { title: "Create New", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/purchase-invoices">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        {/* Purchase Invoice Form */}
        <PurchaseInvoiceForm isEditing={false} />
      </div>
    </AdminPanelLayout>
  );
}