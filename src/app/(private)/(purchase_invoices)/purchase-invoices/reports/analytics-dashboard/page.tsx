"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  Download, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  FileText,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileSpreadsheet,
  Target,
  Zap,
  Users,
  Calendar
} from "lucide-react";
import { useRouter } from "next/navigation";
import { getAnalyticsDashboard } from "@/services/purchase-invoices/reports.service";
import type { AnalyticsDashboardData } from "@/types/purchase-invoices/reports.types";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { formatDate } from "@/lib/utils";

export default function AnalyticsDashboardPage() {
  const router = useRouter();
  const [data, setData] = useState<AnalyticsDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState("last-12-months");

  useEffect(() => {
    loadData();
  }, [selectedPeriod]);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await getAnalyticsDashboard();
      setData(result);
    } catch (error) {
      console.error("Error loading analytics dashboard:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = (format: "excel" | "csv" | "pdf") => {
    console.log(`Exporting to ${format}`);
  };

  const getPerformanceColor = (value: number, benchmark: number, isHigherBetter: boolean = true) => {
    const ratio = value / benchmark;
    if (isHigherBetter) {
      if (ratio >= 1.1) return "text-green-600";
      if (ratio >= 0.9) return "text-blue-600";
      if (ratio >= 0.7) return "text-orange-600";
      return "text-red-600";
    } else {
      if (ratio <= 0.7) return "text-green-600";
      if (ratio <= 0.9) return "text-blue-600";
      if (ratio <= 1.1) return "text-orange-600";
      return "text-red-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              Executive dashboard with charts, trends, and key financial metrics
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Executive Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Invoice Value</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.overview.total_invoice_value || 0} currency="USD" />
                </p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12.5% vs last period
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <FileText className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Invoice Count</p>
                <p className="text-2xl font-bold">{data?.overview.total_invoice_count.toLocaleString() || 0}</p>
                <p className="text-xs text-blue-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8.3% vs last period
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-50 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Processing Time</p>
                <p className="text-2xl font-bold">{data?.overview.average_processing_time.toFixed(1) || 0}d</p>
                <p className="text-xs text-orange-600 flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  -0.5d vs last period
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-purple-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approval Rate</p>
                <p className="text-2xl font-bold">{data?.overview.approval_rate.toFixed(1) || 0}%</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.1% vs last period
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-yellow-50 rounded-lg">
                <Target className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Payment Completion</p>
                <p className="text-2xl font-bold">{data?.overview.payment_completion_rate.toFixed(1) || 0}%</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +3.8% vs last period
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Process Efficiency Metrics</span>
            </CardTitle>
            <CardDescription>
              Key efficiency indicators and performance benchmarks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Approval Rate</span>
                  <span className="text-sm text-muted-foreground">{data?.overview.approval_rate.toFixed(1) || 0}%</span>
                </div>
                <Progress value={data?.overview.approval_rate || 0} className="w-full" />
                <p className="text-xs text-muted-foreground mt-1">Target: 95%</p>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Payment Completion Rate</span>
                  <span className="text-sm text-muted-foreground">{data?.overview.payment_completion_rate.toFixed(1) || 0}%</span>
                </div>
                <Progress value={data?.overview.payment_completion_rate || 0} className="w-full" />
                <p className="text-xs text-muted-foreground mt-1">Target: 90%</p>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Processing Time Efficiency</span>
                  <span className="text-sm text-muted-foreground">
                    {(100 - (data?.overview.average_processing_time || 0) * 10).toFixed(1)}%
                  </span>
                </div>
                <Progress value={100 - (data?.overview.average_processing_time || 0) * 10} className="w-full" />
                <p className="text-xs text-muted-foreground mt-1">Target: 2 days average</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>Exception Summary</span>
            </CardTitle>
            <CardDescription>
              Current exceptions and bottlenecks requiring attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-1 bg-yellow-100 rounded">
                    <BarChart3 className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Three-Way Match Exceptions</p>
                    <p className="text-xs text-muted-foreground">Require manual review</p>
                  </div>
                </div>
                <Badge className="bg-yellow-100 text-yellow-800">
                  {data?.exceptions_summary.three_way_match_exceptions || 0}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-1 bg-orange-100 rounded">
                    <Clock className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Approval Bottlenecks</p>
                    <p className="text-xs text-muted-foreground">Pending approvals &gt; 5 days</p>
                  </div>
                </div>
                <Badge className="bg-orange-100 text-orange-800">
                  {data?.exceptions_summary.approval_bottlenecks || 0}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-1 bg-red-100 rounded">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Payment Delays</p>
                    <p className="text-xs text-muted-foreground">Overdue payments</p>
                  </div>
                </div>
                <Badge className="bg-red-100 text-red-800">
                  {data?.exceptions_summary.payment_delays || 0}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-1 bg-blue-100 rounded">
                    <FileText className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Duplicate Invoices</p>
                    <p className="text-xs text-muted-foreground">Potential duplicates detected</p>
                  </div>
                </div>
                <Badge className="bg-blue-100 text-blue-800">
                  {data?.exceptions_summary.duplicate_invoices || 0}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Financial Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Financial Insights & Opportunities</span>
          </CardTitle>
          <CardDescription>
            Strategic financial insights and optimization opportunities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg bg-green-50">
              <div className="p-2 bg-green-100 rounded-lg inline-block mb-2">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <h4 className="font-semibold text-green-800">Early Payment Savings</h4>
              <p className="text-2xl font-bold text-green-600">
                <CurrencyDisplay amount={data?.financial_insights.potential_early_payment_savings || 0} currency="USD" />
              </p>
              <p className="text-xs text-green-600 mt-1">Potential annual savings</p>
            </div>
            
            <div className="text-center p-4 border rounded-lg bg-blue-50">
              <div className="p-2 bg-blue-100 rounded-lg inline-block mb-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <h4 className="font-semibold text-blue-800">Cash Flow Optimization</h4>
              <p className="text-2xl font-bold text-blue-600">
                <CurrencyDisplay amount={data?.financial_insights.cash_flow_optimization_opportunities || 0} currency="USD" />
              </p>
              <p className="text-xs text-blue-600 mt-1">Working capital opportunity</p>
            </div>
            
            <div className="text-center p-4 border rounded-lg bg-orange-50">
              <div className="p-2 bg-orange-100 rounded-lg inline-block mb-2">
                <PieChart className="h-5 w-5 text-orange-600" />
              </div>
              <h4 className="font-semibold text-orange-800">Supplier Concentration</h4>
              <p className="text-2xl font-bold text-orange-600">
                {data?.financial_insights.supplier_concentration_risk.toFixed(1) || 0}%
              </p>
              <p className="text-xs text-orange-600 mt-1">Risk from top 5 suppliers</p>
            </div>
            
            <div className="text-center p-4 border rounded-lg bg-purple-50">
              <div className="p-2 bg-purple-100 rounded-lg inline-block mb-2">
                <CheckCircle className="h-5 w-5 text-purple-600" />
              </div>
              <h4 className="font-semibold text-purple-800">Compliance Score</h4>
              <p className="text-2xl font-bold text-purple-600">
                {data?.financial_insights.compliance_score.toFixed(1) || 0}%
              </p>
              <p className="text-xs text-purple-600 mt-1">Overall compliance rating</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Suppliers Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Suppliers by Volume</CardTitle>
            <CardDescription>Highest transaction value suppliers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((rank) => (
                <div key={rank} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                      rank === 1 ? "bg-yellow-500" : 
                      rank === 2 ? "bg-gray-400" : 
                      rank === 3 ? "bg-orange-500" : 
                      "bg-gray-300"
                    }`}>
                      {rank}
                    </div>
                    <div>
                      <p className="font-medium text-sm">Supplier {rank}</p>
                      <p className="text-xs text-muted-foreground">SUPP00{rank}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-sm">
                      <CurrencyDisplay amount={500000 - (rank * 75000)} currency="USD" />
                    </p>
                    <p className="text-xs text-muted-foreground">{25 - rank * 3}% of total</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Suppliers by Count</CardTitle>
            <CardDescription>Most frequent transaction suppliers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((rank) => (
                <div key={rank} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                      rank === 1 ? "bg-yellow-500" : 
                      rank === 2 ? "bg-gray-400" : 
                      rank === 3 ? "bg-orange-500" : 
                      "bg-gray-300"
                    }`}>
                      {rank}
                    </div>
                    <div>
                      <p className="font-medium text-sm">Supplier {rank + 5}</p>
                      <p className="text-xs text-muted-foreground">SUPP0{rank + 5}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-sm">{150 - rank * 20} invoices</p>
                    <p className="text-xs text-muted-foreground">{20 - rank * 2}% of total</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Performing Suppliers</CardTitle>
            <CardDescription>Highest performance score suppliers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((rank) => (
                <div key={rank} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                      rank <= 3 ? "bg-green-500" : "bg-blue-500"
                    }`}>
                      {rank}
                    </div>
                    <div>
                      <p className="font-medium text-sm">Supplier {rank + 10}</p>
                      <p className="text-xs text-muted-foreground">SUPP{rank + 10}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-sm">{98 - rank}%</p>
                    <Badge className={rank <= 3 ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}>
                      {rank <= 3 ? "A" : "B"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Monthly Trends Overview</span>
          </CardTitle>
          <CardDescription>
            Key metrics trends over the selected period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">Interactive Charts Coming Soon</h3>
            <p className="text-sm text-gray-500 mb-4">
              Monthly invoice volume, payment trends, and approval patterns will be displayed here
            </p>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <p className="font-medium">Invoice Volume Trend</p>
                <p className="text-green-600">↗ +8.3% growth</p>
              </div>
              <div>
                <p className="font-medium">Payment Performance</p>
                <p className="text-blue-600">→ Stable at 89%</p>
              </div>
              <div>
                <p className="font-medium">Processing Efficiency</p>
                <p className="text-orange-600">↗ -0.5 days improvement</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}