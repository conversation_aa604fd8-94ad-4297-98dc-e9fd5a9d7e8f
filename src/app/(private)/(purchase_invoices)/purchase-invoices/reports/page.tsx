"use client";

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { 
  FileText, 
  Calendar, 
  AlertTriangle, 
  CreditCard, 
  TrendingUp,
  BarChart3,
  DollarSign,
  Clock,
  Users,
  Activity
} from "lucide-react";

const reports = [
  {
    id: "outstanding-payables",
    title: "Outstanding Payables Report",
    description: "View all outstanding invoices with aging analysis and payment priorities",
    icon: DollarSign,
    color: "text-red-600",
    bgColor: "bg-red-50",
    route: "/purchase-invoices/reports/outstanding-payables"
  },
  {
    id: "payment-due",
    title: "Payment Due Report",
    description: "Track upcoming payments grouped by due dates and priority levels",
    icon: Calendar,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    route: "/purchase-invoices/reports/payment-due"
  },
  {
    id: "three-way-match-exceptions",
    title: "Three-Way Match Exceptions",
    description: "Analyze variances and exceptions in three-way matching process",
    icon: <PERSON>ert<PERSON>rian<PERSON>,
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    route: "/purchase-invoices/reports/three-way-match-exceptions"
  },
  {
    id: "supplier-payment-history",
    title: "Supplier Payment History",
    description: "Comprehensive payment history and trends by supplier",
    icon: CreditCard,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    route: "/purchase-invoices/reports/supplier-payment-history"
  },
  {
    id: "vendor-performance",
    title: "Vendor Performance Metrics",
    description: "Dashboard with key performance indicators for all vendors",
    icon: TrendingUp,
    color: "text-green-600",
    bgColor: "bg-green-50",
    route: "/purchase-invoices/reports/vendor-performance"
  },
  {
    id: "analytics-dashboard",
    title: "Analytics Dashboard",
    description: "Executive dashboard with charts, trends, and key financial metrics",
    icon: BarChart3,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    route: "/purchase-invoices/reports/analytics-dashboard"
  }
];

export default function PurchaseInvoiceReportsPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Purchase Invoice Reports</h1>
          <p className="text-muted-foreground">
            Comprehensive reporting and analytics for purchase invoice management
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Activity className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Live Data</span>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Outstanding Amount</p>
                <p className="text-2xl font-bold">$2.4M</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-50 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Due This Week</p>
                <p className="text-2xl font-bold">$485K</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-yellow-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Match Exceptions</p>
                <p className="text-2xl font-bold">23</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Suppliers</p>
                <p className="text-2xl font-bold">142</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {reports.map((report) => {
          const IconComponent = report.icon;
          return (
            <Card key={report.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className={`p-3 rounded-lg ${report.bgColor}`}>
                    <IconComponent className={`h-6 w-6 ${report.color}`} />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{report.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="mb-4 min-h-[3rem]">
                  {report.description}
                </CardDescription>
                <Button 
                  className="w-full" 
                  variant="outline"
                  onClick={() => router.push(report.route)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  View Report
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Report Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Data Freshness</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Real-time data for outstanding balances</li>
                <li>• Updated every 15 minutes for analytics</li>
                <li>• Historical data available for trend analysis</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Export Options</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Excel export with multiple sheets</li>
                <li>• CSV format for data analysis</li>
                <li>• PDF reports for presentations</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}