"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/ui/data-table";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  Download, 
  TrendingUp, 
  TrendingDown,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Target,
  Award,
  FileSpreadsheet,
  FileText,
  Users,
  BarChart3
} from "lucide-react";
import { useRouter } from "next/navigation";
import { getVendorPerformanceMetrics } from "@/services/purchase-invoices/reports.service";
import type { VendorPerformanceData, VendorPerformanceMetrics } from "@/types/purchase-invoices/reports.types";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { formatDate } from "@/lib/utils";

const vendorPerformanceColumns = [
  {
    accessorKey: "supplier.name",
    header: "Supplier",
    cell: ({ row }: any) => (
      <div>
        <div className="font-medium">{row.original.supplier?.name}</div>
        <div className="text-sm text-muted-foreground">{row.original.supplier?.supplier_code}</div>
      </div>
    ),
  },
  {
    accessorKey: "performance_score",
    header: "Performance Score",
    cell: ({ row }: any) => {
      const score = row.getValue("performance_score");
      const getScoreColor = (score: number) => {
        if (score >= 90) return "text-green-600";
        if (score >= 70) return "text-blue-600";
        if (score >= 50) return "text-orange-600";
        return "text-red-600";
      };
      return (
        <div className={`font-bold text-lg ${getScoreColor(score)}`}>
          {score.toFixed(1)}
        </div>
      );
    },
  },
  {
    accessorKey: "performance_grade",
    header: "Grade",
    cell: ({ row }: any) => {
      const grade = row.getValue("performance_grade");
      const gradeColors = {
        "A": "bg-green-100 text-green-800",
        "B": "bg-blue-100 text-blue-800",
        "C": "bg-orange-100 text-orange-800",
        "D": "bg-red-100 text-red-800",
        "F": "bg-red-200 text-red-900",
      };
      return (
        <Badge className={gradeColors[grade as keyof typeof gradeColors] + " text-lg font-bold"}>
          {grade}
        </Badge>
      );
    },
  },
  {
    accessorKey: "on_time_payment_percentage",
    header: "Payment Performance",
    cell: ({ row }: any) => {
      const percentage = row.getValue("on_time_payment_percentage");
      return (
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-sm">{percentage.toFixed(1)}%</span>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </div>
          <Progress value={percentage} className="w-16 h-2" />
        </div>
      );
    },
  },
  {
    accessorKey: "invoice_accuracy_percentage",
    header: "Invoice Accuracy",
    cell: ({ row }: any) => {
      const percentage = row.getValue("invoice_accuracy_percentage");
      return (
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-sm">{percentage.toFixed(1)}%</span>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </div>
          <Progress value={percentage} className="w-16 h-2" />
        </div>
      );
    },
  },
  {
    accessorKey: "total_invoice_value",
    header: "Total Value",
    cell: ({ row }: any) => (
      <CurrencyDisplay 
        amount={row.getValue("total_invoice_value")} 
        currency="USD" 
      />
    ),
  },
  {
    accessorKey: "risk_level",
    header: "Risk Level",
    cell: ({ row }: any) => {
      const level = row.getValue("risk_level");
      const levelColors = {
        "LOW": "bg-green-100 text-green-800",
        "MEDIUM": "bg-orange-100 text-orange-800",
        "HIGH": "bg-red-100 text-red-800",
      };
      const levelIcons = {
        "LOW": <CheckCircle className="h-4 w-4" />,
        "MEDIUM": <Clock className="h-4 w-4" />,
        "HIGH": <AlertTriangle className="h-4 w-4" />,
      };
      return (
        <div className="flex items-center space-x-2">
          {levelIcons[level as keyof typeof levelIcons]}
          <Badge className={levelColors[level as keyof typeof levelColors]}>
            {level}
          </Badge>
        </div>
      );
    },
  },
];

export default function VendorPerformancePage() {
  const router = useRouter();
  const [data, setData] = useState<VendorPerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState<string>("OVERALL");
  const [selectedTab, setSelectedTab] = useState("all");

  useEffect(() => {
    loadData();
  }, [selectedMetric]);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await getVendorPerformanceMetrics({
        performance_metric: selectedMetric as any,
        sort_by: "performance_score"
      });
      setData(result);
    } catch (error) {
      console.error("Error loading vendor performance metrics:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = (format: "excel" | "csv" | "pdf") => {
    console.log(`Exporting to ${format}`);
  };

  const getTabData = () => {
    if (!data) return [];
    
    switch (selectedTab) {
      case "top-performers":
        return data.metrics.filter(vendor => vendor.performance_grade === "A");
      case "at-risk":
        return data.metrics.filter(vendor => vendor.risk_level === "HIGH");
      case "improvement-needed":
        return data.metrics.filter(vendor => vendor.performance_score < 70);
      default:
        return data.metrics;
    }
  };

  const tabData = getTabData();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Vendor Performance Metrics</h1>
            <p className="text-muted-foreground">
              Comprehensive performance dashboard with KPIs for all vendors
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Suppliers</p>
                <p className="text-2xl font-bold">{data?.summary.total_suppliers || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <Star className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Performance</p>
                <p className="text-2xl font-bold">{data?.summary.average_performance_score.toFixed(1) || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-yellow-50 rounded-lg">
                <Award className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Top Performers</p>
                <p className="text-2xl font-bold">{data?.summary.top_performers_count || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">At Risk</p>
                <p className="text-2xl font-bold">{data?.summary.at_risk_suppliers_count || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Benchmarks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Performance Benchmarks</span>
          </CardTitle>
          <CardDescription>
            Industry benchmarks and organizational targets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Payment Days Target</span>
                <span className="text-sm text-muted-foreground">≤ {data?.benchmarks.payment_days_benchmark || 30} days</span>
              </div>
              <div className="space-y-2">
                <Progress value={85} className="w-full" />
                <p className="text-xs text-muted-foreground">85% of suppliers meet target</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Invoice Accuracy Target</span>
                <span className="text-sm text-muted-foreground">≥ {data?.benchmarks.accuracy_benchmark || 95}%</span>
              </div>
              <div className="space-y-2">
                <Progress value={78} className="w-full" />
                <p className="text-xs text-muted-foreground">78% of suppliers meet target</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Dispute Rate Target</span>
                <span className="text-sm text-muted-foreground">≤ {data?.benchmarks.dispute_rate_benchmark || 2}%</span>
              </div>
              <div className="space-y-2">
                <Progress value={92} className="w-full" />
                <p className="text-xs text-muted-foreground">92% of suppliers meet target</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Grade Distribution</CardTitle>
            <CardDescription>
              Distribution of suppliers by performance grade
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {["A", "B", "C", "D", "F"].map((grade) => {
                const count = data?.metrics.filter(m => m.performance_grade === grade).length || 0;
                const percentage = data?.summary.total_suppliers ? (count / data.summary.total_suppliers) * 100 : 0;
                const colors = {
                  "A": "bg-green-500",
                  "B": "bg-blue-500", 
                  "C": "bg-orange-500",
                  "D": "bg-red-500",
                  "F": "bg-red-700"
                };
                
                return (
                  <div key={grade} className="flex items-center space-x-4">
                    <div className="w-8 text-center">
                      <Badge className={`${colors[grade as keyof typeof colors]} text-white`}>
                        {grade}
                      </Badge>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm">{count} suppliers</span>
                        <span className="text-sm text-muted-foreground">{percentage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${colors[grade as keyof typeof colors]}`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Risk Factors</CardTitle>
            <CardDescription>
              Most common performance issues across suppliers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data?.trends.top_risk_factors.map((factor, index) => (
                <div key={factor.factor} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                      index === 0 ? "bg-red-500" : 
                      index === 1 ? "bg-orange-500" : 
                      "bg-yellow-500"
                    }`}>
                      {index + 1}
                    </div>
                    <span className="font-medium">{factor.factor}</span>
                  </div>
                  <Badge variant="outline">{factor.count} suppliers</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metric Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Performance Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Focus Metric</label>
              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger>
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="OVERALL">Overall Performance</SelectItem>
                  <SelectItem value="PAYMENT_TERMS">Payment Performance</SelectItem>
                  <SelectItem value="INVOICE_ACCURACY">Invoice Accuracy</SelectItem>
                  <SelectItem value="DELIVERY_PERFORMANCE">Delivery Performance</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vendor Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Performance Details</CardTitle>
          <CardDescription>
            Comprehensive performance metrics for all vendors
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All Vendors</TabsTrigger>
              <TabsTrigger value="top-performers" className="text-green-600">
                Top Performers ({data?.summary.top_performers_count || 0})
              </TabsTrigger>
              <TabsTrigger value="at-risk" className="text-red-600">
                At Risk ({data?.summary.at_risk_suppliers_count || 0})
              </TabsTrigger>
              <TabsTrigger value="improvement-needed" className="text-orange-600">
                Needs Improvement
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value={selectedTab} className="mt-6">
              <DataTable
                columns={vendorPerformanceColumns}
                data={tabData}
                loading={loading}
                searchable={true}
                searchPlaceholder="Search vendors..."
                pageSize={15}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}