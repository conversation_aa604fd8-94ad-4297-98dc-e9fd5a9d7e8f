"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/ui/data-table";
import { 
  ArrowLeft, 
  Download, 
  Filter, 
  Search, 
  TrendingUp, 
  AlertCircle,
  Calendar,
  DollarSign,
  Clock,
  FileSpreadsheet,
  FileText,
  HelpCircle,
  Info
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";
import { getOutstandingPayables } from "@/services/purchase-invoices/reports.service";
import type { OutstandingPayablesData, AgingBucket } from "@/types/purchase-invoices/reports.types";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { formatDate } from "@/lib/utils";

const agingBuckets: AgingBucket[] = [
  { label: "Current (0-30 days)", min: 0, max: 30, color: "bg-green-100 text-green-800" },
  { label: "31-60 days", min: 31, max: 60, color: "bg-yellow-100 text-yellow-800" },
  { label: "61-90 days", min: 61, max: 90, color: "bg-orange-100 text-orange-800" },
  { label: "Over 90 days", min: 91, max: null, color: "bg-red-100 text-red-800" }
];

const outstandingPayablesColumns = [
  {
    accessorKey: "invoice_number",
    header: "Invoice Number",
    cell: ({ row }: any) => (
      <div className="font-medium">{row.getValue("invoice_number")}</div>
    ),
  },
  {
    accessorKey: "supplier.name",
    header: "Supplier",
    cell: ({ row }: any) => (
      <div>
        <div className="font-medium">{row.original.supplier?.name}</div>
        <div className="text-sm text-muted-foreground">{row.original.supplier?.supplier_code}</div>
      </div>
    ),
  },
  {
    accessorKey: "invoice_date",
    header: "Invoice Date",
    cell: ({ row }: any) => formatDate(row.getValue("invoice_date")),
  },
  {
    accessorKey: "due_date",
    header: "Due Date",
    cell: ({ row }: any) => {
      const dueDate = row.getValue("due_date");
      const isOverdue = dueDate && new Date(dueDate) < new Date();
      return (
        <div className={isOverdue ? "text-red-600 font-medium" : ""}>
          {formatDate(dueDate)}
        </div>
      );
    },
  },
  {
    accessorKey: "outstanding_amount",
    header: "Outstanding Amount",
    cell: ({ row }: any) => (
      <CurrencyDisplay 
        amount={row.getValue("outstanding_amount")} 
        currency={row.original.currency_code} 
      />
    ),
  },
  {
    accessorKey: "days_overdue",
    header: "Days Overdue",
    cell: ({ row }: any) => {
      const days = row.getValue("days_overdue") as number;
      if (days <= 0) return <span className="text-muted-foreground">-</span>;
      
      let badgeColor = "bg-green-100 text-green-800";
      if (days > 90) badgeColor = "bg-red-100 text-red-800";
      else if (days > 60) badgeColor = "bg-orange-100 text-orange-800";
      else if (days > 30) badgeColor = "bg-yellow-100 text-yellow-800";
      
      return <Badge className={badgeColor}>{days} days</Badge>;
    },
  },
  {
    accessorKey: "aging_bucket",
    header: "Aging",
    cell: ({ row }: any) => {
      const days = row.original.days_overdue as number;
      const bucket = agingBuckets.find(b => 
        days >= b.min && (b.max === null || days <= b.max)
      );
      return bucket ? <Badge className={bucket.color}>{bucket.label}</Badge> : null;
    },
  },
];

export default function OutstandingPayablesReportPage() {
  const router = useRouter();
  const [data, setData] = useState<OutstandingPayablesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedEntity, setSelectedEntity] = useState<string>("all");
  const [selectedBucket, setSelectedBucket] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("due_date");

  useEffect(() => {
    loadData();
  }, [selectedEntity, selectedBucket, sortBy]);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await getOutstandingPayables({
        entity_id: selectedEntity === "all" ? undefined : selectedEntity,
        aging_bucket: selectedBucket === "all" ? undefined : selectedBucket,
        sort_by: sortBy,
        search: searchTerm || undefined
      });
      setData(result);
    } catch (error) {
      console.error("Error loading outstanding payables:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    loadData();
  };

  const handleExport = (format: "excel" | "csv" | "pdf") => {
    // Implementation will be added in export functionality task
    console.log(`Exporting to ${format}`);
  };

  const filteredInvoices = data?.invoices?.filter(invoice =>
    !searchTerm || 
    invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invoice.supplier?.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <TooltipProvider>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Outstanding Payables Report</h1>
            <p className="text-muted-foreground">
              Track all outstanding invoices with aging analysis and payment priorities
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Outstanding</p>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-3 w-3 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Total amount across all unpaid invoices</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.total_outstanding || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-50 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue Amount</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.overdue_amount || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-yellow-50 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Days Outstanding</p>
                <p className="text-2xl font-bold">{Math.round(data?.summary.average_days_outstanding || 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Invoices</p>
                <p className="text-2xl font-bold">{data?.summary.total_invoices || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Aging Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Aging Analysis</span>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Invoices are categorized by days past due date. Current (0-30 days) are within payment terms.</p>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
          <CardDescription>
            Breakdown of outstanding amounts by aging buckets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {agingBuckets.map((bucket, index) => {
              const amount = data?.aging_analysis[index]?.amount || 0;
              const count = data?.aging_analysis[index]?.count || 0;
              const percentage = data?.summary.total_outstanding 
                ? Math.round((amount / data.summary.total_outstanding) * 100)
                : 0;
              
              return (
                <div key={bucket.label} className="text-center p-4 border rounded-lg">
                  <Badge className={bucket.color + " mb-2"}>{bucket.label}</Badge>
                  <div className="space-y-1">
                    <p className="text-lg font-bold">
                      <CurrencyDisplay amount={amount} currency="USD" />
                    </p>
                    <p className="text-sm text-muted-foreground">{count} invoices</p>
                    <p className="text-sm font-medium">{percentage}%</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                <Input
                  placeholder="Invoice number or supplier..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Entity</label>
              <Select value={selectedEntity} onValueChange={setSelectedEntity}>
                <SelectTrigger>
                  <SelectValue placeholder="Select entity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Entities</SelectItem>
                  <SelectItem value="entity1">Entity 1</SelectItem>
                  <SelectItem value="entity2">Entity 2</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Aging Bucket</label>
              <Select value={selectedBucket} onValueChange={setSelectedBucket}>
                <SelectTrigger>
                  <SelectValue placeholder="Select aging bucket" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Buckets</SelectItem>
                  {agingBuckets.map((bucket) => (
                    <SelectItem key={bucket.label} value={bucket.label}>
                      {bucket.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Sort By</label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="due_date">Due Date</SelectItem>
                  <SelectItem value="amount_desc">Amount (High to Low)</SelectItem>
                  <SelectItem value="amount_asc">Amount (Low to High)</SelectItem>
                  <SelectItem value="days_overdue">Days Overdue</SelectItem>
                  <SelectItem value="supplier_name">Supplier Name</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Outstanding Invoices</CardTitle>
          <CardDescription>
            Detailed list of all outstanding purchase invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={outstandingPayablesColumns}
            data={filteredInvoices}
            loading={loading}
            searchable={false} // We have custom search
          />
        </CardContent>
      </Card>
      </div>
    </TooltipProvider>
  );
}