"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/ui/data-table";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Download, 
  AlertTriangle, 
  TrendingDown,
  TrendingUp,
  PieChart,
  BarChart3,
  FileSpreadsheet,
  FileText,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { useRouter } from "next/navigation";
import { getThreeWayMatchExceptions } from "@/services/purchase-invoices/reports.service";
import type { ThreeWayMatchExceptionData } from "@/types/purchase-invoices/reports.types";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { formatDate } from "@/lib/utils";

const exceptionColumns = [
  {
    accessorKey: "invoice_number",
    header: "Invoice Number",
    cell: ({ row }: any) => (
      <div className="font-medium">{row.getValue("invoice_number")}</div>
    ),
  },
  {
    accessorKey: "supplier.name",
    header: "Supplier",
    cell: ({ row }: any) => (
      <div>
        <div className="font-medium">{row.original.supplier?.name}</div>
        <div className="text-sm text-muted-foreground">{row.original.supplier?.supplier_code}</div>
      </div>
    ),
  },
  {
    accessorKey: "exception_type",
    header: "Exception Type",
    cell: ({ row }: any) => {
      const type = row.getValue("exception_type");
      const colors = {
        "QUANTITY_VARIANCE": "bg-orange-100 text-orange-800",
        "PRICE_VARIANCE": "bg-red-100 text-red-800",
        "MISSING_PO": "bg-purple-100 text-purple-800",
        "MISSING_RECEIPT": "bg-blue-100 text-blue-800",
      };
      return (
        <Badge className={colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800"}>
          {type?.replace(/_/g, " ")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "variance_amount",
    header: "Variance Amount",
    cell: ({ row }: any) => {
      const amount = row.getValue("variance_amount");
      const isNegative = amount < 0;
      return (
        <div className={`flex items-center space-x-1 ${isNegative ? "text-green-600" : "text-red-600"}`}>
          {isNegative ? <TrendingDown className="h-4 w-4" /> : <TrendingUp className="h-4 w-4" />}
          <CurrencyDisplay amount={Math.abs(amount)} currency="USD" />
        </div>
      );
    },
  },
  {
    accessorKey: "variance_percentage",
    header: "Variance %",
    cell: ({ row }: any) => {
      const percentage = row.getValue("variance_percentage");
      const isHigh = Math.abs(percentage) > 5;
      return (
        <Badge className={isHigh ? "bg-red-100 text-red-800" : "bg-yellow-100 text-yellow-800"}>
          {percentage > 0 ? "+" : ""}{percentage.toFixed(2)}%
        </Badge>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }: any) => {
      const status = row.getValue("status");
      const icons = {
        "RESOLVED": <CheckCircle className="h-4 w-4 text-green-600" />,
        "UNRESOLVED": <XCircle className="h-4 w-4 text-red-600" />,
        "IN_PROGRESS": <Clock className="h-4 w-4 text-orange-600" />,
      };
      const colors = {
        "RESOLVED": "bg-green-100 text-green-800",
        "UNRESOLVED": "bg-red-100 text-red-800",
        "IN_PROGRESS": "bg-orange-100 text-orange-800",
      };
      return (
        <div className="flex items-center space-x-2">
          {icons[status as keyof typeof icons]}
          <Badge className={colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"}>
            {status?.replace(/_/g, " ")}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: "Created Date",
    cell: ({ row }: any) => formatDate(row.getValue("created_at")),
  },
];

export default function ThreeWayMatchExceptionsPage() {
  const router = useRouter();
  const [data, setData] = useState<ThreeWayMatchExceptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState("all");

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await getThreeWayMatchExceptions({});
      setData(result);
    } catch (error) {
      console.error("Error loading three-way match exceptions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = (format: "excel" | "csv" | "pdf") => {
    console.log(`Exporting to ${format}`);
  };

  const getTabData = () => {
    if (!data) return [];
    
    switch (selectedTab) {
      case "unresolved":
        return data.exceptions.filter(exc => exc.status === "UNRESOLVED");
      case "high-variance":
        return data.exceptions.filter(exc => Math.abs(exc.variance_percentage) > 5);
      case "price-variance":
        return data.exceptions.filter(exc => exc.exception_type === "PRICE_VARIANCE");
      case "quantity-variance":
        return data.exceptions.filter(exc => exc.exception_type === "QUANTITY_VARIANCE");
      default:
        return data.exceptions;
    }
  };

  const tabData = getTabData();
  const resolutionRate = data?.summary.total_exceptions && data.summary.total_exceptions > 0 
    ? ((data.summary.total_exceptions - (data.summary.unresolved_exceptions || 0)) / data.summary.total_exceptions) * 100 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Three-Way Match Exception Report</h1>
            <p className="text-muted-foreground">
              Analyze variances and exceptions in the three-way matching process
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Exceptions</p>
                <p className="text-2xl font-bold">{data?.summary.total_exceptions || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-50 rounded-lg">
                <XCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unresolved</p>
                <p className="text-2xl font-bold">{data?.summary.unresolved_exceptions || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Variance</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.total_variance_amount || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <BarChart3 className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Variance %</p>
                <p className="text-2xl font-bold">{data?.summary.average_variance_percentage.toFixed(1) || 0}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resolution Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span>Exception Resolution Progress</span>
          </CardTitle>
          <CardDescription>
            Track progress in resolving three-way match exceptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Resolution Rate</span>
              <span className="text-sm text-muted-foreground">{resolutionRate.toFixed(1)}%</span>
            </div>
            <Progress value={resolutionRate} className="w-full" />
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {(data?.summary.total_exceptions || 0) - (data?.summary.unresolved_exceptions || 0)}
                </p>
                <p className="text-sm text-muted-foreground">Resolved</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-orange-600">{data?.summary.unresolved_exceptions || 0}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600">{data?.summary.total_exceptions || 0}</p>
                <p className="text-sm text-muted-foreground">Total</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exception Type Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChart className="h-5 w-5" />
            <span>Exception Type Analysis</span>
          </CardTitle>
          <CardDescription>
            Breakdown of exceptions by type and their resolution status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {data?.trends.exception_types.map((type) => (
              <div key={type.type} className="text-center p-4 border rounded-lg">
                <Badge className="mb-2">{type.type.replace(/_/g, " ")}</Badge>
                <div className="space-y-1">
                  <p className="text-lg font-bold">{type.count}</p>
                  <p className="text-sm text-muted-foreground">
                    {type.percentage.toFixed(1)}% of total
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${type.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Variance Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Variance Analysis</CardTitle>
          <CardDescription>
            Detailed breakdown of quantity and price variances
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold">Quantity Variances</h4>
              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Total Quantity Variances</span>
                  <Badge className="bg-orange-100 text-orange-800">
                    {data?.variance_analysis.quantity_variances || 0}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  Differences between invoiced and received quantities
                </p>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-semibold">Price Variances</h4>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Total Price Variances</span>
                  <Badge className="bg-red-100 text-red-800">
                    {data?.variance_analysis.price_variances || 0}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  Differences between PO prices and invoice prices
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Amount Variance</p>
                <p className="text-lg font-bold">
                  <CurrencyDisplay amount={data?.variance_analysis.amount_variance || 0} currency="USD" />
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Percentage Variance</p>
                <p className="text-lg font-bold">{data?.variance_analysis.percentage_variance.toFixed(2) || 0}%</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Exception List */}
      <Card>
        <CardHeader>
          <CardTitle>Exception Details</CardTitle>
          <CardDescription>
            Comprehensive list of all three-way match exceptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">All Exceptions</TabsTrigger>
              <TabsTrigger value="unresolved" className="text-red-600">
                Unresolved ({data?.summary.unresolved_exceptions || 0})
              </TabsTrigger>
              <TabsTrigger value="high-variance">High Variance</TabsTrigger>
              <TabsTrigger value="price-variance">Price Issues</TabsTrigger>
              <TabsTrigger value="quantity-variance">Quantity Issues</TabsTrigger>
            </TabsList>
            
            <TabsContent value={selectedTab} className="mt-6">
              <DataTable
                columns={exceptionColumns}
                data={tabData}
                loading={loading}
                searchable={true}
                searchPlaceholder="Search exceptions..."
                pageSize={15}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}