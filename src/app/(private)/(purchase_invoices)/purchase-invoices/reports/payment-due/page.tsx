"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/ui/data-table";
import { 
  ArrowLeft, 
  Download, 
  Calendar, 
  DollarSign,
  Clock,
  AlertTriangle,
  TrendingUp,
  FileSpreadsheet,
  FileText
} from "lucide-react";
import { useRouter } from "next/navigation";
import { getPaymentDueReport } from "@/services/purchase-invoices/reports.service";
import type { PaymentDueData, PaymentDueGrouping } from "@/types/purchase-invoices/reports.types";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { formatDate } from "@/lib/utils";

const paymentDueColumns = [
  {
    accessorKey: "invoice_number",
    header: "Invoice Number",
    cell: ({ row }: any) => (
      <div className="font-medium">{row.getValue("invoice_number")}</div>
    ),
  },
  {
    accessorKey: "supplier.name",
    header: "Supplier",
    cell: ({ row }: any) => (
      <div>
        <div className="font-medium">{row.original.supplier?.name}</div>
        <div className="text-sm text-muted-foreground">{row.original.supplier?.supplier_code}</div>
      </div>
    ),
  },
  {
    accessorKey: "due_date",
    header: "Due Date",
    cell: ({ row }: any) => {
      const dueDate = row.getValue("due_date");
      const today = new Date();
      const due = new Date(dueDate);
      const isOverdue = due < today;
      const isDueSoon = due <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      return (
        <div className={`flex items-center space-x-2 ${isOverdue ? "text-red-600" : isDueSoon ? "text-orange-600" : ""}`}>
          <span>{formatDate(dueDate)}</span>
          {isOverdue && <Badge variant="destructive" className="text-xs">Overdue</Badge>}
          {!isOverdue && isDueSoon && <Badge variant="secondary" className="text-xs">Due Soon</Badge>}
        </div>
      );
    },
  },
  {
    accessorKey: "outstanding_amount",
    header: "Amount Due",
    cell: ({ row }: any) => (
      <CurrencyDisplay 
        amount={row.getValue("outstanding_amount")} 
        currency={row.original.currency_code} 
      />
    ),
  },
  {
    accessorKey: "payment_priority",
    header: "Priority",
    cell: ({ row }: any) => {
      const dueDate = new Date(row.original.due_date);
      const today = new Date();
      const isOverdue = dueDate < today;
      const isDueSoon = dueDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      if (isOverdue) {
        return <Badge className="bg-red-100 text-red-800">High</Badge>;
      } else if (isDueSoon) {
        return <Badge className="bg-orange-100 text-orange-800">Medium</Badge>;
      } else {
        return <Badge className="bg-green-100 text-green-800">Low</Badge>;
      }
    },
  },
  {
    accessorKey: "supplier.payment_terms",
    header: "Payment Terms",
    cell: ({ row }: any) => {
      const terms = row.getValue("supplier.payment_terms");
      return terms ? `${terms} days` : "-";
    },
  },
];

export default function PaymentDueReportPage() {
  const router = useRouter();
  const [data, setData] = useState<PaymentDueData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState("all");

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await getPaymentDueReport({});
      setData(result);
    } catch (error) {
      console.error("Error loading payment due report:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = (format: "excel" | "csv" | "pdf") => {
    console.log(`Exporting to ${format}`);
  };

  const getTabData = () => {
    if (!data) return [];
    
    const allInvoices = data.groupings.flatMap(group => group.invoices);
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    switch (selectedTab) {
      case "overdue":
        return allInvoices.filter(inv => new Date(inv.due_date!) < today);
      case "due-week":
        return allInvoices.filter(inv => {
          const dueDate = new Date(inv.due_date!);
          return dueDate >= today && dueDate <= nextWeek;
        });
      case "upcoming":
        return allInvoices.filter(inv => new Date(inv.due_date!) > nextWeek);
      default:
        return allInvoices;
    }
  };

  const tabData = getTabData();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Due Report</h1>
            <p className="text-muted-foreground">
              Track upcoming payments grouped by due dates and priority levels
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Due Amount</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.total_due_amount || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-50 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Due This Week</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.due_this_week || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue Amount</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.overdue_amount || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Due Next Week</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.summary.due_next_week || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Priority Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Priority Breakdown</CardTitle>
          <CardDescription>
            Categorization of payments by urgency and risk level
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg bg-red-50">
              <Badge className="bg-red-100 text-red-800 mb-2">High Priority</Badge>
              <div className="space-y-1">
                <p className="text-lg font-bold">
                  <CurrencyDisplay amount={data?.priority_breakdown.high.amount || 0} currency="USD" />
                </p>
                <p className="text-sm text-muted-foreground">
                  {data?.priority_breakdown.high.count || 0} overdue invoices
                </p>
                <p className="text-xs text-red-600 font-medium">Immediate Action Required</p>
              </div>
            </div>
            
            <div className="text-center p-4 border rounded-lg bg-orange-50">
              <Badge className="bg-orange-100 text-orange-800 mb-2">Medium Priority</Badge>
              <div className="space-y-1">
                <p className="text-lg font-bold">
                  <CurrencyDisplay amount={data?.priority_breakdown.medium.amount || 0} currency="USD" />
                </p>
                <p className="text-sm text-muted-foreground">
                  {data?.priority_breakdown.medium.count || 0} due this week
                </p>
                <p className="text-xs text-orange-600 font-medium">Plan Payment This Week</p>
              </div>
            </div>
            
            <div className="text-center p-4 border rounded-lg bg-green-50">
              <Badge className="bg-green-100 text-green-800 mb-2">Low Priority</Badge>
              <div className="space-y-1">
                <p className="text-lg font-bold">
                  <CurrencyDisplay amount={data?.priority_breakdown.low.amount || 0} currency="USD" />
                </p>
                <p className="text-sm text-muted-foreground">
                  {data?.priority_breakdown.low.count || 0} future payments
                </p>
                <p className="text-xs text-green-600 font-medium">Schedule for Later</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Schedule by Date */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Schedule</CardTitle>
          <CardDescription>
            Upcoming payments grouped by due date
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data?.groupings.slice(0, 10).map((group: PaymentDueGrouping) => {
              const dueDate = new Date(group.date);
              const today = new Date();
              const isOverdue = dueDate < today;
              const isDueSoon = dueDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
              
              return (
                <div 
                  key={group.date} 
                  className={`flex items-center justify-between p-4 border rounded-lg ${
                    isOverdue ? "bg-red-50 border-red-200" : 
                    isDueSoon ? "bg-orange-50 border-orange-200" : 
                    "bg-gray-50"
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-lg ${
                      isOverdue ? "bg-red-100" : 
                      isDueSoon ? "bg-orange-100" : 
                      "bg-gray-100"
                    }`}>
                      <Calendar className={`h-5 w-5 ${
                        isOverdue ? "text-red-600" : 
                        isDueSoon ? "text-orange-600" : 
                        "text-gray-600"
                      }`} />
                    </div>
                    <div>
                      <p className="font-medium">{formatDate(group.date)}</p>
                      <p className="text-sm text-muted-foreground">
                        {group.invoice_count} invoice{group.invoice_count !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">
                      <CurrencyDisplay amount={group.total_amount} currency="USD" />
                    </p>
                    {isOverdue && (
                      <Badge variant="destructive" className="text-xs">Overdue</Badge>
                    )}
                    {!isOverdue && isDueSoon && (
                      <Badge variant="secondary" className="text-xs">Due Soon</Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Invoice List */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Details</CardTitle>
          <CardDescription>
            Detailed breakdown of all invoices requiring payment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All Payments</TabsTrigger>
              <TabsTrigger value="overdue" className="text-red-600">
                Overdue ({data?.priority_breakdown.high.count || 0})
              </TabsTrigger>
              <TabsTrigger value="due-week" className="text-orange-600">
                Due This Week ({data?.priority_breakdown.medium.count || 0})
              </TabsTrigger>
              <TabsTrigger value="upcoming" className="text-green-600">
                Upcoming ({data?.priority_breakdown.low.count || 0})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value={selectedTab} className="mt-6">
              <DataTable
                columns={paymentDueColumns}
                data={tabData}
                loading={loading}
                searchable={true}
                searchPlaceholder="Search invoices..."
                pageSize={15}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}