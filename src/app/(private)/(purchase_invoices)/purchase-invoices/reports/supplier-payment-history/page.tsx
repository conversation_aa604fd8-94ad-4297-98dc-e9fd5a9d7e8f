"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/ui/data-table";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Download, 
  CreditCard, 
  Clock,
  TrendingUp,
  DollarSign,
  Calendar,
  Percent,
  FileSpreadsheet,
  FileText,
  Building
} from "lucide-react";
import { useRouter } from "next/navigation";
import { getSupplierPaymentHistory } from "@/services/purchase-invoices/reports.service";
import type { SupplierPaymentHistoryData } from "@/types/purchase-invoices/reports.types";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { formatDate } from "@/lib/utils";

const paymentHistoryColumns = [
  {
    accessorKey: "payment_date",
    header: "Payment Date",
    cell: ({ row }: any) => formatDate(row.getValue("payment_date")),
  },
  {
    accessorKey: "invoice_number",
    header: "Invoice Number",
    cell: ({ row }: any) => (
      <div className="font-medium">{row.getValue("invoice_number")}</div>
    ),
  },
  {
    accessorKey: "amount",
    header: "Payment Amount",
    cell: ({ row }: any) => (
      <CurrencyDisplay 
        amount={row.getValue("amount")} 
        currency={row.original.currency_code} 
      />
    ),
  },
  {
    accessorKey: "payment_method",
    header: "Payment Method",
    cell: ({ row }: any) => {
      const method = row.getValue("payment_method");
      const methodColors = {
        "BANK_TRANSFER": "bg-blue-100 text-blue-800",
        "CHEQUE": "bg-green-100 text-green-800",
        "CASH": "bg-orange-100 text-orange-800",
        "ONLINE": "bg-purple-100 text-purple-800",
        "CREDIT_CARD": "bg-pink-100 text-pink-800",
      };
      return (
        <Badge className={methodColors[method as keyof typeof methodColors] || "bg-gray-100 text-gray-800"}>
          {method?.replace(/_/g, " ")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "early_payment_discount",
    header: "Early Payment Discount",
    cell: ({ row }: any) => {
      const discount = row.getValue("early_payment_discount");
      return discount ? (
        <div className="text-green-600 font-medium">
          <CurrencyDisplay amount={discount} currency={row.original.currency_code} />
        </div>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    accessorKey: "reference_number",
    header: "Reference",
    cell: ({ row }: any) => {
      const ref = row.getValue("reference_number");
      return ref ? (
        <span className="font-mono text-sm">{ref}</span>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    accessorKey: "net_amount",
    header: "Net Amount",
    cell: ({ row }: any) => (
      <div className="font-semibold">
        <CurrencyDisplay 
          amount={row.getValue("net_amount")} 
          currency={row.original.currency_code} 
        />
      </div>
    ),
  },
];

export default function SupplierPaymentHistoryPage() {
  const router = useRouter();
  const [data, setData] = useState<SupplierPaymentHistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedSupplier, setSelectedSupplier] = useState<string>("supplier1");

  useEffect(() => {
    loadData();
  }, [selectedSupplier]);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await getSupplierPaymentHistory(selectedSupplier, {});
      setData(result);
    } catch (error) {
      console.error("Error loading supplier payment history:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = (format: "excel" | "csv" | "pdf") => {
    console.log(`Exporting to ${format}`);
  };

  const getPaymentTimingColor = (timing: string) => {
    switch (timing) {
      case "early": return "text-green-600";
      case "on_time": return "text-blue-600";
      case "late": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Supplier Payment History</h1>
            <p className="text-muted-foreground">
              Comprehensive payment history and analytics by supplier
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Supplier Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5" />
            <span>Supplier Selection</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Supplier</label>
              <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a supplier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="supplier1">ABC Technologies Ltd</SelectItem>
                  <SelectItem value="supplier2">Global Supplies Inc</SelectItem>
                  <SelectItem value="supplier3">TechCorp Solutions</SelectItem>
                  <SelectItem value="supplier4">Office Dynamics</SelectItem>
                  <SelectItem value="supplier5">Premium Services Co</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {data?.supplier && (
              <div className="md:col-span-2">
                <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{data.supplier.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {data.supplier.supplier_code} • Payment Terms: {data.supplier.payment_terms || "N/A"} days
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Paid</p>
                <p className="text-2xl font-bold">
                  <CurrencyDisplay amount={data?.stats.total_amount || 0} currency="USD" />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <CreditCard className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Payments</p>
                <p className="text-2xl font-bold">{data?.stats.total_payments || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-50 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Payment Days</p>
                <p className="text-2xl font-bold">{Math.round(data?.stats.average_payment_days || 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-purple-50 rounded-lg">
                <Percent className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">On-Time Rate</p>
                <p className="text-2xl font-bold">{data?.stats.on_time_payment_percentage.toFixed(1) || 0}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Performance Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Payment Performance</CardTitle>
            <CardDescription>
              Analysis of payment timing and efficiency
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">On-Time Payments</span>
                  <span className="text-sm text-muted-foreground">
                    {data?.stats.on_time_payment_percentage.toFixed(1) || 0}%
                  </span>
                </div>
                <Progress value={data?.stats.on_time_payment_percentage || 0} className="w-full" />
              </div>
              
              <div className="grid grid-cols-3 gap-4 pt-4">
                <div className="text-center">
                  <p className="text-lg font-bold text-green-600">
                    {data?.trends.payment_timing.early || 0}%
                  </p>
                  <p className="text-xs text-muted-foreground">Early Payments</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold text-blue-600">
                    {data?.trends.payment_timing.on_time || 0}%
                  </p>
                  <p className="text-xs text-muted-foreground">On-Time Payments</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold text-red-600">
                    {data?.trends.payment_timing.late || 0}%
                  </p>
                  <p className="text-xs text-muted-foreground">Late Payments</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Early Payment Discounts</CardTitle>
            <CardDescription>
              Savings achieved through early payment programs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-green-800">Total Discounts Earned</p>
                  <p className="text-2xl font-bold text-green-600">
                    <CurrencyDisplay amount={data?.stats.early_payment_discount_total || 0} currency="USD" />
                  </p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Avg Payment Amount</p>
                  <p className="font-semibold">
                    <CurrencyDisplay amount={data?.stats.average_payment_amount || 0} currency="USD" />
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">Preferred Method</p>
                  <p className="font-semibold">{data?.stats.preferred_payment_method?.replace(/_/g, " ") || "N/A"}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Payment Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Monthly Payment Trends</span>
          </CardTitle>
          <CardDescription>
            Payment volume and frequency over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Placeholder for monthly trends - in real implementation, this would be a chart */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-xl font-bold">$45,230</p>
                <p className="text-xs text-green-600">+12% from last month</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-muted-foreground">Last Month</p>
                <p className="text-xl font-bold">$40,410</p>
                <p className="text-xs text-blue-600">8 payments</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-muted-foreground">3 Months Ago</p>
                <p className="text-xl font-bold">$38,920</p>
                <p className="text-xs text-orange-600">6 payments</p>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Payment Insights</h4>
              <ul className="text-sm space-y-1 text-blue-700">
                <li>• Consistent payment pattern with {data?.stats.average_payment_days.toFixed(0) || 0}-day average</li>
                <li>• Strong early payment discount utilization</li>
                <li>• Preferred payment method: {data?.stats.preferred_payment_method?.replace(/_/g, " ") || "Bank Transfer"}</li>
                <li>• {data?.stats.on_time_payment_percentage.toFixed(0) || 0}% on-time payment rate indicates reliable partnership</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment History Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription>
            Detailed record of all payments made to this supplier
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={paymentHistoryColumns}
            data={data?.payments || []}
            loading={loading}
            searchable={true}
            searchPlaceholder="Search payments..."
            pageSize={15}
          />
        </CardContent>
      </Card>
    </div>
  );
}