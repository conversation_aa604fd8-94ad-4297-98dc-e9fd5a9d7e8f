"use client";

import { useState, useEffect, Suspense } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PurchaseInvoicesTable } from "@/components/table/purchase-invoices-table";
import Link from "next/link";
import { 
  PlusCircle, 
  CreditCard, 
  Clock, 
  AlertTriangle, 
  CheckCircle2,
  TrendingUp 
} from "lucide-react";
import type { PurchaseInvoiceSummary } from "@/types/purchase-invoices/purchase-invoices.types";
import { getPurchaseInvoicesSummary } from "@/services/purchase-invoices/purchase-invoices.service";

export default function PurchaseInvoicesPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Purchase Invoices",
        isCurrentPage: true,
      },
    ],
  };

  const [summary, setSummary] = useState<PurchaseInvoiceSummary>({
    total_count: 0,
    outstanding_amount: 0,
    overdue_count: 0,
    pending_approval_count: 0,
    this_month_amount: 0,
    average_processing_days: 0,
  });

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      const summaryData = await getPurchaseInvoicesSummary();
      setSummary(summaryData);
    } catch (error) {
      console.error("Error loading summary:", error);
    }
  };

  const handleSummaryUpdate = (newSummary: PurchaseInvoiceSummary) => {
    setSummary(newSummary);
  };


  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Purchase Invoices
            </h1>
            <p className="text-sm text-gray-600">
              Manage supplier invoices, approvals, and payments
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild size="sm">
              <Link href="/purchase-invoices/add">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Invoice
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/purchase-invoices/payment">
                <CreditCard className="mr-2 h-4 w-4" />
                Process Payments
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/purchase-invoices/match">
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Three-Way Match
              </Link>
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-6 mb-6">
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total Invoices
              </p>
              <p className="text-2xl font-bold">{summary.total_count.toLocaleString()}</p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Outstanding Amount
              </p>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(summary.outstanding_amount)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <AlertTriangle className="mr-1 h-4 w-4" />
                Overdue
              </p>
              <p className="text-2xl font-bold text-red-600">
                {summary.overdue_count.toLocaleString()}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                Pending Approval
              </p>
              <p className="text-2xl font-bold text-amber-600">
                {summary.pending_approval_count.toLocaleString()}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <TrendingUp className="mr-1 h-4 w-4" />
                This Month
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(summary.this_month_amount)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Avg. Processing
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {summary.average_processing_days.toFixed(1)} days
              </p>
            </div>
          </Card>
        </div>

        <Suspense fallback={
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        }>
          <PurchaseInvoicesTable 
            summary={summary}
            onSummaryUpdate={handleSummaryUpdate}
          />
        </Suspense>
      </div>
    </AdminPanelLayout>
  );
}