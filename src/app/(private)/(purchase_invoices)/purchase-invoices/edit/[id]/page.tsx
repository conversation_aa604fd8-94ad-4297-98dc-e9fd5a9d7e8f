"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { PurchaseInvoiceForm } from "@/components/purchase-invoices/forms/purchase-invoice-form";
import { getPurchaseInvoiceById } from "@/services/purchase-invoices/purchase-invoices.service";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

export default function EditPurchaseInvoicePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [invoice, setInvoice] = useState<PurchaseInvoice | null>(null);

  useEffect(() => {
    const loadInvoice = async () => {
      if (!params.id || typeof params.id !== 'string') {
        toast({
          title: "Error",
          description: "Invalid invoice ID",
          variant: "destructive",
        });
        router.push("/purchase-invoices");
        return;
      }

      try {
        setLoading(true);
        const data = await getPurchaseInvoiceById(params.id);
        
        if (!data) {
          throw new Error("Invoice not found");
        }
        
        setInvoice(data);
      } catch (error) {
        console.error("Error loading invoice:", error);
        toast({
          title: "Error",
          description: "Failed to load purchase invoice",
          variant: "destructive",
        });
        router.push("/purchase-invoices");
      } finally {
        setLoading(false);
      }
    };

    loadInvoice();
  }, [params.id, router, toast]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <p className="text-muted-foreground">Invoice not found</p>
          <Link href="/purchase-invoices" className="text-primary hover:underline mt-4 inline-block">
            Back to List
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Link
          href="/purchase-invoices"
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to List
        </Link>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Purchase Invoice</h1>
        <p className="text-muted-foreground">Update invoice {invoice.invoice_number}</p>
      </div>

      <PurchaseInvoiceForm 
        isEditing={true}
        invoiceId={params.id as string}
        initialData={invoice}
      />
    </div>
  );
}