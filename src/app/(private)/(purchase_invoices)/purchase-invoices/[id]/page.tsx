"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { PurchaseInvoiceDetail } from "@/components/purchase-invoices/detail/purchase-invoice-detail";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { getPurchaseInvoiceById } from "@/services/purchase-invoices/purchase-invoices.service";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

export default function PurchaseInvoiceDetailPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [purchaseInvoice, setPurchaseInvoice] = useState<PurchaseInvoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPurchaseInvoice();
  }, [params.id]);

  const loadPurchaseInvoice = async () => {
    try {
      setLoading(true);
      const data = await getPurchaseInvoiceById(params.id);
      
      if (data) {
        setPurchaseInvoice(data);
      } else {
        setError("Purchase invoice not found");
      }
    } catch (error) {
      console.error("Error loading purchase invoice:", error);
      setError("Failed to load purchase invoice");
      toast({
        title: "Error",
        description: "Failed to load purchase invoice details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/purchase-invoices/edit/${params.id}`);
  };

  const handlePrint = () => {
    toast({
      title: "Info",
      description: "Print functionality will be implemented",
    });
  };

  const handleEmail = () => {
    toast({
      title: "Info", 
      description: "Email functionality will be implemented",
    });
  };

  // Approval functionality is now handled by the PurchaseInvoiceDetail component

  const handlePay = () => {
    router.push(`/purchase-invoices/pay?invoice_id=${params.id}`);
  };

  const handleCancel = () => {
    toast({
      title: "Info",
      description: "Cancel functionality will be implemented",
    });
  };

  const handleClone = () => {
    toast({
      title: "Info",
      description: "Clone functionality will be implemented",
    });
  };

  const handleMatchPO = () => {
    toast({
      title: "Info",
      description: "Three-way match functionality will be implemented",
    });
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Purchase Invoices", href: "/purchase-invoices" },
            { title: `Loading...`, isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (error || !purchaseInvoice) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Purchase Invoices", href: "/purchase-invoices" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/purchase-invoices">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to List
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Invoices", href: "/purchase-invoices" },
          { title: purchaseInvoice.invoice_number, isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/purchase-invoices">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        {/* Purchase Invoice Detail Component */}
        <PurchaseInvoiceDetail
          purchaseInvoice={purchaseInvoice}
          onEdit={handleEdit}
          onPrint={handlePrint}
          onEmail={handleEmail}
          onPay={handlePay}
          onCancel={handleCancel}
          onClone={handleClone}
          onMatchPO={handleMatchPO}
          onRefresh={loadPurchaseInvoice}
        />
      </div>
    </AdminPanelLayout>
  );
}