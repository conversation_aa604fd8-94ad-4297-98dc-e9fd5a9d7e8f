/**
 * Purchase Invoice Payment Processing Page
 * 
 * This comprehensive payment processing interface handles supplier payments efficiently with:
 * 
 * 1. Payment Queue Management:
 *    - Displays approved invoices ready for payment
 *    - Advanced filtering by supplier, entity, currency, due dates
 *    - Payment priority indicators (overdue, early discounts)
 *    - Bulk selection for batch payment creation
 * 
 * 2. Payment Batch Creation:
 *    - Groups payments by supplier, currency, payment method
 *    - Payment validation and authorization workflows
 *    - Bank account selection and balance verification
 *    - Payment scheduling (immediate vs scheduled)
 * 
 * 3. Authorization & Approval:
 *    - Multi-level authorization based on payment amounts
 *    - Dual authorization for large transactions
 *    - Authorization limits checking by user role
 *    - Complete audit trail of approvals
 * 
 * 4. Payment Summary & Reporting:
 *    - Real-time payment statistics by currency
 *    - Payment method breakdown and analytics
 *    - Bank balance monitoring and alerts
 *    - Payment file generation for banking systems
 * 
 * Features:
 * - Multi-currency payment support with real-time rates
 * - Early payment discount automation
 * - Payment method routing (bank transfer, check, etc.)
 * - Payment file export (CSV, XML, bank formats)
 * - Integration with banking APIs (placeholder implementation)
 * - Comprehensive error handling and validation
 * - Enterprise-level security and audit trails
 */

"use client";

import { useState, useEffect, Suspense } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  CreditCard, 
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle2,
  Clock,
  FileText,
  Download,
  Upload,
  Filter,
  Search,
  Users,
  Building,
  Banknote,
  Shield,
  Eye,
  Plus,
  X,
  ArrowUpDown,
  TrendingUp,
  Calculator,
  Globe,
  Zap
} from "lucide-react";
import type { 
  PurchaseInvoice, 
  PaymentMethod, 
  PaymentStatus,
  SupplierInfo,
  EntityInfo
} from "@/types/purchase-invoices/purchase-invoices.types";
import { format } from "date-fns";

// Enhanced types for payment processing
interface PaymentBatch {
  id: string;
  batch_name: string;
  supplier_id: string;
  currency_code: string;
  payment_method: PaymentMethod;
  payment_date: string;
  bank_account_id: string;
  total_amount: number;
  invoice_count: number;
  status: "DRAFT" | "PENDING_APPROVAL" | "APPROVED" | "PROCESSING" | "COMPLETED" | "FAILED";
  created_by: string;
  created_at: string;
  authorized_by?: string;
  authorized_at?: string;
  invoices: PaymentInvoiceSelection[];
}

interface PaymentInvoiceSelection {
  invoice: PurchaseInvoice;
  payment_amount: number;
  early_discount_amount: number;
  currency_rate: number;
  selected: boolean;
  priority: "HIGH" | "MEDIUM" | "LOW";
}

interface BankAccount {
  id: string;
  account_name: string;
  account_number: string;
  bank_name: string;
  currency_code: string;
  balance: number;
  status: "ACTIVE" | "INACTIVE";
}

interface PaymentApprovalLimit {
  user_id: string;
  max_amount: number;
  currency_code: string;
  requires_dual_approval: boolean;
}

interface PaymentFileFormat {
  id: string;
  name: string;
  format: "CSV" | "XML" | "MT940" | "ACH" | "WIRE";
  bank_supported: boolean;
}

export default function PaymentProcessingPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Purchase Invoices",
        href: "/purchase-invoices",
      },
      {
        title: "Payment Processing",
        isCurrentPage: true,
      },
    ],
  };

  // State management
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("queue");
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const [paymentBatches, setPaymentBatches] = useState<PaymentBatch[]>([]);
  const [currentBatch, setCurrentBatch] = useState<PaymentBatch | null>(null);
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [approvalLimits, setApprovalLimits] = useState<PaymentApprovalLimit[]>([]);
  
  // Mock data - would be fetched from API
  const [payableInvoices] = useState<PurchaseInvoice[]>([
    {
      id: "inv-001",
      invoice_number: "INV-2024-001",
      supplier_id: "sup-001",
      entity_id: "ent-001",
      purchase_order_id: "po-001",
      invoice_date: "2024-01-15",
      due_date: "2024-02-14",
      received_date: "2024-01-16",
      total_amount: 15000.00,
      tax_amount: 1500.00,
      discount_amount: 0,
      status: "APPROVED" as any,
      payment_status: "PENDING" as PaymentStatus,
      currency_code: "USD",
      reference_number: "REF-001",
      supplier_reference: "SUP-REF-001",
      notes: null,
      created_at: "2024-01-15T10:00:00Z",
      created_by: "user-001",
      updated_at: null,
      updated_by: null,
      approved_at: "2024-01-16T14:30:00Z",
      approved_by: "user-002",
      outstanding_amount: 15000.00,
      days_overdue: 5,
      early_payment_discount: 150.00,
      supplier: {
        id: "sup-001",
        supplier_code: "SUP001",
        name: "Tech Solutions Inc.",
        display_name: "Tech Solutions",
        email: "<EMAIL>",
        phone: "******-0123",
        payment_terms: 30,
        status: "ACTIVE"
      },
      entity: {
        id: "ent-001",
        name: "Main Entity",
        code: "MAIN"
      }
    },
    {
      id: "inv-002",
      invoice_number: "INV-2024-002",
      supplier_id: "sup-002",
      entity_id: "ent-001",
      purchase_order_id: "po-002",
      invoice_date: "2024-01-20",
      due_date: "2024-02-19",
      received_date: "2024-01-21",
      total_amount: 8500.00,
      tax_amount: 850.00,
      discount_amount: 0,
      status: "APPROVED" as any,
      payment_status: "PENDING" as PaymentStatus,
      currency_code: "USD",
      reference_number: "REF-002",
      supplier_reference: "SUP-REF-002",
      notes: null,
      created_at: "2024-01-20T09:00:00Z",
      created_by: "user-001",
      updated_at: null,
      updated_by: null,
      approved_at: "2024-01-21T16:00:00Z",
      approved_by: "user-002",
      outstanding_amount: 8500.00,
      days_overdue: 0,
      early_payment_discount: 85.00,
      supplier: {
        id: "sup-002",
        supplier_code: "SUP002",
        name: "Office Supplies Co.",
        display_name: "Office Supplies",
        email: "<EMAIL>",
        phone: "******-0124",
        payment_terms: 30,
        status: "ACTIVE"
      },
      entity: {
        id: "ent-001",
        name: "Main Entity",
        code: "MAIN"
      }
    }
  ]);

  const [filters, setFilters] = useState({
    supplier_id: "",
    entity_id: "",
    currency_code: "",
    payment_method: "",
    due_date_from: "",
    due_date_to: "",
    min_amount: "",
    max_amount: "",
    priority: "",
    search: ""
  });

  // Mock bank accounts
  useEffect(() => {
    setBankAccounts([
      {
        id: "bank-001",
        account_name: "Main Operating Account",
        account_number: "****1234",
        bank_name: "First National Bank",
        currency_code: "USD",
        balance: 250000.00,
        status: "ACTIVE"
      },
      {
        id: "bank-002",
        account_name: "Euro Account",
        account_number: "****5678",
        bank_name: "International Bank",
        currency_code: "EUR",
        balance: 180000.00,
        status: "ACTIVE"
      }
    ]);

    setApprovalLimits([
      {
        user_id: "current-user",
        max_amount: 50000.00,
        currency_code: "USD",
        requires_dual_approval: true
      }
    ]);
  }, []);

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const getPriorityBadge = (invoice: PurchaseInvoice) => {
    if (invoice.days_overdue && invoice.days_overdue > 0) {
      return <Badge variant="destructive" className="text-xs">Overdue</Badge>;
    }
    if (invoice.early_payment_discount && invoice.early_payment_discount > 0) {
      return <Badge variant="secondary" className="text-xs">Early Discount</Badge>;
    }
    return <Badge variant="outline" className="text-xs">Normal</Badge>;
  };

  const handleInvoiceSelection = (invoiceId: string, selected: boolean) => {
    if (selected) {
      setSelectedInvoices(prev => [...prev, invoiceId]);
    } else {
      setSelectedInvoices(prev => prev.filter(id => id !== invoiceId));
    }
  };

  const handleBulkSelection = (selected: boolean) => {
    if (selected) {
      setSelectedInvoices(payableInvoices.map(inv => inv.id));
    } else {
      setSelectedInvoices([]);
    }
  };

  const createPaymentBatch = () => {
    const selectedInvoiceData = payableInvoices.filter(inv => selectedInvoices.includes(inv.id));
    
    if (selectedInvoiceData.length === 0) {
      alert("Please select invoices to create a payment batch");
      return;
    }

    // Group by supplier and currency for batch creation
    const batches = new Map<string, PurchaseInvoice[]>();
    
    selectedInvoiceData.forEach(invoice => {
      const key = `${invoice.supplier_id}-${invoice.currency_code}`;
      if (!batches.has(key)) {
        batches.set(key, []);
      }
      batches.get(key)?.push(invoice);
    });

    // Create batch objects
    const newBatches: PaymentBatch[] = Array.from(batches.entries()).map(([key, invoices], index) => {
      const [supplierId, currencyCode] = key.split('-');
      const totalAmount = invoices.reduce((sum, inv) => sum + (inv.outstanding_amount || inv.total_amount), 0);
      
      return {
        id: `batch-${Date.now()}-${index}`,
        batch_name: `Payment Batch ${invoices[0].supplier?.name} - ${currencyCode}`,
        supplier_id: supplierId,
        currency_code: currencyCode,
        payment_method: "BANK_TRANSFER" as PaymentMethod,
        payment_date: format(new Date(), 'yyyy-MM-dd'),
        bank_account_id: bankAccounts.find(acc => acc.currency_code === currencyCode)?.id || bankAccounts[0].id,
        total_amount: totalAmount,
        invoice_count: invoices.length,
        status: "DRAFT",
        created_by: "current-user",
        created_at: new Date().toISOString(),
        invoices: invoices.map(invoice => ({
          invoice,
          payment_amount: invoice.outstanding_amount || invoice.total_amount,
          early_discount_amount: invoice.early_payment_discount || 0,
          currency_rate: 1.0,
          selected: true,
          priority: (invoice.days_overdue && invoice.days_overdue > 0) ? "HIGH" : "MEDIUM"
        }))
      };
    });

    setPaymentBatches(prev => [...prev, ...newBatches]);
    setSelectedInvoices([]);
    setActiveTab("batches");
  };

  const PaymentQueueSection = () => (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Payment Queue Filters
          </CardTitle>
          <CardDescription>
            Filter invoices ready for payment processing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Invoice number, supplier..."
                  className="pl-8"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="supplier">Supplier</Label>
              <Select value={filters.supplier_id} onValueChange={(value) => setFilters(prev => ({ ...prev, supplier_id: value === "ALL" ? "" : value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="All suppliers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All suppliers</SelectItem>
                  <SelectItem value="sup-001">Tech Solutions Inc.</SelectItem>
                  <SelectItem value="sup-002">Office Supplies Co.</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={filters.currency_code} onValueChange={(value) => setFilters(prev => ({ ...prev, currency_code: value === "ALL" ? "" : value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="All currencies" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All currencies</SelectItem>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="GBP">GBP</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select value={filters.priority} onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value === "ALL" ? "" : value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="All priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All priorities</SelectItem>
                  <SelectItem value="HIGH">High (Overdue)</SelectItem>
                  <SelectItem value="MEDIUM">Medium (Early Discount)</SelectItem>
                  <SelectItem value="LOW">Low (Normal)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Queue Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-5 w-5" />
                Invoices Ready for Payment ({payableInvoices.length})
              </CardTitle>
              <CardDescription>
                Approved invoices awaiting payment processing
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkSelection(selectedInvoices.length !== payableInvoices.length)}
              >
                {selectedInvoices.length === payableInvoices.length ? "Deselect All" : "Select All"}
              </Button>
              <Button
                onClick={createPaymentBatch}
                disabled={selectedInvoices.length === 0}
                className="bg-green-600 hover:bg-green-700"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Payment Batch ({selectedInvoices.length})
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedInvoices.length === payableInvoices.length && payableInvoices.length > 0}
                      onCheckedChange={handleBulkSelection}
                    />
                  </TableHead>
                  <TableHead>Invoice</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Currency</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Early Discount</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payableInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedInvoices.includes(invoice.id)}
                        onCheckedChange={(checked) => handleInvoiceSelection(invoice.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{invoice.invoice_number}</div>
                        <div className="text-sm text-muted-foreground">
                          {invoice.supplier_reference}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{invoice.supplier?.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Terms: {invoice.supplier?.payment_terms} days
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={invoice.days_overdue && invoice.days_overdue > 0 ? "text-red-600" : ""}>
                        {invoice.due_date ? format(new Date(invoice.due_date), 'MMM dd, yyyy') : 'N/A'}
                        {invoice.days_overdue && invoice.days_overdue > 0 && (
                          <div className="text-xs text-red-600">
                            {invoice.days_overdue} days overdue
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(invoice.outstanding_amount || invoice.total_amount, invoice.currency_code)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{invoice.currency_code}</Badge>
                    </TableCell>
                    <TableCell>
                      {getPriorityBadge(invoice)}
                    </TableCell>
                    <TableCell>
                      {invoice.early_payment_discount && invoice.early_payment_discount > 0 ? (
                        <div className="text-green-600 font-medium">
                          {formatCurrency(invoice.early_payment_discount, invoice.currency_code)}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const PaymentBatchesSection = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Banknote className="mr-2 h-5 w-5" />
            Payment Batches ({paymentBatches.length})
          </CardTitle>
          <CardDescription>
            Manage payment batches and processing
          </CardDescription>
        </CardHeader>
        <CardContent>
          {paymentBatches.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Banknote className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>No payment batches created yet</p>
              <p className="text-sm">Select invoices from the Payment Queue to create batches</p>
            </div>
          ) : (
            <div className="space-y-4">
              {paymentBatches.map((batch) => (
                <Card key={batch.id} className="border-l-4 border-l-blue-500">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{batch.batch_name}</CardTitle>
                        <CardDescription>
                          Created {format(new Date(batch.created_at), 'MMM dd, yyyy HH:mm')}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={batch.status === "DRAFT" ? "secondary" : "default"}>
                          {batch.status}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentBatch(batch)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <Label className="text-xs text-muted-foreground">Total Amount</Label>
                        <p className="text-lg font-semibold">
                          {formatCurrency(batch.total_amount, batch.currency_code)}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Invoices</Label>
                        <p className="text-lg font-semibold">{batch.invoice_count}</p>
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Payment Method</Label>
                        <p className="text-sm">{batch.payment_method.replace('_', ' ')}</p>
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Payment Date</Label>
                        <p className="text-sm">{format(new Date(batch.payment_date), 'MMM dd, yyyy')}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const AuthorizationSection = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            Payment Authorization
          </CardTitle>
          <CardDescription>
            Review and authorize payment batches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Authorization Limits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {approvalLimits.map((limit, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">Single Payment Limit</p>
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(limit.max_amount, limit.currency_code)}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant={limit.requires_dual_approval ? "secondary" : "outline"}>
                          {limit.requires_dual_approval ? "Dual Auth Required" : "Single Auth"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Bank Account Validation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {bankAccounts.map((account) => (
                    <div key={account.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{account.account_name}</p>
                        <p className="text-sm text-muted-foreground">
                          {account.bank_name} • {account.account_number}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {formatCurrency(account.balance, account.currency_code)}
                        </p>
                        <Badge variant={account.status === "ACTIVE" ? "default" : "secondary"}>
                          {account.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const PaymentSummarySection = () => {
    const totalAmount = paymentBatches.reduce((sum, batch) => sum + batch.total_amount, 0);
    const totalInvoices = paymentBatches.reduce((sum, batch) => sum + batch.invoice_count, 0);
    const currencySummary = paymentBatches.reduce((acc, batch) => {
      acc[batch.currency_code] = (acc[batch.currency_code] || 0) + batch.total_amount;
      return acc;
    }, {} as { [key: string]: number });

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
              <p className="text-xs text-muted-foreground">
                Across {Object.keys(currencySummary).length} currencies
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Invoice Count</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalInvoices}</div>
              <p className="text-xs text-muted-foreground">
                In {paymentBatches.length} batches
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Methods</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(paymentBatches.map(b => b.payment_method)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Different methods
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processing Status</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {paymentBatches.filter(b => b.status === "COMPLETED").length}
              </div>
              <p className="text-xs text-muted-foreground">
                Completed batches
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="mr-2 h-5 w-5" />
              Currency Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(currencySummary).map(([currency, amount]) => (
                <div key={currency} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center">
                    <Badge variant="outline" className="mr-3">{currency}</Badge>
                    <span className="font-medium">
                      {paymentBatches.filter(b => b.currency_code === currency).length} batches
                    </span>
                  </div>
                  <span className="text-lg font-semibold">
                    {formatCurrency(amount, currency)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="mr-2 h-5 w-5" />
              Export Payment Files
            </CardTitle>
            <CardDescription>
              Generate payment files for bank processing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="justify-start">
                <FileText className="mr-2 h-4 w-4" />
                Export CSV
              </Button>
              <Button variant="outline" className="justify-start">
                <Download className="mr-2 h-4 w-4" />
                Bank Transfer File
              </Button>
              <Button variant="outline" className="justify-start">
                <Upload className="mr-2 h-4 w-4" />
                ACH File
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="space-y-1">
            <h1 className="text-2xl font-semibold">Payment Processing</h1>
            <p className="text-muted-foreground">
              Process supplier payments efficiently with batch creation and authorization
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Calculator className="mr-2 h-4 w-4" />
              Currency Rates
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Zap className="mr-2 h-4 w-4" />
                  Process All Batches
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Process Payment Batches</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will process all approved payment batches. Payments will be submitted to the banking system.
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction className="bg-green-600 hover:bg-green-700">
                    Process Payments
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="queue" className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Payment Queue
            </TabsTrigger>
            <TabsTrigger value="batches" className="flex items-center">
              <Banknote className="mr-2 h-4 w-4" />
              Payment Batches
            </TabsTrigger>
            <TabsTrigger value="authorization" className="flex items-center">
              <Shield className="mr-2 h-4 w-4" />
              Authorization
            </TabsTrigger>
            <TabsTrigger value="summary" className="flex items-center">
              <TrendingUp className="mr-2 h-4 w-4" />
              Summary
            </TabsTrigger>
          </TabsList>

          <TabsContent value="queue">
            <PaymentQueueSection />
          </TabsContent>

          <TabsContent value="batches">
            <PaymentBatchesSection />
          </TabsContent>

          <TabsContent value="authorization">
            <AuthorizationSection />
          </TabsContent>

          <TabsContent value="summary">
            <PaymentSummarySection />
          </TabsContent>
        </Tabs>

        {/* Batch Detail Modal */}
        {currentBatch && (
          <Dialog open={!!currentBatch} onOpenChange={() => setCurrentBatch(null)}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{currentBatch.batch_name}</DialogTitle>
                <DialogDescription>
                  Payment batch details and invoice breakdown
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <Label className="text-xs text-muted-foreground">Total Amount</Label>
                    <p className="text-lg font-semibold">
                      {formatCurrency(currentBatch.total_amount, currentBatch.currency_code)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Payment Method</Label>
                    <p className="text-sm">{currentBatch.payment_method.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Payment Date</Label>
                    <p className="text-sm">{format(new Date(currentBatch.payment_date), 'MMM dd, yyyy')}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Status</Label>
                    <Badge variant={currentBatch.status === "DRAFT" ? "secondary" : "default"}>
                      {currentBatch.status}
                    </Badge>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Invoice Count</Label>
                    <p className="text-sm">{currentBatch.invoice_count}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">Currency</Label>
                    <Badge variant="outline">{currentBatch.currency_code}</Badge>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-3">Invoices in Batch</h4>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Invoice</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Original Amount</TableHead>
                          <TableHead>Payment Amount</TableHead>
                          <TableHead>Early Discount</TableHead>
                          <TableHead>Priority</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {currentBatch.invoices.map((item) => (
                          <TableRow key={item.invoice.id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{item.invoice.invoice_number}</div>
                                <div className="text-sm text-muted-foreground">
                                  {item.invoice.supplier_reference}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{item.invoice.supplier?.name}</TableCell>
                            <TableCell>
                              {formatCurrency(item.invoice.total_amount, item.invoice.currency_code)}
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(item.payment_amount, item.invoice.currency_code)}
                            </TableCell>
                            <TableCell>
                              {item.early_discount_amount > 0 ? (
                                <span className="text-green-600 font-medium">
                                  {formatCurrency(item.early_discount_amount, item.invoice.currency_code)}
                                </span>
                              ) : (
                                <span className="text-muted-foreground">—</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge variant={item.priority === "HIGH" ? "destructive" : item.priority === "MEDIUM" ? "secondary" : "outline"}>
                                {item.priority}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setCurrentBatch(null)}>
                  Close
                </Button>
                {currentBatch.status === "DRAFT" && (
                  <>
                    <Button variant="outline">
                      <FileText className="mr-2 h-4 w-4" />
                      Edit Batch
                    </Button>
                    <Button className="bg-green-600 hover:bg-green-700">
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Approve Batch
                    </Button>
                  </>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </AdminPanelLayout>
  );
}