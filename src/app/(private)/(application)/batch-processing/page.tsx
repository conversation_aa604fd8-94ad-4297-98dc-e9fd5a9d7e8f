"use client";

import { BatchProcessingTable } from "@/components/table/batch-processing-table";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";

export default function BatchProcessingPage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Applications", href: "/applications" },
          { title: "Batch Processing", isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4">
        <BatchProcessingTable />
      </div>
    </AdminPanelLayout>
  );
}
