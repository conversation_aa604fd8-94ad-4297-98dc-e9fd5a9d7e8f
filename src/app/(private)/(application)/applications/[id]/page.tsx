'use client'

import { useEffect, useState } from "react"
import { notFound } from "next/navigation"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { ApplicationService } from "@/services/applications/application.service"
import { ApplicationDetails } from "@/components/application-details"
import { ApplicationSectionsConfig, AppointmentDetail, type ApplicationDetail } from "@/types/applications/applications.types"
import { type FormSection } from "@/types/application-form.types"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

interface ApplicationDetailsPageProps {
    params: {
        id: string
    }
}

export default function ApplicationDetailsPage({ params }: ApplicationDetailsPageProps) {
    const [application, setApplication] = useState<ApplicationDetail>();
    const [formSection, setFormSection] = useState<FormSection[]>([]);
    const [appointment, setAppointment] = useState<AppointmentDetail[]>([]);
    const [requiredPhysicalSubmission, setRequiredPhysicalSubmission] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {

            const [applicationData, formSectionData, appointmentData] = await Promise.all([
                ApplicationService.getApplicationDetailsById(params.id),
                ApplicationService.getFormSections(params.id),
                ApplicationService.getAppointmentDetails(params.id),
            ]);

            if (!applicationData) {
                notFound();
            }

            setApplication(applicationData);
            setFormSection(formSectionData.sections);
            setRequiredPhysicalSubmission(formSectionData.required_physical_submission);
            setAppointment(appointmentData.AppointmentDetail);

            setIsLoading(false);
        };

        fetchData();
    }, [params.id]);

    const generateDynamicTabConfig = (): ApplicationSectionsConfig => {
        const DEFAULT_TAB_CONFIG = {
            details: true,
            documents: true,
        };

        return {
            ...DEFAULT_TAB_CONFIG,
            appointment: requiredPhysicalSubmission,
        };
    };

    if (isLoading) {
        return <LoadingSpinner />;
    }

    if (!application) {
        notFound();
    }

    const breadcrumbs = {
        items: [
            {
                title: "Dashboard",
                href: "/",
            },
            {
                title: "Applications",
                href: "/applications",
            },
            {
                title: `${application.personalInfo.full_name}`,
                isCurrentPage: true,
            },
        ],
    }

    return (
        <AdminPanelLayout breadcrumbs={breadcrumbs}>
            <ApplicationDetails application={application} formSection={formSection} appointment={appointment} tabSection={generateDynamicTabConfig()} />
        </AdminPanelLayout>
    )
}
