"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { CreditNoteDetail } from "@/components/credit-notes/detail/credit-note-detail";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { getCreditNoteById } from "@/services/credit-notes/credit-notes.service";
import type { CreditNote } from "@/types/credit-notes/credit-notes.types";

export default function CreditNoteDetailPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [creditNote, setCreditNote] = useState<CreditNote | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCreditNote();
  }, [params.id]);

  const loadCreditNote = async () => {
    try {
      setLoading(true);
      const data = await getCreditNoteById(params.id);
      
      if (data) {
        setCreditNote(data);
      } else {
        setError("Credit note not found");
      }
    } catch (error) {
      console.error("Error loading credit note:", error);
      setError("Failed to load credit note");
      toast({
        title: "Error",
        description: "Failed to load credit note details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/credit-notes/edit?id=${params.id}`);
  };

  const handlePrint = () => {
    toast({
      title: "Info",
      description: "Print functionality will be implemented",
    });
  };

  const handleEmail = () => {
    toast({
      title: "Info", 
      description: "Email functionality will be implemented",
    });
  };

  const handleApplyCredit = () => {
    router.push(`/credit-notes/apply?credit_note_id=${params.id}`);
  };

  const handleReverse = () => {
    toast({
      title: "Info",
      description: "Reverse functionality will be implemented",
    });
  };

  const handleCancel = () => {
    toast({
      title: "Info",
      description: "Cancel functionality will be implemented",
    });
  };

  const handleClone = () => {
    toast({
      title: "Info",
      description: "Clone functionality will be implemented",
    });
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Credit Notes", href: "/credit-notes" },
            { title: `Loading...`, isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (error || !creditNote) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Credit Notes", href: "/credit-notes" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/credit-notes">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to List
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Credit Notes", href: "/credit-notes" },
          { title: creditNote.invoice_number, isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/credit-notes">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        {/* Credit Note Detail Component */}
        <CreditNoteDetail
          creditNote={creditNote}
          onEdit={handleEdit}
          onPrint={handlePrint}
          onEmail={handleEmail}
          onApplyCredit={handleApplyCredit}
          onReverse={handleReverse}
          onCancel={handleCancel}
          onClone={handleClone}
          onRefresh={loadCreditNote}
        />
      </div>
    </AdminPanelLayout>
  );
}