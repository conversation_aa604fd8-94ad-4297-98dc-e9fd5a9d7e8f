"use client";

import { useState, useEffect, Suspense } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditNotesTable } from "@/components/table/credit-notes-table";
import Link from "next/link";
import { 
  PlusCircle, 
  CreditCard, 
  Clock, 
  AlertTriangle, 
  DollarSign,
  FileText 
} from "lucide-react";
import type { CreditNoteSummary } from "@/types/credit-notes/credit-notes.types";
import { getCreditNotesSummary } from "@/services/credit-notes/credit-notes.service";

export default function CreditNotesPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Credit Notes",
        isCurrentPage: true,
      },
    ],
  };

  const [summary, setSummary] = useState<CreditNoteSummary>({
    total_count: 0,
    total_value: 0,
    unapplied_credits: 0,
    monthly_credits: 0,
    expiring_soon_count: 0,
  });

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      const summaryData = await getCreditNotesSummary();
      setSummary(summaryData);
    } catch (error) {
      console.error("Error loading summary:", error);
    }
  };

  const handleSummaryUpdate = (newSummary: CreditNoteSummary) => {
    setSummary(newSummary);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Customer Credit Notes
            </h1>
            <p className="text-sm text-gray-600">
              Manage credit notes and customer credit balances
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild size="sm">
              <Link href="/credit-notes/add">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Credit Note
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/credit-notes/apply">
                <CreditCard className="mr-2 h-4 w-4" />
                Apply Credits
              </Link>
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-5 mb-6">
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total Credit Notes
              </p>
              <p className="text-2xl font-bold">{summary.total_count.toLocaleString()}</p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total Credit Value
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(summary.total_value)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <DollarSign className="mr-1 h-4 w-4" />
                Unapplied Credits
              </p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(summary.unapplied_credits)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <FileText className="mr-1 h-4 w-4" />
                This Month
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(summary.monthly_credits)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <AlertTriangle className="mr-1 h-4 w-4" />
                Expiring Soon
              </p>
              <p className="text-2xl font-bold text-amber-600">
                {summary.expiring_soon_count.toLocaleString()}
              </p>
            </div>
          </Card>
        </div>

        <Suspense fallback={
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        }>
          <CreditNotesTable 
            summary={summary}
            onSummaryUpdate={handleSummaryUpdate}
          />
        </Suspense>
      </div>
    </AdminPanelLayout>
  );
}