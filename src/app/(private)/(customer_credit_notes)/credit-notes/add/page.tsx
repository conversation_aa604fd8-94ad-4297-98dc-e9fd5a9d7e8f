"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { CreditNoteForm } from "@/components/credit-notes/forms/credit-note-form";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AddCreditNotePage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Credit Notes", href: "/credit-notes" },
          { title: "Create New", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/credit-notes">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        {/* Credit Note Form */}
        <CreditNoteForm isEditing={false} />
      </div>
    </AdminPanelLayout>
  );
}