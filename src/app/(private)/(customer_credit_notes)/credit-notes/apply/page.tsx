"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { CurrencyDisplay } from "@/components/credit-notes/ui/currency-display";
import {
  Search,
  Filter,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  CreditCard,
  FileText,
  DollarSign,
  Clock,
  RefreshCw,
  MapPin,
  Users,
  Calculator,
  Loader2,
  Eye,
  ShoppingCart,
  Trash2,
  Download,
  Upload,
} from "lucide-react";
import { format, isAfter, isBefore, parseISO } from "date-fns";
import {
  getCreditNotes,
  getOutstandingInvoices,
  applyCreditNote,
} from "@/services/credit-notes/credit-notes.service";
import { createClient } from "@/utils/supabase/Client";
import type {
  CreditNote,
  InvoiceInfo,
  CreditApplicationInput,
  CustomerInfo,
} from "@/types/credit-notes/credit-notes.types";

// Extended types for the application page
interface ExtendedCreditNote extends CreditNote {
  isSelected: boolean;
  availableAmount: number;
}

interface ExtendedInvoice extends InvoiceInfo {
  isSelected: boolean;
  applicationAmount: number;
  customer?: CustomerInfo;
  daysOverdue?: number;
}

interface CreditApplication {
  creditNoteId: string;
  invoiceId: string;
  amount: number;
  creditNote: ExtendedCreditNote;
  invoice: ExtendedInvoice;
}

interface CustomerFilter {
  value: string;
  label: string;
  totalCredits: number;
  totalOutstanding: number;
}

export default function ApplyCreditNotesPage() {
  const { toast } = useToast();
  
  // State management
  const [creditNotes, setCreditNotes] = useState<ExtendedCreditNote[]>([]);
  const [invoices, setInvoices] = useState<ExtendedInvoice[]>([]);
  const [customers, setCustomers] = useState<CustomerFilter[]>([]);
  const [applications, setApplications] = useState<CreditApplication[]>([]);
  
  // Loading states
  const [loadingCredits, setLoadingCredits] = useState(true);
  const [loadingInvoices, setLoadingInvoices] = useState(false);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [applying, setApplying] = useState(false);
  
  // Filter states
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [minAmount, setMinAmount] = useState("");
  const [maxAmount, setMaxAmount] = useState("");
  const [showExpiring, setShowExpiring] = useState(false);
  const [showOverdue, setShowOverdue] = useState(false);
  
  // Dialog states
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showSummaryDialog, setShowSummaryDialog] = useState(false);

  const breadcrumbs = {
    items: [
      {
        title: "Credit Notes",
        href: "/credit-notes",
      },
      {
        title: "Apply Credits",
        isCurrentPage: true,
      },
    ],
  };

  // Load initial data
  useEffect(() => {
    loadCreditNotes();
    loadCustomers();
  }, []);

  // Load invoices when customers are selected
  useEffect(() => {
    if (selectedCustomers.length > 0) {
      loadInvoices();
    } else {
      setInvoices([]);
    }
  }, [selectedCustomers]);

  const loadCreditNotes = async () => {
    try {
      setLoadingCredits(true);
      const response = await getCreditNotes({
        status: ["APPROVED"],
        has_unapplied: true,
        limit: 1000,
      });

      const extendedCredits: ExtendedCreditNote[] = response.data.map(credit => ({
        ...credit,
        isSelected: false,
        availableAmount: credit.unapplied_amount || 0,
      }));

      setCreditNotes(extendedCredits);
    } catch (error) {
      console.error("Error loading credit notes:", error);
      toast({
        title: "Error",
        description: "Failed to load credit notes",
        variant: "destructive",
      });
    } finally {
      setLoadingCredits(false);
    }
  };

  const loadCustomers = async () => {
    try {
      setLoadingCustomers(true);
      const supabase = await createClient();
      
      // Get customers with credit notes and outstanding invoices
      const { data, error } = await supabase
        .from("customers")
        .select(`
          id,
          customer_code,
          name,
          display_name,
          email,
          phone
        `)
        .order("name");

      if (error) throw error;

      // Calculate credit and outstanding amounts for each customer
      const customersWithBalances = await Promise.all(
        (data || []).map(async (customer) => {
          const creditResponse = await getCreditNotes({
            customer_id: customer.id,
            status: ["APPROVED"],
            has_unapplied: true,
            limit: 100,
          });

          const invoiceResponse = await getOutstandingInvoices(customer.id);

          const totalCredits = creditResponse.data.reduce(
            (sum, credit) => sum + (credit.unapplied_amount || 0),
            0
          );

          const totalOutstanding = invoiceResponse.reduce(
            (sum, invoice) => sum + (invoice.outstanding_amount || 0),
            0
          );

          return {
            value: customer.id,
            label: `${customer.name} (${customer.customer_code})`,
            totalCredits,
            totalOutstanding,
          };
        })
      );

      // Filter customers with either credits or outstanding amounts
      const filteredCustomers = customersWithBalances.filter(
        customer => customer.totalCredits > 0 || customer.totalOutstanding > 0
      );

      setCustomers(filteredCustomers);
    } catch (error) {
      console.error("Error loading customers:", error);
      toast({
        title: "Error",
        description: "Failed to load customers",
        variant: "destructive",
      });
    } finally {
      setLoadingCustomers(false);
    }
  };

  const loadInvoices = async () => {
    if (selectedCustomers.length === 0) return;
    
    try {
      setLoadingInvoices(true);
      const allInvoices: ExtendedInvoice[] = [];
      
      for (const customerId of selectedCustomers) {
        const customerInvoices = await getOutstandingInvoices(customerId);
        const extendedInvoices = customerInvoices.map(invoice => {
          const daysOverdue = invoice.payment_status === "OVERDUE" 
            ? Math.floor((new Date().getTime() - new Date(invoice.invoice_date).getTime()) / (1000 * 60 * 60 * 24))
            : 0;

          return {
            ...invoice,
            isSelected: false,
            applicationAmount: 0,
            daysOverdue,
          };
        });
        
        allInvoices.push(...extendedInvoices);
      }
      
      setInvoices(allInvoices);
    } catch (error) {
      console.error("Error loading invoices:", error);
      toast({
        title: "Error",
        description: "Failed to load invoices",
        variant: "destructive",
      });
    } finally {
      setLoadingInvoices(false);
    }
  };

  // Filtered data based on search and filters
  const filteredCreditNotes = useMemo(() => {
    return creditNotes.filter(credit => {
      if (searchTerm && !credit.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !credit.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      if (selectedCustomers.length > 0 && !selectedCustomers.includes(credit.customer_id)) {
        return false;
      }

      if (dateFrom && isBefore(parseISO(credit.invoice_date), parseISO(dateFrom))) {
        return false;
      }

      if (dateTo && isAfter(parseISO(credit.invoice_date), parseISO(dateTo))) {
        return false;
      }

      if (minAmount && credit.availableAmount < parseFloat(minAmount)) {
        return false;
      }

      if (maxAmount && credit.availableAmount > parseFloat(maxAmount)) {
        return false;
      }

      if (showExpiring && credit.expiry_date) {
        const daysUntilExpiry = Math.floor(
          (new Date(credit.expiry_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
        );
        if (daysUntilExpiry > 30) return false;
      }

      return true;
    });
  }, [creditNotes, searchTerm, selectedCustomers, dateFrom, dateTo, minAmount, maxAmount, showExpiring]);

  const filteredInvoices = useMemo(() => {
    return invoices.filter(invoice => {
      if (searchTerm && !invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      if (showOverdue && invoice.payment_status !== "OVERDUE") {
        return false;
      }

      return true;
    });
  }, [invoices, searchTerm, showOverdue]);

  // Credit note selection handlers
  const handleCreditNoteSelect = (creditId: string, selected: boolean) => {
    setCreditNotes(prev => 
      prev.map(credit => 
        credit.id === creditId 
          ? { ...credit, isSelected: selected }
          : credit
      )
    );

    // Remove applications for this credit note if deselected
    if (!selected) {
      setApplications(prev => prev.filter(app => app.creditNoteId !== creditId));
    }
  };

  const handleSelectAllCredits = (selected: boolean) => {
    setCreditNotes(prev => 
      prev.map(credit => ({ ...credit, isSelected: selected }))
    );

    if (!selected) {
      setApplications([]);
    }
  };

  // Invoice selection handlers
  const handleInvoiceSelect = (invoiceId: string, selected: boolean) => {
    setInvoices(prev => 
      prev.map(invoice => 
        invoice.id === invoiceId 
          ? { ...invoice, isSelected: selected, applicationAmount: selected ? 0 : 0 }
          : invoice
      )
    );

    // Remove applications for this invoice if deselected
    if (!selected) {
      setApplications(prev => prev.filter(app => app.invoiceId !== invoiceId));
    }
  };

  const handleSelectAllInvoices = (selected: boolean) => {
    setInvoices(prev => 
      prev.map(invoice => ({ ...invoice, isSelected: selected, applicationAmount: 0 }))
    );

    if (!selected) {
      setApplications([]);
    }
  };

  // Application mapping handlers
  const handleCreateApplication = (creditNoteId: string, invoiceId: string, amount: number) => {
    const creditNote = creditNotes.find(c => c.id === creditNoteId);
    const invoice = invoices.find(i => i.id === invoiceId);

    if (!creditNote || !invoice || amount <= 0) return;

    const maxAmount = Math.min(
      creditNote.availableAmount,
      invoice.outstanding_amount || 0
    );

    const finalAmount = Math.min(amount, maxAmount);

    setApplications(prev => {
      const existing = prev.find(app => 
        app.creditNoteId === creditNoteId && app.invoiceId === invoiceId
      );

      if (existing) {
        return prev.map(app =>
          app.creditNoteId === creditNoteId && app.invoiceId === invoiceId
            ? { ...app, amount: finalAmount }
            : app
        );
      } else {
        return [...prev, {
          creditNoteId,
          invoiceId,
          amount: finalAmount,
          creditNote,
          invoice,
        }];
      }
    });
  };

  const handleRemoveApplication = (creditNoteId: string, invoiceId: string) => {
    setApplications(prev => 
      prev.filter(app => 
        !(app.creditNoteId === creditNoteId && app.invoiceId === invoiceId)
      )
    );
  };

  // Auto-apply logic
  const handleAutoApply = () => {
    const selectedCredits = creditNotes.filter(c => c.isSelected);
    const selectedInvoices = invoices.filter(i => i.isSelected);

    if (selectedCredits.length === 0 || selectedInvoices.length === 0) {
      toast({
        title: "Warning",
        description: "Please select both credit notes and invoices to auto-apply",
        variant: "destructive",
      });
      return;
    }

    const newApplications: CreditApplication[] = [];
    let creditIndex = 0;
    let remainingCreditAmount = selectedCredits[creditIndex]?.availableAmount || 0;

    for (const invoice of selectedInvoices) {
      let remainingInvoiceAmount = invoice.outstanding_amount || 0;

      while (remainingInvoiceAmount > 0 && creditIndex < selectedCredits.length) {
        const applicationAmount = Math.min(remainingCreditAmount, remainingInvoiceAmount);

        if (applicationAmount > 0) {
          newApplications.push({
            creditNoteId: selectedCredits[creditIndex].id,
            invoiceId: invoice.id,
            amount: applicationAmount,
            creditNote: selectedCredits[creditIndex],
            invoice,
          });

          remainingCreditAmount -= applicationAmount;
          remainingInvoiceAmount -= applicationAmount;
        }

        if (remainingCreditAmount <= 0) {
          creditIndex++;
          remainingCreditAmount = selectedCredits[creditIndex]?.availableAmount || 0;
        }
      }

      if (creditIndex >= selectedCredits.length) break;
    }

    setApplications(newApplications);
    
    toast({
      title: "Success",
      description: `Auto-applied ${newApplications.length} credit applications`,
    });
  };

  // Summary calculations
  const summary = useMemo(() => {
    const totalCreditSelected = creditNotes
      .filter(c => c.isSelected)
      .reduce((sum, c) => sum + c.availableAmount, 0);

    const totalApplicationAmount = applications
      .reduce((sum, app) => sum + app.amount, 0);

    const totalInvoicesSelected = invoices
      .filter(i => i.isSelected)
      .reduce((sum, i) => sum + (i.outstanding_amount || 0), 0);

    const remainingCredit = totalCreditSelected - totalApplicationAmount;
    
    return {
      totalCreditSelected,
      totalApplicationAmount,
      totalInvoicesSelected,
      remainingCredit,
      applicationCount: applications.length,
    };
  }, [creditNotes, invoices, applications]);

  // Apply all applications
  const handleApplyAll = async () => {
    if (applications.length === 0) {
      toast({
        title: "Warning",
        description: "No applications to process",
        variant: "destructive",
      });
      return;
    }

    setShowConfirmDialog(true);
  };

  const confirmApplyAll = async () => {
    try {
      setApplying(true);
      setShowConfirmDialog(false);

      // Group applications by credit note
      const applicationsByCredit = applications.reduce((acc, app) => {
        if (!acc[app.creditNoteId]) {
          acc[app.creditNoteId] = [];
        }
        acc[app.creditNoteId].push({
          invoice_id: app.invoiceId,
          amount: app.amount,
        });
        return acc;
      }, {} as Record<string, CreditApplicationInput[]>);

      // Apply each credit note
      const results = await Promise.all(
        Object.entries(applicationsByCredit).map(([creditNoteId, creditApplications]) =>
          applyCreditNote({
            credit_note_id: creditNoteId,
            applications: creditApplications,
          })
        )
      );

      toast({
        title: "Success",
        description: `Applied ${applications.length} credit applications across ${results.length} credit notes`,
      });

      // Reset state and reload data
      setApplications([]);
      setCreditNotes(prev => prev.map(c => ({ ...c, isSelected: false })));
      setInvoices(prev => prev.map(i => ({ ...i, isSelected: false, applicationAmount: 0 })));
      
      await loadCreditNotes();
      if (selectedCustomers.length > 0) {
        await loadInvoices();
      }

    } catch (error) {
      console.error("Error applying credits:", error);
      toast({
        title: "Error",
        description: "Failed to apply credit notes",
        variant: "destructive",
      });
    } finally {
      setApplying(false);
    }
  };

  const handleClearAll = () => {
    setApplications([]);
    setCreditNotes(prev => prev.map(c => ({ ...c, isSelected: false })));
    setInvoices(prev => prev.map(i => ({ ...i, isSelected: false, applicationAmount: 0 })));
  };

  const formatCurrency = (amount: number, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  const getExpiryWarning = (expiryDate: string | null | undefined) => {
    if (!expiryDate) return null;
    
    const daysUntilExpiry = Math.floor(
      (new Date(expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysUntilExpiry < 0) {
      return { type: "expired", message: "Expired", variant: "destructive" as const };
    } else if (daysUntilExpiry <= 7) {
      return { type: "warning", message: `Expires in ${daysUntilExpiry} days`, variant: "destructive" as const };
    } else if (daysUntilExpiry <= 30) {
      return { type: "caution", message: `Expires in ${daysUntilExpiry} days`, variant: "secondary" as const };
    }
    
    return null;
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-2xl font-semibold tracking-tight">
              Apply Credit Notes
            </h1>
            <p className="text-sm text-muted-foreground">
              Apply available credit notes to outstanding customer invoices
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleAutoApply}
              disabled={creditNotes.filter(c => c.isSelected).length === 0 || 
                        invoices.filter(i => i.isSelected).length === 0}
            >
              <Calculator className="mr-2 h-4 w-4" />
              Auto Apply
            </Button>
            <Button
              variant="outline"
              onClick={handleClearAll}
              disabled={applications.length === 0}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Clear All
            </Button>
            <Button
              onClick={() => setShowSummaryDialog(true)}
              disabled={applications.length === 0}
            >
              <Eye className="mr-2 h-4 w-4" />
              Review ({applications.length})
            </Button>
            <Button
              onClick={handleApplyAll}
              disabled={applications.length === 0 || applying}
            >
              {applying ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="mr-2 h-4 w-4" />
              )}
              Apply All
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-5">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Selected Credits
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.totalCreditSelected)}
              </div>
              <p className="text-xs text-muted-foreground">
                {creditNotes.filter(c => c.isSelected).length} credit notes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Applications
              </CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.totalApplicationAmount)}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.applicationCount} applications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Outstanding Invoices
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.totalInvoicesSelected)}
              </div>
              <p className="text-xs text-muted-foreground">
                {invoices.filter(i => i.isSelected).length} invoices
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Remaining Credit
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.remainingCredit)}
              </div>
              <p className="text-xs text-muted-foreground">
                After applications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Customers
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {selectedCustomers.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Selected customers
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
              <div className="space-y-2">
                <Label htmlFor="customer-select">Customers</Label>
                <Select
                  value={selectedCustomers.join(",")}
                  onValueChange={(value) => {
                    if (value && value !== "ALL") {
                      setSelectedCustomers([value]);
                    } else {
                      setSelectedCustomers([]);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select customers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Customers</SelectItem>
                    {customers.map((customer) => (
                      <SelectItem key={customer.value} value={customer.value}>
                        {customer.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Credit note or invoice #"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date-from">Date From</Label>
                <Input
                  id="date-from"
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date-to">Date To</Label>
                <Input
                  id="date-to"
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="min-amount">Min Amount</Label>
                <Input
                  id="min-amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={minAmount}
                  onChange={(e) => setMinAmount(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-amount">Max Amount</Label>
                <Input
                  id="max-amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={maxAmount}
                  onChange={(e) => setMaxAmount(e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-expiring"
                  checked={showExpiring}
                  onCheckedChange={(checked) => setShowExpiring(checked === true)}
                />
                <Label htmlFor="show-expiring">Show expiring credits (30 days)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-overdue"
                  checked={showOverdue}
                  onCheckedChange={(checked) => setShowOverdue(checked === true)}
                />
                <Label htmlFor="show-overdue">Show overdue invoices only</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Available Credit Notes */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Available Credit Notes
                  </CardTitle>
                  <CardDescription>
                    Select credit notes to apply to outstanding invoices
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={filteredCreditNotes.length > 0 && 
                             filteredCreditNotes.every(c => c.isSelected)}
                    onCheckedChange={handleSelectAllCredits}
                  />
                  <Label className="text-sm">Select All</Label>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loadingCredits ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">Select</TableHead>
                        <TableHead>Credit Note #</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Available</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCreditNotes.map((credit) => {
                        const expiryWarning = getExpiryWarning(credit.expiry_date);
                        
                        return (
                          <TableRow key={credit.id}>
                            <TableCell>
                              <Checkbox
                                checked={credit.isSelected}
                                onCheckedChange={(checked) =>
                                  handleCreditNoteSelect(credit.id, checked as boolean)
                                }
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              {credit.invoice_number}
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {credit.customer?.name}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {credit.customer?.customer_code}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {format(new Date(credit.invoice_date), "dd MMM yyyy")}
                            </TableCell>
                            <TableCell className="text-right">
                              <CurrencyDisplay
                                amount={credit.availableAmount}
                                currency={credit.currency_code}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col gap-1">
                                <Badge variant="secondary">
                                  {credit.status}
                                </Badge>
                                {expiryWarning && (
                                  <Badge variant={expiryWarning.variant} className="text-xs">
                                    <AlertTriangle className="mr-1 h-3 w-3" />
                                    {expiryWarning.message}
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

          {/* Outstanding Invoices */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Outstanding Invoices
                  </CardTitle>
                  <CardDescription>
                    Customer invoices available for credit application
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={filteredInvoices.length > 0 && 
                             filteredInvoices.every(i => i.isSelected)}
                    onCheckedChange={handleSelectAllInvoices}
                  />
                  <Label className="text-sm">Select All</Label>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loadingInvoices ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : selectedCustomers.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="mx-auto h-8 w-8 mb-2" />
                  <p>Select customers to view outstanding invoices</p>
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">Select</TableHead>
                        <TableHead>Invoice #</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Outstanding</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredInvoices.map((invoice) => (
                        <TableRow key={invoice.id}>
                          <TableCell>
                            <Checkbox
                              checked={invoice.isSelected}
                              onCheckedChange={(checked) =>
                                handleInvoiceSelect(invoice.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            {invoice.invoice_number}
                          </TableCell>
                          <TableCell>
                            {format(new Date(invoice.invoice_date), "dd MMM yyyy")}
                          </TableCell>
                          <TableCell className="text-right">
                            <CurrencyDisplay
                              amount={invoice.outstanding_amount || 0}
                              currency="USD"
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <Badge 
                                variant={invoice.payment_status === "OVERDUE" ? "destructive" : "secondary"}
                              >
                                {invoice.payment_status}
                              </Badge>
                              {invoice.daysOverdue && invoice.daysOverdue > 0 && (
                                <Badge variant="destructive" className="text-xs">
                                  <Clock className="mr-1 h-3 w-3" />
                                  {invoice.daysOverdue} days
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Application Mapping Section */}
        {applications.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Credit Applications
              </CardTitle>
              <CardDescription>
                Review and modify credit applications before processing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Credit Note</TableHead>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead className="text-right">Application Amount</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applications.map((app, index) => (
                    <TableRow key={`${app.creditNoteId}-${app.invoiceId}`}>
                      <TableCell>
                        <div className="font-medium">
                          {app.creditNote.invoice_number}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Available: {formatCurrency(app.creditNote.availableAmount)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {app.invoice.invoice_number}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Outstanding: {formatCurrency(app.invoice.outstanding_amount || 0)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {app.creditNote.customer?.name}
                      </TableCell>
                      <TableCell className="text-right">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max={Math.min(
                            app.creditNote.availableAmount,
                            app.invoice.outstanding_amount || 0
                          )}
                          value={app.amount}
                          onChange={(e) =>
                            handleCreateApplication(
                              app.creditNoteId,
                              app.invoiceId,
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-24 text-right"
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveApplication(app.creditNoteId, app.invoiceId)}
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Confirmation Dialog */}
        <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Credit Applications</DialogTitle>
              <DialogDescription>
                Are you sure you want to apply {applications.length} credit applications?
                This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total applications:</span>
                  <span className="font-medium">{applications.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total amount:</span>
                  <span className="font-medium">
                    {formatCurrency(summary.totalApplicationAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Remaining credit:</span>
                  <span className="font-medium">
                    {formatCurrency(summary.remainingCredit)}
                  </span>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowConfirmDialog(false)}
                disabled={applying}
              >
                Cancel
              </Button>
              <Button onClick={confirmApplyAll} disabled={applying}>
                {applying ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Confirm Application
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Summary Dialog */}
        <Dialog open={showSummaryDialog} onOpenChange={setShowSummaryDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Application Summary</DialogTitle>
              <DialogDescription>
                Review all credit applications before processing
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="grid gap-4 md:grid-cols-3 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {applications.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Applications</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {formatCurrency(summary.totalApplicationAmount)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Amount</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(summary.remainingCredit)}
                  </div>
                  <div className="text-sm text-muted-foreground">Remaining</div>
                </div>
              </div>

              <ScrollArea className="h-64">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Credit → Invoice</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {applications.map((app, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium text-sm">
                              {app.creditNote.invoice_number} → {app.invoice.invoice_number}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {app.creditNote.customer?.name}
                        </TableCell>
                        <TableCell className="text-right">
                          <CurrencyDisplay
                            amount={app.amount}
                            currency={app.creditNote.currency_code}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowSummaryDialog(false)}
              >
                Close
              </Button>
              <Button onClick={handleApplyAll}>
                <CheckCircle className="mr-2 h-4 w-4" />
                Process Applications
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminPanelLayout>
  );
}