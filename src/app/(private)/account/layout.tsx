'use client'

import AdminPanelLayout from '@/components/layout/admin-panel-layout'

export default function AccountLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Account", isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4">
        <div className="border border-gray-300 rounded-md p-4 mb-4">
          {children}
        </div>
      </div>
    </AdminPanelLayout>
  )
}

