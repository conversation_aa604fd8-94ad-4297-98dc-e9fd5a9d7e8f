"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormProvider } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { useProfileChange } from "@/hooks/account/use-profile-change";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { PhoneInput } from "@/components/ui/phone-input";

export default function Profile() {
  const { userForm, isLoading, onUserSubmit } = useProfileChange();
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold tracking-tight">Profile</h2>
        <p className="text-sm text-muted-foreground">
          Your profile details will be displayed here.
        </p>
      </div>

      <FormProvider {...userForm}>
        <form onSubmit={userForm.handleSubmit(onUserSubmit)} className="space-y-6">
          <h1 className="text-lg font-semibold mb-4">User Information</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={userForm.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="fullName">Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter full name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={userForm.control}
              name="partial_nric"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="partial_nric">Partial Nric</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter partial nric" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={userForm.control}
              name="date_of_birth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="date_of_birth">Date Of Birth</FormLabel>
                  <FormControl>
                    <Popover>
                      <PopoverTrigger asChild>
                        <div className="relative">
                          <Input
                            type="text"
                            readOnly
                            className="pointer-events-none"
                            placeholder="Pick a date"
                            value={field.value ? format(field.value, "PPP") : ""}
                          />
                          <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent>
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            if (date) {
                              const utcDate = new Date(Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate()
                              ));
                              field.onChange(utcDate);
                            }
                          }}
                          disabled={[{ after: new Date() }]}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={userForm.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="email">Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="Enter email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={userForm.control}
              name="mobileNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="phone">Mobile Number</FormLabel>
                  <FormControl>
                    <PhoneInput placeholder="Enter mobile number" international {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex justify-end mt-6">
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update User Info'}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  )
}

