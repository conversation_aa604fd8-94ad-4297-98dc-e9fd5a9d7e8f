"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { ReportTable } from "@/components/table/report-table";

export default function ReceiptsReportsPage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Receipts", href: "/receipts" },
          { title: "Reports", isCurrentPage: true },
        ],
      }}
    >
      <div className="rounded-xl p-4">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-lg font-semibold md:text-2xl">
            Receipts Reports
          </h1>
        </div>
        <ReportTable exportType="RECEIPT" />
      </div>
    </AdminPanelLayout>
  );
}
