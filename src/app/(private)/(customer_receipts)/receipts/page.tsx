"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { ReceiptsTable } from "@/components/table/receipt-table";

export default function ReceiptsPage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Receipts", isCurrentPage: true },
        ],
      }}
    >
      <div className="rounded-xl p-4">
        <div className="flex items-center justify-between mb-4 ">
          <h1 className="text-lg font-semibold md:text-2xl">
          Receipts
          </h1>
        </div>
        <ReceiptsTable />
      </div>
    </AdminPanelLayout>
  );
}