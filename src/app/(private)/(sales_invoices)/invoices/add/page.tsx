"use client"

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { AddInvoice } from "@/components/invoices/add-modal";
import { useEffect, useState } from "react";
import { getInvoiceEntities, getTaxFromConfig } from "@/services/finance/invoices.service";

export default function editInvoice() {
    const [taxRate, setTaxRate] = useState<number | null>(null);
    const [invoiceEntities, setInvoiceEntities] = useState<{ id: string, name: string }[]>([]);

    const fetchInvoiceOptions = async () => {
        const [taxResponse, entitiesResponse] = await Promise.all([
            await getTaxFromConfig(),
            await getInvoiceEntities()
        ])

        if (taxResponse) {
            setTaxRate(taxResponse)
        }

        if (entitiesResponse) {
            setInvoiceEntities(entitiesResponse)
        }
    }

    useEffect(() => {
        fetchInvoiceOptions()
    }, [])

    return (
        <AdminPanelLayout>
            {taxRate && (
                <AddInvoice taxRate={taxRate} invoiceEntities={invoiceEntities} />
            )}
        </AdminPanelLayout>
    );
}
