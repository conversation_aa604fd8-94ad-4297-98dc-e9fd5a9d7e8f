"use client"

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Download, FileX2 } from "lucide-react";
import { InvoicesEditModal } from "@/components/invoices/edit-modal";
import type { invoiceDetails } from "@/types/finance/invoices.types";
import { downloadInvoicePdf, getInvoiceEntities, getPaymentOptions, getSpecificInvoices, getTaxFromConfig } from "@/services/finance/invoices.service";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export interface EditInvoiceChildRef {
    onSubmit: () => Promise<void>;
}

export default function editInvoice() {
    const childRef = useRef<EditInvoiceChildRef | null>(null);
    const searchParams = useSearchParams();
    const id = searchParams.get("id");
    const router = useRouter();
    const { toast } = useToast();
    const [invoice, setInvoice] = useState<invoiceDetails | null>(null);
    const [taxRate, setTaxRate] = useState<number | null>(null);
    const [paymentOptions, setPaymentOptions] = useState<{ code: string, label: string }[]>([]);
    const [invoiceEntities, setInvoiceEntities] = useState<{ id: string, name: string }[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
    const [isPdfLoading, setIsPdfLoading] = useState<boolean>(false)

    const fetchInvoice = async () => {
        if (!id) {
            router.back();
            return
        }

        setIsLoading(true);

        const [invoiceResponse, taxResponse, poResponse, entitiesResponse] = await Promise.all([
            await getSpecificInvoices(id),
            await getTaxFromConfig(),
            await getPaymentOptions(),
            await getInvoiceEntities()
        ])

        if (invoiceResponse) {
            setInvoice(invoiceResponse)
        }

        if (taxResponse) {
            setTaxRate(taxResponse)
        }

        if (poResponse) {
            setPaymentOptions(poResponse)
        }

        if (entitiesResponse) {
            setInvoiceEntities(entitiesResponse)
        }

        setIsLoading(false);
    }

    useEffect(() => {
        if (id) {
            fetchInvoice()
        }
    }, [id])

    const downloadPdf = async () => {
        if (!id) return;
        setIsPdfLoading(true);
        try {
            const success = await downloadInvoicePdf(id);
            if (!success) {
                toast({
                    title: "Error",
                    description: "Failed to download the invoice PDF. Please try again.",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error("Error downloading PDF:", error);
            toast({
                title: "Error",
                description: "Failed to download the invoice PDF. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsPdfLoading(false);
        }
    }

    const handleSubmit = async () => {
        if (!childRef.current?.onSubmit) return;

        setIsSubmitting(true);
        await childRef.current.onSubmit();
        setIsSubmitting(false);
    }

    return (
        <AdminPanelLayout>
            <div className="p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold">Edit Invoice</h1>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-red-500 text-red-500 hover:text-red-600" disabled={isLoading}>
                            <FileX2 className="text-red-500" />
                            Write-Off Invoice
                        </Button>
                        <Button
                            variant="outline"
                            className="border-gray-300"
                            onClick={downloadPdf}
                            disabled={isPdfLoading || isLoading}
                        >
                            <Download />
                            {isPdfLoading ? 'Processing...' : 'Download PDF'}
                        </Button>
                        <Button onClick={handleSubmit} disabled={isSubmitting || isLoading}>{isSubmitting ? 'Updating' : 'Update'}</Button>
                    </div>
                </div>
                {(invoice && taxRate && !isLoading) ? (
                    <InvoicesEditModal ref={childRef} invoice={invoice} taxRate={taxRate} paymentOptions={paymentOptions} invoiceEntities={invoiceEntities} fetchInvoice={fetchInvoice} />
                ) : (
                    <LoadingSpinner />
                )}
            </div>
        </AdminPanelLayout>
    );
}
