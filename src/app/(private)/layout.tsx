import { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import SidebarWrapper from "@/components/layout/sidebarWrapper";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ErrorBoundary } from "@/components/layout/error-boundary";
import { RBACProvider } from "@/contexts/rbac-context";
import { cookies } from "next/headers";

export default async function PrivateLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const cookieStore = await cookies();
    const defaultOpen = cookieStore.get("sidebar:state")?.value === "true";

    return (
        <ErrorBoundary>
            <RBACProvider>
                <SidebarWrapper defaultOpen={defaultOpen}>
                    <Suspense fallback={<LoadingSpinner />}>
                        {children}
                    </Suspense>
                    <Toaster />
                </SidebarWrapper>
            </RBACProvider>
        </ErrorBoundary>
    );
}
