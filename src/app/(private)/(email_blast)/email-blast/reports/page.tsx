"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";
import { 
  Mail, 
  TrendingUp,
  Users,
  MousePointer,
  Eye,
  BarChart3,
  Calendar,
  Download,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { 
  Line<PERSON>hart, 
  Line, 
  BarChart, 
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { format } from "date-fns";

interface CampaignMetrics {
  total_campaigns: number;
  total_sent: number;
  total_delivered: number;
  total_opened: number;
  total_clicked: number;
  avg_open_rate: number;
  avg_click_rate: number;
  avg_bounce_rate: number;
}

interface PerformanceDataPoint {
  date: string;
  sent: number;
  opened: number;
  clicked: number;
}

interface EngagementData {
  name: string;
  value: number;
  color: string;
}

interface TopCampaign {
  id: string;
  name: string;
  sent: number;
  openRate: number;
  clickRate: number;
}

interface DeviceStats {
  device: string;
  percentage: number;
}

export default function EmailBlastReportsPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Analytics",
        isCurrentPage: true,
      },
    ],
  };

  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState("last30days");
  const [metrics, setMetrics] = useState<CampaignMetrics>({
    total_campaigns: 0,
    total_sent: 0,
    total_delivered: 0,
    total_opened: 0,
    total_clicked: 0,
    avg_open_rate: 0,
    avg_click_rate: 0,
    avg_bounce_rate: 0,
  });
  const [performanceData, setPerformanceData] = useState<PerformanceDataPoint[]>([]);
  const [engagementData, setEngagementData] = useState<EngagementData[]>([]);
  const [topCampaigns, setTopCampaigns] = useState<TopCampaign[]>([]);
  const [deviceStats, setDeviceStats] = useState<DeviceStats[]>([]);
  const [engagementMetrics, setEngagementMetrics] = useState({
    deliveryRate: 0,
    openRate: 0,
    clickRate: 0,
    unsubscribeRate: 0,
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/email-blast/reports?dateRange=${dateRange}`);
        if (!response.ok) {
          throw new Error('Failed to fetch report data');
        }
        
        const data = await response.json();
        
        setMetrics(data.metrics);
        setPerformanceData(data.performance);
        setEngagementData(data.engagement);
        setTopCampaigns(data.topCampaigns);
        setDeviceStats(data.deviceStats);
        setEngagementMetrics(data.engagementMetrics);
      } catch (error) {
        console.error('Error fetching report data:', error);
        // Set default empty data on error
        setMetrics({
          total_campaigns: 0,
          total_sent: 0,
          total_delivered: 0,
          total_opened: 0,
          total_clicked: 0,
          avg_open_rate: 0,
          avg_click_rate: 0,
          avg_bounce_rate: 0,
        });
        setPerformanceData([]);
        setEngagementData([
          { name: "Opened", value: 0, color: "#10b981" },
          { name: "Clicked", value: 0, color: "#3b82f6" },
          { name: "Unopened", value: 100, color: "#e5e7eb" },
        ]);
        setTopCampaigns([]);
        setDeviceStats([
          { device: "Desktop", percentage: 45 },
          { device: "Mobile", percentage: 38 },
          { device: "Tablet", percentage: 17 },
        ]);
        setEngagementMetrics({
          deliveryRate: 0,
          openRate: 0,
          clickRate: 0,
          unsubscribeRate: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [dateRange]);

  const formatMetricValue = (value: number, isPercentage: boolean = false) => {
    if (isPercentage) {
      return `${value.toFixed(1)}%`;
    }
    return new Intl.NumberFormat("en-US").format(value);
  };

  const MetricCard = ({ 
    title, 
    value, 
    icon: Icon, 
    trend, 
    isPercentage = false 
  }: { 
    title: string;
    value: number;
    icon: any;
    trend?: number;
    isPercentage?: boolean;
  }) => (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">
            {formatMetricValue(value, isPercentage)}
          </p>
          {trend !== undefined && (
            <div className={`flex items-center text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {trend >= 0 ? <ArrowUp className="h-4 w-4 mr-1" /> : <ArrowDown className="h-4 w-4 mr-1" />}
              {Math.abs(trend)}% from last period
            </div>
          )}
        </div>
        <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center">
          <Icon className="h-6 w-6 text-muted-foreground" />
        </div>
      </div>
    </Card>
  );

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Email Campaign Analytics
            </h1>
            <p className="text-sm text-gray-600">
              Track and analyze your email campaign performance
            </p>
          </div>
          <div className="flex gap-2">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-[180px]">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last7days">Last 7 days</SelectItem>
                <SelectItem value="last30days">Last 30 days</SelectItem>
                <SelectItem value="last90days">Last 90 days</SelectItem>
                <SelectItem value="thisMonth">This month</SelectItem>
                <SelectItem value="lastMonth">Last month</SelectItem>
                <SelectItem value="thisYear">This year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast">
                <Mail className="mr-2 h-4 w-4" />
                Back to Campaigns
              </Link>
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <>
            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-4">
              <MetricCard
                title="Total Campaigns"
                value={metrics.total_campaigns}
                icon={Mail}
                trend={12}
              />
              <MetricCard
                title="Emails Sent"
                value={metrics.total_sent}
                icon={Users}
                trend={8}
              />
              <MetricCard
                title="Avg Open Rate"
                value={metrics.avg_open_rate}
                icon={Eye}
                trend={5}
                isPercentage
              />
              <MetricCard
                title="Avg Click Rate"
                value={metrics.avg_click_rate}
                icon={MousePointer}
                trend={-2}
                isPercentage
              />
            </div>

            {/* Charts and Analytics */}
            <Tabs defaultValue="performance" className="space-y-4">
              <TabsList>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="engagement">Engagement</TabsTrigger>
                <TabsTrigger value="campaigns">Top Campaigns</TabsTrigger>
                <TabsTrigger value="devices">Devices</TabsTrigger>
              </TabsList>

              <TabsContent value="performance" className="space-y-4">
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Campaign Performance Over Time</h3>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => format(new Date(value), 'MMM d')}
                      />
                      <YAxis />
                      <Tooltip 
                        labelFormatter={(value) => format(new Date(value), 'PPP')}
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="sent" 
                        stroke="#8b5cf6" 
                        name="Sent"
                        strokeWidth={2}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="opened" 
                        stroke="#10b981" 
                        name="Opened"
                        strokeWidth={2}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="clicked" 
                        stroke="#3b82f6" 
                        name="Clicked"
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Card>
              </TabsContent>

              <TabsContent value="engagement" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-4">Email Engagement Breakdown</h3>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={engagementData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, value }) => `${name}: ${value}%`}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {engagementData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Card>
                  
                  <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-4">Engagement Metrics</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">Delivery Rate</span>
                          <span className="text-sm font-bold">{engagementMetrics.deliveryRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{ width: `${engagementMetrics.deliveryRate}%` }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">Open Rate</span>
                          <span className="text-sm font-bold">{engagementMetrics.openRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${engagementMetrics.openRate}%` }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">Click Rate</span>
                          <span className="text-sm font-bold">{engagementMetrics.clickRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-purple-600 h-2 rounded-full" style={{ width: `${engagementMetrics.clickRate}%` }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">Unsubscribe Rate</span>
                          <span className="text-sm font-bold">{engagementMetrics.unsubscribeRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-red-600 h-2 rounded-full" style={{ width: `${engagementMetrics.unsubscribeRate}%` }}></div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="campaigns" className="space-y-4">
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Top Performing Campaigns</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Campaign Name</th>
                          <th className="text-right py-3 px-4">Sent</th>
                          <th className="text-right py-3 px-4">Open Rate</th>
                          <th className="text-right py-3 px-4">Click Rate</th>
                          <th className="text-right py-3 px-4">Performance</th>
                        </tr>
                      </thead>
                      <tbody>
                        {topCampaigns.map((campaign) => (
                          <tr key={campaign.id} className="border-b">
                            <td className="py-3 px-4">
                              <Link 
                                href={`/email-blast/${campaign.id}`}
                                className="text-blue-600 hover:underline"
                              >
                                {campaign.name}
                              </Link>
                            </td>
                            <td className="text-right py-3 px-4">
                              {formatMetricValue(campaign.sent)}
                            </td>
                            <td className="text-right py-3 px-4">
                              <span className="text-green-600 font-medium">
                                {campaign.openRate.toFixed(1)}%
                              </span>
                            </td>
                            <td className="text-right py-3 px-4">
                              <span className="text-blue-600 font-medium">
                                {campaign.clickRate.toFixed(1)}%
                              </span>
                            </td>
                            <td className="text-right py-3 px-4">
                              <div className="flex justify-end">
                                <TrendingUp className="h-4 w-4 text-green-600" />
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </Card>
              </TabsContent>

              <TabsContent value="devices" className="space-y-4">
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Email Opens by Device</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={deviceStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="device" />
                      <YAxis />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Bar dataKey="percentage" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </AdminPanelLayout>
  );
}