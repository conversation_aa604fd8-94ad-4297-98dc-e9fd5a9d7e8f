"use client";

import { useState, useEffect, Suspense } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { EmailBlastTable } from "@/components/table/email-blast-table";
import Link from "next/link";
import { 
  PlusCircle, 
  Mail, 
  Users, 
  BarChart3, 
  Send,
  Clock,
  CheckCircle2,
  XCircle
} from "lucide-react";
import type { EmailBlast } from "@/types/email-blast/email-blast.types";
import { useCampaigns } from "@/hooks/email-blast/use-campaigns";
import { columns } from "@/components/email-blast/columns";
import { useRouter } from "next/navigation";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface EmailBlastSummary {
  total_campaigns: number;
  sent_campaigns: number;
  scheduled_campaigns: number;
  draft_campaigns: number;
  total_recipients: number;
  success_rate: number;
}

export default function EmailBlastPage() {
  const router = useRouter();
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        isCurrentPage: true,
      },
    ],
  };

  const { data: campaigns, isLoading, refetch } = useCampaigns();
  const [summary, setSummary] = useState<EmailBlastSummary>({
    total_campaigns: 0,
    sent_campaigns: 0,
    scheduled_campaigns: 0,
    draft_campaigns: 0,
    total_recipients: 0,
    success_rate: 0,
  });

  useEffect(() => {
    if (campaigns && campaigns.length > 0) {
      // Calculate summary
      const sent = campaigns.filter(c => c.status === 'sent').length;
      const scheduled = campaigns.filter(c => c.status === 'scheduled').length;
      const draft = campaigns.filter(c => c.status === 'draft').length;
      const totalRecipients = campaigns.reduce((sum, c) => sum + (c.recipientCount || 0), 0);
      const totalSent = campaigns.reduce((sum, c) => sum + (c.sentCount || 0), 0);
      const totalFailed = campaigns.reduce((sum, c) => sum + (c.failedCount || 0), 0);
      const successRate = totalSent > 0 ? ((totalSent - totalFailed) / totalSent) * 100 : 0;
      
      setSummary({
        total_campaigns: campaigns.length,
        sent_campaigns: sent,
        scheduled_campaigns: scheduled,
        draft_campaigns: draft,
        total_recipients: totalRecipients,
        success_rate: successRate,
      });
    }
  }, [campaigns]);

  const handleRowClick = (campaign: EmailBlast) => {
    router.push(`/email-blast/${campaign.id}`);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Email Campaigns
            </h1>
            <p className="text-sm text-gray-600">
              Create and manage email campaigns for your members
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild size="sm">
              <Link href="/email-blast/add">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Campaign
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast/lists">
                <Users className="mr-2 h-4 w-4" />
                Recipient Lists
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast/templates">
                <Mail className="mr-2 h-4 w-4" />
                Templates
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast/reports">
                <BarChart3 className="mr-2 h-4 w-4" />
                Analytics
              </Link>
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-6 mb-6">
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total Campaigns
              </p>
              <p className="text-2xl font-bold">{formatNumber(summary.total_campaigns)}</p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Send className="mr-1 h-4 w-4" />
                Sent
              </p>
              <p className="text-2xl font-bold text-green-600">
                {formatNumber(summary.sent_campaigns)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                Scheduled
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {formatNumber(summary.scheduled_campaigns)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Draft
              </p>
              <p className="text-2xl font-bold text-gray-600">
                {formatNumber(summary.draft_campaigns)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Users className="mr-1 h-4 w-4" />
                Total Recipients
              </p>
              <p className="text-2xl font-bold">
                {formatNumber(summary.total_recipients)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <CheckCircle2 className="mr-1 h-4 w-4" />
                Success Rate
              </p>
              <p className="text-2xl font-bold text-green-600">
                {formatPercentage(summary.success_rate)}
              </p>
            </div>
          </Card>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <Suspense fallback={
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner />
            </div>
          }>
            <EmailBlastTable 
              columns={columns}
              data={campaigns || []}
              onRowClick={handleRowClick}
            />
          </Suspense>
        )}
      </div>
    </AdminPanelLayout>
  );
}