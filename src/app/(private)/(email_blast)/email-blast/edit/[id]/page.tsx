"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { EmailBlastForm } from "@/components/email-blast/forms/email-blast-form";
import { useCampaign } from "@/hooks/email-blast/use-campaigns";
import { useUpdateCampaign, useSendCampaign, useScheduleCampaign } from "@/hooks/email-blast/use-campaign-mutations";
import { useRecipientLists } from "@/hooks/email-blast/use-recipient-lists";
import type { EmailBlast } from "@/types/email-blast/email-blast.types";
import type { RecipientList } from "@/types/email-blast/recipient-list.types";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";

export default function EditEmailCampaignPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const campaignId = typeof params.id === 'string' ? params.id : '';
  
  const { data: campaign, isLoading: campaignLoading, error: campaignError } = useCampaign(campaignId);
  const { data: listsData, isLoading: listsLoading } = useRecipientLists();
  const updateCampaign = useUpdateCampaign(campaignId);
  const sendCampaign = useSendCampaign(campaignId);
  const scheduleCampaign = useScheduleCampaign(campaignId);
  
  const loading = campaignLoading || listsLoading;
  const recipientLists = listsData?.lists || [];

  useEffect(() => {
    if (!campaignId) {
      toast({
        title: "Error",
        description: "Invalid campaign ID",
        variant: "destructive",
      });
      router.push("/email-blast");
      return;
    }

    if (campaignError) {
      toast({
        title: "Error",
        description: "Failed to load campaign",
        variant: "destructive",
      });
      router.push("/email-blast");
      return;
    }

    // Check if campaign can be edited
    if (campaign && campaign.status !== 'draft' && campaign.status !== 'scheduled') {
      toast({
        title: "Error",
        description: "Only draft or scheduled campaigns can be edited",
        variant: "destructive",
      });
      router.push(`/email-blast/${campaignId}`);
    }
  }, [campaignId, campaign, campaignError, router, toast]);

  const handleSubmit = async (data: any, action: "draft" | "schedule" | "send") => {
    try {
      await updateCampaign.mutateAsync(data);
      
      if (action === "send") {
        await sendCampaign.mutateAsync();
      } else if (action === "schedule" && data.scheduledAt) {
        await scheduleCampaign.mutateAsync(data.scheduledAt.toISOString());
      }
      
      router.push(`/email-blast/${campaignId}`);
    } catch (error) {
      // Errors are already handled by the hooks
    }
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Edit Campaign", href: "#" },
            { title: "Loading...", isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (!campaign) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Campaigns
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Campaign not found</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Email Blast", href: "/email-blast" },
          { title: campaign.name, href: `/email-blast/${campaign.id}` },
          { title: "Edit", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/email-blast/${campaign.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Campaign
            </Link>
          </Button>
        </div>

        <div className="space-y-1">
          <h1 className="text-2xl font-bold">Edit Email Campaign</h1>
          <p className="text-muted-foreground">
            Update campaign: {campaign.name}
          </p>
        </div>

        {/* Email Campaign Form */}
        <EmailBlastForm 
          campaign={campaign}
          recipientLists={recipientLists}
          onSubmit={handleSubmit}
        />
      </div>
    </AdminPanelLayout>
  );
}