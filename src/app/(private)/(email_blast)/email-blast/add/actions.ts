'use server';

import { createClient } from '@/utils/supabase/server';
import { emailBlastFormSchema } from '@/schemas/email-blast/email-blast.schema';
import { EmailBlast, EmailCampaignStatus } from '@/types/email-blast/email-blast.types';
import { revalidatePath } from 'next/cache';

type ActionResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
};

// Helper function to transform database row to EmailBlast type
function transformToEmailBlast(campaign: any): EmailBlast {
  return {
    id: campaign.id,
    name: campaign.name,
    description: campaign.description || '',
    status: campaign.status as EmailCampaignStatus,
    content: {
      subject: campaign.subject || '',
      body: campaign.body_text || '',
      bodyHtml: campaign.body_html || '',
      attachments: [],
      templateId: campaign.template_id || undefined,
      variables: campaign.metadata?.variables || {}
    },
    recipientListId: campaign.recipient_list_id,
    recipientCount: campaign.total_recipients || 0,
    sentCount: campaign.sent_count || 0,
    failedCount: campaign.failed_count || 0,
    openCount: campaign.opened_count || 0,
    clickCount: campaign.clicked_count || 0,
    tags: campaign.tags || [],
    metadata: campaign.metadata || {},
    createdAt: new Date(campaign.created_at || Date.now()),
    updatedAt: new Date(campaign.updated_at || campaign.created_at || Date.now()),
    sentAt: campaign.completed_at ? new Date(campaign.completed_at) : undefined,
    scheduledAt: campaign.scheduled_at ? new Date(campaign.scheduled_at) : undefined,
    createdBy: campaign.created_by || 'Unknown',
    updatedBy: campaign.updated_by || campaign.created_by || 'Unknown'
  };
}

export async function createCampaignAction(data: any): Promise<ActionResult<EmailBlast>> {
  try {
    console.log('Server Action - Creating campaign with data:', data);
    
    // Transform the nested form data to the flat structure expected by the server
    const transformedData = {
      name: data.name || '',
      description: data.description || '',
      subject: data.content?.subject || '',
      content_text: data.content?.body || '',
      content_html: data.content?.bodyHtml || data.content?.body || '',
      from_name: data.from_name || 'IES Admin',
      from_email: data.from_email || '<EMAIL>',
      reply_to_email: data.reply_to_email || '',
      recipientListId: data.recipientListId || '',
      tags: data.tags || [],
      metadata: data.metadata || {},
      templateId: data.content?.templateId || '',
    };
    
    console.log('Server Action - Transformed data:', transformedData);
    
    const supabase = await createClient();
    
    // DEBUG: Check recipient list ID and verify it exists
    console.log('Server Action - Checking recipient list ID:', transformedData.recipientListId);
    
    const { data: allLists, error: listsError } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('id, name, recipient_count')
      .limit(10);
    
    console.log('Server Action - Available recipient lists:', allLists);
    if (listsError) {
      console.error('Server Action - Error fetching recipient lists:', listsError);
    }
    
    // Validate data - expect flat structure after transformation
    const validatedData = emailBlastFormSchema.parse(transformedData);
    console.log('Server Action - Validated recipientListId:', validatedData.recipientListId);
    
    // CRITICAL CHECK: Verify the recipient list exists before proceeding
    const { data: listExists, error: listCheckError } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('id, name')
      .eq('id', validatedData.recipientListId)
      .single();
    
    console.log('Server Action - Recipient list existence check:', { listExists, listCheckError });
    
    if (listCheckError || !listExists) {
      console.error('Server Action - Recipient list does not exist:', validatedData.recipientListId);
      throw new Error(`Recipient list with ID ${validatedData.recipientListId} does not exist`);
    }
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // First, let's see what recipient lists are available (already fetched above)
    console.log('Server Action - All available recipient lists (from earlier query):', allLists);
    
    // Calculate total recipients from recipient lists
    console.log('Server Action - Looking up recipient list with ID:', validatedData.recipientListId);
    const { data: list, error: listError } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('recipient_count')
      .eq('id', validatedData.recipientListId)
      .single();
    
    console.log('Server Action - Recipient list lookup result:', { list, listError });

    const totalRecipients = list?.recipient_count || 0;

    // DEBUGGING: Log all the data being inserted
    const insertData = {
      name: validatedData.name,
      description: validatedData.description,
      status: EmailCampaignStatus.DRAFT,
      subject: validatedData.subject,
      body_text: validatedData.content_text || '',
      body_html: validatedData.content_html,
      from_email: validatedData.from_email || '<EMAIL>',
      from_name: validatedData.from_name || 'Example Company',
      recipient_list_id: validatedData.recipientListId,
      total_recipients: totalRecipients,
      sent_count: 0,
      failed_count: 0,
      opened_count: 0,
      clicked_count: 0,
      tags: validatedData.tags,
      metadata: validatedData.metadata,
      // FIX: Convert empty string to null for UUID field
      template_id: validatedData.templateId && validatedData.templateId.trim() !== '' ? validatedData.templateId : null,
      created_by: user.id,
      updated_by: user.id
    };
    
    console.log('Server Action - About to insert data:', JSON.stringify(insertData, null, 2));
    console.log('Server Action - Insert data field by field:');
    Object.entries(insertData).forEach(([key, value]) => {
      console.log(`  ${key}: ${typeof value} = ${JSON.stringify(value)}`);
    });

    const { data: newBlast, error } = await supabase
      .schema('email_system')
      .from('email_campaigns')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Server Action - Error creating email blast:', error);
      console.error('Server Action - Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      throw new Error(`Failed to create email blast: ${error.message}`);
    }

    revalidatePath('/email-blast');
    const campaign = transformToEmailBlast(newBlast);
    console.log('Server Action - Campaign created successfully:', campaign);
    return { success: true, data: campaign };
  } catch (error) {
    console.error('Server Action - Error creating campaign:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create campaign'
    };
  }
}

export async function scheduleCampaignAction(campaignId: string, scheduledAt: string): Promise<ActionResult<EmailBlast>> {
  try {
    const supabase = await createClient();
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data: updatedBlast, error } = await supabase
      .schema('email_system')
      .from('email_campaigns')
      .update({
        status: EmailCampaignStatus.SCHEDULED,
        scheduled_at: scheduledAt,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', campaignId)
      .eq('status', EmailCampaignStatus.DRAFT)
      .select()
      .single();

    if (error) {
      console.error('Server Action - Error scheduling email blast:', error);
      throw new Error('Failed to schedule email blast');
    }

    revalidatePath('/email-blast');
    return { success: true, data: transformToEmailBlast(updatedBlast) };
  } catch (error) {
    console.error('Server Action - Error scheduling campaign:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to schedule campaign'
    };
  }
}

export async function sendCampaignAction(campaignId: string): Promise<ActionResult<EmailBlast>> {
  try {
    const supabase = await createClient();
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // First update the campaign status
    const { data: updatedBlast, error: updateError } = await supabase
      .schema('email_system')
      .from('email_campaigns')
      .update({
        status: EmailCampaignStatus.SENDING,
        started_at: new Date().toISOString(),
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', campaignId)
      .in('status', [EmailCampaignStatus.DRAFT, EmailCampaignStatus.SCHEDULED])
      .select()
      .single();

    if (updateError) {
      console.error('Server Action - Error updating campaign status:', updateError);
      throw new Error('Failed to update campaign status');
    }

    revalidatePath('/email-blast');
    return { success: true, data: transformToEmailBlast(updatedBlast) };
  } catch (error) {
    console.error('Server Action - Error sending campaign:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to send campaign'
    };
  }
}