"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { EmailBlastForm } from "@/components/email-blast/forms/email-blast-form";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { recipientListsService } from "@/services/email-blast/recipient-lists.service";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import type { RecipientList } from "@/types/email-blast/recipient-list.types";
import { createCampaignAction, scheduleCampaignAction, sendCampaignAction } from './actions';

export default function AddEmailCampaignPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [recipientLists, setRecipientLists] = useState<RecipientList[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecipientLists();
  }, []);

  const loadRecipientLists = async () => {
    try {
      const lists = await recipientListsService.getAll();
      setRecipientLists(lists);
    } catch (error) {
      console.error("Error loading recipient lists:", error);
      toast({
        title: "Error",
        description: "Failed to load recipient lists",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: any, action: "draft" | "schedule" | "send") => {
    try {
      console.log('Frontend - Form data being submitted:', JSON.stringify(data, null, 2));
      console.log('Frontend - recipientListId specifically:', data.recipientListId);
      const result = await createCampaignAction(data);
      
      if (result.success) {
        const campaign = result.data;
        
        if (action === "send") {
          const sendResult = await sendCampaignAction(campaign.id);
          if (!sendResult.success) {
            throw new Error(sendResult.error);
          }
        } else if (action === "schedule" && data.scheduledAt) {
          const scheduleResult = await scheduleCampaignAction(campaign.id, data.scheduledAt.toISOString());
          if (!scheduleResult.success) {
            throw new Error(scheduleResult.error);
          }
        }
        
        toast({
          title: "Success",
          description: action === "draft" 
            ? "Campaign saved as draft" 
            : action === "schedule"
            ? "Campaign scheduled successfully"
            : "Campaign is being sent",
        });
        
        router.push(`/email-blast`);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("Error creating campaign:", error);
      toast({
        title: "Error",
        description: `Failed to create campaign: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Create Campaign", isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Email Blast", href: "/email-blast" },
          { title: "Create Campaign", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/email-blast">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Campaigns
            </Link>
          </Button>
        </div>

        <div className="space-y-1">
          <h1 className="text-2xl font-bold">Create Email Campaign</h1>
          <p className="text-muted-foreground">
            Create a new email campaign to send to your members
          </p>
        </div>

        {/* Email Campaign Form */}
        <EmailBlastForm 
          recipientLists={recipientLists}
          onSubmit={handleSubmit}
        />
      </div>
    </AdminPanelLayout>
  );
}