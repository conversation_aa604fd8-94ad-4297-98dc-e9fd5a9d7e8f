import { Metadata } from "next";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { TemplateGallery } from "@/components/email-blast/templates/gallery/template-gallery";

export const metadata: Metadata = {
  title: "Email Templates",
  description: "Manage your email templates for campaigns",
};

export default function EmailTemplatesPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Templates",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="container mx-auto py-8">
        <TemplateGallery />
      </div>
    </AdminPanelLayout>
  );
}