import { Metadata } from "next";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { TemplateForm } from "@/components/email-blast/templates/forms/template-form";

export const metadata: Metadata = {
  title: "Edit Email Template",
  description: "Edit your email template",
};

interface EditTemplatePageProps {
  params: {
    id: string;
  };
}

export default function EditTemplatePage({ params }: EditTemplatePageProps) {
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Templates",
        href: "/email-blast/templates",
      },
      {
        title: "Edit Template",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="container mx-auto py-8">
        <TemplateForm templateId={params.id} mode="edit" />
      </div>
    </AdminPanelLayout>
  );
}