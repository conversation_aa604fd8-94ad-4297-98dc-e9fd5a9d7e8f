import { Metadata } from "next";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { TemplateDetail } from "@/components/email-blast/templates/detail/template-detail";

export const metadata: Metadata = {
  title: "Template Details",
  description: "View email template details and manage template",
};

interface TemplateDetailPageProps {
  params: {
    id: string;
  };
}

export default function TemplateDetailPage({ params }: TemplateDetailPageProps) {
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Templates",
        href: "/email-blast/templates",
      },
      {
        title: "Template Details",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="container mx-auto py-8">
        <TemplateDetail templateId={params.id} />
      </div>
    </AdminPanelLayout>
  );
}