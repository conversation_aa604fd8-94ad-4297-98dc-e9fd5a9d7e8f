import { Metadata } from "next";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { TemplateForm } from "@/components/email-blast/templates/forms/template-form";

export const metadata: Metadata = {
  title: "Create Email Template",
  description: "Create a new email template for your campaigns",
};

export default function CreateTemplatePage() {
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Templates",
        href: "/email-blast/templates",
      },
      {
        title: "Create Template",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="container mx-auto py-8">
        <TemplateForm mode="create" />
      </div>
    </AdminPanelLayout>
  );
}