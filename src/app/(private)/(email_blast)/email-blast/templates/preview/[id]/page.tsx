"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { TemplatePreview } from "@/components/email-blast/templates/ui/template-preview";
import { useEmailTemplate } from "@/hooks/email-blast/use-email-templates";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Send } from "lucide-react";
import Link from "next/link";

interface TemplatePreviewPageProps {
  params: {
    id: string;
  };
}

function TemplatePreviewClient({ templateId }: { templateId: string }) {
  const { template, loading, error } = useEmailTemplate(templateId);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center space-y-2">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto" />
            <p className="text-muted-foreground">Loading template...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !template) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">Template not found</p>
            <Button asChild variant="outline">
              <Link href="/email-blast/templates">
                Back to Templates
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/email-blast/templates/${template.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Template
            </Link>
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Template Preview</h1>
            <p className="text-muted-foreground">
              Full preview of "{template.name}"
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/email-blast/templates/${template.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Template
            </Link>
          </Button>
        </div>
      </div>

      {/* Preview Content */}
      <TemplatePreview template={template} showActions={false} />
    </div>
  );
}

export default function TemplatePreviewPage({ params }: TemplatePreviewPageProps) {
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Templates",
        href: "/email-blast/templates",
      },
      {
        title: "Preview",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="container mx-auto py-8">
        <TemplatePreviewClient templateId={params.id} />
      </div>
    </AdminPanelLayout>
  );
}