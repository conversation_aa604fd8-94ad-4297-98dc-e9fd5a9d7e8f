"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  Edit, 
  Send, 
  Copy, 
  Trash2,
  Mail,
  Users,
  Calendar,
  Eye,
  MousePointer,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useCampaign, useRealtimeCampaign } from "@/hooks/email-blast/use-campaigns";
import { useDeleteCampaign, useDuplicateCampaign, useSendCampaign, useSendTestEmail } from "@/hooks/email-blast/use-campaign-mutations";
import type { EmailBlast } from "@/types/email-blast/email-blast.types";
import { CampaignStatusBadge } from "@/components/email-blast/ui/campaign-status-badge";
import { EmailPreviewModal } from "@/components/email-blast/ui/email-preview-modal";
import { ConfirmSendDialog } from "@/components/email-blast/dialogs/confirm-send-dialog";
import { SendTestEmailDialog } from "@/components/email-blast/dialogs/send-test-email-dialog";
import { format } from "date-fns";

export default function EmailCampaignDetailPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const router = useRouter();
  const { toast } = useToast();
  
  const { campaign, stats, isLoading, error } = useRealtimeCampaign(params.id);
  const deleteCampaign = useDeleteCampaign();
  const duplicateCampaign = useDuplicateCampaign();
  const sendCampaign = useSendCampaign(params.id);
  const sendTestEmail = useSendTestEmail(params.id);
  
  const [previewOpen, setPreviewOpen] = useState(false);
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [testEmailDialogOpen, setTestEmailDialogOpen] = useState(false);

  const handleEdit = () => {
    router.push(`/email-blast/edit/${params.id}`);
  };

  const handleDuplicate = async () => {
    if (campaign) {
      try {
        const duplicated = await duplicateCampaign.mutateAsync(params.id, `${campaign.name} - Copy`);
        if (duplicated) {
          router.push(`/email-blast/edit/${duplicated.id}`);
        }
      } catch (error) {
        // Error is already handled by the hook
      }
    }
  };

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this campaign?")) {
      try {
        await deleteCampaign.mutateAsync(params.id);
        // Navigation is handled by the hook
      } catch (error) {
        // Error is already handled by the hook
      }
    }
  };

  const handleSend = async () => {
    try {
      await sendCampaign.mutateAsync();
      // Navigation is handled by the hook
    } catch (error) {
      // Error is already handled by the hook
    }
  };

  const handleTestEmail = async (emails: string[]) => {
    try {
      await sendTestEmail.mutateAsync(emails);
    } catch (error) {
      // Error is already handled by the hook
    }
  };

  if (isLoading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Loading...", isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (error || !campaign) {
    const errorMessage = error ? (error instanceof Error ? error.message : String(error)) : "Campaign not found";
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Campaigns
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{errorMessage}</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  const canEdit = campaign.status === 'draft' || campaign.status === 'scheduled';
  const canSend = campaign.status === 'draft';

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Email Blast", href: "/email-blast" },
          { title: campaign.name, isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Campaigns
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{campaign.name}</h1>
              <p className="text-muted-foreground">{campaign.description}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <CampaignStatusBadge status={campaign.status} />
            {canSend && (
              <Button
                size="sm"
                onClick={() => setSendDialogOpen(true)}
              >
                <Send className="mr-2 h-4 w-4" />
                Send Campaign
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTestEmailDialogOpen(true)}
            >
              <Mail className="mr-2 h-4 w-4" />
              Send Test
            </Button>
            {canEdit && (
              <Button variant="outline" size="sm" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" />
              Duplicate
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleDelete}
              className="text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-6">
          <Card className="p-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Users className="mr-1 h-4 w-4" />
                Recipients
              </p>
              <p className="text-2xl font-bold">{(campaign.recipientCount || 0).toLocaleString()}</p>
            </div>
          </Card>
          <Card className="p-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <CheckCircle className="mr-1 h-4 w-4" />
                Sent
              </p>
              <p className="text-2xl font-bold text-green-600">
                {(campaign.sentCount || 0).toLocaleString()}
              </p>
            </div>
          </Card>
          <Card className="p-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <XCircle className="mr-1 h-4 w-4" />
                Failed
              </p>
              <p className="text-2xl font-bold text-red-600">
                {(campaign.failedCount || 0).toLocaleString()}
              </p>
            </div>
          </Card>
          <Card className="p-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Eye className="mr-1 h-4 w-4" />
                Opens
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {(campaign.openCount || 0).toLocaleString()}
              </p>
              {(campaign.sentCount || 0) > 0 && (
                <p className="text-xs text-muted-foreground">
                  {(((campaign.openCount || 0) / (campaign.sentCount || 1)) * 100).toFixed(1)}%
                </p>
              )}
            </div>
          </Card>
          <Card className="p-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <MousePointer className="mr-1 h-4 w-4" />
                Clicks
              </p>
              <p className="text-2xl font-bold text-purple-600">
                {(campaign.clickCount || 0).toLocaleString()}
              </p>
              {(campaign.sentCount || 0) > 0 && (
                <p className="text-xs text-muted-foreground">
                  {(((campaign.clickCount || 0) / (campaign.sentCount || 1)) * 100).toFixed(1)}%
                </p>
              )}
            </div>
          </Card>
          <Card className="p-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <BarChart3 className="mr-1 h-4 w-4" />
                Success Rate
              </p>
              <p className="text-2xl font-bold text-green-600">
                {(campaign.sentCount || 0) > 0 
                  ? `${((((campaign.sentCount || 0) - (campaign.failedCount || 0)) / (campaign.sentCount || 1)) * 100).toFixed(1)}%`
                  : '0%'
                }
              </p>
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="details" className="w-full">
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="recipients">Recipients</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Campaign Details</h3>
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Name</dt>
                  <dd className="text-sm">{campaign.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Status</dt>
                  <dd><CampaignStatusBadge status={campaign.status} /></dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Created At</dt>
                  <dd className="text-sm">{format(new Date(campaign.createdAt), 'PPP p')}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Updated At</dt>
                  <dd className="text-sm">{format(new Date(campaign.updatedAt), 'PPP p')}</dd>
                </div>
                {campaign.sentAt && (
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">Sent At</dt>
                    <dd className="text-sm">{format(new Date(campaign.sentAt), 'PPP p')}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Created By</dt>
                  <dd className="text-sm">{campaign.createdBy}</dd>
                </div>
                <div className="col-span-2">
                  <dt className="text-sm font-medium text-muted-foreground">Description</dt>
                  <dd className="text-sm">{campaign.description || 'No description'}</dd>
                </div>
                {campaign.tags && campaign.tags.length > 0 && (
                  <div className="col-span-2">
                    <dt className="text-sm font-medium text-muted-foreground">Tags</dt>
                    <dd className="flex gap-2 mt-1">
                      {campaign.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">{tag}</Badge>
                      ))}
                    </dd>
                  </div>
                )}
              </dl>
            </Card>
          </TabsContent>

          <TabsContent value="content" className="space-y-4">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Email Content</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewOpen(true)}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </Button>
              </div>
              <dl className="space-y-4">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Subject</dt>
                  <dd className="text-sm font-mono bg-muted p-2 rounded mt-1">
                    {campaign.content.subject}
                  </dd>
                </div>
                {campaign.content.templateId && (
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">Template</dt>
                    <dd className="text-sm">{campaign.content.templateId}</dd>
                  </div>
                )}
                {campaign.content.attachments && campaign.content.attachments.length > 0 && (
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">Attachments</dt>
                    <dd className="text-sm">
                      {campaign.content.attachments.length} file(s) attached
                    </dd>
                  </div>
                )}
              </dl>
            </Card>
          </TabsContent>

          <TabsContent value="recipients" className="space-y-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Recipient Information</h3>
              <dl className="space-y-4">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Recipient List</dt>
                  <dd className="text-sm">
                    <Link 
                      href={`/email-blast/lists/${campaign.recipientListId}`}
                      className="text-primary hover:underline"
                    >
                      View Recipient List
                    </Link>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Total Recipients</dt>
                  <dd className="text-sm">{(campaign.recipientCount || 0).toLocaleString()}</dd>
                </div>
              </dl>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Schedule Information</h3>
              {campaign.scheduledAt ? (
                <dl className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">Scheduled For</dt>
                    <dd className="text-sm flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      {format(new Date(campaign.scheduledAt), 'PPP p')}
                    </dd>
                  </div>
                  {campaign.metadata?.timezone && (
                    <div>
                      <dt className="text-sm font-medium text-muted-foreground">Timezone</dt>
                      <dd className="text-sm">{campaign.metadata.timezone}</dd>
                    </div>
                  )}
                </dl>
              ) : (
                <p className="text-sm text-muted-foreground">
                  This campaign is not scheduled. It will be sent immediately when you click "Send Campaign".
                </p>
              )}
            </Card>
          </TabsContent>
        </Tabs>

        {/* Dialogs */}
        <EmailPreviewModal
          open={previewOpen}
          onOpenChange={setPreviewOpen}
          subject={campaign.content.subject}
          htmlContent={campaign.content.bodyHtml}
          textContent={campaign.content.body}
        />
        
        <ConfirmSendDialog
          open={sendDialogOpen}
          onOpenChange={setSendDialogOpen}
          campaignName={campaign.name}
          recipientCount={campaign.recipientCount}
          onConfirm={handleSend}
        />
        
        <SendTestEmailDialog
          open={testEmailDialogOpen}
          onOpenChange={setTestEmailDialogOpen}
          onSendTest={handleTestEmail}
        />
      </div>
    </AdminPanelLayout>
  );
}