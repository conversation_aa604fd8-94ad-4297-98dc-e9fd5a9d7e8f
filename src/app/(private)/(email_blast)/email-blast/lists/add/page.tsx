"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { RecipientListForm } from "@/components/email-blast/recipient-lists/forms/recipient-list-form";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { recipientListsService } from "@/services/email-blast/recipient-lists.service";

export default function AddRecipientListPage() {
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: any) => {
    try {
      const list = await recipientListsService.create(data);
      toast({
        title: "Success",
        description: "Recipient list created successfully",
      });
      router.push(`/email-blast/lists/${list.id}`);
    } catch (error) {
      console.error("Error creating list:", error);
      toast({
        title: "Error",
        description: "Failed to create recipient list",
        variant: "destructive",
      });
    }
  };
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Email Blast", href: "/email-blast" },
          { title: "Recipient Lists", href: "/email-blast/lists" },
          { title: "Create List", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/email-blast/lists">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Lists
            </Link>
          </Button>
        </div>

        <div className="space-y-1">
          <h1 className="text-2xl font-bold">Create Recipient List</h1>
          <p className="text-muted-foreground">
            Create a new recipient list based on member criteria
          </p>
        </div>

        {/* Recipient List Form */}
        <RecipientListForm onSubmit={handleSubmit} />
      </div>
    </AdminPanelLayout>
  );
}