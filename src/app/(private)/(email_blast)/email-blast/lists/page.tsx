"use client";

import { useState, useEffect, Suspense } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RecipientListsTable } from "@/components/table/recipient-lists-table";
import Link from "next/link";
import { 
  PlusCircle, 
  Users, 
  Filter,
  Calendar,
  Activity
} from "lucide-react";
import type { RecipientList } from "@/types/email-blast/recipient-list.types";
import { useRecipientLists, useDeleteRecipientList, useDuplicateRecipientList } from "@/hooks/email-blast/use-recipient-lists";
import { recipientListColumns } from "@/components/email-blast/recipient-lists/columns";
import { useRouter } from "next/navigation";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface RecipientListSummary {
  total_lists: number;
  active_lists: number;
  total_recipients: number;
  lists_used_this_month: number;
}

export default function RecipientListsPage() {
  const router = useRouter();
  const breadcrumbs = {
    items: [
      {
        title: "Email Blast",
        href: "/email-blast",
      },
      {
        title: "Recipient Lists",
        isCurrentPage: true,
      },
    ],
  };

  const { data: listsData, isLoading, refetch } = useRecipientLists();
  const deleteMutation = useDeleteRecipientList();
  const duplicateMutation = useDuplicateRecipientList();
  
  const lists = listsData?.lists || [];
  const [summary, setSummary] = useState<RecipientListSummary>({
    total_lists: 0,
    active_lists: 0,
    total_recipients: 0,
    lists_used_this_month: 0,
  });

  useEffect(() => {
    if (lists && lists.length >= 0) {
      // Calculate summary
      const active = lists.filter(l => l.status === 'active').length;
      const totalRecipients = lists.reduce((sum, l) => sum + (l.recipientCount || 0), 0);
      const usedThisMonth = lists.filter(l => {
        if (!l.lastUsedAt) return false;
        const lastUsed = new Date(l.lastUsedAt);
        const now = new Date();
        return lastUsed.getMonth() === now.getMonth() && 
               lastUsed.getFullYear() === now.getFullYear();
      }).length;
      
      setSummary({
        total_lists: lists.length,
        active_lists: active,
        total_recipients: totalRecipients,
        lists_used_this_month: usedThisMonth,
      });
    }
  }, [lists]);

  const handleRowClick = (list: RecipientList) => {
    router.push(`/email-blast/lists/${list.id}`);
  };

  const handleDuplicate = async (list: RecipientList) => {
    try {
      await duplicateMutation.mutateAsync(list.id, `${list.name} - Copy`);
      refetch();
    } catch (error) {
      // Error is already handled by the hook
    }
  };

  const handleDelete = async (list: RecipientList) => {
    if (confirm(`Are you sure you want to delete "${list.name}"?`)) {
      try {
        await deleteMutation.mutateAsync(list.id);
        refetch();
      } catch (error) {
        // Error is already handled by the hook
      }
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Recipient Lists
            </h1>
            <p className="text-sm text-gray-600">
              Manage and organize your email recipient lists
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild size="sm">
              <Link href="/email-blast/lists/add">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create List
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast">
                <Users className="mr-2 h-4 w-4" />
                Back to Campaigns
              </Link>
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-4 mb-6">
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total Lists
              </p>
              <p className="text-2xl font-bold">{formatNumber(summary.total_lists)}</p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Activity className="mr-1 h-4 w-4" />
                Active Lists
              </p>
              <p className="text-2xl font-bold text-green-600">
                {formatNumber(summary.active_lists)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Users className="mr-1 h-4 w-4" />
                Total Recipients
              </p>
              <p className="text-2xl font-bold">
                {formatNumber(summary.total_recipients)}
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground flex items-center">
                <Calendar className="mr-1 h-4 w-4" />
                Used This Month
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {formatNumber(summary.lists_used_this_month)}
              </p>
            </div>
          </Card>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <Suspense fallback={
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner />
            </div>
          }>
            <RecipientListsTable 
              columns={recipientListColumns({
                onView: handleRowClick,
                onDuplicate: handleDuplicate,
                onDelete: handleDelete,
              })}
              data={lists}
            />
          </Suspense>
        )}
      </div>
    </AdminPanelLayout>
  );
}