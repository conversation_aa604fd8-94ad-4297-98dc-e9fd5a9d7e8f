"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Arrow<PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { RecipientListForm } from "@/components/email-blast/recipient-lists/forms/recipient-list-form";
import { recipientListsService } from "@/services/email-blast/recipient-lists.service";
import type { RecipientList } from "@/types/email-blast/recipient-list.types";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";

export default function EditRecipientListPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [list, setList] = useState<RecipientList | null>(null);

  useEffect(() => {
    const loadList = async () => {
      if (!params.id || typeof params.id !== 'string') {
        toast({
          title: "Error",
          description: "Invalid list ID",
          variant: "destructive",
        });
        router.push("/email-blast/lists");
        return;
      }

      try {
        setLoading(true);
        const data = await recipientListsService.getById(params.id);
        
        if (!data) {
          throw new Error("List not found");
        }
        
        setList(data);
      } catch (error) {
        console.error("Error loading list:", error);
        toast({
          title: "Error",
          description: "Failed to load recipient list",
          variant: "destructive",
        });
        router.push("/email-blast/lists");
      } finally {
        setLoading(false);
      }
    };

    loadList();
  }, [params.id, router, toast]);

  const handleSubmit = async (data: any) => {
    try {
      await recipientListsService.update(params.id as string, data);
      toast({
        title: "Success",
        description: "Recipient list updated successfully",
      });
      router.push(`/email-blast/lists/${params.id}`);
    } catch (error) {
      console.error("Error updating list:", error);
      toast({
        title: "Error",
        description: "Failed to update recipient list",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Recipient Lists", href: "/email-blast/lists" },
            { title: "Edit List", href: "#" },
            { title: "Loading...", isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (!list) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Recipient Lists", href: "/email-blast/lists" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast/lists">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Lists
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">List not found</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Email Blast", href: "/email-blast" },
          { title: "Recipient Lists", href: "/email-blast/lists" },
          { title: list.name, href: `/email-blast/lists/${list.id}` },
          { title: "Edit", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/email-blast/lists/${list.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        <div className="space-y-1">
          <h1 className="text-2xl font-bold">Edit Recipient List</h1>
          <p className="text-muted-foreground">
            Update list: {list.name}
          </p>
        </div>

        {/* Recipient List Form */}
        <RecipientListForm 
          list={list}
          onSubmit={handleSubmit}
        />
      </div>
    </AdminPanelLayout>
  );
}