"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  Edit, 
  Copy, 
  Trash2,
  Users,
  Calendar,
  Filter,
  RefreshCw,
  Download,
  Mail
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { recipientListsService } from "@/services/email-blast/recipient-lists.service";
import type { RecipientList } from "@/types/email-blast/recipient-list.types";
import { format } from "date-fns";
import { RecipientPreviewTable } from "@/components/email-blast/recipient-lists/ui/recipient-preview-table";
import { SavedFilterBadge } from "@/components/email-blast/recipient-lists/ui/saved-filter-badge";
import { DuplicateListDialog } from "@/components/email-blast/recipient-lists/dialogs/duplicate-list-dialog";
import { DeleteListDialog } from "@/components/email-blast/recipient-lists/dialogs/delete-list-dialog";

export default function RecipientListDetailPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [list, setList] = useState<RecipientList | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [previewLoading, setPreviewLoading] = useState(false);

  useEffect(() => {
    loadList();
    loadPreview();
  }, [params.id]);

  const loadList = async () => {
    try {
      setLoading(true);
      const data = await recipientListsService.getById(params.id);
      
      if (data) {
        setList(data);
      } else {
        setError("Recipient list not found");
      }
    } catch (error) {
      console.error("Error loading list:", error);
      setError("Failed to load recipient list");
      toast({
        title: "Error",
        description: "Failed to load recipient list details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadPreview = async () => {
    try {
      setPreviewLoading(true);
      const data = await recipientListsService.getRecipients(params.id, { limit: 10 });
      setPreviewData(data);
    } catch (error) {
      console.error("Error loading preview:", error);
    } finally {
      setPreviewLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/email-blast/lists/${params.id}/edit`);
  };

  const handleDuplicate = async (name: string, description: string) => {
    try {
      const duplicated = await recipientListsService.duplicate(params.id, { name, description });
      toast({
        title: "Success",
        description: "List duplicated successfully",
      });
      router.push(`/email-blast/lists/${duplicated.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate list",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    try {
      await recipientListsService.delete(params.id);
      toast({
        title: "Success",
        description: "List deleted successfully",
      });
      router.push("/email-blast/lists");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete list",
        variant: "destructive",
      });
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await recipientListsService.refreshCount(params.id);
      await loadList();
      await loadPreview();
      toast({
        title: "Success",
        description: "Recipient count refreshed",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh count",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleExport = async () => {
    try {
      await recipientListsService.export(params.id);
      toast({
        title: "Success",
        description: "Export started. You'll receive an email when it's ready.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export list",
        variant: "destructive",
      });
    }
  };

  const handleCreateCampaign = () => {
    router.push(`/email-blast/add?listId=${params.id}`);
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Recipient Lists", href: "/email-blast/lists" },
            { title: "Loading...", isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (error || !list) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Email Blast", href: "/email-blast" },
            { title: "Recipient Lists", href: "/email-blast/lists" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast/lists">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Lists
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Email Blast", href: "/email-blast" },
          { title: "Recipient Lists", href: "/email-blast/lists" },
          { title: list.name, isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/email-blast/lists">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Lists
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{list.name}</h1>
              <p className="text-muted-foreground">{list.description}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={list.status === 'active' ? 'default' : 'secondary'}>
              {list.status}
            </Badge>
            <Button
              size="sm"
              onClick={handleCreateCampaign}
            >
              <Mail className="mr-2 h-4 w-4" />
              Create Campaign
            </Button>
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setDuplicateDialogOpen(true)}
            >
              <Copy className="mr-2 h-4 w-4" />
              Duplicate
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setDeleteDialogOpen(true)}
              className="text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Recipients</p>
                <p className="text-2xl font-bold">{list.recipientCount.toLocaleString()}</p>
                {list.recipientCountChange && (
                  <p className={`text-xs ${list.recipientCountChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {list.recipientCountChange > 0 ? '+' : ''}{list.recipientCountChange} from last update
                  </p>
                )}
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                <p className="text-2xl font-bold">{format(new Date(list.updatedAt), 'MMM d')}</p>
                <p className="text-xs text-muted-foreground">{format(new Date(list.updatedAt), 'yyyy')}</p>
              </div>
              <Calendar className="h-8 w-8 text-muted-foreground" />
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Last Used</p>
                <p className="text-2xl font-bold">
                  {list.lastUsedAt ? format(new Date(list.lastUsedAt), 'MMM d') : 'Never'}
                </p>
                {list.lastUsedAt && (
                  <p className="text-xs text-muted-foreground">{format(new Date(list.lastUsedAt), 'yyyy')}</p>
                )}
              </div>
              <Mail className="h-8 w-8 text-muted-foreground" />
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Times Used</p>
                <p className="text-2xl font-bold">{(list.usageCount || 0).toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">in campaigns</p>
              </div>
              <RefreshCw className="h-8 w-8 text-muted-foreground" />
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="details" className="w-full">
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="preview">Preview Recipients</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">List Details</h3>
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Name</dt>
                  <dd className="text-sm">{list.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Status</dt>
                  <dd>
                    <Badge variant={list.status === 'active' ? 'default' : 'secondary'}>
                      {list.status}
                    </Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Created At</dt>
                  <dd className="text-sm">{format(new Date(list.createdAt), 'PPP p')}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Updated At</dt>
                  <dd className="text-sm">{format(new Date(list.updatedAt), 'PPP p')}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Created By</dt>
                  <dd className="text-sm">{list.createdBy}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Type</dt>
                  <dd className="text-sm capitalize">{list.type}</dd>
                </div>
                <div className="col-span-2">
                  <dt className="text-sm font-medium text-muted-foreground">Description</dt>
                  <dd className="text-sm">{list.description || 'No description'}</dd>
                </div>
                {list.tags && list.tags.length > 0 && (
                  <div className="col-span-2">
                    <dt className="text-sm font-medium text-muted-foreground">Tags</dt>
                    <dd className="flex gap-2 mt-1">
                      {list.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">{tag}</Badge>
                      ))}
                    </dd>
                  </div>
                )}
              </dl>
              <div className="mt-6 flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={refreshing}
                >
                  <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                  Refresh Count
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export List
                </Button>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Filter Criteria</h3>
                <Badge variant="outline">
                  <Filter className="mr-1 h-3 w-3" />
                  {list.filters ? Object.keys(list.filters).length : 0} filters
                </Badge>
              </div>
              {list.filters && Object.keys(list.filters).length > 0 ? (
                <div className="space-y-3">
                  {Object.entries(list.filters).map(([key, value]) => (
                    <SavedFilterBadge
                      key={key}
                      filter={{ field: key, operator: '=', value }}
                    />
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No filters applied. This list includes all members.
                </p>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Recipients Preview</h3>
                <p className="text-sm text-muted-foreground">
                  Showing first 10 recipients
                </p>
              </div>
              {previewLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <RecipientPreviewTable recipients={previewData} />
              )}
            </Card>
          </TabsContent>
        </Tabs>

        {/* Dialogs */}
        <DuplicateListDialog
          open={duplicateDialogOpen}
          onOpenChange={setDuplicateDialogOpen}
          originalList={{
            name: list.name,
            description: list.description,
            recipientCount: list.recipientCount,
            filters: list.filters ? Object.entries(list.filters) : []
          }}
          onConfirm={handleDuplicate}
        />
        
        <DeleteListDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          listName={list.name}
          recipientCount={list.recipientCount}
          onConfirm={handleDelete}
        />
      </div>
    </AdminPanelLayout>
  );
}