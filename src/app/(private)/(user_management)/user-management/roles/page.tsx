// Roles Management Page
// Dedicated page for role and permission management

'use client';

import { Metadata } from 'next'
import { Suspense, useState } from 'react'
import AdminPanelLayout from '@/components/layout/admin-panel-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, ShieldPlus } from 'lucide-react'
import { PermissionGuard } from '@/components/rbac'
import { RoleManagementTable, RoleManagementStats } from '@/components/table'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { CreateRoleDialog } from '@/components/user-management/dialogs/create-role-dialog'

// Note: metadata export removed since this is now a client component

export default function RolesPage() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [tableKey, setTableKey] = useState(0);

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "User Management", href: "/user-management" },
          { title: "Roles", isCurrentPage: true },
        ],
      }}
    >
      <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Roles</h1>
          <p className="text-muted-foreground">
            Configure roles and their permissions across all system modules
          </p>
        </div>
        
        <PermissionGuard module="USER_MANAGEMENT" action="CREATE">
          <Button onClick={() => setCreateDialogOpen(true)}>
            <ShieldPlus className="mr-2 h-4 w-4" />
            Create Role
          </Button>
        </PermissionGuard>
      </div>

      {/* Role Statistics */}
      <Suspense fallback={<LoadingSpinner />}>
        <RoleManagementStats />
      </Suspense>

      {/* Roles Table */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          <div>
            <h2 className="text-xl font-semibold">System Roles</h2>
            <p className="text-sm text-muted-foreground">
              All roles with their permissions and assigned user counts
            </p>
          </div>
        </div>
        
        <Suspense fallback={<LoadingSpinner />}>
          <RoleManagementTable key={tableKey} />
        </Suspense>
      </div>

      {/* Create Role Dialog */}
      <CreateRoleDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={() => {
          setCreateDialogOpen(false);
          // Force table refresh by changing key
          setTableKey(prev => prev + 1);
        }}
      />
    </div>
    </AdminPanelLayout>
  )
}