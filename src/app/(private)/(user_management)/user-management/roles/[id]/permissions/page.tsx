"use client"

import { useEffect, useState } from "react"
import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Save, X, AlertCircle, Shield } from "lucide-react"
import { createClient } from "@/utils/supabase/Client"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { PermissionMatrix } from "@/components/user-management/permissions/permission-matrix"
import { toast } from "@/hooks/use-toast"
import { UserManagementClientService } from "@/services/user-management/user-management-client.service"
import type { Role, Module, ModulePermission } from "@/types/user-management.types"

interface RolePermissionsPageProps {
  params: {
    id: string
  }
}

export default function RolePermissionsPage({ params }: RolePermissionsPageProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [role, setRole] = useState<Role | null>(null)
  const [modules, setModules] = useState<Module[]>([])
  const [permissions, setPermissions] = useState<ModulePermission[]>([])
  const [originalPermissions, setOriginalPermissions] = useState<ModulePermission[]>([])
  const [error, setError] = useState<string | null>(null)
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    fetchRoleAndPermissions()
  }, [params.id])

  useEffect(() => {
    // Check if permissions have changed
    const permissionsChanged = JSON.stringify(permissions) !== JSON.stringify(originalPermissions)
    setHasChanges(permissionsChanged)
  }, [permissions, originalPermissions])

  const fetchRoleAndPermissions = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch role details
      const rolesResult = await UserManagementClientService.getRoles()
      if (!rolesResult.success || !rolesResult.data) {
        throw new Error(rolesResult.error || "Failed to fetch roles")
      }
      
      const foundRole = rolesResult.data.find(r => r.id === params.id)
      if (!foundRole) {
        notFound()
      }
      
      setRole(foundRole)
      
      // Fetch modules
      const modulesResult = await UserManagementClientService.getModules()
      if (!modulesResult.success || !modulesResult.data) {
        throw new Error(modulesResult.error || "Failed to fetch modules")
      }
      
      setModules(modulesResult.data)
      
      // Fetch existing permissions for this role
      const supabase = createClient()
      const { data: permissionsData, error: permissionsError } = await supabase
        .schema("access_control")
        .from("module_permissions")
        .select("*")
        .eq("role_id", params.id)
      
      if (permissionsError) {
        // If permission denied, continue with empty permissions
        if (permissionsError.message.includes('permission denied')) {
          console.warn('Permission denied for module_permissions table. Using empty permissions.')
        } else {
          throw new Error("Failed to fetch role permissions")
        }
      }
      
      // Create permission records for all modules (including those without permissions yet)
      const allPermissions = modulesResult.data.map(module => {
        const existingPermission = permissionsData?.find(p => p.module_id === module.module_id)
        
        if (existingPermission) {
          return existingPermission
        } else {
          // Create default permission object for modules without permissions
          return {
            permission_id: `temp-${module.module_id}`,
            role_id: params.id,
            module_id: module.module_id,
            can_create: false,
            can_read: false,
            can_update: false,
            can_delete: false,
            can_review: false,
            can_approve: false,
            can_export: false,
            accessible_membership_types: [],
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } as ModulePermission
        }
      })
      
      setPermissions(allPermissions)
      setOriginalPermissions(JSON.parse(JSON.stringify(allPermissions)))
    } catch (err) {
      console.error("Error fetching role permissions:", err)
      setError(err instanceof Error ? err.message : "Failed to load role permissions")
    } finally {
      setLoading(false)
    }
  }

  const handlePermissionChange = (moduleId: string, permission: Partial<ModulePermission>) => {
    setPermissions(prevPermissions => 
      prevPermissions.map(p => 
        p.module_id === moduleId ? { ...p, ...permission } : p
      )
    )
  }

  const handleSave = async () => {
    if (!role) return
    
    try {
      setSaving(true)
      const supabase = createClient()
      
      // Get only changed permissions
      const changedPermissions = permissions.filter((perm, index) => {
        const original = originalPermissions[index]
        return JSON.stringify(perm) !== JSON.stringify(original)
      })
      
      // Process each changed permission using direct Supabase operations
      for (const permission of changedPermissions) {
        try {
          let result;
          
          if (permission.permission_id.startsWith("temp-")) {
            // This is a new permission - insert it
            console.log(`Inserting new permission for module ${permission.module_id}`);
            
            result = await supabase
              .schema("access_control")
              .from("module_permissions")
              .insert({
                role_id: permission.role_id,
                module_id: permission.module_id,
                can_create: permission.can_create,
                can_read: permission.can_read,
                can_update: permission.can_update,
                can_delete: permission.can_delete,
                can_review: permission.can_review,
                can_approve: permission.can_approve,
                can_export: permission.can_export,
                accessible_membership_types: permission.accessible_membership_types || null
              })
              .select()
              .single();
          } else {
            // This is an existing permission - update it
            console.log(`Updating existing permission ${permission.permission_id} for module ${permission.module_id}`);
            
            result = await supabase
              .schema("access_control")
              .from("module_permissions")
              .update({
                can_create: permission.can_create,
                can_read: permission.can_read,
                can_update: permission.can_update,
                can_delete: permission.can_delete,
                can_review: permission.can_review,
                can_approve: permission.can_approve,
                can_export: permission.can_export,
                accessible_membership_types: permission.accessible_membership_types || null,
                updated_at: new Date().toISOString()
              })
              .eq("permission_id", permission.permission_id)
              .select()
              .single();
          }
          
          if (result.error) {
            console.error(`Error saving permission for module ${permission.module_id}:`, result.error);
            
            // If it's a duplicate key error, try to update instead
            if (result.error.code === '23505' && permission.permission_id.startsWith("temp-")) {
              console.log(`Duplicate key error, trying update instead for module ${permission.module_id}`);
              
              const updateResult = await supabase
                .schema("access_control")
                .from("module_permissions")
                .update({
                  can_create: permission.can_create,
                  can_read: permission.can_read,
                  can_update: permission.can_update,
                  can_delete: permission.can_delete,
                  can_review: permission.can_review,
                  can_approve: permission.can_approve,
                  can_export: permission.can_export,
                  accessible_membership_types: permission.accessible_membership_types || null,
                  updated_at: new Date().toISOString()
                })
                .eq("role_id", permission.role_id)
                .eq("module_id", permission.module_id)
                .select()
                .single();
              
              if (updateResult.error) {
                throw new Error(`Failed to update permission: ${updateResult.error.message}`);
              } else {
                console.log(`Successfully updated permission for module ${permission.module_id} after duplicate key error`);
              }
            } else if (result.error.message.includes('permission denied')) {
              console.warn(`Permission denied when saving permission for module ${permission.module_id}`)
              // Continue processing other permissions
            } else {
              throw new Error(`Failed to save permission: ${result.error.message}`)
            }
          } else {
            console.log(`Successfully saved permission for module ${permission.module_id}`, result.data);
          }
        } catch (err) {
          console.error(`Error saving permission for module ${permission.module_id}:`, err)
          toast({
            title: "Warning",
            description: `Failed to save some permissions. Error: ${err instanceof Error ? err.message : 'Unknown error'}`,
            variant: "destructive",
          })
          // Continue with other permissions
        }
      }
      
      toast({
        title: "Success",
        description: "Role permissions have been updated successfully",
      })
      
      // Refresh the permissions to get the updated data
      await fetchRoleAndPermissions()
    } catch (err) {
      console.error("Error saving permissions:", err)
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to save permissions",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setPermissions(JSON.parse(JSON.stringify(originalPermissions)))
  }

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return (
      <AdminPanelLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </AdminPanelLayout>
    )
  }

  if (!role) {
    notFound()
  }

  const breadcrumbs = {
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
      },
      {
        title: "User Management",
        href: "/user-management",
      },
      {
        title: "Roles",
        href: "/user-management/roles",
      },
      {
        title: role.name,
        href: `/user-management/roles/${role.id}`,
      },
      {
        title: "Permissions",
        href: `/user-management/roles/${role.id}/permissions`,
      },
    ],
  }

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/user-management/roles/${role.id}`}>
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Role Permissions</h1>
              <p className="text-muted-foreground">
                Configure permissions for {role.name.replace(/_/g, " ")}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={!hasChanges || saving}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || saving}
            >
              <Save className="mr-2 h-4 w-4" />
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>

        {/* Role Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {role.name.replace(/_/g, " ")}
            </CardTitle>
            <CardDescription>
              {role.role_description || "Configure module permissions for this role"}
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Permission Matrix */}
        <PermissionMatrix
          modules={modules}
          permissions={permissions}
          onPermissionChange={handlePermissionChange}
        />

        {/* Save Actions */}
        {hasChanges && (
          <Card>
            <CardContent className="flex justify-end gap-2 pt-6">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel Changes
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? "Saving..." : "Save All Changes"}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminPanelLayout>
  )
}