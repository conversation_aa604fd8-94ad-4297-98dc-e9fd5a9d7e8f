"use client"

import { useEffect, useState } from "react"
import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, AlertCircle } from "lucide-react"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { RoleForm } from "@/components/user-management/forms/role-form"
import { toast } from "@/hooks/use-toast"
import { UserManagementClientService } from "@/services/user-management/user-management-client.service"
import type { Role } from "@/types/user-management.types"

interface RoleEditPageProps {
  params: {
    id: string
  }
}

export default function RoleEditPage({ params }: RoleEditPageProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [role, setRole] = useState<Role | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchRoleDetails()
  }, [params.id])

  const fetchRoleDetails = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch all roles and find the specific one
      const rolesResult = await UserManagementClientService.getRoles()
      if (!rolesResult.success || !rolesResult.data) {
        throw new Error(rolesResult.error || "Failed to fetch roles")
      }
      
      const foundRole = rolesResult.data.find(r => r.id === params.id)
      if (!foundRole) {
        notFound()
      }
      
      setRole(foundRole)
    } catch (err) {
      console.error("Error fetching role details:", err)
      setError(err instanceof Error ? err.message : "Failed to load role details")
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (data: any) => {
    try {
      // TODO: Implement actual update API when available
      // For now, we'll show a not implemented message
      
      // The data object contains:
      // - name (disabled in edit mode)
      // - role_description
      // - is_active
      
      toast({
        title: "Not Implemented",
        description: "Role update functionality is not yet implemented",
        variant: "destructive",
      })
      
      // When implemented, redirect back to detail page
      // router.push(`/user-management/roles/${params.id}`)
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update role",
        variant: "destructive",
      })
    }
  }

  const handleCancel = () => {
    router.push(`/user-management/roles/${params.id}`)
  }

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return (
      <AdminPanelLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </AdminPanelLayout>
    )
  }

  if (!role) {
    notFound()
  }

  const breadcrumbs = {
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
      },
      {
        title: "User Management",
        href: "/user-management",
      },
      {
        title: "Roles",
        href: "/user-management/roles",
      },
      {
        title: role.name,
        href: `/user-management/roles/${role.id}`,
      },
      {
        title: "Edit",
        href: `/user-management/roles/${role.id}/edit`,
      },
    ],
  }

  const isSystemRole = ["SUPER_ADMIN", "ADMIN", "MANAGER", "REVIEWER", "STAFF"].includes(role.name)

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href={`/user-management/roles/${role.id}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Role</h1>
            <p className="text-muted-foreground">Update role information</p>
          </div>
        </div>

        {/* System Role Warning */}
        {isSystemRole && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This is a system role. Only the description and status can be modified.
            </AlertDescription>
          </Alert>
        )}

        {/* Edit Form Card */}
        <Card>
          <CardHeader>
            <CardTitle>Role Information</CardTitle>
            <CardDescription>
              Update the role's details. Role names cannot be changed after creation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RoleForm
              role={role}
              mode="edit"
              onSubmit={handleSubmit}
              onCancel={handleCancel}
            />
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  )
}