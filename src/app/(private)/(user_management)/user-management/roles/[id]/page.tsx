"use client"

import { useEffect, useState } from "react"
import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import {
  ArrowLeft,
  Edit,
  Trash2,
  Shield,
  Users,
  Calendar,
  Clock,
  AlertCircle,
  ShieldCheck,
  ShieldOff,
  Settings,
} from "lucide-react"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertD<PERSON>og<PERSON><PERSON>le,
  Alert<PERSON>ialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { UserManagementClientService } from "@/services/user-management/user-management-client.service"
import type { Role, UserWithRole, Module } from "@/types/user-management.types"

interface RoleDetailsPageProps {
  params: {
    id: string
  }
}

export default function RoleDetailsPage({ params }: RoleDetailsPageProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [role, setRole] = useState<Role | null>(null)
  const [usersWithRole, setUsersWithRole] = useState<UserWithRole[]>([])
  const [modules, setModules] = useState<Module[]>([])
  const [deleting, setDeleting] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchRoleDetails()
  }, [params.id])

  const fetchRoleDetails = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch all roles and find the specific one
      const rolesResult = await UserManagementClientService.getRolesWithUserCounts()
      if (!rolesResult.success || !rolesResult.data) {
        throw new Error(rolesResult.error || "Failed to fetch roles")
      }
      
      const foundRole = rolesResult.data.find(r => r.id === params.id)
      if (!foundRole) {
        notFound()
      }
      
      setRole(foundRole)
      
      // Fetch users with this role
      const usersResult = await UserManagementClientService.getUsersWithRoles()
      if (usersResult.success && usersResult.data) {
        const filteredUsers = usersResult.data.filter(u => u.role_name === foundRole.name)
        setUsersWithRole(filteredUsers)
      }
      
      // Fetch modules for permission summary
      const modulesResult = await UserManagementClientService.getModules()
      if (modulesResult.success && modulesResult.data) {
        setModules(modulesResult.data)
      }
    } catch (err) {
      console.error("Error fetching role details:", err)
      setError(err instanceof Error ? err.message : "Failed to load role details")
    } finally {
      setLoading(false)
    }
  }

  const handleStatusToggle = async () => {
    if (!role) return
    
    try {
      setUpdating(true)
      
      // TODO: Implement actual status toggle API when available
      toast({
        title: "Not Implemented",
        description: "Role status toggle is not yet implemented",
        variant: "destructive",
      })
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update status",
        variant: "destructive",
      })
    } finally {
      setUpdating(false)
    }
  }

  const handleDelete = async () => {
    if (!role) return
    
    try {
      setDeleting(true)
      
      // TODO: Implement actual delete API when available
      toast({
        title: "Not Implemented",
        description: "Role deletion is not yet implemented",
        variant: "destructive",
      })
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to delete role",
        variant: "destructive",
      })
    } finally {
      setDeleting(false)
    }
  }

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return (
      <AdminPanelLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </AdminPanelLayout>
    )
  }

  if (!role) {
    notFound()
  }

  const breadcrumbs = {
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
      },
      {
        title: "User Management",
        href: "/user-management",
      },
      {
        title: "Roles",
        href: "/user-management/roles",
      },
      {
        title: role.name,
        href: `/user-management/roles/${role.id}`,
      },
    ],
  }

  const isSystemRole = ["SUPER_ADMIN", "ADMIN", "MANAGER", "REVIEWER", "STAFF"].includes(role.name)

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/user-management/roles">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Role Details</h1>
              <p className="text-muted-foreground">View and manage role information</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Link href={`/user-management/roles/${role.id}/permissions`}>
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Manage Permissions
              </Button>
            </Link>
            
            <Link href={`/user-management/roles/${role.id}/edit`}>
              <Button variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>
            
            <Button
              variant="outline"
              onClick={handleStatusToggle}
              disabled={updating || isSystemRole}
            >
              {role.is_active ? (
                <>
                  <ShieldOff className="mr-2 h-4 w-4" />
                  Deactivate
                </>
              ) : (
                <>
                  <ShieldCheck className="mr-2 h-4 w-4" />
                  Activate
                </>
              )}
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={isSystemRole}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the role
                    and remove it from all assigned users.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete} disabled={deleting}>
                    {deleting ? "Deleting..." : "Delete Role"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {/* Role Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>Role Information</CardTitle>
            <CardDescription>Basic information about the role</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Shield className="h-4 w-4" />
                    Role Name
                  </div>
                  <p className="font-medium">{role.name.replace(/_/g, " ")}</p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Users className="h-4 w-4" />
                    Assigned Users
                  </div>
                  <p className="font-medium">{role.user_count || 0} users</p>
                </div>
                
                {role.role_description && (
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">
                      Description
                    </div>
                    <p className="text-sm">{role.role_description}</p>
                  </div>
                )}
              </div>
              
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground mb-1">
                    Status
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={role.is_active ? "success" : "destructive"}>
                      {role.is_active ? "Active" : "Inactive"}
                    </Badge>
                    {isSystemRole && (
                      <Badge variant="secondary">System Role</Badge>
                    )}
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Calendar className="h-4 w-4" />
                    Created
                  </div>
                  <p className="font-medium">
                    {format(new Date(role.created_at), "PPP")}
                  </p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Clock className="h-4 w-4" />
                    ID
                  </div>
                  <p className="font-mono text-sm">{role.id}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users with this Role Card */}
        <Card>
          <CardHeader>
            <CardTitle>Users with this Role</CardTitle>
            <CardDescription>
              {usersWithRole.length > 0
                ? `${usersWithRole.length} users currently have this role`
                : "No users currently have this role"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {usersWithRole.length > 0 ? (
              <div className="space-y-2">
                {usersWithRole.slice(0, 10).map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div>
                      <p className="font-medium">{user.full_name || user.email}</p>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={user.status ? "success" : "destructive"}>
                        {user.status ? "Active" : "Inactive"}
                      </Badge>
                      <Link href={`/user-management/users/${user.id}`}>
                        <Button variant="ghost" size="sm">
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
                {usersWithRole.length > 10 && (
                  <div className="text-center pt-4">
                    <Link href={`/user-management/users?role=${role.name}`}>
                      <Button variant="outline" size="sm">
                        View all {usersWithRole.length} users
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No users assigned to this role</p>
                <Link href="/user-management/users">
                  <Button variant="outline" size="sm" className="mt-4">
                    Assign Users
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Permission Summary Card */}
        <Card>
          <CardHeader>
            <CardTitle>Permission Summary</CardTitle>
            <CardDescription>
              Overview of permissions granted by this role
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Permission details will be displayed here once the permission matrix is configured.
              </p>
              <Link href={`/user-management/roles/${role.id}/permissions`}>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Configure Permissions
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  )
}