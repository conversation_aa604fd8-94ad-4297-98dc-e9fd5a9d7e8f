'use client'

// Enterprise User & Role Management Dashboard
// Main dashboard with tabbed interface for Users and Roles management

import { Suspense } from 'react'
import Link from 'next/link'
import AdminPanelLayout from '@/components/layout/admin-panel-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Shield, 
  Settings, 
  Activity,
  UserPlus,
  ShieldPlus,
  Crown,
  AlertTriangle
} from 'lucide-react'
import { PermissionGuard } from '@/components/rbac'
import { UserManagementTable } from '@/components/table/user-management-table'
import { RoleManagementTable } from '@/components/table/role-management-table'
import { UserManagementStats } from '@/components/table/user-management-stats'
import { RoleManagementStats } from '@/components/table/role-management-stats'
import { OwnershipTransferWidget } from '@/components/table/ownership-transfer-widget'
import { QuickActions } from '@/components/table/quick-actions'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function UserManagementPage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "User Management", isCurrentPage: true },
        ],
      }}
    >
      <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">User & Role Management</h1>
          <p className="text-muted-foreground">
            Enterprise-grade user and role management with comprehensive RBAC controls
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <PermissionGuard module="SYSTEM" action="UPDATE">
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </PermissionGuard>
          
          <PermissionGuard module="SYSTEM" action="READ">
            <Button variant="outline" size="sm">
              <Activity className="mr-2 h-4 w-4" />
              Audit Logs
            </Button>
          </PermissionGuard>
        </div>
      </div>

      {/* System Owner Widget */}
      <PermissionGuard module="SYSTEM" action="UPDATE" fallback={null}>
        <Suspense fallback={<LoadingSpinner />}>
          <OwnershipTransferWidget />
        </Suspense>
      </PermissionGuard>

      {/* Quick Navigation */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Link href="/user-management/users">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Users</CardTitle>
                  <CardDescription>Manage user accounts and profiles</CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </Link>

        <Link href="/user-management/roles">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                  <Shield className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Roles</CardTitle>
                  <CardDescription>Configure roles and permissions</CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </Link>

        <Link href="/user-management/audit">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                  <Activity className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Audit Logs</CardTitle>
                  <CardDescription>View system activity and logs</CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </Link>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="users" className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-fit grid-cols-2">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="roles" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Roles
            </TabsTrigger>
          </TabsList>

          {/* Quick Actions */}
          <div className="flex items-center gap-2">
            <QuickActions />
          </div>
        </div>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          {/* User Statistics */}
          <Suspense fallback={<UserStatsLoadingSkeleton />}>
            <UserManagementStats />
          </Suspense>

          {/* Users Table */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                <div>
                  <h2 className="text-xl font-semibold">System Users</h2>
                  <p className="text-sm text-muted-foreground">
                    Manage user accounts, profiles, and role assignments
                  </p>
                </div>
              </div>
              
              <PermissionGuard module="SYSTEM" action="CREATE">
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </PermissionGuard>
            </div>
            
            <Suspense fallback={<TableLoadingSkeleton />}>
              <UserManagementTable />
            </Suspense>
          </div>
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-6">
          {/* Role Statistics */}
          <Suspense fallback={<RoleStatsLoadingSkeleton />}>
            <RoleManagementStats />
          </Suspense>

          {/* Roles Table */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                <div>
                  <h2 className="text-xl font-semibold">System Roles</h2>
                  <p className="text-sm text-muted-foreground">
                    Configure roles and their permissions across all modules
                  </p>
                </div>
              </div>
              
              <PermissionGuard module="SYSTEM" action="CREATE">
                <Button>
                  <ShieldPlus className="mr-2 h-4 w-4" />
                  Create Role
                </Button>
              </PermissionGuard>
            </div>
            
            <Suspense fallback={<TableLoadingSkeleton />}>
              <RoleManagementTable />
            </Suspense>
          </div>
        </TabsContent>
      </Tabs>

      {/* Security Notice */}
      <Card className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950">
        <CardContent className="flex items-center gap-3 pt-6">
          <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          <div className="space-y-1">
            <p className="text-sm font-medium text-amber-900 dark:text-amber-100">
              Security Notice
            </p>
            <p className="text-sm text-amber-700 dark:text-amber-200">
              All user and role changes are audited and logged. System owner accounts are protected and require ownership transfer for modifications.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
    </AdminPanelLayout>
  )
}

// Loading Skeletons
function UserStatsLoadingSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-8 bg-muted rounded w-16"></div>
              <div className="h-3 bg-muted rounded w-32"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function RoleStatsLoadingSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-8 bg-muted rounded w-16"></div>
              <div className="h-3 bg-muted rounded w-32"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function TableLoadingSkeleton() {
  return (
    <div className="p-6 space-y-4">
      {/* Search and filters skeleton */}
      <div className="flex items-center justify-between">
        <div className="h-10 bg-muted rounded w-80 animate-pulse"></div>
        <div className="flex gap-2">
          <div className="h-10 bg-muted rounded w-32 animate-pulse"></div>
          <div className="h-10 bg-muted rounded w-24 animate-pulse"></div>
        </div>
      </div>
      
      {/* Table skeleton */}
      <div className="space-y-3">
        <div className="h-12 bg-muted rounded animate-pulse"></div>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-16 bg-muted rounded animate-pulse"></div>
        ))}
      </div>
    </div>
  )
}