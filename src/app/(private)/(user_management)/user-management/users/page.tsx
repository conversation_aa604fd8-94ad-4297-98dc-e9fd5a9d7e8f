// Users Management Page
// Dedicated page for user listing and management

'use client';

import { Metadata } from 'next'
import { Suspense, useState } from 'react'
import AdminPanelLayout from '@/components/layout/admin-panel-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Users, UserPlus } from 'lucide-react'
import { PermissionGuard } from '@/components/rbac'
import { UserManagementTable, UserManagementStats } from '@/components/table'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { CreateUserDialog } from '@/components/user-management/dialogs/create-user-dialog'

// Note: metadata export removed since this is now a client component

export default function UsersPage() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [tableKey, setTableKey] = useState(0);

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "User Management", href: "/user-management" },
          { title: "Users", isCurrentPage: true },
        ],
      }}
    >
      <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Users</h1>
          <p className="text-muted-foreground">
            Manage user accounts, profiles, and access permissions
          </p>
        </div>
        
        <PermissionGuard module="USER_MANAGEMENT" action="CREATE">
          <Button onClick={() => setCreateDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </PermissionGuard>
      </div>

      {/* User Statistics */}
      <Suspense fallback={<LoadingSpinner />}>
        <UserManagementStats />
      </Suspense>

      {/* Users Table */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          <div>
            <h2 className="text-xl font-semibold">All Users</h2>
            <p className="text-sm text-muted-foreground">
              Complete list of system users with their roles and status
            </p>
          </div>
        </div>
        
        <Suspense fallback={<LoadingSpinner />}>
          <UserManagementTable key={tableKey} />
        </Suspense>
      </div>

      {/* Create User Dialog */}
      <CreateUserDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={() => {
          setCreateDialogOpen(false);
          // Force table refresh by changing key
          setTableKey(prev => prev + 1);
        }}
      />
    </div>
    </AdminPanelLayout>
  )
}