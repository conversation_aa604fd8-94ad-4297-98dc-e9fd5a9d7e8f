"use client"

import { useEffect, useState } from "react"
import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, AlertCircle } from "lucide-react"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { UserForm } from "@/components/user-management/forms/user-form"
import { toast } from "@/hooks/use-toast"
import { UserManagementClientService } from "@/services/user-management/user-management-client.service"
import type { UserWithRole } from "@/types/user-management.types"

interface UserEditPageProps {
  params: {
    id: string
  }
}

export default function UserEditPage({ params }: UserEditPageProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<UserWithRole | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchUserDetails()
  }, [params.id])

  const fetchUserDetails = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch all users and find the specific one
      const usersResult = await UserManagementClientService.getUsersWithRoles()
      if (!usersResult.success || !usersResult.data) {
        throw new Error(usersResult.error || "Failed to fetch users")
      }
      
      const foundUser = usersResult.data.find(u => u.id === params.id)
      if (!foundUser) {
        notFound()
      }
      
      setUser(foundUser)
    } catch (err) {
      console.error("Error fetching user details:", err)
      setError(err instanceof Error ? err.message : "Failed to load user details")
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (data: any) => {
    try {
      // TODO: Implement actual update API when available
      // For now, we'll show a not implemented message
      
      // The data object contains:
      // - email (disabled in edit mode)
      // - full_name
      // - phone_number
      // - department
      // - status
      // - roles (array of role names)
      
      // When API is available, it should:
      // 1. Update user details
      // 2. Update role assignments if changed
      
      toast({
        title: "Not Implemented",
        description: "User update functionality is not yet implemented",
        variant: "destructive",
      })
      
      // When implemented, redirect back to detail page
      // router.push(`/user-management/users/${params.id}`)
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update user",
        variant: "destructive",
      })
    }
  }

  const handleCancel = () => {
    router.push(`/user-management/users/${params.id}`)
  }

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return (
      <AdminPanelLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </AdminPanelLayout>
    )
  }

  if (!user) {
    notFound()
  }

  const breadcrumbs = {
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
      },
      {
        title: "User Management",
        href: "/user-management",
      },
      {
        title: "Users",
        href: "/user-management/users",
      },
      {
        title: user.full_name || user.email,
        href: `/user-management/users/${user.id}`,
      },
      {
        title: "Edit",
        href: `/user-management/users/${user.id}/edit`,
      },
    ],
  }

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href={`/user-management/users/${user.id}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit User</h1>
            <p className="text-muted-foreground">Update user information and role assignments</p>
          </div>
        </div>

        {/* Edit Form Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>
              Update the user's details. Email cannot be changed after account creation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserForm
              user={user}
              mode="edit"
              onSubmit={handleSubmit}
              onCancel={handleCancel}
            />
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  )
}