"use client"

import { useEffect, useState } from "react"
import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import {
  ArrowLeft,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  Shield,
  Activity,
  Mail,
  Phone,
  Calendar,
  Clock,
  AlertCircle,
} from "lucide-react"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertD<PERSON>og<PERSON><PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { UserManagementClientService } from "@/services/user-management/user-management-client.service"
import type { UserWithRole, UserPermissions } from "@/types/user-management.types"

interface UserDetailsPageProps {
  params: {
    id: string
  }
}

export default function UserDetailsPage({ params }: UserDetailsPageProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<UserWithRole | null>(null)
  const [permissions, setPermissions] = useState<UserPermissions[]>([])
  const [deleting, setDeleting] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchUserDetails()
  }, [params.id])

  const fetchUserDetails = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch all users and find the specific one
      const usersResult = await UserManagementClientService.getUsersWithRoles()
      if (!usersResult.success || !usersResult.data) {
        throw new Error(usersResult.error || "Failed to fetch users")
      }
      
      const foundUser = usersResult.data.find(u => u.id === params.id)
      if (!foundUser) {
        notFound()
      }
      
      setUser(foundUser)
      
      // TODO: Fetch user activity/permissions when API is available
      // For now, set empty permissions
      setPermissions([])
    } catch (err) {
      console.error("Error fetching user details:", err)
      setError(err instanceof Error ? err.message : "Failed to load user details")
    } finally {
      setLoading(false)
    }
  }

  const handleStatusToggle = async () => {
    if (!user) return
    
    try {
      setUpdating(true)
      const newStatus = !user.status
      
      const result = await UserManagementClientService.bulkUpdateUserStatus(
        [user.id],
        newStatus
      )
      
      if (!result.success) {
        throw new Error(result.error || "Failed to update user status")
      }
      
      // Update local state
      setUser({ ...user, status: newStatus })
      
      toast({
        title: "Status Updated",
        description: `User ${newStatus ? "activated" : "deactivated"} successfully`,
      })
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update status",
        variant: "destructive",
      })
    } finally {
      setUpdating(false)
    }
  }

  const handleDelete = async () => {
    if (!user) return
    
    try {
      setDeleting(true)
      
      // TODO: Implement actual delete API when available
      toast({
        title: "Not Implemented",
        description: "User deletion is not yet implemented",
        variant: "destructive",
      })
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to delete user",
        variant: "destructive",
      })
    } finally {
      setDeleting(false)
    }
  }

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return (
      <AdminPanelLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </AdminPanelLayout>
    )
  }

  if (!user) {
    notFound()
  }

  const breadcrumbs = {
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
      },
      {
        title: "User Management",
        href: "/user-management",
      },
      {
        title: "Users",
        href: "/user-management/users",
      },
      {
        title: user.full_name || user.email,
        href: `/user-management/users/${user.id}`,
      },
    ],
  }

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/user-management/users">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">User Details</h1>
              <p className="text-muted-foreground">View and manage user information</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Link href={`/user-management/users/${user.id}/edit`}>
              <Button variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>
            
            <Button
              variant="outline"
              onClick={handleStatusToggle}
              disabled={updating}
            >
              {user.status ? (
                <>
                  <UserX className="mr-2 h-4 w-4" />
                  Deactivate
                </>
              ) : (
                <>
                  <UserCheck className="mr-2 h-4 w-4" />
                  Activate
                </>
              )}
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={user.is_system_owner}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the user
                    account and remove all associated data.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete} disabled={deleting}>
                    {deleting ? "Deleting..." : "Delete User"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {/* User Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>Basic information about the user</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Mail className="h-4 w-4" />
                    Email
                  </div>
                  <p className="font-medium">{user.email}</p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Shield className="h-4 w-4" />
                    Full Name
                  </div>
                  <p className="font-medium">{user.full_name || "Not provided"}</p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Phone className="h-4 w-4" />
                    Phone Number
                  </div>
                  <p className="font-medium">{user.phone_number || "Not provided"}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Activity className="h-4 w-4" />
                    Status
                  </div>
                  <Badge variant={user.status ? "success" : "destructive"}>
                    {user.status ? "Active" : "Inactive"}
                  </Badge>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Calendar className="h-4 w-4" />
                    Created
                  </div>
                  <p className="font-medium">
                    {format(new Date(user.created_at), "PPP")}
                  </p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                    <Clock className="h-4 w-4" />
                    ID
                  </div>
                  <p className="font-mono text-sm">{user.id}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Roles and Permissions Card */}
        <Card>
          <CardHeader>
            <CardTitle>Roles & Permissions</CardTitle>
            <CardDescription>Assigned roles and access permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Current Role</h4>
                {user.role_name ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-base">
                      {user.role_name.replace(/_/g, " ")}
                    </Badge>
                    {user.is_system_owner && (
                      <Badge variant="default">System Owner</Badge>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No role assigned</p>
                )}
                {user.role_description && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {user.role_description}
                  </p>
                )}
              </div>
              
              <Separator />
              
              <div>
                <h4 className="text-sm font-medium mb-2">Permission Summary</h4>
                {permissions.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {permissions.map((perm, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium">{perm.module_name}:</span>
                        <span className="ml-2 text-muted-foreground">
                          {[
                            perm.can_read && "Read",
                            perm.can_create && "Create",
                            perm.can_update && "Update",
                            perm.can_delete && "Delete",
                            perm.can_review && "Review",
                            perm.can_approve && "Approve",
                            perm.can_export && "Export",
                          ]
                            .filter(Boolean)
                            .join(", ")}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Permission details will be available after role assignment
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity Summary Card */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Summary</CardTitle>
            <CardDescription>Recent actions and system usage</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Activity tracking will be implemented in a future update
            </p>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  )
}