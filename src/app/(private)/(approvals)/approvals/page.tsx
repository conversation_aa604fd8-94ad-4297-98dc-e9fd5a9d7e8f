"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { ApprovalTable } from "@/components/table/approval-table";
import { Suspense } from "react";

export default function ApprovalsPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Approvals",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Approval Requests
            </h1>
            <p className="text-sm text-gray-600">
              Manage and review approval requests across all modules
            </p>
          </div>
        </div>
        
        <Suspense fallback={
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        }>
          <ApprovalTable />
        </Suspense>
      </div>
    </AdminPanelLayout>
  );
}