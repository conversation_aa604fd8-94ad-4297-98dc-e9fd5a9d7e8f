"use client";

import React, { useEffect, useState } from 'react';
import { notFound } from "next/navigation";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { ApprovalDetail } from "@/components/pages/approvals/approval-detail";
import { ApprovalService } from "@/services/approvals/approval.service";
import { ApprovalRequestWithDetails } from "@/types/approvals/approval.types";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface ApprovalDetailPageProps {
  params: {
    id: string;
  };
}

export default function ApprovalDetailPage({ params }: ApprovalDetailPageProps) {
  const [approvalData, setApprovalData] = useState<ApprovalRequestWithDetails>();
  const [isLoading, setIsLoading] = useState(true);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const result = await ApprovalService.getApprovalRequestById(params.id);
      
      if (!result || !result.data) {
        notFound();
      }

      setApprovalData(result.data);
    } catch (error) {
      console.error('Error fetching approval details:', error);
      notFound();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [params.id]);

  if (isLoading) {
    return (
      <AdminPanelLayout breadcrumbs={{
        items: [
          { title: "Dashboard", href: "/" },
          { title: "Approvals", href: "/approvals" },
          { title: "Loading...", isCurrentPage: true },
        ],
      }}>
        <div className="p-4 relative min-h-96">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (!approvalData) {
    notFound();
  }

  const breadcrumbs = {
    items: [
      {
        title: "Dashboard",
        href: "/",
      },
      {
        title: "Approvals",
        href: "/approvals",
      },
      {
        title: approvalData.request_number,
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <ApprovalDetail approval={approvalData} onRefresh={fetchData} />
      </div>
    </AdminPanelLayout>
  );
}