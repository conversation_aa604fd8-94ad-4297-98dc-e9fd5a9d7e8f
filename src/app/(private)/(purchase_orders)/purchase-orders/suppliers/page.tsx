"use client";

import { useState } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { SuppliersTable } from "@/components/table/suppliers-table";
import { SupplierFormDialog } from "@/components/purchase-orders/forms/supplier-form-dialog";
import { PlusCircle } from "lucide-react";

export default function SuppliersPage() {
  const [showSupplierDialog, setShowSupplierDialog] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleSupplierSuccess = () => {
    setRefreshKey(prev => prev + 1); // Trigger table refresh
  };

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Orders", href: "/purchase-orders" },
          { title: "Suppliers", isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Suppliers
            </h1>
            <p className="text-sm text-gray-600">
              Manage and review suppliers across the system
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setShowSupplierDialog(true)} size="sm">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add New Supplier
            </Button>
          </div>
        </div>

        <SuppliersTable
          key={refreshKey}
        />
      </div>

      {/* add only */}
      <SupplierFormDialog
        open={showSupplierDialog}
        onOpenChange={setShowSupplierDialog}
        onSuccess={handleSupplierSuccess}
      />

      {/* TO BE IMPLEMENT */}
      {/* <BulkStatusChangeDialog
        open={showBulkStatusDialog}
        onOpenChange={setShowBulkStatusDialog}
        suppliers={selectedSuppliers}
        onSuccess={handleStatusChangeSuccess}
      /> */}
    </AdminPanelLayout>
  );
}