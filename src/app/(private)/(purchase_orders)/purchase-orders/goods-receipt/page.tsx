"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { GoodsReceiptForm } from "@/components/purchase-orders/forms/goods-receipt-form";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";

export default function GoodsReceiptPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const poId = searchParams.get("po_id");

  const handleSuccess = (grId: string) => {
    router.push("/purchase-orders");
  };

  const backHref = poId ? `/purchase-orders/${poId}` : "/purchase-orders";

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Orders", href: "/purchase-orders" },
          { title: "Goods Receipt", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={backHref}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Purchase Orders
            </Link>
          </Button>
        </div>

        {/* Goods Receipt Form */}
        <GoodsReceiptForm
          selectedPOId={poId || undefined}
          onSuccess={handleSuccess}
        />
      </div>
    </AdminPanelLayout>
  );
}