"use client";

import { useState, useEffect, Suspense } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PurchaseOrdersTable } from "@/components/table/purchase-orders-table";
import { CurrencyDisplay } from "@/components/purchase-orders/ui";
import Link from "next/link";
import { PlusCircle, Upload, Download, Users } from "lucide-react";
import type { PurchaseOrdersSummary } from "@/types/purchase-orders/purchase-orders.types";
import { getPurchaseOrdersSummary } from "@/services/purchase-orders/purchase-orders.service";

export default function PurchaseOrdersPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Purchase Orders",
        isCurrentPage: true,
      },
    ],
  };
  const [summary, setSummary] = useState<PurchaseOrdersSummary>({
    total_count: 0,
    total_amount: 0,
    pending_approval_count: 0,
    overdue_deliveries_count: 0,
  });

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      const summaryData = await getPurchaseOrdersSummary();
      setSummary(summaryData);
    } catch (error) {
      console.error("Error loading summary:", error);
    }
  };

  const handleSummaryUpdate = (newSummary: PurchaseOrdersSummary) => {
    setSummary(newSummary);
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Purchase Orders
            </h1>
            <p className="text-sm text-gray-600">
              Manage and track purchase orders across all suppliers
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/purchase-orders/suppliers">
                <Users className="mr-2 h-4 w-4" />
                Manage Suppliers
              </Link>
            </Button>
            {/* <Button variant="outline" size="sm" onClick={() => setShowImportDialog(true)}>
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button variant="outline" size="sm" onClick={() => setShowExportDialog(true)}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button> */}
            <Button asChild size="sm">
              <Link href="/purchase-orders/add">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Purchase Order
              </Link>
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-4 mb-6">
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total POs Count
              </p>
              <p className="text-2xl font-bold">{summary.total_count.toLocaleString()}</p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Total Amount
              </p>
              <p className="text-2xl font-bold">
                <CurrencyDisplay amount={summary.total_amount} currency="USD" />
              </p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Pending Approval
              </p>
              <p className="text-2xl font-bold">{summary.pending_approval_count.toLocaleString()}</p>
            </div>
          </Card>
          <Card className="p-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                Overdue Deliveries
              </p>
              <p className="text-2xl font-bold text-red-600">{summary.overdue_deliveries_count.toLocaleString()}</p>
            </div>
          </Card>
        </div>

        <Suspense fallback={
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        }>
          <PurchaseOrdersTable
            onSummaryUpdate={handleSummaryUpdate}
          />
        </Suspense>
      </div>
    </AdminPanelLayout>
  );
}