"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { PurchaseOrderForm } from "@/components/purchase-orders/forms/purchase-order-form";
import { PurchaseOrderErrorBoundary } from "@/components/purchase-orders/error-boundary";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AddPurchaseOrderPage() {
  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Orders", href: "/purchase-orders" },
          { title: "Create New", isCurrentPage: true },
        ],
      }}
    >
        <PurchaseOrderErrorBoundary>
          <div className="space-y-4">
            {/* Back Button */}
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/purchase-orders">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to List
                </Link>
              </Button>
            </div>

            {/* Purchase Order Form */}
            <PurchaseOrderForm isEditing={false} />
          </div>
        </PurchaseOrderErrorBoundary>
    </AdminPanelLayout>
  );
}