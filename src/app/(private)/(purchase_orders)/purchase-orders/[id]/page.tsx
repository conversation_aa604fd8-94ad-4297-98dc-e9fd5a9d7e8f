"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { PurchaseOrderDetail } from "@/components/purchase-orders/detail/purchase-order-detail";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { getPurchaseOrderById } from "@/services/purchase-orders/purchase-orders.service";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";
import { CancelPODialog } from "@/components/purchase-orders/dialogs/cancel-po-dialog";
import { cancelPurchaseOrder, clonePurchaseOrder } from "@/services/purchase-orders/purchase-orders-server.service";
import { AuthService } from "@/services/auth.service";

export default function PurchaseOrderDetailPage({
  params
}: {
  params: { id: string }
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  const [error, setError] = useState<string | null>(null);
  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    const init = async () => {
      // Get authenticated user
      const user = await AuthService.getCurrentUser();

      if (!user?.id) {
        toast({
          title: "Authentication Error",
          description: "You must be logged in to cancel a purchase order.",
          variant: "destructive",
        });
        router.push('/purchase-orders');
        return; // prevent setUserId from running
      }

      setUserId(user.id);
    };

    init();
  }, []);

  useEffect(() => {
    loadPurchaseOrder();
  }, [params.id]);

  const loadPurchaseOrder = async () => {
    try {
      setLoading(true);
      const data = await getPurchaseOrderById(params.id);

      if (data) {
        setPurchaseOrder(data);
      } else {
        setError("Purchase order not found");
      }
    } catch (error) {
      console.error("Error loading purchase order:", error);
      setError("Failed to load purchase order");
      toast({
        title: "Error",
        description: "Failed to load purchase order details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/purchase-orders/edit?id=${params.id}`);
  };

  const handlePrint = () => {
    toast({
      title: "Info",
      description: "Print functionality will be implemented",
    });
  };

  const handleEmail = () => {
    toast({
      title: "Info",
      description: "Email functionality will be implemented",
    });
  };

  const handleCreateGoodsReceipt = () => {
    router.push(`/purchase-orders/goods-receipt?po_id=${params.id}`);
  };

  const handleCancel = () => {
    setCancelDialogOpen(true);
  };

  const handleCancelConfirm = async (reason: string) => {
    if (!purchaseOrder || !userId) return;

    setIsCancelling(true);

    try {
      // Attempt to cancel the purchase order
      const result = await cancelPurchaseOrder(purchaseOrder.id, reason, userId);

      if (!result.success) {
        toast({
          title: "Cancellation Failed",
          description: result.error || "Unable to cancel the purchase order at this time.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Purchase Order Cancelled",
        description: `Purchase Order ${purchaseOrder.po_number} has been cancelled successfully.`,
      });

      setCancelDialogOpen(false);
      await loadPurchaseOrder();
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: error instanceof Error ? error.message : "Something went wrong.",
        variant: "destructive",
      });
    } finally {
      setIsCancelling(false);
    }
  };

  const handleClone = async () => {
    if (!purchaseOrder || !userId) return;

    try {
      const result = await clonePurchaseOrder(purchaseOrder?.id, userId);

      if (result.success && result.newId) {
        console.log("Purchase order cloned successfully from details, new ID:", result.newId);
        toast({
          title: "Success",
          description: "Purchase order cloned successfully",
        });
        // Navigate to edit page for the cloned PO
        router.push(`/purchase-orders/edit?id=${result.newId}`);
      } else {
        console.error("Clone failed from details page:", result.error);
        toast({
          title: "Error",
          description: result.error || "Failed to clone purchase order",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Unexpected error during clone from details:", error);
      toast({
        title: "Error",
        description: `An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Purchase Orders", href: "/purchase-orders" },
            { title: `Loading...`, isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Purchase Orders", href: "/purchase-orders" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/purchase-orders">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to List
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Orders", href: "/purchase-orders" },
          { title: purchaseOrder.po_number, isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/purchase-orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        {/* Purchase Order Detail Component */}
        <PurchaseOrderDetail
          purchaseOrder={purchaseOrder}
          onEdit={handleEdit}
          onPrint={handlePrint}
          onEmail={handleEmail}
          onCreateGoodsReceipt={handleCreateGoodsReceipt}
          onCancel={handleCancel}
          onClone={handleClone}
        />

        {purchaseOrder && (
          <CancelPODialog
            open={cancelDialogOpen}
            onOpenChange={setCancelDialogOpen}
            onConfirm={handleCancelConfirm}
            poNumber={purchaseOrder.po_number}
            isLoading={isCancelling}
          />
        )}
      </div>
    </AdminPanelLayout>
  );
}