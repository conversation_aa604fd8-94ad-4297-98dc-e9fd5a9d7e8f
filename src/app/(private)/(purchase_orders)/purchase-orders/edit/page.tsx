"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { PurchaseOrderForm } from "@/components/purchase-orders/forms/purchase-order-form";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import { getPurchaseOrderAttachment, getPurchaseOrderById } from "@/services/purchase-orders/purchase-orders.service";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";
import type { FileWithPreview } from "@/types/general.types";

export default function EditPurchaseOrderPage() {
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const { toast } = useToast();

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [attachments, setAttachments] = useState<FileWithPreview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadPurchaseOrder(id);
    } else {
      setError("No purchase order ID provided");
      setLoading(false);
    }
  }, [id]);

  const loadPurchaseOrder = async (purchaseOrderId: string) => {
    try {
      setLoading(true);

      const [data, filesData] = await Promise.all([
        getPurchaseOrderById(purchaseOrderId),
        getPurchaseOrderAttachment(purchaseOrderId),
      ]);

      if (data) {
        if (filesData) {
          setAttachments(filesData);
        }
        setPurchaseOrder(data);
      } else {
        console.error("Purchase order not found for editing, ID:", purchaseOrderId);
        setError(`Purchase order not found (ID: ${purchaseOrderId})`);
      }
    } catch (error) {
      console.error("Error loading purchase order for editing:", error);
      setError(`Failed to load purchase order: ${error instanceof Error ? error.message : 'Unknown error'}`);
      toast({
        title: "Error",
        description: "Failed to load purchase order details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Purchase Orders", href: "/purchase-orders" },
          { title: "Edit", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/purchase-orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        {loading ? (
          <LoadingSpinner />
        ) : (error || !purchaseOrder || !id) ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        ) : (
          <PurchaseOrderForm
            isEditing={true}
            purchaseOrderId={id}
            initialData={purchaseOrder}
            attachments={attachments}
            loadPurchaseOrder={loadPurchaseOrder}
          />
        )}

      </div>
    </AdminPanelLayout>
  );
}