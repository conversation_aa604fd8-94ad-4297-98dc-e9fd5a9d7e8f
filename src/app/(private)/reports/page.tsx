"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { 
  BarChart3, 
  Calendar,
  Award,
  Users,
  Activity,
  TrendingUp,
  FileText,
  ChevronRight,
  Clock
} from "lucide-react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";

export default function ReportsPage() {
  const router = useRouter();

  const reportCategories = [
    {
      title: "Attendance Reports",
      description: "Track participant attendance, analyze engagement patterns, and monitor certificate eligibility",
      icon: BarChart3,
      color: "bg-blue-50 text-blue-600",
      href: "/reports/attendance",
      features: [
        "Real-time attendance tracking",
        "Certificate eligibility reports",
        "Daily attendance summaries",
        "Export to Excel/PDF/CSV"
      ]
    },
    // Placeholder for future report types
    {
      title: "Programme Analytics",
      description: "Comprehensive programme performance metrics and participant engagement analysis",
      icon: TrendingUp,
      color: "bg-green-50 text-green-600",
      href: "#",
      features: [
        "Programme completion rates",
        "Participant satisfaction scores", 
        "Revenue and budget analysis",
        "Venue utilization reports"
      ],
      comingSoon: true
    },
    {
      title: "Financial Reports",
      description: "Budget tracking, revenue analysis, and financial performance across programmes",
      icon: FileText,
      color: "bg-purple-50 text-purple-600",
      href: "#",
      features: [
        "Budget vs actual spending",
        "Revenue by programme type",
        "Payment collection status",
        "Profit/loss analysis"
      ],
      comingSoon: true
    }
  ];

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Reports", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Reports Dashboard</h1>
            <p className="text-muted-foreground">
              Access comprehensive analytics and reporting across all system modules
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Live Data</span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Programmes</p>
                  <p className="text-2xl font-bold">12</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-green-50 rounded-lg">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Participants</p>
                  <p className="text-2xl font-bold">1,245</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-orange-50 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg. Attendance</p>
                  <p className="text-2xl font-bold">85.2%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <Award className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Certificates Issued</p>
                  <p className="text-2xl font-bold">892</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {reportCategories.map((category, index) => (
            <Card key={index} className="relative">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${category.color}`}>
                    <category.icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="flex items-center justify-between">
                      {category.title}
                      {category.comingSoon && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          Coming Soon
                        </span>
                      )}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {category.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 mb-4">
                  {category.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <div className="w-1.5 h-1.5 bg-current rounded-full flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button 
                  className="w-full"
                  variant={category.comingSoon ? "secondary" : "default"}
                  disabled={category.comingSoon}
                  onClick={() => !category.comingSoon && router.push(category.href)}
                >
                  {category.comingSoon ? (
                    <>
                      <Clock className="h-4 w-4 mr-2" />
                      Coming Soon
                    </>
                  ) : (
                    <>
                      View Reports
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Information Section */}
        <Card>
          <CardHeader>
            <CardTitle>Report Information</CardTitle>
            <CardDescription>
              Important notes about data freshness and export capabilities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Data Refresh</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Real-time data for active sessions</li>
                  <li>• Analytics updated every 5 minutes</li>
                  <li>• Historical data available for trend analysis</li>
                  <li>• Automated daily summary generation</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Export Options</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Excel format with detailed breakdowns</li>
                  <li>• CSV for external data analysis</li>
                  <li>• PDF reports for presentations</li>
                  <li>• Scheduled email delivery available</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}