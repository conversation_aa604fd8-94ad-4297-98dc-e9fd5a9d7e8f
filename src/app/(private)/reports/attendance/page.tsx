"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { 
  FileText, 
  Calendar, 
  Award, 
  BarChart3, 
  Users,
  Activity,
  Clock,
  TrendingUp,
  CheckCircle,
  UserCheck
} from "lucide-react";
import { AttendanceStatsResponse } from "@/types/attendance/attendance.types";
import { getAttendanceStatistics, getProgrammesWithAttendance } from "@/services/client/attendance-reports.service";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { AttendanceSummaryReport } from "@/components/reports/attendance-summary-report";
import { CertificateEligibilityReport } from "@/components/reports/certificate-eligibility-report";
import { DailyAttendanceReport } from "@/components/reports/daily-attendance-report";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";


export default function AttendanceReportsPage() {
  const router = useRouter();
  const [selectedProgramme, setSelectedProgramme] = useState<string>("all");
  const [stats, setStats] = useState<AttendanceStatsResponse | null>(null);
  const [programmes, setProgrammes] = useState<Array<{ id: string; name: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [programmesLoading, setProgrammesLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch real attendance statistics
  const fetchStats = async (programmeId?: string) => {
    setLoading(true);
    try {
      const { data, error } = await getAttendanceStatistics(programmeId === "all" ? undefined : programmeId);
      if (error) {
        console.error('Failed to fetch attendance stats:', error);
        setError(error);
      } else if (data) {
        setStats(data);
        setError(null);
      }
    } catch (error) {
      console.error('Failed to fetch attendance stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch programmes list
  const fetchProgrammes = async () => {
    setProgrammesLoading(true);
    try {
      const { data, error } = await getProgrammesWithAttendance();
      if (error) {
        console.error('Failed to fetch programmes:', error);
      } else if (data) {
        setProgrammes(data);
      }
    } catch (error) {
      console.error('Failed to fetch programmes:', error);
    } finally {
      setProgrammesLoading(false);
    }
  };

  useEffect(() => {
    fetchProgrammes();
    fetchStats();
  }, []);

  useEffect(() => {
    fetchStats(selectedProgramme === "all" ? undefined : selectedProgramme);
  }, [selectedProgramme]);

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Reports", href: "/reports" },
          { title: "Attendance", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Attendance Reports</h1>
            <p className="text-muted-foreground">
              Comprehensive attendance analytics and certificate eligibility tracking
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Live Data</span>
          </div>
        </div>

        {/* Programme Filter */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter by Programme</CardTitle>
            <CardDescription>
              Select a specific programme to view detailed attendance reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1 max-w-md">
                <Select value={selectedProgramme} onValueChange={setSelectedProgramme}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Programmes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Programmes</SelectItem>
                    {programmesLoading ? (
                      <SelectItem value="loading" disabled>
                        Loading programmes...
                      </SelectItem>
                    ) : (
                      programmes.map((programme) => (
                        <SelectItem key={programme.id} value={programme.id}>
                          {programme.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <Button 
                variant="outline" 
                onClick={() => fetchStats(selectedProgramme === "all" ? undefined : selectedProgramme)}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh Data'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <p className="text-red-600">Error loading attendance data: {error}</p>
            </CardContent>
          </Card>
        )}

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Participants</p>
                  <p className="text-2xl font-bold">{loading ? "-" : stats?.total_participants || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-green-50 rounded-lg">
                  <UserCheck className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Currently Present</p>
                  <p className="text-2xl font-bold">{loading ? "-" : stats?.checked_in_count || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-orange-50 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Attendance Rate</p>
                  <p className="text-2xl font-bold">{loading ? "-" : `${stats?.attendance_rate || 0}%`}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg. Attendance</p>
                  <p className="text-2xl font-bold">{loading ? "-" : `${stats?.average_attendance_percentage || 0}%`}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-yellow-50 rounded-lg">
                  <Award className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Certificate Eligible</p>
                  <p className="text-2xl font-bold">{loading ? "-" : stats?.certificate_eligible_count || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reports Tabs */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance Reports</CardTitle>
            <CardDescription>
              Detailed analytics and reporting for attendance tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="summary" className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Summary</span>
                </TabsTrigger>
                <TabsTrigger value="certificates" className="flex items-center space-x-2">
                  <Award className="h-4 w-4" />
                  <span>Certificates</span>
                </TabsTrigger>
                <TabsTrigger value="daily" className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>Daily Reports</span>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="summary" className="mt-6">
                <AttendanceSummaryReport 
                  programmeId={selectedProgramme === "all" ? undefined : selectedProgramme}
                />
              </TabsContent>
              
              <TabsContent value="certificates" className="mt-6">
                <CertificateEligibilityReport 
                  programmeId={selectedProgramme === "all" ? undefined : selectedProgramme}
                />
              </TabsContent>
              
              <TabsContent value="daily" className="mt-6">
                <DailyAttendanceReport 
                  programmeId={selectedProgramme === "all" ? undefined : selectedProgramme}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Report Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Data Freshness</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Real-time attendance tracking</li>
                  <li>• Updated every 5 minutes for analytics</li>
                  <li>• Historical data available for trend analysis</li>
                  <li>• Certificate eligibility checked automatically</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Export Options</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Excel export with attendance details</li>
                  <li>• CSV format for external analysis</li>
                  <li>• PDF reports for presentations</li>
                  <li>• Certificate generation and bulk download</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}