"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { ParticipantsTableWithAttendance } from "@/components/programmes/participants-table-with-attendance";
import { BulkCheckInModal } from "@/components/attendance/bulk-checkin-modal";
import { AttendanceExportDialog } from "@/components/attendance/attendance-export-dialog";
import { ParticipantAttendanceHistory } from "@/components/attendance/participant-attendance-history-simple";
import { useAttendance } from "@/hooks/attendance/use-attendance-simple";
import { useBulkCheckIn } from "@/hooks/attendance/use-bulk-checkin-simple";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  User<PERSON>he<PERSON>,
  User<PERSON>,
  Clock,
  Award,
  Download,
  Setting<PERSON>,
  Refresh<PERSON><PERSON>,
  <PERSON>Lef<PERSON>
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { sendParticipantCertificate } from '@/services/programmes/participant-server.service';
import { ProgrammeDetails } from '@/types/programmes/programme.types';
import { getSpecificProgramme } from '@/services/programmes/events-client.service';

interface AttendanceStatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  variant?: "default" | "success" | "warning" | "destructive";
}

function AttendanceStatsCard({ title, value, icon, description, variant = "default" }: AttendanceStatsCardProps) {
  const getCardClass = () => {
    switch (variant) {
      case "success":
        return "border-green-200 bg-green-50";
      case "warning":
        return "border-yellow-200 bg-yellow-50";
      case "destructive":
        return "border-red-200 bg-red-50";
      default:
        return "";
    }
  };

  return (
    <Card className={getCardClass()}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  );
}

export default function ProgrammeParticipantsPage() {
  const params = useParams();
  const router = useRouter();
  const programmeId = params.id as string;

  const [programmeDetails, setProgrammeDetails] = useState<ProgrammeDetails | null>(null);
  const [showBulkCheckIn, setShowBulkCheckIn] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [selectedParticipantId, setSelectedParticipantId] = useState<string | null>(null);
  const [showAttendanceHistory, setShowAttendanceHistory] = useState(false);
  const [isParticipantCertificateVisible, setIsParticipantCertificateVisible] = useState(false);

  // Attendance data and actions
  const {
    participants,
    attendanceSummary,
    attendanceConfig,
    isLoading,
    error,
    setError,
    refreshAll,
    quickCheckIn,
    isCheckingIn,
    isRealTimeConnected
  } = useAttendance({
    programmeId,
    enableRealtime: true
  });

  useEffect(() => {
    const fetchProgramme = async () => {

      const result = await getSpecificProgramme(programmeId);

      if (result) {
        setProgrammeDetails(result);

        if (result?.programme?.status === 'COMPLETED' && result.programme.certificate_template) {

          setIsParticipantCertificateVisible(true);
        } else {
          setIsParticipantCertificateVisible(false);
        }
      } else {
        setError(new Error('Failed to fetch programme'));
      }
    };

    if (programmeId) {
      fetchProgramme();
    }
  }, [programmeId]);

  // Bulk check-in functionality
  const {
    selectedParticipants,
    performBulkCheckInSelected,
    checkInAllAbsent,
    getSelectionSummary,
    isLoading: isBulkCheckingIn
  } = useBulkCheckIn({ programmeId });

  // Handle participant quick check-in
  const handleQuickCheckIn = async (participantId: string) => {
    try {
      await quickCheckIn(participantId);
    } catch (error) {
      console.error('Quick check-in failed:', error);
    }
  };

  // Handle view attendance history
  const handleViewHistory = (participantId: string) => {
    setSelectedParticipantId(participantId);
    setShowAttendanceHistory(true);
  };

  // Handle bulk actions
  const handleBulkCheckIn = () => {
    if (selectedParticipants.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select participants to check in",
        variant: "destructive"
      });
      return;
    }
    setShowBulkCheckIn(true);
  };

  const handleCheckInAllAbsent = async () => {
    try {
      await checkInAllAbsent(participants);
    } catch (error) {
      console.error('Bulk check-in all absent failed:', error);
    }
  };

  // Calculate additional stats
  const stats = {
    total: attendanceSummary?.total || 0,
    present: attendanceSummary?.present || 0,
    absent: attendanceSummary?.absent || 0,
    late: attendanceSummary?.late || 0,
    percentage: attendanceSummary?.percentage || 0,
    certificateEligible: participants.filter((p: any) => p.certificate_eligible).length
  };

  const selectionSummary = getSelectionSummary(participants);

  if (error) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Programmes", href: "/programmes" },
            { title: "Programme Details", href: `/programmes/${programmeId}` },
            { title: "Participants", isCurrentPage: true },
          ],
        }}
      >
        <div className="p-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-red-600">
                Error loading attendance data: {error.message}
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Programmes", href: "/programmes" },
          { title: "Programme Details", href: `/programmes/${programmeId}` },
          { title: "Participants", isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Programme Participants</h1>
            <p className="text-muted-foreground">
              Manage attendance and track participant engagement
            </p>
          </div>

          <div className="flex items-center gap-2">
            {isRealTimeConnected && (
              <Badge variant="outline" className="text-green-600 border-green-200">
                Live Updates
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/programmes/${programmeId}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Programme
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/programmes/${programmeId}/attendance-settings`)}
            >
              <Settings className="h-4 w-4 mr-1" />
              Attendance Settings
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshAll}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Attendance Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <AttendanceStatsCard
            title="Total Participants"
            value={stats.total}
            icon={<Users className="h-4 w-4 text-blue-600" />}
            description="Registered participants"
          />
          <AttendanceStatsCard
            title="Present"
            value={stats.present}
            icon={<UserCheck className="h-4 w-4 text-green-600" />}
            description="Currently checked in"
            variant="success"
          />
          <AttendanceStatsCard
            title="Absent"
            value={stats.absent}
            icon={<UserX className="h-4 w-4 text-red-600" />}
            description="Not checked in"
            variant="destructive"
          />
          <AttendanceStatsCard
            title="Late Arrivals"
            value={stats.late}
            icon={<Clock className="h-4 w-4 text-yellow-600" />}
            description="Checked in late"
            variant="warning"
          />
          <AttendanceStatsCard
            title="Certificate Eligible"
            value={stats.certificateEligible}
            icon={<Award className="h-4 w-4 text-purple-600" />}
            description="Meet attendance requirements"
          />
        </div>

        {/* Overall Attendance Rate */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Overall Attendance Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="text-3xl font-bold text-green-600">
                  {stats.percentage}%
                </div>
                <div className="text-sm text-muted-foreground">
                  {stats.present + stats.late} of {stats.total} participants present
                </div>
              </div>
              <div className="w-64 bg-gray-200 rounded-full h-3">
                <div
                  className="bg-green-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${stats.percentage}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
            <CardDescription>
              Bulk operations and attendance management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={handleBulkCheckIn}
                disabled={selectionSummary.total === 0 || isBulkCheckingIn}
                className="flex items-center gap-2"
              >
                <UserCheck className="h-4 w-4" />
                Check In Selected ({selectionSummary.total})
              </Button>

              <Button
                variant="outline"
                onClick={handleCheckInAllAbsent}
                disabled={stats.absent === 0 || isBulkCheckingIn}
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                Check In All Absent ({stats.absent})
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowExportDialog(true)}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export Report
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Participants Table */}
        <Card>
          <CardContent className="p-0">
            <ParticipantsTableWithAttendance
              programmeId={programmeId}
              participants={participants}
              isLoading={isLoading}
              onQuickCheckIn={handleQuickCheckIn}
              onViewHistory={handleViewHistory}
              isParticipantCertificateVisible={isParticipantCertificateVisible}
              isCheckingIn={isCheckingIn}
              onRefresh={refreshAll}
              attendanceConfig={attendanceConfig}
            />
          </CardContent>
        </Card>

        {/* Bulk Check-In Modal */}
        <BulkCheckInModal
          open={showBulkCheckIn}
          onOpenChange={setShowBulkCheckIn}
          programmeId={programmeId}
          selectedParticipantIds={selectedParticipants}
          participants={participants}
          onSuccess={() => {
            setShowBulkCheckIn(false);
            refreshAll();
          }}
        />

        {/* Export Dialog */}
        <AttendanceExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          programmeId={programmeId}
          participants={participants}
        />

        {/* Attendance History Modal */}
        {selectedParticipantId && (
          <ParticipantAttendanceHistory
            open={showAttendanceHistory}
            onOpenChange={setShowAttendanceHistory}
            participantId={selectedParticipantId}
            programmeId={programmeId}
          />
        )}
      </div>
    </AdminPanelLayout>
  );
}