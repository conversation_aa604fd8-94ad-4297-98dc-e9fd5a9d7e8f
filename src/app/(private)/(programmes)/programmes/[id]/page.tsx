import { Suspense } from 'react';
import { getProgrammeDetailsWithAttendance } from "@/services/programmes/events-server.service";
import ProgrammeDetailsClient from "./programme-details-client";
import { Loader2 } from "lucide-react";

export default async function ProgrammeDetailsPage({ params }: { params: { id: string } }) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    }>
      <ProgrammeDetailsContent programmeId={params.id} />
    </Suspense>
  );
}

async function ProgrammeDetailsContent({ programmeId }: { programmeId: string }) {
  try {
    const data = await getProgrammeDetailsWithAttendance(programmeId);
    return <ProgrammeDetailsClient initialData={data} programmeId={programmeId} />;
  } catch (error) {
    console.error('Error loading programme:', error);
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-2xl font-semibold mb-2">Programme Not Found</h2>
        <p className="text-muted-foreground">The programme you're looking for could not be found.</p>
      </div>
    );
  }
}