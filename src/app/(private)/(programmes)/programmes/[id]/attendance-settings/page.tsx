"use client";

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Settings, 
  Award, 
  Clock, 
  UserCheck,
  AlertCircle,
  Save,
  ArrowLeft,
  Loader2
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { getAttendanceConfig, updateAttendanceConfig } from "@/services/client/attendance-client.service";
import type { AttendanceConfiguration } from "@/types/attendance/attendance.types";

export default function AttendanceSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const programmeId = params.id as string;
  
  // State for settings with defaults that match database schema
  const [settings, setSettings] = useState({
    // Certificate Requirements
    minimum_attendance_percentage: 80,
    certificate_minimum_attendance: 80,
    certificate_minimum_sessions: 0,
    
    // Check-in Rules
    allow_self_checkin: true,
    qr_code_enabled: true,
    qr_code_refresh_interval_minutes: 60,
    require_checkout: false,
    checkin_starts_minutes_before: 30,
    checkin_ends_minutes_after: 30,
    
    // Time Settings
    late_arrival_threshold_minutes: 15,
    early_departure_threshold_minutes: 15
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Load settings from database on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        const response = await getAttendanceConfig(programmeId);
        
        if (response.success && response.data) {
          // Map all database fields to UI state
          setSettings({
            minimum_attendance_percentage: response.data.minimum_attendance_percentage || 80,
            certificate_minimum_attendance: response.data.certificate_minimum_attendance || 80,
            certificate_minimum_sessions: response.data.certificate_minimum_sessions || 0,
            allow_self_checkin: response.data.allow_self_checkin,
            qr_code_enabled: response.data.qr_code_enabled,
            qr_code_refresh_interval_minutes: response.data.qr_code_refresh_interval_minutes || 60,
            require_checkout: response.data.require_checkout || false,
            checkin_starts_minutes_before: response.data.checkin_starts_minutes_before || 30,
            checkin_ends_minutes_after: response.data.checkin_ends_minutes_after || 30,
            late_arrival_threshold_minutes: response.data.late_arrival_threshold_minutes || 15,
            early_departure_threshold_minutes: response.data.early_departure_threshold_minutes || 15
          });
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
        toast({
          title: "Error",
          description: "Failed to load attendance settings.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [programmeId]);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Save all configuration fields
      const configUpdate = {
        minimum_attendance_percentage: settings.minimum_attendance_percentage,
        certificate_minimum_attendance: settings.certificate_minimum_attendance,
        certificate_minimum_sessions: settings.certificate_minimum_sessions || undefined,
        allow_self_checkin: settings.allow_self_checkin,
        qr_code_enabled: settings.qr_code_enabled,
        qr_code_refresh_interval_minutes: settings.qr_code_refresh_interval_minutes,
        require_checkout: settings.require_checkout,
        checkin_starts_minutes_before: settings.checkin_starts_minutes_before,
        checkin_ends_minutes_after: settings.checkin_ends_minutes_after,
        late_arrival_threshold_minutes: settings.late_arrival_threshold_minutes,
        early_departure_threshold_minutes: settings.early_departure_threshold_minutes
      };

      const response = await updateAttendanceConfig(programmeId, configUpdate);
      
      if (response.success) {
        toast({
          title: "Settings Saved",
          description: "Attendance settings have been updated successfully.",
        });
      } else {
        throw new Error(response.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Save error:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save settings. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/programmes/${programmeId}`);
  };

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Programmes", href: "/programmes" },
          { title: "Programme Details", href: `/programmes/${programmeId}` },
          { title: "Attendance Settings", isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4 space-y-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Settings className="h-6 w-6" />
              Attendance Settings
            </h1>
            <p className="text-muted-foreground mt-1">
              Configure attendance tracking and certificate requirements
            </p>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <>

        {/* Certificate Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Certificate Requirements
            </CardTitle>
            <CardDescription>
              Set minimum attendance thresholds for certificate eligibility
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="attendance-threshold">Minimum Attendance Percentage</Label>
                <div className="flex items-center gap-4">
                  <Slider
                    id="attendance-threshold"
                    min={0}
                    max={100}
                    step={5}
                    value={[settings.certificate_minimum_attendance]}
                    onValueChange={(value) => {
                      setSettings({ 
                        ...settings, 
                        certificate_minimum_attendance: value[0],
                        minimum_attendance_percentage: value[0] // Keep both in sync
                      });
                    }}
                    className="flex-1"
                  />
                  <Badge variant="secondary" className="min-w-[60px] text-center">
                    {settings.certificate_minimum_attendance}%
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Participants must attend at least this percentage of sessions to be eligible for a certificate
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="min-sessions">Minimum Sessions Required</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="min-sessions"
                    type="number"
                    min="0"
                    value={settings.certificate_minimum_sessions}
                    onChange={(e) => 
                      setSettings({ ...settings, certificate_minimum_sessions: parseInt(e.target.value) || 0 })
                    }
                    className="w-24"
                  />
                  <p className="text-sm text-muted-foreground">
                    Leave as 0 to use percentage only
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Check-in Rules */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              Check-in Rules
            </CardTitle>
            <CardDescription>
              Configure how participants can check in to sessions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Allow Self Check-in</Label>
                  <p className="text-sm text-muted-foreground">
                    Participants can check themselves in to sessions
                  </p>
                </div>
                <Switch
                  checked={settings.allow_self_checkin}
                  onCheckedChange={(checked) => 
                    setSettings({ ...settings, allow_self_checkin: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>QR Code Check-in</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable check-in via QR code scanning
                  </p>
                </div>
                <Switch
                  checked={settings.qr_code_enabled}
                  onCheckedChange={(checked) => 
                    setSettings({ ...settings, qr_code_enabled: checked })
                  }
                />
              </div>

              {settings.qr_code_enabled && (
                <div className="space-y-2 pl-6 border-l-2 border-muted">
                  <Label htmlFor="qr-refresh">QR Code Refresh Interval (minutes)</Label>
                  <div className="flex items-center gap-4">
                    <Input
                      id="qr-refresh"
                      type="number"
                      min="5"
                      max="120"
                      value={settings.qr_code_refresh_interval_minutes}
                      onChange={(e) => 
                        setSettings({ ...settings, qr_code_refresh_interval_minutes: parseInt(e.target.value) || 60 })
                      }
                      className="w-24"
                    />
                    <p className="text-sm text-muted-foreground">
                      How often QR codes regenerate for security
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Require Check-out</Label>
                  <p className="text-sm text-muted-foreground">
                    Participants must check out when leaving
                  </p>
                </div>
                <Switch
                  checked={settings.require_checkout}
                  onCheckedChange={(checked) => 
                    setSettings({ ...settings, require_checkout: checked })
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Time Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Time Settings
            </CardTitle>
            <CardDescription>
              Configure check-in time windows and late arrival thresholds
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="checkin-before">Check-in Opens (minutes before)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="checkin-before"
                      type="number"
                      min="0"
                      max="120"
                      value={settings.checkin_starts_minutes_before}
                      onChange={(e) => 
                        setSettings({ ...settings, checkin_starts_minutes_before: parseInt(e.target.value) || 30 })
                      }
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">minutes</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    How early participants can check in
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="checkin-after">Check-in Closes (minutes after)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="checkin-after"
                      type="number"
                      min="0"
                      max="120"
                      value={settings.checkin_ends_minutes_after}
                      onChange={(e) => 
                        setSettings({ ...settings, checkin_ends_minutes_after: parseInt(e.target.value) || 30 })
                      }
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">minutes</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    How late participants can check in
                  </p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="late-threshold">Late Arrival Threshold</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="late-threshold"
                      type="number"
                      min="0"
                      max="60"
                      value={settings.late_arrival_threshold_minutes}
                      onChange={(e) => 
                        setSettings({ ...settings, late_arrival_threshold_minutes: parseInt(e.target.value) || 15 })
                      }
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">minutes</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Mark as "late" if arrived after this time
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="early-departure">Early Departure Threshold</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="early-departure"
                      type="number"
                      min="0"
                      max="60"
                      value={settings.early_departure_threshold_minutes}
                      onChange={(e) => 
                        setSettings({ ...settings, early_departure_threshold_minutes: parseInt(e.target.value) || 15 })
                      }
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">minutes</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Mark as "partial" if left this early before end
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Time settings affect how attendance status is calculated automatically based on check-in/out times.
              </p>
            </div>
          </CardContent>
        </Card>


        {/* Save Button */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <AlertCircle className="h-4 w-4" />
            Changes will apply to all future check-ins
          </div>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
        </div>
        
        </>
        )}
      </div>
    </AdminPanelLayout>
  );
}