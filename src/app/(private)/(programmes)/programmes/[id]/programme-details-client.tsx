"use client";

import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Calendar, 
  Users, 
  Clock,
  MapPin,
  DollarSign,
  Edit,
  Archive,
  MoreVertical,
  UserCheck,
  Award,
  Building
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AttendanceSettingsForm } from "@/components/attendance/attendance-settings-form";
import { getAttendanceConfig, updateAttendanceConfig } from "@/services/client/attendance-client.service";
import type { AttendanceConfiguration } from "@/types/attendance/attendance.types";
import type { AttendanceConfigForm } from "@/schemas/attendance/attendance-config.schema";
import { ProgrammeRegistrationListComponent } from "@/components/programmes/edit-programme/registrations";

interface ProgrammeDetailsClientProps {
  initialData: {
    programme: any;
    attendanceStats: {
      average: number;
      lastSession: number | null;
      certificateEligible: number;
      totalSessions: number;
      completedSessions: number;
    };
    personInCharge: {
      organizingDept: any;
      staffInCharge: any;
      committee: any;
      secondaryCommittee: any;
    };
    schedules: any[];
  };
  programmeId: string;
}

export default function ProgrammeDetailsClient({ initialData, programmeId }: ProgrammeDetailsClientProps) {
  const router = useRouter();
  const { programme, attendanceStats, personInCharge } = initialData;
  
  const [attendanceConfig, setAttendanceConfig] = useState<AttendanceConfiguration | undefined>();
  const [configLoading, setConfigLoading] = useState(true);

  // Load attendance configuration
  useEffect(() => {
    const loadAttendanceConfig = async () => {
      setConfigLoading(true);
      const response = await getAttendanceConfig(programmeId);
      if (response.success && response.data) {
        setAttendanceConfig(response.data);
      }
      setConfigLoading(false);
    };
    loadAttendanceConfig();
  }, [programmeId]);

  const handleEdit = () => {
    router.push(`/programmes/${programmeId}/edit`);
  };

  const handleArchive = () => {
    // Handle archive logic
    console.log('Archive programme:', programmeId);
  };

  const handleManageParticipants = () => {
    router.push(`/programmes/${programmeId}/participants`);
  };

  const handleSaveAttendanceConfig = async (config: AttendanceConfigForm) => {
    const response = await updateAttendanceConfig(programmeId, config);
    if (response.success && response.data) {
      setAttendanceConfig(response.data);
    }
  };

  // Format dates
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'TBD';
    return new Date(dateString).toLocaleDateString();
  };

  // Get programme dates from schedules
  const programmeStartDate = programme.schedules && programme.schedules.length > 0
    ? programme.schedules.reduce((earliest: any, schedule: any) => {
        if (!schedule.date) return earliest;
        return !earliest || new Date(schedule.date) < new Date(earliest) ? schedule.date : earliest;
      }, null)
    : null;

  const programmeEndDate = programme.schedules && programme.schedules.length > 0
    ? programme.schedules.reduce((latest: any, schedule: any) => {
        if (!schedule.date) return latest;
        return !latest || new Date(schedule.date) > new Date(latest) ? schedule.date : latest;
      }, null)
    : null;

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Programmes", href: "/programmes" },
          { title: programme.name || 'Programme Details', isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4 space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">{programme.name}</h1>
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1 text-sm text-muted-foreground">
              <span>Code: {programme.programme_code || 'N/A'}</span>
              <span className="hidden sm:inline">•</span>
              <span>ID: {programmeId}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={programme.status === 'active' || programme.status === 'ACTIVE' ? 'default' : 'secondary'}>
              {programme.status || 'Active'}
            </Badge>
            
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleArchive}>
                  <Archive className="h-4 w-4 mr-2" />
                  Archive Programme
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Enrollment</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{programme.enrolled}/{programme.capacity}</div>
              <p className="text-xs text-muted-foreground">
                {programme.capacity > 0 ? Math.round((programme.enrolled / programme.capacity) * 100) : 0}% full
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{attendanceStats.average.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {attendanceStats.lastSession !== null 
                  ? `Last session: ${attendanceStats.lastSession.toFixed(1)}%`
                  : 'No session data yet'
                }
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sessions</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {attendanceStats.completedSessions}/{attendanceStats.totalSessions}
              </div>
              <p className="text-xs text-muted-foreground">
                {attendanceStats.totalSessions - attendanceStats.completedSessions} remaining
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Certificate Eligible</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{attendanceStats.certificateEligible}</div>
              <p className="text-xs text-muted-foreground">
                {programme.attendance_threshold || 80}% threshold
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
            <TabsTrigger value="registrations">Registrations</TabsTrigger>
            <TabsTrigger value="participants">Participants</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Programme Details</CardTitle>
                <CardDescription>
                  Basic information about the programme
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Description</p>
                    <p className="text-sm">{programme.description || 'No description available'}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Start Date</p>
                      <p className="text-sm flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {formatDate(programmeStartDate)}
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">End Date</p>
                      <p className="text-sm flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {formatDate(programmeEndDate)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Venue</p>
                    <p className="text-sm flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {programme.venue}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Format</p>
                    <p className="text-sm">
                      {programme.format || 'Standard'}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Type</p>
                    <p className="text-sm">
                      {programme.type || 'General'}
                    </p>
                  </div>
                </div>

                {/* Person in Charge Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Organizing Department</p>
                    <p className="text-sm flex items-center">
                      <Building className="h-4 w-4 mr-1" />
                      {personInCharge.organizingDept?.name || 'Not assigned'}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Staff in Charge</p>
                    <p className="text-sm">
                      {personInCharge.staffInCharge?.name || 'Not assigned'}
                    </p>
                  </div>
                  
                  {personInCharge.committee && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Committee</p>
                      <p className="text-sm">
                        {personInCharge.committee.name}
                      </p>
                    </div>
                  )}
                  
                  {personInCharge.secondaryCommittee && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Secondary Committee</p>
                      <p className="text-sm">
                        {personInCharge.secondaryCommittee.name}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Attendance Settings */}
            <AttendanceSettingsForm
              programmeId={programmeId}
              config={attendanceConfig}
              loading={configLoading}
              onSave={handleSaveAttendanceConfig}
            />
          </TabsContent>

          <TabsContent value="schedule">
            <Card>
              <CardHeader>
                <CardTitle>Programme Schedule</CardTitle>
                <CardDescription>
                  View session schedules and timings
                </CardDescription>
              </CardHeader>
              <CardContent>
                {programme.schedules && programme.schedules.length > 0 ? (
                  <div className="space-y-4">
                    {programme.schedules
                      .sort((a: any, b: any) => {
                        if (!a.date || !b.date) return 0;
                        return new Date(a.date).getTime() - new Date(b.date).getTime();
                      })
                      .map((schedule: any, index: number) => (
                        <div key={schedule.id || index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center gap-4">
                            <div className="flex flex-col items-center justify-center w-12 h-12 rounded-lg bg-primary/10">
                              <span className="text-xs font-medium text-primary">Day</span>
                              <span className="text-lg font-bold text-primary">{schedule.day_number || index + 1}</span>
                            </div>
                            <div>
                              <p className="font-medium">
                                {schedule.date ? formatDate(schedule.date) : 'Date TBD'}
                              </p>
                              <p className="text-sm text-muted-foreground flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {schedule.start_time || 'TBD'} - {schedule.end_time || 'TBD'}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 space-y-4">
                    <Calendar className="h-12 w-12 text-muted-foreground" />
                    <p className="text-center text-muted-foreground">
                      No schedule information available yet.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="registrations">
            <ProgrammeRegistrationListComponent 
              programmeId={programmeId} 
              programmeName={programme.name}
              programmeCode={programme.programme_code}
            />
          </TabsContent>

          <TabsContent value="participants">
            <Card>
              <CardHeader>
                <CardTitle>Participant Management</CardTitle>
                <CardDescription>
                  Manage programme participants and track attendance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center py-8 space-y-4">
                  <Users className="h-12 w-12 text-muted-foreground" />
                  <p className="text-center text-muted-foreground">
                    View and manage all programme participants, track attendance, and monitor progress.
                  </p>
                  <Button onClick={handleManageParticipants}>
                    <Users className="h-4 w-4 mr-2" />
                    Manage Participants
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminPanelLayout>
  );
}