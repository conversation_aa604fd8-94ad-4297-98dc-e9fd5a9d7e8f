"use client"

import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import AddProgramme from "@/components/programmes/add-programme";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";

export default function AddProgrammePage() {
  const breadcrumbs = {
    items: [
      {
        title: "Programmes",
        href: "/programmes",
      },
      {
        title: "Add Programme",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <h1 className="text-2xl font-semibold mb-4">Add Programme</h1>
        <AddProgramme />
      </div>
    </AdminPanelLayout>
  );
}