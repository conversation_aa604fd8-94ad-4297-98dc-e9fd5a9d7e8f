"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { ArchivedProgrammesTable } from "@/components/table/archived-programmes-table";

export default function ArchivedProgrammesPage() {
    const breadcrumbs = {
        items: [
            {
                title: "Archived Programmes",
                isCurrentPage: true,
            },
        ],
    };

    return (
        <AdminPanelLayout breadcrumbs={breadcrumbs}>
            <div className="p-4">
                <ArchivedProgrammesTable />
            </div>
        </AdminPanelLayout>
    );
}