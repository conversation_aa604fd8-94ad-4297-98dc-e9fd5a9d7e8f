"use client"

import { useRef } from "react";
import BudgetDetailForm from "@/components/budgets/budget-details";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { BudgetChildRef } from "@/types/budgets/budgets.types";

export default function AddBudget() {
    const breadcrumbs = {
        items: [
            {
                title: "Budgets",
                href: "/budgets",
            },
            {
                title: "Add Budget",
                isCurrentPage: true,
            },
        ],
    };
    const childRef = useRef<BudgetChildRef | null>(null)

    const onSubmit = async () => {
        if (childRef.current?.detailsubmit) {
            await childRef.current.detailsubmit(true);
        }
    }

    return (
        <AdminPanelLayout breadcrumbs={breadcrumbs}>
            <div className="p-4">
                <h1 className="text-2xl font-semibold mb-4">Create Budget</h1>

                <Card className="mt-4 p-8">
                    <BudgetDetailForm ref={childRef} createState={true} />
                </Card>

                <div className="mt-4 flex justify-end">
                    <Dialog>
                        <DialogTrigger asChild>
                            <Button>Create Budget</Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Are you absolutely sure?</DialogTitle>
                                <DialogDescription>
                                    Once the budget is created, the type and format fields can no longer be edited.
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                                <DialogClose asChild>
                                    <Button type="button" variant="secondary">
                                        Close
                                    </Button>
                                </DialogClose>
                                <Button type="button" onClick={() => onSubmit()}>Confirm</Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>

            </div>
        </AdminPanelLayout>
    );
}