"use client";

import { useEffect, useState } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Badge } from "@/components/ui/badge";
import { useRouter, useSearchParams } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { ComplexBudgetView } from "@/components/budgets/complex-view";
import { EventBudgetView } from "@/components/budgets/simple-view";
import { BudgetReviewHistoryComponent } from "@/components/budgets/reviewHistory";
import { BudgetExportButton } from "@/components/budgets/BudgetExportButton";
import type { BudgetRevisions, BudgetReviewComplexProps, BudgetReviewSimpleEventProps } from "@/types/budgets/budgets.types";
import type { approvalActionHistory } from "@/types/approvals/approval.types";
import { getSimpleBudgetData, getComplexBudgetData, getBudgetDetails, useVerifyPageDestinationOnBudget, getAllBudgetVersion, getBudgetReviewHistory, checkReviseBudgetPermission, getLatestBudgetId } from "@/services/budgets/budget.service";
import { setBudgetArchived } from "@/services/budgets/budget-server.service";
import { useVerifyAccessToBudgetReview } from "@/services/budgets/budget.service";
import { processReviseBudget } from "@/services/budgets/budget-server.service";

export default function BudgetReviewPage() {
    const breadcrumbs = {
        items: [
            {
                title: "Budgets",
                href: "/budgets",
            },
            {
                title: "Budget Review",
                isCurrentPage: true,
            },
        ],
    };
    const { toast } = useToast();
    const searchParams = useSearchParams();
    const router = useRouter();
    const id = searchParams.get('id');
    const verifyAccessToBudgetReview = useVerifyAccessToBudgetReview();
    const verifyPageDestination = useVerifyPageDestinationOnBudget();
    const [isInitialLoading, setIsInitialLoading] = useState(true);
    const [isRevising, setIsRevising] = useState(false);
    const [canRevise, setCanRevise] = useState(false);
    const [isArchiving, setArchiving] = useState(false);

    const [budgetData, setBudgetData] = useState<BudgetRevisions | null>(null);
    const [selectedBudgetData, setSelectedBudgetData] = useState<BudgetRevisions | null>(null);
    const [complexItemData, setComplexItemData] = useState<BudgetReviewComplexProps | null>(null);
    const [simpleItemData, setSimpleItemData] = useState<BudgetReviewSimpleEventProps | null>(null);
    const [selectedComplexItemData, setSelectedComplexItemData] = useState<BudgetReviewComplexProps | null>(null);
    const [selectedSimpleItemData, setSelectedSimpleItemData] = useState<BudgetReviewSimpleEventProps | null>(null);
    const [selectedBudgetVersion, setSelectedBudgetVersion] = useState<string>();
    const [budgetVersion, setBudgetVersion] = useState<{ id: string, version: number, status: string }[]>([]);
    const [reviewHistory, setReviewHistory] = useState<approvalActionHistory[] | null>(null);

    useEffect(() => {
        const verifyAccess = async () => {
            setIsInitialLoading(true);
            if (id) {
                await verifyAccessToBudgetReview(id);
                loadBudget();
            }
        }

        verifyAccess();
    }, [])

    useEffect(() => {
        if (budgetData?.format) {
            loadBudgetDetails();
            setIsInitialLoading(false);
        }
    }, [budgetData])

    useEffect(() => {
        if (selectedBudgetVersion && selectedBudgetVersion !== id) {
            // Update URL with new budget ID
            router.push(`/budgets/budget-review?id=${selectedBudgetVersion}`);
            loadBudget(selectedBudgetVersion);
        }
    }, [selectedBudgetVersion])

    const loadBudget = async (selectedBudgetId?: string) => {
        const budgetIdToUse = selectedBudgetId ?? id;

        if (!budgetIdToUse) {
            toast({
                title: "Error",
                description: "Budget ID is required",
                variant: "destructive",
            });
            return;
        }

        // Handle the case where selectedBudgetId is not given or equals current budgetData
        const isMainBudget = !selectedBudgetId || selectedBudgetId === budgetData?.id;

        if (isMainBudget && budgetData) {
            setSelectedBudgetData(budgetData);
            setSelectedComplexItemData(complexItemData);
            setSelectedSimpleItemData(simpleItemData);
            return;
        }

        // Fetch selected budget details
        const details = await getBudgetDetails(budgetIdToUse);

        if (!details) {
            toast({
                title: "Error",
                description: "Budget not found",
                variant: "destructive",
            });
            return;
        }

        // If this is the main load (initial), also set the main state
        if (!selectedBudgetId) {
            setBudgetData(details);
            setSelectedBudgetVersion(details.id);
        }

        setSelectedBudgetData(details);

        const [complexResult, simpleResult] = await Promise.all([
            details.format === "COMPLEX" ? getComplexBudgetData(details.id) : Promise.resolve(null),
            details.format !== "COMPLEX" ? getSimpleBudgetData(details.id) : Promise.resolve(null),
        ]);

        if (details.format === "COMPLEX" && complexResult) {
            if (!selectedBudgetId) setComplexItemData(complexResult);
            setSelectedComplexItemData(complexResult);
        } else if (simpleResult) {
            if (!selectedBudgetId) setSimpleItemData(simpleResult);
            setSelectedSimpleItemData(simpleResult);
        }
    }

    const loadBudgetDetails = async () => {
        const idToUse = budgetData?.parent_id ? budgetData?.parent_id : id;
        if (!idToUse) {
            toast({
                title: "Error",
                description: "Budget ID is required",
                variant: "destructive",
            });
            return;
        }

        const [versionResult, historyResult, revisePermission] = await Promise.all([
            getAllBudgetVersion(idToUse, undefined),
            getBudgetReviewHistory(idToUse, undefined),
            checkReviseBudgetPermission(idToUse)
        ])

        if (versionResult && historyResult) {
            setBudgetVersion(versionResult);
            setReviewHistory(historyResult);
            setCanRevise(revisePermission);
        } else {
            toast({
                title: "Error",
                description: "Failed to fetch the budget version or history",
                variant: "destructive",
            });
        }
    }

    const onReviseBudget = async () => {
        if (!budgetData || !id) {
            toast({
                title: "Error",
                description: "Budget ID is required",
                variant: "destructive",
            });
            return;
        }

        let redirectId;

        if (canRevise) {
            setIsRevising(true);
            const result = await processReviseBudget(budgetData);

            if (!result.success && !result.responseId) {
                toast({
                    title: "Error",
                    description: result.message,
                    variant: "destructive"
                });
                setIsRevising(false);
                return;
            }

            redirectId = result.responseId;
        } else {
            const idToPass = budgetData.parent_id ? budgetData.parent_id : id
            const result = await getLatestBudgetId(undefined, idToPass);

            if (!result) {
                toast({
                    title: "Error",
                    description: "No budget Id found, please try again later.",
                    variant: "destructive"
                });
                return;
            }

            redirectId = result;
        }

        if (redirectId) {
            verifyPageDestination(redirectId);
        }
    }

    const handleArchiveBudget = async () => {
        const parentId = budgetData?.parent_id ?? budgetData?.id

        if (!parentId) {
            toast({
                title: "Error",
                description: "Budget ID is required",
                variant: "destructive",
            })
            return;
        }

        setArchiving(true)
        const result = await setBudgetArchived(parentId)
        setArchiving(false)

        if (!result) {
            toast({
                title: "Error",
                description: "Failed to archive the budget, please try again later.",
                variant: "destructive"
            })
            return
        }

        loadBudget()
    }

    return (
        <AdminPanelLayout breadcrumbs={breadcrumbs}>
            {isInitialLoading ? (
                <LoadingSpinner />
            ) : (
                <div className="p-4">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4 mb-4">
                            <h1 className="text-2xl font-semibold leading-tight">Budget Review</h1>
                            {selectedBudgetData?.is_archived ? (
                                <Badge className="bg-gray-500 hover:bg-gray-400">Archived</Badge>
                            ) : selectedBudgetData?.status === 'PENDING_APPROVAL' ? (
                                <Badge className="bg-yellow-500 hover:bg-yellow-300">Pending Approval</Badge>
                            ) : selectedBudgetData?.status === 'APPROVED' ? (
                                <Badge className="bg-green-500 hover:bg-green-300">Approved</Badge>
                            ) : selectedBudgetData?.status === 'REJECTED' ? (
                                <Badge className="bg-red-500 hover:bg-red-300">Rejected</Badge>
                            ) : null}
                        </div>

                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-4">
                                <div>
                                    <Select
                                        onValueChange={(value) => setSelectedBudgetVersion(value)}
                                        value={selectedBudgetVersion}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a version" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {budgetVersion.map((budget) => {
                                                const isDraft = budget.status === "DRAFT";

                                                return (
                                                    <SelectItem
                                                        key={budget.id}
                                                        value={budget.id}
                                                        disabled={isDraft}
                                                        className={isDraft ? "opacity-50 cursor-not-allowed" : ""}
                                                    >
                                                        v{budget.version}
                                                        {budget.status === 'PENDING_REVIEW' ? (
                                                            <Badge className="ml-2 bg-yellow-500 hover:bg-yellow-300">Pending Approval</Badge>
                                                        ) : budget.status === 'APPROVED' ? (
                                                            <Badge className="ml-2 bg-green-500 hover:bg-green-300">Approved</Badge>
                                                        ) : budget.status === 'REJECTED' ? (
                                                            <Badge className="ml-2 bg-red-500 hover:bg-red-300">Rejected</Badge>
                                                        ) : null}
                                                    </SelectItem>
                                                );
                                            })}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-center gap-2">
                                    {selectedBudgetData && (
                                        <BudgetExportButton
                                            budgetData={selectedBudgetData}
                                            complexItemData={selectedComplexItemData}
                                            simpleItemData={selectedSimpleItemData}
                                        />
                                    )}
                                    {!budgetData?.is_archived && (
                                        <>
                                            {canRevise ? (
                                                <div className="flex justify-between gap-2">
                                                    <Dialog>
                                                        <DialogTrigger asChild>
                                                            <Button type="button" variant="outline">
                                                                <RotateCcw />
                                                                Revise Budget
                                                            </Button>
                                                        </DialogTrigger>
                                                        <DialogContent>
                                                            <DialogHeader>
                                                                <DialogTitle>Start a Budget Revision?</DialogTitle>
                                                                <DialogDescription>
                                                                    This will create a new editable version of the current budget. The original version will remain unchanged.
                                                                    Do you want to proceed?
                                                                </DialogDescription>
                                                            </DialogHeader>
                                                            <DialogFooter>
                                                                <DialogClose asChild>
                                                                    <Button type="button" variant="secondary">
                                                                        Close
                                                                    </Button>
                                                                </DialogClose>
                                                                <Button type="button" onClick={onReviseBudget} disabled={isRevising}>{isRevising ? 'Revising Budget...' : 'Revise Budget'}</Button>
                                                            </DialogFooter>
                                                        </DialogContent>
                                                    </Dialog>

                                                    <Dialog>
                                                        <DialogTrigger asChild>
                                                            <Button type="button" variant="destructive">Archive</Button>
                                                        </DialogTrigger>
                                                        <DialogContent>
                                                            <DialogHeader>
                                                                <DialogTitle>Archive Budget</DialogTitle>
                                                                <DialogDescription>
                                                                    Are you sure you want to archive this budget? This action cannot be undone.
                                                                </DialogDescription>
                                                            </DialogHeader>

                                                            <DialogFooter className="mt-6 flex justify-end space-x-4">
                                                                <DialogClose asChild>
                                                                    <Button variant="outline">Cancel</Button>
                                                                </DialogClose>
                                                                <Button
                                                                    variant="destructive"
                                                                    onClick={handleArchiveBudget}
                                                                    disabled={isArchiving}
                                                                >
                                                                    {isArchiving ? 'Archiving' : 'Archive'}
                                                                </Button>
                                                            </DialogFooter>
                                                        </DialogContent>
                                                    </Dialog>
                                                </div>
                                            ) : (
                                                null
                                            )}
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    <Card className="p-8 mt-4">
                        <h1 className="text-lg font-semibold pb-2 mb-4">Budget Details</h1>
                        <div className="grid grid-cols-2 gap-y-4">
                            <div>
                                <label className="text-sm uppercase font-bold text-gray-400 block">
                                    Code
                                </label>
                                <span className="text-sm font-semibold block">
                                    {selectedBudgetData?.budget_code ? selectedBudgetData.budget_code : '-'}
                                </span>
                            </div>
                            <div>
                                <label className="text-sm uppercase font-bold text-gray-400 block">
                                    Type
                                </label>
                                <span className="text-sm font-semibold block">
                                    {selectedBudgetData?.entity_type &&
                                        selectedBudgetData.entity_type.charAt(0).toUpperCase() + selectedBudgetData.entity_type.slice(1).toLowerCase()}
                                </span>
                            </div>
                            <div>
                                <label className="text-sm uppercase font-bold text-gray-400 block">
                                    Format
                                </label>
                                <span className="text-sm font-semibold block">
                                    {selectedBudgetData?.format &&
                                        selectedBudgetData.format.charAt(0).toUpperCase() + selectedBudgetData.format.slice(1).toLowerCase()}
                                </span>
                            </div>
                            <div>
                                <label className="text-sm uppercase font-bold text-gray-400 block">
                                    Title
                                </label>
                                <span className="text-sm font-semibold block">
                                    {selectedBudgetData?.title}
                                </span>
                            </div>
                            <div>
                                <label className="text-sm uppercase font-bold text-gray-400 block">
                                    Description
                                </label>
                                <span className="text-sm font-semibold block">
                                    {selectedBudgetData?.description}
                                </span>
                            </div>
                        </div>
                    </Card>

                    <Card className="p-8 mt-4">
                        {selectedBudgetData?.format === "COMPLEX" ? (
                            <ComplexBudgetView
                                incomeEntries={selectedComplexItemData?.incomeEntries ?? []}
                                expenditureEntries={selectedComplexItemData?.expenditureEntries ?? []}
                                summary={selectedComplexItemData?.summary || null}
                            />
                        ) : (
                            <EventBudgetView
                                incomeEntries={selectedSimpleItemData?.incomeEntries ?? []}
                                expenditureEntries={selectedSimpleItemData?.expenditureEntries ?? []}
                                summary={selectedSimpleItemData?.summary || null}
                            />
                        )}
                    </Card>

                    <Card className="p-8 mt-4">
                        <h1 className="text-lg font-semibold pb-2 mb-4">Review History</h1>
                        <BudgetReviewHistoryComponent
                            reviewHistory={reviewHistory}
                        />
                    </Card>
                </div>
            )}
        </AdminPanelLayout >
    )
}
