"use client"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { useEffect, useRef, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast"
import { getBudgetDetails, useVerifyAccessToEditBudget } from "@/services/budgets/budget.service"
import ConferenceBudgetTable from "@/components/budgets/complex-budget"
import CourseEventBudgetTable from "@/components/budgets/simple-budget"
import BudgetDetailForm from "@/components/budgets/budget-details"
import type { BudgetChildRef, BudgetRevisions } from "@/types/budgets/budgets.types"
import { deleteBudgetRevisions, submitBudgetForReview } from "@/services/budgets/budget-server.service"

export default function EditBudgetPage() {
    const breadcrumbs = {
        items: [
            {
                title: "Budgets",
                href: "/budgets",
            },
            {
                title: "Edit Budget",
                isCurrentPage: true,
            },
        ],
    }
    const detailFormRef = useRef<BudgetChildRef | null>(null)
    const itemTableRef = useRef<BudgetChildRef | null>(null)
    const searchParams = useSearchParams()
    const id = searchParams.get('id')
    const router = useRouter()
    const [budgetDetail, setBudgetDetail] = useState<BudgetRevisions>()
    const verifyAccessToEditBudget = useVerifyAccessToEditBudget()
    const [isDeleting, setIsDeleting] = useState(false)

    useEffect(() => {
        const verifyAccess = async () => {
            if (id) {
                await verifyAccessToEditBudget(id);
                loadBudgetDetails();
            }
        }

        verifyAccess();
    }, [id])

    const loadBudgetDetails = async () => {
        if (!id) {
            router.push('/budgets')
            return
        }

        const response = await getBudgetDetails(id)

        if (response) {
            setBudgetDetail(response)
        } else {
            toast({ variant: 'destructive', title: 'Error', description: 'Failed to fetch budget data.' })
            router.push('/budgets')
        }
    }

    const onSubmitDetail = async () => {
        if (detailFormRef.current?.detailsubmit) {
            await detailFormRef.current.detailsubmit(true); // Wait for child submission
        }
    }

    const onSubmitItem = async () => {
        if (itemTableRef.current?.itemsubmit) {
            await itemTableRef.current.itemsubmit(true); // Wait for child submission
        }
    }

    const onSubmitReview = async () => {
        if (id) {
            const result = await submitBudgetForReview(id)

            if (result) {
                toast({
                    title: "Success",
                    description: "Budget submitted for review successfully.",
                });

                const params = new URLSearchParams();
                params.set("id", id);
                router.push(`/budgets/budget-review?${params.toString()}`);
            } else {
                toast({
                    variant: 'destructive',
                    title: "Error",
                    description: "An error occurred while submitting programme for review. Please try again.",
                });
            }
        }
    }

    const onDeleteBudget = async () => {
        if (!id) {
            toast({
                variant: 'destructive',
                title: "Error",
                description: "Budget ID is required.",
            });
            return
        }
        setIsDeleting(true)

        const response = await deleteBudgetRevisions(id)

        if (!response) {
            toast({
                variant: 'destructive',
                title: "Error",
                description: "An error occurred while deleting the budget. Please try again.",
            })
        } else {
            toast({
                title: "Success",
                description: "Budget has been successfully deleted.",
            })
            router.push('/budgets')
        }

        setIsDeleting(false)
    }

    return (
        <AdminPanelLayout breadcrumbs={breadcrumbs}>
            <div className="p-4">
                <h1 className="text-2xl font-semibold mb-4">Edit budget</h1>

                {(id && budgetDetail) && (
                    <>
                        <Card className="mt-4 p-8">
                            <h1 className="text-xl font-semibold mb-6">Budget</h1>
                            <BudgetDetailForm
                                ref={detailFormRef}
                                createState={false}
                                submitForReview={onSubmitItem}
                                budgetRevisionId={id}
                                budgetDetails={budgetDetail}
                            />
                        </Card>

                        <Card className="mt-4 p-8">
                            <h1 className="text-xl font-semibold mb-6">Budget Preparation</h1>
                            {budgetDetail.format === "COMPLEX" ? (
                                <ConferenceBudgetTable ref={itemTableRef} budgetRevisionId={id} submitForReview={onSubmitReview} />
                            ) : (
                                <CourseEventBudgetTable ref={itemTableRef} budgetRevisionId={id} submitForReview={onSubmitReview} />
                            )}
                        </Card>

                        <div className="flex justify-between mt-4 space-x-4">
                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button variant="destructive">Delete</Button>
                                </DialogTrigger>

                                <DialogContent>
                                    <DialogTitle>Delete Budget</DialogTitle>
                                    <DialogDescription>
                                        Are you sure you want to delete this budget? This action cannot be undone.
                                    </DialogDescription>

                                    <DialogFooter>
                                        <DialogClose asChild>
                                            <Button type="button" variant="secondary">
                                                Cancel
                                            </Button>
                                        </DialogClose>
                                        <Button variant="destructive" onClick={onDeleteBudget} disabled={isDeleting}>
                                            Delete
                                        </Button>
                                    </DialogFooter>
                                </DialogContent>
                            </Dialog>

                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button>Submit for Review</Button>
                                </DialogTrigger>

                                <DialogContent>
                                    <DialogTitle>Submit Budget for Review</DialogTitle>
                                    <DialogDescription>
                                        Once submitted, you will no longer be able to edit this budget. Do you want to continue?
                                    </DialogDescription>

                                    <DialogFooter>
                                        <DialogClose asChild>
                                            <Button type="button" variant="secondary">
                                                Cancel
                                            </Button>
                                        </DialogClose>
                                        <Button onClick={onSubmitDetail}>Submit</Button>
                                    </DialogFooter>
                                </DialogContent>
                            </Dialog>
                        </div>

                    </>
                )}
            </div>
        </AdminPanelLayout>
    )
}