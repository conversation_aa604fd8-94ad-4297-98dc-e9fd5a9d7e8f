"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { CustomerFormDialog } from "@/components/customer-management/forms/customer-form-dialog";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function AddCustomerPage() {
  const router = useRouter();
  const [dialogOpen, setDialogOpen] = useState(true);

  const handleSuccess = () => {
    router.push("/customers");
  };

  const handleDialogClose = (open: boolean) => {
    if (!open) {
      router.push("/customers");
    }
    setDialogOpen(open);
  };

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Customers", href: "/customers" },
          { title: "Create New", isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
        </div>

        <div className="flex items-center justify-center">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-semibold">Create New Customer</h1>
            <p className="text-muted-foreground">
              Fill in the form to create a new customer record.
            </p>
          </div>
        </div>

        {/* Customer Form Dialog */}
        <CustomerFormDialog
          open={dialogOpen}
          onOpenChange={handleDialogClose}
          onSuccess={handleSuccess}
        />
      </div>
    </AdminPanelLayout>
  );
}