"use client";

import { useState, useEffect } from "react";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { CustomerDetail } from "@/components/customer-management/detail/customer-detail";
import { CustomerFormDialog } from "@/components/customer-management/forms/customer-form-dialog";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, FileText, Mail, Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { getCustomerById, deleteCustomer } from "@/services/customer-management/customers.service";
import type { Customer } from "@/types/customer-management";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function CustomerDetailPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    loadCustomer();
  }, [params.id]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getCustomerById(params.id);
      
      if (data) {
        setCustomer(data);
      } else {
        console.error("Customer not found for ID:", params.id);
        setError(`Customer not found (ID: ${params.id})`);
      }
    } catch (error) {
      console.error("Error loading customer:", error);
      setError(`Failed to load customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
      toast({
        title: "Error",
        description: "Failed to load customer details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    setShowEditDialog(true);
  };

  const handleEditSuccess = () => {
    setShowEditDialog(false);
    loadCustomer(); // Reload customer data
  };

  const handlePrint = () => {
    toast({
      title: "Info",
      description: "Print functionality will be implemented",
    });
  };

  const handleEmail = () => {
    toast({
      title: "Info", 
      description: "Email functionality will be implemented",
    });
  };



  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!customer) return;
    
    setIsDeleting(true);
    try {
      const result = await deleteCustomer(customer.id);
      
      if (result.success) {
        toast({
          title: "Success",
          description: `Customer ${customer.name} has been deleted successfully`,
        });
        router.push("/customers");
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete customer",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (loading) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Customers", href: "/customers" },
            { title: `Loading...`, isCurrentPage: true },
          ],
        }}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </AdminPanelLayout>
    );
  }

  if (error || !customer) {
    return (
      <AdminPanelLayout
        breadcrumbs={{
          items: [
            { title: "Customer Management", href: "/customers" },
            { title: "Customers", href: "/customers" },
            { title: "Error", isCurrentPage: true },
          ],
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/customers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to List
              </Link>
            </Button>
          </div>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Customer Management", href: "/customers" },
          { title: "Customers", href: "/customers" },
          { title: customer.name, isCurrentPage: true },
        ],
      }}
    >
      <div className="space-y-4">
        {/* Header with Back Button and Actions */}
        <div className="flex items-center justify-between">
          <Button variant="outline" size="sm" asChild>
            <Link href="/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <FileText className="mr-2 h-4 w-4" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={handleEmail}>
              <Mail className="mr-2 h-4 w-4" />
              Email
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleDelete}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Customer Detail Component */}
        <CustomerDetail
          customerId={customer.id}
          onEditClick={handleEdit}
        />

        {/* Edit Dialog */}
        <CustomerFormDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          customerId={customer.id}
          onSuccess={handleEditSuccess}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete the customer "{customer.name}" and all associated data. 
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteConfirm}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AdminPanelLayout>
  );
}