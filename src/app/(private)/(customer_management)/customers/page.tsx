"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { Button } from "@/components/ui/button";
import { CustomersTable } from "@/components/table/customers-table";
import { CustomerFormDialog } from "@/components/customer-management/forms/customer-form-dialog";
import { CustomerStatusChangeDialog } from "@/components/customer-management/dialogs/customer-status-change-dialog";
import { PlusCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getCustomerById } from "@/services/customer-management/customers.service";
import type { Customer, CustomerStatus } from "@/types/customer-management";

export default function CustomersPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showStatusChangeDialog, setShowStatusChangeDialog] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | undefined>();
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [newStatus, setNewStatus] = useState<CustomerStatus | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleAddCustomer = () => {
    setSelectedCustomerId(undefined);
    setShowCustomerDialog(true);
  };

  const handleEditCustomer = (customerId: string) => {
    setSelectedCustomerId(customerId);
    setShowCustomerDialog(true);
  };

  const handleViewCustomer = (customerId: string) => {
    router.push(`/customers/${customerId}`);
  };

  const handleCustomerSuccess = () => {
    setRefreshKey(prev => prev + 1); // Trigger table refresh
  };

  const handleStatusChange = async (customerId: string, status: string) => {
    try {
      // Fetch customer details
      const customer = await getCustomerById(customerId);
      if (customer) {
        setSelectedCustomer(customer);
        setNewStatus(status as CustomerStatus);
        setShowStatusChangeDialog(true);
      } else {
        toast({
          title: "Error",
          description: "Failed to load customer details",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while loading customer details",
        variant: "destructive"
      });
    }
  };

  const handleStatusChangeSuccess = () => {
    setRefreshKey(prev => prev + 1); // Trigger table refresh
    setShowStatusChangeDialog(false);
  };

  const handleDeleteCustomer = async (customerId: string) => {
    // Import the delete function
    const { deleteCustomer } = await import("@/services/customer-management/customers.service");
    
    const confirmed = window.confirm("Are you sure you want to delete this customer? This action cannot be undone.");
    
    if (!confirmed) return;
    
    try {
      const result = await deleteCustomer(customerId);
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Customer deleted successfully",
        });
        setRefreshKey(prev => prev + 1); // Trigger table refresh
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete customer",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while deleting the customer",
        variant: "destructive"
      });
    }
  };

  const handleBulkStatusChange = async (customerIds: string[], newStatus: string) => {
    // Import the bulk update function
    const { bulkUpdateCustomerStatus } = await import("@/services/customer-management/customers.service");
    
    const confirmed = window.confirm(`Are you sure you want to change the status of ${customerIds.length} customer(s) to ${newStatus}?`);
    
    if (!confirmed) return;
    
    try {
      const result = await bulkUpdateCustomerStatus(customerIds, newStatus as CustomerStatus);
      
      if (result.success) {
        toast({
          title: "Success",
          description: `Successfully updated status for ${customerIds.length} customer(s)`,
        });
        setRefreshKey(prev => prev + 1); // Trigger table refresh
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update customer status",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while updating customer status",
        variant: "destructive"
      });
    }
  };

  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Your customer list is being exported...",
    });
  };

  return (
    <AdminPanelLayout
      breadcrumbs={{
        items: [
          { title: "Customers", isCurrentPage: true },
        ],
      }}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Customers
            </h1>
            <p className="text-sm text-gray-600">
              Manage and review customers across the system
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleAddCustomer} size="sm">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add New Customer
            </Button>
          </div>
        </div>
        
        <CustomersTable 
          key={refreshKey}
          onEdit={handleEditCustomer}
          onView={handleViewCustomer}
          onDelete={handleDeleteCustomer}
          onStatusChange={handleStatusChange}
          onBulkStatusChange={handleBulkStatusChange}
        />
      </div>

      {/* Customer Form Dialog */}
      <CustomerFormDialog
        open={showCustomerDialog}
        onOpenChange={setShowCustomerDialog}
        customerId={selectedCustomerId}
        onSuccess={handleCustomerSuccess}
      />

      {/* Status Change Dialog */}
      <CustomerStatusChangeDialog
        open={showStatusChangeDialog}
        onOpenChange={setShowStatusChangeDialog}
        customer={selectedCustomer}
        newStatus={newStatus}
        onSuccess={handleStatusChangeSuccess}
      />
    </AdminPanelLayout>
  );
}