"use client";

import AdminPanelLayout from "@/components/layout/admin-panel-layout";
import { MembershipsTable } from "@/components/table/membership-table";
import { Suspense } from "react";

export default function ApplicationsPage() {
  const breadcrumbs = {
    items: [
      {
        title: "Members",
        isCurrentPage: true,
      },
    ],
  };

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <div className="p-4">
        <MembershipsTable />
      </div>
    </AdminPanelLayout>
  );
}

