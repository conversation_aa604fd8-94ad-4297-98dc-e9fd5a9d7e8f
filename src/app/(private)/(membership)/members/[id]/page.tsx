"use client"

import { useEffect, useState } from "react"
import { notFound } from "next/navigation"

import AdminPanelLayout from "@/components/layout/admin-panel-layout"
import { MemberDetails } from "@/components/member-details/index"
import { MemberService } from "@/services/members/member.service"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import type { Member, TabConfig } from "@/types/members/member-details"

interface MemberDetailsPageProps {
  params: {
    id: string
  }
}

export default function MemberDetailsPage({ params }: MemberDetailsPageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [member, setMember] = useState<Member>();
  const [tabConfig, setTabConfig] = useState<TabConfig | null>();

  const generateDynamicTabConfig = (member: Member) => {
    const BASE_TAB_CONFIG: TabConfig = {
      summary: true,
      points: false,
      auditLogs: false,
      activities: false,
      applicationHistory: true,
      profile: false,
      workExperience: false,
      educationDetails: false,
      company: false,
      companyNominees: false,
      awards: false,
      projectDetails: false,
      documents: false,
      criminalRecords: false,
      sponsorship: false,
    };

    if (member.memberships.membershipType.name === "Organisational Member") {
      return {
        ...BASE_TAB_CONFIG,
        company: true,
        companyNominees: true,
        documents: true,
      };
    } else {
      return {
        ...BASE_TAB_CONFIG,
        profile: true,
        employment: true,
        workExperience: true,
        educationDetails: true,
        companyNominees: true,
        awards: true,
        projectDetails: true,
        documents: true,
        criminalRecords: true,
        sponsorship: true,
      };
    }
  }

  useEffect(() => {
    const fetchData = async () => {
      const member = await MemberService.getMemberById(params.id);

      if (!member) {
        notFound();
      }

      const tab_config = generateDynamicTabConfig(member);
      setTabConfig(tab_config);
      setMember(member);
      setIsLoading(false);
    };

    fetchData();
  }, [params.id])

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!member) {
    notFound();
  }

  const breadcrumbs = {
    items: [
      {
        title: "Members",
        href: "/members",
      },
      {
        title: `${member.personalInfo.full_name}`,
        isCurrentPage: true,
      },
    ],
  }

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      {(member && tabConfig) && (
        <MemberDetails member={member} tabConfig={tabConfig} />
      )}
    </AdminPanelLayout>
  )
}