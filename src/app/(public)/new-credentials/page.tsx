"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { resetPassword } from '@/services/auth-server.service';

const formSchema = z
  .object({
    newPassword: z
      .string()
      .min(1, "New Password is required")
      .min(8, "Password must be at least 8 characters long")
      .max(20, "Password cannot exceed 20 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$%^&*])[A-Za-z\d@#$%^&*]{8,20}$/,
        "Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character (@#$%^&*)"
      ),
    confirmNewPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: "Passwords do not match",
    path: ["confirmNewPassword"],
  });

const CredentialsPage = () => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      newPassword: "",
      confirmNewPassword: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    const result = await resetPassword(values.newPassword);

    if (!result.success) {
      setErrorMessage(`Error: ${result.error}`);
      setIsSubmitting(false);
    } else {
      setSuccessMessage("Your password has been updated successfully. You will be redirected to the login page shortly.");

      setTimeout(() => {
        router.push("/login");
      }, 2000);
    }
  }

  return (
    <div className="relative h-screen flex flex-col lg:flex-row items-center justify-center lg:px-0">
      <div className="relative hidden lg:flex h-full flex-col bg-muted p-10 text-white dark:border-r lg:w-1/2">
        <Image
          src="/images/auth-bg.jpg"
          alt="Background"
          layout="fill"
          objectFit="cover"
          className="absolute inset-0"
        />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Image
            src="/images/ies-logo.png"
            alt="IES Logo"
            width={24}
            height={24}
            className="mr-2 h-6 w-6"
          />
          CRM System (Institution of Engineering Singapore)
        </div>
      </div>
      <div className="p-8 flex flex-col justify-center w-full lg:w-1/2">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Reset Password
            </h1>
            <p className="text-sm text-muted-foreground">
              Please enter your new password.
            </p>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {errorMessage && (
                <div className="space-y-2 text-sm text-destructive bg-destructive/15 p-3 rounded-md">
                  {errorMessage}
                </div>
              )}
              {successMessage && (
                <div className="space-y-2 text-sm text-emerald-600 bg-emerald-50 p-3 rounded-md">
                  {successMessage}
                </div>
              )}
              <FormField
                control={form.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter your new password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmNewPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Confirm your new password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="mt-16 space-y-4">
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? "Resetting Password..." : "Reset Password"}
                </Button>
                <div className="text-left">
                  <Link href="/login" className="text-gray-900 hover:text-gray-600">
                    Back to Login
                  </Link>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CredentialsPage;
