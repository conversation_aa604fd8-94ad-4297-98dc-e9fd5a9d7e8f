"use client";

import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { AuthService } from '@/services/auth.service';
import { useState } from 'react';
import { useRouter, useSearchParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";

// Define Zod schema for form validation
const LoginFormSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof LoginFormSchema>;

export default function LoginPage() {
  const searchParams = useSearchParams();
  const logout = searchParams.get('logout');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();
  const form = useForm<LoginFormData>({
    resolver: zodResolver(LoginFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  useEffect(() => {
    async function checkInitialMessage() {
      if (logout) {
        setTimeout(() => {
          toast({
            title: "Success",
            description: "You have successfully signed out.",
          });
        });
        const params = new URLSearchParams(Array.from(searchParams.entries()));
        params.delete("logout");
        router.replace(`?${params.toString()}`, { scroll: false });
      }
    }

    checkInitialMessage();
  }, [logout]);

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setError(null);

    const result = await AuthService.signIn(data.email, data.password);

    if (result.success) {
      if (result.firstLogin) {
        router.push('/first-login');
      } else {
        router.push('/dashboard');
      }
    } else {
      setError(`Error: ${result.error}`);
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="relative h-screen flex flex-col lg:flex-row items-center justify-center lg:px-0">
        <div className="relative hidden lg:flex h-full flex-col bg-muted p-10 text-white dark:border-r lg:w-1/2">
          <Image
            src="/images/auth-bg.jpg"
            alt="Background"
            fill
            className="absolute inset-0 object-cover"
          />
          <div className="relative z-20 flex items-center text-lg font-medium">
            <Image
              src="/images/ies-logo.png"
              alt="IES Logo"
              width={24}
              height={24}
              className="mr-2 h-6 w-6"
            />
            CRM System (Institution of Engineering Singapore)
          </div>
        </div>
        <div className="p-8 flex flex-col justify-center w-full lg:w-1/2">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <div className="flex flex-col space-y-2 text-center">
              <h1 className="text-2xl font-semibold tracking-tight">
                Login
              </h1>
              <p className="text-sm text-muted-foreground">
                Enter your email and password to login
              </p>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6" method="post" action="#">
                {error && (
                  <div className="space-y-2 text-sm text-destructive bg-destructive/15 p-3 rounded-md">
                    {error}
                  </div>
                )}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email address</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your email address" autoComplete="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Enter your password" autoComplete="current-password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Link href="/forgot-password" className="text-gray-900 hover:text-gray-600">
                    Forgot password?
                  </Link>
                </div>
                <div className="mt-16 space-y-4">
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Loading..." : "Login"}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
