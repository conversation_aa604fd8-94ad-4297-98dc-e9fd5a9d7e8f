"use client"; // This makes the component a Client Component

import React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useState } from 'react';
import { forgetPassword } from "@/services/auth-server.service";

const formSchema = z.object({
  email: z.coerce
    .string()
    .min(1, "Email is required")
    .email("Invalid email address"),
});

const ForgotPasswordPage = () => {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    const { email } = values;

    await forgetPassword(email);
    setSuccessMessage("If an account with this email exists, you will receive a password reset link shortly.");
    form.reset();
    setIsLoading(false);
  }

  return (
    <>
      <div className="relative h-screen flex flex-col lg:flex-row items-center justify-center lg:px-0">
        <div className="relative hidden lg:flex h-full flex-col bg-muted p-10 text-white dark:border-r lg:w-1/2">
          <Image
            src="/images/auth-bg.jpg"
            alt="Background"
            fill
            className="absolute inset-0 object-cover"
          />
          <div className="relative z-20 flex items-center text-lg font-medium">
            <Image
              src="/images/ies-logo.png"
              alt="IES Logo"
              width={24}
              height={24}
              className="mr-2 h-6 w-6"
            />
            CRM System (Institution of Engineering Singapore)
          </div>
        </div>
        <div className="p-8 flex flex-col justify-center w-full lg:w-1/2">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <div className="flex flex-col space-y-2 text-center">
              <h1 className="text-2xl font-semibold tracking-tight">
                Forgot Password
              </h1>
              <p className="text-sm text-muted-foreground">
                Please provide your account&apos;s email for which you want to reset your password.
              </p>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {successMessage && (
                  <div className="space-y-2 text-sm text-emerald-600 bg-emerald-50 p-3 rounded-md">
                    {successMessage}
                  </div>
                )}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email address</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          autoComplete="email"
                          placeholder="Enter your email address"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="mt-16 space-y-4">
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Loading..." : "Next"}
                  </Button>
                  <div className="text-left">
                    <Link href="/login" className="text-gray-900 hover:text-gray-600">
                      Back to Login
                    </Link>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default ForgotPasswordPage;
