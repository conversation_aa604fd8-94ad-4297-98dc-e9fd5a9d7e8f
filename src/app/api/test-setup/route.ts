import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export async function GET() {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Create test user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: '<EMAIL>.MBX6rxy',
      email_confirm: true
    });

    if (authError && !authError.message.includes('already registered')) {
      throw authError;
    }

    const userId = authData?.user?.id || 'existing-user';

    // Insert or update user profile
    const { error: profileError } = await supabase
      .from('users')
      .upsert({
        id: userId,
        email: '<EMAIL>',
        full_name: 'Super Admin',
        role_name: 'SUPER_ADMIN',
        status: true
      }, { onConflict: 'email' });

    if (profileError) {
      console.error('Profile error:', profileError);
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Test user created/updated successfully',
      userId 
    });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}