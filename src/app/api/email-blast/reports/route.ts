import { NextRequest, NextResponse } from "next/server";
import {
  getCampaignMetrics,
  getPerformanceData,
  getEngagementData,
  getTopCampaigns,
  getDeviceStats,
  getEngagementMetrics,
} from "@/services/email-blast/reports.service";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const dateRange = searchParams.get("dateRange") || "last30days";
    const reportType = searchParams.get("type");

    if (reportType === "metrics") {
      const metrics = await getCampaignMetrics(dateRange);
      return NextResponse.json(metrics);
    }
    
    if (reportType === "performance") {
      const data = await getPerformanceData(dateRange);
      return NextResponse.json(data);
    }
    
    if (reportType === "engagement") {
      const data = await getEngagementData(dateRange);
      return NextResponse.json(data);
    }
    
    if (reportType === "topCampaigns") {
      const data = await getTopCampaigns(dateRange);
      return NextResponse.json(data);
    }
    
    if (reportType === "deviceStats") {
      const data = await getDeviceStats(dateRange);
      return NextResponse.json(data);
    }
    
    if (reportType === "engagementMetrics") {
      const data = await getEngagementMetrics(dateRange);
      return NextResponse.json(data);
    }

    // Default: return all data
    const [
      metrics,
      performance,
      engagement,
      topCampaigns,
      deviceStats,
      engagementMetrics
    ] = await Promise.all([
      getCampaignMetrics(dateRange),
      getPerformanceData(dateRange),
      getEngagementData(dateRange),
      getTopCampaigns(dateRange),
      getDeviceStats(dateRange),
      getEngagementMetrics(dateRange)
    ]);

    return NextResponse.json({
      metrics,
      performance,
      engagement,
      topCampaigns,
      deviceStats,
      engagementMetrics
    });
  } catch (error) {
    console.error("Error fetching report data:", error);
    return NextResponse.json(
      { error: "Failed to fetch report data" },
      { status: 500 }
    );
  }
}