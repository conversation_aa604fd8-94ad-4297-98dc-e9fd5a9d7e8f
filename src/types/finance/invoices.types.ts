import { Database } from "@/types/database.types";

export type InvoiceStatus = Database["finance"]["Enums"]["invoice_status"]
export type invoiceDetailsView = Database['finance']['Views']['invoice_details']['Row']

export type invoiceDetails = Database['finance']['Tables']['invoices']['Row'] & {
    invoice_items: Database['finance']['Tables']['invoice_items']['Row'][];
    receipts: Database['finance']['Tables']['receipts']['Row'][];
    file_name?: string;
}

export const invoiceStatus = [
    {
        value: "DRAFT",
        label: "Draft",
    },
    {
        value: "UNPAID",
        label: "Unpaid",
    },
    {
        value: "SENT",
        label: "Sent",
    },
    {
        value: "PARTIALLY_PAID",
        label: "Partially Paid",
    },
    {
        value: "PAID",
        label: "Paid",
    },
    {
        value: "VOID",
        label: "Void",
    },
    {
        value: "OVERDUE",
        label: "Overdue",
    },
]
