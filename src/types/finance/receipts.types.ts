import { Database } from "@/types/database.types";

export type receiptDetails = Database['finance']['Tables']['receipts']['Row']

export interface receiptsInterface {
    receipt_id: string;
    receipt_number: string;
    invoice_number: string;
    payment_method: string;
    payment_status: string;
    payment_reference: string;
    payment_date: string;
    payment_by: string;
}

export const paymentMethod = [
    {
        value: "CASH",
        label: "Cash",
    },
    {
        value: "BANK_TRANSFER",
        label: "Bank Transfer",
    },
    {
        value: "CREDIT_CARD",
        label: "Credit Card",
    },
    {
        value: "CHEQUE",
        label: "Cheque",
    },
    {
        value: "DIGITAL_WALLET",
        label: "Digital Wallet",
    },
];

export const paymentStatus = [
    {
        value: "SUCCESS",
        label: "Success",
    },
    {
        value: "PENDING",
        label: "Pending",
    },
    {
        value: "FAILED",
        label: "Failed",
    },
    {
        value: "VOID",
        label: "Void",
    },
];