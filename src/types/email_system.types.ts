export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  email_system: {
    Tables: {
      campaign_recipients: {
        Row: {
          bounce_type: string | null
          bounced_at: string | null
          campaign_id: string
          click_count: number | null
          clicked_at: string | null
          created_at: string | null
          delivered_at: string | null
          email: string
          error_message: string | null
          failed_at: string | null
          first_clicked_at: string | null
          first_opened_at: string | null
          id: string
          name: string | null
          open_count: number | null
          opened_at: string | null
          queue_id: string | null
          recipient_data: Json | null
          sent_at: string | null
          status: string
          unsubscribed_at: string | null
          updated_at: string | null
        }
        Insert: {
          bounce_type?: string | null
          bounced_at?: string | null
          campaign_id: string
          click_count?: number | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          email: string
          error_message?: string | null
          failed_at?: string | null
          first_clicked_at?: string | null
          first_opened_at?: string | null
          id?: string
          name?: string | null
          open_count?: number | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_data?: Json | null
          sent_at?: string | null
          status?: string
          unsubscribed_at?: string | null
          updated_at?: string | null
        }
        Update: {
          bounce_type?: string | null
          bounced_at?: string | null
          campaign_id?: string
          click_count?: number | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          email?: string
          error_message?: string | null
          failed_at?: string | null
          first_clicked_at?: string | null
          first_opened_at?: string | null
          id?: string
          name?: string | null
          open_count?: number | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_data?: Json | null
          sent_at?: string | null
          status?: string
          unsubscribed_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_recipients_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_recipients_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_recipients_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "campaign_recipients_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_statistics: {
        Row: {
          avg_click_time_hours: number | null
          avg_open_time_hours: number | null
          bounce_count: number | null
          bounce_rate: number | null
          campaign_id: string
          click_rate: number | null
          created_at: string | null
          delivered_count: number | null
          delivery_rate: number | null
          failed_count: number | null
          hard_bounce_count: number | null
          id: string
          last_calculated_at: string | null
          open_rate: number | null
          pending_count: number | null
          sent_count: number | null
          soft_bounce_count: number | null
          spam_report_count: number | null
          total_clicks: number | null
          total_opens: number | null
          total_recipients: number
          unique_clicks: number | null
          unique_opens: number | null
          unsubscribe_count: number | null
          unsubscribe_rate: number | null
          updated_at: string | null
        }
        Insert: {
          avg_click_time_hours?: number | null
          avg_open_time_hours?: number | null
          bounce_count?: number | null
          bounce_rate?: number | null
          campaign_id: string
          click_rate?: number | null
          created_at?: string | null
          delivered_count?: number | null
          delivery_rate?: number | null
          failed_count?: number | null
          hard_bounce_count?: number | null
          id?: string
          last_calculated_at?: string | null
          open_rate?: number | null
          pending_count?: number | null
          sent_count?: number | null
          soft_bounce_count?: number | null
          spam_report_count?: number | null
          total_clicks?: number | null
          total_opens?: number | null
          total_recipients?: number
          unique_clicks?: number | null
          unique_opens?: number | null
          unsubscribe_count?: number | null
          unsubscribe_rate?: number | null
          updated_at?: string | null
        }
        Update: {
          avg_click_time_hours?: number | null
          avg_open_time_hours?: number | null
          bounce_count?: number | null
          bounce_rate?: number | null
          campaign_id?: string
          click_rate?: number | null
          created_at?: string | null
          delivered_count?: number | null
          delivery_rate?: number | null
          failed_count?: number | null
          hard_bounce_count?: number | null
          id?: string
          last_calculated_at?: string | null
          open_rate?: number | null
          pending_count?: number | null
          sent_count?: number | null
          soft_bounce_count?: number | null
          spam_report_count?: number | null
          total_clicks?: number | null
          total_opens?: number | null
          total_recipients?: number
          unique_clicks?: number | null
          unique_opens?: number | null
          unsubscribe_count?: number | null
          unsubscribe_rate?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_statistics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_statistics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      email_campaigns: {
        Row: {
          body_html: string
          body_text: string | null
          bounced_count: number | null
          clicked_count: number | null
          completed_at: string | null
          created_at: string | null
          created_by: string
          delivered_count: number | null
          description: string | null
          failed_count: number | null
          from_email: string
          from_name: string
          id: string
          include_unsubscribe: boolean | null
          metadata: Json | null
          name: string
          opened_count: number | null
          recipient_list_id: string
          reply_to_email: string | null
          scheduled_at: string | null
          sent_count: number | null
          started_at: string | null
          status: string
          subject: string
          tags: string[] | null
          template_id: string | null
          total_recipients: number | null
          track_clicks: boolean | null
          track_opens: boolean | null
          unsubscribed_count: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          body_html: string
          body_text?: string | null
          bounced_count?: number | null
          clicked_count?: number | null
          completed_at?: string | null
          created_at?: string | null
          created_by: string
          delivered_count?: number | null
          description?: string | null
          failed_count?: number | null
          from_email: string
          from_name: string
          id?: string
          include_unsubscribe?: boolean | null
          metadata?: Json | null
          name: string
          opened_count?: number | null
          recipient_list_id: string
          reply_to_email?: string | null
          scheduled_at?: string | null
          sent_count?: number | null
          started_at?: string | null
          status?: string
          subject: string
          tags?: string[] | null
          template_id?: string | null
          total_recipients?: number | null
          track_clicks?: boolean | null
          track_opens?: boolean | null
          unsubscribed_count?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          body_html?: string
          body_text?: string | null
          bounced_count?: number | null
          clicked_count?: number | null
          completed_at?: string | null
          created_at?: string | null
          created_by?: string
          delivered_count?: number | null
          description?: string | null
          failed_count?: number | null
          from_email?: string
          from_name?: string
          id?: string
          include_unsubscribe?: boolean | null
          metadata?: Json | null
          name?: string
          opened_count?: number | null
          recipient_list_id?: string
          reply_to_email?: string | null
          scheduled_at?: string | null
          sent_count?: number | null
          started_at?: string | null
          status?: string
          subject?: string
          tags?: string[] | null
          template_id?: string | null
          total_recipients?: number | null
          track_clicks?: boolean | null
          track_opens?: boolean | null
          unsubscribed_count?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_campaigns_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_campaigns_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "shared_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      email_clicks: {
        Row: {
          campaign_id: string | null
          click_position: string | null
          clicked_at: string | null
          device_type: string | null
          id: string
          ip_address: unknown | null
          is_blast_click: boolean | null
          queue_id: string | null
          recipient_id: string | null
          url: string
          user_agent: string | null
        }
        Insert: {
          campaign_id?: string | null
          click_position?: string | null
          clicked_at?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_click?: boolean | null
          queue_id?: string | null
          recipient_id?: string | null
          url: string
          user_agent?: string | null
        }
        Update: {
          campaign_id?: string | null
          click_position?: string | null
          clicked_at?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_click?: boolean | null
          queue_id?: string | null
          recipient_id?: string | null
          url?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_clicks_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_clicks_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "campaign_recipients"
            referencedColumns: ["id"]
          },
        ]
      }
      email_configurations: {
        Row: {
          active: boolean | null
          attachment_config: Json | null
          conditions: Json
          created_at: string | null
          description: string | null
          id: string
          name: string
          priority: number | null
          recipients: Json
          template_id: string
          trigger_type: string
          updated_at: string | null
        }
        Insert: {
          active?: boolean | null
          attachment_config?: Json | null
          conditions?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          priority?: number | null
          recipients?: Json
          template_id: string
          trigger_type: string
          updated_at?: string | null
        }
        Update: {
          active?: boolean | null
          attachment_config?: Json | null
          conditions?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          priority?: number | null
          recipients?: Json
          template_id?: string
          trigger_type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_configurations_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_configurations_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "shared_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      email_delivery_events: {
        Row: {
          bounce_sub_type: string | null
          bounce_type: string | null
          complaint_feedback_type: string | null
          complaint_sub_type: string | null
          created_at: string | null
          delivery_delay: number | null
          diagnostic_code: string | null
          event_data: Json | null
          event_timestamp: string
          event_type: string
          id: string
          processed: boolean | null
          processed_at: string | null
          queue_id: string | null
          recipient_email: string
          ses_message_id: string
          source_email: string | null
          status: string
        }
        Insert: {
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_feedback_type?: string | null
          complaint_sub_type?: string | null
          created_at?: string | null
          delivery_delay?: number | null
          diagnostic_code?: string | null
          event_data?: Json | null
          event_timestamp: string
          event_type: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          queue_id?: string | null
          recipient_email: string
          ses_message_id: string
          source_email?: string | null
          status?: string
        }
        Update: {
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_feedback_type?: string | null
          complaint_sub_type?: string | null
          created_at?: string | null
          delivery_delay?: number | null
          diagnostic_code?: string | null
          event_data?: Json | null
          event_timestamp?: string
          event_type?: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          queue_id?: string | null
          recipient_email?: string
          ses_message_id?: string
          source_email?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_delivery_events_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_delivery_events_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      email_events: {
        Row: {
          created_at: string | null
          error_message: string | null
          event_data: Json
          event_type: string
          id: string
          processed: boolean | null
          processed_at: string | null
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          event_data: Json
          event_type: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
        }
        Relationships: []
      }
      email_logs: {
        Row: {
          action: string
          created_at: string | null
          details: Json | null
          error_message: string | null
          id: string
          queue_id: string | null
          status: string
        }
        Insert: {
          action: string
          created_at?: string | null
          details?: Json | null
          error_message?: string | null
          id?: string
          queue_id?: string | null
          status: string
        }
        Update: {
          action?: string
          created_at?: string | null
          details?: Json | null
          error_message?: string | null
          id?: string
          queue_id?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_logs_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_logs_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      email_opens: {
        Row: {
          campaign_id: string | null
          device_type: string | null
          id: string
          ip_address: unknown | null
          is_blast_open: boolean | null
          opened_at: string | null
          queue_id: string | null
          recipient_id: string | null
          user_agent: string | null
        }
        Insert: {
          campaign_id?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_open?: boolean | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_id?: string | null
          user_agent?: string | null
        }
        Update: {
          campaign_id?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_open?: boolean | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_id?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_opens_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_opens_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "campaign_recipients"
            referencedColumns: ["id"]
          },
        ]
      }
      email_queue: {
        Row: {
          attachments: Json | null
          attempts: number | null
          bcc_email: string[] | null
          body_html: string
          body_text: string | null
          bounce_type: string | null
          campaign_id: string | null
          campaign_recipient_id: string | null
          cc_email: string[] | null
          complaint_type: string | null
          configuration_id: string | null
          configuration_set: string | null
          created_at: string | null
          delivery_confirmed_at: string | null
          error_message: string | null
          id: string
          is_blast_email: boolean | null
          last_attempt_at: string | null
          max_attempts: number | null
          metadata: Json | null
          personalization_data: Json | null
          priority: number | null
          send_rate_limit: number | null
          sent_at: string | null
          ses_message_id: string | null
          status: string
          subject: string
          template_id: string
          to_email: string[]
          updated_at: string | null
        }
        Insert: {
          attachments?: Json | null
          attempts?: number | null
          bcc_email?: string[] | null
          body_html: string
          body_text?: string | null
          bounce_type?: string | null
          campaign_id?: string | null
          campaign_recipient_id?: string | null
          cc_email?: string[] | null
          complaint_type?: string | null
          configuration_id?: string | null
          configuration_set?: string | null
          created_at?: string | null
          delivery_confirmed_at?: string | null
          error_message?: string | null
          id?: string
          is_blast_email?: boolean | null
          last_attempt_at?: string | null
          max_attempts?: number | null
          metadata?: Json | null
          personalization_data?: Json | null
          priority?: number | null
          send_rate_limit?: number | null
          sent_at?: string | null
          ses_message_id?: string | null
          status?: string
          subject: string
          template_id: string
          to_email: string[]
          updated_at?: string | null
        }
        Update: {
          attachments?: Json | null
          attempts?: number | null
          bcc_email?: string[] | null
          body_html?: string
          body_text?: string | null
          bounce_type?: string | null
          campaign_id?: string | null
          campaign_recipient_id?: string | null
          cc_email?: string[] | null
          complaint_type?: string | null
          configuration_id?: string | null
          configuration_set?: string | null
          created_at?: string | null
          delivery_confirmed_at?: string | null
          error_message?: string | null
          id?: string
          is_blast_email?: boolean | null
          last_attempt_at?: string | null
          max_attempts?: number | null
          metadata?: Json | null
          personalization_data?: Json | null
          priority?: number | null
          send_rate_limit?: number | null
          sent_at?: string | null
          ses_message_id?: string | null
          status?: string
          subject?: string
          template_id?: string
          to_email?: string[]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_queue_configuration_id_fkey"
            columns: ["configuration_id"]
            isOneToOne: false
            referencedRelation: "email_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_queue_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_queue_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "shared_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      email_suppressions: {
        Row: {
          bounce_count: number | null
          bounce_sub_type: string | null
          bounce_type: string | null
          complaint_count: number | null
          complaint_sub_type: string | null
          created_at: string | null
          email_address: string
          first_bounce_at: string | null
          first_complaint_at: string | null
          id: string
          is_active: boolean | null
          last_bounce_at: string | null
          last_complaint_at: string | null
          notes: string | null
          suppression_reason: string | null
          suppression_type: string
          updated_at: string | null
        }
        Insert: {
          bounce_count?: number | null
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_count?: number | null
          complaint_sub_type?: string | null
          created_at?: string | null
          email_address: string
          first_bounce_at?: string | null
          first_complaint_at?: string | null
          id?: string
          is_active?: boolean | null
          last_bounce_at?: string | null
          last_complaint_at?: string | null
          notes?: string | null
          suppression_reason?: string | null
          suppression_type: string
          updated_at?: string | null
        }
        Update: {
          bounce_count?: number | null
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_count?: number | null
          complaint_sub_type?: string | null
          created_at?: string | null
          email_address?: string
          first_bounce_at?: string | null
          first_complaint_at?: string | null
          id?: string
          is_active?: boolean | null
          last_bounce_at?: string | null
          last_complaint_at?: string | null
          notes?: string | null
          suppression_reason?: string | null
          suppression_type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      email_templates: {
        Row: {
          active: boolean | null
          body_html: string
          body_text: string | null
          category: string
          created_at: string | null
          description: string | null
          id: string
          is_blast_template: boolean | null
          last_used_at: string | null
          name: string
          parent_template_id: string | null
          subject: string
          template_type: string | null
          thumbnail_url: string | null
          updated_at: string | null
          usage_count: number | null
          variables: Json | null
          version: number | null
        }
        Insert: {
          active?: boolean | null
          body_html: string
          body_text?: string | null
          category: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_blast_template?: boolean | null
          last_used_at?: string | null
          name: string
          parent_template_id?: string | null
          subject: string
          template_type?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: Json | null
          version?: number | null
        }
        Update: {
          active?: boolean | null
          body_html?: string
          body_text?: string | null
          category?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_blast_template?: boolean | null
          last_used_at?: string | null
          name?: string
          parent_template_id?: string | null
          subject?: string
          template_type?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: Json | null
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "email_templates_parent_template_id_fkey"
            columns: ["parent_template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_templates_parent_template_id_fkey"
            columns: ["parent_template_id"]
            isOneToOne: false
            referencedRelation: "shared_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      recipient_list_members: {
        Row: {
          added_at: string | null
          added_by: string | null
          email: string
          id: string
          is_active: boolean | null
          list_id: string
          member_data: Json | null
          name: string | null
        }
        Insert: {
          added_at?: string | null
          added_by?: string | null
          email: string
          id?: string
          is_active?: boolean | null
          list_id: string
          member_data?: Json | null
          name?: string | null
        }
        Update: {
          added_at?: string | null
          added_by?: string | null
          email?: string
          id?: string
          is_active?: boolean | null
          list_id?: string
          member_data?: Json | null
          name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recipient_list_members_list_id_fkey"
            columns: ["list_id"]
            isOneToOne: false
            referencedRelation: "recipient_lists"
            referencedColumns: ["id"]
          },
        ]
      }
      recipient_lists: {
        Row: {
          created_at: string | null
          created_by: string
          description: string | null
          filters: Json | null
          id: string
          is_system: boolean | null
          last_calculated_at: string | null
          last_used_at: string | null
          list_type: string
          metadata: Json | null
          name: string
          recipient_count: number | null
          status: string | null
          tags: string[] | null
          updated_at: string | null
          updated_by: string | null
          usage_count: number | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          description?: string | null
          filters?: Json | null
          id?: string
          is_system?: boolean | null
          last_calculated_at?: string | null
          last_used_at?: string | null
          list_type?: string
          metadata?: Json | null
          name: string
          recipient_count?: number | null
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
          updated_by?: string | null
          usage_count?: number | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          description?: string | null
          filters?: Json | null
          id?: string
          is_system?: boolean | null
          last_calculated_at?: string | null
          last_used_at?: string | null
          list_type?: string
          metadata?: Json | null
          name?: string
          recipient_count?: number | null
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
          updated_by?: string | null
          usage_count?: number | null
        }
        Relationships: []
      }
      template_usage_stats: {
        Row: {
          avg_bounce_rate: number | null
          avg_click_rate: number | null
          avg_open_rate: number | null
          blast_usage: number | null
          created_at: string | null
          id: string
          last_calculated_at: string | null
          last_used_at: string | null
          notification_usage: number | null
          popular_variables: string[] | null
          template_id: string
          total_usage: number | null
        }
        Insert: {
          avg_bounce_rate?: number | null
          avg_click_rate?: number | null
          avg_open_rate?: number | null
          blast_usage?: number | null
          created_at?: string | null
          id?: string
          last_calculated_at?: string | null
          last_used_at?: string | null
          notification_usage?: number | null
          popular_variables?: string[] | null
          template_id: string
          total_usage?: number | null
        }
        Update: {
          avg_bounce_rate?: number | null
          avg_click_rate?: number | null
          avg_open_rate?: number | null
          blast_usage?: number | null
          created_at?: string | null
          id?: string
          last_calculated_at?: string | null
          last_used_at?: string | null
          notification_usage?: number | null
          popular_variables?: string[] | null
          template_id?: string
          total_usage?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "template_usage_stats_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: true
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "template_usage_stats_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: true
            referencedRelation: "shared_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      campaign_overview: {
        Row: {
          bounce_rate: number | null
          click_rate: number | null
          completed_at: string | null
          created_at: string | null
          created_by: string | null
          delivery_rate: number | null
          description: string | null
          id: string | null
          name: string | null
          open_rate: number | null
          recipient_list_name: string | null
          scheduled_at: string | null
          started_at: string | null
          status: string | null
          template_name: string | null
          total_recipients: number | null
        }
        Relationships: []
      }
      daily_delivery_metrics: {
        Row: {
          bounce_rate_percent: number | null
          complaint_rate_percent: number | null
          complaints: number | null
          date: string | null
          delivery_rate_percent: number | null
          emails_delivered: number | null
          emails_sent: number | null
          hard_bounces: number | null
          soft_bounces: number | null
        }
        Relationships: []
      }
      email_delivery_analytics: {
        Row: {
          bounce_type: string | null
          complaint_type: string | null
          delivery_confirmed_at: string | null
          delivery_status: string | null
          delivery_time_seconds: number | null
          event_count: number | null
          queue_id: string | null
          queue_status: string | null
          sent_at: string | null
          ses_message_id: string | null
          subject: string | null
          to_email: string[] | null
        }
        Insert: {
          bounce_type?: string | null
          complaint_type?: string | null
          delivery_confirmed_at?: string | null
          delivery_status?: never
          delivery_time_seconds?: never
          event_count?: never
          queue_id?: string | null
          queue_status?: string | null
          sent_at?: string | null
          ses_message_id?: string | null
          subject?: string | null
          to_email?: string[] | null
        }
        Update: {
          bounce_type?: string | null
          complaint_type?: string | null
          delivery_confirmed_at?: string | null
          delivery_status?: never
          delivery_time_seconds?: never
          event_count?: never
          queue_id?: string | null
          queue_status?: string | null
          sent_at?: string | null
          ses_message_id?: string | null
          subject?: string | null
          to_email?: string[] | null
        }
        Relationships: []
      }
      email_queue_health: {
        Row: {
          active: boolean | null
          failed_emails: number | null
          health_status: string | null
          jobname: string | null
          pending_emails: number | null
          schedule: string | null
        }
        Insert: {
          active?: boolean | null
          failed_emails?: never
          health_status?: never
          jobname?: string | null
          pending_emails?: never
          schedule?: string | null
        }
        Update: {
          active?: boolean | null
          failed_emails?: never
          health_status?: never
          jobname?: string | null
          pending_emails?: never
          schedule?: string | null
        }
        Relationships: []
      }
      shared_email_templates: {
        Row: {
          active: boolean | null
          avg_click_rate: number | null
          avg_open_rate: number | null
          blast_usage: number | null
          body_html: string | null
          body_text: string | null
          category: string | null
          created_at: string | null
          description: string | null
          id: string | null
          is_blast_template: boolean | null
          last_used_at: string | null
          name: string | null
          notification_usage: number | null
          parent_template_id: string | null
          subject: string | null
          template_type: string | null
          thumbnail_url: string | null
          total_usage: number | null
          updated_at: string | null
          usage_count: number | null
          variables: Json | null
          version: number | null
        }
        Relationships: [
          {
            foreignKeyName: "email_templates_parent_template_id_fkey"
            columns: ["parent_template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_templates_parent_template_id_fkey"
            columns: ["parent_template_id"]
            isOneToOne: false
            referencedRelation: "shared_email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      suppression_analytics: {
        Row: {
          active_suppressions: number | null
          avg_bounce_count: number | null
          latest_bounce: string | null
          latest_complaint: string | null
          repeat_bounces: number | null
          repeat_complaints: number | null
          suppression_type: string | null
          total_suppressed: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      calculate_recipient_list: {
        Args: { p_list_id: string }
        Returns: number
      }
      get_delivery_stats: {
        Args: { days_back?: number }
        Returns: {
          date: string
          total_sent: number
          total_delivered: number
          total_bounced: number
          total_complaints: number
          delivery_rate: number
          bounce_rate: number
          complaint_rate: number
        }[]
      }
      get_email_processing_stats: {
        Args: { days_back?: number }
        Returns: {
          date: string
          total_processed: number
          successful: number
          failed: number
          success_rate: number
        }[]
      }
      get_email_queue_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          status: string
          count: number
          oldest_email: string
          newest_email: string
        }[]
      }
      is_email_suppressed: {
        Args: { email_addr: string }
        Returns: boolean
      }
      manage_email_queue_cron: {
        Args: { action: string; new_schedule?: string; batch_limit?: number }
        Returns: string
      }
      migrate_templates_for_blast: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      process_email_queue_trigger: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      process_ses_delivery_event: {
        Args: {
          ses_message_id_param: string
          event_type_param: string
          event_timestamp_param: string
          recipient_email_param: string
          event_data_param?: Json
        }
        Returns: string
      }
      queue_campaign_emails: {
        Args: { p_campaign_id: string; p_batch_size?: number }
        Returns: number
      }
      suppress_email_address: {
        Args: {
          email_addr: string
          suppression_type_param: string
          reason?: string
          bounce_type_param?: string
          bounce_sub_type_param?: string
          complaint_sub_type_param?: string
        }
        Returns: string
      }
      unsuppress_email_address: {
        Args: { email_addr: string }
        Returns: boolean
      }
      update_campaign_statistics: {
        Args: { p_campaign_id: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  email_system: {
    Enums: {},
  },
} as const

