/**
 * Type definitions specific to Purchase Order PDF generation
 */

/**
 * Formatted values for PDF display
 */
export interface FormattedPOValues {
  poNumber: string;
  orderDate: string;
  deliveryDate: string;
  paymentTerms: string;
  department: string;
  subtotal: string;
  gstAmount: string;
  totalAmount: string;
}

/**
 * Line item data formatted for PDF table
 */
export interface FormattedLineItem {
  itemNumber: string;
  description: string;
  quantity: string;
  unitPrice: string;
  totalAmount: string;
}

/**
 * PDF layout constants
 */
export const PDF_LAYOUT = {
  pageWidth: 210,
  pageHeight: 297,
  margin: 10,
  headerHeight: 50,
  footerHeight: 30,
  maxContentHeight: 270,
  leftBoxX: 10,
  leftBoxWidth: 95,
  rightBoxX: 105,
  rightBoxWidth: 95,
  boxHeight: 50,
  lineHeight: 7,
  totalsX: 140,
  totalsValueX: 165,
  totalsAmountX: 195
} as const;

/**
 * Table column widths for line items
 */
export const TABLE_COLUMNS = {
  item: 15,
  description: 90,
  quantity: 20,
  unitPrice: 30,
  totalAmount: 35
} as const;

/**
 * Text styles for PDF
 */
export const PDF_STYLES = {
  header: {
    fontSize: 16,
    font: 'helvetica' as const,
    style: 'bold' as const
  },
  subheader: {
    fontSize: 14,
    font: 'helvetica' as const,
    style: 'bold' as const
  },
  normal: {
    fontSize: 10,
    font: 'helvetica' as const,
    style: 'normal' as const
  },
  label: {
    fontSize: 10,
    font: 'helvetica' as const,
    style: 'bold' as const
  }
} as const;