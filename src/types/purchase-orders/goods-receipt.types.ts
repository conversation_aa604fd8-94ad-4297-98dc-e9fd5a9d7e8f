// Goods Receipt Module Types
// Aligned with procurement.goods_receipts and procurement.goods_receipt_items tables

import type { 
  PurchaseOrder, 
  PurchaseOrderItem, 
  Supplier,
  GoodsReceiptStatus 
} from "./purchase-orders.types";

// Database entity types (matches the actual database schema)
export interface GoodsReceiptEntity {
  id: string;
  gr_number: string;
  purchase_order_id: string;
  receipt_date: string; // ISO date string
  received_by: string; // User ID
  notes?: string | null;
  status: GoodsReceiptStatus;
  
  // Audit fields
  archived_at?: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string | null;
}

export interface GoodsReceiptItemEntity {
  id: string;
  goods_receipt_id: string;
  po_item_id: string; // Note: Database uses po_item_id, not purchase_order_item_id
  quantity_received: number;
  quantity_accepted: number;
  quantity_rejected: number;
  rejection_reason?: string | null;
  
  // Audit fields
  created_at: string;
  updated_at: string;
}

// Extended types with relationships (for frontend use)
export interface GoodsReceiptWithRelations extends GoodsReceiptEntity {
  purchase_order?: PurchaseOrderWithSupplier;
  items?: GoodsReceiptItemWithRelations[];
  received_by_user?: UserInfo;
  created_by_user?: UserInfo;
  updated_by_user?: UserInfo;
}

export interface GoodsReceiptItemWithRelations extends GoodsReceiptItemEntity {
  purchase_order_item?: PurchaseOrderItemWithDetails;
}

export interface PurchaseOrderWithSupplier extends PurchaseOrder {
  supplier?: Supplier;
}

export interface PurchaseOrderItemWithDetails extends PurchaseOrderItem {
  quantity_received?: number; // Total received across all GRs
  remaining_quantity?: number; // Calculated field
}

export interface UserInfo {
  id: string;
  name?: string;
  email: string;
}

// Form-specific types (for data transformation)
export interface GoodsReceiptFormValues {
  purchase_order_id: string;
  receipt_date: Date;
  received_by: string;
  notes?: string;
  inspection_required?: boolean;
  inspector_name?: string;
  inspection_date?: Date;
  inspection_report_number?: string;
  items: GoodsReceiptItemFormValues[];
}

export interface GoodsReceiptItemFormValues {
  po_item_id: string;
  quantity_received: number;
  quantity_accepted: number;
  quantity_rejected: number;
  rejection_reason?: string;
  rejection_reason_type?: RejectionReasonType;
  rejection_notes?: string;
}

// Enums and constants
export type RejectionReasonType = 
  | "damaged" 
  | "wrong_item" 
  | "quality_issue" 
  | "expired" 
  | "other";

export const REJECTION_REASON_LABELS: Record<RejectionReasonType, string> = {
  damaged: "Damaged",
  wrong_item: "Wrong Item",
  quality_issue: "Quality Issue",
  expired: "Expired",
  other: "Other"
};

// Receipt status calculation helpers
export interface ReceiptStatusInfo {
  item_id: string;
  ordered_quantity: number;
  received_quantity: number;
  accepted_quantity: number;
  rejected_quantity: number;
  remaining_quantity: number;
  is_fully_received: boolean;
}

export interface PurchaseOrderReceiptStatus {
  purchase_order: PurchaseOrderWithSupplier;
  items: ReceiptStatusInfo[];
  total_ordered: number;
  total_received: number;
  total_accepted: number;
  total_rejected: number;
  total_remaining: number;
  receipt_percentage: number;
  has_rejections: boolean;
  is_fully_received: boolean;
  previous_receipts: GoodsReceiptSummary[];
}

export interface GoodsReceiptSummary {
  id: string;
  gr_number: string;
  receipt_date: string;
  status: GoodsReceiptStatus;
  total_received: number;
  total_accepted: number;
  total_rejected: number;
  has_rejections: boolean;
}

// API Response types
export interface GoodsReceiptListResponse {
  goods_receipts: GoodsReceiptWithRelations[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateGoodsReceiptResponse {
  success: boolean;
  data?: GoodsReceiptEntity;
  newId?: string;
  error?: string;
}

export interface UpdateGoodsReceiptResponse {
  success: boolean;
  data?: GoodsReceiptEntity;
  error?: string;
}

// Filter types for list views
export interface GoodsReceiptFilters {
  status?: GoodsReceiptStatus[];
  supplier_id?: string[];
  date_from?: Date;
  date_to?: Date;
  has_rejections?: boolean;
  purchase_order_id?: string;
  search?: string;
}

// Inspection-related types (for future database schema extension)
export interface InspectionDetails {
  inspection_required: boolean;
  inspector_name?: string | null;
  inspection_date?: string | null; // ISO date string
  inspection_report_number?: string | null;
  has_discrepancies?: boolean;
  discrepancy_notes?: string | null;
}

// Data transformation utilities type guards
export function isGoodsReceiptEntity(obj: any): obj is GoodsReceiptEntity {
  return obj && 
    typeof obj.id === 'string' &&
    typeof obj.gr_number === 'string' &&
    typeof obj.purchase_order_id === 'string' &&
    typeof obj.receipt_date === 'string' &&
    typeof obj.status === 'string';
}

export function isGoodsReceiptItemEntity(obj: any): obj is GoodsReceiptItemEntity {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.goods_receipt_id === 'string' &&
    typeof obj.po_item_id === 'string' &&
    typeof obj.quantity_received === 'number' &&
    typeof obj.quantity_accepted === 'number' &&
    typeof obj.quantity_rejected === 'number';
}