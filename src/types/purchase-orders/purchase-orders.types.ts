// Purchase Orders Module Types
// Based on the procurement schema from the implementation guide

import { Database } from "@/types/database.types";

export type POStatus = Database['procurement']['Enums']['po_status_enum'];

export type SupplierStatus = Database['procurement']['Enums']['supplier_status_enum'] | null;

export type GoodsReceiptStatus = Database['procurement']['Enums']['goods_receipt_status'];

type SupplierRow = Database['procurement']['Tables']['suppliers']['Row'];

// Extend and override specific fields
export interface Supplier extends Omit<SupplierRow,
  'billing_address' | 'shipping_address' | 'contact_persons' | 'bank_details'
> {
  billing_address?: Address;
  shipping_address?: Address;
  contact_persons?: ContactPerson[];
  bank_details?: BankDetails[];
}

export interface Address {
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  // Support legacy field for backward compatibility
  street_address?: string;
}

export interface OptionalAddress {
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  // Support legacy field for backward compatibility
  street_address?: string;
}

export interface ContactPerson {
  id?: string; // Optional for new records, required for updates
  name: string;
  title?: string;
  email: string;
  phone?: string;
  is_primary: boolean;
}

export interface BankDetails {
  id?: string; // Optional for new records, required for updates
  bank_name: string;
  account_number: string;
  swift_code?: string;
  account_type: string;
}

export interface PurchaseOrder {
  id: string;
  po_number: string;
  entity_id: string;
  title: string;
  description?: string;
  supplier_id: string;
  po_date: string;
  expected_delivery_date?: string;
  currency_code: string;
  exchange_rate: number;
  budget_revision_id?: string;
  department_code?: string;
  project_reference?: string;
  delivery_address?: OptionalAddress;
  delivery_instructions?: string;
  subtotal: number;
  tax_amount: number;
  total_tax: number; // Added for compatibility with components
  total_amount: number;
  status: POStatus;
  created_at: string;
  updated_at: string;
  created_by: string;
  approved_by?: string;
  approved_at?: string;

  // Related data (populated via joins)
  suppliers?: Supplier;
  entity?: Entity;
  department?: {
    id: string;
    code: string;
    name: string;
  };
  created_by_user?: {
    id: string;
    name: string;
    email: string;
  };
  items?: PurchaseOrderItem[];
  goods_receipts?: GoodsReceipt[];
  approval_requests?: ApprovalRequest[];
  created_by_name?: {
    full_name?: string;
    email?: string;
  };
}

export interface PurchaseOrderItem {
  id: string;
  purchase_order_id: string;
  line_number: number;
  item_code?: string;
  description: string;
  quantity: number;
  unit_of_measure: string;
  unit_price: number;
  discount_percentage: number;
  tax_code?: string;
  tax_rate: number;
  line_total: number;
  gl_account_code?: string;
  budget_item_id?: string;
  created_at: string;
  updated_at: string;
}

export interface GoodsReceipt {
  id: string;
  gr_number: string;
  purchase_order_id: string;
  receipt_date: string;
  received_by: string;
  notes?: string;
  status: GoodsReceiptStatus;
  created_at: string;
  updated_at: string;

  // Related data
  purchase_order?: PurchaseOrder;
  items?: GoodsReceiptItem[];
  received_by_user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface GoodsReceiptItem {
  id: string;
  goods_receipt_id: string;
  purchase_order_item_id: string;
  quantity_received: number;
  quantity_accepted: number;
  quantity_rejected: number;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;

  // Related data
  purchase_order_item?: PurchaseOrderItem;
}

// Supporting types
export interface Entity {
  id: string;
  name: string;
  merchant_abbreviation?: string;
}

export interface ApprovalRequest {
  id: string;
  request_type: string;
  reference_id: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface BudgetRevision {
  id: string;
  name: string;
  code: string;
  total_amount: number;
  available_amount: number;
}

export interface BudgetItem {
  id: string;
  budget_revision_id: string;
  code: string;
  description: string;
  allocated_amount: number;
  available_amount: number;
}

export interface SupplierFormData {
  supplier_code?: string;
  name: string;
  display_name?: string;
  registration_number?: string;
  tax_registration_number?: string;
  supplier_type?: string;
  category?: string;
  status: SupplierStatus;
  email: string;
  phone?: string;
  website?: string;
  billing_address?: Address;
  shipping_address?: Address;
  same_as_billing?: boolean;
  payment_terms?: number;
  currency_code: string;
  credit_limit?: number;
  is_gst_registered: boolean;
  contact_persons?: ContactPersonFormData[];
  bank_details?: BankDetailsFormData[];
}

export interface ContactPersonFormData {
  id?: string; // Optional for new records, required for updates
  name: string;
  title?: string;
  email: string;
  phone?: string;
  is_primary: boolean;
}

export interface BankDetailsFormData {
  id?: string; // Optional for new records, required for updates
  bank_name: string;
  account_number: string;
  swift_code?: string;
  account_type: string;
}

export interface GoodsReceiptFormData {
  purchase_order_id: string;
  receipt_date: Date;
  received_by: string;
  notes?: string;
  items: GoodsReceiptItemFormData[];
}

export interface GoodsReceiptItemFormData {
  purchase_order_item_id: string;
  quantity_received: number;
  quantity_accepted: number;
  quantity_rejected: number;
  rejection_reason?: string;
}

// Table and filter types
export interface PurchaseOrdersTableFilters {
  status: POStatus[];
  supplier_ids: string[];
  entity_ids: string[];
  department_codes: string[];
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
}

export interface SuppliersTableFilters {
  status: SupplierStatus[];
  supplier_types: string[];
  categories: string[];
}

// Status Management types
export interface StatusChangeRequest {
  supplierId: string;
  status: SupplierStatus;
  reason?: string;
}

export interface BulkStatusChangeRequest {
  supplierIds: string[];
  status: SupplierStatus;
  reason?: string;
}

export interface StatusHistoryItem {
  status: SupplierStatus;
  changed_at: string;
  changed_by?: string;
  reason?: string;
}

// API Response types
export interface SuppliersResponse {
  suppliers: Supplier[] | null;
  success: boolean;
  total: number;
  currentPage: number;
  allSupplierIds: string[];
}

// Summary/Stats types
export interface PurchaseOrdersSummary {
  total_count: number;
  total_amount: number;
  pending_approval_count: number;
  overdue_deliveries_count: number;
}