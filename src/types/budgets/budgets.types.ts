import { Database } from "@/types/database.types";
import { ExtendedBudgetRevision, ExtendedParentBudgetRevision } from "@/types/database-extensions.types";

export type BudgetRevisionView = ExtendedParentBudgetRevision & {
    programme_name: string | null;
    programme_code: string | null;
}

export type BudgetRevisions = ExtendedBudgetRevision

export type BudgetItems = (Database['budget']['Tables']['budget_items']['Row'])

export type Budgets = (Database['budget']['Tables']['budgets']['Row'])

export type budgetsItemGrouped = {
    id: string;
    name: string;
    types: Budgets[] | null;
}

export type budgetsSimpleItem = {
    incomeEntries: Budgets[] | null;
    expenditureEntries: Budgets[] | null;
}

export type budgetComplexItem = {
    incomeEntries: budgetsItemGrouped[] | null;
    expenditureEntries: budgetsItemGrouped[] | null;
}

export type budgetItemCheck = {
    id?: string;
    type?: string;
    proposedRate: string;
    quantity: string;
    remarks: string;
}

export interface BudgetDataEntries {
    type: string;
    proposedRate: number;
    quantity: number;
    subTotal: number;
    remarks: string;
}

export interface BudgetComplexDataEntries {
    sectionName: string;
    types: BudgetDataEntries[];
}

export interface BudgetReviewSimpleEventProps {
    incomeEntries: BudgetDataEntries[];
    expenditureEntries: BudgetDataEntries[];
    summary: Database['budget']['Tables']['budget_summaries']['Row'] | null;
}

export interface BudgetReviewComplexProps {
    incomeEntries: BudgetComplexDataEntries[];
    expenditureEntries: BudgetComplexDataEntries[];
    summary: Database['budget']['Tables']['budget_summaries']['Row'] | null;
}

export interface BudgetDataQueryResponse {
    id: string;
    budget_item_id: string;
    type: 'INCOME' | 'EXPENDITURE';
    proposed_rate: number;
    quantity: number;
    sub_total: number;
    remarks: string | null;
    created_at: string;
    budget_items: {
        name: string;
        section_id?: string;
    };
}

export interface BudgetSectionResponse {
    id: string;
    name: string;
    type: 'INCOME' | 'EXPENDITURE';
    sequence: number;
    created_at: string;
}

export type budgetSectionsData = {
    id: string;
    name?: string;
    type?: string;
    sequence?: string;
}

export interface BudgetChildRef {
    detailsubmit?: (callFromParent?: boolean) => Promise<void>;
    itemsubmit?: (callFromParent?: boolean) => Promise<void>;
}