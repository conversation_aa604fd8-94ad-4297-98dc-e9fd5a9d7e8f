import { Database } from "@/types/database.types";

// Core approval workflow types based on database schema
export interface ApprovalWorkflow {
  id: string;
  name: string;
  description?: string;
  module_type: string;
  priority: number;
  conditions: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ApprovalStage {
  id: string;
  workflow_id: string;
  stage_order: number;
  stage_name: string;
  description?: string;
  approver_type: 'user' | 'role' | 'department' | 'dynamic';
  approver_value: Record<string, any>;
  is_parallel: boolean;
  auto_approve_days?: number;
  skip_conditions?: Record<string, any>;
}

export interface ApprovalRequest {
  id: string;
  request_number: string;
  module_type: string;
  module_record_id: string;
  workflow_id: string;
  current_stage_id?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  submitted_by: string;
  submitted_at: string;
  completed_at?: string;
  metadata: Record<string, any>;
}

export interface ApprovalAction {
  id: string;
  request_id: string;
  stage_id: string;
  action: 'approve' | 'reject';
  comments?: string;
  actioned_by: string;
  actioned_by_name: string;
  actioned_at: string;
}

export type approvalActionHistory = (Database['approval']['Tables']['approval_actions']['Row'] & {
    version?: number; // for budget
})

// Current approver information for parallel support
export interface CurrentApprover {
  user_id: string;
  user_name: string;
  user_email: string;
  approver_type: string;
  stage_id: string;
  stage_name: string;
  has_acted: boolean;
  action_taken?: string;
  action_date?: string;
  comments?: string;
}

// Delegation information
export interface ActiveDelegation {
  delegator_id: string;
  delegator_name: string;
  delegate_id: string;
  delegate_name: string;
  start_date: string;
  end_date: string;
  reason?: string;
}

// Extended interfaces for table display with joined data
export interface ApprovalRequestWithDetails extends ApprovalRequest {
  workflow?: ApprovalWorkflow;
  current_stage?: ApprovalStage;
  submitted_by_user?: {
    id: string;
    email: string;
    name?: string;
  };
  actions?: ApprovalAction[];
  workflow_stages?: ApprovalStage[];
  // Enhanced parallel approval support
  current_approvers?: CurrentApprover[];
  parallel_approvals_received?: number;
  parallel_approvals_required?: number;
  current_stage_is_parallel?: boolean;
  active_delegations?: ActiveDelegation[];
  recent_actions?: any[];
  urgency_status?: 'OVERDUE' | 'DUE_SOON' | 'AUTO_APPROVE_SOON' | 'NORMAL';
}

// Table-specific types for listing page
export interface ApprovalTableRow {
  id: string;
  request_number: string;
  module_type: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  submitted_by: string;
  submitted_by_name?: string;
  submitted_at: string;
  priority: number;
  data_summary?: string;
}

// Filter and search types
export interface ApprovalFilters {
  status?: string[];
  module_type?: string[];
  date_range?: {
    from: string;
    to: string;
  };
  search?: string;
}

// API response types
export interface ApprovalListResponse {
  data: ApprovalTableRow[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ApprovalDetailsResponse {
  data: ApprovalRequestWithDetails;
}

// Action types for table operations
export interface ApprovalDecision {
  requestId: string;
  action: 'approve' | 'reject';
  comments?: string;
}

export interface ApprovalActionResult {
  success: boolean;
  error?: string;
  data?: ApprovalAction;
}

// Column visibility and table state types
export interface ApprovalTableState {
  sorting: Array<{
    id: string;
    desc: boolean;
  }>;
  columnFilters: Array<{
    id: string;
    value: any;
  }>;
  columnVisibility: Record<string, boolean>;
  rowSelection: Record<string, boolean>;
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
}

// Constants for dropdown options
export const APPROVAL_STATUSES = [
  { label: 'Pending', value: 'pending' },
  { label: 'Approved', value: 'approved' },
  { label: 'Rejected', value: 'rejected' },
  { label: 'Cancelled', value: 'cancelled' },
] as const;

export const MODULE_TYPES = [
  { label: 'Budget', value: 'budget' },
  { label: 'Expense', value: 'expense' },
  { label: 'Leave', value: 'leave' },
  { label: 'Programme', value: 'programme' },
  { label: 'Application', value: 'application' },
] as const;

export type ApprovalStatus = typeof APPROVAL_STATUSES[number]['value'];
export type ModuleType = typeof MODULE_TYPES[number]['value'];