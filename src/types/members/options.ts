import { Database } from "@/types/database.types";

export type option_type = Database['system']['Enums']['option_type']

interface Option {
  code: string
  label: string
}

interface IndustryOption {
  id: string
  name: string
  group: string
}

export interface InstituteOption {
  id: string;
  name: string;
  country: string;
}

export interface InstituteCourseOption {
  id: string;
  name: string;
  category: string;
}

export interface SystemOptions {
  countries: Option[]
  salutations: Option[]
  idTypes: Option[]
  languages: Option[]
  institute: InstituteOption[]
  instituteCourse: InstituteCourseOption[]
  industries: IndustryOption[]
}