import { Database } from "@/types/database.types"

// config type
export interface TabConfig {
  summary: boolean
  profile: boolean
  company: boolean
  workExperience: boolean
  educationDetails: boolean
  companyNominees: boolean
  awards: boolean
  projectDetails: boolean
  documents: boolean
  points: boolean
  applicationHistory: boolean
  auditLogs: boolean
  activities: boolean
  criminalRecords: boolean
  sponsorship: boolean
}

export interface EditPermissionConfig {
  personalInformation: boolean
  companyInformation: boolean
  contactDetails: boolean
  employmentDetails: boolean
  workExperience: boolean
  educationDetails: boolean
  companyNominees: boolean
  awards: boolean
  projectDetails: boolean
  documents: boolean
  criminalRecords: boolean
  sponsorship: boolean
}

// other
type membership = Database['public']['Tables']['memberships']['Row']
type application = Database['public']['Tables']['applications']['Row']
type profile = Database['public']['Tables']['profiles']['Row']
type employment = Database['public']['Tables']['employments']['Row']
type education = Database['public']['Tables']['educations']['Row']
type workExperience = Database['public']['Tables']['working_experiences']['Row']
type award = Database['public']['Tables']['awards']['Row']
type project = Database['public']['Tables']['projects']['Row']
type criminalRecord = Database['public']['Tables']['criminal_records']['Row']
type sponsorship = Database['public']['Tables']['sponsorships']['Row']
type nominee = Database['public']['Tables']['nominees']['Row']

export interface DocumentDisplay {
  id: string
  name: string
  type: string
  uploadDate: string
  size: string
  path: string
}

export interface PersonalInfo extends profile {
  address: Address
  industries: {
    name: string,
    group: string
  }
}

export interface Address {
  line1?: string
  line2?: string | null
  line3?: string | null
  city?: string | null
  state?: string | null
  postalCode?: string
  country?: string
}

export interface CompanyDetails {
  companyName: string
  telephone?: string
  email?: string
  address: Address
  isSameAddress?: boolean
  preferredAddress?: Address
}

export interface EmploymentDetails extends employment {
  company: {
    name: string
    phone: string,
    email: string,
    isSameAddress: boolean,
    address: Address,
    preferredAddress?: Address
  }
}

export interface WorkExperienceDetails extends workExperience {
  company: {
    name: string
  },
  address: Address
}

export interface EducationDetails extends education {
  institute: {
    name?: string;
    country?: string;
  }
  instituteCourse: {
    name?: string;
    category?: string;
  }
}

export interface AwardDetails extends award { }

export interface ProjectDetails extends project {
  company: {
    name: string
  },
  address: Address
}

export interface criminalRecordDetails extends criminalRecord { }

export interface sponsorshipDetails extends sponsorship { }

export interface NomineeDetails extends nominee {
  education?: EducationDetails;
  award?: AwardDetails;
}

export interface ProfileDetials {
  profileId: string
  personalInfo: PersonalInfo
  employment: EmploymentDetails | null
  education: EducationDetails[]
  workExperience: WorkExperienceDetails[]
  nominees: NomineeDetails[]
  projects: ProjectDetails[]
  criminalRecords: criminalRecordDetails
  awards: AwardDetails[]
  sponsorship: sponsorshipDetails[]
  documents: DocumentDisplay[]
}

export interface Member extends ProfileDetials {
  applications: application[],
  memberships: membership & {
    membershipType: {
      name: string
      group: string
    }
  },
  accountInfo?: {
    hasAccount: boolean;
    status?: boolean;
    lastSignInAt?: string;
    email?: string;
  }
}