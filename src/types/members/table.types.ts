import { z } from "zod"

export const MembershipSchema = z.object({
    membership_id: z.string().nullable(),
    membership_type_name: z.string().nullable(),
    membership_status: z.string().nullable(),
    membership_number: z.string().nullable(),
    start_date: z.string().nullable(),
    end_date: z.string().nullable(),
    member_since: z.string().nullable(),
    name: z.string().nullable(),
})

export type Membership = z.infer<typeof MembershipSchema>

// filters
export const statuses = [
    {
        value: "ACTIVE",
        label: "Active",
    },
    {
        value: "INACTIVE",
        label: "Inactive",
    },
    {
        value: "SUSPENDED",
        label: "Suspended",
    },
    {
        value: "EXPIRED",
        label: "Expired",
    },
    {
        value: "TERMINATED",
        label: "Terminated",
    },
    {
        value: "PENDING_RENEWAL",
        label: "Pending Renewal",
    },
    {
        value: "PENDING_RE_APPLICATION",
        label: "Pending Re Application",
    },
];