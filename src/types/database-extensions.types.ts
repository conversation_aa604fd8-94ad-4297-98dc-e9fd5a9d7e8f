import type { Database } from './database.types'

// Extended budget_revisions type with missing properties
export type ExtendedBudgetRevision = Database['budget']['Tables']['budget_revisions']['Row'] & {
  is_archived?: boolean
  entity_type?: string
  entity_id?: string | null
  entity_metadata?: Record<string, any>
}

// Extended parent_budget_revisions view type
export type ExtendedParentBudgetRevision = Database['budget']['Views']['parent_budget_revisions']['Row'] & {
  is_archived?: boolean
  entity_type?: string
  entity_id?: string | null
  entity_metadata?: Record<string, any>
}

// Extended programmes table type with is_archived
export type ExtendedProgramme = Database['programme']['Tables']['programmes']['Row'] & {
  is_archived?: boolean
  secondary_committee_id?: string | null
}

// Extended pricing type with price_types join
export type ExtendedProgrammePricing = Database['programme']['Tables']['pricing']['Row'] & {
  pricing_memberships?: any[] // Since pricing_memberships table doesn't exist
}

// Type for ProgrammePricingWithType that includes joined data
export type ProgrammePricingWithType = ExtendedProgrammePricing & {
  // Properties from joined price_types table
  name?: string
  is_active?: boolean
  requires_membership?: boolean
}

// Missing database schemas/views types
export type ApprovalAction = {
  id: string
  name: string
  description?: string | null
  created_at?: string | null
  updated_at?: string | null
}

export type InvoiceDetails = {
  id: string
  invoice_id: string
  // Add other properties as needed
}

// Since programmes_with_details view doesn't exist, we'll define it based on programme tables
export type ProgrammesWithDetails = Database['programme']['Tables']['programmes']['Row'] & {
  // Add any additional properties that would be in the view
  programme_code?: string | null
  programme_name?: string | null
  status?: string | null
  type?: string | null
  max_participants?: number | null
  registration_start_date?: string | null
  registration_end_date?: string | null
}

export type ProcurementTypes = {
  // Add procurement-related types here
}