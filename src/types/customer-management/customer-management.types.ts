// Customer Management Module Types
// Based on the procurement schema from the implementation guide

export type CustomerStatus = 
  | 'ACTIVE' 
  | 'INACTIVE' 
  | 'SUSPENDED' 
  | 'BLACKLISTED';

export type CustomerType = 
  | 'INDIVIDUAL'
  | 'COMPANY'
  | 'GOVERNMENT'
  | 'NON_PROFIT'
  | 'EDUCATIONAL';

export type CustomerCategory = 
  | 'REGULAR'
  | 'VIP'
  | 'CORPORATE'
  | 'RESELLER'
  | 'DISTRIBUTOR';

export interface Customer {
  id: string;
  customer_code: string;
  name: string;
  display_name?: string;
  registration_number?: string;
  tax_registration_number?: string;
  customer_type: CustomerType;
  category?: CustomerCategory;
  status: CustomerStatus;
  email: string;
  phone?: string;
  website?: string;
  billing_address?: Address;
  shipping_addresses?: Address[];
  payment_terms?: string;
  credit_limit?: number;
  is_gst_registered: boolean;
  contact_persons?: ContactPerson[];
  created_at: string;
  updated_at: string;
}

export interface Address {
  street_address: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
}

export interface ContactPerson {
  id?: string; // Optional for new records, required for updates
  name: string;
  title?: string;
  email: string;
  phone?: string;
  is_primary: boolean;
}

// Form types for creating/editing
export interface CustomerFormData {
  customer_code?: string;
  name: string;
  display_name?: string;
  registration_number?: string;
  tax_registration_number?: string;
  customer_type: CustomerType;
  category?: CustomerCategory;
  status: CustomerStatus;
  email: string;
  phone?: string;
  website?: string;
  billing_address?: Address | null;
  shipping_addresses?: Address[] | null;
  payment_terms?: string | null;
  credit_limit?: number | null;
  is_gst_registered: boolean;
  contact_persons?: ContactPersonFormData[];
}

export interface ContactPersonFormData {
  id?: string;
  name: string;
  title?: string;
  email: string;
  phone?: string;
  is_primary: boolean;
}

// Table and filter types
export interface CustomersTableFilters {
  status: CustomerStatus[];
  customer_types: CustomerType[];
  categories: CustomerCategory[];
}

// Status Management types
export interface StatusChangeRequest {
  customerId: string;
  status: CustomerStatus;
  reason?: string;
}

export interface BulkStatusChangeRequest {
  customerIds: string[];
  status: CustomerStatus;
  reason?: string;
}

export interface StatusHistoryItem {
  status: CustomerStatus;
  changed_at: string;
  changed_by?: string;
  reason?: string;
}

// API Response types
export interface CustomersResponse {
  customers: Customer[] | null;
  success: boolean;
  total: number;
  currentPage: number;
  allCustomerIds: string[];
}

// Summary/Stats types
export interface CustomersSummary {
  total_count: number;
  active_count: number;
  vip_count: number;
  total_credit_limit: number;
}

// Performance tracking types
export interface CustomerPerformance {
  totalOrders: number;
  totalAmount: number;
  onTimePaymentRate: number;
  averagePaymentDays: number;
  lastOrderDate?: string;
  totalInvoices: number;
  outstandingAmount: number;
}

// Dropdown option types
export interface DropdownOption {
  value: string;
  label: string;
}