import { ValidationRule } from "react-hook-form";

export enum FormSectionType {
    PERSONAL = 'PERSONAL',
    COMPANY = 'COMPANY',
    EMPLOYMENT = 'EMPLOYMENT',
    EDUCATION = 'EDUCATION',
    WORK_EXPERIENCE = 'WORK_EXPERIENCE',
    INDUSTRY = 'INDUSTRY',
    AWARDS = 'AWARDS',
    DOCUMENTS = 'DOCUMENTS',
    DECLARATION = 'DECLARATION',
    SPONSORSHIP = 'SPONSORSHIP',
    COMPANY_NOMINEES = 'COMPANY_NOMINEES',
    CRIMINAL_RECORD = 'CRIMINAL_RECORD',
    POST_GRAD_EXPERIENCE = 'POST_GRAD_EXPERIENCE'
}

export interface FormSection {
    name: string;
    type: FormSectionType;
    order_number: number;
    is_required: boolean;
    is_visible: boolean;
    validation_rules: Record<string, ValidationRule[]>;
}
