// Attendance tracking types based on the database schema
// Generated based on migration file: 20241125000003_attendance_tracking_schema.sql

export type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused' | 'partial';
export type CheckInMethod = 'qr_code' | 'manual' | 'auto' | 'facial_recognition';
export type SessionType = 'regular' | 'workshop' | 'breakout' | 'keynote' | 'networking';
export type CompletionStatus = 'incomplete' | 'completed' | 'partially_completed' | 'withdrawn';

// Core attendance record interface
export interface AttendanceRecord {
  id: string;
  participant_id: string;
  programme_id: string;
  schedule_id?: string;
  qr_code_id?: string;
  check_in_time: string | null; // ISO timestamp - nullable
  check_out_time?: string; // ISO timestamp
  attendance_status: AttendanceStatus;
  check_in_method: CheckInMethod;
  check_in_location?: string;
  check_in_device_info?: Record<string, any>;
  checked_in_by?: string; // User ID
  checked_out_by?: string; // User ID
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Session-specific attendance interface
export interface SessionAttendance {
  id: string;
  attendance_record_id: string;
  participant_id: string;
  programme_id: string;
  schedule_id: string;
  session_date: string; // DATE format YYYY-MM-DD
  session_name?: string;
  session_type: SessionType;
  is_present: boolean;
  arrival_time?: string; // ISO timestamp
  departure_time?: string; // ISO timestamp
  duration_minutes?: number; // Generated column
  participation_score?: number; // 0-100
  engagement_notes?: string;
  created_at: string;
  updated_at: string;
}

// Attendance summary for participants
export interface AttendanceSummary {
  id: string;
  participant_id: string;
  programme_id: string;
  registration_id: string;
  total_sessions: number;
  attended_sessions: number;
  attendance_percentage?: number; // Generated column
  total_duration_minutes: number;
  first_attendance?: string; // ISO timestamp
  last_attendance?: string; // ISO timestamp
  completion_status: CompletionStatus;
  completion_date?: string; // DATE format
  certificate_eligible: boolean;
  certificate_issued: boolean;
  certificate_issued_date?: string; // ISO timestamp
  certificate_number?: string;
  created_at: string;
  updated_at: string;
}

// Programme attendance configuration
export interface AttendanceConfiguration {
  id: string;
  programme_id: string;
  minimum_attendance_percentage: number; // Hidden in UI, default 80.00
  late_arrival_threshold_minutes: number; // Hidden in UI, default 15
  early_departure_threshold_minutes: number; // Hidden in UI, default 15
  certificate_minimum_attendance: number; // Hidden in UI, default 80.00
  certificate_minimum_sessions?: number; // Hidden in UI
  allow_self_checkin: boolean; // Visible in UI, default true
  require_checkout: boolean; // Hidden in UI, default false
  checkin_starts_minutes_before: number; // Hidden in UI, default 30
  checkin_ends_minutes_after: number; // Hidden in UI, default 30
  qr_code_enabled: boolean; // Visible in UI, default true
  qr_code_refresh_interval_minutes: number; // Hidden in UI, default 60
  created_at: string;
  updated_at: string;
}

// Form data interfaces for API calls
export interface CheckInRequest {
  participant_id: string;
  programme_id: string;
  schedule_id?: string;
  qr_code_id?: string;
  check_in_method: CheckInMethod;
  check_in_location?: string;
  check_in_device_info?: Record<string, any>;
  notes?: string;
}

export interface CheckOutRequest {
  attendance_record_id?: string; // Either this OR participant_id + programme_id
  participant_id?: string; // Used to find today's check-in record
  programme_id?: string; // Used to find today's check-in record
  check_out_time?: string; // If not provided, uses current time
  notes?: string;
}

export interface BulkCheckInRequest {
  participant_ids: string[];
  programme_id: string;
  schedule_id?: string;
  check_in_method: CheckInMethod;
  check_in_location?: string;
  notes?: string;
}

// UI-specific interfaces
export interface AttendanceStatusSummary {
  total: number;
  present: number;
  absent: number;
  late: number;
  excused: number;
  partial: number;
  percentage: number;
}

export interface ParticipantAttendanceData {
  participant_id: string;
  name: string;
  email: string;
  latest_attendance?: AttendanceRecord;
  attendance_summary?: AttendanceSummary;
  current_status: AttendanceStatus;
  last_check_in?: string;
  attendance_percentage: number;
  certificate_eligible: boolean;
  total_sessions: number;
  attended_sessions: number;
}

// Extended participant interface with attendance data
export interface ParticipantWithAttendance {
  // Base participant data
  id: string;
  participant_id: string;
  name: string;
  email: string;
  contact_no?: string;
  salutation?: string;
  identification_type?: string;
  identification_no?: string;
  nationality?: string;
  registration_type?: string;
  registration_status?: string;

  // Attendance data
  attendance_records?: AttendanceRecord[];
  attendance_summary?: AttendanceSummary;
  latest_attendance?: AttendanceRecord;
  current_attendance_status: AttendanceStatus;
  attendance_percentage: number;
  certificate_eligible: boolean;
  last_check_in_time?: string;
  can_check_in: boolean;
  can_check_out: boolean;

  //certificate
  certificate_status?: "SENT" | "NOT SENT";
  certificate_no?: string;
  certificate_filename?: string;
}

// For the attendance report view (database view)
export interface AttendanceReportRecord {
  // Participant details
  participant_id: string;
  participant_name: string;
  participant_email: string;
  participant_contact: string;

  // Programme information
  programme_id: string;
  programme_name: string;
  programme_code: string;

  // Registration status
  registration_id: string;
  registration_status: string;
  registration_type: string;

  // Attendance metrics
  total_sessions: number;
  attended_sessions: number;
  attendance_percentage: number;
  total_duration_minutes: number;
  first_attendance?: string;
  last_attendance?: string;

  // Certificate status
  certificate_eligible: boolean;
  certificate_issued: boolean;
  certificate_number?: string;
  completion_status: CompletionStatus;
}

// API response interfaces
export interface AttendanceApiResponse<T> {
  data: T;
  error?: string;
  success: boolean;
}

export interface PaginatedAttendanceResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  success: boolean;
  error?: string;
}

// Real-time subscription payloads
export interface AttendanceUpdatePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  table: 'attendance_records' | 'session_attendance' | 'attendance_summary';
  old_record?: any;
  new_record?: any;
  programme_id: string;
  participant_id?: string;
}

// Export options
export interface AttendanceExportOptions {
  programme_id: string;
  participant_ids?: string[];
  include_summary: boolean;
  include_sessions: boolean;
  include_certificate_status: boolean;
  format: 'csv' | 'excel' | 'pdf';
  date_range?: {
    start: string;
    end: string;
  };
}

// API Response types
export interface AttendanceStatsResponse {
  total_participants: number;
  checked_in_count: number;
  attendance_rate: number;
  average_attendance_percentage: number;
  certificate_eligible_count: number;
}

export interface AttendanceHistoryItem {
  id: string;
  date: string;
  session_name: string | null;
  check_in_time: string | null;
  check_out_time: string | null;
  duration_minutes: number | null;
  attendance_status: AttendanceStatus;
  check_in_method: CheckInMethod;
  notes: string | null;
}

// Certificate eligibility check
export interface CertificateEligibilityResult {
  participant_id: string;
  programme_id: string;
  is_eligible: boolean;
  attendance_percentage: number;
  required_percentage: number;
  sessions_attended: number;
  required_sessions?: number;
  missing_requirements: string[];
}

// Additional types for hooks and components
export interface AttendanceConfigFormData {
  programme_id: string;
  allow_self_checkin: boolean;
  qr_code_enabled: boolean;
  // Hidden fields with defaults
  minimum_attendance_percentage?: number;
  certificate_minimum_attendance?: number;
  late_arrival_threshold_minutes?: number;
  checkin_starts_minutes_before?: number;
  checkin_ends_minutes_after?: number;
}

// Type aliases for backward compatibility
export type AttendanceConfigForm = AttendanceConfigFormData;
export type UpdateAttendanceConfig = Partial<AttendanceConfigFormData>;