import { Database } from "@/types/database.types";

export type InvoiceStatus = "DRAFT" | "PENDING_APPROVAL" | "APPROVED" | "APPLIED" | "PARTIALLY_APPLIED" | "EXPIRED" | "CANCELLED";
export type PaymentStatus = "UNPAID" | "PARTIAL" | "PAID" | "OVERDUE" | "CANCELLED";
export type CreditReasonEnum = "RETURN" | "PRICING_ADJUSTMENT" | "SERVICE_ISSUE" | "PROMOTIONAL" | "OTHER";
export type ApplicationMethodEnum = "AUTO_APPLY" | "MANUAL" | "HOLD";

export interface DropdownOption {
  value: string;
  label: string;
  code?: string;
  email?: string;
  phone?: string;
  creditBalance?: number;
  outstandingBalance?: number;
}

export interface CreditNote {
  id: string;
  invoice_number: string;
  invoice_type: "CREDIT_NOTE";
  customer_id: string;
  entity_id: string;
  invoice_date: string;
  due_date: string | null;
  total_amount: number;
  status: InvoiceStatus;
  payment_status: PaymentStatus;
  currency_code: string;
  reference_number: string | null;
  notes: string | null;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  
  // Credit note specific fields
  credit_reason?: CreditReasonEnum;
  original_invoice_id?: string | null;
  original_order_id?: string | null;
  expiry_date?: string | null;
  application_method?: ApplicationMethodEnum;
  application_restrictions?: string | null;
  unapplied_amount?: number;
  
  // Relationships
  customer?: CustomerInfo;
  original_invoice?: InvoiceInfo;
  items?: CreditNoteItem[];
  applications?: CreditNoteApplication[];
}

export interface CreditNoteItem {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  discount_amount: number | null;
  discount_percentage: number | null;
  tax_amount: number;
  tax_percentage: number;
  line_total: number;
  item_code: string | null;
  gl_account_code: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface CreditNoteApplication {
  id: string;
  credit_note_id: string;
  invoice_id: string;
  amount_applied: number;
  application_date: string;
  created_at: string;
  created_by: string;
  
  // Relationships
  invoice?: InvoiceInfo;
}

export interface CustomerInfo {
  id: string;
  customer_code: string;
  name: string;
  display_name: string | null;
  email: string | null;
  phone: string | null;
  credit_balance?: number;
  outstanding_balance?: number;
}

export interface InvoiceInfo {
  id: string;
  invoice_number: string;
  invoice_date: string;
  total_amount: number;
  payment_status: PaymentStatus;
  outstanding_amount?: number;
}

export interface CreditNoteSummary {
  total_count: number;
  total_value: number;
  unapplied_credits: number;
  monthly_credits: number;
  expiring_soon_count: number;
}

export interface CreditNoteFilters {
  search?: string;
  status?: InvoiceStatus[];
  credit_reason?: CreditReasonEnum[];
  customer_id?: string;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  has_unapplied?: boolean;
  expiring_within_days?: number;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface CreateCreditNoteInput {
  customer_id: string;
  entity_id: string;
  invoice_date: string;
  due_date?: string;
  currency_code: string;
  reference_number?: string;
  notes?: string;
  credit_reason: CreditReasonEnum;
  original_invoice_id?: string;
  original_order_id?: string;
  expiry_date?: string;
  application_method: ApplicationMethodEnum;
  application_restrictions?: string;
  items: CreateCreditNoteItemInput[];
}

export interface CreateCreditNoteItemInput {
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  discount_amount?: number;
  discount_percentage?: number;
  tax_percentage: number;
  item_code?: string;
  gl_account_code?: string;
}

export interface ApplyCreditNoteInput {
  credit_note_id: string;
  applications: CreditApplicationInput[];
}

export interface CreditApplicationInput {
  invoice_id: string;
  amount: number;
}

export interface CustomerCreditStatement {
  customer_id: string;
  customer_name: string;
  from_date: string;
  to_date: string;
  opening_balance: number;
  closing_balance: number;
  total_credits_issued: number;
  total_credits_applied: number;
  transactions: CreditTransaction[];
}

export interface CreditTransaction {
  date: string;
  type: "CREDIT_ISSUED" | "CREDIT_APPLIED" | "CREDIT_EXPIRED";
  reference: string;
  description: string;
  amount: number;
  balance: number;
}

export interface CreditNoteExportData {
  credit_notes: CreditNote[];
  summary: CreditNoteSummary;
  export_date: string;
  exported_by: string;
}

// Re-export form data types from schema
export type { CreditNoteFormData, CreditNoteItemFormData } from "@/schemas/credit-notes/credit-note.schema";