import { PurchaseInvoice, SupplierInfo, EntityInfo } from "./purchase-invoices.types";

// Outstanding Payables Report Types
export interface OutstandingPayablesFilters {
  entity_id?: string;
  supplier_id?: string;
  aging_bucket?: string;
  sort_by?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
}

export interface OutstandingPayablesSummary {
  total_outstanding: number;
  overdue_amount: number;
  average_days_outstanding: number;
  total_invoices: number;
  currency_breakdown: { [currency: string]: number };
}

export interface AgingBucket {
  label: string;
  min: number;
  max: number | null;
  color: string;
}

export interface AgingAnalysisItem {
  bucket: string;
  amount: number;
  count: number;
  percentage: number;
}

export interface OutstandingPayablesData {
  summary: OutstandingPayablesSummary;
  aging_analysis: AgingAnalysisItem[];
  invoices: PurchaseInvoice[];
  generated_at: string;
}

// Payment Due Report Types
export interface PaymentDueFilters {
  entity_id?: string;
  supplier_id?: string;
  due_date_from?: string;
  due_date_to?: string;
  priority?: "HIGH" | "MEDIUM" | "LOW";
  payment_method?: string;
  sort_by?: string;
}

export interface PaymentDueGrouping {
  date: string;
  total_amount: number;
  invoice_count: number;
  invoices: PurchaseInvoice[];
}

export interface PaymentDueData {
  summary: {
    total_due_amount: number;
    due_this_week: number;
    due_next_week: number;
    overdue_amount: number;
    total_invoices: number;
  };
  groupings: PaymentDueGrouping[];
  priority_breakdown: {
    high: { amount: number; count: number };
    medium: { amount: number; count: number };
    low: { amount: number; count: number };
  };
  generated_at: string;
}

// Three-Way Match Exception Report Types
export interface ThreeWayMatchExceptionFilters {
  entity_id?: string;
  supplier_id?: string;
  exception_type?: "QUANTITY_VARIANCE" | "PRICE_VARIANCE" | "MISSING_PO" | "MISSING_RECEIPT";
  variance_threshold?: number;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
}

export interface VarianceAnalysis {
  total_variances: number;
  quantity_variances: number;
  price_variances: number;
  amount_variance: number;
  percentage_variance: number;
}

export interface ThreeWayMatchException {
  invoice_id: string;
  invoice_number: string;
  supplier: SupplierInfo;
  exception_type: string;
  variance_amount: number;
  variance_percentage: number;
  status: string;
  created_at: string;
  resolved_at?: string;
  notes?: string;
}

export interface ThreeWayMatchExceptionData {
  summary: {
    total_exceptions: number;
    unresolved_exceptions: number;
    total_variance_amount: number;
    average_variance_percentage: number;
  };
  variance_analysis: VarianceAnalysis;
  exceptions: ThreeWayMatchException[];
  trends: {
    daily_exceptions: { date: string; count: number; amount: number }[];
    exception_types: { type: string; count: number; percentage: number }[];
  };
  generated_at: string;
}

// Supplier Payment History Types
export interface SupplierPaymentHistoryFilters {
  supplier_id?: string;
  entity_id?: string;
  date_from?: string;
  date_to?: string;
  payment_method?: string;
  sort_by?: string;
}

export interface PaymentHistoryItem {
  payment_id: string;
  invoice_id: string;
  invoice_number: string;
  payment_date: string;
  amount: number;
  currency_code: string;
  payment_method: string;
  reference_number?: string;
  early_payment_discount?: number;
  bank_charges?: number;
  net_amount: number;
}

export interface SupplierPaymentStats {
  total_payments: number;
  total_amount: number;
  average_payment_amount: number;
  average_payment_days: number;
  early_payment_discount_total: number;
  on_time_payment_percentage: number;
  preferred_payment_method: string;
}

export interface SupplierPaymentHistoryData {
  supplier: SupplierInfo;
  stats: SupplierPaymentStats;
  payments: PaymentHistoryItem[];
  trends: {
    monthly_payments: { month: string; amount: number; count: number }[];
    payment_timing: { on_time: number; early: number; late: number };
  };
  generated_at: string;
}

// Vendor Performance Metrics Types
export interface VendorPerformanceFilters {
  entity_id?: string;
  date_from?: string;
  date_to?: string;
  performance_metric?: "PAYMENT_TERMS" | "INVOICE_ACCURACY" | "DELIVERY_PERFORMANCE" | "OVERALL";
  sort_by?: string;
  limit?: number;
}

export interface VendorPerformanceMetrics {
  supplier_id: string;
  supplier: SupplierInfo;
  
  // Payment Performance
  average_payment_days: number;
  on_time_payment_percentage: number;
  early_payment_discount_utilization: number;
  
  // Invoice Accuracy
  invoice_accuracy_percentage: number;
  three_way_match_success_rate: number;
  dispute_rate: number;
  
  // Volume Metrics
  total_invoice_value: number;
  total_invoice_count: number;
  average_invoice_value: number;
  
  // Delivery Performance (if linked to POs)
  on_time_delivery_percentage?: number;
  delivery_accuracy_percentage?: number;
  
  // Overall Score
  performance_score: number;
  performance_grade: "A" | "B" | "C" | "D" | "F";
  
  // Risk Indicators
  risk_level: "LOW" | "MEDIUM" | "HIGH";
  risk_factors: string[];
}

export interface VendorPerformanceData {
  summary: {
    total_suppliers: number;
    average_performance_score: number;
    top_performers_count: number;
    at_risk_suppliers_count: number;
  };
  metrics: VendorPerformanceMetrics[];
  benchmarks: {
    payment_days_benchmark: number;
    accuracy_benchmark: number;
    dispute_rate_benchmark: number;
  };
  trends: {
    performance_over_time: { period: string; average_score: number }[];
    top_risk_factors: { factor: string; count: number }[];
  };
  generated_at: string;
}

// Analytics Dashboard Types
export interface AnalyticsDashboardData {
  overview: {
    total_invoice_value: number;
    total_invoice_count: number;
    average_processing_time: number;
    approval_rate: number;
    payment_completion_rate: number;
  };
  
  trends: {
    monthly_invoice_volume: { month: string; value: number; count: number }[];
    payment_trends: { month: string; on_time: number; early: number; late: number }[];
    approval_trends: { month: string; approved: number; rejected: number; pending: number }[];
  };
  
  top_suppliers: {
    by_volume: { supplier: SupplierInfo; total_value: number }[];
    by_count: { supplier: SupplierInfo; total_count: number }[];
    by_performance: { supplier: SupplierInfo; performance_score: number }[];
  };
  
  exceptions_summary: {
    three_way_match_exceptions: number;
    approval_bottlenecks: number;
    payment_delays: number;
    duplicate_invoices: number;
  };
  
  financial_insights: {
    potential_early_payment_savings: number;
    cash_flow_optimization_opportunities: number;
    supplier_concentration_risk: number;
    compliance_score: number;
  };
  
  generated_at: string;
}

// Export Types
export interface ExportOptions {
  format: "excel" | "csv" | "pdf";
  include_charts?: boolean;
  include_summary?: boolean;
  date_range?: {
    from: string;
    to: string;
  };
  filters?: any;
}

export interface ExportResult {
  success: boolean;
  file_url?: string;
  file_name?: string;
  error_message?: string;
}