import { Database } from "@/types/database.types";
import { PaymentMethod, PaymentStatus, PurchaseInvoice } from "./purchase-invoices.types";

export type PaymentBatchStatus = "DRAFT" | "PENDING_APPROVAL" | "APPROVED" | "PROCESSING" | "COMPLETED" | "FAILED" | "CANCELLED";
export type PaymentPriority = "HIGH" | "MEDIUM" | "LOW";
export type PaymentFileFormat = "CSV" | "XML" | "MT940" | "ACH" | "WIRE" | "SWIFT";
export type AuthorizationLevel = "SINGLE" | "DUAL" | "MULTI_LEVEL";
export type CurrencyRateSource = "MANUAL" | "BANK_API" | "CENTRAL_BANK" | "MARKET_DATA";

export interface PaymentBatch {
  id: string;
  batch_number: string;
  batch_name: string;
  supplier_id: string;
  entity_id: string;
  currency_code: string;
  payment_method: PaymentMethod;
  payment_date: string;
  value_date?: string;
  bank_account_id: string;
  total_amount: number;
  invoice_count: number;
  status: PaymentBatchStatus;
  priority: PaymentPriority;
  requires_authorization: boolean;
  authorization_level: AuthorizationLevel;
  created_by: string;
  created_at: string;
  updated_at?: string;
  updated_by?: string;
  authorized_by?: string;
  authorized_at?: string;
  processed_by?: string;
  processed_at?: string;
  reference_number?: string;
  notes?: string;
  
  // Relationships
  supplier?: SupplierInfo;
  entity?: EntityInfo;
  bank_account?: BankAccount;
  invoices?: PaymentInvoiceSelection[];
  authorization_history?: PaymentAuthorizationHistory[];
  processing_log?: PaymentProcessingLog[];
}

export interface PaymentInvoiceSelection {
  id: string;
  batch_id: string;
  invoice_id: string;
  payment_amount: number;
  original_amount: number;
  early_discount_amount: number;
  withholding_tax_amount: number;
  currency_rate: number;
  currency_rate_date: string;
  currency_rate_source: CurrencyRateSource;
  selected: boolean;
  priority: PaymentPriority;
  reference_number?: string;
  notes?: string;
  
  // Relationships
  invoice?: PurchaseInvoice;
}

export interface BankAccount {
  id: string;
  account_name: string;
  account_number: string;
  iban?: string;
  swift_code?: string;
  bank_name: string;
  bank_code?: string;
  branch_name?: string;
  branch_code?: string;
  currency_code: string;
  account_type: "CHECKING" | "SAVINGS" | "MONEY_MARKET" | "FOREIGN_CURRENCY";
  balance: number;
  available_balance: number;
  status: "ACTIVE" | "INACTIVE" | "FROZEN" | "CLOSED";
  daily_limit?: number;
  transaction_limit?: number;
  is_default: boolean;
  entity_id: string;
  created_at: string;
  updated_at?: string;
  
  // Relationships
  entity?: EntityInfo;
}

export interface PaymentApprovalLimit {
  id: string;
  user_id: string;
  role_id?: string;
  entity_id?: string;
  currency_code: string;
  single_payment_limit: number;
  daily_limit: number;
  monthly_limit: number;
  requires_dual_approval: boolean;
  requires_multi_level_approval: boolean;
  authorization_level: AuthorizationLevel;
  is_active: boolean;
  effective_from: string;
  effective_to?: string;
  created_at: string;
  updated_at?: string;
}

export interface PaymentAuthorizationHistory {
  id: string;
  batch_id: string;
  user_id: string;
  user_name: string;
  action: "SUBMITTED" | "APPROVED" | "REJECTED" | "RETURNED" | "CANCELLED";
  authorization_level: number;
  comments?: string;
  ip_address?: string;
  user_agent?: string;
  authorization_date: string;
}

export interface PaymentProcessingLog {
  id: string;
  batch_id: string;
  step: "VALIDATION" | "AUTHORIZATION" | "BANK_SUBMISSION" | "CONFIRMATION" | "COMPLETION" | "ERROR";
  status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  message: string;
  error_code?: string;
  error_details?: any;
  processing_time_ms?: number;
  created_at: string;
}

export interface CurrencyRate {
  id: string;
  from_currency: string;
  to_currency: string;
  rate: number;
  inverse_rate: number;
  source: CurrencyRateSource;
  rate_date: string;
  valid_from: string;
  valid_to: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface PaymentFileTemplate {
  id: string;
  name: string;
  format: PaymentFileFormat;
  bank_name: string;
  bank_code?: string;
  file_extension: string;
  header_template: string;
  row_template: string;
  footer_template: string;
  validation_rules: any;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface PaymentFile {
  id: string;
  batch_id: string;
  file_name: string;
  file_format: PaymentFileFormat;
  file_size: number;
  file_path: string;
  file_hash: string;
  record_count: number;
  total_amount: number;
  currency_code: string;
  status: "GENERATED" | "TRANSMITTED" | "ACKNOWLEDGED" | "PROCESSED" | "FAILED";
  generated_by: string;
  generated_at: string;
  transmitted_at?: string;
  acknowledged_at?: string;
  processed_at?: string;
  error_message?: string;
}

// Filter and search interfaces
export interface PaymentBatchFilters {
  search?: string;
  status?: PaymentBatchStatus[];
  payment_method?: PaymentMethod[];
  supplier_id?: string;
  entity_id?: string;
  currency_code?: string;
  bank_account_id?: string;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  priority?: PaymentPriority[];
  requires_authorization?: boolean;
  authorized_by?: string;
  created_by?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface PaymentSummary {
  total_batches: number;
  total_amount: number;
  total_invoices: number;
  pending_authorization: number;
  processing_count: number;
  completed_count: number;
  failed_count: number;
  currency_breakdown: { [currency: string]: number };
  method_breakdown: { [method: string]: number };
  daily_processed_amount: number;
  monthly_processed_amount: number;
}

// Input interfaces for creating/updating payments
export interface CreatePaymentBatchInput {
  batch_name: string;
  supplier_id?: string;
  entity_id: string;
  currency_code: string;
  payment_method: PaymentMethod;
  payment_date: string;
  value_date?: string;
  bank_account_id: string;
  priority: PaymentPriority;
  reference_number?: string;
  notes?: string;
  invoice_selections: CreatePaymentInvoiceSelectionInput[];
}

export interface CreatePaymentInvoiceSelectionInput {
  invoice_id: string;
  payment_amount: number;
  apply_early_discount: boolean;
  withholding_tax_amount?: number;
  currency_rate?: number;
  reference_number?: string;
  notes?: string;
}

export interface ProcessPaymentBatchInput {
  batch_id: string;
  payment_date?: string;
  value_date?: string;
  bank_account_id?: string;
  authorization_comments?: string;
  force_processing?: boolean;
}

export interface AuthorizePaymentBatchInput {
  batch_id: string;
  action: "APPROVE" | "REJECT" | "RETURN";
  comments?: string;
  authorization_level: number;
}

export interface GeneratePaymentFileInput {
  batch_ids: string[];
  file_format: PaymentFileFormat;
  template_id: string;
  include_header: boolean;
  include_footer: boolean;
  file_name?: string;
}

// Response interfaces
export interface PaymentBatchResponse {
  batch: PaymentBatch;
  validation_errors?: string[];
  warnings?: string[];
}

export interface PaymentFileResponse {
  file: PaymentFile;
  download_url: string;
  expires_at: string;
}

// Validation interfaces
export interface PaymentValidationRule {
  id: string;
  name: string;
  description: string;
  rule_type: "AMOUNT" | "CURRENCY" | "BANK_ACCOUNT" | "AUTHORIZATION" | "BUSINESS_RULE";
  validation_logic: any;
  error_message: string;
  warning_message?: string;
  is_active: boolean;
  severity: "ERROR" | "WARNING" | "INFO";
}

export interface PaymentValidationResult {
  batch_id: string;
  is_valid: boolean;
  errors: PaymentValidationError[];
  warnings: PaymentValidationWarning[];
  validation_date: string;
}

export interface PaymentValidationError {
  rule_id: string;
  rule_name: string;
  error_message: string;
  field_name?: string;
  field_value?: any;
  severity: "ERROR" | "WARNING";
}

export interface PaymentValidationWarning {
  rule_id: string;
  rule_name: string;
  warning_message: string;
  field_name?: string;
  field_value?: any;
}

// Bank integration interfaces
export interface BankIntegrationConfig {
  id: string;
  bank_name: string;
  bank_code: string;
  api_endpoint: string;
  api_version: string;
  authentication_type: "API_KEY" | "OAUTH" | "CERTIFICATE" | "BASIC_AUTH";
  authentication_config: any;
  supported_formats: PaymentFileFormat[];
  supported_currencies: string[];
  daily_limit: number;
  transaction_limit: number;
  is_active: boolean;
  test_mode: boolean;
}

export interface BankTransactionStatus {
  transaction_id: string;
  bank_reference: string;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED" | "RETURNED";
  status_message: string;
  amount: number;
  currency_code: string;
  value_date: string;
  processing_date?: string;
  completion_date?: string;
  error_code?: string;
  error_message?: string;
}

// Additional utility types
export interface SupplierInfo {
  id: string;
  supplier_code: string;
  name: string;
  display_name: string | null;
  email: string | null;
  phone: string | null;
  payment_terms: number | null;
  preferred_payment_method: PaymentMethod | null;
  bank_details: any;
  status: string;
}

export interface EntityInfo {
  id: string;
  name: string;
  code: string;
  currency_code: string;
  country_code: string;
  tax_id: string;
}

export interface UserInfo {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
}