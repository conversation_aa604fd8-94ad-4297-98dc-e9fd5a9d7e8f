// Purchase Invoice types
// Since procurement schema doesn't exist in database, we define these types directly

export type PurchaseInvoiceStatus = "DRAFT" | "PENDING" | "APPROVED" | "PAID" | "CANCELLED" | "REJECTED" | "SUBMITTED" | "RECEIVED" | "PENDING_APPROVAL" | "DISPUTED";
export type PaymentStatus = "UNPAID" | "PARTIALLY_PAID" | "PAID" | "OVERDUE" | "PENDING" | "COMPLETED" | "FAILED" | "CANCELLED";
export type PaymentMethod = "BANK_TRANSFER" | "CHEQUE" | "CASH" | "ONLINE" | "CREDIT_CARD";
export type MatchStatus = "MATCHED" | "PARTIALLY_MATCHED" | "NOT_MATCHED" | "DISPUTED";

export interface PurchaseInvoice {
  id: string;
  invoice_number: string;
  supplier_id: string;
  entity_id: string;
  purchase_order_id: string | null;
  invoice_date: string;
  due_date: string | null;
  received_date: string | null;
  total_amount: number;
  tax_amount: number;
  discount_amount: number | null;
  status: PurchaseInvoiceStatus;
  payment_status: PaymentStatus;
  currency_code: string;
  reference_number: string | null;
  supplier_reference: string | null;
  notes: string | null;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  approved_at: string | null;
  approved_by: string | null;
  
  // Computed fields
  outstanding_amount?: number;
  days_overdue?: number;
  match_status?: MatchStatus;
  early_payment_discount?: number;
  
  // Relationships
  supplier?: SupplierInfo;
  entity?: EntityInfo;
  purchase_order?: PurchaseOrderInfo;
  items?: PurchaseInvoiceItem[];
  payments?: PaymentInfo[];
  three_way_match?: ThreeWayMatchResult;
  approval_history?: ApprovalHistoryItem[];
  documents?: DocumentInfo[];
}

export interface PurchaseInvoiceItem {
  id: string;
  invoice_id: string;
  po_item_id: string | null;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  discount_amount: number | null;
  discount_percentage: number | null;
  tax_amount: number;
  tax_percentage: number;
  line_total: number;
  item_code: string | null;
  gl_account_code: string | null;
  cost_center: string | null;
  created_at: string;
  updated_at: string | null;
  
  // Three-way match fields
  po_quantity?: number;
  received_quantity?: number;
  variance_quantity?: number;
  variance_amount?: number;
}

export interface SupplierInfo {
  id: string;
  supplier_code: string;
  name: string;
  display_name: string | null;
  email: string | null;
  phone: string | null;
  payment_terms: number | null;
  status: string;
}

export interface EntityInfo {
  id: string;
  name: string;
  code: string;
}

export interface PurchaseOrderInfo {
  id: string;
  po_number: string;
  po_date: string;
  total_amount: number;
  status: string;
  delivery_date: string | null;
}

export interface PaymentInfo {
  id: string;
  payment_number: string;
  payment_date: string;
  amount: number;
  payment_method: PaymentMethod;
  reference_number: string | null;
  status: PaymentStatus;
  bank_details: any;
  batch_id?: string;
  early_discount_amount?: number;
  withholding_tax_amount?: number;
  currency_rate?: number;
  value_date?: string;
}

export interface ThreeWayMatchResult {
  invoice_id: string;
  po_id: string | null;
  gr_id: string | null;
  match_status: MatchStatus;
  total_variance: number;
  quantity_variance: number;
  price_variance: number;
  tax_variance: number;
  tolerance_exceeded: boolean;
  requires_approval: boolean;
  match_date: string | null;
  matched_by: string | null;
  notes: string | null;
  line_matches: ThreeWayMatchLineResult[];
}

export interface ThreeWayMatchLineResult {
  line_number: number;
  description: string;
  invoice_quantity: number;
  invoice_price: number;
  po_quantity: number | null;
  po_price: number | null;
  received_quantity: number | null;
  quantity_variance: number;
  price_variance: number;
  total_variance: number;
  match_status: MatchStatus;
  tolerance_status: "WITHIN" | "EXCEEDED" | "CRITICAL";
}

export interface ApprovalHistoryItem {
  id: string;
  action: "SUBMITTED" | "APPROVED" | "REJECTED" | "RETURNED";
  approver_id: string;
  approver_name: string;
  approval_date: string;
  comments: string | null;
  approval_level: number;
}

export interface DocumentInfo {
  id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  file_url: string;
  uploaded_at: string;
  uploaded_by: string;
}

export interface PurchaseInvoiceSummary {
  total_count: number;
  outstanding_amount: number;
  overdue_count: number;
  pending_approval_count: number;
  this_month_amount: number;
  average_processing_days: number;
}

export interface PurchaseInvoiceFilters {
  search?: string;
  status?: PurchaseInvoiceStatus[];
  payment_status?: PaymentStatus[];
  supplier_id?: string;
  entity_id?: string;
  purchase_order_id?: string;
  date_from?: string;
  date_to?: string;
  due_date_from?: string;
  due_date_to?: string;
  amount_min?: number;
  amount_max?: number;
  is_overdue?: boolean;
  requires_three_way_match?: boolean;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface CreatePurchaseInvoiceInput {
  supplier_id: string;
  entity_id: string;
  purchase_order_id?: string;
  invoice_number: string;
  supplier_reference?: string;
  invoice_date: string;
  due_date?: string;
  currency_code: string;
  reference_number?: string;
  notes?: string;
  items: CreatePurchaseInvoiceItemInput[];
  perform_three_way_match?: boolean;
}

export interface CreatePurchaseInvoiceItemInput {
  po_item_id?: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  discount_amount?: number;
  discount_percentage?: number;
  tax_percentage: number;
  item_code?: string;
  gl_account_code?: string;
  cost_center?: string;
}

export interface ProcessPaymentInput {
  invoice_ids: string[];
  payment_date: string;
  payment_method: PaymentMethod;
  reference_number?: string;
  bank_details?: any;
  notes?: string;
  apply_early_payment_discount?: boolean;
  batch_name?: string;
  bank_account_id?: string;
  value_date?: string;
  priority?: "HIGH" | "MEDIUM" | "LOW";
}

export interface PaymentAllocationInput {
  invoice_id: string;
  amount: number;
  discount_amount?: number;
}

export interface SupplierStatementLine {
  date: string;
  type: "INVOICE" | "PAYMENT" | "CREDIT_NOTE" | "ADJUSTMENT";
  reference: string;
  description: string;
  debit_amount: number;
  credit_amount: number;
  balance: number;
}

export interface SupplierStatement {
  supplier_id: string;
  supplier_name: string;
  statement_date: string;
  from_date: string;
  to_date: string;
  opening_balance: number;
  closing_balance: number;
  total_invoices: number;
  total_payments: number;
  total_adjustments: number;
  lines: SupplierStatementLine[];
}

export interface PurchaseInvoiceExportData {
  invoices: PurchaseInvoice[];
  summary: PurchaseInvoiceSummary;
  export_date: string;
  exported_by: string;
}

export interface ToleranceSettings {
  quantity_tolerance_percentage: number;
  price_tolerance_percentage: number;
  total_tolerance_amount: number;
  auto_approve_within_tolerance: boolean;
  require_approval_above_tolerance: boolean;
}

export interface VendorPerformanceMetrics {
  supplier_id: string;
  supplier_name: string;
  total_invoices: number;
  total_amount: number;
  average_processing_days: number;
  on_time_delivery_rate: number;
  price_variance_percentage: number;
  quality_score: number;
  payment_terms_compliance: number;
}

export interface PaymentSchedule {
  due_date: string;
  amount: number;
  description: string;
  status: "PENDING" | "PAID" | "OVERDUE";
  early_payment_discount?: number;
  discount_date?: string;
}

export interface ReconciliationSummary {
  statement_date: string;
  supplier_id: string;
  supplier_name: string;
  statement_balance: number;
  system_balance: number;
  variance: number;
  reconciled_items: number;
  pending_items: number;
  disputed_items: number;
}