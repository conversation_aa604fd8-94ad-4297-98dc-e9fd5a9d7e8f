import type { ProfileDetials } from "@/types/members/member-details"

export interface ApplicationInterface {
    application_id: string,
    reference_no: string,
    membership_type_name: string,
    submitted_at: string,
    application_status: string,
    application_type: string,
    approved_or_rejected_at: string,
    comment: string,
    full_name: string,
}

export interface ApplicationDetail extends ProfileDetials {
    applications: {
        id: string;
        type: string;
        membershipNames: string;
        status: string;
        referenceNo: string;
        createdAt: string;
        submittedAt: string;
        approvedOrRejectedAt: string;
        comment: string;
    };
}

export interface AppointmentDetail {
    scheduled_at: string
    status: string
    location: string
    appointment_type: string
    duration_minutes: number
}

export interface ApplicationSectionsConfig {
    details: boolean
    appointment: boolean
    documents: boolean
}

// filter
export const statuses = [
    {
        value: "SUBMITTED_PENDING_PAYMENT",
        label: "Pending Payment",
    },
    {
        value: "PAID_PENDING_REVIEW",
        label: "Pending Review",
    },
    {
        value: "TO_INTERVIEW",
        label: "Pending Interview",
    },
    {
        value: "DOCUMENT_REQUIRED",
        label: "Documents Needed",
    },
    {
        value: "APPROVED",
        label: "Approved",
    },
    {
        value: "REJECTED",
        label: "Rejected",
    },
    {
        value: "CANCELLED",
        label: "Cancelled",
    },
]

export const types = [
    {
        value: "NEW",
        label: "New",
    },
    {
        value: "RENEWAL",
        label: "Renewal",
    },
]

export const mtype = [
    {
        value: "",
        label: "",
    }
]