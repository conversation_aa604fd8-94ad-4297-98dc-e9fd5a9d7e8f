export enum EmailCampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending',
  SENT = 'sent',
  FAILED = 'failed'
}

export interface EmailContent {
  subject: string;
  body: string;
  bodyHtml?: string;
  attachments?: EmailAttachment[];
  templateId?: string;
  variables?: Record<string, any>;
}

export interface EmailAttachment {
  filename: string;
  url: string;
  size: number;
  mimeType: string;
}

export interface EmailBlast {
  id: string;
  name: string;
  description?: string;
  status: EmailCampaignStatus;
  content: EmailContent;
  recipientListId: string;
  recipientCount?: number;
  sentCount?: number;
  failedCount?: number;
  openCount?: number;
  clickCount?: number;
  scheduledAt?: Date;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface EmailBlastStatistics {
  campaignId: string;
  totalRecipients: number;
  sentCount: number;
  deliveredCount: number;
  openCount: number;
  uniqueOpenCount: number;
  clickCount: number;
  uniqueClickCount: number;
  bounceCount: number;
  unsubscribeCount: number;
  spamReportCount: number;
  failedCount: number;
  lastUpdated: Date;
}

export interface EmailBlastRecipient {
  id: string;
  campaignId: string;
  recipientId: string;
  email: string;
  name: string;
  status: 'pending' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'failed' | 'unsubscribed';
  sentAt?: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  bouncedAt?: Date;
  failedAt?: Date;
  unsubscribedAt?: Date;
  errorMessage?: string;
  metadata?: Record<string, any>;
}