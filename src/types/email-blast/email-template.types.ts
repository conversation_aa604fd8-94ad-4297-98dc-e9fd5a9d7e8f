export enum TemplateCategory {
  NEWSLETTER = 'newsletter',
  PROMOTIONAL = 'promotional',
  ANNOUNCEMENT = 'announcement',
  WELCOME = 'welcome',
  FOLLOWUP = 'followup',
  EVENT = 'event',
  SURVEY = 'survey',
  NOTIFICATION = 'notification',
  CUSTOM = 'custom'
}

export enum TemplateStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived'
}

export interface TemplateVariable {
  id: string;
  name: string;
  key: string;
  description?: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'url' | 'email';
  required: boolean;
  defaultValue?: string;
  placeholder?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    options?: string[];
  };
}

export interface EmailContent {
  subject: string;
  body: string;
  bodyHtml?: string;
  previewText?: string;
  attachments?: EmailAttachment[];
  variables?: Record<string, any>;
}

export interface EmailAttachment {
  filename: string;
  url: string;
  size: number;
  mimeType: string;
}

export interface TemplateUsageStats {
  templateId: string;
  totalUsage: number;
  campaignsUsed: number;
  lastUsedAt?: Date;
  successRate: number;
  avgOpenRate: number;
  avgClickRate: number;
  popularVariables: string[];
  monthlyUsage: Array<{
    month: string;
    count: number;
  }>;
}

export interface EmailTemplate {
  id: string;
  name: string;
  description?: string;
  category: TemplateCategory;
  status: TemplateStatus;
  content: EmailContent;
  variables: TemplateVariable[];
  thumbnail?: string;
  tags?: string[];
  isSystem: boolean;
  version: number;
  parentTemplateId?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  metadata?: Record<string, any>;
  usageStats?: TemplateUsageStats;
}

export interface TemplateListFilters {
  category?: TemplateCategory;
  status?: TemplateStatus;
  tags?: string[];
  search?: string;
  createdBy?: string;
  isSystem?: boolean;
}

export interface TemplateFormData {
  name: string;
  description?: string;
  category: TemplateCategory;
  status: TemplateStatus;
  subject: string;
  body: string;
  bodyHtml?: string;
  previewText?: string;
  variables: TemplateVariable[];
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface TemplateDuplicateOptions {
  name: string;
  description?: string;
  preserveVariables?: boolean;
  copyTags?: boolean;
}

export interface TemplatePreviewData {
  templateId: string;
  variableValues: Record<string, any>;
  recipientEmail?: string;
}

export interface TemplateValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    type: 'error' | 'warning';
  }>;
  missingVariables: string[];
  unusedVariables: string[];
}