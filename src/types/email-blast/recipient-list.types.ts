export enum MembershipStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
  INACTIVE = 'INACTIVE',
  TERMINATED = 'TERMINATED',
  PENDING_RENEWAL = 'PENDING_RENEWAL',
  PENDING_RE_APPLICATION = 'PENDING_RE_APPLICATION'
}

export enum FilterOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  GREATER_THAN_OR_EQUAL = 'greater_than_or_equal',
  LESS_THAN_OR_EQUAL = 'less_than_or_equal',
  IS_EMPTY = 'is_empty',
  IS_NOT_EMPTY = 'is_not_empty',
  IN = 'in',
  NOT_IN = 'not_in'
}

export interface CustomFieldFilter {
  fieldName: string;
  operator: FilterOperator | string;
  value?: any;
}

export interface RecipientFilters {
  membershipStatus?: MembershipStatus[];
  expiryDateRange?: {
    from?: Date;
    to?: Date;
  };
  membershipTypes?: string[];
  customFields?: CustomFieldFilter[];
  tags?: string[];
  locations?: string[];
  ageRange?: {
    min?: number;
    max?: number;
  };
  registrationDateRange?: {
    from?: Date;
    to?: Date;
  };
  lastActivityDateRange?: {
    from?: Date;
    to?: Date;
  };
  excludeUnsubscribed?: boolean;
  excludeBounced?: boolean;
}

export interface RecipientList {
  id: string;
  name: string;
  description?: string;
  filters: RecipientFilters;
  recipientCount: number;
  recipientCountChange?: number; // Change in recipient count from last update
  isStatic?: boolean; // If true, recipients are manually added/removed
  staticRecipientIds?: string[]; // Used when isStatic is true
  status?: 'active' | 'inactive' | 'archived';
  type?: 'dynamic' | 'static';
  lastUsedAt?: Date;
  usageCount?: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface RecipientPreview {
  id: string;
  name: string;
  email: string;
  membershipType: string;
  status: MembershipStatus;
  expiryDate?: Date;
  location?: string;
  phoneNumber?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface RecipientListStatistics {
  listId: string;
  totalCount: number;
  activeCount: number;
  expiredCount: number;
  pendingCount: number;
  suspendedCount: number;
  byMembershipType: Record<string, number>;
  byLocation?: Record<string, number>;
  lastUpdated: Date;
}

export interface RecipientImportResult {
  listId: string;
  successCount: number;
  failedCount: number;
  duplicateCount: number;
  errors: Array<{
    row: number;
    email: string;
    error: string;
  }>;
  importedAt: Date;
  importedBy: string;
}