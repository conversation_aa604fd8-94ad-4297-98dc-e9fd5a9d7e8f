export enum EmailTemplateType {
  NEWSLETTER = 'newsletter',
  ANNOUNCEMENT = 'announcement',
  EVENT_INVITATION = 'event_invitation',
  MEMBERSHIP_RENEWAL = 'membership_renewal',
  WELCOME = 'welcome',
  CUSTOM = 'custom'
}

export enum EmailPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum EmailDeliveryStatus {
  PENDING = 'pending',
  QUEUED = 'queued',
  SENT = 'sent',
  DELIVERED = 'delivered',
  BOUNCED = 'bounced',
  FAILED = 'failed',
  OPENED = 'opened',
  CLICKED = 'clicked',
  UNSUBSCRIBED = 'unsubscribed',
  SPAM_REPORTED = 'spam_reported'
}

export interface EmailTemplate {
  id: string;
  name: string;
  description?: string;
  type: EmailTemplateType;
  subject: string;
  bodyHtml: string;
  bodyText?: string;
  variables?: string[]; // List of variable names that can be used in the template
  thumbnailUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface EmailSchedule {
  type: 'immediate' | 'scheduled' | 'recurring';
  scheduledAt?: Date;
  timezone?: string;
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    daysOfWeek?: number[]; // 0-6, where 0 is Sunday
    dayOfMonth?: number;
    endDate?: Date;
    occurrences?: number;
  };
}

export interface EmailSettings {
  fromName: string;
  fromEmail: string;
  replyToEmail?: string;
  unsubscribeUrl?: string;
  trackOpens?: boolean;
  trackClicks?: boolean;
  includeUnsubscribeLink?: boolean;
  includeFooter?: boolean;
  footerContent?: string;
}

export interface EmailPersonalization {
  recipientId: string;
  variables: Record<string, any>;
  customSubject?: string;
  customContent?: string;
}

export interface EmailBounce {
  id: string;
  email: string;
  campaignId?: string;
  bounceType: 'hard' | 'soft';
  bounceReason?: string;
  bouncedAt: Date;
  metadata?: Record<string, any>;
}

export interface EmailUnsubscribe {
  id: string;
  email: string;
  campaignId?: string;
  reason?: string;
  unsubscribedAt: Date;
  metadata?: Record<string, any>;
}