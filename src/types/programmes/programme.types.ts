import { Database } from "@/types/database.types"
import { ExtendedProgramme, ProgrammePricingWithType as ExtendedPricingType, ProgrammesWithDetails } from '@/types/database-extensions.types'

//filters
export const statuses = [
    {
        value: "DRAFT",
        label: "Draft",
    },
    {
        value: "PENDING_REVIEW",
        label: "Pending Review",
    },
    {
        value: "APPROVED",
        label: "Approved",
    },
    {
        value: "REJECTED",
        label: "Rejected",
    },
    {
        value: "PUBLISHED",
        label: "Published",
    },
    {
        value: "UNPUBLISHED",
        label: "Unpublished",
    },
    {
        value: "TERMINATED",
        label: "Terminated",
    },
    {
        value: "COMPLETED",
        label: "Completed",
    },
]

export const types = [
    {
        value: "COURSE",
        label: "Course",
    },
    {
        value: "EVENT",
        label: "Event",
    },
    {
        value: "SEMINAR",
        label: "Seminar",
    },
    {
        value: "CONFERENCE",
        label: "Conference",
    },
]

export const registrationType = [
    {
        value: "INDIVIDUAL",
        label: "Individual",
    },
    {
        value: "GROUP",
        label: "Group",
    },
]

export const registrationStatus = [
    {
        value: "PENDING",
        label: "Pending",
    },
    {
        value: "CONFIRMED",
        label: "Confirmed",
    },
    {
        value: "CANCELLED",
        label: "Cancelled",
    },
];

//data
export type ProgrammeList = (ProgrammesWithDetails & {
    programmeSchedules: Database['programme']['Tables']['schedules']['Row'][] | null;
});

type ProgrammeWithVenue = (ExtendedProgramme & {
    venue: Database['programme']['Tables']['venues']['Row'] | null;
}) | null;

export type ProgrammePricingWithType = ExtendedPricingType;

type ProgrammePersonInCharge = {
    organizingDept: { name: string };
    staffInCharge: { name: string };
    committee: { name: string };
};

export type ProgrammeDetails = {
    programme: ProgrammeWithVenue | null;
    programmePricing: ProgrammePricingWithType[] | null;
    programmeSchedules: Database['programme']['Tables']['schedules']['Row'][] | null;
    programmePersonInCharge: ProgrammePersonInCharge | null;
};

export type ProgrammeParticipantInterface = Database['programme']['Views']['programme_participants']['Row'] & {
    identification_type?: string | null;
    identification_no?: string | null;
    professional_id_type?: string | null;
    professional_id?: string | null;
    unit_price?: number | null;
};

export type ProgrammeRegistrationsInterface = Database['programme']['Tables']['registrations']['Row'] & {
    contact_persons?: {
        id: string;
        registration_id: string | null;
        name: string;
        address?: {
            address_line_1?: string;
            address_line_2?: string;
            city?: string;
            state?: string;
            postal_code?: string;
            country?: string;
        } | null;
        is_company: boolean;
        company_name: string | null;
        company_registration_number: string | null;
        email: string | null;
        contact_no: string | null;
        fax_no?: string | null;
        business_unit_code?: string | null;
        created_at: string | null;
        updated_at: string | null;
        created_by: string | null;
    };
    table_registrations?: Database['programme']['Tables']['table_registrations']['Row'];
    participants?: { count: number }[];
};

export interface ProfitSharingDetail {
    has_profit_sharing: boolean;
    profit_sharing_entity: string | null;
    prepared_by: string | null;
    approved_by: string | null;
    // Add these to store the names
    prepared_by_name?: string;
    approved_by_name?: string;
}

export interface FileWithPreview extends File {
    isExisting?: boolean;
    path?: string;
    preview?: string;
}