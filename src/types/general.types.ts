// Dropdown option types
export interface DropdownOption {
  value: string;
  label: string;
}

export interface CurrencyOption extends DropdownOption {
  symbol: string;
}

export interface TaxCodeOption extends DropdownOption {
  rate: number;
}

export interface GlAccountOption {
  group: string;
  list: DropdownOption[];
}

export interface SupplierOption extends DropdownOption {
  currency_code: string;
}

// document
export interface FileWithPreview extends File {
    isExisting?: boolean;
    path?: string;
}