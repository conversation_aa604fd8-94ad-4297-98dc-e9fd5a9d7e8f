﻿export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  access_control: {
    Tables: {
      module_permissions: {
        Row: {
          accessible_membership_types: string[] | null
          can_approve: boolean
          can_create: boolean
          can_delete: boolean
          can_export: boolean
          can_read: boolean
          can_review: boolean
          can_update: boolean
          created_at: string | null
          data_filters: Json | null
          field_restrictions: Json | null
          is_active: boolean
          module_id: string
          permission_id: string
          role_id: string
          status_constraints: string[] | null
          updated_at: string | null
          valid_from: string | null
          valid_to: string | null
        }
        Insert: {
          accessible_membership_types?: string[] | null
          can_approve?: boolean
          can_create?: boolean
          can_delete?: boolean
          can_export?: boolean
          can_read?: boolean
          can_review?: boolean
          can_update?: boolean
          created_at?: string | null
          data_filters?: Json | null
          field_restrictions?: Json | null
          is_active?: boolean
          module_id: string
          permission_id?: string
          role_id: string
          status_constraints?: string[] | null
          updated_at?: string | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Update: {
          accessible_membership_types?: string[] | null
          can_approve?: boolean
          can_create?: boolean
          can_delete?: boolean
          can_export?: boolean
          can_read?: boolean
          can_review?: boolean
          can_update?: boolean
          created_at?: string | null
          data_filters?: Json | null
          field_restrictions?: Json | null
          is_active?: boolean
          module_id?: string
          permission_id?: string
          role_id?: string
          status_constraints?: string[] | null
          updated_at?: string | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "module_permissions_module_id_fkey"
            columns: ["module_id"]
            isOneToOne: false
            referencedRelation: "modules"
            referencedColumns: ["module_id"]
          },
          {
            foreignKeyName: "module_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      modules: {
        Row: {
          created_at: string | null
          is_active: boolean
          module_category: string
          module_description: string | null
          module_id: string
          module_name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          is_active?: boolean
          module_category?: string
          module_description?: string | null
          module_id?: string
          module_name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          is_active?: boolean
          module_category?: string
          module_description?: string | null
          module_id?: string
          module_name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      ownership_transfer_log: {
        Row: {
          action_by: string
          action_reason: string | null
          created_at: string | null
          from_user_id: string
          id: string
          metadata: Json | null
          to_user_id: string
          transfer_id: string
          transfer_type: string
        }
        Insert: {
          action_by: string
          action_reason?: string | null
          created_at?: string | null
          from_user_id: string
          id?: string
          metadata?: Json | null
          to_user_id: string
          transfer_id: string
          transfer_type: string
        }
        Update: {
          action_by?: string
          action_reason?: string | null
          created_at?: string | null
          from_user_id?: string
          id?: string
          metadata?: Json | null
          to_user_id?: string
          transfer_id?: string
          transfer_type?: string
        }
        Relationships: []
      }
      permission_audit: {
        Row: {
          action: string
          audit_id: string
          created_at: string | null
          module_name: string | null
          new_permissions: Json | null
          old_permissions: Json | null
          reason: string | null
          role_name: string | null
          target_user_id: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          audit_id?: string
          created_at?: string | null
          module_name?: string | null
          new_permissions?: Json | null
          old_permissions?: Json | null
          reason?: string | null
          role_name?: string | null
          target_user_id?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          audit_id?: string
          created_at?: string | null
          module_name?: string | null
          new_permissions?: Json | null
          old_permissions?: Json | null
          reason?: string | null
          role_name?: string | null
          target_user_id?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      protected_administrators: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean
          ownership_transfer_target: string | null
          protected_by: string | null
          protection_reason: string | null
          protection_type: string
          transfer_expires_at: string | null
          transfer_initiated_at: string | null
          transfer_message: string | null
          transfer_status: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean
          ownership_transfer_target?: string | null
          protected_by?: string | null
          protection_reason?: string | null
          protection_type?: string
          transfer_expires_at?: string | null
          transfer_initiated_at?: string | null
          transfer_message?: string | null
          transfer_status?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean
          ownership_transfer_target?: string | null
          protected_by?: string | null
          protection_reason?: string | null
          protection_type?: string
          transfer_expires_at?: string | null
          transfer_initiated_at?: string | null
          transfer_message?: string | null
          transfer_status?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      role_hierarchy: {
        Row: {
          child_role_id: string
          created_at: string | null
          id: string
          inheritance_level: number
          is_active: boolean
          parent_role_id: string
        }
        Insert: {
          child_role_id: string
          created_at?: string | null
          id?: string
          inheritance_level?: number
          is_active?: boolean
          parent_role_id: string
        }
        Update: {
          child_role_id?: string
          created_at?: string | null
          id?: string
          inheritance_level?: number
          is_active?: boolean
          parent_role_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_hierarchy_child_role_id_fkey"
            columns: ["child_role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_hierarchy_parent_role_id_fkey"
            columns: ["parent_role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          can_approve: boolean
          can_export: boolean
          created_at: string | null
          data_filters: Json | null
          deletep: boolean | null
          field_restrictions: Json | null
          id: string
          insertp: boolean | null
          is_active: boolean
          membership_type: string | null
          package_name: string
          reviewp: boolean | null
          role_id: string
          selectp: boolean | null
          status_constraints: string[] | null
          updated_at: string | null
          updatep: boolean | null
          valid_from: string | null
          valid_to: string | null
        }
        Insert: {
          can_approve?: boolean
          can_export?: boolean
          created_at?: string | null
          data_filters?: Json | null
          deletep?: boolean | null
          field_restrictions?: Json | null
          id?: string
          insertp?: boolean | null
          is_active?: boolean
          membership_type?: string | null
          package_name: string
          reviewp?: boolean | null
          role_id: string
          selectp?: boolean | null
          status_constraints?: string[] | null
          updated_at?: string | null
          updatep?: boolean | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Update: {
          can_approve?: boolean
          can_export?: boolean
          created_at?: string | null
          data_filters?: Json | null
          deletep?: boolean | null
          field_restrictions?: Json | null
          id?: string
          insertp?: boolean | null
          is_active?: boolean
          membership_type?: string | null
          package_name?: string
          reviewp?: boolean | null
          role_id?: string
          selectp?: boolean | null
          status_constraints?: string[] | null
          updated_at?: string | null
          updatep?: boolean | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean
          name: string
          role_description: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean
          name: string
          role_description?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean
          name?: string
          role_description?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      user_permission_cache: {
        Row: {
          cache_expires_at: string
          cache_id: string
          created_at: string | null
          membership_types: string[] | null
          module_name: string
          permissions: Json
          user_id: string
        }
        Insert: {
          cache_expires_at?: string
          cache_id?: string
          created_at?: string | null
          membership_types?: string[] | null
          module_name: string
          permissions: Json
          user_id: string
        }
        Update: {
          cache_expires_at?: string
          cache_id?: string
          created_at?: string | null
          membership_types?: string[] | null
          module_name?: string
          permissions?: Json
          user_id?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean
          role_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean
          role_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean
          role_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_scope: {
        Row: {
          accessible_membership_types: string[] | null
          created_at: string | null
          data_scope_filters: Json | null
          department: string | null
          is_active: boolean
          organization_id: string | null
          scope_id: string
          updated_at: string | null
          user_id: string
          valid_from: string | null
          valid_to: string | null
        }
        Insert: {
          accessible_membership_types?: string[] | null
          created_at?: string | null
          data_scope_filters?: Json | null
          department?: string | null
          is_active?: boolean
          organization_id?: string | null
          scope_id?: string
          updated_at?: string | null
          user_id: string
          valid_from?: string | null
          valid_to?: string | null
        }
        Update: {
          accessible_membership_types?: string[] | null
          created_at?: string | null
          data_scope_filters?: Json | null
          department?: string | null
          is_active?: boolean
          organization_id?: string | null
          scope_id?: string
          updated_at?: string | null
          user_id?: string
          valid_from?: string | null
          valid_to?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      accept_ownership_transfer: {
        Args: { p_accept?: boolean }
        Returns: Json
      }
      assign_user_role: {
        Args: {
          p_user_id: string
          p_role_name: string
          p_assigned_by?: string
          p_reason?: string
        }
        Returns: boolean
      }
      assign_user_role_protected: {
        Args: {
          p_user_id: string
          p_role_name: string
          p_assigned_by?: string
          p_reason?: string
          p_force_override?: boolean
        }
        Returns: Json
      }
      cancel_ownership_transfer: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      create_module_admin: {
        Args: {
          p_user_id: string
          p_module_names: string[]
          p_membership_types?: string[]
          p_assigned_by?: string
        }
        Returns: boolean
      }
      get_membership_types: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          description: string
          status: string
        }[]
      }
      get_role_permissions_detailed: {
        Args: { p_role_id: string }
        Returns: {
          permission_id: string
          role_id: string
          module_id: string
          module_name: string
          can_create: boolean
          can_read: boolean
          can_update: boolean
          can_delete: boolean
          can_review: boolean
          can_approve: boolean
          can_export: boolean
          accessible_membership_types: string[]
          created_at: string
          updated_at: string
        }[]
      }
      get_system_modules: {
        Args: Record<PropertyKey, never>
        Returns: {
          module_id: string
          module_name: string
          module_description: string
          module_category: string
          is_active: boolean
          created_at: string
          updated_at: string
        }[]
      }
      get_user_accessible_membership_types: {
        Args: { p_user_id: string; p_module_name: string }
        Returns: string[]
      }
      get_user_data_scope: {
        Args: { p_user_id: string }
        Returns: Json
      }
      get_user_effective_roles: {
        Args: { p_user_id: string }
        Returns: {
          role_id: string
          role_name: string
          inheritance_level: number
        }[]
      }
      initiate_ownership_transfer: {
        Args: { p_target_user_id: string; p_message?: string }
        Returns: Json
      }
      log_permission_change: {
        Args: {
          p_user_id: string
          p_target_user_id: string
          p_action: string
          p_module_name?: string
          p_role_name?: string
          p_old_permissions?: Json
          p_new_permissions?: Json
          p_reason?: string
        }
        Returns: undefined
      }
      refresh_user_permission_cache: {
        Args: { p_user_id: string }
        Returns: undefined
      }
      setup_system_owner_protection: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      upsert_role_permission: {
        Args: {
          p_role_id: string
          p_module_id: string
          p_can_create?: boolean
          p_can_read?: boolean
          p_can_update?: boolean
          p_can_delete?: boolean
          p_can_review?: boolean
          p_can_approve?: boolean
          p_can_export?: boolean
          p_membership_type_restrictions?: string[]
        }
        Returns: string
      }
      user_can_access_membership_record: {
        Args: {
          p_user_id: string
          p_membership_type_id: string
          p_action?: string
        }
        Returns: boolean
      }
      user_has_permission: {
        Args: {
          p_user_id: string
          p_module_name: string
          p_action: string
          p_membership_type_id?: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  approval: {
    Tables: {
      approval_actions: {
        Row: {
          action: Database["approval"]["Enums"]["approval_action"]
          action_at: string | null
          approver_id: string | null
          attachments: Json | null
          comments: string | null
          delegated_from: string | null
          id: string
          request_id: string | null
          stage_id: string | null
          system_action: boolean | null
        }
        Insert: {
          action: Database["approval"]["Enums"]["approval_action"]
          action_at?: string | null
          approver_id?: string | null
          attachments?: Json | null
          comments?: string | null
          delegated_from?: string | null
          id?: string
          request_id?: string | null
          stage_id?: string | null
          system_action?: boolean | null
        }
        Update: {
          action?: Database["approval"]["Enums"]["approval_action"]
          action_at?: string | null
          approver_id?: string | null
          attachments?: Json | null
          comments?: string | null
          delegated_from?: string | null
          id?: string
          request_id?: string | null
          stage_id?: string | null
          system_action?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "approval_actions_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "approval_actions_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_summary"
            referencedColumns: ["request_id"]
          },
          {
            foreignKeyName: "approval_actions_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["request_id"]
          },
          {
            foreignKeyName: "approval_actions_stage_id_fkey"
            columns: ["stage_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["current_stage_id"]
          },
          {
            foreignKeyName: "approval_actions_stage_id_fkey"
            columns: ["stage_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["next_stage_id"]
          },
          {
            foreignKeyName: "approval_actions_stage_id_fkey"
            columns: ["stage_id"]
            isOneToOne: false
            referencedRelation: "approval_stages"
            referencedColumns: ["id"]
          },
        ]
      }
      approval_delegations: {
        Row: {
          created_at: string | null
          delegate_id: string | null
          delegator_id: string | null
          end_date: string
          id: string
          is_active: boolean | null
          reason: string | null
          start_date: string
          workflow_id: string | null
        }
        Insert: {
          created_at?: string | null
          delegate_id?: string | null
          delegator_id?: string | null
          end_date: string
          id?: string
          is_active?: boolean | null
          reason?: string | null
          start_date: string
          workflow_id?: string | null
        }
        Update: {
          created_at?: string | null
          delegate_id?: string | null
          delegator_id?: string | null
          end_date?: string
          id?: string
          is_active?: boolean | null
          reason?: string | null
          start_date?: string
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "approval_delegations_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["workflow_id"]
          },
          {
            foreignKeyName: "approval_delegations_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "approval_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      approval_notifications: {
        Row: {
          created_at: string | null
          id: string
          is_read: boolean | null
          message: string | null
          read_at: string | null
          request_id: string | null
          title: string
          type: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message?: string | null
          read_at?: string | null
          request_id?: string | null
          title: string
          type: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message?: string | null
          read_at?: string | null
          request_id?: string | null
          title?: string
          type?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "approval_notifications_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "approval_notifications_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_summary"
            referencedColumns: ["request_id"]
          },
          {
            foreignKeyName: "approval_notifications_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["request_id"]
          },
        ]
      }
      approval_requests: {
        Row: {
          completed_at: string | null
          completion_notes: string | null
          current_stage_id: string | null
          current_stage_order: number | null
          due_date: string | null
          id: string
          metadata: Json | null
          module_data: Json
          module_record_id: string
          module_type: string
          priority: string | null
          request_number: string
          status: Database["approval"]["Enums"]["approval_status"] | null
          submitted_at: string | null
          submitted_by: string | null
          workflow_id: string | null
        }
        Insert: {
          completed_at?: string | null
          completion_notes?: string | null
          current_stage_id?: string | null
          current_stage_order?: number | null
          due_date?: string | null
          id?: string
          metadata?: Json | null
          module_data: Json
          module_record_id: string
          module_type: string
          priority?: string | null
          request_number: string
          status?: Database["approval"]["Enums"]["approval_status"] | null
          submitted_at?: string | null
          submitted_by?: string | null
          workflow_id?: string | null
        }
        Update: {
          completed_at?: string | null
          completion_notes?: string | null
          current_stage_id?: string | null
          current_stage_order?: number | null
          due_date?: string | null
          id?: string
          metadata?: Json | null
          module_data?: Json
          module_record_id?: string
          module_type?: string
          priority?: string | null
          request_number?: string
          status?: Database["approval"]["Enums"]["approval_status"] | null
          submitted_at?: string | null
          submitted_by?: string | null
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "approval_requests_current_stage_id_fkey"
            columns: ["current_stage_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["current_stage_id"]
          },
          {
            foreignKeyName: "approval_requests_current_stage_id_fkey"
            columns: ["current_stage_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["next_stage_id"]
          },
          {
            foreignKeyName: "approval_requests_current_stage_id_fkey"
            columns: ["current_stage_id"]
            isOneToOne: false
            referencedRelation: "approval_stages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "approval_requests_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["workflow_id"]
          },
          {
            foreignKeyName: "approval_requests_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "approval_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      approval_stages: {
        Row: {
          approver_type: Database["approval"]["Enums"]["approver_type"]
          approver_value: Json
          auto_approve_days: number | null
          can_skip: boolean | null
          created_at: string | null
          description: string | null
          id: string
          is_parallel: boolean | null
          skip_conditions: Json | null
          stage_name: string
          stage_order: number
          workflow_id: string | null
        }
        Insert: {
          approver_type: Database["approval"]["Enums"]["approver_type"]
          approver_value: Json
          auto_approve_days?: number | null
          can_skip?: boolean | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_parallel?: boolean | null
          skip_conditions?: Json | null
          stage_name: string
          stage_order: number
          workflow_id?: string | null
        }
        Update: {
          approver_type?: Database["approval"]["Enums"]["approver_type"]
          approver_value?: Json
          auto_approve_days?: number | null
          can_skip?: boolean | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_parallel?: boolean | null
          skip_conditions?: Json | null
          stage_name?: string
          stage_order?: number
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "approval_stages_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["workflow_id"]
          },
          {
            foreignKeyName: "approval_stages_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "approval_workflows"
            referencedColumns: ["id"]
          },
        ]
      }
      approval_workflows: {
        Row: {
          conditions: Json | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          module_type: string
          name: string
          priority: number
          updated_at: string | null
        }
        Insert: {
          conditions?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          module_type: string
          name: string
          priority: number
          updated_at?: string | null
        }
        Update: {
          conditions?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          module_type?: string
          name?: string
          priority?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      post_approval_configs: {
        Row: {
          action_config: Json
          action_type: Database["approval"]["Enums"]["post_approval_action_type"]
          approval_outcome_condition: Json | null
          conditions: Json | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          module_type: string
          name: string
          priority: number | null
          retry_count: number | null
          run_async: boolean | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          action_config: Json
          action_type: Database["approval"]["Enums"]["post_approval_action_type"]
          approval_outcome_condition?: Json | null
          conditions?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          module_type: string
          name: string
          priority?: number | null
          retry_count?: number | null
          run_async?: boolean | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          action_config?: Json
          action_type?: Database["approval"]["Enums"]["post_approval_action_type"]
          approval_outcome_condition?: Json | null
          conditions?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          module_type?: string
          name?: string
          priority?: number | null
          retry_count?: number | null
          run_async?: boolean | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      post_approval_execution_log: {
        Row: {
          approval_request_id: string
          config_id: string
          error_message: string | null
          executed_at: string | null
          execution_time_ms: number | null
          id: string
          result: Json | null
          retry_attempt: number | null
          status: string
        }
        Insert: {
          approval_request_id: string
          config_id: string
          error_message?: string | null
          executed_at?: string | null
          execution_time_ms?: number | null
          id?: string
          result?: Json | null
          retry_attempt?: number | null
          status?: string
        }
        Update: {
          approval_request_id?: string
          config_id?: string
          error_message?: string | null
          executed_at?: string | null
          execution_time_ms?: number | null
          id?: string
          result?: Json | null
          retry_attempt?: number | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "post_approval_execution_log_approval_request_id_fkey"
            columns: ["approval_request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "post_approval_execution_log_approval_request_id_fkey"
            columns: ["approval_request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_summary"
            referencedColumns: ["request_id"]
          },
          {
            foreignKeyName: "post_approval_execution_log_approval_request_id_fkey"
            columns: ["approval_request_id"]
            isOneToOne: false
            referencedRelation: "approval_requests_view"
            referencedColumns: ["request_id"]
          },
          {
            foreignKeyName: "post_approval_execution_log_config_id_fkey"
            columns: ["config_id"]
            isOneToOne: false
            referencedRelation: "post_approval_configs"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      approval_requests_summary: {
        Row: {
          current_stage_name: string | null
          current_stage_order: number | null
          due_date: string | null
          is_parallel_stage: boolean | null
          module_type: string | null
          parallel_progress: string | null
          priority: string | null
          request_id: string | null
          request_number: string | null
          responsible_party: string | null
          status: Database["approval"]["Enums"]["approval_status"] | null
          submitted_at: string | null
          submitter_name: string | null
          urgency: string | null
        }
        Relationships: []
      }
      approval_requests_view: {
        Row: {
          active_delegations: Json | null
          auto_approve_deadline: string | null
          completed_at: string | null
          completion_notes: string | null
          current_approver_type:
            | Database["approval"]["Enums"]["approver_type"]
            | null
          current_approver_value: Json | null
          current_approvers: Json | null
          current_stage_auto_approve_days: number | null
          current_stage_description: string | null
          current_stage_id: string | null
          current_stage_is_parallel: boolean | null
          current_stage_name: string | null
          current_stage_order: number | null
          days_since_submission: number | null
          days_until_auto_approve: number | null
          days_until_due: number | null
          due_date: string | null
          module_data: Json | null
          module_record_id: string | null
          module_type: string | null
          next_stage_approver_type:
            | Database["approval"]["Enums"]["approver_type"]
            | null
          next_stage_id: string | null
          next_stage_name: string | null
          parallel_approvals_received: number | null
          parallel_approvals_required: number | null
          priority: string | null
          recent_actions: Json | null
          request_id: string | null
          request_metadata: Json | null
          request_number: string | null
          status: Database["approval"]["Enums"]["approval_status"] | null
          submitted_at: string | null
          submitter_email: string | null
          submitter_id: string | null
          submitter_name: string | null
          total_stages: number | null
          urgency_status: string | null
          workflow_description: string | null
          workflow_id: string | null
          workflow_name: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      action_call_function: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      action_insert_record: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      action_link_entities: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      action_send_notification: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      action_trigger_process: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      action_update_record: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      action_update_status: {
        Args: { p_config: Json; p_request: Record<string, unknown> }
        Returns: Json
      }
      approve_as_role: {
        Args: {
          p_request_id: string
          p_user_id: string
          p_role_name: string
          p_action: Database["approval"]["Enums"]["approval_action"]
          p_comments?: string
        }
        Returns: Json
      }
      auto_approve_overdue_stages: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      build_column_list: {
        Args: { p_columns: Json }
        Returns: string
      }
      build_set_clause: {
        Args: { p_set_config: Json; p_request: Record<string, unknown> }
        Returns: string
      }
      build_values_clause: {
        Args: { p_values_config: Json; p_request: Record<string, unknown> }
        Returns: string
      }
      build_where_clause: {
        Args: { p_where_config: Json; p_request: Record<string, unknown> }
        Returns: string
      }
      can_user_approve: {
        Args: { p_request_id: string; p_user_id: string }
        Returns: boolean
      }
      evaluate_conditions: {
        Args: { p_conditions: Json; p_module_data: Json; p_priority?: string }
        Returns: boolean
      }
      execute_configured_action: {
        Args: {
          p_action_type: Database["approval"]["Enums"]["post_approval_action_type"]
          p_action_config: Json
          p_request: Record<string, unknown>
          p_config: Record<string, unknown>
        }
        Returns: Json
      }
      execute_post_approval_actions: {
        Args: { p_approval_request_id: string }
        Returns: Json
      }
      execute_post_completion_actions: {
        Args: { p_approval_request_id: string }
        Returns: Json
      }
      get_pending_approvals: {
        Args: Record<PropertyKey, never>
        Returns: {
          request_id: string
          request_number: string
          module_type: string
          module_data: Json
          stage_name: string
          submitted_at: string
          priority: string
        }[]
      }
      get_stage_approvers: {
        Args: { stage_id: string }
        Returns: {
          user_id: string
        }[]
      }
      get_user_approval_context: {
        Args: { p_request_id: string; p_user_id: string }
        Returns: {
          stage_id: string
          stage_name: string
          stage_order: number
          approver_type: Database["approval"]["Enums"]["approver_type"]
          approver_role: string
          is_parallel: boolean
          user_can_approve: boolean
          already_approved: boolean
          already_rejected: boolean
          is_current_stage: boolean
        }[]
      }
      interpolate_string: {
        Args: { p_template: string; p_request: Record<string, unknown> }
        Returns: string
      }
      process_approval_action: {
        Args: {
          p_request_id: string
          p_user_id: string
          p_action: Database["approval"]["Enums"]["approval_action"]
          p_stage_id: string
          p_comments?: string
        }
        Returns: Json
      }
      submit_approval_request: {
        Args: {
          p_module_type: string
          p_module_record_id: string
          p_module_data: Json
          p_submitted_by?: string
          p_priority?: string
          p_due_date?: string
          p_metadata?: Json
        }
        Returns: string
      }
      validate_approval_action: {
        Args: {
          p_request_id: string
          p_user_id: string
          p_stage_id: string
          p_action: Database["approval"]["Enums"]["approval_action"]
        }
        Returns: Json
      }
    }
    Enums: {
      approval_action: "APPROVE" | "REJECT" | "REQUEST_INFO" | "DELEGATE"
      approval_status: "PENDING" | "APPROVED" | "REJECTED" | "CANCELLED"
      approver_type: "ROLE" | "USER" | "DEPARTMENT" | "DYNAMIC"
      post_approval_action_type:
        | "update_record"
        | "insert_record"
        | "call_function"
        | "send_notification"
        | "update_status"
        | "link_entities"
        | "trigger_process"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  budget: {
    Tables: {
      budget_items: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          section_id: string | null
          sequence: number | null
          type: Database["budget"]["Enums"]["budget_group"]
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          section_id?: string | null
          sequence?: number | null
          type: Database["budget"]["Enums"]["budget_group"]
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          section_id?: string | null
          sequence?: number | null
          type?: Database["budget"]["Enums"]["budget_group"]
        }
        Relationships: [
          {
            foreignKeyName: "budget_items_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "budget_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      budget_revisions: {
        Row: {
          approval_request_id: string | null
          budget_code: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          entity_id: string | null
          entity_metadata: Json | null
          entity_type: string
          format: Database["budget"]["Enums"]["budget_format"]
          id: string
          is_archived: boolean | null
          notes: string | null
          parent_id: string | null
          status: Database["budget"]["Enums"]["budget_status"] | null
          title: string
          updated_at: string | null
          version: number
        }
        Insert: {
          approval_request_id?: string | null
          budget_code?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          entity_id?: string | null
          entity_metadata?: Json | null
          entity_type: string
          format: Database["budget"]["Enums"]["budget_format"]
          id?: string
          is_archived?: boolean | null
          notes?: string | null
          parent_id?: string | null
          status?: Database["budget"]["Enums"]["budget_status"] | null
          title: string
          updated_at?: string | null
          version?: number
        }
        Update: {
          approval_request_id?: string | null
          budget_code?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          entity_id?: string | null
          entity_metadata?: Json | null
          entity_type?: string
          format?: Database["budget"]["Enums"]["budget_format"]
          id?: string
          is_archived?: boolean | null
          notes?: string | null
          parent_id?: string | null
          status?: Database["budget"]["Enums"]["budget_status"] | null
          title?: string
          updated_at?: string | null
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["entity_type"]
            isOneToOne: false
            referencedRelation: "entity_types"
            referencedColumns: ["entity_type"]
          },
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["entity_type"]
            isOneToOne: false
            referencedRelation: "entity_types_summary"
            referencedColumns: ["entity_type"]
          },
          {
            foreignKeyName: "budget_revisions_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "budget_revisions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_revisions_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "budget_status_universal"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_revisions_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "parent_budget_revisions"
            referencedColumns: ["id"]
          },
        ]
      }
      budget_sections: {
        Row: {
          created_at: string | null
          id: string
          name: string
          sequence: number
          type: Database["budget"]["Enums"]["budget_group"]
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          sequence: number
          type: Database["budget"]["Enums"]["budget_group"]
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          sequence?: number
          type?: Database["budget"]["Enums"]["budget_group"]
        }
        Relationships: []
      }
      budget_summaries: {
        Row: {
          balance_amount: number | null
          budget_revision_id: string | null
          commitment_rate: number | null
          committed_amount: number
          id: string
          invoiced_amount: number
          net_surplus_deficit: number | null
          paid_amount: number
          surplus_deficit_percentage: number | null
          total_expenditure: number | null
          total_income: number | null
          total_utilized_amount: number | null
          updated_at: string | null
          utilization_rate: number | null
        }
        Insert: {
          balance_amount?: number | null
          budget_revision_id?: string | null
          commitment_rate?: number | null
          committed_amount?: number
          id?: string
          invoiced_amount?: number
          net_surplus_deficit?: number | null
          paid_amount?: number
          surplus_deficit_percentage?: number | null
          total_expenditure?: number | null
          total_income?: number | null
          total_utilized_amount?: number | null
          updated_at?: string | null
          utilization_rate?: number | null
        }
        Update: {
          balance_amount?: number | null
          budget_revision_id?: string | null
          commitment_rate?: number | null
          committed_amount?: number
          id?: string
          invoiced_amount?: number
          net_surplus_deficit?: number | null
          paid_amount?: number
          surplus_deficit_percentage?: number | null
          total_expenditure?: number | null
          total_income?: number | null
          total_utilized_amount?: number | null
          updated_at?: string | null
          utilization_rate?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_summaries_budget_revision_id_fkey"
            columns: ["budget_revision_id"]
            isOneToOne: true
            referencedRelation: "budget_revisions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_summaries_budget_revision_id_fkey"
            columns: ["budget_revision_id"]
            isOneToOne: true
            referencedRelation: "budget_status_universal"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budget_summaries_budget_revision_id_fkey"
            columns: ["budget_revision_id"]
            isOneToOne: true
            referencedRelation: "parent_budget_revisions"
            referencedColumns: ["id"]
          },
        ]
      }
      budgets: {
        Row: {
          budget_item_id: string | null
          budget_revision_id: string | null
          created_at: string | null
          id: string
          proposed_rate: number
          quantity: number
          remarks: string | null
          sub_total: number | null
          type: Database["budget"]["Enums"]["budget_group"]
        }
        Insert: {
          budget_item_id?: string | null
          budget_revision_id?: string | null
          created_at?: string | null
          id?: string
          proposed_rate: number
          quantity: number
          remarks?: string | null
          sub_total?: number | null
          type: Database["budget"]["Enums"]["budget_group"]
        }
        Update: {
          budget_item_id?: string | null
          budget_revision_id?: string | null
          created_at?: string | null
          id?: string
          proposed_rate?: number
          quantity?: number
          remarks?: string | null
          sub_total?: number | null
          type?: Database["budget"]["Enums"]["budget_group"]
        }
        Relationships: [
          {
            foreignKeyName: "budgets_budget_item_id_fkey"
            columns: ["budget_item_id"]
            isOneToOne: false
            referencedRelation: "budget_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_budget_revision_id_fkey"
            columns: ["budget_revision_id"]
            isOneToOne: false
            referencedRelation: "budget_revisions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_budget_revision_id_fkey"
            columns: ["budget_revision_id"]
            isOneToOne: false
            referencedRelation: "budget_status_universal"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "budgets_budget_revision_id_fkey"
            columns: ["budget_revision_id"]
            isOneToOne: false
            referencedRelation: "parent_budget_revisions"
            referencedColumns: ["id"]
          },
        ]
      }
      entity_types: {
        Row: {
          code_pattern: string
          code_prefix: string
          created_at: string | null
          default_format: Database["budget"]["Enums"]["budget_format"] | null
          description: string | null
          display_name: string
          entity_type: string
          is_active: boolean | null
          metadata_schema: Json | null
          requires_entity_id: boolean | null
          validation_rules: Json | null
        }
        Insert: {
          code_pattern: string
          code_prefix: string
          created_at?: string | null
          default_format?: Database["budget"]["Enums"]["budget_format"] | null
          description?: string | null
          display_name: string
          entity_type: string
          is_active?: boolean | null
          metadata_schema?: Json | null
          requires_entity_id?: boolean | null
          validation_rules?: Json | null
        }
        Update: {
          code_pattern?: string
          code_prefix?: string
          created_at?: string | null
          default_format?: Database["budget"]["Enums"]["budget_format"] | null
          description?: string | null
          display_name?: string
          entity_type?: string
          is_active?: boolean | null
          metadata_schema?: Json | null
          requires_entity_id?: boolean | null
          validation_rules?: Json | null
        }
        Relationships: []
      }
    }
    Views: {
      budget_status_universal: {
        Row: {
          approval_completed_at: string | null
          approval_request_id: string | null
          approval_request_number: string | null
          approval_status:
            | Database["approval"]["Enums"]["approval_status"]
            | null
          approval_submitted_at: string | null
          budget_code: string | null
          budget_type: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          document_status: Database["budget"]["Enums"]["budget_status"] | null
          entity_code: string | null
          entity_id: string | null
          entity_metadata: Json | null
          entity_name: string | null
          entity_type: string | null
          entity_type_display: string | null
          format: Database["budget"]["Enums"]["budget_format"] | null
          id: string | null
          net_surplus_deficit: number | null
          surplus_deficit_percentage: number | null
          title: string | null
          total_expenditure: number | null
          total_income: number | null
          updated_at: string | null
          uses_approval_workflow: boolean | null
          version: number | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["budget_type"]
            isOneToOne: false
            referencedRelation: "entity_types"
            referencedColumns: ["entity_type"]
          },
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["entity_type"]
            isOneToOne: false
            referencedRelation: "entity_types"
            referencedColumns: ["entity_type"]
          },
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["budget_type"]
            isOneToOne: false
            referencedRelation: "entity_types_summary"
            referencedColumns: ["entity_type"]
          },
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["entity_type"]
            isOneToOne: false
            referencedRelation: "entity_types_summary"
            referencedColumns: ["entity_type"]
          },
        ]
      }
      entity_types_summary: {
        Row: {
          approved_count: number | null
          budget_count: number | null
          code_prefix: string | null
          description: string | null
          display_name: string | null
          draft_count: number | null
          entity_type: string | null
          is_active: boolean | null
          pending_count: number | null
        }
        Relationships: []
      }
      parent_budget_revisions: {
        Row: {
          approval_request_id: string | null
          budget_code: string | null
          created_at: string | null
          created_by: string | null
          entity_id: string | null
          entity_metadata: Json | null
          entity_type: string | null
          format: Database["budget"]["Enums"]["budget_format"] | null
          id: string | null
          is_archived: boolean | null
          latest_approval_id: string | null
          latest_id: string | null
          latest_reviewed_by: string | null
          latest_status: Database["budget"]["Enums"]["budget_status"] | null
          latest_title: string | null
          latest_version: number | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["entity_type"]
            isOneToOne: false
            referencedRelation: "entity_types"
            referencedColumns: ["entity_type"]
          },
          {
            foreignKeyName: "budget_revisions_entity_type_fkey"
            columns: ["entity_type"]
            isOneToOne: false
            referencedRelation: "entity_types_summary"
            referencedColumns: ["entity_type"]
          },
        ]
      }
    }
    Functions: {
      calculate_committed_amount: {
        Args: { p_budget_revision_id: string }
        Returns: number
      }
      calculate_invoiced_amount: {
        Args: { p_budget_revision_id: string }
        Returns: number
      }
      calculate_paid_amount: {
        Args: { p_budget_revision_id: string }
        Returns: number
      }
      cancel_budget_revision: {
        Args: { revision_id: string; reason?: string }
        Returns: boolean
      }
      check_budget_availability: {
        Args: { p_budget_revision_id: string; p_requested_amount: number }
        Returns: {
          has_sufficient_balance: boolean
          current_balance: number
          current_utilization_rate: number
          projected_utilization_rate: number
          message: string
        }[]
      }
      get_budget_context: {
        Args: { p_budget_id: string }
        Returns: {
          entity_type: string
          entity_id: string
          entity_name: string
          entity_code: string
          entity_metadata: Json
        }[]
      }
      get_budget_utilization_report: {
        Args: { p_entity_type?: string }
        Returns: {
          budget_revision_id: string
          budget_code: string
          title: string
          entity_type: string
          entity_id: string
          status: Database["budget"]["Enums"]["budget_status"]
          total_expenditure: number
          committed_amount: number
          invoiced_amount: number
          paid_amount: number
          total_utilized_amount: number
          balance_amount: number
          utilization_rate: number
          commitment_rate: number
          created_at: string
          updated_at: string
        }[]
      }
      link_budget_to_entity: {
        Args: {
          p_budget_id: string
          p_entity_id: string
          p_update_metadata?: boolean
        }
        Returns: boolean
      }
      prepare_approval_data: {
        Args: { p_budget_id: string }
        Returns: Json
      }
      recalculate_all_utilization: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      should_use_approval_workflow: {
        Args: { p_budget_id: string }
        Returns: boolean
      }
      update_entity_metadata: {
        Args: { p_budget_id: string; p_metadata: Json }
        Returns: boolean
      }
      validate_entity_context: {
        Args: { p_entity_type: string; p_entity_id: string }
        Returns: boolean
      }
    }
    Enums: {
      budget_format: "SIMPLE" | "COMPLEX"
      budget_group: "INCOME" | "EXPENDITURE"
      budget_status:
        | "DRAFT"
        | "PENDING_APPROVAL"
        | "APPROVED"
        | "REJECTED"
        | "CANCELLED"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  calendar: {
    Tables: {
      appointment_references: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          record_id: string
          table_name: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          record_id: string
          table_name: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          record_id?: string
          table_name?: string
        }
        Relationships: []
      }
      appointment_schedules: {
        Row: {
          appointment_type_id: string
          created_at: string | null
          created_by: string | null
          days_of_week: number[]
          end_time: string
          id: string
          is_active: boolean | null
          max_concurrent_appointments: number | null
          slot_duration_minutes: number
          start_time: string
          updated_at: string | null
          updated_by: string | null
          valid_from: string
          valid_until: string | null
        }
        Insert: {
          appointment_type_id: string
          created_at?: string | null
          created_by?: string | null
          days_of_week: number[]
          end_time: string
          id?: string
          is_active?: boolean | null
          max_concurrent_appointments?: number | null
          slot_duration_minutes: number
          start_time: string
          updated_at?: string | null
          updated_by?: string | null
          valid_from: string
          valid_until?: string | null
        }
        Update: {
          appointment_type_id?: string
          created_at?: string | null
          created_by?: string | null
          days_of_week?: number[]
          end_time?: string
          id?: string
          is_active?: boolean | null
          max_concurrent_appointments?: number | null
          slot_duration_minutes?: number
          start_time?: string
          updated_at?: string | null
          updated_by?: string | null
          valid_from?: string
          valid_until?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "appointment_schedules_appointment_type_id_fkey"
            columns: ["appointment_type_id"]
            isOneToOne: false
            referencedRelation: "appointment_types"
            referencedColumns: ["id"]
          },
        ]
      }
      appointment_types: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          duration_minutes: number
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          duration_minutes: number
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          duration_minutes?: number
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      appointments: {
        Row: {
          additional_notes: string | null
          appointment_reference_id: string | null
          appointment_type_id: string
          calendar_event_id: string | null
          cancellation_reason: string | null
          created_at: string | null
          created_by: string | null
          id: string
          location: string | null
          previous_appointment_id: string | null
          schedule_id: string
          scheduled_at: string
          status: Database["calendar"]["Enums"]["appointment_status"]
          updated_at: string | null
          updated_by: string | null
          user_id: string
        }
        Insert: {
          additional_notes?: string | null
          appointment_reference_id?: string | null
          appointment_type_id: string
          calendar_event_id?: string | null
          cancellation_reason?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          location?: string | null
          previous_appointment_id?: string | null
          schedule_id: string
          scheduled_at: string
          status?: Database["calendar"]["Enums"]["appointment_status"]
          updated_at?: string | null
          updated_by?: string | null
          user_id: string
        }
        Update: {
          additional_notes?: string | null
          appointment_reference_id?: string | null
          appointment_type_id?: string
          calendar_event_id?: string | null
          cancellation_reason?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          location?: string | null
          previous_appointment_id?: string | null
          schedule_id?: string
          scheduled_at?: string
          status?: Database["calendar"]["Enums"]["appointment_status"]
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "appointments_appointment_reference_id_fkey"
            columns: ["appointment_reference_id"]
            isOneToOne: false
            referencedRelation: "appointment_references"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_appointment_type_id_fkey"
            columns: ["appointment_type_id"]
            isOneToOne: false
            referencedRelation: "appointment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_previous_appointment_id_fkey"
            columns: ["previous_appointment_id"]
            isOneToOne: false
            referencedRelation: "application_appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_previous_appointment_id_fkey"
            columns: ["previous_appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_previous_appointment_id_fkey"
            columns: ["previous_appointment_id"]
            isOneToOne: false
            referencedRelation: "upcoming_appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "appointment_schedules"
            referencedColumns: ["id"]
          },
        ]
      }
      schedule_exceptions: {
        Row: {
          alternative_end_time: string | null
          alternative_start_time: string | null
          created_at: string | null
          created_by: string | null
          exception_date: string
          id: string
          is_available: boolean | null
          reason: string | null
          schedule_id: string | null
        }
        Insert: {
          alternative_end_time?: string | null
          alternative_start_time?: string | null
          created_at?: string | null
          created_by?: string | null
          exception_date: string
          id?: string
          is_available?: boolean | null
          reason?: string | null
          schedule_id?: string | null
        }
        Update: {
          alternative_end_time?: string | null
          alternative_start_time?: string | null
          created_at?: string | null
          created_by?: string | null
          exception_date?: string
          id?: string
          is_available?: boolean | null
          reason?: string | null
          schedule_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "schedule_exceptions_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "appointment_schedules"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      application_appointments: {
        Row: {
          additional_notes: string | null
          appointment_type: string | null
          available_days: number[] | null
          created_at: string | null
          duration_minutes: number | null
          end_time: string | null
          id: string | null
          location: string | null
          reference_id: string | null
          reference_table: string | null
          scheduled_at: string | null
          start_time: string | null
          status: Database["calendar"]["Enums"]["appointment_status"] | null
          updated_at: string | null
          user_email: string | null
        }
        Relationships: []
      }
      available_slots: {
        Row: {
          appointment_type_id: string | null
          appointment_type_name: string | null
          available_slots: number | null
          end_time: string | null
          schedule_id: string | null
          start_time: string | null
        }
        Relationships: []
      }
      upcoming_appointments: {
        Row: {
          additional_notes: string | null
          appointment_type: string | null
          available_days: number[] | null
          created_at: string | null
          duration_minutes: number | null
          end_time: string | null
          id: string | null
          location: string | null
          reference_id: string | null
          reference_table: string | null
          scheduled_at: string | null
          start_time: string | null
          status: Database["calendar"]["Enums"]["appointment_status"] | null
          updated_at: string | null
          user_email: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      create_appointment: {
        Args: {
          p_appointment_type_id: string
          p_schedule_id: string
          p_scheduled_at: string
          p_location: string
          p_user_id: string
          p_additional_notes?: string
          p_reference_table_name?: string
          p_reference_record_id?: string
        }
        Returns: {
          additional_notes: string | null
          appointment_reference_id: string | null
          appointment_type_id: string
          calendar_event_id: string | null
          cancellation_reason: string | null
          created_at: string | null
          created_by: string | null
          id: string
          location: string | null
          previous_appointment_id: string | null
          schedule_id: string
          scheduled_at: string
          status: Database["calendar"]["Enums"]["appointment_status"]
          updated_at: string | null
          updated_by: string | null
          user_id: string
        }
      }
      get_available_slots: {
        Args: { p_date: string; p_appointment_type_id?: string }
        Returns: {
          start_time: string
          end_time: string
          appointment_type_id: string
          appointment_type_name: string
          schedule_id: string
          available_slots: number
        }[]
      }
    }
    Enums: {
      appointment_status:
        | "SCHEDULED"
        | "COMPLETED"
        | "CANCELLED"
        | "RESCHEDULED"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  email_system: {
    Tables: {
      campaign_analytics: {
        Row: {
          bounce_rate: number | null
          bounced: number | null
          campaign_id: string
          click_rate: number | null
          clicked: number | null
          delivered: number | null
          delivery_rate: number | null
          failed: number | null
          id: string
          last_calculated: string
          open_rate: number | null
          opened: number | null
          sent: number | null
          spam_reports: number | null
          unsubscribe_rate: number | null
          unsubscribed: number | null
        }
        Insert: {
          bounce_rate?: number | null
          bounced?: number | null
          campaign_id: string
          click_rate?: number | null
          clicked?: number | null
          delivered?: number | null
          delivery_rate?: number | null
          failed?: number | null
          id?: string
          last_calculated?: string
          open_rate?: number | null
          opened?: number | null
          sent?: number | null
          spam_reports?: number | null
          unsubscribe_rate?: number | null
          unsubscribed?: number | null
        }
        Update: {
          bounce_rate?: number | null
          bounced?: number | null
          campaign_id?: string
          click_rate?: number | null
          clicked?: number | null
          delivered?: number | null
          delivery_rate?: number | null
          failed?: number | null
          id?: string
          last_calculated?: string
          open_rate?: number | null
          opened?: number | null
          sent?: number | null
          spam_reports?: number | null
          unsubscribe_rate?: number | null
          unsubscribed?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_analytics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_analytics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_analytics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_recipients: {
        Row: {
          bounce_type: string | null
          bounced_at: string | null
          campaign_id: string
          click_count: number | null
          clicked_at: string | null
          created_at: string | null
          delivered_at: string | null
          email: string
          error_message: string | null
          failed_at: string | null
          first_clicked_at: string | null
          first_opened_at: string | null
          id: string
          metadata: Json | null
          name: string | null
          open_count: number | null
          opened_at: string | null
          queue_id: string | null
          recipient_data: Json | null
          recipient_id: string | null
          sent_at: string | null
          status: string
          unsubscribed_at: string | null
          updated_at: string | null
        }
        Insert: {
          bounce_type?: string | null
          bounced_at?: string | null
          campaign_id: string
          click_count?: number | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          email: string
          error_message?: string | null
          failed_at?: string | null
          first_clicked_at?: string | null
          first_opened_at?: string | null
          id?: string
          metadata?: Json | null
          name?: string | null
          open_count?: number | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_data?: Json | null
          recipient_id?: string | null
          sent_at?: string | null
          status?: string
          unsubscribed_at?: string | null
          updated_at?: string | null
        }
        Update: {
          bounce_type?: string | null
          bounced_at?: string | null
          campaign_id?: string
          click_count?: number | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          email?: string
          error_message?: string | null
          failed_at?: string | null
          first_clicked_at?: string | null
          first_opened_at?: string | null
          id?: string
          metadata?: Json | null
          name?: string | null
          open_count?: number | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_data?: Json | null
          recipient_id?: string | null
          sent_at?: string | null
          status?: string
          unsubscribed_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_recipients_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_recipients_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_recipients_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_recipients_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "campaign_recipients_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_statistics: {
        Row: {
          avg_click_time_hours: number | null
          avg_open_time_hours: number | null
          bounce_count: number | null
          bounce_rate: number | null
          campaign_id: string
          click_count: number | null
          click_rate: number | null
          created_at: string | null
          delivered_count: number | null
          delivery_rate: number | null
          failed_count: number | null
          hard_bounce_count: number | null
          id: string
          last_calculated_at: string | null
          last_updated: string | null
          open_count: number | null
          open_rate: number | null
          pending_count: number | null
          sent_count: number | null
          soft_bounce_count: number | null
          spam_report_count: number | null
          total_clicks: number | null
          total_opens: number | null
          total_recipients: number
          unique_click_count: number | null
          unique_clicks: number | null
          unique_open_count: number | null
          unique_opens: number | null
          unsubscribe_count: number | null
          unsubscribe_rate: number | null
          updated_at: string | null
        }
        Insert: {
          avg_click_time_hours?: number | null
          avg_open_time_hours?: number | null
          bounce_count?: number | null
          bounce_rate?: number | null
          campaign_id: string
          click_count?: number | null
          click_rate?: number | null
          created_at?: string | null
          delivered_count?: number | null
          delivery_rate?: number | null
          failed_count?: number | null
          hard_bounce_count?: number | null
          id?: string
          last_calculated_at?: string | null
          last_updated?: string | null
          open_count?: number | null
          open_rate?: number | null
          pending_count?: number | null
          sent_count?: number | null
          soft_bounce_count?: number | null
          spam_report_count?: number | null
          total_clicks?: number | null
          total_opens?: number | null
          total_recipients?: number
          unique_click_count?: number | null
          unique_clicks?: number | null
          unique_open_count?: number | null
          unique_opens?: number | null
          unsubscribe_count?: number | null
          unsubscribe_rate?: number | null
          updated_at?: string | null
        }
        Update: {
          avg_click_time_hours?: number | null
          avg_open_time_hours?: number | null
          bounce_count?: number | null
          bounce_rate?: number | null
          campaign_id?: string
          click_count?: number | null
          click_rate?: number | null
          created_at?: string | null
          delivered_count?: number | null
          delivery_rate?: number | null
          failed_count?: number | null
          hard_bounce_count?: number | null
          id?: string
          last_calculated_at?: string | null
          last_updated?: string | null
          open_count?: number | null
          open_rate?: number | null
          pending_count?: number | null
          sent_count?: number | null
          soft_bounce_count?: number | null
          spam_report_count?: number | null
          total_clicks?: number | null
          total_opens?: number | null
          total_recipients?: number
          unique_click_count?: number | null
          unique_clicks?: number | null
          unique_open_count?: number | null
          unique_opens?: number | null
          unsubscribe_count?: number | null
          unsubscribe_rate?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_statistics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_statistics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_statistics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: true
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      email_attachments: {
        Row: {
          campaign_id: string
          filename: string
          id: string
          mime_type: string
          size: number
          uploaded_at: string
          uploaded_by: string
          url: string
        }
        Insert: {
          campaign_id: string
          filename: string
          id?: string
          mime_type: string
          size: number
          uploaded_at?: string
          uploaded_by: string
          url: string
        }
        Update: {
          campaign_id?: string
          filename?: string
          id?: string
          mime_type?: string
          size?: number
          uploaded_at?: string
          uploaded_by?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_attachments_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_attachments_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_attachments_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      email_bounces: {
        Row: {
          bounce_reason: string | null
          bounce_type: string
          bounced_at: string
          campaign_id: string | null
          email: string
          id: string
          metadata: Json | null
        }
        Insert: {
          bounce_reason?: string | null
          bounce_type: string
          bounced_at?: string
          campaign_id?: string | null
          email: string
          id?: string
          metadata?: Json | null
        }
        Update: {
          bounce_reason?: string | null
          bounce_type?: string
          bounced_at?: string
          campaign_id?: string | null
          email?: string
          id?: string
          metadata?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "email_bounces_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_bounces_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_bounces_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      email_campaigns: {
        Row: {
          body_html: string
          body_text: string | null
          bounced_count: number | null
          click_count: number | null
          clicked_count: number | null
          completed_at: string | null
          created_at: string | null
          created_by: string
          delivered_count: number | null
          description: string | null
          failed_count: number | null
          from_email: string
          from_name: string
          id: string
          include_unsubscribe: boolean | null
          metadata: Json | null
          name: string
          open_count: number | null
          opened_count: number | null
          recipient_count: number | null
          recipient_list_id: string
          reply_to_email: string | null
          scheduled_at: string | null
          sent_at: string | null
          sent_count: number | null
          started_at: string | null
          status: string
          subject: string
          tags: string[] | null
          template_id: string | null
          total_recipients: number | null
          track_clicks: boolean | null
          track_opens: boolean | null
          unsubscribed_count: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          body_html: string
          body_text?: string | null
          bounced_count?: number | null
          click_count?: number | null
          clicked_count?: number | null
          completed_at?: string | null
          created_at?: string | null
          created_by: string
          delivered_count?: number | null
          description?: string | null
          failed_count?: number | null
          from_email: string
          from_name: string
          id?: string
          include_unsubscribe?: boolean | null
          metadata?: Json | null
          name: string
          open_count?: number | null
          opened_count?: number | null
          recipient_count?: number | null
          recipient_list_id: string
          reply_to_email?: string | null
          scheduled_at?: string | null
          sent_at?: string | null
          sent_count?: number | null
          started_at?: string | null
          status?: string
          subject: string
          tags?: string[] | null
          template_id?: string | null
          total_recipients?: number | null
          track_clicks?: boolean | null
          track_opens?: boolean | null
          unsubscribed_count?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          body_html?: string
          body_text?: string | null
          bounced_count?: number | null
          click_count?: number | null
          clicked_count?: number | null
          completed_at?: string | null
          created_at?: string | null
          created_by?: string
          delivered_count?: number | null
          description?: string | null
          failed_count?: number | null
          from_email?: string
          from_name?: string
          id?: string
          include_unsubscribe?: boolean | null
          metadata?: Json | null
          name?: string
          open_count?: number | null
          opened_count?: number | null
          recipient_count?: number | null
          recipient_list_id?: string
          reply_to_email?: string | null
          scheduled_at?: string | null
          sent_at?: string | null
          sent_count?: number | null
          started_at?: string | null
          status?: string
          subject?: string
          tags?: string[] | null
          template_id?: string | null
          total_recipients?: number | null
          track_clicks?: boolean | null
          track_opens?: boolean | null
          unsubscribed_count?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_campaigns_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_campaigns_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "template_performance_view"
            referencedColumns: ["id"]
          },
        ]
      }
      email_clicks: {
        Row: {
          campaign_id: string | null
          click_position: string | null
          clicked_at: string | null
          device_type: string | null
          id: string
          ip_address: unknown | null
          is_blast_click: boolean | null
          queue_id: string | null
          recipient_id: string | null
          url: string
          user_agent: string | null
        }
        Insert: {
          campaign_id?: string | null
          click_position?: string | null
          clicked_at?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_click?: boolean | null
          queue_id?: string | null
          recipient_id?: string | null
          url: string
          user_agent?: string | null
        }
        Update: {
          campaign_id?: string | null
          click_position?: string | null
          clicked_at?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_click?: boolean | null
          queue_id?: string | null
          recipient_id?: string | null
          url?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_clicks_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_clicks_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_clicks_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "campaign_recipients"
            referencedColumns: ["id"]
          },
        ]
      }
      email_configurations: {
        Row: {
          active: boolean | null
          attachment_config: Json | null
          conditions: Json
          created_at: string | null
          description: string | null
          id: string
          name: string
          priority: number | null
          recipients: Json
          template_id: string
          trigger_type: string
          updated_at: string | null
        }
        Insert: {
          active?: boolean | null
          attachment_config?: Json | null
          conditions?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          priority?: number | null
          recipients?: Json
          template_id: string
          trigger_type: string
          updated_at?: string | null
        }
        Update: {
          active?: boolean | null
          attachment_config?: Json | null
          conditions?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          priority?: number | null
          recipients?: Json
          template_id?: string
          trigger_type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_configurations_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_configurations_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "template_performance_view"
            referencedColumns: ["id"]
          },
        ]
      }
      email_delivery_events: {
        Row: {
          bounce_sub_type: string | null
          bounce_type: string | null
          complaint_feedback_type: string | null
          complaint_sub_type: string | null
          created_at: string | null
          delivery_delay: number | null
          diagnostic_code: string | null
          event_data: Json | null
          event_timestamp: string
          event_type: string
          id: string
          processed: boolean | null
          processed_at: string | null
          queue_id: string | null
          recipient_email: string
          ses_message_id: string
          source_email: string | null
          status: string
        }
        Insert: {
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_feedback_type?: string | null
          complaint_sub_type?: string | null
          created_at?: string | null
          delivery_delay?: number | null
          diagnostic_code?: string | null
          event_data?: Json | null
          event_timestamp: string
          event_type: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          queue_id?: string | null
          recipient_email: string
          ses_message_id: string
          source_email?: string | null
          status?: string
        }
        Update: {
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_feedback_type?: string | null
          complaint_sub_type?: string | null
          created_at?: string | null
          delivery_delay?: number | null
          diagnostic_code?: string | null
          event_data?: Json | null
          event_timestamp?: string
          event_type?: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          queue_id?: string | null
          recipient_email?: string
          ses_message_id?: string
          source_email?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_delivery_events_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_delivery_events_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      email_events: {
        Row: {
          created_at: string | null
          error_message: string | null
          event_data: Json
          event_type: string
          id: string
          processed: boolean | null
          processed_at: string | null
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          event_data: Json
          event_type: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          processed?: boolean | null
          processed_at?: string | null
        }
        Relationships: []
      }
      email_jobs: {
        Row: {
          attempts: number | null
          completed_at: string | null
          created_at: string
          error: string | null
          id: string
          max_attempts: number | null
          payload: Json
          result: Json | null
          scheduled_for: string | null
          started_at: string | null
          status: string
          type: string
        }
        Insert: {
          attempts?: number | null
          completed_at?: string | null
          created_at?: string
          error?: string | null
          id?: string
          max_attempts?: number | null
          payload?: Json
          result?: Json | null
          scheduled_for?: string | null
          started_at?: string | null
          status?: string
          type: string
        }
        Update: {
          attempts?: number | null
          completed_at?: string | null
          created_at?: string
          error?: string | null
          id?: string
          max_attempts?: number | null
          payload?: Json
          result?: Json | null
          scheduled_for?: string | null
          started_at?: string | null
          status?: string
          type?: string
        }
        Relationships: []
      }
      email_logs: {
        Row: {
          action: string
          created_at: string | null
          details: Json | null
          error_message: string | null
          id: string
          queue_id: string | null
          status: string
        }
        Insert: {
          action: string
          created_at?: string | null
          details?: Json | null
          error_message?: string | null
          id?: string
          queue_id?: string | null
          status: string
        }
        Update: {
          action?: string
          created_at?: string | null
          details?: Json | null
          error_message?: string | null
          id?: string
          queue_id?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_logs_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_logs_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      email_opens: {
        Row: {
          campaign_id: string | null
          device_type: string | null
          id: string
          ip_address: unknown | null
          is_blast_open: boolean | null
          opened_at: string | null
          queue_id: string | null
          recipient_id: string | null
          user_agent: string | null
        }
        Insert: {
          campaign_id?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_open?: boolean | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_id?: string | null
          user_agent?: string | null
        }
        Update: {
          campaign_id?: string | null
          device_type?: string | null
          id?: string
          ip_address?: unknown | null
          is_blast_open?: boolean | null
          opened_at?: string | null
          queue_id?: string | null
          recipient_id?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_opens_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_delivery_analytics"
            referencedColumns: ["queue_id"]
          },
          {
            foreignKeyName: "email_opens_queue_id_fkey"
            columns: ["queue_id"]
            isOneToOne: false
            referencedRelation: "email_queue"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_opens_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "campaign_recipients"
            referencedColumns: ["id"]
          },
        ]
      }
      email_provider_logs: {
        Row: {
          campaign_id: string | null
          created_at: string
          error_message: string | null
          id: string
          message_id: string | null
          provider: string
          request: Json | null
          response: Json | null
          status_code: number | null
        }
        Insert: {
          campaign_id?: string | null
          created_at?: string
          error_message?: string | null
          id?: string
          message_id?: string | null
          provider: string
          request?: Json | null
          response?: Json | null
          status_code?: number | null
        }
        Update: {
          campaign_id?: string | null
          created_at?: string
          error_message?: string | null
          id?: string
          message_id?: string | null
          provider?: string
          request?: Json | null
          response?: Json | null
          status_code?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "email_provider_logs_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_provider_logs_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_provider_logs_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      email_queue: {
        Row: {
          attachments: Json | null
          attempts: number | null
          bcc_email: string[] | null
          body_html: string
          body_text: string | null
          bounce_type: string | null
          campaign_id: string | null
          campaign_recipient_id: string | null
          cc_email: string[] | null
          complaint_type: string | null
          configuration_id: string | null
          configuration_set: string | null
          created_at: string | null
          delivery_confirmed_at: string | null
          error_message: string | null
          id: string
          is_blast_email: boolean | null
          last_attempt_at: string | null
          max_attempts: number | null
          metadata: Json | null
          personalization_data: Json | null
          priority: number | null
          send_rate_limit: number | null
          sent_at: string | null
          ses_message_id: string | null
          status: string
          subject: string
          template_id: string
          to_email: string[]
          updated_at: string | null
        }
        Insert: {
          attachments?: Json | null
          attempts?: number | null
          bcc_email?: string[] | null
          body_html: string
          body_text?: string | null
          bounce_type?: string | null
          campaign_id?: string | null
          campaign_recipient_id?: string | null
          cc_email?: string[] | null
          complaint_type?: string | null
          configuration_id?: string | null
          configuration_set?: string | null
          created_at?: string | null
          delivery_confirmed_at?: string | null
          error_message?: string | null
          id?: string
          is_blast_email?: boolean | null
          last_attempt_at?: string | null
          max_attempts?: number | null
          metadata?: Json | null
          personalization_data?: Json | null
          priority?: number | null
          send_rate_limit?: number | null
          sent_at?: string | null
          ses_message_id?: string | null
          status?: string
          subject: string
          template_id: string
          to_email: string[]
          updated_at?: string | null
        }
        Update: {
          attachments?: Json | null
          attempts?: number | null
          bcc_email?: string[] | null
          body_html?: string
          body_text?: string | null
          bounce_type?: string | null
          campaign_id?: string | null
          campaign_recipient_id?: string | null
          cc_email?: string[] | null
          complaint_type?: string | null
          configuration_id?: string | null
          configuration_set?: string | null
          created_at?: string | null
          delivery_confirmed_at?: string | null
          error_message?: string | null
          id?: string
          is_blast_email?: boolean | null
          last_attempt_at?: string | null
          max_attempts?: number | null
          metadata?: Json | null
          personalization_data?: Json | null
          priority?: number | null
          send_rate_limit?: number | null
          sent_at?: string | null
          ses_message_id?: string | null
          status?: string
          subject?: string
          template_id?: string
          to_email?: string[]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_queue_configuration_id_fkey"
            columns: ["configuration_id"]
            isOneToOne: false
            referencedRelation: "email_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_queue_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_queue_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "template_performance_view"
            referencedColumns: ["id"]
          },
        ]
      }
      email_suppressions: {
        Row: {
          bounce_count: number | null
          bounce_sub_type: string | null
          bounce_type: string | null
          complaint_count: number | null
          complaint_sub_type: string | null
          created_at: string | null
          email_address: string
          first_bounce_at: string | null
          first_complaint_at: string | null
          id: string
          is_active: boolean | null
          last_bounce_at: string | null
          last_complaint_at: string | null
          notes: string | null
          suppression_reason: string | null
          suppression_type: string
          updated_at: string | null
        }
        Insert: {
          bounce_count?: number | null
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_count?: number | null
          complaint_sub_type?: string | null
          created_at?: string | null
          email_address: string
          first_bounce_at?: string | null
          first_complaint_at?: string | null
          id?: string
          is_active?: boolean | null
          last_bounce_at?: string | null
          last_complaint_at?: string | null
          notes?: string | null
          suppression_reason?: string | null
          suppression_type: string
          updated_at?: string | null
        }
        Update: {
          bounce_count?: number | null
          bounce_sub_type?: string | null
          bounce_type?: string | null
          complaint_count?: number | null
          complaint_sub_type?: string | null
          created_at?: string | null
          email_address?: string
          first_bounce_at?: string | null
          first_complaint_at?: string | null
          id?: string
          is_active?: boolean | null
          last_bounce_at?: string | null
          last_complaint_at?: string | null
          notes?: string | null
          suppression_reason?: string | null
          suppression_type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      email_templates: {
        Row: {
          active: boolean | null
          body_html: string
          body_text: string | null
          category: string
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_blast_template: boolean | null
          is_system: boolean | null
          last_used_at: string | null
          metadata: Json | null
          name: string
          parent_template_id: string | null
          preview_text: string | null
          status: string | null
          subject: string
          tags: string[] | null
          template_type: string | null
          thumbnail_url: string | null
          updated_at: string | null
          updated_by: string | null
          usage_count: number | null
          variables: Json | null
          version: number | null
        }
        Insert: {
          active?: boolean | null
          body_html: string
          body_text?: string | null
          category: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_blast_template?: boolean | null
          is_system?: boolean | null
          last_used_at?: string | null
          metadata?: Json | null
          name: string
          parent_template_id?: string | null
          preview_text?: string | null
          status?: string | null
          subject: string
          tags?: string[] | null
          template_type?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_count?: number | null
          variables?: Json | null
          version?: number | null
        }
        Update: {
          active?: boolean | null
          body_html?: string
          body_text?: string | null
          category?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_blast_template?: boolean | null
          is_system?: boolean | null
          last_used_at?: string | null
          metadata?: Json | null
          name?: string
          parent_template_id?: string | null
          preview_text?: string | null
          status?: string | null
          subject?: string
          tags?: string[] | null
          template_type?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_count?: number | null
          variables?: Json | null
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "email_templates_parent_template_id_fkey"
            columns: ["parent_template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_templates_parent_template_id_fkey"
            columns: ["parent_template_id"]
            isOneToOne: false
            referencedRelation: "template_performance_view"
            referencedColumns: ["id"]
          },
        ]
      }
      email_unsubscribes: {
        Row: {
          campaign_id: string | null
          email: string
          id: string
          metadata: Json | null
          reason: string | null
          unsubscribed_at: string
        }
        Insert: {
          campaign_id?: string | null
          email: string
          id?: string
          metadata?: Json | null
          reason?: string | null
          unsubscribed_at?: string
        }
        Update: {
          campaign_id?: string | null
          email?: string
          id?: string
          metadata?: Json | null
          reason?: string | null
          unsubscribed_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_unsubscribes_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "active_campaigns_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_unsubscribes_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaign_overview"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_unsubscribes_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "email_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      recipient_list_members: {
        Row: {
          added_at: string | null
          added_by: string | null
          email: string
          id: string
          is_active: boolean | null
          list_id: string
          member_data: Json | null
          member_id: string | null
          name: string | null
        }
        Insert: {
          added_at?: string | null
          added_by?: string | null
          email: string
          id?: string
          is_active?: boolean | null
          list_id: string
          member_data?: Json | null
          member_id?: string | null
          name?: string | null
        }
        Update: {
          added_at?: string | null
          added_by?: string | null
          email?: string
          id?: string
          is_active?: boolean | null
          list_id?: string
          member_data?: Json | null
          member_id?: string | null
          name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recipient_list_members_list_id_fkey"
            columns: ["list_id"]
            isOneToOne: false
            referencedRelation: "recipient_lists"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recipient_list_members_list_id_fkey"
            columns: ["list_id"]
            isOneToOne: false
            referencedRelation: "recipient_lists_summary"
            referencedColumns: ["id"]
          },
        ]
      }
      recipient_lists: {
        Row: {
          created_at: string | null
          created_by: string
          description: string | null
          filters: Json | null
          id: string
          is_static: boolean | null
          is_system: boolean | null
          last_calculated_at: string | null
          last_used_at: string | null
          list_type: string
          metadata: Json | null
          name: string
          recipient_count: number | null
          recipient_count_change: number | null
          status: string | null
          tags: string[] | null
          type: string | null
          updated_at: string | null
          updated_by: string | null
          usage_count: number | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          description?: string | null
          filters?: Json | null
          id?: string
          is_static?: boolean | null
          is_system?: boolean | null
          last_calculated_at?: string | null
          last_used_at?: string | null
          list_type?: string
          metadata?: Json | null
          name: string
          recipient_count?: number | null
          recipient_count_change?: number | null
          status?: string | null
          tags?: string[] | null
          type?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_count?: number | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          description?: string | null
          filters?: Json | null
          id?: string
          is_static?: boolean | null
          is_system?: boolean | null
          last_calculated_at?: string | null
          last_used_at?: string | null
          list_type?: string
          metadata?: Json | null
          name?: string
          recipient_count?: number | null
          recipient_count_change?: number | null
          status?: string | null
          tags?: string[] | null
          type?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_count?: number | null
        }
        Relationships: []
      }
      template_usage_stats: {
        Row: {
          avg_bounce_rate: number | null
          avg_click_rate: number | null
          avg_open_rate: number | null
          blast_usage: number | null
          campaigns_used: number | null
          created_at: string | null
          id: string
          last_calculated_at: string | null
          last_used_at: string | null
          monthly_usage: Json | null
          notification_usage: number | null
          popular_variables: string[] | null
          success_rate: number | null
          template_id: string
          total_usage: number | null
        }
        Insert: {
          avg_bounce_rate?: number | null
          avg_click_rate?: number | null
          avg_open_rate?: number | null
          blast_usage?: number | null
          campaigns_used?: number | null
          created_at?: string | null
          id?: string
          last_calculated_at?: string | null
          last_used_at?: string | null
          monthly_usage?: Json | null
          notification_usage?: number | null
          popular_variables?: string[] | null
          success_rate?: number | null
          template_id: string
          total_usage?: number | null
        }
        Update: {
          avg_bounce_rate?: number | null
          avg_click_rate?: number | null
          avg_open_rate?: number | null
          blast_usage?: number | null
          campaigns_used?: number | null
          created_at?: string | null
          id?: string
          last_calculated_at?: string | null
          last_used_at?: string | null
          monthly_usage?: Json | null
          notification_usage?: number | null
          popular_variables?: string[] | null
          success_rate?: number | null
          template_id?: string
          total_usage?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "template_usage_stats_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: true
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "template_usage_stats_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: true
            referencedRelation: "template_performance_view"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      active_campaigns_view: {
        Row: {
          bounce_rate: number | null
          click_rate: number | null
          clicked_count: number | null
          created_at: string | null
          delivery_rate: number | null
          id: string | null
          name: string | null
          open_rate: number | null
          opened_count: number | null
          scheduled_at: string | null
          sent_count: number | null
          status: string | null
          subject: string | null
          total_recipients: number | null
          unsubscribe_rate: number | null
        }
        Relationships: []
      }
      campaign_overview: {
        Row: {
          bounce_rate: number | null
          bounced_count: number | null
          click_rate: number | null
          clicked_count: number | null
          created_at: string | null
          delivery_rate: number | null
          failed_count: number | null
          id: string | null
          name: string | null
          open_rate: number | null
          opened_count: number | null
          recipient_list_count: number | null
          recipient_list_name: string | null
          scheduled_at: string | null
          sent_at: string | null
          sent_count: number | null
          status: string | null
          subject: string | null
          total_recipients: number | null
          updated_at: string | null
        }
        Relationships: []
      }
      daily_delivery_metrics: {
        Row: {
          bounce_rate_percent: number | null
          complaint_rate_percent: number | null
          complaints: number | null
          date: string | null
          delivery_rate_percent: number | null
          emails_delivered: number | null
          emails_sent: number | null
          hard_bounces: number | null
          soft_bounces: number | null
        }
        Relationships: []
      }
      email_delivery_analytics: {
        Row: {
          bounce_type: string | null
          complaint_type: string | null
          delivery_confirmed_at: string | null
          delivery_status: string | null
          delivery_time_seconds: number | null
          event_count: number | null
          queue_id: string | null
          queue_status: string | null
          sent_at: string | null
          ses_message_id: string | null
          subject: string | null
          to_email: string[] | null
        }
        Insert: {
          bounce_type?: string | null
          complaint_type?: string | null
          delivery_confirmed_at?: string | null
          delivery_status?: never
          delivery_time_seconds?: never
          event_count?: never
          queue_id?: string | null
          queue_status?: string | null
          sent_at?: string | null
          ses_message_id?: string | null
          subject?: string | null
          to_email?: string[] | null
        }
        Update: {
          bounce_type?: string | null
          complaint_type?: string | null
          delivery_confirmed_at?: string | null
          delivery_status?: never
          delivery_time_seconds?: never
          event_count?: never
          queue_id?: string | null
          queue_status?: string | null
          sent_at?: string | null
          ses_message_id?: string | null
          subject?: string | null
          to_email?: string[] | null
        }
        Relationships: []
      }
      email_events_structured: {
        Row: {
          campaign_id: string | null
          id: string | null
          ip_address: unknown | null
          metadata: Json | null
          recipient_email: string | null
          recipient_id: string | null
          timestamp: string | null
          type: string | null
          user_agent: string | null
        }
        Insert: {
          campaign_id?: never
          id?: string | null
          ip_address?: never
          metadata?: never
          recipient_email?: never
          recipient_id?: never
          timestamp?: string | null
          type?: string | null
          user_agent?: never
        }
        Update: {
          campaign_id?: never
          id?: string | null
          ip_address?: never
          metadata?: never
          recipient_email?: never
          recipient_id?: never
          timestamp?: string | null
          type?: string | null
          user_agent?: never
        }
        Relationships: []
      }
      email_queue_health: {
        Row: {
          active: boolean | null
          failed_emails: number | null
          health_status: string | null
          jobname: string | null
          pending_emails: number | null
          schedule: string | null
        }
        Insert: {
          active?: boolean | null
          failed_emails?: never
          health_status?: never
          jobname?: string | null
          pending_emails?: never
          schedule?: string | null
        }
        Update: {
          active?: boolean | null
          failed_emails?: never
          health_status?: never
          jobname?: string | null
          pending_emails?: never
          schedule?: string | null
        }
        Relationships: []
      }
      recipient_lists_summary: {
        Row: {
          actual_count: number | null
          created_at: string | null
          description: string | null
          id: string | null
          last_used_at: string | null
          list_type: string | null
          name: string | null
          recipient_count: number | null
          recipient_count_change: number | null
          status: string | null
          updated_at: string | null
          usage_count: number | null
        }
        Insert: {
          actual_count?: never
          created_at?: string | null
          description?: string | null
          id?: string | null
          last_used_at?: string | null
          list_type?: never
          name?: string | null
          recipient_count?: never
          recipient_count_change?: number | null
          status?: string | null
          updated_at?: string | null
          usage_count?: never
        }
        Update: {
          actual_count?: never
          created_at?: string | null
          description?: string | null
          id?: string | null
          last_used_at?: string | null
          list_type?: never
          name?: string | null
          recipient_count?: never
          recipient_count_change?: number | null
          status?: string | null
          updated_at?: string | null
          usage_count?: never
        }
        Relationships: []
      }
      suppression_analytics: {
        Row: {
          active_suppressions: number | null
          avg_bounce_count: number | null
          latest_bounce: string | null
          latest_complaint: string | null
          repeat_bounces: number | null
          repeat_complaints: number | null
          suppression_type: string | null
          total_suppressed: number | null
        }
        Relationships: []
      }
      template_performance_view: {
        Row: {
          avg_bounce_rate: number | null
          avg_click_rate: number | null
          avg_open_rate: number | null
          campaigns_used: number | null
          category: string | null
          created_at: string | null
          id: string | null
          last_used_at: string | null
          name: string | null
          status: string | null
          total_usage: number | null
          updated_at: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      calculate_campaign_analytics: {
        Args: { p_campaign_id: string }
        Returns: undefined
      }
      calculate_recipient_list: {
        Args: { p_list_id: string }
        Returns: number
      }
      get_campaign_statistics: {
        Args: { p_campaign_id: string }
        Returns: {
          campaign_id: string
          campaign_name: string
          status: string
          total_recipients: number
          sent_count: number
          delivered_count: number
          open_count: number
          unique_open_count: number
          click_count: number
          unique_click_count: number
          bounce_count: number
          unsubscribe_count: number
          delivery_rate: number
          open_rate: number
          click_rate: number
          bounce_rate: number
        }[]
      }
      get_delivery_stats: {
        Args: { days_back?: number }
        Returns: {
          date: string
          total_sent: number
          total_delivered: number
          total_bounced: number
          total_complaints: number
          delivery_rate: number
          bounce_rate: number
          complaint_rate: number
        }[]
      }
      get_email_processing_stats: {
        Args: { days_back?: number }
        Returns: {
          date: string
          total_processed: number
          successful: number
          failed: number
          success_rate: number
        }[]
      }
      get_email_queue_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          status: string
          count: number
          oldest_email: string
          newest_email: string
        }[]
      }
      is_email_suppressed: {
        Args: { email_addr: string }
        Returns: boolean
      }
      manage_email_queue_cron: {
        Args: { action: string; new_schedule?: string; batch_limit?: number }
        Returns: string
      }
      migrate_templates_for_blast: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      process_email_queue_trigger: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      process_ses_delivery_event: {
        Args: {
          ses_message_id_param: string
          event_type_param: string
          event_timestamp_param: string
          recipient_email_param: string
          event_data_param?: Json
        }
        Returns: string
      }
      queue_campaign_emails: {
        Args: { p_campaign_id: string; p_batch_size?: number }
        Returns: number
      }
      suppress_email_address: {
        Args: {
          email_addr: string
          suppression_type_param: string
          reason?: string
          bounce_type_param?: string
          bounce_sub_type_param?: string
          complaint_sub_type_param?: string
        }
        Returns: string
      }
      unsuppress_email_address: {
        Args: { email_addr: string }
        Returns: boolean
      }
      update_campaign_statistics: {
        Args: { p_campaign_id: string }
        Returns: undefined
      }
    }
    Enums: {
      email_campaign_status:
        | "DRAFT"
        | "SCHEDULED"
        | "SENDING"
        | "SENT"
        | "FAILED"
      email_delivery_status:
        | "PENDING"
        | "QUEUED"
        | "SENT"
        | "DELIVERED"
        | "BOUNCED"
        | "FAILED"
        | "OPENED"
        | "CLICKED"
        | "UNSUBSCRIBED"
        | "SPAM_REPORTED"
      email_priority: "LOW" | "NORMAL" | "HIGH" | "URGENT"
      membership_status: "ACTIVE" | "EXPIRED" | "PENDING" | "SUSPENDED"
      template_category:
        | "NEWSLETTER"
        | "PROMOTIONAL"
        | "ANNOUNCEMENT"
        | "WELCOME"
        | "FOLLOWUP"
        | "EVENT"
        | "SURVEY"
        | "NOTIFICATION"
        | "CUSTOM"
      template_status: "DRAFT" | "ACTIVE" | "ARCHIVED"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  finance: {
    Tables: {
      audit_log: {
        Row: {
          action: string
          changed_at: string | null
          changed_by: string | null
          changed_fields: Json | null
          id: string
          main_object_id: string
          main_object_type: string
          record_id: string
          table_name: string
        }
        Insert: {
          action: string
          changed_at?: string | null
          changed_by?: string | null
          changed_fields?: Json | null
          id?: string
          main_object_id: string
          main_object_type: string
          record_id: string
          table_name: string
        }
        Update: {
          action?: string
          changed_at?: string | null
          changed_by?: string | null
          changed_fields?: Json | null
          id?: string
          main_object_id?: string
          main_object_type?: string
          record_id?: string
          table_name?: string
        }
        Relationships: []
      }
      credit_note_applications: {
        Row: {
          amount_applied: number
          application_date: string
          created_at: string | null
          created_by: string
          credit_note_id: string
          id: string
          invoice_id: string
        }
        Insert: {
          amount_applied: number
          application_date?: string
          created_at?: string | null
          created_by: string
          credit_note_id: string
          id?: string
          invoice_id: string
        }
        Update: {
          amount_applied?: number
          application_date?: string
          created_at?: string | null
          created_by?: string
          credit_note_id?: string
          id?: string
          invoice_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "credit_note_applications_credit_note_id_fkey"
            columns: ["credit_note_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "credit_note_applications_credit_note_id_fkey"
            columns: ["credit_note_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_note_applications_credit_note_id_fkey"
            columns: ["credit_note_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "credit_note_applications_credit_note_id_fkey"
            columns: ["credit_note_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "credit_note_applications_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "credit_note_applications_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_note_applications_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "credit_note_applications_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
        ]
      }
      currencies: {
        Row: {
          code: string
          is_active: boolean | null
          name: string
          symbol: string | null
        }
        Insert: {
          code: string
          is_active?: boolean | null
          name: string
          symbol?: string | null
        }
        Update: {
          code?: string
          is_active?: boolean | null
          name?: string
          symbol?: string | null
        }
        Relationships: []
      }
      customer_categories: {
        Row: {
          category_name: string
          description: string | null
          id: string
          is_active: boolean | null
        }
        Insert: {
          category_name: string
          description?: string | null
          id?: string
          is_active?: boolean | null
        }
        Update: {
          category_name?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
        }
        Relationships: []
      }
      customer_statements: {
        Row: {
          closing_balance: number | null
          created_at: string | null
          created_by: string
          customer_id: string
          entity_id: string
          from_date: string
          id: string
          opening_balance: number | null
          statement_date: string
          statement_file_path: string | null
          statement_number: string
          status: Database["finance"]["Enums"]["statement_status_enum"] | null
          to_date: string
          total_adjustments: number | null
          total_invoices: number | null
          total_payments: number | null
        }
        Insert: {
          closing_balance?: number | null
          created_at?: string | null
          created_by: string
          customer_id: string
          entity_id: string
          from_date: string
          id?: string
          opening_balance?: number | null
          statement_date: string
          statement_file_path?: string | null
          statement_number: string
          status?: Database["finance"]["Enums"]["statement_status_enum"] | null
          to_date: string
          total_adjustments?: number | null
          total_invoices?: number | null
          total_payments?: number | null
        }
        Update: {
          closing_balance?: number | null
          created_at?: string | null
          created_by?: string
          customer_id?: string
          entity_id?: string
          from_date?: string
          id?: string
          opening_balance?: number | null
          statement_date?: string
          statement_file_path?: string | null
          statement_number?: string
          status?: Database["finance"]["Enums"]["statement_status_enum"] | null
          to_date?: string
          total_adjustments?: number | null
          total_invoices?: number | null
          total_payments?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_statements_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_statements_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_status_history: {
        Row: {
          changed_at: string | null
          changed_by: string | null
          created_at: string | null
          customer_id: string
          id: string
          new_status: string
          old_status: string | null
          reason: string | null
        }
        Insert: {
          changed_at?: string | null
          changed_by?: string | null
          created_at?: string | null
          customer_id: string
          id?: string
          new_status: string
          old_status?: string | null
          reason?: string | null
        }
        Update: {
          changed_at?: string | null
          changed_by?: string | null
          created_at?: string | null
          customer_id?: string
          id?: string
          new_status?: string
          old_status?: string | null
          reason?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_status_history_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          archived_at: string | null
          billing_address: Json | null
          category: string | null
          contact_persons: Json | null
          created_at: string | null
          created_by: string | null
          credit_limit: number | null
          currency_code: string | null
          customer_code: string | null
          customer_type:
            | Database["finance"]["Enums"]["customer_type_enum"]
            | null
          display_name: string | null
          email: string
          id: string
          is_gst_registered: boolean | null
          metadata: Json | null
          name: string
          notes: string | null
          payment_terms: number | null
          phone: string | null
          registration_number: string | null
          shipping_address: Json | null
          shipping_addresses: Json | null
          status: Database["finance"]["Enums"]["customer_status_enum"] | null
          status_change_reason: string | null
          tags: string[] | null
          tax_registration_number: string | null
          updated_at: string | null
          updated_by: string | null
          user_id: string | null
          website: string | null
        }
        Insert: {
          archived_at?: string | null
          billing_address?: Json | null
          category?: string | null
          contact_persons?: Json | null
          created_at?: string | null
          created_by?: string | null
          credit_limit?: number | null
          currency_code?: string | null
          customer_code?: string | null
          customer_type?:
            | Database["finance"]["Enums"]["customer_type_enum"]
            | null
          display_name?: string | null
          email: string
          id?: string
          is_gst_registered?: boolean | null
          metadata?: Json | null
          name: string
          notes?: string | null
          payment_terms?: number | null
          phone?: string | null
          registration_number?: string | null
          shipping_address?: Json | null
          shipping_addresses?: Json | null
          status?: Database["finance"]["Enums"]["customer_status_enum"] | null
          status_change_reason?: string | null
          tags?: string[] | null
          tax_registration_number?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
          website?: string | null
        }
        Update: {
          archived_at?: string | null
          billing_address?: Json | null
          category?: string | null
          contact_persons?: Json | null
          created_at?: string | null
          created_by?: string | null
          credit_limit?: number | null
          currency_code?: string | null
          customer_code?: string | null
          customer_type?:
            | Database["finance"]["Enums"]["customer_type_enum"]
            | null
          display_name?: string | null
          email?: string
          id?: string
          is_gst_registered?: boolean | null
          metadata?: Json | null
          name?: string
          notes?: string | null
          payment_terms?: number | null
          phone?: string | null
          registration_number?: string | null
          shipping_address?: Json | null
          shipping_addresses?: Json | null
          status?: Database["finance"]["Enums"]["customer_status_enum"] | null
          status_change_reason?: string | null
          tags?: string[] | null
          tax_registration_number?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
          website?: string | null
        }
        Relationships: []
      }
      entities: {
        Row: {
          bc_entity_id: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          entity_type: Database["finance"]["Enums"]["entity_type"]
          filename: string | null
          id: string
          is_active: boolean | null
          is_default: boolean | null
          merchant_abbreviation: string | null
          merchant_id: string
          name: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          bc_entity_id?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          entity_type: Database["finance"]["Enums"]["entity_type"]
          filename?: string | null
          id?: string
          is_active?: boolean | null
          is_default?: boolean | null
          merchant_abbreviation?: string | null
          merchant_id: string
          name: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          bc_entity_id?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          entity_type?: Database["finance"]["Enums"]["entity_type"]
          filename?: string | null
          id?: string
          is_active?: boolean | null
          is_default?: boolean | null
          merchant_abbreviation?: string | null
          merchant_id?: string
          name?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      gl_account_departments: {
        Row: {
          account_id: string | null
          created_at: string | null
          department_code: string
          id: string
          is_default: boolean | null
        }
        Insert: {
          account_id?: string | null
          created_at?: string | null
          department_code: string
          id?: string
          is_default?: boolean | null
        }
        Update: {
          account_id?: string | null
          created_at?: string | null
          department_code?: string
          id?: string
          is_default?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "gl_account_departments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "gl_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      gl_account_entity_settings: {
        Row: {
          account_id: string | null
          created_at: string | null
          entity_id: string | null
          entity_specific_code: string | null
          entity_specific_name: string | null
          id: string
          is_active: boolean | null
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          created_at?: string | null
          entity_id?: string | null
          entity_specific_code?: string | null
          entity_specific_name?: string | null
          id?: string
          is_active?: boolean | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          created_at?: string | null
          entity_id?: string | null
          entity_specific_code?: string | null
          entity_specific_name?: string | null
          id?: string
          is_active?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "gl_account_entity_settings_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "gl_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "gl_account_entity_settings_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      gl_accounts: {
        Row: {
          account_category: string
          account_code: string
          account_level: number
          account_name: string
          account_subcategory: string | null
          account_type: string
          allow_manual_posting: boolean | null
          created_at: string | null
          created_by: string | null
          default_department_code: string | null
          entity_restrictions: string[] | null
          external_codes: Json | null
          financial_statement_type: string
          hierarchy_path: string | null
          id: string
          is_active: boolean | null
          is_budgetable: boolean | null
          is_global: boolean | null
          is_reconcilable: boolean | null
          is_system_account: boolean | null
          legacy_mappings: Json | null
          normal_balance: string
          parent_account_id: string | null
          requires_department: boolean | null
          short_name: string | null
          sort_order: number | null
          totalling_formula: string | null
          updated_at: string | null
          updated_by: string | null
          version: number | null
        }
        Insert: {
          account_category: string
          account_code: string
          account_level?: number
          account_name: string
          account_subcategory?: string | null
          account_type: string
          allow_manual_posting?: boolean | null
          created_at?: string | null
          created_by?: string | null
          default_department_code?: string | null
          entity_restrictions?: string[] | null
          external_codes?: Json | null
          financial_statement_type: string
          hierarchy_path?: string | null
          id?: string
          is_active?: boolean | null
          is_budgetable?: boolean | null
          is_global?: boolean | null
          is_reconcilable?: boolean | null
          is_system_account?: boolean | null
          legacy_mappings?: Json | null
          normal_balance: string
          parent_account_id?: string | null
          requires_department?: boolean | null
          short_name?: string | null
          sort_order?: number | null
          totalling_formula?: string | null
          updated_at?: string | null
          updated_by?: string | null
          version?: number | null
        }
        Update: {
          account_category?: string
          account_code?: string
          account_level?: number
          account_name?: string
          account_subcategory?: string | null
          account_type?: string
          allow_manual_posting?: boolean | null
          created_at?: string | null
          created_by?: string | null
          default_department_code?: string | null
          entity_restrictions?: string[] | null
          external_codes?: Json | null
          financial_statement_type?: string
          hierarchy_path?: string | null
          id?: string
          is_active?: boolean | null
          is_budgetable?: boolean | null
          is_global?: boolean | null
          is_reconcilable?: boolean | null
          is_system_account?: boolean | null
          legacy_mappings?: Json | null
          normal_balance?: string
          parent_account_id?: string | null
          requires_department?: boolean | null
          short_name?: string | null
          sort_order?: number | null
          totalling_formula?: string | null
          updated_at?: string | null
          updated_by?: string | null
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "gl_accounts_parent_account_id_fkey"
            columns: ["parent_account_id"]
            isOneToOne: false
            referencedRelation: "gl_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_items: {
        Row: {
          created_at: string | null
          description: string | null
          gl_account_code: string | null
          id: string
          invoice_id: string | null
          item_id: string | null
          line_total: number
          metadata: Json | null
          quantity: number
          tax_amount: number | null
          tax_rate: number | null
          unit_price: number
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          gl_account_code?: string | null
          id?: string
          invoice_id?: string | null
          item_id?: string | null
          line_total: number
          metadata?: Json | null
          quantity: number
          tax_amount?: number | null
          tax_rate?: number | null
          unit_price: number
        }
        Update: {
          created_at?: string | null
          description?: string | null
          gl_account_code?: string | null
          id?: string
          invoice_id?: string | null
          item_id?: string | null
          line_total?: number
          metadata?: Json | null
          quantity?: number
          tax_amount?: number | null
          tax_rate?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoice_items_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "items"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_sources: {
        Row: {
          created_at: string | null
          id: string
          invoice_id: string | null
          source_id: string
          source_type: Database["finance"]["Enums"]["invoice_source_type"]
        }
        Insert: {
          created_at?: string | null
          id?: string
          invoice_id?: string | null
          source_id: string
          source_type: Database["finance"]["Enums"]["invoice_source_type"]
        }
        Update: {
          created_at?: string | null
          id?: string
          invoice_id?: string | null
          source_id?: string
          source_type?: Database["finance"]["Enums"]["invoice_source_type"]
        }
        Relationships: [
          {
            foreignKeyName: "invoice_sources_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoice_sources_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_sources_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoice_sources_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
        ]
      }
      invoices: {
        Row: {
          amount_applied: number | null
          amount_paid: number | null
          archived_at: string | null
          balance_due: number | null
          bill_to: string | null
          created_at: string | null
          created_by: string | null
          customer_id: string | null
          delivery_address: Json | null
          delivery_date: string | null
          due_date: string
          entity_id: string
          exchange_rate: number | null
          file_name: string | null
          id: string
          invoice_number: string
          invoice_type: Database["finance"]["Enums"]["invoice_type_enum"] | null
          issue_date: string
          metadata: Json | null
          notes: string | null
          parent_invoice_id: string | null
          payment_terms: string | null
          sales_order_reference: string | null
          status: Database["finance"]["Enums"]["invoice_status"] | null
          subtotal: number
          tax_amount: number | null
          total_amount: number
          updated_at: string | null
          updated_by: string | null
          user_id: string | null
        }
        Insert: {
          amount_applied?: number | null
          amount_paid?: number | null
          archived_at?: string | null
          balance_due?: number | null
          bill_to?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          delivery_address?: Json | null
          delivery_date?: string | null
          due_date: string
          entity_id: string
          exchange_rate?: number | null
          file_name?: string | null
          id?: string
          invoice_number: string
          invoice_type?:
            | Database["finance"]["Enums"]["invoice_type_enum"]
            | null
          issue_date: string
          metadata?: Json | null
          notes?: string | null
          parent_invoice_id?: string | null
          payment_terms?: string | null
          sales_order_reference?: string | null
          status?: Database["finance"]["Enums"]["invoice_status"] | null
          subtotal: number
          tax_amount?: number | null
          total_amount: number
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Update: {
          amount_applied?: number | null
          amount_paid?: number | null
          archived_at?: string | null
          balance_due?: number | null
          bill_to?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          delivery_address?: Json | null
          delivery_date?: string | null
          due_date?: string
          entity_id?: string
          exchange_rate?: number | null
          file_name?: string | null
          id?: string
          invoice_number?: string
          invoice_type?:
            | Database["finance"]["Enums"]["invoice_type_enum"]
            | null
          issue_date?: string
          metadata?: Json | null
          notes?: string | null
          parent_invoice_id?: string | null
          payment_terms?: string | null
          sales_order_reference?: string | null
          status?: Database["finance"]["Enums"]["invoice_status"] | null
          subtotal?: number
          tax_amount?: number | null
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_parent_invoice_id_fkey"
            columns: ["parent_invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoices_parent_invoice_id_fkey"
            columns: ["parent_invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_parent_invoice_id_fkey"
            columns: ["parent_invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "invoices_parent_invoice_id_fkey"
            columns: ["parent_invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
        ]
      }
      items: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          tax_rate: number | null
          unit_price: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          tax_rate?: number | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          tax_rate?: number | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      payment_allocations: {
        Row: {
          allocation_date: string | null
          amount_allocated: number
          created_at: string | null
          created_by: string | null
          id: string
          invoice_id: string | null
          notes: string | null
          receipt_id: string | null
        }
        Insert: {
          allocation_date?: string | null
          amount_allocated: number
          created_at?: string | null
          created_by?: string | null
          id?: string
          invoice_id?: string | null
          notes?: string | null
          receipt_id?: string | null
        }
        Update: {
          allocation_date?: string | null
          amount_allocated?: number
          created_at?: string | null
          created_by?: string | null
          id?: string
          invoice_id?: string | null
          notes?: string | null
          receipt_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_allocations_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "payment_allocations_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payment_allocations_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "payment_allocations_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "payment_allocations_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["receipt_id"]
          },
          {
            foreignKeyName: "payment_allocations_receipt_id_fkey"
            columns: ["receipt_id"]
            isOneToOne: false
            referencedRelation: "receipts"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_gateway_logs: {
        Row: {
          error_code: string | null
          error_message: string | null
          http_status: number | null
          id: string
          interaction_type: string
          is_success: boolean | null
          raw_request: string | null
          raw_response: string | null
          request_payload: Json | null
          request_timestamp: string | null
          response_payload: Json | null
          response_timestamp: string | null
          session_id: string | null
        }
        Insert: {
          error_code?: string | null
          error_message?: string | null
          http_status?: number | null
          id?: string
          interaction_type: string
          is_success?: boolean | null
          raw_request?: string | null
          raw_response?: string | null
          request_payload?: Json | null
          request_timestamp?: string | null
          response_payload?: Json | null
          response_timestamp?: string | null
          session_id?: string | null
        }
        Update: {
          error_code?: string | null
          error_message?: string | null
          http_status?: number | null
          id?: string
          interaction_type?: string
          is_success?: boolean | null
          raw_request?: string | null
          raw_response?: string | null
          request_payload?: Json | null
          request_timestamp?: string | null
          response_payload?: Json | null
          response_timestamp?: string | null
          session_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_gateway_logs_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "payment_gateway_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_gateway_sessions: {
        Row: {
          amount: number
          attempt_number: number
          backend_return_url: string
          created_at: string | null
          created_by: string | null
          currency_code: string
          expiry_time: string | null
          frontend_return_url: string
          id: string
          invoice_id: string | null
          invoice_no: string
          is_latest_attempt: boolean | null
          merchant_id: string
          payment_channel: string | null
          payment_description: string | null
          payment_token: string | null
          status: Database["finance"]["Enums"]["payment_gateway_status"] | null
          updated_at: string | null
          web_payment_url: string | null
        }
        Insert: {
          amount: number
          attempt_number?: number
          backend_return_url: string
          created_at?: string | null
          created_by?: string | null
          currency_code: string
          expiry_time?: string | null
          frontend_return_url: string
          id?: string
          invoice_id?: string | null
          invoice_no: string
          is_latest_attempt?: boolean | null
          merchant_id: string
          payment_channel?: string | null
          payment_description?: string | null
          payment_token?: string | null
          status?: Database["finance"]["Enums"]["payment_gateway_status"] | null
          updated_at?: string | null
          web_payment_url?: string | null
        }
        Update: {
          amount?: number
          attempt_number?: number
          backend_return_url?: string
          created_at?: string | null
          created_by?: string | null
          currency_code?: string
          expiry_time?: string | null
          frontend_return_url?: string
          id?: string
          invoice_id?: string | null
          invoice_no?: string
          is_latest_attempt?: boolean | null
          merchant_id?: string
          payment_channel?: string | null
          payment_description?: string | null
          payment_token?: string | null
          status?: Database["finance"]["Enums"]["payment_gateway_status"] | null
          updated_at?: string | null
          web_payment_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_gateway_sessions_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "payment_gateway_sessions_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payment_gateway_sessions_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "payment_gateway_sessions_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
        ]
      }
      quotation_items: {
        Row: {
          created_at: string | null
          description: string
          discount_percentage: number | null
          gl_account_code: string | null
          id: string
          item_id: string | null
          line_number: number
          line_total: number
          quantity: number
          quotation_id: string
          tax_amount: number | null
          tax_code: string | null
          tax_rate: number | null
          unit_price: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description: string
          discount_percentage?: number | null
          gl_account_code?: string | null
          id?: string
          item_id?: string | null
          line_number: number
          line_total: number
          quantity: number
          quotation_id: string
          tax_amount?: number | null
          tax_code?: string | null
          tax_rate?: number | null
          unit_price: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string
          discount_percentage?: number | null
          gl_account_code?: string | null
          id?: string
          item_id?: string | null
          line_number?: number
          line_total?: number
          quantity?: number
          quotation_id?: string
          tax_amount?: number | null
          tax_code?: string | null
          tax_rate?: number | null
          unit_price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quotation_items_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotation_items_quotation_id_fkey"
            columns: ["quotation_id"]
            isOneToOne: false
            referencedRelation: "quotations"
            referencedColumns: ["id"]
          },
        ]
      }
      quotations: {
        Row: {
          archived_at: string | null
          created_at: string | null
          created_by: string
          currency_code: string | null
          customer_id: string
          customer_reference: string | null
          description: string | null
          entity_id: string
          exchange_rate: number | null
          id: string
          project_reference: string | null
          quotation_date: string
          quotation_number: string
          status: Database["finance"]["Enums"]["quotation_status"] | null
          subtotal: number
          tax_amount: number
          title: string
          total_amount: number
          updated_at: string | null
          updated_by: string | null
          valid_until: string
        }
        Insert: {
          archived_at?: string | null
          created_at?: string | null
          created_by: string
          currency_code?: string | null
          customer_id: string
          customer_reference?: string | null
          description?: string | null
          entity_id: string
          exchange_rate?: number | null
          id?: string
          project_reference?: string | null
          quotation_date?: string
          quotation_number: string
          status?: Database["finance"]["Enums"]["quotation_status"] | null
          subtotal?: number
          tax_amount?: number
          title: string
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
          valid_until: string
        }
        Update: {
          archived_at?: string | null
          created_at?: string | null
          created_by?: string
          currency_code?: string | null
          customer_id?: string
          customer_reference?: string | null
          description?: string | null
          entity_id?: string
          exchange_rate?: number | null
          id?: string
          project_reference?: string | null
          quotation_date?: string
          quotation_number?: string
          status?: Database["finance"]["Enums"]["quotation_status"] | null
          subtotal?: number
          tax_amount?: number
          title?: string
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
          valid_until?: string
        }
        Relationships: [
          {
            foreignKeyName: "quotations_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotations_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      receipts: {
        Row: {
          amount_paid: number
          archived_at: string | null
          created_at: string | null
          created_by: string | null
          customer_id: string | null
          entity_id: string | null
          file_name: string | null
          id: string
          invoice_id: string | null
          notes: string | null
          payment_date: string
          payment_method: Database["finance"]["Enums"]["payment_method_type"]
          payment_reference: string | null
          payment_status: Database["finance"]["Enums"]["payment_status"] | null
          receipt_number: string
          receipt_type: Database["finance"]["Enums"]["receipt_type_enum"] | null
          transaction_id: string | null
          user_id: string | null
        }
        Insert: {
          amount_paid: number
          archived_at?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          entity_id?: string | null
          file_name?: string | null
          id?: string
          invoice_id?: string | null
          notes?: string | null
          payment_date: string
          payment_method: Database["finance"]["Enums"]["payment_method_type"]
          payment_reference?: string | null
          payment_status?: Database["finance"]["Enums"]["payment_status"] | null
          receipt_number: string
          receipt_type?:
            | Database["finance"]["Enums"]["receipt_type_enum"]
            | null
          transaction_id?: string | null
          user_id?: string | null
        }
        Update: {
          amount_paid?: number
          archived_at?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          entity_id?: string | null
          file_name?: string | null
          id?: string
          invoice_id?: string | null
          notes?: string | null
          payment_date?: string
          payment_method?: Database["finance"]["Enums"]["payment_method_type"]
          payment_reference?: string | null
          payment_status?: Database["finance"]["Enums"]["payment_status"] | null
          receipt_number?: string
          receipt_type?:
            | Database["finance"]["Enums"]["receipt_type_enum"]
            | null
          transaction_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "receipts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "receipts_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "receipts_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoice_details"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "receipts_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "receipts_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "receipt_user"
            referencedColumns: ["invoice_id"]
          },
          {
            foreignKeyName: "receipts_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "source_payment_status"
            referencedColumns: ["invoice_id"]
          },
        ]
      }
      recurring_invoice_templates: {
        Row: {
          created_at: string | null
          created_by: string
          customer_id: string
          end_date: string | null
          entity_id: string
          frequency: Database["finance"]["Enums"]["interval_enum"] | null
          id: string
          invoice_template: Json
          next_invoice_date: string
          start_date: string
          status: Database["finance"]["Enums"]["template_status_enum"] | null
          template_name: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          customer_id: string
          end_date?: string | null
          entity_id: string
          frequency?: Database["finance"]["Enums"]["interval_enum"] | null
          id?: string
          invoice_template: Json
          next_invoice_date: string
          start_date: string
          status?: Database["finance"]["Enums"]["template_status_enum"] | null
          template_name: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          customer_id?: string
          end_date?: string | null
          entity_id?: string
          frequency?: Database["finance"]["Enums"]["interval_enum"] | null
          id?: string
          invoice_template?: Json
          next_invoice_date?: string
          start_date?: string
          status?: Database["finance"]["Enums"]["template_status_enum"] | null
          template_name?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recurring_invoice_templates_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recurring_invoice_templates_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      sales_order_items: {
        Row: {
          created_at: string | null
          description: string
          discount_percentage: number | null
          gl_account_code: string | null
          id: string
          item_id: string | null
          line_number: number
          line_total: number
          quantity: number
          quantity_delivered: number | null
          quantity_invoiced: number | null
          sales_order_id: string
          tax_amount: number | null
          tax_code: string | null
          tax_rate: number | null
          unit_price: number
        }
        Insert: {
          created_at?: string | null
          description: string
          discount_percentage?: number | null
          gl_account_code?: string | null
          id?: string
          item_id?: string | null
          line_number: number
          line_total: number
          quantity: number
          quantity_delivered?: number | null
          quantity_invoiced?: number | null
          sales_order_id: string
          tax_amount?: number | null
          tax_code?: string | null
          tax_rate?: number | null
          unit_price: number
        }
        Update: {
          created_at?: string | null
          description?: string
          discount_percentage?: number | null
          gl_account_code?: string | null
          id?: string
          item_id?: string | null
          line_number?: number
          line_total?: number
          quantity?: number
          quantity_delivered?: number | null
          quantity_invoiced?: number | null
          sales_order_id?: string
          tax_amount?: number | null
          tax_code?: string | null
          tax_rate?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "sales_order_items_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_order_items_sales_order_id_fkey"
            columns: ["sales_order_id"]
            isOneToOne: false
            referencedRelation: "sales_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      sales_orders: {
        Row: {
          archived_at: string | null
          created_at: string | null
          created_by: string
          currency_code: string | null
          customer_id: string
          customer_po_reference: string | null
          delivery_address: Json | null
          entity_id: string
          exchange_rate: number | null
          expected_delivery_date: string | null
          id: string
          order_date: string
          order_number: string
          project_reference: string | null
          quotation_id: string | null
          status: Database["finance"]["Enums"]["sales_order_status_enum"] | null
          subtotal: number
          tax_amount: number
          total_amount: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          archived_at?: string | null
          created_at?: string | null
          created_by: string
          currency_code?: string | null
          customer_id: string
          customer_po_reference?: string | null
          delivery_address?: Json | null
          entity_id: string
          exchange_rate?: number | null
          expected_delivery_date?: string | null
          id?: string
          order_date?: string
          order_number: string
          project_reference?: string | null
          quotation_id?: string | null
          status?:
            | Database["finance"]["Enums"]["sales_order_status_enum"]
            | null
          subtotal?: number
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          archived_at?: string | null
          created_at?: string | null
          created_by?: string
          currency_code?: string | null
          customer_id?: string
          customer_po_reference?: string | null
          delivery_address?: Json | null
          entity_id?: string
          exchange_rate?: number | null
          expected_delivery_date?: string | null
          id?: string
          order_date?: string
          order_number?: string
          project_reference?: string | null
          quotation_id?: string | null
          status?:
            | Database["finance"]["Enums"]["sales_order_status_enum"]
            | null
          subtotal?: number
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sales_orders_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_orders_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_orders_quotation_id_fkey"
            columns: ["quotation_id"]
            isOneToOne: false
            referencedRelation: "quotations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      invoice_audit_log_view: {
        Row: {
          action: string | null
          changed_by_user: string | null
          field_name: string | null
          invoice_id: string | null
          invoice_number: string | null
          log_date: string | null
          new_value: string | null
          old_value: string | null
          table_name: string | null
        }
        Relationships: []
      }
      invoice_details: {
        Row: {
          amount_paid: number | null
          balance_due: number | null
          created_at: string | null
          due_date: string | null
          email: string | null
          file_name: string | null
          full_name: string | null
          invoice_id: string | null
          invoice_number: string | null
          issue_date: string | null
          membership_type: string | null
          membership_type_name: string | null
          notes: string | null
          payment_terms: string | null
          source_id: string | null
          source_type:
            | Database["finance"]["Enums"]["invoice_source_type"]
            | null
          status: Database["finance"]["Enums"]["invoice_status"] | null
          subtotal: number | null
          tax_amount: number | null
          total_amount: number | null
          user_id: string | null
        }
        Relationships: []
      }
      receipt_user: {
        Row: {
          amount_paid: number | null
          created_at: string | null
          email: string | null
          file_name: string | null
          full_name: string | null
          invoice_id: string | null
          invoice_number: string | null
          notes: string | null
          payment_date: string | null
          payment_method:
            | Database["finance"]["Enums"]["payment_method_type"]
            | null
          payment_reference: string | null
          payment_status: Database["finance"]["Enums"]["payment_status"] | null
          receipt_id: string | null
          receipt_number: string | null
          subtotal: number | null
          tax_amount: number | null
          total_amount: number | null
          transaction_id: string | null
          user_id: string | null
        }
        Relationships: []
      }
      source_payment_status: {
        Row: {
          amount_paid: number | null
          balance_due: number | null
          invoice_created_at: string | null
          invoice_id: string | null
          invoice_number: string | null
          invoice_status: Database["finance"]["Enums"]["invoice_status"] | null
          is_paid: boolean | null
          requires_payment: boolean | null
          source_id: string | null
          source_type:
            | Database["finance"]["Enums"]["invoice_source_type"]
            | null
          total_amount: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      build_hierarchy_path: {
        Args: { p_account_id: string }
        Returns: string
      }
      calculate_account_level: {
        Args: { p_account_id: string }
        Returns: number
      }
      check_gl_account_usage: {
        Args: { p_account_id: string }
        Returns: boolean
      }
      convert_quotation_to_order: {
        Args: { quotation_id: string }
        Returns: string
      }
      filter_audit_fields: {
        Args: { record: Json; excluded: string[] }
        Returns: Json
      }
      generate_credit_note_number: {
        Args: { p_entity_id: string }
        Returns: string
      }
      generate_customer_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_invoice_number: {
        Args: { p_entity_id: string }
        Returns: string
      }
      generate_quotation_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_receipt_number: {
        Args: { p_entity_id: string }
        Returns: string
      }
      generate_so_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_statement_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_account_category: {
        Args: { p_account_code: string }
        Returns: string
      }
      get_next_receipt_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_normal_balance: {
        Args: { p_category: string }
        Returns: string
      }
      validate_customer_data: {
        Args: { customer_data: Json }
        Returns: boolean
      }
    }
    Enums: {
      certificate_source_type: "MEMBERSHIP" | "PROGRAMME" | "OTHER"
      customer_status_enum: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "BLACKLISTED"
      customer_type_enum: "INDIVIDUAL" | "COMPANY" | "MEMBER" | "NON_MEMBER"
      entity_type:
        | "ORGANIZATION"
        | "ENTERPRISE"
        | "CHARITY"
        | "GOVERNMENT"
        | "OTHER"
      interval_enum:
        | "WEEKLY"
        | "MONTHLY"
        | "QUARTERLY"
        | "SEMI_ANNUALLY"
        | "ANNUALLY"
      invoice_source_type: "MEMBERSHIP" | "PROGRAMME" | "OTHER"
      invoice_status:
        | "DRAFT"
        | "UNPAID"
        | "SENT"
        | "PARTIALLY_PAID"
        | "PAID"
        | "VOID"
        | "OVERDUE"
      invoice_type_enum:
        | "STANDARD"
        | "PROFORMA"
        | "RECURRING"
        | "ADJUSTMENT"
        | "CREDIT_NOTE"
      payment_gateway_status:
        | "INITIALIZED"
        | "PENDING_PAYMENT"
        | "PROCESSING"
        | "SUCCESS"
        | "FAILED"
        | "CANCELLED"
        | "EXPIRED"
      payment_method_type:
        | "CASH"
        | "BANK_TRANSFER"
        | "CREDIT_CARD"
        | "CHEQUE"
        | "DIGITAL_WALLET"
        | "2C2P"
        | "SFC"
        | "GIRO"
        | "FAST"
      payment_status: "SUCCESS" | "PENDING" | "FAILED" | "VOID"
      quotation_status:
        | "DRAFT"
        | "SENT"
        | "ACCEPTED"
        | "REJECTED"
        | "EXPIRED"
        | "CANCELLED"
      receipt_type_enum: "PAYMENT" | "REFUND" | "ADJUSTMENT"
      sales_order_status_enum:
        | "DRAFT"
        | "CONFIRMED"
        | "PARTIALLY_INVOICED"
        | "INVOICED"
        | "DELIVERED"
        | "CANCELLED"
      statement_status_enum: "GENERATED" | "SENT" | "ACKNOWLEDGED"
      template_status_enum: "ACTIVE" | "PAUSED" | "COMPLETED" | "CANCELLED"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  notification: {
    Tables: {
      notification_logs: {
        Row: {
          created_at: string | null
          error_message: string | null
          event_type: string
          id: string
          recipients: Json
          reference_id: string
          retry_count: number | null
          sent_at: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          event_type: string
          id?: string
          recipients: Json
          reference_id: string
          retry_count?: number | null
          sent_at?: string | null
          status: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          event_type?: string
          id?: string
          recipients?: Json
          reference_id?: string
          retry_count?: number | null
          sent_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_logs_event_type_fkey"
            columns: ["event_type"]
            isOneToOne: false
            referencedRelation: "notification_templates"
            referencedColumns: ["event_type"]
          },
        ]
      }
      notification_templates: {
        Row: {
          attachment_config: Json | null
          body_template_html: string
          body_template_text: string | null
          created_at: string | null
          data_query: string
          event_type: string
          id: string
          is_active: boolean | null
          recipient_rules: Json
          subject_template: string
          updated_at: string | null
        }
        Insert: {
          attachment_config?: Json | null
          body_template_html: string
          body_template_text?: string | null
          created_at?: string | null
          data_query: string
          event_type: string
          id?: string
          is_active?: boolean | null
          recipient_rules: Json
          subject_template: string
          updated_at?: string | null
        }
        Update: {
          attachment_config?: Json | null
          body_template_html?: string
          body_template_text?: string | null
          created_at?: string | null
          data_query?: string
          event_type?: string
          id?: string
          is_active?: boolean | null
          recipient_rules?: Json
          subject_template?: string
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  organisation: {
    Tables: {
      committee_members: {
        Row: {
          committee_id: string | null
          created_at: string | null
          end_date: string | null
          id: string
          is_active: boolean | null
          role: string | null
          staff_id: string | null
          start_date: string | null
        }
        Insert: {
          committee_id?: string | null
          created_at?: string | null
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          role?: string | null
          staff_id?: string | null
          start_date?: string | null
        }
        Update: {
          committee_id?: string | null
          created_at?: string | null
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          role?: string | null
          staff_id?: string | null
          start_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "committee_members_committee_id_fkey"
            columns: ["committee_id"]
            isOneToOne: false
            referencedRelation: "committee_details"
            referencedColumns: ["committee_id"]
          },
          {
            foreignKeyName: "committee_members_committee_id_fkey"
            columns: ["committee_id"]
            isOneToOne: false
            referencedRelation: "committees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "committee_members_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "active_staff"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "committee_members_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "staff"
            referencedColumns: ["id"]
          },
        ]
      }
      committees: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      departments: {
        Row: {
          code: string | null
          created_at: string | null
          entity_id: string | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          code?: string | null
          created_at?: string | null
          entity_id?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          code?: string | null
          created_at?: string | null
          entity_id?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      staff: {
        Row: {
          contact_no: string | null
          created_at: string | null
          department_id: string | null
          email: string | null
          employee_id: string | null
          id: string
          is_active: boolean | null
          name: string
          position: string | null
          updated_at: string | null
        }
        Insert: {
          contact_no?: string | null
          created_at?: string | null
          department_id?: string | null
          email?: string | null
          employee_id?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          position?: string | null
          updated_at?: string | null
        }
        Update: {
          contact_no?: string | null
          created_at?: string | null
          department_id?: string | null
          email?: string | null
          employee_id?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          position?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "staff_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      active_staff: {
        Row: {
          contact_no: string | null
          created_at: string | null
          department_id: string | null
          email: string | null
          employee_id: string | null
          id: string | null
          is_active: boolean | null
          name: string | null
          position: string | null
          updated_at: string | null
        }
        Insert: {
          contact_no?: string | null
          created_at?: string | null
          department_id?: string | null
          email?: string | null
          employee_id?: string | null
          id?: string | null
          is_active?: boolean | null
          name?: string | null
          position?: string | null
          updated_at?: string | null
        }
        Update: {
          contact_no?: string | null
          created_at?: string | null
          department_id?: string | null
          email?: string | null
          employee_id?: string | null
          id?: string | null
          is_active?: boolean | null
          name?: string | null
          position?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "staff_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
        ]
      }
      committee_details: {
        Row: {
          committee_id: string | null
          committee_name: string | null
          department_name: string | null
          role: string | null
          staff_email: string | null
          staff_name: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  points: {
    Tables: {
      member_points: {
        Row: {
          allocated_at: string | null
          allocated_by: string | null
          allocation_type: Database["points"]["Enums"]["allocation_type"] | null
          created_at: string | null
          expiry_date: string | null
          id: string
          point_type_id: string | null
          points: number
          programme_id: string | null
          remarks: string | null
          status: Database["points"]["Enums"]["point_status"] | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          allocated_at?: string | null
          allocated_by?: string | null
          allocation_type?:
            | Database["points"]["Enums"]["allocation_type"]
            | null
          created_at?: string | null
          expiry_date?: string | null
          id?: string
          point_type_id?: string | null
          points?: number
          programme_id?: string | null
          remarks?: string | null
          status?: Database["points"]["Enums"]["point_status"] | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          allocated_at?: string | null
          allocated_by?: string | null
          allocation_type?:
            | Database["points"]["Enums"]["allocation_type"]
            | null
          created_at?: string | null
          expiry_date?: string | null
          id?: string
          point_type_id?: string | null
          points?: number
          programme_id?: string | null
          remarks?: string | null
          status?: Database["points"]["Enums"]["point_status"] | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "member_points_point_type_id_fkey"
            columns: ["point_type_id"]
            isOneToOne: false
            referencedRelation: "point_types"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_point_requirements: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          membership_type_id: string | null
          minimum_points: number
          point_type_id: string | null
          points_validity_months: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          membership_type_id?: string | null
          minimum_points?: number
          point_type_id?: string | null
          points_validity_months?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          membership_type_id?: string | null
          minimum_points?: number
          point_type_id?: string | null
          points_validity_months?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "membership_point_requirements_point_type_id_fkey"
            columns: ["point_type_id"]
            isOneToOne: false
            referencedRelation: "point_types"
            referencedColumns: ["id"]
          },
        ]
      }
      point_transactions: {
        Row: {
          balance: number
          created_at: string | null
          id: string
          member_point_id: string | null
          point_type_id: string | null
          points: number
          remarks: string | null
          transaction_type: string | null
          user_id: string | null
        }
        Insert: {
          balance: number
          created_at?: string | null
          id?: string
          member_point_id?: string | null
          point_type_id?: string | null
          points: number
          remarks?: string | null
          transaction_type?: string | null
          user_id?: string | null
        }
        Update: {
          balance?: number
          created_at?: string | null
          id?: string
          member_point_id?: string | null
          point_type_id?: string | null
          points?: number
          remarks?: string | null
          transaction_type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "point_transactions_member_point_id_fkey"
            columns: ["member_point_id"]
            isOneToOne: false
            referencedRelation: "member_points"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "point_transactions_point_type_id_fkey"
            columns: ["point_type_id"]
            isOneToOne: false
            referencedRelation: "point_types"
            referencedColumns: ["id"]
          },
        ]
      }
      point_types: {
        Row: {
          code: string
          created_at: string | null
          description: string | null
          has_expiry: boolean | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
          validity_days: number | null
          validity_months: number | null
        }
        Insert: {
          code: string
          created_at?: string | null
          description?: string | null
          has_expiry?: boolean | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
          validity_days?: number | null
          validity_months?: number | null
        }
        Update: {
          code?: string
          created_at?: string | null
          description?: string | null
          has_expiry?: boolean | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
          validity_days?: number | null
          validity_months?: number | null
        }
        Relationships: []
      }
      programme_points: {
        Row: {
          created_at: string | null
          id: string
          point_type_id: string | null
          points: number
          programme_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          point_type_id?: string | null
          points?: number
          programme_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          point_type_id?: string | null
          points?: number
          programme_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "programme_points_point_type_id_fkey"
            columns: ["point_type_id"]
            isOneToOne: false
            referencedRelation: "point_types"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      member_points_balance: {
        Row: {
          active_points: number | null
          earliest_expiry: string | null
          has_expiry: boolean | null
          last_submission_date: string | null
          latest_expiry: string | null
          pending_points: number | null
          point_type_code: string | null
          point_type_description: string | null
          point_type_id: string | null
          point_type_name: string | null
          rejected_points: number | null
          total_earned_points: number | null
          total_submissions: number | null
          user_created_at: string | null
          user_email: string | null
          user_full_name: string | null
          user_id: string | null
          user_phone: string | null
          user_status: boolean | null
          user_type: Database["public"]["Enums"]["user_types"] | null
          validity_days: number | null
          validity_months: number | null
        }
        Relationships: [
          {
            foreignKeyName: "member_points_point_type_id_fkey"
            columns: ["point_type_id"]
            isOneToOne: false
            referencedRelation: "point_types"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      get_user_points_balance: {
        Args: { p_user_id: string }
        Returns: {
          point_type_id: string
          point_type_name: string
          point_type_code: string
          point_type_description: string
          active_points: number
          pending_points: number
          total_earned_points: number
          rejected_points: number
          has_expiry: boolean
          validity_months: number
          validity_days: number
          earliest_expiry: string
          latest_expiry: string
          total_submissions: number
          last_submission_date: string
        }[]
      }
      submit_points: {
        Args: {
          p_user_id: string
          p_submissions: Json
          p_allocated_by?: string
          p_remarks?: string
        }
        Returns: {
          submission_id: string
          point_type_id: string
          points: number
          status: Database["points"]["Enums"]["point_status"]
          message: string
        }[]
      }
    }
    Enums: {
      allocation_type: "MANUAL" | "AUTOMATIC"
      point_status: "PENDING" | "APPROVED" | "REJECTED"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  procurement: {
    Tables: {
      approval_history: {
        Row: {
          action: string
          approval_date: string
          approval_level: number
          approver_id: string
          comments: string | null
          created_at: string
          entity_id: string
          entity_type: string
          id: string
        }
        Insert: {
          action: string
          approval_date?: string
          approval_level?: number
          approver_id: string
          comments?: string | null
          created_at?: string
          entity_id: string
          entity_type: string
          id?: string
        }
        Update: {
          action?: string
          approval_date?: string
          approval_level?: number
          approver_id?: string
          comments?: string | null
          created_at?: string
          entity_id?: string
          entity_type?: string
          id?: string
        }
        Relationships: []
      }
      goods_receipt_items: {
        Row: {
          created_at: string | null
          goods_receipt_id: string
          id: string
          po_item_id: string
          quantity_accepted: number
          quantity_received: number
          quantity_rejected: number | null
          rejection_reason: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          goods_receipt_id: string
          id?: string
          po_item_id: string
          quantity_accepted: number
          quantity_received: number
          quantity_rejected?: number | null
          rejection_reason?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          goods_receipt_id?: string
          id?: string
          po_item_id?: string
          quantity_accepted?: number
          quantity_received?: number
          quantity_rejected?: number | null
          rejection_reason?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "goods_receipt_items_goods_receipt_id_fkey"
            columns: ["goods_receipt_id"]
            isOneToOne: false
            referencedRelation: "goods_receipts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "goods_receipt_items_po_item_id_fkey"
            columns: ["po_item_id"]
            isOneToOne: false
            referencedRelation: "purchase_order_items"
            referencedColumns: ["id"]
          },
        ]
      }
      goods_receipts: {
        Row: {
          archived_at: string | null
          created_at: string | null
          created_by: string | null
          gr_number: string
          id: string
          notes: string | null
          purchase_order_id: string
          receipt_date: string
          received_by: string | null
          status:
            | Database["procurement"]["Enums"]["goods_receipt_status"]
            | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          archived_at?: string | null
          created_at?: string | null
          created_by?: string | null
          gr_number: string
          id?: string
          notes?: string | null
          purchase_order_id: string
          receipt_date?: string
          received_by?: string | null
          status?:
            | Database["procurement"]["Enums"]["goods_receipt_status"]
            | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          archived_at?: string | null
          created_at?: string | null
          created_by?: string | null
          gr_number?: string
          id?: string
          notes?: string | null
          purchase_order_id?: string
          receipt_date?: string
          received_by?: string | null
          status?:
            | Database["procurement"]["Enums"]["goods_receipt_status"]
            | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "goods_receipts_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_allocations: {
        Row: {
          allocated_amount: number
          allocation_date: string | null
          id: string
          purchase_invoice_id: string
          supplier_payment_id: string
        }
        Insert: {
          allocated_amount: number
          allocation_date?: string | null
          id?: string
          purchase_invoice_id: string
          supplier_payment_id: string
        }
        Update: {
          allocated_amount?: number
          allocation_date?: string | null
          id?: string
          purchase_invoice_id?: string
          supplier_payment_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_allocations_purchase_invoice_id_fkey"
            columns: ["purchase_invoice_id"]
            isOneToOne: false
            referencedRelation: "purchase_invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payment_allocations_supplier_payment_id_fkey"
            columns: ["supplier_payment_id"]
            isOneToOne: false
            referencedRelation: "supplier_payments"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_invoice_items: {
        Row: {
          budget_item_id: string | null
          created_at: string | null
          description: string
          gl_account_code: string | null
          id: string
          line_number: number
          line_total: number
          purchase_invoice_id: string
          purchase_order_item_id: string | null
          quantity: number
          tax_amount: number | null
          tax_rate: number | null
          unit_price: number
        }
        Insert: {
          budget_item_id?: string | null
          created_at?: string | null
          description: string
          gl_account_code?: string | null
          id?: string
          line_number: number
          line_total: number
          purchase_invoice_id: string
          purchase_order_item_id?: string | null
          quantity: number
          tax_amount?: number | null
          tax_rate?: number | null
          unit_price: number
        }
        Update: {
          budget_item_id?: string | null
          created_at?: string | null
          description?: string
          gl_account_code?: string | null
          id?: string
          line_number?: number
          line_total?: number
          purchase_invoice_id?: string
          purchase_order_item_id?: string | null
          quantity?: number
          tax_amount?: number | null
          tax_rate?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "purchase_invoice_items_purchase_invoice_id_fkey"
            columns: ["purchase_invoice_id"]
            isOneToOne: false
            referencedRelation: "purchase_invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_invoice_items_purchase_order_item_id_fkey"
            columns: ["purchase_order_item_id"]
            isOneToOne: false
            referencedRelation: "purchase_order_items"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_invoices: {
        Row: {
          amount_due: number | null
          amount_paid: number | null
          approval_request_id: string | null
          approved_at: string | null
          approved_by: string | null
          archived_at: string | null
          created_at: string | null
          created_by: string
          currency_code: string | null
          due_date: string
          entity_id: string
          exchange_rate: number | null
          goods_receipt_id: string | null
          id: string
          invoice_date: string
          invoice_file_path: string | null
          invoice_number: string
          our_reference: string | null
          payment_method: string | null
          payment_terms: number | null
          purchase_order_id: string | null
          received_date: string | null
          status:
            | Database["procurement"]["Enums"]["purchase_invoice_status_enum"]
            | null
          subtotal: number
          supplier_id: string
          tax_amount: number
          total_amount: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          amount_due?: number | null
          amount_paid?: number | null
          approval_request_id?: string | null
          approved_at?: string | null
          approved_by?: string | null
          archived_at?: string | null
          created_at?: string | null
          created_by: string
          currency_code?: string | null
          due_date: string
          entity_id: string
          exchange_rate?: number | null
          goods_receipt_id?: string | null
          id?: string
          invoice_date: string
          invoice_file_path?: string | null
          invoice_number: string
          our_reference?: string | null
          payment_method?: string | null
          payment_terms?: number | null
          purchase_order_id?: string | null
          received_date?: string | null
          status?:
            | Database["procurement"]["Enums"]["purchase_invoice_status_enum"]
            | null
          subtotal: number
          supplier_id: string
          tax_amount?: number
          total_amount: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          amount_due?: number | null
          amount_paid?: number | null
          approval_request_id?: string | null
          approved_at?: string | null
          approved_by?: string | null
          archived_at?: string | null
          created_at?: string | null
          created_by?: string
          currency_code?: string | null
          due_date?: string
          entity_id?: string
          exchange_rate?: number | null
          goods_receipt_id?: string | null
          id?: string
          invoice_date?: string
          invoice_file_path?: string | null
          invoice_number?: string
          our_reference?: string | null
          payment_method?: string | null
          payment_terms?: number | null
          purchase_order_id?: string | null
          received_date?: string | null
          status?:
            | Database["procurement"]["Enums"]["purchase_invoice_status_enum"]
            | null
          subtotal?: number
          supplier_id?: string
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_invoices_goods_receipt_id_fkey"
            columns: ["goods_receipt_id"]
            isOneToOne: false
            referencedRelation: "goods_receipts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_invoices_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_invoices_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_order_items: {
        Row: {
          budget_item_id: string | null
          created_at: string | null
          description: string
          discount_amount: number | null
          discount_percentage: number | null
          gl_account_code: string | null
          id: string
          item_code: string | null
          line_number: number
          line_total: number
          purchase_order_id: string
          quantity: number
          quantity_invoiced: number | null
          quantity_received: number | null
          tax_amount: number | null
          tax_code: string | null
          tax_rate: number | null
          unit_of_measure: string | null
          unit_price: number
          updated_at: string | null
        }
        Insert: {
          budget_item_id?: string | null
          created_at?: string | null
          description: string
          discount_amount?: number | null
          discount_percentage?: number | null
          gl_account_code?: string | null
          id?: string
          item_code?: string | null
          line_number: number
          line_total: number
          purchase_order_id: string
          quantity: number
          quantity_invoiced?: number | null
          quantity_received?: number | null
          tax_amount?: number | null
          tax_code?: string | null
          tax_rate?: number | null
          unit_of_measure?: string | null
          unit_price: number
          updated_at?: string | null
        }
        Update: {
          budget_item_id?: string | null
          created_at?: string | null
          description?: string
          discount_amount?: number | null
          discount_percentage?: number | null
          gl_account_code?: string | null
          id?: string
          item_code?: string | null
          line_number?: number
          line_total?: number
          purchase_order_id?: string
          quantity?: number
          quantity_invoiced?: number | null
          quantity_received?: number | null
          tax_amount?: number | null
          tax_code?: string | null
          tax_rate?: number | null
          unit_of_measure?: string | null
          unit_price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_items_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_orders: {
        Row: {
          approval_request_id: string | null
          approved_at: string | null
          approved_by: string | null
          archived_at: string | null
          budget_revision_id: string | null
          created_at: string | null
          created_by: string
          currency_code: string | null
          delivery_address: Json | null
          delivery_instructions: string | null
          department_code: string | null
          description: string | null
          entity_id: string
          exchange_rate: number | null
          expected_delivery_date: string | null
          id: string
          po_date: string
          po_number: string
          project_reference: string | null
          status: Database["procurement"]["Enums"]["po_status_enum"] | null
          subtotal: number
          supplier_id: string
          tax_amount: number
          title: string
          total_amount: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          approval_request_id?: string | null
          approved_at?: string | null
          approved_by?: string | null
          archived_at?: string | null
          budget_revision_id?: string | null
          created_at?: string | null
          created_by: string
          currency_code?: string | null
          delivery_address?: Json | null
          delivery_instructions?: string | null
          department_code?: string | null
          description?: string | null
          entity_id: string
          exchange_rate?: number | null
          expected_delivery_date?: string | null
          id?: string
          po_date?: string
          po_number: string
          project_reference?: string | null
          status?: Database["procurement"]["Enums"]["po_status_enum"] | null
          subtotal?: number
          supplier_id: string
          tax_amount?: number
          title: string
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          approval_request_id?: string | null
          approved_at?: string | null
          approved_by?: string | null
          archived_at?: string | null
          budget_revision_id?: string | null
          created_at?: string | null
          created_by?: string
          currency_code?: string | null
          delivery_address?: Json | null
          delivery_instructions?: string | null
          department_code?: string | null
          description?: string | null
          entity_id?: string
          exchange_rate?: number | null
          expected_delivery_date?: string | null
          id?: string
          po_date?: string
          po_number?: string
          project_reference?: string | null
          status?: Database["procurement"]["Enums"]["po_status_enum"] | null
          subtotal?: number
          supplier_id?: string
          tax_amount?: number
          title?: string
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_orders_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      supplier_categories: {
        Row: {
          category_name: string
          description: string | null
          id: string
          is_active: boolean | null
        }
        Insert: {
          category_name: string
          description?: string | null
          id?: string
          is_active?: boolean | null
        }
        Update: {
          category_name?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
        }
        Relationships: []
      }
      supplier_payments: {
        Row: {
          approval_request_id: string | null
          approved_at: string | null
          approved_by: string | null
          archived_at: string | null
          bank_details: Json | null
          created_at: string | null
          created_by: string
          currency_code: string | null
          entity_id: string
          id: string
          payment_date: string
          payment_method: string
          payment_number: string
          reference_number: string | null
          status: Database["procurement"]["Enums"]["payment_status_enum"] | null
          supplier_id: string
          total_amount: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          approval_request_id?: string | null
          approved_at?: string | null
          approved_by?: string | null
          archived_at?: string | null
          bank_details?: Json | null
          created_at?: string | null
          created_by: string
          currency_code?: string | null
          entity_id: string
          id?: string
          payment_date?: string
          payment_method: string
          payment_number: string
          reference_number?: string | null
          status?:
            | Database["procurement"]["Enums"]["payment_status_enum"]
            | null
          supplier_id: string
          total_amount: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          approval_request_id?: string | null
          approved_at?: string | null
          approved_by?: string | null
          archived_at?: string | null
          bank_details?: Json | null
          created_at?: string | null
          created_by?: string
          currency_code?: string | null
          entity_id?: string
          id?: string
          payment_date?: string
          payment_method?: string
          payment_number?: string
          reference_number?: string | null
          status?:
            | Database["procurement"]["Enums"]["payment_status_enum"]
            | null
          supplier_id?: string
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "supplier_payments_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      suppliers: {
        Row: {
          archived_at: string | null
          bank_details: Json | null
          billing_address: Json | null
          category: string | null
          contact_persons: Json | null
          created_at: string | null
          created_by: string | null
          credit_limit: number | null
          currency_code: string | null
          display_name: string | null
          email: string | null
          id: string
          is_gst_registered: boolean | null
          name: string
          payment_terms: number | null
          phone: string | null
          registration_number: string | null
          shipping_address: Json | null
          status:
            | Database["procurement"]["Enums"]["supplier_status_enum"]
            | null
          status_change_reason: string | null
          supplier_code: string
          supplier_type:
            | Database["procurement"]["Enums"]["supplier_type_enum"]
            | null
          tax_registration_number: string | null
          updated_at: string | null
          updated_by: string | null
          website: string | null
        }
        Insert: {
          archived_at?: string | null
          bank_details?: Json | null
          billing_address?: Json | null
          category?: string | null
          contact_persons?: Json | null
          created_at?: string | null
          created_by?: string | null
          credit_limit?: number | null
          currency_code?: string | null
          display_name?: string | null
          email?: string | null
          id?: string
          is_gst_registered?: boolean | null
          name: string
          payment_terms?: number | null
          phone?: string | null
          registration_number?: string | null
          shipping_address?: Json | null
          status?:
            | Database["procurement"]["Enums"]["supplier_status_enum"]
            | null
          status_change_reason?: string | null
          supplier_code: string
          supplier_type?:
            | Database["procurement"]["Enums"]["supplier_type_enum"]
            | null
          tax_registration_number?: string | null
          updated_at?: string | null
          updated_by?: string | null
          website?: string | null
        }
        Update: {
          archived_at?: string | null
          bank_details?: Json | null
          billing_address?: Json | null
          category?: string | null
          contact_persons?: Json | null
          created_at?: string | null
          created_by?: string | null
          credit_limit?: number | null
          currency_code?: string | null
          display_name?: string | null
          email?: string | null
          id?: string
          is_gst_registered?: boolean | null
          name?: string
          payment_terms?: number | null
          phone?: string | null
          registration_number?: string | null
          shipping_address?: Json | null
          status?:
            | Database["procurement"]["Enums"]["supplier_status_enum"]
            | null
          status_change_reason?: string | null
          supplier_code?: string
          supplier_type?:
            | Database["procurement"]["Enums"]["supplier_type_enum"]
            | null
          tax_registration_number?: string | null
          updated_at?: string | null
          updated_by?: string | null
          website?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      approve_purchase_invoice: {
        Args: { p_invoice_id: string; p_comments?: string }
        Returns: Json
      }
      generate_gr_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_pi_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_po_number: {
        Args: { p_entity_id: string }
        Returns: string
      }
      generate_sp_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_supplier_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_purchase_invoice_approval_history: {
        Args: { p_invoice_id: string }
        Returns: {
          id: string
          action: string
          approver_id: string
          approver_name: string
          approval_date: string
          comments: string
          approval_level: number
        }[]
      }
      perform_three_way_match: {
        Args: { invoice_id: string }
        Returns: Json
      }
      prepare_pi_approval_data: {
        Args: { p_invoice_id: string }
        Returns: Json
      }
      prepare_po_approval_data: {
        Args: { p_po_id: string }
        Returns: Json
      }
      process_supplier_payment: {
        Args: {
          p_invoice_ids: string[]
          p_payment_date: string
          p_payment_method: string
          p_reference_number: string
          p_bank_details?: Json
          p_notes?: string
          p_apply_early_payment_discount?: boolean
        }
        Returns: Json
      }
      reject_purchase_invoice: {
        Args: { p_invoice_id: string; p_reason: string }
        Returns: Json
      }
      submit_purchase_invoice_for_approval: {
        Args: { p_invoice_id: string; p_comments?: string }
        Returns: Json
      }
      validate_po_data: {
        Args: { po_data: Json }
        Returns: boolean
      }
    }
    Enums: {
      goods_receipt_status: "PENDING" | "PARTIAL" | "COMPLETE" | "CANCELLED"
      payment_status_enum: "PENDING" | "COMPLETED" | "CANCELLED" | "FAILED"
      po_status_enum:
        | "DRAFT"
        | "SUBMITTED"
        | "PENDING_APPROVAL"
        | "APPROVED"
        | "SENT"
        | "PARTIALLY_RECEIVED"
        | "RECEIVED"
        | "CANCELLED"
        | "CLOSED"
      purchase_invoice_status_enum:
        | "SUBMITTED"
        | "PENDING_APPROVAL"
        | "RECEIVED"
        | "APPROVED"
        | "PAID"
        | "DISPUTED"
        | "CANCELLED"
        | "RETURNED"
      supplier_status_enum: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "BLACKLISTED"
      supplier_type_enum:
        | "VENDOR"
        | "CONTRACTOR"
        | "SERVICE_PROVIDER"
        | "CONSULTANT"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  programme: {
    Tables: {
      addresses: {
        Row: {
          address_line_1: string | null
          address_line_2: string | null
          city: string | null
          country: string | null
          created_at: string | null
          created_by: string | null
          id: string
          postal_code: string | null
          state: string | null
          updated_at: string | null
        }
        Insert: {
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          postal_code?: string | null
          state?: string | null
          updated_at?: string | null
        }
        Update: {
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          postal_code?: string | null
          state?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      attendance_configuration: {
        Row: {
          allow_self_checkin: boolean | null
          certificate_minimum_attendance: number | null
          certificate_minimum_sessions: number | null
          checkin_ends_minutes_after: number | null
          checkin_starts_minutes_before: number | null
          created_at: string | null
          early_departure_threshold_minutes: number | null
          id: string
          late_arrival_threshold_minutes: number | null
          minimum_attendance_percentage: number | null
          programme_id: string
          qr_code_enabled: boolean | null
          qr_code_refresh_interval_minutes: number | null
          require_checkout: boolean | null
          updated_at: string | null
        }
        Insert: {
          allow_self_checkin?: boolean | null
          certificate_minimum_attendance?: number | null
          certificate_minimum_sessions?: number | null
          checkin_ends_minutes_after?: number | null
          checkin_starts_minutes_before?: number | null
          created_at?: string | null
          early_departure_threshold_minutes?: number | null
          id?: string
          late_arrival_threshold_minutes?: number | null
          minimum_attendance_percentage?: number | null
          programme_id: string
          qr_code_enabled?: boolean | null
          qr_code_refresh_interval_minutes?: number | null
          require_checkout?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allow_self_checkin?: boolean | null
          certificate_minimum_attendance?: number | null
          certificate_minimum_sessions?: number | null
          checkin_ends_minutes_after?: number | null
          checkin_starts_minutes_before?: number | null
          created_at?: string | null
          early_departure_threshold_minutes?: number | null
          id?: string
          late_arrival_threshold_minutes?: number | null
          minimum_attendance_percentage?: number | null
          programme_id?: string
          qr_code_enabled?: boolean | null
          qr_code_refresh_interval_minutes?: number | null
          require_checkout?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_configuration_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: true
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_configuration_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: true
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_records: {
        Row: {
          attendance_status: string
          check_in_device_info: Json | null
          check_in_location: string | null
          check_in_method: string | null
          check_in_time: string | null
          check_out_time: string | null
          checked_in_by: string | null
          checked_out_by: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          notes: string | null
          participant_id: string
          programme_id: string
          qr_code_id: string | null
          schedule_id: string | null
          updated_at: string | null
        }
        Insert: {
          attendance_status?: string
          check_in_device_info?: Json | null
          check_in_location?: string | null
          check_in_method?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          checked_in_by?: string | null
          checked_out_by?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          participant_id: string
          programme_id: string
          qr_code_id?: string | null
          schedule_id?: string | null
          updated_at?: string | null
        }
        Update: {
          attendance_status?: string
          check_in_device_info?: Json | null
          check_in_location?: string | null
          check_in_method?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          checked_in_by?: string | null
          checked_out_by?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          notes?: string | null
          participant_id?: string
          programme_id?: string
          qr_code_id?: string | null
          schedule_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_records_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "attendance_report"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "attendance_records_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "participants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_records_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "programme_participants"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "attendance_records_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_records_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_records_qr_code_id_fkey"
            columns: ["qr_code_id"]
            isOneToOne: false
            referencedRelation: "qr_codes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_records_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "schedules"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_summary: {
        Row: {
          attendance_percentage: number | null
          attended_sessions: number | null
          certificate_eligible: boolean | null
          certificate_issued: boolean | null
          certificate_issued_date: string | null
          certificate_number: string | null
          completion_date: string | null
          completion_status: string | null
          created_at: string | null
          first_attendance: string | null
          id: string
          last_attendance: string | null
          participant_id: string
          programme_id: string
          registration_id: string
          total_duration_minutes: number | null
          total_sessions: number | null
          updated_at: string | null
        }
        Insert: {
          attendance_percentage?: number | null
          attended_sessions?: number | null
          certificate_eligible?: boolean | null
          certificate_issued?: boolean | null
          certificate_issued_date?: string | null
          certificate_number?: string | null
          completion_date?: string | null
          completion_status?: string | null
          created_at?: string | null
          first_attendance?: string | null
          id?: string
          last_attendance?: string | null
          participant_id: string
          programme_id: string
          registration_id: string
          total_duration_minutes?: number | null
          total_sessions?: number | null
          updated_at?: string | null
        }
        Update: {
          attendance_percentage?: number | null
          attended_sessions?: number | null
          certificate_eligible?: boolean | null
          certificate_issued?: boolean | null
          certificate_issued_date?: string | null
          certificate_number?: string | null
          completion_date?: string | null
          completion_status?: string | null
          created_at?: string | null
          first_attendance?: string | null
          id?: string
          last_attendance?: string | null
          participant_id?: string
          programme_id?: string
          registration_id?: string
          total_duration_minutes?: number | null
          total_sessions?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_summary_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "attendance_report"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "attendance_summary_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "participants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_summary_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "programme_participants"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "attendance_summary_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_summary_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_summary_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: false
            referencedRelation: "programme_participants"
            referencedColumns: ["registration_id"]
          },
          {
            foreignKeyName: "attendance_summary_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: false
            referencedRelation: "registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      config: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          key: string
          updated_at: string | null
          value: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          key: string
          updated_at?: string | null
          value: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          key?: string
          updated_at?: string | null
          value?: string
        }
        Relationships: []
      }
      contact_persons: {
        Row: {
          address: Json | null
          business_unit_code: string | null
          company_name: string | null
          company_registration_number: string | null
          contact_no: string | null
          created_at: string | null
          created_by: string | null
          email: string | null
          fax_no: string | null
          id: string
          is_company: boolean | null
          name: string
          registration_id: string | null
          updated_at: string | null
        }
        Insert: {
          address?: Json | null
          business_unit_code?: string | null
          company_name?: string | null
          company_registration_number?: string | null
          contact_no?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          fax_no?: string | null
          id?: string
          is_company?: boolean | null
          name: string
          registration_id?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: Json | null
          business_unit_code?: string | null
          company_name?: string | null
          company_registration_number?: string | null
          contact_no?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          fax_no?: string | null
          id?: string
          is_company?: boolean | null
          name?: string
          registration_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "contact_persons_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: true
            referencedRelation: "programme_participants"
            referencedColumns: ["registration_id"]
          },
          {
            foreignKeyName: "contact_persons_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: true
            referencedRelation: "registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      participants: {
        Row: {
          address_id: string | null
          business_unit_code: string | null
          certificate_filename: string | null
          certificate_no: string | null
          certificate_status:
            | Database["programme"]["Enums"]["participant_cert_status"]
            | null
          company_name: string | null
          contact_no: string | null
          created_at: string | null
          created_by: string | null
          designation: string | null
          email: string | null
          id: string
          id_last_4: string | null
          identification_no: string | null
          identification_type: string | null
          name: string
          nationality: string | null
          pricing_id: string | null
          professional_id: string | null
          professional_id_type: string | null
          registration_id: string | null
          salutation: string | null
          unit_price: number | null
          updated_at: string | null
        }
        Insert: {
          address_id?: string | null
          business_unit_code?: string | null
          certificate_filename?: string | null
          certificate_no?: string | null
          certificate_status?:
            | Database["programme"]["Enums"]["participant_cert_status"]
            | null
          company_name?: string | null
          contact_no?: string | null
          created_at?: string | null
          created_by?: string | null
          designation?: string | null
          email?: string | null
          id?: string
          id_last_4?: string | null
          identification_no?: string | null
          identification_type?: string | null
          name: string
          nationality?: string | null
          pricing_id?: string | null
          professional_id?: string | null
          professional_id_type?: string | null
          registration_id?: string | null
          salutation?: string | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Update: {
          address_id?: string | null
          business_unit_code?: string | null
          certificate_filename?: string | null
          certificate_no?: string | null
          certificate_status?:
            | Database["programme"]["Enums"]["participant_cert_status"]
            | null
          company_name?: string | null
          contact_no?: string | null
          created_at?: string | null
          created_by?: string | null
          designation?: string | null
          email?: string | null
          id?: string
          id_last_4?: string | null
          identification_no?: string | null
          identification_type?: string | null
          name?: string
          nationality?: string | null
          pricing_id?: string | null
          professional_id?: string | null
          professional_id_type?: string | null
          registration_id?: string | null
          salutation?: string | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "participants_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "participants_pricing_id_fkey"
            columns: ["pricing_id"]
            isOneToOne: false
            referencedRelation: "pricing"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "participants_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: false
            referencedRelation: "programme_participants"
            referencedColumns: ["registration_id"]
          },
          {
            foreignKeyName: "participants_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: false
            referencedRelation: "registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      pricing: {
        Row: {
          amount: number | null
          budget_id: string | null
          created_at: string | null
          currency: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          programme_id: string | null
          requires_membership: boolean | null
          updated_at: string | null
        }
        Insert: {
          amount?: number | null
          budget_id?: string | null
          created_at?: string | null
          currency?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          programme_id?: string | null
          requires_membership?: boolean | null
          updated_at?: string | null
        }
        Update: {
          amount?: number | null
          budget_id?: string | null
          created_at?: string | null
          currency?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          programme_id?: string | null
          requires_membership?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pricing_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pricing_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pricing_memberships: {
        Row: {
          created_at: string | null
          membership_type_id: string | null
          pricing_id: string | null
        }
        Insert: {
          created_at?: string | null
          membership_type_id?: string | null
          pricing_id?: string | null
        }
        Update: {
          created_at?: string | null
          membership_type_id?: string | null
          pricing_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pricing_memberships_pricing_id_fkey"
            columns: ["pricing_id"]
            isOneToOne: false
            referencedRelation: "pricing"
            referencedColumns: ["id"]
          },
        ]
      }
      programmes: {
        Row: {
          approved_budget_revision_id: string | null
          approved_by: string | null
          certificate_template: string | null
          committee_id: string | null
          coordinator_id: string | null
          created_at: string | null
          created_by: string | null
          current_participant_count: number | null
          description: string | null
          feature_image_path: string | null
          format: Database["programme"]["Enums"]["programme_format"]
          has_profit_sharing: boolean | null
          id: string
          is_archived: boolean | null
          max_participants: number | null
          name: string
          organizing_dept_id: string | null
          parent_programme: string | null
          prepared_by: string | null
          profit_sharing_entity: string | null
          programme_code: string | null
          publish_end_date: string | null
          publish_start_date: string | null
          registration_end_date: string | null
          registration_start_date: string | null
          remarks: string | null
          run_code: number
          secondary_committee_id: string | null
          staff_in_charge_id: string | null
          status: Database["programme"]["Enums"]["programme_status"] | null
          type: Database["programme"]["Enums"]["programme_type"]
          updated_at: string | null
          venue_id: string | null
        }
        Insert: {
          approved_budget_revision_id?: string | null
          approved_by?: string | null
          certificate_template?: string | null
          committee_id?: string | null
          coordinator_id?: string | null
          created_at?: string | null
          created_by?: string | null
          current_participant_count?: number | null
          description?: string | null
          feature_image_path?: string | null
          format?: Database["programme"]["Enums"]["programme_format"]
          has_profit_sharing?: boolean | null
          id?: string
          is_archived?: boolean | null
          max_participants?: number | null
          name: string
          organizing_dept_id?: string | null
          parent_programme?: string | null
          prepared_by?: string | null
          profit_sharing_entity?: string | null
          programme_code?: string | null
          publish_end_date?: string | null
          publish_start_date?: string | null
          registration_end_date?: string | null
          registration_start_date?: string | null
          remarks?: string | null
          run_code?: number
          secondary_committee_id?: string | null
          staff_in_charge_id?: string | null
          status?: Database["programme"]["Enums"]["programme_status"] | null
          type: Database["programme"]["Enums"]["programme_type"]
          updated_at?: string | null
          venue_id?: string | null
        }
        Update: {
          approved_budget_revision_id?: string | null
          approved_by?: string | null
          certificate_template?: string | null
          committee_id?: string | null
          coordinator_id?: string | null
          created_at?: string | null
          created_by?: string | null
          current_participant_count?: number | null
          description?: string | null
          feature_image_path?: string | null
          format?: Database["programme"]["Enums"]["programme_format"]
          has_profit_sharing?: boolean | null
          id?: string
          is_archived?: boolean | null
          max_participants?: number | null
          name?: string
          organizing_dept_id?: string | null
          parent_programme?: string | null
          prepared_by?: string | null
          profit_sharing_entity?: string | null
          programme_code?: string | null
          publish_end_date?: string | null
          publish_start_date?: string | null
          registration_end_date?: string | null
          registration_start_date?: string | null
          remarks?: string | null
          run_code?: number
          secondary_committee_id?: string | null
          staff_in_charge_id?: string | null
          status?: Database["programme"]["Enums"]["programme_status"] | null
          type?: Database["programme"]["Enums"]["programme_type"]
          updated_at?: string | null
          venue_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "programmes_coordinator_id_fkey"
            columns: ["coordinator_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["staff_in_charge_id"]
          },
          {
            foreignKeyName: "programmes_organizing_dept_id_fkey"
            columns: ["organizing_dept_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["department_id"]
          },
          {
            foreignKeyName: "programmes_parent_programme_fkey"
            columns: ["parent_programme"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "programmes_parent_programme_fkey"
            columns: ["parent_programme"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "programmes_staff_in_charge_id_fkey"
            columns: ["staff_in_charge_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["staff_in_charge_id"]
          },
          {
            foreignKeyName: "programmes_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["venue_id"]
          },
          {
            foreignKeyName: "programmes_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      qr_codes: {
        Row: {
          action: Database["programme"]["Enums"]["qr_code_action"]
          created_at: string | null
          id: string
          programme_id: string | null
          secret: string
          updated_at: string | null
        }
        Insert: {
          action: Database["programme"]["Enums"]["qr_code_action"]
          created_at?: string | null
          id?: string
          programme_id?: string | null
          secret: string
          updated_at?: string | null
        }
        Update: {
          action?: Database["programme"]["Enums"]["qr_code_action"]
          created_at?: string | null
          id?: string
          programme_id?: string | null
          secret?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "qr_codes_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "qr_codes_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      registrations: {
        Row: {
          created_at: string | null
          created_by: string | null
          currency: string | null
          id: string
          payment_date: string | null
          payment_status: string | null
          programme_id: string | null
          registration_type: Database["programme"]["Enums"]["registration_type"]
          remarks: string | null
          status: Database["programme"]["Enums"]["registration_status"] | null
          total_amount: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          currency?: string | null
          id?: string
          payment_date?: string | null
          payment_status?: string | null
          programme_id?: string | null
          registration_type: Database["programme"]["Enums"]["registration_type"]
          remarks?: string | null
          status?: Database["programme"]["Enums"]["registration_status"] | null
          total_amount?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          currency?: string | null
          id?: string
          payment_date?: string | null
          payment_status?: string | null
          programme_id?: string | null
          registration_type?: Database["programme"]["Enums"]["registration_type"]
          remarks?: string | null
          status?: Database["programme"]["Enums"]["registration_status"] | null
          total_amount?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "registrations_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "registrations_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      schedules: {
        Row: {
          date: string
          day_number: number
          end_time: string
          id: string
          programme_id: string | null
          start_time: string
        }
        Insert: {
          date: string
          day_number: number
          end_time: string
          id?: string
          programme_id?: string | null
          start_time: string
        }
        Update: {
          date?: string
          day_number?: number
          end_time?: string
          id?: string
          programme_id?: string | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "schedules_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedules_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      session_attendance: {
        Row: {
          arrival_time: string | null
          attendance_record_id: string
          created_at: string | null
          departure_time: string | null
          duration_minutes: number | null
          engagement_notes: string | null
          id: string
          is_present: boolean | null
          participant_id: string
          participation_score: number | null
          programme_id: string
          schedule_id: string
          session_date: string
          session_name: string | null
          session_type: string | null
          updated_at: string | null
        }
        Insert: {
          arrival_time?: string | null
          attendance_record_id: string
          created_at?: string | null
          departure_time?: string | null
          duration_minutes?: number | null
          engagement_notes?: string | null
          id?: string
          is_present?: boolean | null
          participant_id: string
          participation_score?: number | null
          programme_id: string
          schedule_id: string
          session_date: string
          session_name?: string | null
          session_type?: string | null
          updated_at?: string | null
        }
        Update: {
          arrival_time?: string | null
          attendance_record_id?: string
          created_at?: string | null
          departure_time?: string | null
          duration_minutes?: number | null
          engagement_notes?: string | null
          id?: string
          is_present?: boolean | null
          participant_id?: string
          participation_score?: number | null
          programme_id?: string
          schedule_id?: string
          session_date?: string
          session_name?: string | null
          session_type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "session_attendance_attendance_record_id_fkey"
            columns: ["attendance_record_id"]
            isOneToOne: false
            referencedRelation: "attendance_records"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_attendance_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "attendance_report"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "session_attendance_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "participants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_attendance_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "programme_participants"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "session_attendance_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_attendance_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_attendance_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "schedules"
            referencedColumns: ["id"]
          },
        ]
      }
      table_registrations: {
        Row: {
          business_unit_code: string | null
          created_at: string | null
          created_by: string | null
          id: string
          registration_id: string | null
          seats_allocated: number | null
          seats_confirmed: number | null
          table_number: string | null
          updated_at: string | null
        }
        Insert: {
          business_unit_code?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          registration_id?: string | null
          seats_allocated?: number | null
          seats_confirmed?: number | null
          table_number?: string | null
          updated_at?: string | null
        }
        Update: {
          business_unit_code?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          registration_id?: string | null
          seats_allocated?: number | null
          seats_confirmed?: number | null
          table_number?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "table_registrations_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: true
            referencedRelation: "programme_participants"
            referencedColumns: ["registration_id"]
          },
          {
            foreignKeyName: "table_registrations_registration_id_fkey"
            columns: ["registration_id"]
            isOneToOne: true
            referencedRelation: "registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      venues: {
        Row: {
          address_line_1: string | null
          address_line_2: string | null
          city: string | null
          code: string | null
          contact_email: string | null
          contact_person: string | null
          contact_phone: string | null
          country: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          latitude: number | null
          longitude: number | null
          name: string
          postal_code: string | null
          updated_at: string | null
        }
        Insert: {
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          code?: string | null
          contact_email?: string | null
          contact_person?: string | null
          contact_phone?: string | null
          country?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          latitude?: number | null
          longitude?: number | null
          name: string
          postal_code?: string | null
          updated_at?: string | null
        }
        Update: {
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          code?: string | null
          contact_email?: string | null
          contact_person?: string | null
          contact_phone?: string | null
          country?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          latitude?: number | null
          longitude?: number | null
          name?: string
          postal_code?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      attendance_report: {
        Row: {
          attendance_percentage: number | null
          attended_sessions: number | null
          certificate_eligible: boolean | null
          certificate_issued: boolean | null
          certificate_number: string | null
          completion_status: string | null
          email: string | null
          participant_id: string | null
          participant_name: string | null
          programme_name: string | null
          programme_type:
            | Database["programme"]["Enums"]["programme_type"]
            | null
          registration_status:
            | Database["programme"]["Enums"]["registration_status"]
            | null
          registration_type:
            | Database["programme"]["Enums"]["registration_type"]
            | null
          total_duration_minutes: number | null
          total_sessions: number | null
        }
        Relationships: []
      }
      programme_participants: {
        Row: {
          business_unit_code: string | null
          contact_no: string | null
          designation: string | null
          email: string | null
          identification_no: string | null
          identification_type: string | null
          name: string | null
          nationality: string | null
          participant_id: string | null
          payment_status: string | null
          pricing_id: string | null
          professional_id: string | null
          professional_id_type: string | null
          programme_id: string | null
          registration_id: string | null
          registration_status:
            | Database["programme"]["Enums"]["registration_status"]
            | null
          registration_type:
            | Database["programme"]["Enums"]["registration_type"]
            | null
          salutation: string | null
          table_number: string | null
          unit_price: number | null
        }
        Relationships: [
          {
            foreignKeyName: "participants_pricing_id_fkey"
            columns: ["pricing_id"]
            isOneToOne: false
            referencedRelation: "pricing"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "registrations_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "registrations_programme_id_fkey"
            columns: ["programme_id"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      programmes_with_details: {
        Row: {
          address_line_1: string | null
          address_line_2: string | null
          city: string | null
          country: string | null
          created_at: string | null
          department_id: string | null
          department_name: string | null
          format: Database["programme"]["Enums"]["programme_format"] | null
          id: string | null
          is_archived: boolean | null
          name: string | null
          parent_programme: string | null
          postal_code: string | null
          programme_code: string | null
          publish_end_date: string | null
          publish_start_date: string | null
          registration_end_date: string | null
          registration_start_date: string | null
          run_code: number | null
          staff_in_charge_id: string | null
          staff_in_charge_name: string | null
          status: Database["programme"]["Enums"]["programme_status"] | null
          type: Database["programme"]["Enums"]["programme_type"] | null
          venue_id: string | null
          venue_name: string | null
        }
        Relationships: [
          {
            foreignKeyName: "programmes_parent_programme_fkey"
            columns: ["parent_programme"]
            isOneToOne: false
            referencedRelation: "programmes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "programmes_parent_programme_fkey"
            columns: ["parent_programme"]
            isOneToOne: false
            referencedRelation: "programmes_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      check_certificate_eligibility: {
        Args: { p_participant_id: string; p_programme_id: string }
        Returns: boolean
      }
      clone_programme_api: {
        Args: { source_programme_id: string }
        Returns: string
      }
    }
    Enums: {
      participant_cert_status: "SENT" | "NOT_SENT"
      programme_format: "IN-PERSON" | "VIRTUAL" | "HYBRID"
      programme_status:
        | "DRAFT"
        | "PUBLISHED"
        | "UNPUBLISHED"
        | "TERMINATED"
        | "COMPLETED"
      programme_type: "COURSE" | "EVENT" | "SEMINAR" | "CONFERENCE"
      qr_code_action: "CHECKIN" | "CHECKOUT"
      registration_status: "PENDING" | "CONFIRMED" | "CANCELLED"
      registration_type: "INDIVIDUAL" | "GROUP" | "TABLE"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      addresses: {
        Row: {
          city: string | null
          country_code: string | null
          id: string
          postal_code: string | null
          state: string | null
          street_line_1: string | null
          street_line_2: string | null
          street_line_3: string | null
          type: Database["public"]["Enums"]["address_types"] | null
        }
        Insert: {
          city?: string | null
          country_code?: string | null
          id?: string
          postal_code?: string | null
          state?: string | null
          street_line_1?: string | null
          street_line_2?: string | null
          street_line_3?: string | null
          type?: Database["public"]["Enums"]["address_types"] | null
        }
        Update: {
          city?: string | null
          country_code?: string | null
          id?: string
          postal_code?: string | null
          state?: string | null
          street_line_1?: string | null
          street_line_2?: string | null
          street_line_3?: string | null
          type?: Database["public"]["Enums"]["address_types"] | null
        }
        Relationships: []
      }
      applications: {
        Row: {
          applicant_form_path: string | null
          approved_or_rejected_at: string | null
          comment: string | null
          created_at: string | null
          date_of_previous_application: string | null
          has_awards: boolean | null
          has_declaration: boolean | null
          has_employment: boolean | null
          has_previous_application: boolean | null
          has_working_experience: boolean | null
          id: string
          membership_grade_applied: string | null
          membership_id: string | null
          membership_type: string
          pe_form_path: string | null
          profile_id: string | null
          reference_no: string | null
          sponsorship_form_path: string | null
          status: Database["public"]["Enums"]["application_statuses"] | null
          submitted_applicant_form_path: string | null
          submitted_at: string | null
          submitted_pe_form_path: string | null
          submitted_sponsorship_form_path: string | null
          type: Database["public"]["Enums"]["application_types"] | null
          user_id: string
        }
        Insert: {
          applicant_form_path?: string | null
          approved_or_rejected_at?: string | null
          comment?: string | null
          created_at?: string | null
          date_of_previous_application?: string | null
          has_awards?: boolean | null
          has_declaration?: boolean | null
          has_employment?: boolean | null
          has_previous_application?: boolean | null
          has_working_experience?: boolean | null
          id?: string
          membership_grade_applied?: string | null
          membership_id?: string | null
          membership_type: string
          pe_form_path?: string | null
          profile_id?: string | null
          reference_no?: string | null
          sponsorship_form_path?: string | null
          status?: Database["public"]["Enums"]["application_statuses"] | null
          submitted_applicant_form_path?: string | null
          submitted_at?: string | null
          submitted_pe_form_path?: string | null
          submitted_sponsorship_form_path?: string | null
          type?: Database["public"]["Enums"]["application_types"] | null
          user_id: string
        }
        Update: {
          applicant_form_path?: string | null
          approved_or_rejected_at?: string | null
          comment?: string | null
          created_at?: string | null
          date_of_previous_application?: string | null
          has_awards?: boolean | null
          has_declaration?: boolean | null
          has_employment?: boolean | null
          has_previous_application?: boolean | null
          has_working_experience?: boolean | null
          id?: string
          membership_grade_applied?: string | null
          membership_id?: string | null
          membership_type?: string
          pe_form_path?: string | null
          profile_id?: string | null
          reference_no?: string | null
          sponsorship_form_path?: string | null
          status?: Database["public"]["Enums"]["application_statuses"] | null
          submitted_applicant_form_path?: string | null
          submitted_at?: string | null
          submitted_pe_form_path?: string | null
          submitted_sponsorship_form_path?: string | null
          type?: Database["public"]["Enums"]["application_types"] | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "application_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "membership_profiles"
            referencedColumns: ["membership_id"]
          },
          {
            foreignKeyName: "application_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "application_membership_type_fkey"
            columns: ["membership_type"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "application_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      awards: {
        Row: {
          abbreviation: string | null
          date_of_election: string | null
          detail: string | null
          expiry_date: string | null
          id: string
          nominee_id: string | null
          profile_id: string | null
          record_number: string | null
          record_type: Database["public"]["Enums"]["record_types"] | null
        }
        Insert: {
          abbreviation?: string | null
          date_of_election?: string | null
          detail?: string | null
          expiry_date?: string | null
          id?: string
          nominee_id?: string | null
          profile_id?: string | null
          record_number?: string | null
          record_type?: Database["public"]["Enums"]["record_types"] | null
        }
        Update: {
          abbreviation?: string | null
          date_of_election?: string | null
          detail?: string | null
          expiry_date?: string | null
          id?: string
          nominee_id?: string | null
          profile_id?: string | null
          record_number?: string | null
          record_type?: Database["public"]["Enums"]["record_types"] | null
        }
        Relationships: [
          {
            foreignKeyName: "awards_nominee_id_fkey"
            columns: ["nominee_id"]
            isOneToOne: false
            referencedRelation: "nominees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "awards_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      certificate: {
        Row: {
          certificate_no: string | null
          created_at: string | null
          file_name: string | null
          id: string
          issue_date: string | null
          source_id: string
          source_type: Database["finance"]["Enums"]["certificate_source_type"]
          title: string | null
          user_id: string
        }
        Insert: {
          certificate_no?: string | null
          created_at?: string | null
          file_name?: string | null
          id?: string
          issue_date?: string | null
          source_id: string
          source_type: Database["finance"]["Enums"]["certificate_source_type"]
          title?: string | null
          user_id: string
        }
        Update: {
          certificate_no?: string | null
          created_at?: string | null
          file_name?: string | null
          id?: string
          issue_date?: string | null
          source_id?: string
          source_type?: Database["finance"]["Enums"]["certificate_source_type"]
          title?: string | null
          user_id?: string
        }
        Relationships: []
      }
      companies: {
        Row: {
          address_id: string | null
          email: string | null
          employee_no: number | null
          fax: string | null
          id: string
          is_same_address: boolean | null
          name: string | null
          phone: string | null
          phone_country_code: string | null
          preferred_address_id: string | null
          registration_date: string | null
          registration_no: string | null
          registration_place: string | null
          website: string | null
        }
        Insert: {
          address_id?: string | null
          email?: string | null
          employee_no?: number | null
          fax?: string | null
          id?: string
          is_same_address?: boolean | null
          name?: string | null
          phone?: string | null
          phone_country_code?: string | null
          preferred_address_id?: string | null
          registration_date?: string | null
          registration_no?: string | null
          registration_place?: string | null
          website?: string | null
        }
        Update: {
          address_id?: string | null
          email?: string | null
          employee_no?: number | null
          fax?: string | null
          id?: string
          is_same_address?: boolean | null
          name?: string | null
          phone?: string | null
          phone_country_code?: string | null
          preferred_address_id?: string | null
          registration_date?: string | null
          registration_no?: string | null
          registration_place?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "companies_preferred_address_id_fkey"
            columns: ["preferred_address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
        ]
      }
      criminal_records: {
        Row: {
          declaration: boolean | null
          details: string | null
          id: string
          profile_id: string | null
        }
        Insert: {
          declaration?: boolean | null
          details?: string | null
          id?: string
          profile_id?: string | null
        }
        Update: {
          declaration?: boolean | null
          details?: string | null
          id?: string
          profile_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "criminalrecord_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          id: string
          notes: string | null
          path: string | null
          profile_id: string | null
          type: string | null
          upload_date: string | null
        }
        Insert: {
          id?: string
          notes?: string | null
          path?: string | null
          profile_id?: string | null
          type?: string | null
          upload_date?: string | null
        }
        Update: {
          id?: string
          notes?: string | null
          path?: string | null
          profile_id?: string | null
          type?: string | null
          upload_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      educations: {
        Row: {
          date_of_graduation: string | null
          icourse_id: string | null
          id: string
          institute_id: string | null
          nominee_id: string | null
          other_course: string | null
          other_institute: string | null
          period_from: string | null
          period_to: string | null
          profile_id: string | null
        }
        Insert: {
          date_of_graduation?: string | null
          icourse_id?: string | null
          id?: string
          institute_id?: string | null
          nominee_id?: string | null
          other_course?: string | null
          other_institute?: string | null
          period_from?: string | null
          period_to?: string | null
          profile_id?: string | null
        }
        Update: {
          date_of_graduation?: string | null
          icourse_id?: string | null
          id?: string
          institute_id?: string | null
          nominee_id?: string | null
          other_course?: string | null
          other_institute?: string | null
          period_from?: string | null
          period_to?: string | null
          profile_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "education_icourse_id_fkey"
            columns: ["icourse_id"]
            isOneToOne: false
            referencedRelation: "institute_courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "education_institute_id_fkey"
            columns: ["institute_id"]
            isOneToOne: false
            referencedRelation: "institutes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "education_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "educations_nominee_id_fkey"
            columns: ["nominee_id"]
            isOneToOne: false
            referencedRelation: "nominees"
            referencedColumns: ["id"]
          },
        ]
      }
      employments: {
        Row: {
          company_id: string | null
          designation: string | null
          id: string
          profile_id: string | null
          start_date: string | null
        }
        Insert: {
          company_id?: string | null
          designation?: string | null
          id?: string
          profile_id?: string | null
          start_date?: string | null
        }
        Update: {
          company_id?: string | null
          designation?: string | null
          id?: string
          profile_id?: string | null
          start_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "employment_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "employment_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      export_jobs: {
        Row: {
          completed_at: string | null
          created_at: string
          error: string | null
          export_type: Database["public"]["Enums"]["export_type"] | null
          filename: string | null
          filters: Json | null
          id: string
          status: Database["public"]["Enums"]["export_job_status"]
          user_id: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string
          error?: string | null
          export_type?: Database["public"]["Enums"]["export_type"] | null
          filename?: string | null
          filters?: Json | null
          id?: string
          status?: Database["public"]["Enums"]["export_job_status"]
          user_id?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string
          error?: string | null
          export_type?: Database["public"]["Enums"]["export_type"] | null
          filename?: string | null
          filters?: Json | null
          id?: string
          status?: Database["public"]["Enums"]["export_job_status"]
          user_id?: string | null
        }
        Relationships: []
      }
      form_sections: {
        Row: {
          created_at: string | null
          id: string
          name: string
          order_number: number
          type: Database["public"]["Enums"]["form_section_type"]
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          order_number: number
          type: Database["public"]["Enums"]["form_section_type"]
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          order_number?: number
          type?: Database["public"]["Enums"]["form_section_type"]
        }
        Relationships: []
      }
      institute_courses: {
        Row: {
          category: string | null
          id: string
          institute_id: string | null
          name: string | null
        }
        Insert: {
          category?: string | null
          id?: string
          institute_id?: string | null
          name?: string | null
        }
        Update: {
          category?: string | null
          id?: string
          institute_id?: string | null
          name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "institutecourse_institute_id_fkey"
            columns: ["institute_id"]
            isOneToOne: false
            referencedRelation: "institutes"
            referencedColumns: ["id"]
          },
        ]
      }
      institutes: {
        Row: {
          abbr: string | null
          country: string | null
          id: string
          name: string | null
          type: string | null
        }
        Insert: {
          abbr?: string | null
          country?: string | null
          id?: string
          name?: string | null
          type?: string | null
        }
        Update: {
          abbr?: string | null
          country?: string | null
          id?: string
          name?: string | null
          type?: string | null
        }
        Relationships: []
      }
      membership_section_mapping: {
        Row: {
          id: string
          is_required: boolean | null
          is_visible: boolean | null
          membership_type_id: string
          section_id: string
          validation_rules: Json | null
        }
        Insert: {
          id?: string
          is_required?: boolean | null
          is_visible?: boolean | null
          membership_type_id: string
          section_id: string
          validation_rules?: Json | null
        }
        Update: {
          id?: string
          is_required?: boolean | null
          is_visible?: boolean | null
          membership_type_id?: string
          section_id?: string
          validation_rules?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "membership_section_mapping_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "membership_section_mapping_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "form_sections"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_type_additional_forms: {
        Row: {
          created_at: string | null
          description: string | null
          form_name: string
          form_path: string
          id: string
          is_required: boolean | null
          membership_type_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          form_name: string
          form_path: string
          id?: string
          is_required?: boolean | null
          membership_type_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          form_name?: string
          form_path?: string
          id?: string
          is_required?: boolean | null
          membership_type_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "membership_type_additional_forms_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_type_notice: {
        Row: {
          description: string | null
          id: string
          membership_type_id: string
          title: string | null
          type: Database["public"]["Enums"]["membership_notice_types"]
        }
        Insert: {
          description?: string | null
          id?: string
          membership_type_id: string
          title?: string | null
          type: Database["public"]["Enums"]["membership_notice_types"]
        }
        Update: {
          description?: string | null
          id?: string
          membership_type_id?: string
          title?: string | null
          type?: Database["public"]["Enums"]["membership_notice_types"]
        }
        Relationships: [
          {
            foreignKeyName: "membership_type_notice_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_type_prerequisites: {
        Row: {
          description: string
          id: string
          membership_type_id: string
          title: string
        }
        Insert: {
          description: string
          id?: string
          membership_type_id: string
          title: string
        }
        Update: {
          description?: string
          id?: string
          membership_type_id?: string
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "membership_type_prerequisites_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_type_rules: {
        Row: {
          created_at: string | null
          error_message: string | null
          id: string
          membership_type_id: string
          rule_type: Database["public"]["Enums"]["membership_rule_types"]
          rule_value: Json
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          id?: string
          membership_type_id: string
          rule_type: Database["public"]["Enums"]["membership_rule_types"]
          rule_value: Json
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          id?: string
          membership_type_id?: string
          rule_type?: Database["public"]["Enums"]["membership_rule_types"]
          rule_value?: Json
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "membership_type_rules_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_types: {
        Row: {
          abbr: string | null
          admin_fee: number | null
          allow_online_registration: boolean | null
          application_fee: number | null
          application_number_prefix: string | null
          application_number_sequence: number | null
          application_number_suffix: string | null
          certificate_template: string | null
          description: string | null
          entity_id: string | null
          group: Database["public"]["Enums"]["membership_type_group"] | null
          id: string
          is_active: boolean | null
          is_one_fee: boolean | null
          membership_number_prefix: string | null
          membership_number_sequence: number | null
          membership_number_suffix: string | null
          name: string
          renewal_period_start_from: string | null
          required_additional_forms: boolean | null
          required_membership: boolean | null
          required_physical_submission: boolean | null
          required_renewal_points: boolean | null
          required_signature: boolean | null
          required_tucss_membership: boolean | null
          subscription_fee: number | null
          subscription_period_from: string | null
          subscription_period_until: string | null
        }
        Insert: {
          abbr?: string | null
          admin_fee?: number | null
          allow_online_registration?: boolean | null
          application_fee?: number | null
          application_number_prefix?: string | null
          application_number_sequence?: number | null
          application_number_suffix?: string | null
          certificate_template?: string | null
          description?: string | null
          entity_id?: string | null
          group?: Database["public"]["Enums"]["membership_type_group"] | null
          id?: string
          is_active?: boolean | null
          is_one_fee?: boolean | null
          membership_number_prefix?: string | null
          membership_number_sequence?: number | null
          membership_number_suffix?: string | null
          name: string
          renewal_period_start_from?: string | null
          required_additional_forms?: boolean | null
          required_membership?: boolean | null
          required_physical_submission?: boolean | null
          required_renewal_points?: boolean | null
          required_signature?: boolean | null
          required_tucss_membership?: boolean | null
          subscription_fee?: number | null
          subscription_period_from?: string | null
          subscription_period_until?: string | null
        }
        Update: {
          abbr?: string | null
          admin_fee?: number | null
          allow_online_registration?: boolean | null
          application_fee?: number | null
          application_number_prefix?: string | null
          application_number_sequence?: number | null
          application_number_suffix?: string | null
          certificate_template?: string | null
          description?: string | null
          entity_id?: string | null
          group?: Database["public"]["Enums"]["membership_type_group"] | null
          id?: string
          is_active?: boolean | null
          is_one_fee?: boolean | null
          membership_number_prefix?: string | null
          membership_number_sequence?: number | null
          membership_number_suffix?: string | null
          name?: string
          renewal_period_start_from?: string | null
          required_additional_forms?: boolean | null
          required_membership?: boolean | null
          required_physical_submission?: boolean | null
          required_renewal_points?: boolean | null
          required_signature?: boolean | null
          required_tucss_membership?: boolean | null
          subscription_fee?: number | null
          subscription_period_from?: string | null
          subscription_period_until?: string | null
        }
        Relationships: []
      }
      memberships: {
        Row: {
          email_sent: boolean | null
          end_date: string | null
          id: string
          member_since: string | null
          membership_number: string | null
          membership_type: string | null
          profile_id: string
          start_date: string | null
          status: Database["public"]["Enums"]["membership_status"] | null
          user_id: string | null
        }
        Insert: {
          email_sent?: boolean | null
          end_date?: string | null
          id?: string
          member_since?: string | null
          membership_number?: string | null
          membership_type?: string | null
          profile_id: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["membership_status"] | null
          user_id?: string | null
        }
        Update: {
          email_sent?: boolean | null
          end_date?: string | null
          id?: string
          member_since?: string | null
          membership_number?: string | null
          membership_type?: string | null
          profile_id?: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["membership_status"] | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "membership_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "memberships_membership_type_fkey"
            columns: ["membership_type"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
        ]
      }
      nominees: {
        Row: {
          date_of_birth: string | null
          email: string | null
          full_name: string | null
          has_awards: boolean | null
          id: string
          phone_number: string | null
          phone_number_country_code: string | null
          profile_id: string | null
          title: string | null
        }
        Insert: {
          date_of_birth?: string | null
          email?: string | null
          full_name?: string | null
          has_awards?: boolean | null
          id?: string
          phone_number?: string | null
          phone_number_country_code?: string | null
          profile_id?: string | null
          title?: string | null
        }
        Update: {
          date_of_birth?: string | null
          email?: string | null
          full_name?: string | null
          has_awards?: boolean | null
          id?: string
          phone_number?: string | null
          phone_number_country_code?: string | null
          profile_id?: string | null
          title?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nominees_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          address_id: string | null
          country: string | null
          created_at: string | null
          date_of_birth: string | null
          email: string | null
          full_name: string | null
          gender: string | null
          home_number: string | null
          home_number_country_code: string | null
          id: string
          identification_no: string | null
          identification_type: string | null
          import_member_id: string | null
          industry_id: string | null
          is_aces_member: boolean | null
          mobile_number: string | null
          mobile_number_country_code: string | null
          nationality: string | null
          old_reference_no: string | null
          pedeclaration_form: string | null
          place_of_birth: string | null
          spoken_languages: string[] | null
          title: string | null
          type: Database["public"]["Enums"]["profile_types"] | null
          updated_at: string | null
          user_id: string | null
          written_languages: string[] | null
        }
        Insert: {
          address_id?: string | null
          country?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string | null
          full_name?: string | null
          gender?: string | null
          home_number?: string | null
          home_number_country_code?: string | null
          id?: string
          identification_no?: string | null
          identification_type?: string | null
          import_member_id?: string | null
          industry_id?: string | null
          is_aces_member?: boolean | null
          mobile_number?: string | null
          mobile_number_country_code?: string | null
          nationality?: string | null
          old_reference_no?: string | null
          pedeclaration_form?: string | null
          place_of_birth?: string | null
          spoken_languages?: string[] | null
          title?: string | null
          type?: Database["public"]["Enums"]["profile_types"] | null
          updated_at?: string | null
          user_id?: string | null
          written_languages?: string[] | null
        }
        Update: {
          address_id?: string | null
          country?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string | null
          full_name?: string | null
          gender?: string | null
          home_number?: string | null
          home_number_country_code?: string | null
          id?: string
          identification_no?: string | null
          identification_type?: string | null
          import_member_id?: string | null
          industry_id?: string | null
          is_aces_member?: boolean | null
          mobile_number?: string | null
          mobile_number_country_code?: string | null
          nationality?: string | null
          old_reference_no?: string | null
          pedeclaration_form?: string | null
          place_of_birth?: string | null
          spoken_languages?: string[] | null
          title?: string | null
          type?: Database["public"]["Enums"]["profile_types"] | null
          updated_at?: string | null
          user_id?: string | null
          written_languages?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "profile_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "sys_industries"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          company_id: string | null
          cost_of_project: string | null
          details: string | null
          duration: string | null
          employment_start_date: string | null
          form_path: string | null
          id: string
          involvement_from: string | null
          involvement_to: string | null
          is_ongoing: boolean | null
          position_held: string | null
          profile_id: string | null
          reference_no: string | null
          spe_name: string | null
          spe_number: string | null
          title: string | null
          total_month: number | null
          total_year: number | null
          work_type: string | null
        }
        Insert: {
          company_id?: string | null
          cost_of_project?: string | null
          details?: string | null
          duration?: string | null
          employment_start_date?: string | null
          form_path?: string | null
          id?: string
          involvement_from?: string | null
          involvement_to?: string | null
          is_ongoing?: boolean | null
          position_held?: string | null
          profile_id?: string | null
          reference_no?: string | null
          spe_name?: string | null
          spe_number?: string | null
          title?: string | null
          total_month?: number | null
          total_year?: number | null
          work_type?: string | null
        }
        Update: {
          company_id?: string | null
          cost_of_project?: string | null
          details?: string | null
          duration?: string | null
          employment_start_date?: string | null
          form_path?: string | null
          id?: string
          involvement_from?: string | null
          involvement_to?: string | null
          is_ongoing?: boolean | null
          position_held?: string | null
          profile_id?: string | null
          reference_no?: string | null
          spe_name?: string | null
          spe_number?: string | null
          title?: string | null
          total_month?: number | null
          total_year?: number | null
          work_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "projectdetails_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "projectdetails_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      reports: {
        Row: {
          created_at: string | null
          created_by: string
          file_name: string
          file_path: string | null
          file_type: string
          id: string
          status: string
        }
        Insert: {
          created_at?: string | null
          created_by: string
          file_name: string
          file_path?: string | null
          file_type: string
          id?: string
          status: string
        }
        Update: {
          created_at?: string | null
          created_by?: string
          file_name?: string
          file_path?: string | null
          file_type?: string
          id?: string
          status?: string
        }
        Relationships: []
      }
      sponsorships: {
        Row: {
          date: string | null
          grade: string | null
          id: string
          membership_id: string | null
          name: string
          profile_id: string | null
          type: Database["public"]["Enums"]["sponsorship_types"]
        }
        Insert: {
          date?: string | null
          grade?: string | null
          id?: string
          membership_id?: string | null
          name: string
          profile_id?: string | null
          type: Database["public"]["Enums"]["sponsorship_types"]
        }
        Update: {
          date?: string | null
          grade?: string | null
          id?: string
          membership_id?: string | null
          name?: string
          profile_id?: string | null
          type?: Database["public"]["Enums"]["sponsorship_types"]
        }
        Relationships: [
          {
            foreignKeyName: "sponsorship_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      sys_configs: {
        Row: {
          category: Database["public"]["Enums"]["config_categories"]
          config_key: string
          config_value: string
          data_type: Database["public"]["Enums"]["config_data_types"]
          description: string | null
          is_required: boolean | null
          updated_at: string | null
        }
        Insert: {
          category: Database["public"]["Enums"]["config_categories"]
          config_key: string
          config_value: string
          data_type: Database["public"]["Enums"]["config_data_types"]
          description?: string | null
          is_required?: boolean | null
          updated_at?: string | null
        }
        Update: {
          category?: Database["public"]["Enums"]["config_categories"]
          config_key?: string
          config_value?: string
          data_type?: Database["public"]["Enums"]["config_data_types"]
          description?: string | null
          is_required?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      sys_industries: {
        Row: {
          group: string | null
          id: string
          name: string | null
        }
        Insert: {
          group?: string | null
          id?: string
          name?: string | null
        }
        Update: {
          group?: string | null
          id?: string
          name?: string | null
        }
        Relationships: []
      }
      user_meta: {
        Row: {
          created_at: string | null
          date_of_birth: string | null
          email: string | null
          first_login: boolean | null
          full_name: string | null
          id: string
          last_sign_in_at: string | null
          metadata: Json | null
          partial_nric: string | null
          phone_number: string | null
          phone_number_country_code: string | null
          status: boolean | null
          updated_at: string | null
          user_type: Database["public"]["Enums"]["user_types"]
        }
        Insert: {
          created_at?: string | null
          date_of_birth?: string | null
          email?: string | null
          first_login?: boolean | null
          full_name?: string | null
          id: string
          last_sign_in_at?: string | null
          metadata?: Json | null
          partial_nric?: string | null
          phone_number?: string | null
          phone_number_country_code?: string | null
          status?: boolean | null
          updated_at?: string | null
          user_type?: Database["public"]["Enums"]["user_types"]
        }
        Update: {
          created_at?: string | null
          date_of_birth?: string | null
          email?: string | null
          first_login?: boolean | null
          full_name?: string | null
          id?: string
          last_sign_in_at?: string | null
          metadata?: Json | null
          partial_nric?: string | null
          phone_number?: string | null
          phone_number_country_code?: string | null
          status?: boolean | null
          updated_at?: string | null
          user_type?: Database["public"]["Enums"]["user_types"]
        }
        Relationships: []
      }
      working_experiences: {
        Row: {
          company_id: string | null
          designation: string | null
          duties: string | null
          id: string
          period_from: string | null
          period_to: string | null
          profile_id: string | null
        }
        Insert: {
          company_id?: string | null
          designation?: string | null
          duties?: string | null
          id?: string
          period_from?: string | null
          period_to?: string | null
          profile_id?: string | null
        }
        Update: {
          company_id?: string | null
          designation?: string | null
          duties?: string | null
          id?: string
          period_from?: string | null
          period_to?: string | null
          profile_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workingexperience_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workingexperience_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      application_export_view: {
        Row: {
          address_id: string | null
          applicant_form_path: string | null
          application_created_at: string | null
          application_id: string | null
          application_status:
            | Database["public"]["Enums"]["application_statuses"]
            | null
          application_type:
            | Database["public"]["Enums"]["application_types"]
            | null
          approved_or_rejected_at: string | null
          awards: Json | null
          city: string | null
          comment: string | null
          country: string | null
          country_code: string | null
          criminal_record: Json | null
          date_of_birth: string | null
          documents: Json | null
          education_history: Json | null
          email: string | null
          full_name: string | null
          gender: string | null
          has_declaration: boolean | null
          has_previous_application: boolean | null
          home_number: string | null
          identification_no: string | null
          identification_type: string | null
          industry_group: string | null
          industry_id: string | null
          industry_name: string | null
          is_aces_member: boolean | null
          membership_id: string | null
          membership_type: string | null
          membership_type_abbr: string | null
          membership_type_group:
            | Database["public"]["Enums"]["membership_type_group"]
            | null
          membership_type_name: string | null
          mobile_number: string | null
          nationality: string | null
          pe_form_path: string | null
          pedeclaration_form: string | null
          place_of_birth: string | null
          postal_code: string | null
          profile_created_at: string | null
          profile_id: string | null
          profile_type: Database["public"]["Enums"]["profile_types"] | null
          profile_updated_at: string | null
          projects: Json | null
          reference_no: string | null
          spoken_languages: string[] | null
          sponsorship_form_path: string | null
          sponsorships: Json | null
          state: string | null
          street_line_1: string | null
          street_line_2: string | null
          submitted_applicant_form_path: string | null
          submitted_at: string | null
          submitted_pe_form_path: string | null
          submitted_sponsorship_form_path: string | null
          title: string | null
          user_id: string | null
          work_experience: Json | null
          written_languages: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "application_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "membership_profiles"
            referencedColumns: ["membership_id"]
          },
          {
            foreignKeyName: "application_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "application_membership_type_fkey"
            columns: ["membership_type"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "application_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "sys_industries"
            referencedColumns: ["id"]
          },
        ]
      }
      application_profiles: {
        Row: {
          address_id: string | null
          applicant_form_path: string | null
          application_created_at: string | null
          application_id: string | null
          application_status:
            | Database["public"]["Enums"]["application_statuses"]
            | null
          application_type:
            | Database["public"]["Enums"]["application_types"]
            | null
          approved_or_rejected_at: string | null
          comment: string | null
          country: string | null
          date_of_birth: string | null
          email: string | null
          full_name: string | null
          gender: string | null
          has_declaration: boolean | null
          has_previous_application: boolean | null
          home_number: string | null
          identification_no: string | null
          identification_type: string | null
          industry_id: string | null
          is_aces_member: boolean | null
          membership_id: string | null
          membership_type: string | null
          membership_type_abbr: string | null
          membership_type_group:
            | Database["public"]["Enums"]["membership_type_group"]
            | null
          membership_type_name: string | null
          mobile_number: string | null
          nationality: string | null
          pe_form_path: string | null
          pedeclaration_form: string | null
          place_of_birth: string | null
          profile_created_at: string | null
          profile_id: string | null
          profile_type: Database["public"]["Enums"]["profile_types"] | null
          profile_updated_at: string | null
          reference_no: string | null
          spoken_languages: string[] | null
          sponsorship_form_path: string | null
          submitted_applicant_form_path: string | null
          submitted_at: string | null
          submitted_pe_form_path: string | null
          submitted_sponsorship_form_path: string | null
          title: string | null
          user_id: string | null
          written_languages: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "application_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "membership_profiles"
            referencedColumns: ["membership_id"]
          },
          {
            foreignKeyName: "application_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "application_membership_type_fkey"
            columns: ["membership_type"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "application_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "sys_industries"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_profiles: {
        Row: {
          address_id: string | null
          country: string | null
          date_of_birth: string | null
          email: string | null
          email_sent: boolean | null
          end_date: string | null
          full_name: string | null
          gender: string | null
          home_number: string | null
          identification_no: string | null
          identification_type: string | null
          industry_id: string | null
          is_aces_member: boolean | null
          member_since: string | null
          membership_id: string | null
          membership_number: string | null
          membership_status:
            | Database["public"]["Enums"]["membership_status"]
            | null
          membership_type: string | null
          membership_type_abbr: string | null
          membership_type_group:
            | Database["public"]["Enums"]["membership_type_group"]
            | null
          membership_type_name: string | null
          mobile_number: string | null
          nationality: string | null
          pedeclaration_form: string | null
          place_of_birth: string | null
          profile_created_at: string | null
          profile_id: string | null
          profile_type: Database["public"]["Enums"]["profile_types"] | null
          profile_updated_at: string | null
          spoken_languages: string[] | null
          start_date: string | null
          title: string | null
          user_id: string | null
          written_languages: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "membership_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "memberships_membership_type_fkey"
            columns: ["membership_type"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_industry_id_fkey"
            columns: ["industry_id"]
            isOneToOne: false
            referencedRelation: "sys_industries"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      assign_role_to_user: {
        Args: { target_user_id: string; role_name: string; reason?: string }
        Returns: boolean
      }
      bytea_to_text: {
        Args: { data: string }
        Returns: string
      }
      calculate_membership_dates: {
        Args: { membership_type_id: string; approved_or_rejected_at: string }
        Returns: Record<string, unknown>
      }
      calculate_registries_dates: {
        Args: { membership_type_id: string; approved_or_rejected_at: string }
        Returns: Record<string, unknown>
      }
      can_access_membership_record: {
        Args: { membership_type_id: string; action?: string }
        Returns: boolean
      }
      check_user_permission: {
        Args: {
          module_name: string
          action: string
          membership_type_id?: string
        }
        Returns: boolean
      }
      clone_profile: {
        Args: {
          old_profile_id: string
          new_profile_type: Database["public"]["Enums"]["profile_types"]
        }
        Returns: string
      }
      clone_profile_for_membership: {
        Args: { application_id: string; new_membership_id: string }
        Returns: string
      }
      create_module_admin_user: {
        Args: {
          target_user_id: string
          module_names: string[]
          membership_types?: string[]
        }
        Returns: boolean
      }
      create_new_role: {
        Args: { role_name: string; role_description?: string }
        Returns: string
      }
      deactivate_role: {
        Args: { target_role_name: string; reason?: string }
        Returns: boolean
      }
      export_applications: {
        Args: {
          status_filter?: Database["public"]["Enums"]["application_statuses"][]
          date_from?: string
          date_to?: string
          membership_type_id?: string
        }
        Returns: {
          address_id: string | null
          applicant_form_path: string | null
          application_created_at: string | null
          application_id: string | null
          application_status:
            | Database["public"]["Enums"]["application_statuses"]
            | null
          application_type:
            | Database["public"]["Enums"]["application_types"]
            | null
          approved_or_rejected_at: string | null
          awards: Json | null
          city: string | null
          comment: string | null
          country: string | null
          country_code: string | null
          criminal_record: Json | null
          date_of_birth: string | null
          documents: Json | null
          education_history: Json | null
          email: string | null
          full_name: string | null
          gender: string | null
          has_declaration: boolean | null
          has_previous_application: boolean | null
          home_number: string | null
          identification_no: string | null
          identification_type: string | null
          industry_group: string | null
          industry_id: string | null
          industry_name: string | null
          is_aces_member: boolean | null
          membership_id: string | null
          membership_type: string | null
          membership_type_abbr: string | null
          membership_type_group:
            | Database["public"]["Enums"]["membership_type_group"]
            | null
          membership_type_name: string | null
          mobile_number: string | null
          nationality: string | null
          pe_form_path: string | null
          pedeclaration_form: string | null
          place_of_birth: string | null
          postal_code: string | null
          profile_created_at: string | null
          profile_id: string | null
          profile_type: Database["public"]["Enums"]["profile_types"] | null
          profile_updated_at: string | null
          projects: Json | null
          reference_no: string | null
          spoken_languages: string[] | null
          sponsorship_form_path: string | null
          sponsorships: Json | null
          state: string | null
          street_line_1: string | null
          street_line_2: string | null
          submitted_applicant_form_path: string | null
          submitted_at: string | null
          submitted_pe_form_path: string | null
          submitted_sponsorship_form_path: string | null
          title: string | null
          user_id: string | null
          work_experience: Json | null
          written_languages: string[] | null
        }[]
      }
      generate_certificate_no: {
        Args: {
          source_type: Database["finance"]["Enums"]["certificate_source_type"]
          source_id: string
        }
        Returns: string
      }
      generate_invoice_from_registration: {
        Args: { input_registration_id: string }
        Returns: string
      }
      generate_invoice_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_accessible_applications: {
        Args: { status_filter?: string[]; membership_type_filter?: string }
        Returns: {
          application_id: string
          application_number: string
          profile_id: string
          membership_type_id: string
          membership_type_name: string
          full_name: string
          email: string
          status: string
          created_at: string
          can_update: boolean
          can_review: boolean
          can_approve: boolean
        }[]
      }
      get_accessible_membership_types: {
        Args: { module_name: string }
        Returns: string[]
      }
      get_accessible_membership_types_simple: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          abbr: string
          group: Database["public"]["Enums"]["membership_type_group"]
        }[]
      }
      get_accessible_memberships: {
        Args: { status_filter?: string[]; membership_type_filter?: string }
        Returns: {
          membership_id: string
          membership_number: string
          profile_id: string
          membership_type_id: string
          membership_type_name: string
          full_name: string
          email: string
          status: string
          valid_from: string
          valid_to: string
          can_update: boolean
          can_delete: boolean
        }[]
      }
      get_ownership_transfer_status: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_permission_audit_log: {
        Args: { target_user_id?: string; limit_count?: number }
        Returns: {
          audit_id: string
          user_email: string
          target_user_email: string
          action: string
          module_name: string
          role_name: string
          reason: string
          created_at: string
        }[]
      }
      get_roles_with_user_counts: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          role_description: string
          is_active: boolean
          created_at: string
          updated_at: string
          user_count: number
        }[]
      }
      get_user_accessible_membership_types: {
        Args: { p_user_id: string; p_module_name: string }
        Returns: string[]
      }
      get_user_permissions: {
        Args: Record<PropertyKey, never>
        Returns: {
          module_name: string
          can_create: boolean
          can_read: boolean
          can_update: boolean
          can_delete: boolean
          can_review: boolean
          can_approve: boolean
          can_export: boolean
          accessible_membership_types: string[]
        }[]
      }
      get_users_with_roles: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          email: string
          full_name: string
          phone_number: string
          partial_nric: string
          status: boolean
          role_name: string
          role_description: string
          is_system_owner: boolean
          created_at: string
        }[]
      }
      handle_personal_particulars_and_application: {
        Args: {
          input_user_id: string
          address_data: Json
          profile_data: Json
          membership_type_id: string
        }
        Returns: Json
      }
      http: {
        Args: { request: Database["public"]["CompositeTypes"]["http_request"] }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_delete: {
        Args:
          | { uri: string }
          | { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_get: {
        Args: { uri: string } | { uri: string; data: Json }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_head: {
        Args: { uri: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_header: {
        Args: { field: string; value: string }
        Returns: Database["public"]["CompositeTypes"]["http_header"]
      }
      http_list_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: {
          curlopt: string
          value: string
        }[]
      }
      http_patch: {
        Args: { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_post: {
        Args:
          | { uri: string; content: string; content_type: string }
          | { uri: string; data: Json }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_put: {
        Args: { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_reset_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      http_set_curlopt: {
        Args: { curlopt: string; value: string }
        Returns: boolean
      }
      import_membership_data: {
        Args: { input_data: Json }
        Returns: Json
      }
      import_programme_data_v2: {
        Args: { input_data: Json }
        Returns: Json
      }
      import_registry_data: {
        Args: { input_data: Json }
        Returns: Json
      }
      import_single_membership_record: {
        Args: { record_data: Json }
        Returns: Json
      }
      is_current_user_system_owner: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_user_type_admin: {
        Args: Record<PropertyKey, never> | { p_user_id: string }
        Returns: boolean
      }
      jsonb_diff: {
        Args: { old_val: Json; new_val: Json }
        Returns: Json
      }
      set_statement_timeout_for_import: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      submit_nominees: {
        Args: {
          application_id: string
          awards: Json
          educations: Json
          nominees: Json
        }
        Returns: Json
      }
      text_to_bytea: {
        Args: { data: string }
        Returns: string
      }
      urlencode: {
        Args: { data: Json } | { string: string } | { string: string }
        Returns: string
      }
      validate_field_length: {
        Args: {
          p_table_name: string
          p_column_name: string
          p_field_value: string
          p_row_number: number
        }
        Returns: undefined
      }
    }
    Enums: {
      address_types: "PROFILE" | "COMPANY"
      application_statuses:
        | "DRAFT"
        | "SUBMITTED_PENDING_PAYMENT"
        | "PAID_PENDING_REVIEW"
        | "TO_INTERVIEW"
        | "DOCUMENT_REQUIRED"
        | "APPROVED"
        | "REJECTED"
        | "CANCELLED"
      application_types: "NEW" | "RENEWAL"
      config_categories:
        | "INVOICE"
        | "RECEIPT"
        | "COMPANY"
        | "TAX"
        | "EMAIL"
        | "SYSTEM"
        | "PROFILE"
      config_data_types: "STRING" | "NUMBER" | "BOOLEAN" | "JSON" | "ARRAY"
      export_job_status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED"
      export_type: "APPLICATION" | "MEMBER" | "INVOICE" | "RECEIPT"
      form_section_type:
        | "PERSONAL"
        | "COMPANY"
        | "EMPLOYMENT"
        | "EDUCATION"
        | "WORK_EXPERIENCE"
        | "INDUSTRY"
        | "AWARDS"
        | "DOCUMENTS"
        | "DECLARATION"
        | "SPONSORSHIP"
        | "COMPANY_NOMINEES"
        | "CRIMINAL_RECORD"
        | "POST_GRAD_EXPERIENCE"
      membership_notice_types: "PRE_APPLY" | "DOCUMENTS"
      membership_rule_types:
        | "AGE_MIN"
        | "AGE_MAX"
        | "REQUIRES_PHYSICAL_SUBMISSION"
        | "REQUIRES_SPONSOR"
        | "REQUIRES_WORK_EXPERIENCE"
        | "REQUIRES_EDUCATION_LEVEL"
        | "REQUIRES_PROFESSIONAL_QUALIFICATION"
      membership_status:
        | "ACTIVE"
        | "INACTIVE"
        | "SUSPENDED"
        | "TERMINATED"
        | "EXPIRED"
        | "PENDING_RENEWAL"
        | "PENDING_RE_APPLICATION"
      membership_type_group: "MBR" | "PR" | "JR"
      profile_types: "APPLICATION" | "MEMBERSHIP"
      record_types: "AFFILIATION" | "AWARD"
      sponsorship_types: "PROPOSER" | "SECONDER"
      user_types: "ADMIN" | "USER"
    }
    CompositeTypes: {
      http_header: {
        field: string | null
        value: string | null
      }
      http_request: {
        method: unknown | null
        uri: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content_type: string | null
        content: string | null
      }
      http_response: {
        status: number | null
        content_type: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content: string | null
      }
    }
  }
  system: {
    Tables: {
      document_numbering: {
        Row: {
          created_at: string | null
          current_number: number | null
          current_year: number | null
          document_type: string
          entity_id: string | null
          id: string
          last_reset_year: number | null
          padding_length: number | null
          pattern: string | null
          prefix: string
          reset_yearly: boolean | null
          separator: string | null
          updated_at: string | null
          year_included: boolean | null
        }
        Insert: {
          created_at?: string | null
          current_number?: number | null
          current_year?: number | null
          document_type: string
          entity_id?: string | null
          id?: string
          last_reset_year?: number | null
          padding_length?: number | null
          pattern?: string | null
          prefix: string
          reset_yearly?: boolean | null
          separator?: string | null
          updated_at?: string | null
          year_included?: boolean | null
        }
        Update: {
          created_at?: string | null
          current_number?: number | null
          current_year?: number | null
          document_type?: string
          entity_id?: string | null
          id?: string
          last_reset_year?: number | null
          padding_length?: number | null
          pattern?: string | null
          prefix?: string
          reset_yearly?: boolean | null
          separator?: string | null
          updated_at?: string | null
          year_included?: boolean | null
        }
        Relationships: []
      }
      options: {
        Row: {
          code: string
          created_at: string | null
          id: string
          is_active: boolean | null
          label: string
          type: Database["system"]["Enums"]["option_type"]
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          label: string
          type: Database["system"]["Enums"]["option_type"]
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          label?: string
          type?: Database["system"]["Enums"]["option_type"]
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      generate_document_number: {
        Args: { p_document_type: string; p_entity_id?: string }
        Returns: string
      }
      get_next_document_number: {
        Args: { doc_type: string; p_entity_id?: string }
        Returns: string
      }
      get_options: {
        Args: { option_type: Database["system"]["Enums"]["option_type"] }
        Returns: {
          id: string
          code: string
          label: string
        }[]
      }
      get_padding_from_pattern: {
        Args: { p_pattern: string }
        Returns: number
      }
    }
    Enums: {
      option_type:
        | "country"
        | "salutation"
        | "id_type"
        | "language"
        | "budget_type"
        | "budget_format"
        | "payment_options"
        | "supplier_type"
        | "supplier_category"
        | "currency"
        | "uom"
        | "professional_id_type"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  access_control: {
    Enums: {},
  },
  approval: {
    Enums: {
      approval_action: ["APPROVE", "REJECT", "REQUEST_INFO", "DELEGATE"],
      approval_status: ["PENDING", "APPROVED", "REJECTED", "CANCELLED"],
      approver_type: ["ROLE", "USER", "DEPARTMENT", "DYNAMIC"],
      post_approval_action_type: [
        "update_record",
        "insert_record",
        "call_function",
        "send_notification",
        "update_status",
        "link_entities",
        "trigger_process",
      ],
    },
  },
  budget: {
    Enums: {
      budget_format: ["SIMPLE", "COMPLEX"],
      budget_group: ["INCOME", "EXPENDITURE"],
      budget_status: [
        "DRAFT",
        "PENDING_APPROVAL",
        "APPROVED",
        "REJECTED",
        "CANCELLED",
      ],
    },
  },
  calendar: {
    Enums: {
      appointment_status: [
        "SCHEDULED",
        "COMPLETED",
        "CANCELLED",
        "RESCHEDULED",
      ],
    },
  },
  email_system: {
    Enums: {
      email_campaign_status: [
        "DRAFT",
        "SCHEDULED",
        "SENDING",
        "SENT",
        "FAILED",
      ],
      email_delivery_status: [
        "PENDING",
        "QUEUED",
        "SENT",
        "DELIVERED",
        "BOUNCED",
        "FAILED",
        "OPENED",
        "CLICKED",
        "UNSUBSCRIBED",
        "SPAM_REPORTED",
      ],
      email_priority: ["LOW", "NORMAL", "HIGH", "URGENT"],
      membership_status: ["ACTIVE", "EXPIRED", "PENDING", "SUSPENDED"],
      template_category: [
        "NEWSLETTER",
        "PROMOTIONAL",
        "ANNOUNCEMENT",
        "WELCOME",
        "FOLLOWUP",
        "EVENT",
        "SURVEY",
        "NOTIFICATION",
        "CUSTOM",
      ],
      template_status: ["DRAFT", "ACTIVE", "ARCHIVED"],
    },
  },
  finance: {
    Enums: {
      certificate_source_type: ["MEMBERSHIP", "PROGRAMME", "OTHER"],
      customer_status_enum: ["ACTIVE", "INACTIVE", "SUSPENDED", "BLACKLISTED"],
      customer_type_enum: ["INDIVIDUAL", "COMPANY", "MEMBER", "NON_MEMBER"],
      entity_type: [
        "ORGANIZATION",
        "ENTERPRISE",
        "CHARITY",
        "GOVERNMENT",
        "OTHER",
      ],
      interval_enum: [
        "WEEKLY",
        "MONTHLY",
        "QUARTERLY",
        "SEMI_ANNUALLY",
        "ANNUALLY",
      ],
      invoice_source_type: ["MEMBERSHIP", "PROGRAMME", "OTHER"],
      invoice_status: [
        "DRAFT",
        "UNPAID",
        "SENT",
        "PARTIALLY_PAID",
        "PAID",
        "VOID",
        "OVERDUE",
      ],
      invoice_type_enum: [
        "STANDARD",
        "PROFORMA",
        "RECURRING",
        "ADJUSTMENT",
        "CREDIT_NOTE",
      ],
      payment_gateway_status: [
        "INITIALIZED",
        "PENDING_PAYMENT",
        "PROCESSING",
        "SUCCESS",
        "FAILED",
        "CANCELLED",
        "EXPIRED",
      ],
      payment_method_type: [
        "CASH",
        "BANK_TRANSFER",
        "CREDIT_CARD",
        "CHEQUE",
        "DIGITAL_WALLET",
        "2C2P",
        "SFC",
        "GIRO",
        "FAST",
      ],
      payment_status: ["SUCCESS", "PENDING", "FAILED", "VOID"],
      quotation_status: [
        "DRAFT",
        "SENT",
        "ACCEPTED",
        "REJECTED",
        "EXPIRED",
        "CANCELLED",
      ],
      receipt_type_enum: ["PAYMENT", "REFUND", "ADJUSTMENT"],
      sales_order_status_enum: [
        "DRAFT",
        "CONFIRMED",
        "PARTIALLY_INVOICED",
        "INVOICED",
        "DELIVERED",
        "CANCELLED",
      ],
      statement_status_enum: ["GENERATED", "SENT", "ACKNOWLEDGED"],
      template_status_enum: ["ACTIVE", "PAUSED", "COMPLETED", "CANCELLED"],
    },
  },
  graphql_public: {
    Enums: {},
  },
  notification: {
    Enums: {},
  },
  organisation: {
    Enums: {},
  },
  points: {
    Enums: {
      allocation_type: ["MANUAL", "AUTOMATIC"],
      point_status: ["PENDING", "APPROVED", "REJECTED"],
    },
  },
  procurement: {
    Enums: {
      goods_receipt_status: ["PENDING", "PARTIAL", "COMPLETE", "CANCELLED"],
      payment_status_enum: ["PENDING", "COMPLETED", "CANCELLED", "FAILED"],
      po_status_enum: [
        "DRAFT",
        "SUBMITTED",
        "PENDING_APPROVAL",
        "APPROVED",
        "SENT",
        "PARTIALLY_RECEIVED",
        "RECEIVED",
        "CANCELLED",
        "CLOSED",
      ],
      purchase_invoice_status_enum: [
        "SUBMITTED",
        "PENDING_APPROVAL",
        "RECEIVED",
        "APPROVED",
        "PAID",
        "DISPUTED",
        "CANCELLED",
        "RETURNED",
      ],
      supplier_status_enum: ["ACTIVE", "INACTIVE", "SUSPENDED", "BLACKLISTED"],
      supplier_type_enum: [
        "VENDOR",
        "CONTRACTOR",
        "SERVICE_PROVIDER",
        "CONSULTANT",
      ],
    },
  },
  programme: {
    Enums: {
      participant_cert_status: ["SENT", "NOT_SENT"],
      programme_format: ["IN-PERSON", "VIRTUAL", "HYBRID"],
      programme_status: [
        "DRAFT",
        "PUBLISHED",
        "UNPUBLISHED",
        "TERMINATED",
        "COMPLETED",
      ],
      programme_type: ["COURSE", "EVENT", "SEMINAR", "CONFERENCE"],
      qr_code_action: ["CHECKIN", "CHECKOUT"],
      registration_status: ["PENDING", "CONFIRMED", "CANCELLED"],
      registration_type: ["INDIVIDUAL", "GROUP", "TABLE"],
    },
  },
  public: {
    Enums: {
      address_types: ["PROFILE", "COMPANY"],
      application_statuses: [
        "DRAFT",
        "SUBMITTED_PENDING_PAYMENT",
        "PAID_PENDING_REVIEW",
        "TO_INTERVIEW",
        "DOCUMENT_REQUIRED",
        "APPROVED",
        "REJECTED",
        "CANCELLED",
      ],
      application_types: ["NEW", "RENEWAL"],
      config_categories: [
        "INVOICE",
        "RECEIPT",
        "COMPANY",
        "TAX",
        "EMAIL",
        "SYSTEM",
        "PROFILE",
      ],
      config_data_types: ["STRING", "NUMBER", "BOOLEAN", "JSON", "ARRAY"],
      export_job_status: ["PENDING", "PROCESSING", "COMPLETED", "FAILED"],
      export_type: ["APPLICATION", "MEMBER", "INVOICE", "RECEIPT"],
      form_section_type: [
        "PERSONAL",
        "COMPANY",
        "EMPLOYMENT",
        "EDUCATION",
        "WORK_EXPERIENCE",
        "INDUSTRY",
        "AWARDS",
        "DOCUMENTS",
        "DECLARATION",
        "SPONSORSHIP",
        "COMPANY_NOMINEES",
        "CRIMINAL_RECORD",
        "POST_GRAD_EXPERIENCE",
      ],
      membership_notice_types: ["PRE_APPLY", "DOCUMENTS"],
      membership_rule_types: [
        "AGE_MIN",
        "AGE_MAX",
        "REQUIRES_PHYSICAL_SUBMISSION",
        "REQUIRES_SPONSOR",
        "REQUIRES_WORK_EXPERIENCE",
        "REQUIRES_EDUCATION_LEVEL",
        "REQUIRES_PROFESSIONAL_QUALIFICATION",
      ],
      membership_status: [
        "ACTIVE",
        "INACTIVE",
        "SUSPENDED",
        "TERMINATED",
        "EXPIRED",
        "PENDING_RENEWAL",
        "PENDING_RE_APPLICATION",
      ],
      membership_type_group: ["MBR", "PR", "JR"],
      profile_types: ["APPLICATION", "MEMBERSHIP"],
      record_types: ["AFFILIATION", "AWARD"],
      sponsorship_types: ["PROPOSER", "SECONDER"],
      user_types: ["ADMIN", "USER"],
    },
  },
  system: {
    Enums: {
      option_type: [
        "country",
        "salutation",
        "id_type",
        "language",
        "budget_type",
        "budget_format",
        "payment_options",
        "supplier_type",
        "supplier_category",
        "currency",
        "uom",
        "professional_id_type",
      ],
    },
  },
} as const

