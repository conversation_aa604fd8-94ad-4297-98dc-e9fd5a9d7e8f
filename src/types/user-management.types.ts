// Enterprise User & Role Management Types
// Comprehensive type definitions for the RBAC system

export interface User {
  id: string
  email: string
  full_name?: string
  user_type: 'ADMIN' | 'USER'
  partial_nric?: string
  date_of_birth?: string
  phone_number_country_code?: string
  phone_number?: string
  status: boolean
  first_login?: boolean
  metadata?: Record<string, any>
  created_at: string
  updated_at?: string
  last_sign_in_at?: string
}

export interface UserWithRole {
  id: string
  email: string
  full_name?: string
  phone_number?: string
  partial_nric?: string
  status: boolean
  role_name?: string
  role_description?: string
  is_system_owner?: boolean
  created_at: string
}

export interface Role {
  id: string
  name: string
  role_description?: string
  is_active: boolean
  created_at: string
  updated_at?: string
  user_count?: number
}

export interface Module {
  module_id: string
  module_name: string
  module_description?: string
  module_category: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface Permission {
  can_create: boolean
  can_read: boolean
  can_update: boolean
  can_delete: boolean
  can_review: boolean
  can_approve: boolean
  can_export: boolean
}

export interface ModulePermission extends Permission {
  permission_id: string
  role_id: string
  module_id: string
  accessible_membership_types?: string[]
  data_filters?: Record<string, any>
  field_restrictions?: Record<string, any>
  status_constraints?: string[]
  valid_from?: string
  valid_to?: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface UserPermissions {
  module_name: string
  can_create: boolean
  can_read: boolean
  can_update: boolean
  can_delete: boolean
  can_review: boolean
  can_approve: boolean
  can_export: boolean
  accessible_membership_types: string[]
}

export interface RoleWithPermissions extends Role {
  permissions: ModulePermission[]
  user_count?: number
}

export interface OwnershipTransfer {
  id: string
  user_id: string
  protection_type: string
  protection_reason?: string
  protected_by?: string
  ownership_transfer_target?: string
  transfer_initiated_at?: string
  transfer_expires_at?: string
  transfer_status: 'NONE' | 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'COMPLETED'
  transfer_message?: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface OwnershipTransferLog {
  id: string
  transfer_id: string
  from_user_id: string
  to_user_id: string
  transfer_type: 'INITIATED' | 'ACCEPTED' | 'COMPLETED' | 'EXPIRED' | 'CANCELLED'
  action_by: string
  action_reason?: string
  metadata?: Record<string, any>
  created_at: string
}

export interface PermissionAudit {
  audit_id: string
  action: string
  user_id?: string
  target_user_id?: string
  role_name?: string
  module_name?: string
  old_permissions?: Record<string, any>
  new_permissions?: Record<string, any>
  reason?: string
  created_at: string
}

// API Response Types
export interface UserManagementResponse<T> {
  data: T
  error?: string
  success: boolean
}

export interface CreateUserRequest {
  email: string
  full_name: string
  password: string
  role_name?: string
  phone_number_country_code?: string
  phone_number?: string
  partial_nric?: string
  date_of_birth?: string
  send_invitation?: boolean
}

export interface UpdateUserRequest {
  full_name?: string
  phone_number_country_code?: string
  phone_number?: string
  partial_nric?: string
  date_of_birth?: string
  status?: boolean
  metadata?: Record<string, any>
}

export interface AssignRoleRequest {
  user_id: string
  role_name: string
  reason?: string
}

export interface CreateRoleRequest {
  name: string
  role_description?: string
}

export interface UpdateRolePermissionsRequest {
  role_id: string
  module_id: string
  permissions: Partial<Permission>
  accessible_membership_types?: string[]
  data_filters?: Record<string, any>
  field_restrictions?: Record<string, any>
  status_constraints?: string[]
  reason?: string
}

export interface InitiateOwnershipTransferRequest {
  target_user_id: string
  transfer_message?: string
  reason?: string
}

export interface RespondToOwnershipTransferRequest {
  transfer_id: string
  action: 'ACCEPT' | 'REJECT'
  reason?: string
}

// Filter and Search Types
export interface UserFilters {
  search?: string
  role?: string
  status?: boolean | 'ALL'
  created_date_from?: string
  created_date_to?: string
}

export interface RoleFilters {
  search?: string
  is_active?: boolean | 'ALL'
  module?: string
}

export interface AuditFilters {
  user_id?: string
  action?: string
  module_name?: string
  role_name?: string
  date_from?: string
  date_to?: string
}

// Table Action Types
export type UserAction = 'view' | 'edit' | 'delete' | 'assign_role' | 'deactivate' | 'activate'
export type RoleAction = 'view' | 'edit' | 'delete' | 'assign_users' | 'permissions'

// Component State Types
export interface UserManagementState {
  users: UserWithRole[]
  roles: Role[]
  modules: Module[]
  selectedUser: UserWithRole | null
  selectedRole: RoleWithPermissions | null
  loading: boolean
  error: string | null
}

export interface UserTableState {
  data: UserWithRole[]
  filteredData: UserWithRole[]
  filters: UserFilters
  sorting: {
    field: keyof UserWithRole
    direction: 'asc' | 'desc'
  }
  pagination: {
    page: number
    pageSize: number
    total: number
  }
  selectedRows: string[]
}

export interface RoleTableState {
  data: RoleWithPermissions[]
  filteredData: RoleWithPermissions[]
  filters: RoleFilters
  sorting: {
    field: keyof RoleWithPermissions
    direction: 'asc' | 'desc'
  }
  pagination: {
    page: number
    pageSize: number
    total: number
  }
  selectedRows: string[]
}