// Data transformation utilities for Goods Receipt module
// Handles conversions between UI forms, API calls, and database entities

import type {
  GoodsReceiptFormData,
  GoodsReceiptItemFormData,
} from "@/schemas/purchase-orders/goods-receipt.schema";
import type {
  GoodsReceiptEntity,
  GoodsReceiptItemEntity,
  RejectionReasonType,
} from "@/types/purchase-orders/goods-receipt.types";
import { REJECTION_REASON_LABELS } from "@/types/purchase-orders/goods-receipt.types";

/**
 * Formats a rejection reason for display/storage
 * Combines the rejection type and optional notes into a single string
 */
export function formatRejectionReason(
  reasonType?: RejectionReasonType | string,
  notes?: string
): string {
  if (!reasonType) return "";
  
  // Get the label for the rejection type
  const label = REJECTION_REASON_LABELS[reasonType as RejectionReasonType] || reasonType;
  
  // If it's "other" and we have notes, append them
  if (reasonType === "other" && notes && notes.trim()) {
    return `${label}: ${notes.trim()}`;
  }
  
  return label;
}

/**
 * Parses a formatted rejection reason back into type and notes
 * Used when editing existing goods receipts
 */
export function parseRejectionReason(
  rejectionReason?: string | null
): { type?: RejectionReasonType; notes?: string } {
  if (!rejectionReason) return {};
  
  // Check if it matches a standard reason
  const standardReasons = Object.entries(REJECTION_REASON_LABELS);
  for (const [key, label] of standardReasons) {
    if (rejectionReason === label && key !== "other") {
      return { type: key as RejectionReasonType };
    }
  }
  
  // Check if it's an "Other" reason with notes
  const otherPrefix = `${REJECTION_REASON_LABELS.other}: `;
  if (rejectionReason.startsWith(otherPrefix)) {
    return {
      type: "other",
      notes: rejectionReason.substring(otherPrefix.length)
    };
  }
  
  // Default to other with the full string as notes
  return {
    type: "other",
    notes: rejectionReason
  };
}

/**
 * Transforms form data for API submission
 * Handles date conversions and rejection reason formatting
 */
export function transformFormDataForAPI(formData: GoodsReceiptFormData): any {
  return {
    purchase_order_id: formData.purchase_order_id,
    receipt_date: formData.receipt_date.toISOString(),
    received_by: formData.received_by,
    notes: formData.notes || null,
    
    // Transform items with formatted rejection reasons
    items: formData.items.map(item => ({
      po_item_id: item.po_item_id,
      quantity_received: Number(item.quantity_received.toFixed(4)),
      quantity_accepted: Number(item.quantity_accepted.toFixed(4)),
      quantity_rejected: Number(item.quantity_rejected.toFixed(4)),
      rejection_reason: item.quantity_rejected > 0 
        ? formatRejectionReason(item.rejection_reason_type, item.rejection_notes)
        : null
    }))
  };
}

/**
 * Transforms database entity to form values for editing
 * Parses rejection reasons and converts dates
 */
export function transformEntityToFormData(
  entity: GoodsReceiptEntity,
  items: GoodsReceiptItemEntity[]
): GoodsReceiptFormData {
  return {
    purchase_order_id: entity.purchase_order_id,
    receipt_date: new Date(entity.receipt_date),
    received_by: entity.received_by,
    notes: entity.notes || undefined,
    
    // Inspection fields - default values since not in database yet
    inspection_required: false,
    inspector_name: undefined,
    inspection_date: undefined,
    inspection_report_number: undefined,
    
    // Transform items with parsed rejection reasons
    items: items.map(item => {
      const { type, notes } = parseRejectionReason(item.rejection_reason);
      return {
        po_item_id: item.po_item_id,
        quantity_received: item.quantity_received,
        quantity_accepted: item.quantity_accepted,
        quantity_rejected: item.quantity_rejected,
        rejection_reason: item.rejection_reason || undefined,
        rejection_reason_type: type,
        rejection_notes: notes
      };
    })
  };
}

/**
 * Calculates receipt totals from items
 */
export function calculateReceiptTotals(items: GoodsReceiptItemFormData[]) {
  return items.reduce(
    (totals, item) => ({
      totalReceived: totals.totalReceived + item.quantity_received,
      totalAccepted: totals.totalAccepted + item.quantity_accepted,
      totalRejected: totals.totalRejected + item.quantity_rejected,
      hasRejections: totals.hasRejections || item.quantity_rejected > 0
    }),
    { totalReceived: 0, totalAccepted: 0, totalRejected: 0, hasRejections: false }
  );
}

/**
 * Validates if quantities are within acceptable ranges
 * Accounts for floating point precision issues
 */
export function validateQuantities(
  received: number,
  accepted: number,
  rejected: number
): { valid: boolean; error?: string } {
  // Check non-negative
  if (received < 0 || accepted < 0 || rejected < 0) {
    return { valid: false, error: "Quantities cannot be negative" };
  }
  
  // Check sum with tolerance for floating point
  const sum = accepted + rejected;
  const diff = Math.abs(received - sum);
  
  if (diff > 0.0001) {
    return { 
      valid: false, 
      error: "Accepted + Rejected must equal Received quantity" 
    };
  }
  
  return { valid: true };
}

/**
 * Formats a quantity for display with proper decimal places
 * Shows up to 4 decimal places, removing trailing zeros
 */
export function formatQuantity(quantity: number): string {
  return Number(quantity.toFixed(4)).toString();
}

/**
 * Parses a quantity string to number with validation
 */
export function parseQuantity(value: string): number {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : parsed;
}