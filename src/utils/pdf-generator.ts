import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { 
  BudgetReviewSimpleEventProps, 
  BudgetReviewComplexProps
} from '@/types/budgets/budgets.types';

interface BudgetDetails {
  budget_code?: string;
  title?: string;
  description?: string;
  entity_type?: string;
  format?: 'SIMPLE' | 'COMPLEX';
  status?: string;
  programme_name?: string;
  programme_code?: string;
}

export async function generateBudgetPDF(
  budgetData: BudgetReviewSimpleEventProps | BudgetReviewComplexProps,
  budgetDetails: BudgetDetails,
  format: 'SIMPLE' | 'COMPLEX'
): Promise<void> {
  const doc = new jsPDF();
  let currentY = 20;

  // Helper function to check if we need a new page
  const checkPageBreak = (neededSpace: number) => {
    if (currentY + neededSpace > 270) { // Leave space for footer
      doc.addPage();
      currentY = 20;
    }
  };

  // Title and Header
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('Budget Report', 105, currentY, { align: 'center' });
  currentY += 15;

  // Budget Details
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  
  if (budgetDetails.budget_code) {
    doc.text(`Budget Code: ${budgetDetails.budget_code}`, 20, currentY);
    currentY += 7;
  }
  
  if (budgetDetails.title) {
    doc.text(`Title: ${budgetDetails.title}`, 20, currentY);
    currentY += 7;
  }
  
  if (budgetDetails.programme_name && budgetDetails.programme_code) {
    doc.text(`Programme: ${budgetDetails.programme_name} (${budgetDetails.programme_code})`, 20, currentY);
    currentY += 7;
  }
  
  if (budgetDetails.entity_type) {
    doc.text(`Entity Type: ${budgetDetails.entity_type}`, 20, currentY);
    currentY += 7;
  }
  
  if (budgetDetails.format) {
    doc.text(`Format: ${budgetDetails.format}`, 20, currentY);
    currentY += 7;
  }
  
  if (budgetDetails.status) {
    doc.text(`Status: ${budgetDetails.status}`, 20, currentY);
    currentY += 7;
  }
  
  if (budgetDetails.description) {
    const splitDescription = doc.splitTextToSize(budgetDetails.description, 170);
    doc.text(`Description: ${splitDescription}`, 20, currentY);
    currentY += splitDescription.length * 7 + 5;
  }

  currentY += 10;

  if (format === 'SIMPLE') {
    const simpleData = budgetData as BudgetReviewSimpleEventProps;
    
    // Income Section
    checkPageBreak(60);
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Income', 20, currentY);
    currentY += 10;

    const incomeTableData = simpleData.incomeEntries.map(entry => [
      entry.type,
      entry.proposedRate.toFixed(2),
      entry.quantity.toString(),
      entry.subTotal.toFixed(2),
      entry.remarks || ''
    ]);

    // Add total row
    incomeTableData.push([
      'Total Income',
      '',
      '',
      simpleData.summary?.total_income?.toFixed(2) || '0.00',
      ''
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Item', 'Proposed Rate', 'Quantity', 'Sub Total', 'Remarks']],
      body: incomeTableData,
      theme: 'grid',
      headStyles: { fillColor: [66, 139, 202] },
      footStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
      didDrawPage: (data) => {
        if (data.cursor) {
          currentY = data.cursor.y + 10;
        }
      }
    });

    // Expenditure Section
    checkPageBreak(60);
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Expenditure', 20, currentY);
    currentY += 10;

    const expenditureTableData = simpleData.expenditureEntries.map(entry => [
      entry.type,
      entry.proposedRate.toFixed(2),
      entry.quantity.toString(),
      entry.subTotal.toFixed(2),
      entry.remarks || ''
    ]);

    // Add total row
    expenditureTableData.push([
      'Total Expenditure',
      '',
      '',
      simpleData.summary?.total_expenditure?.toFixed(2) || '0.00',
      ''
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Item', 'Proposed Rate', 'Quantity', 'Sub Total', 'Remarks']],
      body: expenditureTableData,
      theme: 'grid',
      headStyles: { fillColor: [66, 139, 202] },
      footStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
      didDrawPage: (data) => {
        if (data.cursor) {
          currentY = data.cursor.y + 10;
        }
      }
    });

  } else {
    const complexData = budgetData as BudgetReviewComplexProps;
    
    // Income Sections
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Income', 20, currentY);
    currentY += 10;

    for (const section of complexData.incomeEntries) {
      checkPageBreak(50);
      
      // Section header
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.setFillColor(240, 240, 240);
      doc.rect(20, currentY - 5, 170, 10, 'F');
      doc.text(toTitleCase(section.sectionName), 25, currentY);
      currentY += 10;

      const sectionData = section.types.map(type => [
        type.type,
        type.proposedRate.toFixed(2),
        type.quantity.toString(),
        type.subTotal.toFixed(2),
        type.remarks || ''
      ]);

      // Add section total
      const sectionTotal = section.types.reduce((total, type) => total + (type.subTotal || 0), 0);
      sectionData.push([
        'Section Total',
        '',
        '',
        sectionTotal.toFixed(2),
        ''
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [['Item', 'Proposed Rate', 'Quantity', 'Sub Total', 'Remarks']],
        body: sectionData,
        theme: 'grid',
        headStyles: { fillColor: [66, 139, 202] },
        footStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
        didDrawPage: (data) => {
          if (data.cursor) {
            currentY = data.cursor.y + 10;
          }
        }
      });
    }

    // Total Income
    checkPageBreak(30);
    autoTable(doc, {
      startY: currentY,
      body: [[
        'Total Income',
        '',
        '',
        complexData.summary?.total_income?.toFixed(2) || '0.00',
        ''
      ]],
      theme: 'grid',
      bodyStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
      didDrawPage: (data) => {
        if (data.cursor) {
          currentY = data.cursor.y + 15;
        }
      }
    });

    // Expenditure Sections
    checkPageBreak(40);
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Expenditure', 20, currentY);
    currentY += 10;

    for (const section of complexData.expenditureEntries) {
      checkPageBreak(50);
      
      // Section header
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.setFillColor(240, 240, 240);
      doc.rect(20, currentY - 5, 170, 10, 'F');
      doc.text(toTitleCase(section.sectionName), 25, currentY);
      currentY += 10;

      const sectionData = section.types.map(type => [
        type.type,
        type.proposedRate.toFixed(2),
        type.quantity.toString(),
        type.subTotal.toFixed(2),
        type.remarks || ''
      ]);

      // Add section total
      const sectionTotal = section.types.reduce((total, type) => total + (type.subTotal || 0), 0);
      sectionData.push([
        'Section Total',
        '',
        '',
        sectionTotal.toFixed(2),
        ''
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [['Item', 'Proposed Rate', 'Quantity', 'Sub Total', 'Remarks']],
        body: sectionData,
        theme: 'grid',
        headStyles: { fillColor: [66, 139, 202] },
        footStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
        didDrawPage: (data) => {
          if (data.cursor) {
            currentY = data.cursor.y + 10;
          }
        }
      });
    }

    // Total Expenditure
    checkPageBreak(30);
    autoTable(doc, {
      startY: currentY,
      body: [[
        'Total Expenditure',
        '',
        '',
        complexData.summary?.total_expenditure?.toFixed(2) || '0.00',
        ''
      ]],
      theme: 'grid',
      bodyStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold' },
      didDrawPage: (data) => {
        if (data.cursor) {
          currentY = data.cursor.y + 15;
        }
      }
    });
  }

  // Summary Section
  checkPageBreak(60);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Budget Summary', 20, currentY);
  currentY += 10;

  const summaryData = [
    ['Total Income (Excl GST)', budgetData.summary?.total_income?.toFixed(2) || '0.00'],
    ['Total Expenditure (Excl GST)', budgetData.summary?.total_expenditure?.toFixed(2) || '0.00'],
    ['Net Surplus / Deficit (A - B)', budgetData.summary?.net_surplus_deficit?.toFixed(2) || '0.00'],
    ['% Of Net Surplus / Deficit Over Total Income', `${budgetData.summary?.surplus_deficit_percentage?.toFixed(2) || '0.00'}%`]
  ];

  autoTable(doc, {
    startY: currentY,
    head: [['Description', 'Amount']],
    body: summaryData,
    theme: 'grid',
    headStyles: { fillColor: [66, 139, 202] },
    didDrawPage: (data) => {
      if (data.cursor) {
        currentY = data.cursor.y + 20;
      }
    }
  });

  // Signature Lines
  checkPageBreak(80);
  currentY += 20;
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  
  // Requestor signature
  doc.line(20, currentY, 90, currentY);
  doc.text('Requestor', 55, currentY + 7, { align: 'center' });
  doc.text('Name: _____________________', 20, currentY + 15);
  doc.text('Date: _____________________', 20, currentY + 23);
  
  // Approver signature
  doc.line(120, currentY, 190, currentY);
  doc.text('Approver', 155, currentY + 7, { align: 'center' });
  doc.text('Name: _____________________', 120, currentY + 15);
  doc.text('Date: _____________________', 120, currentY + 23);

  // Save the PDF
  const fileName = `budget_${budgetDetails.budget_code || 'report'}_${new Date().toISOString().split('T')[0]}.pdf`;
  doc.save(fileName);
}

// Helper function to convert to title case
function toTitleCase(str: string): string {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}