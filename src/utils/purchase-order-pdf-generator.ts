import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { PurchaseOrder } from '@/types/purchase-orders/purchase-orders.types';
import { format } from 'date-fns';
import {
  formatCurrency,
  formatTableCurrency,
  formatLineNumber,
  formatPaymentTerms,
  formatAddressLines,
  calculateGST,
  formatGSTLabel,
  PO_DATE_FORMAT
} from '@/utils/purchase-order-helpers';

export async function generatePurchaseOrderPDF(purchaseOrder: PurchaseOrder): Promise<void> {
  const doc = new jsPDF();
  let currentY = 20;

  // Helper function to check if we need a new page
  const checkPageBreak = (neededSpace: number) => {
    if (currentY + neededSpace > 270) { // Leave space for footer
      doc.addPage();
      currentY = 20;
    }
  };

  // Header Section
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('The Institution Of Engineers, Singapore', 105, currentY, { align: 'center' });
  currentY += 8;

  doc.setFontSize(14);
  doc.text('Purchase Order Form', 105, currentY, { align: 'center' });
  currentY += 8;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('70 Bukit Tinggi Road. Singapore 289758. Tel. 6469 5000', 105, currentY, { align: 'center' });
  currentY += 15;

  // Create the two boxes side by side
  const boxStartY = currentY;
  const leftBoxWidth = 95;
  const rightBoxWidth = 95;
  const boxHeight = 50;
  const leftBoxX = 10;
  const rightBoxX = 105;

  // Left Box - Vendor Information
  doc.rect(leftBoxX, boxStartY, leftBoxWidth, boxHeight);
  doc.setFont('helvetica', 'bold');
  doc.text('Vendor Name & Address :', leftBoxX + 3, boxStartY + 8);

  doc.setFont('helvetica', 'normal');
  let vendorY = boxStartY + 15;

  // Vendor name
  if (purchaseOrder.suppliers?.name) {
    doc.text(purchaseOrder.suppliers.name, leftBoxX + 3, vendorY);
    vendorY += 6;
  }

  // Display name if different
  if (purchaseOrder.suppliers?.display_name && purchaseOrder.suppliers.display_name !== purchaseOrder.suppliers.name) {
    doc.text(purchaseOrder.suppliers.display_name, leftBoxX + 3, vendorY);
    vendorY += 6;
  }

  // Address
  const addressLines = formatAddressLines(purchaseOrder.suppliers?.billing_address);

  if (addressLines.length > 0) {
    addressLines.forEach((line) => {
      if (vendorY < boxStartY + boxHeight - 6) {
        // Split long lines if needed
        if (line.length > 40) {
          const splitLines = doc.splitTextToSize(line, leftBoxWidth - 6);
          splitLines.forEach((splitLine: string) => {
            if (vendorY < boxStartY + boxHeight - 6) {
              doc.text(splitLine, leftBoxX + 3, vendorY);
              vendorY += 6;
            }
          });
        } else {
          doc.text(line, leftBoxX + 3, vendorY);
          vendorY += 6;
        }
      }
    });
  } else {
    doc.text('Address', leftBoxX + 3, vendorY);
    vendorY += 6;
    doc.text('Singapore', leftBoxX + 3, vendorY);
  }

  // Right Box - PO Information
  doc.rect(rightBoxX, boxStartY, rightBoxWidth, boxHeight);

  // Define the layout for the right box
  const labelX = rightBoxX + 3;
  const valueX = rightBoxX + 35;
  let rightY = boxStartY + 8;
  const lineHeight = 7;

  // PO Number
  doc.setFont('helvetica', 'bold');
  doc.text('P.O. No.:', labelX, rightY);
  doc.setFont('helvetica', 'normal');
  doc.text(purchaseOrder.po_number || 'P2024-XXXX', valueX, rightY);
  rightY += lineHeight;

  // Order Date
  doc.setFont('helvetica', 'bold');
  doc.text('Order Date:', labelX, rightY);
  doc.setFont('helvetica', 'normal');
  doc.text(purchaseOrder.po_date ? format(new Date(purchaseOrder.po_date), PO_DATE_FORMAT) : '', valueX, rightY);
  rightY += lineHeight;

  // Term
  doc.setFont('helvetica', 'bold');
  doc.text('Term:', labelX, rightY);
  doc.setFont('helvetica', 'normal');
  doc.text(formatPaymentTerms(purchaseOrder.suppliers?.payment_terms), valueX, rightY);
  rightY += lineHeight;

  // Delivery Date
  doc.setFont('helvetica', 'bold');
  doc.text('Delivery Date:', labelX, rightY);
  doc.setFont('helvetica', 'normal');
  doc.text(purchaseOrder.expected_delivery_date ? format(new Date(purchaseOrder.expected_delivery_date), PO_DATE_FORMAT) : '-', valueX, rightY);
  rightY += lineHeight;

  // Draw a horizontal line to separate from Dept/Committee
  doc.line(rightBoxX, boxStartY + 35, rightBoxX + rightBoxWidth, boxStartY + 35);

  // Dept/Committee
  rightY = boxStartY + 42;
  doc.setFont('helvetica', 'bold');
  doc.text('Dept/Committee:', labelX, rightY);
  doc.setFont('helvetica', 'normal');
  doc.text(purchaseOrder.department?.name || purchaseOrder.department_code || '-', labelX + 35, rightY);

  currentY = boxStartY + boxHeight + 10;

  // Line Items Table
  checkPageBreak(50);

  // Format line items data
  const tableData: string[][] = [];

  if (purchaseOrder.items && purchaseOrder.items.length > 0) {
    purchaseOrder.items.forEach((item) => {
      tableData.push([
        formatLineNumber(item.line_number),
        item.description,
        item.quantity.toString(),
        formatTableCurrency(item.unit_price),
        formatTableCurrency(item.line_total)
      ]);
    });
  } else {
    // Add empty rows to show the table structure
    for (let i = 1; i <= 12; i++) {
      tableData.push(['', '', '', '', '']);
    }
  }

  // Add the table
  autoTable(doc, {
    startY: currentY,
    head: [['Item', 'Description', 'Qty', 'Unit Price', 'Total Amt (S$)']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [255, 255, 255],
      textColor: [0, 0, 0],
      lineColor: [0, 0, 0],
      lineWidth: 0.5,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fillColor: [255, 255, 255],
      textColor: [0, 0, 0],
      lineColor: [0, 0, 0],
      lineWidth: 0.5
    },
    columnStyles: {
      0: { cellWidth: 15 }, // Item
      1: { cellWidth: 90 }, // Description
      2: { cellWidth: 20, halign: 'center' }, // Qty
      3: { cellWidth: 30, halign: 'right' }, // Unit Price
      4: { cellWidth: 35, halign: 'right' } // Total Amt
    },
    margin: { left: 10, right: 10 },
    didDrawPage: (data) => {
      if (data.cursor) {
        currentY = data.cursor.y;
      }
    }
  });

  // Remarks section
  currentY += 5;
  checkPageBreak(40);

  doc.setFont('helvetica', 'normal');
  doc.text('Remarks (if any):', 10, currentY);
  currentY += 5;

  // Draw remarks box
  doc.rect(10, currentY, 190, 20);

  // Add remarks text if available
  if (purchaseOrder.description) {
    const remarksLines = doc.splitTextToSize(purchaseOrder.description, 185);
    let remarksY = currentY + 5;
    remarksLines.forEach((line: string, index: number) => {
      if (index < 3 && remarksY < currentY + 18) { // Limit to 3 lines
        doc.text(line, 12, remarksY);
        remarksY += 5;
      }
    });
  }

  currentY += 25;

  // Totals section
  checkPageBreak(30);

  const totalsX = 140;
  const totalsValueX = 165;

  // Calculate GST
  const gstAmount = calculateGST(purchaseOrder.subtotal);

  // Amount Before GST
  doc.setFont('helvetica', 'bold');
  doc.text('Amount Before GST', totalsX, currentY, { align: 'right' });
  doc.text('S$', totalsValueX, currentY);
  doc.text(formatTableCurrency(purchaseOrder.subtotal), 195, currentY, { align: 'right' });
  currentY += 7;

  // GST
  doc.text(formatGSTLabel(), totalsX, currentY, { align: 'right' });
  doc.text('S$', totalsValueX, currentY);
  doc.text(formatTableCurrency(gstAmount), 195, currentY, { align: 'right' });
  currentY += 7;

  // Total Amount After GST
  doc.text('Total Amount After GST', totalsX, currentY, { align: 'right' });
  doc.text('S$', totalsValueX, currentY);
  doc.text(formatTableCurrency(purchaseOrder.total_amount), 195, currentY, { align: 'right' });

  // Save the PDF
  const fileName = `PO_${purchaseOrder.po_number}_${format(new Date(), 'yyyy-MM-dd')}.pdf`;
  doc.save(fileName);
}