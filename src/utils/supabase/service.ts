import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase Client with URL and Service Role Key
const supabaseUrl: string = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const serviceRoleKey: string = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabaseClient: SupabaseClient = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false, // Disable auto-refresh token
    persistSession: false,    // Disable session persistence
  },
});

// Access the Auth Admin API
const adminAuthClient = supabaseClient.auth.admin;

// Exporting the clients for use in other modules
export { supabaseClient, adminAuthClient };