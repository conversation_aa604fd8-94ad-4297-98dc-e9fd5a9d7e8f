/**
 * Helper functions and constants for Purchase Order operations
 */

/**
 * Format currency value with S$ prefix
 * @param value - The numeric value to format
 * @param includePrefix - Whether to include the S$ prefix (default: true)
 * @returns Formatted currency string
 */
export function formatCurrency(value: number, includePrefix: boolean = true): string {
  const formatted = value.toFixed(2);
  return includePrefix ? `S$ ${formatted}` : formatted;
}

/**
 * Format currency value for display in tables (without prefix)
 * @param value - The numeric value to format
 * @returns Formatted currency string without prefix
 */
export function formatTableCurrency(value: number): string {
  return value.toFixed(2);
}

/**
 * Format line number as XXXX (4-digit padded)
 * @param lineNumber - The line number to format
 * @returns Formatted line number string
 */
export function formatLineNumber(lineNumber: number): string {
  return lineNumber.toString().padStart(4, '0');
}

/**
 * Standard date format for Purchase Orders
 */
export const PO_DATE_FORMAT = 'dd/MM/yyyy';

/**
 * Format payment terms
 * @param days - Number of days for payment terms
 * @returns Formatted payment terms string
 */
export function formatPaymentTerms(days: number | undefined | null): string {
  if (!days) return '-';
  return `${days} days`;
}

/**
 * Format address for display
 * @param address - Address object
 * @returns Array of address lines
 */
export function formatAddressLines(address: {
  street_address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
} | null | undefined): string[] {
  if (!address) return [];
  
  const lines: string[] = [];
  
  if (address.street_address) {
    lines.push(address.street_address);
  }
  
  const cityPostal = [address.city, address.postal_code]
    .filter(Boolean)
    .join(' ')
    .trim();
  
  if (cityPostal) {
    lines.push(cityPostal);
  }
  
  if (address.country) {
    lines.push(address.country);
  }
  
  return lines;
}

/**
 * Calculate GST amount (9% in Singapore)
 */
export const GST_RATE = 0.09;

export function calculateGST(subtotal: number): number {
  return subtotal * GST_RATE;
}

/**
 * Format GST percentage for display
 */
export function formatGSTLabel(): string {
  return `GST - ${(GST_RATE * 100).toFixed(0)}%`;
}