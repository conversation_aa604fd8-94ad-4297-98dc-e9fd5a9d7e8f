"use client";

import { useState, useEffe<PERSON> } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { ReportExportControls } from "./report-export-controls";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  BarC<PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart,
  PieChart,
  Pie,
  Cell
} from "recharts";
import { 
  Calendar, 
  Clock, 
  Users, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  XCircle,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "lucide-react";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";

interface DailyAttendanceReportProps {
  programmeId?: string;
}

interface DailyAttendanceData {
  date: string;
  totalSessions: number;
  expectedAttendance: number;
  actualAttendance: number;
  attendanceRate: number;
  lateArrivals: number;
  earlyDepartures: number;
  noShows: number;
  peakCheckInTime: string;
  averageSessionDuration: number;
}

interface SessionDetails {
  sessionId: string;
  sessionName: string;
  sessionType: string;
  startTime: string;
  endTime: string;
  expectedParticipants: number;
  actualAttendance: number;
  attendanceRate: number;
  lateCount: number;
  noShowCount: number;
}

// Mock data until services are ready
const mockDailyData: DailyAttendanceData[] = [
  {
    date: "2024-01-01",
    totalSessions: 4,
    expectedAttendance: 320,
    actualAttendance: 276,
    attendanceRate: 86.3,
    lateArrivals: 24,
    earlyDepartures: 12,
    noShows: 44,
    peakCheckInTime: "09:15",
    averageSessionDuration: 90
  },
  {
    date: "2024-01-02",
    totalSessions: 5,
    expectedAttendance: 400,
    actualAttendance: 358,
    attendanceRate: 89.5,
    lateArrivals: 18,
    earlyDepartures: 8,
    noShows: 42,
    peakCheckInTime: "09:30",
    averageSessionDuration: 85
  },
  {
    date: "2024-01-03",
    totalSessions: 3,
    expectedAttendance: 240,
    actualAttendance: 195,
    attendanceRate: 81.3,
    lateArrivals: 32,
    earlyDepartures: 15,
    noShows: 45,
    peakCheckInTime: "10:00",
    averageSessionDuration: 95
  }
];

const mockSessionData: SessionDetails[] = [
  {
    sessionId: "1",
    sessionName: "Opening Keynote",
    sessionType: "keynote",
    startTime: "09:00",
    endTime: "10:30",
    expectedParticipants: 100,
    actualAttendance: 95,
    attendanceRate: 95.0,
    lateCount: 3,
    noShowCount: 5
  },
  {
    sessionId: "2",
    sessionName: "Workshop: Digital Innovation",
    sessionType: "workshop",
    startTime: "11:00",
    endTime: "12:30",
    expectedParticipants: 50,
    actualAttendance: 42,
    attendanceRate: 84.0,
    lateCount: 5,
    noShowCount: 8
  },
  {
    sessionId: "3",
    sessionName: "Panel Discussion",
    sessionType: "panel",
    startTime: "14:00",
    endTime: "15:30",
    expectedParticipants: 80,
    actualAttendance: 72,
    attendanceRate: 90.0,
    lateCount: 2,
    noShowCount: 8
  }
];

const mockHourlyData = [
  { hour: "08:00", checkIns: 12, checkOuts: 0 },
  { hour: "09:00", checkIns: 45, checkOuts: 2 },
  { hour: "10:00", checkIns: 28, checkOuts: 8 },
  { hour: "11:00", checkIns: 38, checkOuts: 15 },
  { hour: "12:00", checkIns: 15, checkOuts: 42 },
  { hour: "13:00", checkIns: 8, checkOuts: 5 },
  { hour: "14:00", checkIns: 35, checkOuts: 3 },
  { hour: "15:00", checkIns: 12, checkOuts: 28 },
  { hour: "16:00", checkIns: 5, checkOuts: 35 },
  { hour: "17:00", checkIns: 2, checkOuts: 48 }
];

export function DailyAttendanceReport({ programmeId }: DailyAttendanceReportProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [viewMode, setViewMode] = useState<'daily' | 'range'>('daily');
  const [chartType, setChartType] = useState<'overview' | 'hourly' | 'sessions'>('overview');
  const [dailyData, setDailyData] = useState<DailyAttendanceData[]>(mockDailyData);
  const [sessionData, setSessionData] = useState<SessionDetails[]>(mockSessionData);
  const [loading, setLoading] = useState(false);

  const currentDayData = dailyData.find(d => d.date === format(selectedDate, 'yyyy-MM-dd')) || mockDailyData[0];

  const fetchDailyData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setDailyData(mockDailyData);
      setSessionData(mockSessionData);
    } catch (error) {
      console.error('Failed to fetch daily attendance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'csv' | 'excel') => {
    // TODO: Implement export functionality
    console.log(`Exporting daily attendance report as ${format}`);
  };

  useEffect(() => {
    fetchDailyData();
  }, [programmeId, selectedDate, dateRange]);

  const getSessionTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      keynote: "#3b82f6",
      workshop: "#10b981",
      panel: "#f59e0b",
      networking: "#8b5cf6",
      regular: "#6b7280"
    };
    return colors[type] || colors.regular;
  };

  const getAttendanceRateColor = (rate: number) => {
    if (rate >= 90) return "text-green-600";
    if (rate >= 80) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      {/* Date Selection and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Daily Attendance Report</span>
          </CardTitle>
          <CardDescription>
            Detailed daily breakdown of attendance patterns and session performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="View mode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Single Day</SelectItem>
                  <SelectItem value="range">Date Range</SelectItem>
                </SelectContent>
              </Select>

              {viewMode === 'daily' ? (
                <div className="flex items-center space-x-2">
                  <Input
                    type="date"
                    value={format(selectedDate, 'yyyy-MM-dd')}
                    onChange={(e) => setSelectedDate(new Date(e.target.value))}
                    className="w-[160px]"
                  />
                </div>
              ) : (
                <DatePickerWithRange 
                  value={dateRange}
                  onChange={setDateRange}
                  placeholder="Select date range"
                />
              )}

              <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Chart view" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Overview</SelectItem>
                  <SelectItem value="hourly">Hourly Patterns</SelectItem>
                  <SelectItem value="sessions">Session Details</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                onClick={fetchDailyData}
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Refresh'}
              </Button>
            </div>

            <ReportExportControls
              onExport={handleExport}
              reportType="daily-attendance"
              data={viewMode === 'daily' ? [currentDayData] : dailyData}
            />
          </div>
        </CardContent>
      </Card>

      {/* Daily Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Expected</p>
                <p className="text-2xl font-bold">{currentDayData.expectedAttendance}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Actual</p>
                <p className="text-2xl font-bold">{currentDayData.actualAttendance}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-50 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Late Arrivals</p>
                <p className="text-2xl font-bold">{currentDayData.lateArrivals}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-50 rounded-lg">
                <XCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">No Shows</p>
                <p className="text-2xl font-bold">{currentDayData.noShows}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Main Chart */}
        <Card>
          <CardHeader>
            <CardTitle>
              {chartType === 'overview' && 'Daily Attendance Overview'}
              {chartType === 'hourly' && 'Hourly Check-in/Check-out Pattern'}
              {chartType === 'sessions' && 'Session-wise Attendance'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {chartType === 'overview' ? (
                <BarChart data={viewMode === 'daily' ? [currentDayData] : dailyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="expectedAttendance" fill="#e5e7eb" name="Expected" />
                  <Bar dataKey="actualAttendance" fill="#3b82f6" name="Actual" />
                </BarChart>
              ) : chartType === 'hourly' ? (
                <AreaChart data={mockHourlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="checkIns"
                    stackId="1"
                    stroke="#10b981"
                    fill="#10b981"
                    fillOpacity={0.6}
                    name="Check-ins"
                  />
                  <Area
                    type="monotone"
                    dataKey="checkOuts"
                    stackId="2"
                    stroke="#f59e0b"
                    fill="#f59e0b"
                    fillOpacity={0.6}
                    name="Check-outs"
                  />
                </AreaChart>
              ) : (
                <BarChart data={sessionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="sessionName" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="expectedParticipants" fill="#e5e7eb" name="Expected" />
                  <Bar dataKey="actualAttendance" fill="#8b5cf6" name="Actual" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Additional Analytics */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance Rate Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={dailyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="attendanceRate"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  dot={{ fill: "#3b82f6", strokeWidth: 2, r: 6 }}
                  name="Attendance Rate (%)"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Session Details Table */}
      {chartType === 'sessions' && (
        <Card>
          <CardHeader>
            <CardTitle>Session Details</CardTitle>
            <CardDescription>
              Detailed breakdown of attendance for each session
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Session</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Expected</TableHead>
                    <TableHead>Actual</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Late</TableHead>
                    <TableHead>No Show</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sessionData.map((session) => (
                    <TableRow key={session.sessionId}>
                      <TableCell className="font-medium">
                        {session.sessionName}
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant="outline"
                          style={{ color: getSessionTypeColor(session.sessionType) }}
                        >
                          {session.sessionType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {session.startTime} - {session.endTime}
                      </TableCell>
                      <TableCell>{session.expectedParticipants}</TableCell>
                      <TableCell>{session.actualAttendance}</TableCell>
                      <TableCell>
                        <span className={getAttendanceRateColor(session.attendanceRate)}>
                          {session.attendanceRate.toFixed(1)}%
                        </span>
                      </TableCell>
                      <TableCell>{session.lateCount}</TableCell>
                      <TableCell>{session.noShowCount}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Key Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Peak Check-in Time</span>
              </div>
              <p className="text-2xl font-bold">{currentDayData.peakCheckInTime}</p>
              <p className="text-sm text-muted-foreground">Most popular arrival time</p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <BarChart3 className="h-4 w-4 text-green-600" />
                <span className="font-medium">Attendance Rate</span>
              </div>
              <p className="text-2xl font-bold">{currentDayData.attendanceRate.toFixed(1)}%</p>
              <div className="flex items-center space-x-1 text-sm">
                {currentDayData.attendanceRate > 85 ? (
                  <ArrowUp className="h-3 w-3 text-green-600" />
                ) : (
                  <ArrowDown className="h-3 w-3 text-red-600" />
                )}
                <span className="text-muted-foreground">vs. 85% target</span>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <span className="font-medium">Late Rate</span>
              </div>
              <p className="text-2xl font-bold">
                {((currentDayData.lateArrivals / currentDayData.actualAttendance) * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-muted-foreground">
                {currentDayData.lateArrivals} of {currentDayData.actualAttendance} attendees
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}