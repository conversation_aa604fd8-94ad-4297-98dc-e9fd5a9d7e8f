"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { DataTablePagination } from "@/components/ui/data-table-pagination";
import { ReportExportControls } from "./report-export-controls";
import { 
  Award, 
  Download, 
  Mail, 
  FileText, 
  CheckCircle, 
  XCir<PERSON>,
  Clock,
  Users
} from "lucide-react";
// TODO: Use real AttendanceReport types when available
interface AttendanceReport {
  participant_id: string;
  participant_name: string;
  email: string;
  programme_name: string;
  programme_type: string;
  registration_type: string;
  registration_status: string;
  attendance_percentage: number;
  attended_sessions: number;
  total_sessions: number;
  total_duration_minutes: number;
  completion_status: string;
  certificate_eligible: boolean;
  certificate_issued: boolean;
  certificate_number: string | null;
}

interface CertificateEligibilityReportProps {
  programmeId?: string;
}

// Mock data until services are ready
const mockCertificateData: AttendanceReport[] = [
  {
    participant_id: "1",
    participant_name: "John Smith",
    email: "<EMAIL>",
    programme_name: "Digital Leadership Program",
    programme_type: "COURSE",
    registration_type: "REGULAR",
    registration_status: "CONFIRMED",
    attendance_percentage: 92.5,
    attended_sessions: 18,
    total_sessions: 20,
    total_duration_minutes: 1080,
    completion_status: "completed",
    certificate_eligible: true,
    certificate_issued: false,
    certificate_number: null
  },
  {
    participant_id: "2",
    participant_name: "Sarah Johnson",
    email: "<EMAIL>",
    programme_name: "Digital Leadership Program",
    programme_type: "COURSE",
    registration_type: "REGULAR",
    registration_status: "CONFIRMED",
    attendance_percentage: 75.0,
    attended_sessions: 15,
    total_sessions: 20,
    total_duration_minutes: 900,
    completion_status: "partially_completed",
    certificate_eligible: false,
    certificate_issued: false,
    certificate_number: null
  },
  {
    participant_id: "3",
    participant_name: "Michael Brown",
    email: "<EMAIL>",
    programme_name: "Digital Leadership Program",
    programme_type: "COURSE",
    registration_type: "REGULAR",
    registration_status: "CONFIRMED",
    attendance_percentage: 88.0,
    attended_sessions: 17,
    total_sessions: 20,
    total_duration_minutes: 1020,
    completion_status: "completed",
    certificate_eligible: true,
    certificate_issued: true,
    certificate_number: "CERT-2024-001"
  }
];

export function CertificateEligibilityReport({ programmeId }: CertificateEligibilityReportProps) {
  const [data, setData] = useState<AttendanceReport[]>(mockCertificateData);
  const [loading, setLoading] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [eligibilityFilter, setEligibilityFilter] = useState<'all' | 'eligible' | 'not-eligible' | 'issued'>('all');

  const columns: ColumnDef<AttendanceReport>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "participant_name",
      header: "Participant",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue("participant_name")}</div>
          <div className="text-sm text-muted-foreground">{row.original.email}</div>
        </div>
      ),
    },
    {
      accessorKey: "attendance_percentage",
      header: "Attendance %",
      cell: ({ row }) => {
        const percentage = row.getValue("attendance_percentage") as number;
        const isEligible = percentage >= 80; // Assuming 80% is the threshold
        return (
          <div className="flex items-center space-x-2">
            <span className={`font-medium ${isEligible ? 'text-green-600' : 'text-red-600'}`}>
              {percentage.toFixed(1)}%
            </span>
            {isEligible && <CheckCircle className="h-4 w-4 text-green-600" />}
          </div>
        );
      },
    },
    {
      accessorKey: "attended_sessions",
      header: "Sessions",
      cell: ({ row }) => (
        <span>
          {row.getValue("attended_sessions")} / {row.original.total_sessions}
        </span>
      ),
    },
    {
      accessorKey: "completion_status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("completion_status") as string;
        const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
          completed: "default",
          partially_completed: "secondary",
          incomplete: "outline",
          withdrawn: "destructive"
        };
        return (
          <Badge variant={variants[status] || "outline"}>
            {status?.replace('_', ' ').toUpperCase()}
          </Badge>
        );
      },
    },
    {
      accessorKey: "certificate_eligible",
      header: "Eligible",
      cell: ({ row }) => {
        const eligible = row.getValue("certificate_eligible") as boolean;
        return (
          <div className="flex items-center">
            {eligible ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Eligible
              </Badge>
            ) : (
              <Badge variant="secondary">
                <XCircle className="h-3 w-3 mr-1" />
                Not Eligible
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "certificate_issued",
      header: "Certificate",
      cell: ({ row }) => {
        const issued = row.getValue("certificate_issued") as boolean;
        const certificateNumber = row.original.certificate_number;
        
        if (issued && certificateNumber) {
          return (
            <div>
              <Badge variant="default" className="bg-blue-100 text-blue-800">
                <Award className="h-3 w-3 mr-1" />
                Issued
              </Badge>
              <div className="text-xs text-muted-foreground mt-1">
                {certificateNumber}
              </div>
            </div>
          );
        } else if (row.original.certificate_eligible) {
          return (
            <Badge variant="outline">
              <Clock className="h-3 w-3 mr-1" />
              Pending
            </Badge>
          );
        } else {
          return (
            <Badge variant="secondary">
              N/A
            </Badge>
          );
        }
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const participant = row.original;
        const eligible = participant.certificate_eligible;
        const issued = participant.certificate_issued;

        return (
          <div className="flex items-center space-x-2">
            {eligible && !issued && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleGenerateCertificate(participant.participant_id)}
              >
                <Award className="h-4 w-4 mr-1" />
                Generate
              </Button>
            )}
            {issued && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadCertificate(participant.certificate_number!)}
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSendEmail(participant.email)}
            >
              <Mail className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const filteredData = data.filter((item) => {
    switch (eligibilityFilter) {
      case 'eligible':
        return item.certificate_eligible && !item.certificate_issued;
      case 'not-eligible':
        return !item.certificate_eligible;
      case 'issued':
        return item.certificate_issued;
      default:
        return true;
    }
  });

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const fetchCertificateData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setData(mockCertificateData);
    } catch (error) {
      console.error('Failed to fetch certificate data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateCertificate = async (participantId: string) => {
    // TODO: Implement certificate generation
    console.log('Generating certificate for participant:', participantId);
  };

  const handleDownloadCertificate = async (certificateNumber: string) => {
    // TODO: Implement certificate download
    console.log('Downloading certificate:', certificateNumber);
  };

  const handleSendEmail = async (email: string) => {
    // TODO: Implement email functionality
    console.log('Sending email to:', email);
  };

  const handleBulkGenerate = async () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const eligibleParticipants = selectedRows
      .map(row => row.original)
      .filter(participant => participant.certificate_eligible && !participant.certificate_issued);
    
    // TODO: Implement bulk certificate generation
    console.log('Bulk generating certificates for:', eligibleParticipants.length, 'participants');
  };

  const handleExport = async (format: 'pdf' | 'csv' | 'excel') => {
    // TODO: Implement export functionality
    console.log(`Exporting certificate eligibility report as ${format}`);
  };

  useEffect(() => {
    fetchCertificateData();
  }, [programmeId]);

  const stats = {
    total: data.length,
    eligible: data.filter(p => p.certificate_eligible).length,
    issued: data.filter(p => p.certificate_issued).length,
    pending: data.filter(p => p.certificate_eligible && !p.certificate_issued).length
  };

  const selectedCount = table.getSelectedRowModel().rows.length;
  const selectedEligibleCount = table.getSelectedRowModel().rows
    .filter(row => row.original.certificate_eligible && !row.original.certificate_issued).length;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Participants</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Eligible</p>
                <p className="text-2xl font-bold">{stats.eligible}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-50 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-50 rounded-lg">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Issued</p>
                <p className="text-2xl font-bold">{stats.issued}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Certificate Eligibility Report</CardTitle>
          <CardDescription>
            Manage certificate eligibility and generation for programme participants
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Filter:</span>
                <div className="flex space-x-2">
                  <Button
                    variant={eligibilityFilter === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEligibilityFilter('all')}
                  >
                    All ({stats.total})
                  </Button>
                  <Button
                    variant={eligibilityFilter === 'eligible' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEligibilityFilter('eligible')}
                  >
                    Eligible ({stats.eligible})
                  </Button>
                  <Button
                    variant={eligibilityFilter === 'issued' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEligibilityFilter('issued')}
                  >
                    Issued ({stats.issued})
                  </Button>
                </div>
              </div>

              <Input
                placeholder="Filter participants..."
                value={(table.getColumn("participant_name")?.getFilterValue() as string) ?? ""}
                onChange={(event) =>
                  table.getColumn("participant_name")?.setFilterValue(event.target.value)
                }
                className="max-w-sm"
              />
            </div>

            <div className="flex items-center space-x-2">
              {selectedEligibleCount > 0 && (
                <Button
                  onClick={handleBulkGenerate}
                  disabled={loading}
                >
                  <Award className="h-4 w-4 mr-2" />
                  Generate {selectedEligibleCount} Certificates
                </Button>
              )}
              
              <ReportExportControls
                onExport={handleExport}
                reportType="certificate-eligibility"
                data={filteredData}
              />
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          <DataTablePagination table={table} />
        </CardContent>
      </Card>
    </div>
  );
}