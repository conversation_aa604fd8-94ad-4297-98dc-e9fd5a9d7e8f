"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Download, 
  FileText, 
  FileSpreadsheet,
  FileBarChart,
  Settings,
  Calendar,
  Filter
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ReportExportControlsProps {
  onExport: (format: 'pdf' | 'csv' | 'excel', options?: ExportOptions) => Promise<void>;
  reportType: string;
  data: any[];
  disabled?: boolean;
}

interface ExportOptions {
  includeCharts?: boolean;
  includeSummary?: boolean;
  includeDetails?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  customTitle?: string;
  customDescription?: string;
  selectedColumns?: string[];
  filterCriteria?: Record<string, any>;
}

const exportFormats = [
  {
    id: 'pdf',
    label: 'PDF Report',
    description: 'Professional report with charts and formatting',
    icon: FileText,
    color: 'text-red-600'
  },
  {
    id: 'excel',
    label: 'Excel Workbook',
    description: 'Spreadsheet with multiple sheets and formulas',
    icon: FileSpreadsheet,
    color: 'text-green-600'
  },
  {
    id: 'csv',
    label: 'CSV Data',
    description: 'Raw data for external analysis',
    icon: FileBarChart,
    color: 'text-blue-600'
  }
];

const defaultColumns = [
  { id: 'participant_name', label: 'Participant Name', checked: true },
  { id: 'email', label: 'Email', checked: true },
  { id: 'attendance_percentage', label: 'Attendance %', checked: true },
  { id: 'sessions_attended', label: 'Sessions Attended', checked: true },
  { id: 'completion_status', label: 'Completion Status', checked: true },
  { id: 'certificate_eligible', label: 'Certificate Eligible', checked: false },
  { id: 'last_attendance', label: 'Last Attendance', checked: false },
  { id: 'total_duration', label: 'Total Duration', checked: false }
];

export function ReportExportControls({ 
  onExport, 
  reportType, 
  data, 
  disabled = false 
}: ReportExportControlsProps) {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [showAdvancedDialog, setShowAdvancedDialog] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeCharts: true,
    includeSummary: true,
    includeDetails: true,
    customTitle: '',
    customDescription: '',
    selectedColumns: defaultColumns.filter(col => col.checked).map(col => col.id)
  });
  const [selectedColumns, setSelectedColumns] = useState(defaultColumns);

  const handleQuickExport = async (format: 'pdf' | 'csv' | 'excel') => {
    setIsExporting(true);
    try {
      await onExport(format, exportOptions);
      toast({
        title: "Export Started",
        description: `Your ${format.toUpperCase()} report is being generated and will download shortly.`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: "There was an error generating your report. Please try again.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleAdvancedExport = async (format: 'pdf' | 'csv' | 'excel') => {
    setIsExporting(true);
    try {
      const options: ExportOptions = {
        ...exportOptions,
        selectedColumns: selectedColumns.filter(col => col.checked).map(col => col.id)
      };
      
      await onExport(format, options);
      setShowAdvancedDialog(false);
      
      toast({
        title: "Export Started",
        description: `Your customized ${format.toUpperCase()} report is being generated.`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: "There was an error generating your report. Please try again.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleColumnToggle = (columnId: string, checked: boolean) => {
    setSelectedColumns(prev => 
      prev.map(col => 
        col.id === columnId ? { ...col, checked } : col
      )
    );
  };

  const getReportTitle = () => {
    const titles: Record<string, string> = {
      'attendance-summary': 'Attendance Summary Report',
      'certificate-eligibility': 'Certificate Eligibility Report',
      'daily-attendance': 'Daily Attendance Report'
    };
    return titles[reportType] || 'Attendance Report';
  };

  return (
    <div className="flex items-center space-x-2">
      {/* Quick Export Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" disabled={disabled || isExporting}>
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Quick Export</span>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {exportFormats.map((format) => {
            const IconComponent = format.icon;
            return (
              <DropdownMenuItem
                key={format.id}
                onClick={() => handleQuickExport(format.id as any)}
                disabled={isExporting}
              >
                <div className="flex items-center space-x-3 w-full">
                  <IconComponent className={`h-4 w-4 ${format.color}`} />
                  <div className="flex-1">
                    <div className="font-medium">{format.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {format.description}
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            );
          })}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setShowAdvancedDialog(true)}>
            <Settings className="h-4 w-4 mr-2" />
            Advanced Options
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Data Count Badge */}
      <Badge variant="secondary">
        {data.length} {data.length === 1 ? 'record' : 'records'}
      </Badge>

      {/* Advanced Export Dialog */}
      <Dialog open={showAdvancedDialog} onOpenChange={setShowAdvancedDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Advanced Export Options</span>
            </DialogTitle>
            <DialogDescription>
              Customize your {getReportTitle().toLowerCase()} export with advanced options
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Report Content Options */}
            <div>
              <Label className="text-base font-medium">Report Content</Label>
              <div className="space-y-3 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportOptions.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportOptions(prev => ({ ...prev, includeSummary: !!checked }))
                    }
                  />
                  <Label htmlFor="includeSummary">Include summary statistics</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeCharts"
                    checked={exportOptions.includeCharts}
                    onCheckedChange={(checked) =>
                      setExportOptions(prev => ({ ...prev, includeCharts: !!checked }))
                    }
                  />
                  <Label htmlFor="includeCharts">Include charts and visualizations (PDF only)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeDetails"
                    checked={exportOptions.includeDetails}
                    onCheckedChange={(checked) =>
                      setExportOptions(prev => ({ ...prev, includeDetails: !!checked }))
                    }
                  />
                  <Label htmlFor="includeDetails">Include detailed participant data</Label>
                </div>
              </div>
            </div>

            {/* Custom Title and Description */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="customTitle">Custom Report Title</Label>
                <Input
                  id="customTitle"
                  placeholder={getReportTitle()}
                  value={exportOptions.customTitle}
                  onChange={(e) =>
                    setExportOptions(prev => ({ ...prev, customTitle: e.target.value }))
                  }
                />
              </div>
              <div>
                <Label htmlFor="customDescription">Report Description</Label>
                <Textarea
                  id="customDescription"
                  placeholder="Add a custom description for this report..."
                  value={exportOptions.customDescription}
                  onChange={(e) =>
                    setExportOptions(prev => ({ ...prev, customDescription: e.target.value }))
                  }
                  rows={2}
                />
              </div>
            </div>

            {/* Column Selection */}
            <div>
              <Label className="text-base font-medium">Data Columns</Label>
              <div className="grid grid-cols-2 gap-3 mt-2">
                {selectedColumns.map((column) => (
                  <div key={column.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={column.id}
                      checked={column.checked}
                      onCheckedChange={(checked) => handleColumnToggle(column.id, !!checked)}
                    />
                    <Label htmlFor={column.id} className="text-sm">
                      {column.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range Filter */}
            <div>
              <Label className="text-base font-medium">Date Range Filter</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label htmlFor="startDate" className="text-sm">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    onChange={(e) =>
                      setExportOptions(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, start: e.target.value, end: prev.dateRange?.end || '' }
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="endDate" className="text-sm">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    onChange={(e) =>
                      setExportOptions(prev => ({
                        ...prev,
                        dateRange: { start: prev.dateRange?.start || '', end: e.target.value }
                      }))
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <div className="flex justify-between w-full">
              <Button variant="outline" onClick={() => setShowAdvancedDialog(false)}>
                Cancel
              </Button>
              <div className="space-x-2">
                {exportFormats.map((format) => {
                  const IconComponent = format.icon;
                  return (
                    <Button
                      key={format.id}
                      onClick={() => handleAdvancedExport(format.id as any)}
                      disabled={isExporting}
                      variant={format.id === 'pdf' ? 'default' : 'outline'}
                    >
                      <IconComponent className="h-4 w-4 mr-2" />
                      {format.id.toUpperCase()}
                    </Button>
                  );
                })}
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}