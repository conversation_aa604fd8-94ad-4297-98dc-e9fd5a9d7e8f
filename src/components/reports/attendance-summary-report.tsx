"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { ReportExportControls } from "./report-export-controls";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { Download, BarChart3, TrendingUp, Calendar } from "lucide-react";
// TODO: Use real AttendanceReport types when available
import { DateRange } from "react-day-picker";

interface AttendanceSummaryReportProps {
  programmeId?: string;
}

// Mock data until services are ready
const mockAttendanceData = [
  { name: "Week 1", attendanceRate: 92, participants: 85, sessions: 5 },
  { name: "Week 2", attendanceRate: 88, participants: 82, sessions: 5 },
  { name: "Week 3", attendanceRate: 85, participants: 79, sessions: 5 },
  { name: "Week 4", attendanceRate: 90, participants: 83, sessions: 5 },
  { name: "Week 5", attendanceRate: 87, participants: 81, sessions: 5 },
  { name: "Week 6", attendanceRate: 93, participants: 86, sessions: 5 }
];

const mockStatusDistribution = [
  { name: "Present", value: 78, color: "#22c55e" },
  { name: "Late", value: 12, color: "#f59e0b" },
  { name: "Absent", value: 8, color: "#ef4444" },
  { name: "Excused", value: 2, color: "#6b7280" }
];

const mockSessionAttendance = [
  { session: "Opening Ceremony", attendance: 95, capacity: 100 },
  { session: "Keynote Session", attendance: 92, capacity: 100 },
  { session: "Workshop A", attendance: 78, capacity: 85 },
  { session: "Workshop B", attendance: 82, capacity: 85 },
  { session: "Panel Discussion", attendance: 88, capacity: 100 },
  { session: "Networking", attendance: 75, capacity: 100 },
  { session: "Closing Ceremony", attendance: 85, capacity: 100 }
];

const mockTrendData = [
  { date: "2024-01-01", checkIns: 45, checkOuts: 42 },
  { date: "2024-01-02", checkIns: 52, checkOuts: 48 },
  { date: "2024-01-03", checkIns: 48, checkOuts: 45 },
  { date: "2024-01-04", checkIns: 55, checkOuts: 52 },
  { date: "2024-01-05", checkIns: 58, checkOuts: 55 },
  { date: "2024-01-06", checkIns: 42, checkOuts: 40 },
  { date: "2024-01-07", checkIns: 38, checkOuts: 35 }
];

export function AttendanceSummaryReport({ programmeId }: AttendanceSummaryReportProps) {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [viewType, setViewType] = useState<'overview' | 'detailed' | 'trends'>('overview');
  const [exportFormat, setExportFormat] = useState<'pdf' | 'csv' | 'excel'>('pdf');

  // TODO: Replace with real API call when services are ready
  const fetchAttendanceData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Set mock data
    } catch (error) {
      console.error('Failed to fetch attendance data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAttendanceData();
  }, [programmeId, dateRange]);

  const handleExport = async (format: 'pdf' | 'csv' | 'excel') => {
    // TODO: Implement actual export functionality
    console.log(`Exporting attendance summary as ${format}`);
  };

  return (
    <div className="space-y-6">
      {/* Filters and Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <Select value={viewType} onValueChange={(value: any) => setViewType(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="overview">Overview</SelectItem>
              <SelectItem value="detailed">Detailed Analysis</SelectItem>
              <SelectItem value="trends">Trend Analysis</SelectItem>
            </SelectContent>
          </Select>

          <DatePickerWithRange 
            value={dateRange}
            onChange={setDateRange}
            placeholder="Select date range"
          />

          <Button 
            variant="outline" 
            onClick={fetchAttendanceData}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>

        <ReportExportControls
          onExport={handleExport}
          reportType="attendance-summary"
          data={mockAttendanceData}
        />
      </div>

      {/* Overview View */}
      {viewType === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Weekly Attendance Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Weekly Attendance Rates</span>
              </CardTitle>
              <CardDescription>
                Attendance percentage over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={mockAttendanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar 
                    dataKey="attendanceRate" 
                    fill="#3b82f6" 
                    name="Attendance Rate (%)"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Attendance Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Attendance Status Distribution</CardTitle>
              <CardDescription>
                Current distribution of attendance statuses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={mockStatusDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {mockStatusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Analysis View */}
      {viewType === 'detailed' && (
        <div className="space-y-6">
          {/* Session-wise Attendance */}
          <Card>
            <CardHeader>
              <CardTitle>Session-wise Attendance Analysis</CardTitle>
              <CardDescription>
                Detailed breakdown of attendance by session
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={mockSessionAttendance} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="session" type="category" width={120} />
                  <Tooltip />
                  <Legend />
                  <Bar 
                    dataKey="attendance" 
                    fill="#10b981" 
                    name="Attendance"
                    radius={[0, 4, 4, 0]}
                  />
                  <Bar 
                    dataKey="capacity" 
                    fill="#e5e7eb" 
                    name="Capacity"
                    radius={[0, 4, 4, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Attendance Metrics Table */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">85.5%</div>
                  <div className="text-sm text-muted-foreground">Average Attendance</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">247</div>
                  <div className="text-sm text-muted-foreground">Total Sessions</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">5.2 hrs</div>
                  <div className="text-sm text-muted-foreground">Avg Session Duration</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">12%</div>
                  <div className="text-sm text-muted-foreground">Late Arrivals</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Trends Analysis View */}
      {viewType === 'trends' && (
        <div className="space-y-6">
          {/* Check-in/Check-out Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Daily Check-in/Check-out Trends</span>
              </CardTitle>
              <CardDescription>
                Daily patterns of participant check-ins and check-outs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={mockTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="checkIns"
                    stackId="1"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.6}
                    name="Check-ins"
                  />
                  <Area
                    type="monotone"
                    dataKey="checkOuts"
                    stackId="2"
                    stroke="#10b981"
                    fill="#10b981"
                    fillOpacity={0.6}
                    name="Check-outs"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Attendance Rate Trend */}
          <Card>
            <CardHeader>
              <CardTitle>Attendance Rate Trend</CardTitle>
              <CardDescription>
                Trend analysis of attendance rates over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={mockAttendanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="attendanceRate"
                    stroke="#8b5cf6"
                    strokeWidth={3}
                    dot={{ fill: "#8b5cf6", strokeWidth: 2, r: 6 }}
                    name="Attendance Rate (%)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}