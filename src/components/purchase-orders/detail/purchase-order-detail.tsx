"use client";

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import {
  POStatusBadge,
  CurrencyDisplay,
  ApprovalStatus,
  DocumentPreview,
  PurchaseOrderPDFButton
} from "@/components/purchase-orders/ui";
import {
  Edit,
  Printer,
  Mail,
  Package,
  Copy,
  X,
  CalendarDays,
  Building,
  User,
  MapPin,
  Banknote
} from "lucide-react";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";
import { format } from "date-fns";

interface PurchaseOrderDetailProps {
  purchaseOrder: PurchaseOrder;
  onEdit?: () => void;
  onPrint?: () => void;
  onEmail?: () => void;
  onCreateGoodsReceipt?: () => void;
  onCancel?: () => void;
  onClone?: () => void;
}

export function PurchaseOrderDetail({
  purchaseOrder,
  onEdit,
  onPrint,
  onEmail,
  onCreateGoodsReceipt,
  onCancel,
  onClone
}: PurchaseOrderDetailProps) {
  const [activeTab, setActiveTab] = useState("details");

  const canEdit = purchaseOrder.status === "DRAFT";
  const canCreateGoodsReceipt = ["APPROVED", "SENT", "PARTIALLY_RECEIVED"].includes(purchaseOrder.status);
  const canCancel = !["CANCELLED", "CLOSED", "RECEIVED"].includes(purchaseOrder.status);

  // Mock data for demonstration
  const mockApprovalSteps = [
    {
      id: "1",
      name: "Department Approval",
      status: "approved" as const,
      approver: "John Smith",
      approvedAt: "2024-01-15T10:30:00Z",
      comments: "Approved for procurement"
    },
    {
      id: "2",
      name: "Finance Approval",
      status: "current" as const,
      approver: "Jane Doe",
      comments: "Under review"
    }
  ];

  const mockDocuments = [
    {
      id: "1",
      name: "Quotation.pdf",
      size: 245760,
      type: "application/pdf",
      url: "/mock-document.pdf",
      uploadedAt: "2024-01-15T10:30:00Z",
      uploadedBy: "John Smith"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-4">
                <h2 className="text-2xl font-bold">{purchaseOrder.po_number}</h2>
                <POStatusBadge status={purchaseOrder.status} />
              </div>
              <p className="text-muted-foreground">{purchaseOrder.title}</p>
            </div>

            <div className="flex flex-wrap gap-2">
              {canEdit && (
                <Button variant="outline" size="sm" onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              )}
              
              <PurchaseOrderPDFButton purchaseOrder={purchaseOrder} />
              
              {/* Temporarily hidden - Print/Email functionality to be implemented
              <Button variant="outline" size="sm" onClick={onPrint}>
                <Printer className="mr-2 h-4 w-4" />
                Print
              </Button> */}

              {/* <Button variant="outline" size="sm" onClick={onEmail}>
                <Mail className="mr-2 h-4 w-4" />
                Email
              </Button> */}

              {canCreateGoodsReceipt && (
                <Button variant="outline" size="sm" onClick={onCreateGoodsReceipt}>
                  <Package className="mr-2 h-4 w-4" />
                  Create GR
                </Button>
              )}

              <Button variant="outline" size="sm" onClick={onClone}>
                <Copy className="mr-2 h-4 w-4" />
                Clone
              </Button>

              {canCancel && (
                <Button variant="outline" size="sm" onClick={onCancel}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Summary Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center gap-3">
              <Building className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Supplier</p>
                <p className="font-medium">
                  {purchaseOrder.suppliers?.display_name || purchaseOrder.suppliers?.name || '-'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Banknote className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                <CurrencyDisplay
                  amount={purchaseOrder.total_amount}
                  currency={purchaseOrder.currency_code}
                  className="text-lg font-bold"
                />
              </div>
            </div>

            <div className="flex items-center gap-3">
              <CalendarDays className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">PO Date</p>
                <p className="font-medium">
                  {format(new Date(purchaseOrder.po_date), 'dd MMM yyyy')}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Created By</p>
                <p className="font-medium">
                  {purchaseOrder.created_by_name?.full_name || purchaseOrder.created_by_name?.email || "--"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Section */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="details">Order Details</TabsTrigger>
          <TabsTrigger value="items">Line Items</TabsTrigger>
          <TabsTrigger value="receipts">Goods Receipts</TabsTrigger>
          <TabsTrigger value="invoices">Related Invoices</TabsTrigger>
          <TabsTrigger value="approval">Approval History</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        {/* Order Details Tab */}
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Purchase Order Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div>
                <h4 className="font-semibold mb-4">Basic Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">PO Number</label>
                    <p className="font-medium">{purchaseOrder.po_number}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Entity</label>
                    <p className="font-medium">{purchaseOrder.entity?.name || '-'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Title</label>
                    <p className="font-medium">{purchaseOrder.title}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Department</label>
                    <p className="font-medium">{purchaseOrder.department?.name || purchaseOrder.department_code || '-'}</p>
                  </div>
                  {purchaseOrder.description && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-muted-foreground">Description</label>
                      <p className="font-medium">{purchaseOrder.description}</p>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Financial Information */}
              <div>
                <h4 className="font-semibold mb-4">Financial Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Currency</label>
                    <p className="font-medium">{purchaseOrder.currency_code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Exchange Rate</label>
                    <p className="font-medium">{purchaseOrder.exchange_rate}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Subtotal</label>
                    <p className="font-medium">
                      <CurrencyDisplay
                        amount={purchaseOrder.subtotal}
                        currency={purchaseOrder.currency_code}
                        showCurrency={false}
                      />
                    </p>
                  </div>
                </div>
              </div>

              {/* Delivery Information */}
              {purchaseOrder.delivery_address && (
                <>
                  <Separator />
                  <div>
                    <h4 className="font-semibold mb-4">Delivery Information</h4>
                    <div className="flex items-start gap-3">
                      <MapPin className="h-5 w-5 text-muted-foreground mt-1" />
                      <div>
                        <p className="font-medium">Delivery Address</p>
                        <div className="text-muted-foreground">
                          <p>{purchaseOrder.delivery_address.street_address}</p>
                          <p>{purchaseOrder.delivery_address.city}, {purchaseOrder.delivery_address.postal_code}</p>
                          <p>{purchaseOrder.delivery_address.country}</p>
                        </div>
                      </div>
                    </div>
                    {purchaseOrder.delivery_instructions && (
                      <div className="mt-4">
                        <label className="text-sm font-medium text-muted-foreground">Delivery Instructions</label>
                        <p className="font-medium">{purchaseOrder.delivery_instructions}</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Line Items Tab */}
        <TabsContent value="items">
          <Card>
            <CardHeader>
              <CardTitle>Line Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Line #</TableHead>
                      <TableHead>Item Code</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>UOM</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Tax Code</TableHead>
                      <TableHead>Tax Rate</TableHead>
                      <TableHead>Line Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseOrder.items?.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.line_number}</TableCell>
                        <TableCell>{item.item_code || '-'}</TableCell>
                        <TableCell>{item.description}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.unit_of_measure}</TableCell>
                        <TableCell>
                          <CurrencyDisplay
                            amount={item.unit_price}
                            currency={purchaseOrder.currency_code}
                            showCurrency={false}
                          />
                        </TableCell>
                        <TableCell>{item.discount_percentage}%</TableCell>
                        <TableCell>{item.tax_rate}%</TableCell>
                        <TableCell>
                          <CurrencyDisplay
                            amount={item.line_total}
                            currency={purchaseOrder.currency_code}
                            showCurrency={false}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Totals */}
              <div className="flex justify-end mt-6">
                <div className="w-80 space-y-2">
                  <Separator />
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <CurrencyDisplay
                      amount={purchaseOrder.subtotal}
                      currency={purchaseOrder.currency_code}
                      showCurrency={false}
                    />
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Grand Total:</span>
                    <CurrencyDisplay
                      amount={purchaseOrder.total_amount}
                      currency={purchaseOrder.currency_code}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Goods Receipts Tab */}
        <TabsContent value="receipts">
          <Card>
            <CardHeader>
              <CardTitle>Goods Receipts</CardTitle>
            </CardHeader>
            <CardContent>
              {purchaseOrder.goods_receipts && purchaseOrder.goods_receipts.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>GR Number</TableHead>
                      <TableHead>Receipt Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Received By</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseOrder.goods_receipts.map((receipt) => (
                      <TableRow key={receipt.id}>
                        <TableCell>{receipt.gr_number}</TableCell>
                        <TableCell>
                          {format(new Date(receipt.receipt_date), 'dd MMM yyyy')}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{receipt.status}</Badge>
                        </TableCell>
                        <TableCell>{receipt.received_by}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm">View</Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No goods receipts found for this purchase order
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Related Invoices Tab */}
        <TabsContent value="invoices">
          <Card>
            <CardHeader>
              <CardTitle>Related Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                No related invoices found for this purchase order
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Approval History Tab */}
        <TabsContent value="approval">
          <Card>
            <CardHeader>
              <CardTitle>Approval History</CardTitle>
            </CardHeader>
            <CardContent>
              <ApprovalStatus steps={mockApprovalSteps} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Trail Tab */}
        <TabsContent value="audit">
          <Card>
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Audit trail will be implemented with change tracking
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle>Attached Documents</CardTitle>
            </CardHeader>
            <CardContent>
              {mockDocuments.length > 0 ? (
                <div className="space-y-4">
                  {mockDocuments.map((document) => (
                    <DocumentPreview key={document.id} document={document} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No documents attached to this purchase order
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}