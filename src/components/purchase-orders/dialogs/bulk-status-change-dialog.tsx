"use client";

import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { SupplierStatusBadge } from "@/components/purchase-orders/ui";
import { bulkUpdateSupplierStatus } from "@/services/purchase-orders";
import { useToast } from "@/hooks/use-toast";
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import type { Supplier, SupplierStatus } from "@/types/purchase-orders/purchase-orders.types";

interface BulkStatusChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  suppliers: Supplier[];
  onSuccess?: () => void;
}

// Define form schema with conditional reason requirement
const bulkStatusChangeSchema = z.object({
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "BLACKLISTED"]),
  reason: z.string().optional(),
}).refine((data) => {
  // Reason is required for SUSPENDED and BLACKLISTED statuses
  if (data.status === "SUSPENDED" || data.status === "BLACKLISTED") {
    return data.reason && data.reason.trim().length > 0;
  }
  return true;
}, {
  message: "Reason is required for suspended and blacklisted statuses",
  path: ["reason"],
});

type BulkStatusChangeFormData = z.infer<typeof bulkStatusChangeSchema>;

export function BulkStatusChangeDialog({
  open,
  onOpenChange,
  suppliers,
  onSuccess,
}: BulkStatusChangeDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<{ success: number; failed: number } | null>(null);

  const form = useForm<BulkStatusChangeFormData>({
    resolver: zodResolver(bulkStatusChangeSchema),
    defaultValues: {
      status: "INACTIVE",
      reason: "",
    },
  });

  const handleSubmit = async (data: BulkStatusChangeFormData) => {
    if (suppliers.length === 0) return;

    setIsSubmitting(true);
    setProgress(0);
    setResults(null);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setProgress((prev) => Math.min(prev + 10, 90));
      }, 100);

      const supplierIds = suppliers.map(s => s.id);
      const result = await bulkUpdateSupplierStatus(
        supplierIds,
        data.status,
        data.reason
      );

      clearInterval(progressInterval);
      setProgress(100);

      if (result.success) {
        setResults({
          success: result.updatedCount || suppliers.length,
          failed: 0
        });

        setTimeout(() => {
          toast({
            title: "Success",
            description: `Successfully updated ${result.updatedCount || suppliers.length} suppliers`,
          });
          onSuccess?.();
          onOpenChange(false);
          form.reset();
          setProgress(0);
          setResults(null);
        }, 1500);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to change supplier statuses",
          variant: "destructive",
        });
        setResults({ success: 0, failed: suppliers.length });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      setResults({ success: 0, failed: suppliers.length });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedStatus = form.watch("status");
  const requiresReason = selectedStatus === "SUSPENDED" || selectedStatus === "BLACKLISTED";

  // Group suppliers by current status
  const statusGroups = suppliers.reduce((acc, supplier) => {
    const key = supplier.status ?? 'UNKNOWN'; // fallback for null or undefined
    if (!acc[key]) {
      acc[key] = 0;
    }
    acc[key]++;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Bulk Status Change</DialogTitle>
          <DialogDescription>
            Update the status for {suppliers.length} selected supplier{suppliers.length !== 1 ? 's' : ''}
          </DialogDescription>
        </DialogHeader>

        {/* Selected Suppliers Summary */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Current status distribution:</p>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {Object.entries(statusGroups).map(([status, count]) => (
                  <div key={status} className="flex items-center gap-2">
                    <SupplierStatusBadge status={status as SupplierStatus} className="text-xs" />
                    <span className="text-sm text-muted-foreground">({count})</span>
                  </div>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* New Status Selection */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>New Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="SUSPENDED">Suspended</SelectItem>
                      <SelectItem value="BLACKLISTED">Blacklisted</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    This status will be applied to all selected suppliers
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Reason Field */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Reason {requiresReason && <span className="text-red-500">*</span>}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder={
                        requiresReason
                          ? "Please provide a reason for this status change..."
                          : "Optional: Provide a reason for this status change..."
                      }
                      rows={4}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    {requiresReason
                      ? "A reason is required for suspended and blacklisted statuses"
                      : "Optionally provide a reason for this bulk status change"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Progress Indicator */}
            {isSubmitting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Processing...</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}

            {/* Results Summary */}
            {results && (
              <Alert className={results.failed > 0 ? "border-red-200" : "border-green-200"}>
                {results.failed > 0 ? (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                ) : (
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                )}
                <AlertDescription>
                  {results.failed > 0 ? (
                    <span className="text-red-700">
                      Failed to update {results.failed} supplier{results.failed !== 1 ? 's' : ''}
                    </span>
                  ) : (
                    <span className="text-green-700">
                      Successfully updated {results.success} supplier{results.success !== 1 ? 's' : ''}
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || suppliers.length === 0}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update {suppliers.length} Supplier{suppliers.length !== 1 ? 's' : ''}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}