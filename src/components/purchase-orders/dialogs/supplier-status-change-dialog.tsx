"use client";

import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { SupplierStatusBadge } from "@/components/purchase-orders/ui";
import { updateSupplierStatus } from "@/services/purchase-orders";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ArrowRight } from "lucide-react";
import type { Supplier, SupplierStatus } from "@/types/purchase-orders/purchase-orders.types";

interface SupplierStatusChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplier: Supplier | null;
  newStatus: SupplierStatus;
  onSuccess?: () => void;
}

// Define form schema with conditional reason requirement
const statusChangeSchema = z.object({
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "BLACKLISTED"]),
  reason: z.string().optional(),
}).refine((data) => {
  // Reason is required for SUSPENDED and BLACKLISTED statuses
  if (data.status === "SUSPENDED" || data.status === "BLACKLISTED") {
    return data.reason && data.reason.trim().length > 0;
  }
  return true;
}, {
  message: "Reason is required for suspended and blacklisted statuses",
  path: ["reason"],
});

type StatusChangeFormData = z.infer<typeof statusChangeSchema>;

export function SupplierStatusChangeDialog({
  open,
  onOpenChange,
  supplier,
  newStatus,
  onSuccess,
}: SupplierStatusChangeDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<StatusChangeFormData>({
    resolver: zodResolver(statusChangeSchema),
    defaultValues: {
      status: newStatus || "INACTIVE",
      reason: "",
    },
  });

  const handleSubmit = async (data: StatusChangeFormData) => {
    if (!supplier || !newStatus) return;

    setIsSubmitting(true);
    try {
      const result = await updateSupplierStatus(
        supplier.id,
        newStatus, // Use the pre-selected status from props
        data.reason
      );

      if (result.success) {
        toast({
          title: "Success",
          description: `Supplier status changed to ${getStatusLabel(newStatus)}`,
        });
        onSuccess?.();
        onOpenChange(false);
        form.reset();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to change supplier status",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusLabel = (status: SupplierStatus): string => {
    const labels: Record<Exclude<SupplierStatus, null>, string> = {
      ACTIVE: "Active",
      INACTIVE: "Inactive",
      SUSPENDED: "Suspended",
      BLACKLISTED: "Blacklisted",
    };

    if (status === null) return "Unknown"; // or "No Status"
    return labels[status] ?? status;
  };

  const requiresReason = newStatus === "SUSPENDED" || newStatus === "BLACKLISTED";

  if (!supplier || !newStatus) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Confirm Status Change</DialogTitle>
          <DialogDescription>
            You are about to change the status of {supplier.name}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Status Change Display */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Status Change</label>
              <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                <div className="flex-1 flex flex-col items-center">
                  <div className="text-sm text-muted-foreground mb-1">Current Status</div>
                  <SupplierStatusBadge status={supplier.status} />
                </div>
                <ArrowRight className="h-5 w-5 text-muted-foreground" />
                <div className="flex-1 flex flex-col items-center">
                  <div className="text-sm text-muted-foreground mb-1">New Status</div>
                  <SupplierStatusBadge status={newStatus} />
                </div>
              </div>
            </div>

            {/* Reason Field */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Reason {requiresReason && <span className="text-red-500">*</span>}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder={
                        requiresReason
                          ? "Please provide a reason for this status change..."
                          : "Optional: Provide a reason for this status change..."
                      }
                      rows={4}
                      autoFocus
                    />
                  </FormControl>
                  <FormDescription>
                    {requiresReason
                      ? "A reason is required for suspended and blacklisted statuses"
                      : "Optionally provide a reason for this status change"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Change Status
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}