"use client";

import { FC, useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Loader2,
  Building,
  Mail,
  Phone,
  Globe,
  CreditCard,
  Calendar,
  History,
  User,
  Clock
} from "lucide-react";
import {
  getSupplierById,
  getSupplierPerformance
} from "@/services/purchase-orders/suppliers.service";
import { getSupplierStatusHistory } from "@/services/purchase-orders/suppliers-server.service";
import type { Supplier } from "@/types/purchase-orders/purchase-orders.types";
import { SupplierStatusBadge } from "@/components/purchase-orders/ui";
import { formatCurrency } from "@/lib/utils";

interface SupplierDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplierId?: string;
}

export const SupplierDetailsDialog: FC<SupplierDetailsDialogProps> = ({
  open,
  onOpenChange,
  supplierId,
}) => {
  const [loading, setLoading] = useState(true);
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [performance, setPerformance] = useState<any>(null);
  const [statusHistory, setStatusHistory] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("details");

  useEffect(() => {
    if (open && supplierId) {
      loadSupplierDetails();
    }
  }, [open, supplierId]);

  const loadSupplierDetails = async () => {
    if (!supplierId) return;

    setLoading(true);
    try {
      const [supplierData, performanceData, historyData] = await Promise.all([
        getSupplierById(supplierId),
        getSupplierPerformance(supplierId),
        getSupplierStatusHistory(supplierId),
      ]);

      setSupplier(supplierData);
      setPerformance(performanceData);
      if (historyData.success && historyData.data) {
        setStatusHistory(historyData.data);
      }
    } catch (error) {
      console.error("Error loading supplier details:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Supplier Details</DialogTitle>
          <DialogDescription>
            View comprehensive information about this supplier including contact details, performance metrics, and financial data.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : supplier ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="history">Status History</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              {/* Header Section */}
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-2xl font-semibold">{supplier.display_name || supplier.name}</h3>
                  <p className="text-sm text-muted-foreground">Code: {supplier.supplier_code}</p>
                </div>
                <SupplierStatusBadge status={supplier.status} />
              </div>

              <Separator />

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Company Information</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{supplier.supplier_type || "Not specified"}</span>
                    </div>
                    {supplier.registration_number && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Registration #:</span> {supplier.registration_number}
                      </div>
                    )}
                    {supplier.tax_registration_number && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Tax Registration #:</span> {supplier.tax_registration_number}
                      </div>
                    )}
                    <div className="text-sm">
                      <span className="text-muted-foreground">GST Registered:</span> {supplier.is_gst_registered ? "Yes" : "No"}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">Contact Information</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <a href={`mailto:${supplier.email}`} className="text-sm hover:underline">
                        {supplier.email}
                      </a>
                    </div>
                    {supplier.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{supplier.phone}</span>
                      </div>
                    )}
                    {supplier.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-muted-foreground" />
                        <a
                          href={supplier.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm hover:underline"
                        >
                          {supplier.website}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Financial Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Financial Details</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Currency: {supplier.currency_code}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-muted-foreground">Payment Terms:</span> {supplier.payment_terms || 0} days
                    </div>
                    {supplier.credit_limit && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Credit Limit:</span> {formatCurrency(supplier.credit_limit, supplier.currency_code ?? undefined)}
                      </div>
                    )}
                  </div>
                </div>

                {performance && (
                  <div className="space-y-4">
                    <h4 className="font-semibold">Performance Metrics</h4>
                    <div className="space-y-2">
                      <div className="text-sm">
                        <span className="text-muted-foreground">Total Orders:</span> {performance.totalOrders}
                      </div>
                      <div className="text-sm">
                        <span className="text-muted-foreground">Total Amount:</span> {formatCurrency(performance.totalAmount, supplier.currency_code ?? undefined)}
                      </div>
                      <div className="text-sm">
                        <span className="text-muted-foreground">On-Time Delivery:</span> {performance.onTimeDeliveryRate.toFixed(1)}%
                      </div>
                      <div className="text-sm">
                        <span className="text-muted-foreground">Quality Score:</span> {performance.qualityScore.toFixed(1)}%
                      </div>
                      {performance.lastOrderDate && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">Last Order:</span> {new Date(performance.lastOrderDate).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Addresses */}
              {(supplier.billing_address || supplier.shipping_address) && (
                <>
                  <Separator />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {supplier.billing_address && (
                      <div className="space-y-2">
                        <h4 className="font-semibold">Billing Address</h4>
                        <div className="text-sm space-y-1">
                          <p>{supplier.billing_address.street_address}</p>
                          <p>{supplier.billing_address.city}, {supplier.billing_address.postal_code}</p>
                          <p>{supplier.billing_address.country}</p>
                        </div>
                      </div>
                    )}
                    {supplier.shipping_address && (
                      <div className="space-y-2">
                        <h4 className="font-semibold">Shipping Address</h4>
                        <div className="text-sm space-y-1">
                          <p>{supplier.shipping_address.street_address}</p>
                          <p>{supplier.shipping_address.city}, {supplier.shipping_address.postal_code}</p>
                          <p>{supplier.shipping_address.country}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Contact Persons */}
              {supplier.contact_persons && supplier.contact_persons.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h4 className="font-semibold">Contact Persons</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {supplier.contact_persons.map((contact) => (
                        <div key={contact.id} className="border rounded-lg p-3 space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{contact.name}</span>
                            {contact.is_primary && (
                              <Badge variant="secondary" className="text-xs">Primary</Badge>
                            )}
                          </div>
                          {contact.title && (
                            <p className="text-sm text-muted-foreground">{contact.title}</p>
                          )}
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Mail className="h-3 w-3 text-muted-foreground" />
                              <a href={`mailto:${contact.email}`} className="text-xs hover:underline">
                                {contact.email}
                              </a>
                            </div>
                            {contact.phone && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-3 w-3 text-muted-foreground" />
                                <span className="text-xs">{contact.phone}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Bank Details */}
              {supplier.bank_details && supplier.bank_details.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h4 className="font-semibold">Bank Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {supplier.bank_details.map((bank) => (
                        <div key={bank.id} className="border rounded-lg p-3 space-y-2">
                          <p className="font-medium">{bank.bank_name}</p>
                          <div className="space-y-1 text-sm">
                            <p><span className="text-muted-foreground">Account #:</span> {bank.account_number}</p>
                            {bank.swift_code && (
                              <p><span className="text-muted-foreground">SWIFT:</span> {bank.swift_code}</p>
                            )}
                            <p><span className="text-muted-foreground">Type:</span> {bank.account_type}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Timestamps */}
              <Separator />
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Created: {supplier.updated_at ? new Date(supplier.updated_at).toLocaleString() : "N/A"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Updated: {supplier.updated_at ? new Date(supplier.updated_at).toLocaleString() : "N/A"}</span>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-semibold flex items-center gap-2">
                  <History className="h-4 w-4" />
                  Status Change History
                </h4>

                {statusHistory.length > 0 ? (
                  <div className="space-y-4">
                    {/* Current Status */}
                    <div className="border rounded-lg p-4 bg-muted/50">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <SupplierStatusBadge status={supplier.status} />
                            <span className="text-sm font-medium">Current Status</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>
                                Last updated: {supplier.updated_at ? new Date(supplier.updated_at).toLocaleString() : "N/A"}
                              </span>
                            </div>
                          </div>
                          {statusHistory[0]?.reason && (
                            <div className="mt-2 p-2 bg-background rounded text-sm">
                              <span className="text-muted-foreground">Reason: </span>
                              {statusHistory[0].reason}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Timeline for future implementation */}
                    <div className="text-center py-8 text-muted-foreground">
                      <p className="text-sm">Full status history timeline will be available when status tracking is implemented.</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No status history available</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Supplier not found</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};