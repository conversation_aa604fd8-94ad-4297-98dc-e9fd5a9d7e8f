"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Upload, Download, FileSpreadsheet, AlertCircle } from "lucide-react";

interface ImportExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: "import" | "export";
  entityType: "purchase-orders" | "suppliers";
  onSuccess?: () => void;
}

export function ImportExportDialog({
  open,
  onOpenChange,
  mode,
  entityType,
  onSuccess,
}: ImportExportDialogProps) {
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [processing, setProcessing] = useState(false);
  const [exportFormat, setExportFormat] = useState<"csv" | "excel">("excel");

  const entityLabel = entityType === "purchase-orders" ? "Purchase Orders" : "Suppliers";

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const validTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
      if (!validTypes.includes(selectedFile.type)) {
        toast({
          title: "Invalid File Type",
          description: "Please upload a CSV or Excel file",
          variant: "destructive",
        });
        return;
      }
      setFile(selectedFile);
    }
  };

  const handleImport = async () => {
    if (!file) {
      toast({
        title: "No File Selected",
        description: "Please select a file to import",
        variant: "destructive",
      });
      return;
    }

    setProcessing(true);
    try {
      // TODO: Implement actual import logic
      // For now, simulate import process
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: "Import Successful",
        description: `${entityLabel} have been imported successfully`,
      });
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Import Failed",
        description: "An error occurred while importing the file",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleExport = async () => {
    setProcessing(true);
    try {
      // TODO: Implement actual export logic
      // For now, simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: "Export Started",
        description: `Your ${entityLabel} are being exported. You'll receive a download link shortly.`,
      });
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "An error occurred while exporting the data",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const downloadTemplate = () => {
    // TODO: Implement template download
    toast({
      title: "Template Downloaded",
      description: `${entityLabel} import template has been downloaded`,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {mode === "import" ? (
              <>
                <Upload className="inline-block mr-2 h-5 w-5" />
                Import {entityLabel}
              </>
            ) : (
              <>
                <Download className="inline-block mr-2 h-5 w-5" />
                Export {entityLabel}
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {mode === "import"
              ? `Upload a CSV or Excel file to import ${entityLabel.toLowerCase()}`
              : `Export your ${entityLabel.toLowerCase()} data to a file`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {mode === "import" ? (
            <>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Make sure your file follows the required format. 
                  <Button
                    variant="link"
                    className="px-1 h-auto"
                    onClick={downloadTemplate}
                  >
                    Download template
                  </Button>
                </AlertDescription>
              </Alert>

              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="file-upload">Select File</Label>
                      <Input
                        id="file-upload"
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={handleFileChange}
                      />
                      {file && (
                        <p className="text-sm text-muted-foreground">
                          Selected: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">File Requirements:</h4>
                      <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
                        <li>File must be in CSV or Excel format</li>
                        <li>First row must contain column headers</li>
                        <li>Required fields must be included</li>
                        <li>Maximum file size: 10MB</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Export Format</Label>
                    <RadioGroup value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="excel" id="excel" />
                        <Label htmlFor="excel" className="font-normal cursor-pointer">
                          <FileSpreadsheet className="inline-block mr-2 h-4 w-4" />
                          Excel (.xlsx)
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="csv" id="csv" />
                        <Label htmlFor="csv" className="font-normal cursor-pointer">
                          <FileSpreadsheet className="inline-block mr-2 h-4 w-4" />
                          CSV (.csv)
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Export Options:</h4>
                    <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
                      <li>All data will be exported based on current filters</li>
                      <li>Export includes all visible columns</li>
                      <li>Date format: YYYY-MM-DD</li>
                      <li>Number format: Standard decimal</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={processing}
          >
            Cancel
          </Button>
          <Button
            onClick={mode === "import" ? handleImport : handleExport}
            disabled={processing || (mode === "import" && !file)}
          >
            {processing ? (
              "Processing..."
            ) : mode === "import" ? (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}