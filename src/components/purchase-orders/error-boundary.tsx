"use client";

import React from "react";
import { ErrorBoundary } from "@/components/layout/error-boundary";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>riangle, ArrowLeft } from "lucide-react";
import Link from "next/link";

interface PurchaseOrderErrorBoundaryProps {
  children: React.ReactNode;
}

export function PurchaseOrderErrorBoundary({ children }: PurchaseOrderErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <div className="container mx-auto p-6">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <CardTitle>Purchase Order Error</CardTitle>
              </div>
              <CardDescription>
                We encountered an error while processing your purchase order request.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <p className="text-sm">
                  This could be due to:
                </p>
                <ul className="list-disc list-inside mt-2 space-y-1 text-sm text-muted-foreground">
                  <li>Network connectivity issues</li>
                  <li>Invalid data in the form</li>
                  <li>Server temporarily unavailable</li>
                  <li>Insufficient permissions</li>
                </ul>
              </div>
              
              <div className="flex gap-3">
                <Button asChild variant="default">
                  <Link href="/purchase-orders">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Purchase Orders
                  </Link>
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                >
                  Refresh Page
                </Button>
              </div>
              
              <p className="text-xs text-muted-foreground text-center">
                If this issue persists, please contact IT support with error code: PO-{Date.now()}
              </p>
            </CardContent>
          </Card>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
}