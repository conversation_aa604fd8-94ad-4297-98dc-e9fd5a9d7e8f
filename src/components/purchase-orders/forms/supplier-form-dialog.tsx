"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supplierSchema } from "@/schemas/purchase-orders/supplier.schema";
import { type SupplierFormData } from "@/schemas/purchase-orders/supplier.schema";
import { createSupplier, updateSupplier } from "@/services/purchase-orders/suppliers-server.service";
import { getSupplierById } from "@/services/purchase-orders/suppliers.service";

// Import form sections
import { BasicInformationSection } from "@/components/purchase-orders/forms/supplier-form-sections/basic-information-section";
import { AddressSection } from "@/components/purchase-orders/forms/supplier-form-sections/address-section";
import { ContactPersonsSection } from "@/components/purchase-orders/forms/supplier-form-sections/contact-persons-section";
import { BankDetailsSection } from "@/components/purchase-orders/forms/supplier-form-sections/bank-details-section";
import { FinancialSection } from "@/components/purchase-orders/forms/supplier-form-sections/financial-section";

const tabKeys = {
  basic: "basic_info",
  address: "address",
  contacts: "contact_persons",
  bank: "bank_details",
  financial: "financial",
} as const;

interface SupplierFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplierId?: string;
  onSuccess?: () => void;
}

export function SupplierFormDialog({
  open,
  onOpenChange,
  supplierId,
  onSuccess,
}: SupplierFormDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const isEditMode = !!supplierId;

  const form = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    mode: "onChange",
    defaultValues: {
      basic_info: {
        supplier_code: "",
        name: "",
        display_name: "",
        registration_number: "",
        tax_registration_number: "",
        supplier_type: "",
        category: "",
        status: "ACTIVE",
        email: "",
        phone_number: "",
        website: "",
      },
      address: {
        billing_address: undefined,
        shipping_address: undefined,
        same_as_billing: false,
      },
      contact_persons: [],
      bank_details: [],
      financial: {
        currency_code: undefined,
        payment_terms: undefined,
        credit_limit: undefined,
        is_gst_registered: false,
      },
    },
  });

  useEffect(() => {
    if (open && supplierId) {
      loadSupplier();
    } else if (!open) {
      form.reset();
    }
  }, [open, supplierId]);

  const loadSupplier = async () => {
    if (!supplierId) return;

    setLoading(true);

    try {
      const supplier = await getSupplierById(supplierId);

      if (supplier) {
        form.reset({
          basic_info: {
            supplier_code: supplier.supplier_code ?? "",
            name: supplier.name ?? "",
            display_name: supplier.display_name ?? "",
            registration_number: supplier.registration_number ?? "",
            tax_registration_number: supplier.tax_registration_number ?? "",
            supplier_type: supplier.supplier_type ?? "",
            category: supplier.category ?? "",
            status: supplier.status ?? "ACTIVE",
            email: supplier.email ?? "",
            phone_number: supplier.phone ?? "",
            website: supplier.website ?? "",
          },
          address: {
            billing_address: supplier.billing_address ?? undefined,
            shipping_address: supplier.shipping_address ?? undefined,
            same_as_billing:
              JSON.stringify(supplier.billing_address) ===
              JSON.stringify(supplier.shipping_address),
          },
          contact_persons: supplier.contact_persons ?? [],
          bank_details: supplier.bank_details ?? [],
          financial: {
            currency_code: supplier.currency_code ?? "",
            payment_terms: supplier.payment_terms ?? undefined,
            credit_limit: supplier.credit_limit ?? undefined,
            is_gst_registered: supplier.is_gst_registered ?? false,
          },
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load supplier details",
        variant: "destructive",
      });
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  };

  const hasError = (key: keyof typeof tabKeys) => {
    return !!form.formState.errors?.[tabKeys[key]];
  };

  const onSubmit = async (data: SupplierFormData) => {
    setSaving(true);

    try {
      const result = isEditMode
        ? await updateSupplier(supplierId, data)
        : await createSupplier(data);

      if (result.success) {
        toast({
          title: "Success",
          description: isEditMode
            ? "Supplier updated successfully"
            : "Supplier created successfully",
        });
        onSuccess?.();
        onOpenChange(false);

      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save supplier",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? "Edit Supplier" : "Add New Supplier"}</DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Update the supplier information below."
              : "Fill in the details to create a new supplier."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <ScrollArea className="h-[calc(90vh-200px)] pr-4">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger
                    value="basic"
                    className={hasError("basic") ? "text-red-600 border-b-2 border-red-600" : ""}
                  >
                    Basic Info
                  </TabsTrigger>
                  <TabsTrigger
                    value="address"
                    className={hasError("address") ? "text-red-600 border-b-2 border-red-600" : ""}
                  >
                    Addresses
                  </TabsTrigger>
                  <TabsTrigger
                    value="contacts"
                    className={hasError("contacts") ? "text-red-600 border-b-2 border-red-600" : ""}
                  >
                    Contacts
                  </TabsTrigger>
                  <TabsTrigger
                    value="bank"
                    className={hasError("bank") ? "text-red-600 border-b-2 border-red-600" : ""}
                  >
                    Bank Details
                  </TabsTrigger>
                  <TabsTrigger
                    value="financial"
                    className={hasError("financial") ? "text-red-600 border-b-2 border-red-600" : ""}
                  >
                    Financial
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 mt-4">
                  <BasicInformationSection form={form} isEditMode={isEditMode} />
                </TabsContent>

                <TabsContent value="address" className="space-y-4 mt-4">
                  <AddressSection form={form} />
                </TabsContent>

                <TabsContent value="contacts" className="space-y-4 mt-4">
                  <ContactPersonsSection form={form} />
                </TabsContent>

                <TabsContent value="bank" className="space-y-4 mt-4">
                  <BankDetailsSection form={form} />
                </TabsContent>

                <TabsContent value="financial" className="space-y-4 mt-4">
                  <FinancialSection form={form} />
                </TabsContent>
              </Tabs>
            </ScrollArea>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading || saving}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || saving}
              >
                {saving ? "Saving..." : isEditMode ? "Update Supplier" : "Create Supplier"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}