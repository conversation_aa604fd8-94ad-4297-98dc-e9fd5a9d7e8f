"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CalendarIcon, Info, Package, Truck, AlertCircle, CheckCircle, Loader2, ChevronDown, ChevronUp, X, XIcon, CheckIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { goodsReceiptSchema, type GoodsReceiptFormData } from "@/schemas/purchase-orders/goods-receipt.schema";
import { getPurchaseOrdersForReceipt, getPurchaseOrderReceiptStatus, searchSpecificPurchaseOrder } from "@/services/purchase-orders/goods-receipt.service";
import { createGoodsReceipt } from "@/services/purchase-orders/goods-receipt-server.service";
import { AttachmentsSection } from "@/components/purchase-orders/forms/sections/attachments-section";
import type { PurchaseOrder, PurchaseOrderItem } from "@/types/purchase-orders/purchase-orders.types";
import type { RejectionReasonType } from "@/types/purchase-orders/goods-receipt.types";
import { createClient } from "@/utils/supabase/Client";
import { UserService } from "@/services/user.service";
import { FileWithPreview } from "@/types/general.types";
import SearchInput from "@/components/pages/general/data-table-search-input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface GoodsReceiptFormProps {
  selectedPOId?: string;
  onSuccess?: (grId: string) => void;
}

// Extended PO Item interface with previous receipt information
interface ExtendedPOItem extends PurchaseOrderItem {
  previouslyReceived: number;
  remainingQuantity: number;
}

// Rejection reason options
const REJECTION_REASONS: Array<{ value: RejectionReasonType; label: string }> = [
  { value: "damaged", label: "Damaged" },
  { value: "wrong_item", label: "Wrong Item" },
  { value: "quality_issue", label: "Quality Issue" },
  { value: "expired", label: "Expired" },
  { value: "other", label: "Other" },
];

export function GoodsReceiptForm({ selectedPOId, onSuccess }: GoodsReceiptFormProps) {
  const { toast } = useToast();
  const [extendedPOItems, setExtendedPOItems] = useState<ExtendedPOItem[]>([]);
  const [loadingReceiptStatus, setLoadingReceiptStatus] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [currentUser, setCurrentUser] = useState<{ id: string; name: string; email: string } | null>(null);
  const [inspectionRequired, setInspectionRequired] = useState(false);
  const [expandedDescription, setExpandedDescription] = useState<{ [key: number]: boolean }>({});
  const [showRejectionDetails, setShowRejectionDetails] = useState<{ [key: number]: boolean }>({});

  // purchase order
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [availablePOs, setAvailablePOs] = useState<PurchaseOrder[]>([]);
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  const form = useForm<GoodsReceiptFormData & {
    inspection_required?: boolean;
    inspector_name?: string;
    inspection_date?: Date;
    inspection_report_number?: string;
  }>({
    resolver: zodResolver(goodsReceiptSchema),
    defaultValues: {
      purchase_order_id: selectedPOId || "",
      receipt_date: new Date(),
      received_by: "",
      notes: "",
      items: [],
      inspection_required: false,
      inspector_name: "",
      inspection_date: undefined,
      inspection_report_number: ""
    }
  });
  const { fields, replace } = useFieldArray({
    control: form.control,
    name: "items"
  });
  const files = form.watch("files") || [];
  const watchedPOId = form.watch("purchase_order_id");

  useEffect(() => {
    const init = async (po_id: string) => {
      const result = await searchSpecificPurchaseOrder(po_id);
      if (result) {
        form.setValue('purchase_order_id', result.id);
        setSelectedPO(result);
      }
    }

    if (selectedPOId) {
      init(selectedPOId);
    }
    loadCurrentUser();
  }, []);

  const loadCurrentUser = async () => {
    try {
      const supabase = await createClient();
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

      if (authError || !authUser) {
        console.error("Failed to get auth user:", authError);
        return;
      }

      // Get user display information
      const userDisplayInfo = await UserService.getUserDisplayInfo(authUser.id);

      if (userDisplayInfo) {
        setCurrentUser({
          id: authUser.id,
          name: userDisplayInfo.name,
          email: userDisplayInfo.email
        });
        // Auto-populate the received_by field with the current user's name
        form.setValue("received_by", userDisplayInfo.name || userDisplayInfo.email);
      } else {
        // Fallback to auth user data
        setCurrentUser({
          id: authUser.id,
          name: authUser.user_metadata?.full_name || authUser.email?.split('@')[0] || 'User',
          email: authUser.email || ''
        });
        form.setValue("received_by", authUser.user_metadata?.full_name || authUser.email?.split('@')[0] || 'User');
      }
    } catch (error) {
      console.error("Failed to load current user:", error);
    }
  };

  useEffect(() => {
    if (watchedPOId) {
      const po = availablePOs.find(p => p.id === watchedPOId);
      if (po) {
        setSelectedPO(po);
        loadPOReceiptStatus(po);
      }
    } else {
      setSelectedPO(null);
      setExtendedPOItems([]);
      replace([]);
    }
  }, [watchedPOId, availablePOs]);

  const loadPOReceiptStatus = async (po: PurchaseOrder) => {
    setLoadingReceiptStatus(true);
    try {
      const { items: receiptStatus } = await getPurchaseOrderReceiptStatus(po.id);

      if (po.items) {
        const extendedItems = po.items.map((item) => {
          const status = receiptStatus.find(s => s.item_id === item.id);
          return {
            ...item,
            previouslyReceived: status?.received_quantity || 0,
            remainingQuantity: item.quantity - (status?.received_quantity || 0)
          };
        });

        setExtendedPOItems(extendedItems);
        initializeReceiptItems(extendedItems);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load receipt status",
        variant: "destructive",
      });
    } finally {
      setLoadingReceiptStatus(false);
    }
  };

  const onPOSearch = async (pageToLoad = page) => {
    setIsSearching(true);

    const result = await getPurchaseOrdersForReceipt(searchQuery, pageToLoad);
    if (result.success) {
      setAvailablePOs(result.purchaseOrder);
      setPage(result.currentPage);
      setTotalPages(result.totalPages);
    }
    setIsSearching(false);
  }

  useEffect(() => {
    onPOSearch();
  }, [searchQuery])

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      onPOSearch(newPage);
    }
  }

  const initializeReceiptItems = (items: ExtendedPOItem[]) => {
    const receiptItems = items.map(item => ({
      po_item_id: item.id, // Use po_item_id to match database schema
      quantity_received: 0,
      quantity_accepted: 0,
      quantity_rejected: 0,
      rejection_reason: "",
      rejection_reason_type: undefined,
      rejection_notes: ""
    }));
    replace(receiptItems);
  };

  const setFiles = (updatedFiles: FileWithPreview[]) => {
    form.setValue("files", updatedFiles, {
      shouldDirty: true,
      shouldValidate: true,
    });
  };

  const handleReceiveAll = () => {
    if (!extendedPOItems.length) return;

    const updatedItems = form.getValues("items").map((item, index) => {
      const poItem = extendedPOItems[index];
      const receiveQty = poItem.remainingQuantity;
      return {
        ...item,
        quantity_received: receiveQty,
        quantity_accepted: receiveQty,
        quantity_rejected: 0,
        rejection_reason: "",
        rejection_reason_type: undefined,
        rejection_notes: ""
      };
    });

    replace(updatedItems);
    toast({
      title: "Success",
      description: "All remaining quantities set to receive",
    });
  };

  const handleAcceptAll = () => {
    const updatedItems = form.getValues("items").map(item => ({
      ...item,
      quantity_accepted: item.quantity_received,
      quantity_rejected: 0,
      rejection_reason: "",
      rejection_reason_type: undefined,
      rejection_notes: ""
    }));

    replace(updatedItems);

    // Hide all rejection details
    setShowRejectionDetails({});

    toast({
      title: "Success",
      description: "All received quantities set to accepted",
    });
  };

  const handleClearAll = () => {
    const clearedItems = form.getValues("items").map(item => ({
      ...item,
      quantity_received: 0,
      quantity_accepted: 0,
      quantity_rejected: 0,
      rejection_reason: "",
      rejection_reason_type: undefined,
      rejection_notes: ""
    }));

    replace(clearedItems);
    setShowRejectionDetails({});

    toast({
      title: "Success",
      description: "All quantities cleared",
    });
  };

  const updateQuantityReceived = (index: number, value: number) => {
    const maxQty = extendedPOItems[index]?.remainingQuantity || 0;
    const clampedValue = Math.min(Math.max(0, value), maxQty);

    form.setValue(`items.${index}.quantity_received`, clampedValue);
    // Auto-set accepted quantity to received quantity
    form.setValue(`items.${index}.quantity_accepted`, clampedValue);
    form.setValue(`items.${index}.quantity_rejected`, 0);

    // Hide rejection details if no rejection
    if (clampedValue === form.getValues(`items.${index}.quantity_accepted`)) {
      setShowRejectionDetails(prev => ({ ...prev, [index]: false }));
    }
  };

  const updateQuantityAccepted = (index: number, value: number) => {
    const received = form.getValues(`items.${index}.quantity_received`);
    const clampedValue = Math.min(Math.max(0, value), received);

    form.setValue(`items.${index}.quantity_accepted`, clampedValue);
    const rejected = received - clampedValue;
    form.setValue(`items.${index}.quantity_rejected`, rejected);

    // Show/hide rejection details
    if (rejected > 0) {
      setShowRejectionDetails(prev => ({ ...prev, [index]: true }));
    } else {
      setShowRejectionDetails(prev => ({ ...prev, [index]: false }));
      form.setValue(`items.${index}.rejection_reason`, "");
      form.setValue(`items.${index}.rejection_reason_type`, undefined);
      form.setValue(`items.${index}.rejection_notes`, "");
    }
  };

  const updateQuantityRejected = (index: number, value: number) => {
    const received = form.getValues(`items.${index}.quantity_received`);
    const clampedValue = Math.min(Math.max(0, value), received);

    form.setValue(`items.${index}.quantity_rejected`, clampedValue);
    form.setValue(`items.${index}.quantity_accepted`, received - clampedValue);

    // Show/hide rejection details
    if (clampedValue > 0) {
      setShowRejectionDetails(prev => ({ ...prev, [index]: true }));
    } else {
      setShowRejectionDetails(prev => ({ ...prev, [index]: false }));
      form.setValue(`items.${index}.rejection_reason`, "");
      form.setValue(`items.${index}.rejection_reason_type`, undefined);
      form.setValue(`items.${index}.rejection_notes`, "");
    }
  };

  const toggleDescription = (index: number) => {
    setExpandedDescription(prev => ({ ...prev, [index]: !prev[index] }));
  };

  const formatRejectionReason = (reasonType: string | undefined, notes: string | undefined) => {
    if (!reasonType) return "";
    const reason = REJECTION_REASONS.find(r => r.value === reasonType)?.label || reasonType;
    return reasonType === "other" && notes ? `${reason}: ${notes}` : reason;
  };

  const onSubmit = async (data: GoodsReceiptFormData) => {
    const dataToSave = {
      ...data,
      files: undefined,
    };
    // Validate that at least one item has quantity received > 0
    const hasReceivedItems = dataToSave.items.some(item => item.quantity_received > 0);
    if (!hasReceivedItems) {
      toast({
        title: "Validation Error",
        description: "At least one item must have a quantity received",
        variant: "destructive",
      });
      return;
    }

    // Validate rejection reasons
    const itemsWithRejections = dataToSave.items.filter(item => item.quantity_rejected > 0);
    const missingReasons = itemsWithRejections.some(item => !item.rejection_reason);
    if (missingReasons) {
      toast({
        title: "Validation Error",
        description: "Rejection reason is required for all rejected items",
        variant: "destructive",
      });
      return;
    }

    setSubmitting(true);
    try {
      // Format rejection reasons
      const formattedData = {
        ...dataToSave,
        items: dataToSave.items.map((item, index) => {
          const reasonType = form.getValues(`items.${index}.rejection_reason_type`);
          const reasonNotes = form.getValues(`items.${index}.rejection_notes`);
          return {
            ...item,
            po_item_id: item.po_item_id, // Ensure correct field name
            rejection_reason: item.quantity_rejected > 0 ? formatRejectionReason(reasonType, reasonNotes) : ""
          };
        })
      };

      const result = await createGoodsReceipt(formattedData);

      if (result.success && result.newId) {
        toast({
          title: "Success",
          description: "Goods receipt created successfully",
        });

        if (onSuccess) {
          onSuccess(result.newId);
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create goods receipt",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const isOverdue = (date: string) => {
    return new Date(date) < new Date();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Goods Receipt Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="col-span-2">
                <FormField
                  control={form.control}
                  name="purchase_order_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purchase Order *</FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          <div className="relative">
                            <SearchInput onSearch={setSearchQuery} value={searchQuery} placeholder="Search purchase order..." />
                          </div>

                          {selectedPO && (
                            <div
                              key={selectedPO.id}
                              className={`flex flex-col items-start gap-1 p-3 cursor-pointer bg-green-50 border rounded-md border-green-200`}
                            >
                              <div className="flex items-center gap-2 w-full">
                                <span className="font-medium">{selectedPO.po_number}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="ml-auto hover:bg-red-500"
                                  onClick={() => {
                                    setSelectedPO(null); // Clear selected budget
                                    form.setValue('purchase_order_id', ""); // Clear form field
                                  }}
                                >
                                  <XIcon className="h-4 w-4" />
                                </Button>
                              </div>
                              <div className="text-sm text-gray-500">{selectedPO.title}</div>
                            </div>
                          )}
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="w-[20%]">PO Number</TableHead>
                                <TableHead className="w-[25%]">Title</TableHead>
                                <TableHead className="w-[45%]">Description</TableHead>
                                <TableHead className="w-[10%]">Selected</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {isSearching ? (
                                <TableRow>
                                  <TableCell colSpan={4} className="text-center">
                                    <LoadingSpinner />
                                  </TableCell>
                                </TableRow>
                              ) : availablePOs.length > 0 ? (
                                availablePOs.map((result) => (
                                  <TableRow
                                    key={result.id}
                                    onClick={() => {
                                      field.onChange(result.id);
                                      setSelectedPO(result);
                                    }}
                                    className={`text-sm hover:bg-gray-50 cursor-pointer`}
                                  >
                                    <TableCell className="flex items-center gap-2">
                                      <span>{result.po_number ?? "--"}</span>
                                    </TableCell>
                                    <TableCell>{result.title ?? "--"}</TableCell>
                                    <TableCell className="whitespace-normal break-words max-w-xs">
                                      {result.description ?? "--"}
                                    </TableCell>
                                    <TableCell>
                                      {field.value === result.id && (
                                        <CheckIcon size={16} className="text-green-500 stroke-[3]" />
                                      )}
                                    </TableCell>
                                  </TableRow>
                                ))
                              ) : (
                                <TableRow>
                                  <TableCell colSpan={4} className="text-center">
                                    {searchQuery
                                      ? "No purchase order found. Try a different search term."
                                      : "Type to search for purchase order."}
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="outline"
                              className="h-8 w-8 p-0"
                              onClick={(e) => {
                                e.preventDefault();
                                handlePageChange(page - 1);
                              }}
                              disabled={page <= 1}
                            >
                              <span className="sr-only">Go to previous page</span>
                              <ChevronLeft />
                            </Button>
                            <Button
                              variant="outline"
                              className="h-8 w-8 p-0"
                              onClick={(e) => {
                                e.preventDefault();
                                handlePageChange(page + 1);
                              }}
                              disabled={page >= totalPages}
                            >
                              <span className="sr-only">Go to next page</span>
                              <ChevronRight />
                            </Button>
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormItem>
                <FormLabel>GR Number</FormLabel>
                <FormControl>
                  <Input
                    value="Auto-generated"
                    disabled
                    className="bg-muted"
                  />
                </FormControl>
                <FormDescription>
                  Will be generated automatically on save
                </FormDescription>
              </FormItem>

              <FormField
                control={form.control}
                name="receipt_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Receipt Date *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            if (date) {
                              const utcDate = new Date(Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate()
                              ));
                              field.onChange(utcDate);
                            }
                          }}
                          disabled={[{ after: new Date() }]}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Cannot be a future date
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="received_by"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Received By *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter receiver name"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {currentUser && `Current user: ${currentUser.name || currentUser.email}`}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter delivery conditions, issues, or special instructions"
                        className="resize-none"
                        rows={3}
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormDescription>
                      Maximum 1000 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* PO Information Display */}
        {selectedPO && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Purchase Order Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Supplier Information */}
              <div>
                <h4 className="font-medium mb-3 text-sm text-muted-foreground">Supplier Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span className="text-sm font-medium">Name:</span>
                    <div className="text-sm">{selectedPO.suppliers?.name || "Unknown"}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Contact:</span>
                    <div className="text-sm">
                      {selectedPO.suppliers?.contact_persons?.[0]?.name || "-"}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Phone:</span>
                    <div className="text-sm">
                      {selectedPO.suppliers?.phone || selectedPO.suppliers?.contact_persons?.[0]?.phone || "-"}
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Information */}
              <div>
                <h4 className="font-medium mb-3 text-sm text-muted-foreground">Order Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <span className="text-sm font-medium">PO Number:</span>
                    <div className="text-sm">{selectedPO.po_number}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">PO Date:</span>
                    <div className="text-sm">
                      {format(new Date(selectedPO.po_date), 'dd MMM yyyy')}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Expected Delivery:</span>
                    <div className="text-sm flex items-center gap-1">
                      {selectedPO.expected_delivery_date ? (
                        <>
                          {format(new Date(selectedPO.expected_delivery_date), 'dd MMM yyyy')}
                          {isOverdue(selectedPO.expected_delivery_date) && (
                            <Badge variant="destructive" className="ml-1">
                              Overdue
                            </Badge>
                          )}
                        </>
                      ) : (
                        "-"
                      )}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Total Value:</span>
                    <div className="text-sm">
                      {selectedPO.currency_code} {selectedPO.total_amount.toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Delivery Details */}
              {(selectedPO.delivery_address || selectedPO.delivery_instructions) && (
                <div>
                  <h4 className="font-medium mb-3 text-sm text-muted-foreground">Delivery Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedPO.delivery_address && (
                      <div>
                        <span className="text-sm font-medium">Delivery Address:</span>
                        <div className="text-sm space-y-1 mt-1">
                          {selectedPO.delivery_address.street_address && (
                            <div>{selectedPO.delivery_address.street_address}</div>
                          )}
                          <div>
                            {[selectedPO.delivery_address?.city, selectedPO.delivery_address?.postal_code]
                              .filter(Boolean)
                              .join(", ")}
                          </div>
                          {selectedPO.delivery_address.country && (
                            <div>{selectedPO.delivery_address.country}</div>
                          )}
                        </div>
                      </div>
                    )}
                    {selectedPO.delivery_instructions && (
                      <div>
                        <span className="text-sm font-medium">Delivery Instructions:</span>
                        <div className="text-sm mt-1">{selectedPO.delivery_instructions}</div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Line Items Receipt */}
        {selectedPO && fields.length > 0 && (
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Receipt Items
                </CardTitle>
                <div className="flex flex-wrap gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleReceiveAll}
                    disabled={loadingReceiptStatus}
                  >
                    Receive All Remaining
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAcceptAll}
                  >
                    Accept All Received
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                  >
                    Clear All
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loadingReceiptStatus ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-16">Line #</TableHead>
                        <TableHead className="min-w-[100px]">Item Code</TableHead>
                        <TableHead className="min-w-[200px]">Description</TableHead>
                        <TableHead className="min-w-[100px]">GL Account</TableHead>
                        <TableHead className="text-center min-w-[60px]">UoM</TableHead>
                        <TableHead className="text-right min-w-[80px]">Ordered</TableHead>
                        <TableHead className="text-right min-w-[80px]">Previously Received</TableHead>
                        <TableHead className="text-right min-w-[80px]">Remaining</TableHead>
                        <TableHead className="text-right min-w-[100px]">Open to Receive</TableHead>
                        <TableHead className="text-right min-w-[120px]">Qty Received</TableHead>
                        <TableHead className="text-right min-w-[120px]">Qty Accepted</TableHead>
                        <TableHead className="text-right min-w-[120px]">Qty Rejected</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fields.map((field, index) => {
                        const poItem = extendedPOItems[index];
                        if (!poItem) return null;

                        const rejectedQty = form.watch(`items.${index}.quantity_rejected`);
                        const hasRejection = rejectedQty > 0;

                        return (
                          <>
                            <TableRow key={field.id}>
                              <TableCell className="font-medium">{poItem.line_number}</TableCell>
                              <TableCell>{poItem.item_code || '-'}</TableCell>
                              <TableCell>
                                <div className="max-w-[200px]">
                                  <div className={cn(
                                    "text-sm",
                                    !expandedDescription[index] && "truncate"
                                  )}>
                                    {poItem.description}
                                  </div>
                                  {poItem.description.length > 50 && (
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
                                      onClick={() => toggleDescription(index)}
                                    >
                                      {expandedDescription[index] ? (
                                        <><ChevronUp className="h-3 w-3 mr-1" />Less</>
                                      ) : (
                                        <><ChevronDown className="h-3 w-3 mr-1" />More</>
                                      )}
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="text-xs">{poItem.gl_account_code || '-'}</TableCell>
                              <TableCell className="text-center">{poItem.unit_of_measure || '-'}</TableCell>
                              <TableCell className="text-right">{poItem.quantity.toFixed(2)}</TableCell>
                              <TableCell className="text-right">
                                <span className="text-muted-foreground">
                                  {poItem.previouslyReceived.toFixed(2)}
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <span className="font-medium">
                                  {poItem.remainingQuantity.toFixed(2)}
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <span className="text-blue-600 font-medium">
                                  {poItem.remainingQuantity.toFixed(2)}
                                </span>
                              </TableCell>

                              {/* Quantity Received */}
                              <TableCell>
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.quantity_received`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          min="0"
                                          max={poItem.remainingQuantity}
                                          step="0.0001"
                                          className="w-24 text-right"
                                          placeholder="0.0000"
                                          {...field}
                                          onChange={(e) => {
                                            const value = parseFloat(e.target.value) || 0;
                                            updateQuantityReceived(index, value);
                                          }}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </TableCell>

                              {/* Quantity Accepted */}
                              <TableCell>
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.quantity_accepted`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          min="0"
                                          max={form.watch(`items.${index}.quantity_received`)}
                                          step="0.0001"
                                          className="w-24 text-right"
                                          placeholder="0.0000"
                                          {...field}
                                          onChange={(e) => {
                                            const value = parseFloat(e.target.value) || 0;
                                            updateQuantityAccepted(index, value);
                                          }}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </TableCell>

                              {/* Quantity Rejected */}
                              <TableCell>
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.quantity_rejected`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <div className="relative">
                                          <Input
                                            type="number"
                                            min="0"
                                            max={form.watch(`items.${index}.quantity_received`)}
                                            step="0.0001"
                                            className={cn(
                                              "w-24 text-right",
                                              hasRejection && "border-orange-500"
                                            )}
                                            placeholder="0.0000"
                                            {...field}
                                            onChange={(e) => {
                                              const value = parseFloat(e.target.value) || 0;
                                              updateQuantityRejected(index, value);
                                            }}
                                          />
                                          {hasRejection && (
                                            <AlertCircle className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 text-orange-500" />
                                          )}
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </TableCell>
                            </TableRow>

                            {/* Rejection Details Row */}
                            {showRejectionDetails[index] && (
                              <TableRow className="bg-orange-50/50">
                                <TableCell colSpan={12} className="pt-2 pb-4">
                                  <div className="flex items-start gap-4 pl-4">
                                    <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
                                    <div className="flex-1 space-y-3">
                                      <div className="font-medium text-sm">Rejection Details for Line {poItem.line_number}</div>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {/* Rejection Reason Dropdown */}
                                        <FormField
                                          control={form.control}
                                          name={`items.${index}.rejection_reason_type`}
                                          render={({ field }) => (
                                            <FormItem>
                                              <FormLabel className="text-sm">Rejection Reason *</FormLabel>
                                              <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                              >
                                                <FormControl>
                                                  <SelectTrigger className="h-9">
                                                    <SelectValue placeholder="Select reason" />
                                                  </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                  {REJECTION_REASONS.map((reason) => (
                                                    <SelectItem key={reason.value} value={reason.value}>
                                                      {reason.label}
                                                    </SelectItem>
                                                  ))}
                                                </SelectContent>
                                              </Select>
                                              <FormMessage />
                                            </FormItem>
                                          )}
                                        />

                                        {/* Additional Notes for "Other" */}
                                        {form.watch(`items.${index}.rejection_reason_type`) === "other" && (
                                          <FormField
                                            control={form.control}
                                            name={`items.${index}.rejection_notes`}
                                            render={({ field }) => (
                                              <FormItem>
                                                <FormLabel className="text-sm">Additional Notes *</FormLabel>
                                                <FormControl>
                                                  <Input
                                                    placeholder="Please specify the reason"
                                                    className="h-9"
                                                    {...field}
                                                  />
                                                </FormControl>
                                                <FormMessage />
                                              </FormItem>
                                            )}
                                          />
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Summary Statistics */}
              {fields.length > 0 && !loadingReceiptStatus && (
                <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Items:</span>
                      <div className="font-medium">{fields.length}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Items Received:</span>
                      <div className="font-medium">
                        {fields.filter((_, index) => form.watch(`items.${index}.quantity_received`) > 0).length}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Items with Rejections:</span>
                      <div className="font-medium text-orange-600">
                        {fields.filter((_, index) => form.watch(`items.${index}.quantity_rejected`) > 0).length}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Receipt Status:</span>
                      <div className="font-medium">
                        {fields.every((_, index) => form.watch(`items.${index}.quantity_received`) === 0)
                          ? "Not Started"
                          : fields.every((_, index) => {
                            const item = extendedPOItems[index];
                            return item && form.watch(`items.${index}.quantity_received`) >= item.remainingQuantity;
                          })
                            ? "Complete"
                            : "Partial"
                        }
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Quality Control Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Quality Control
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Inspection Required Checkbox */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inspection-required"
                  checked={inspectionRequired}
                  onCheckedChange={(checked) => {
                    if (checked === "indeterminate") return;
                    setInspectionRequired(checked);
                  }}
                />
                <label
                  htmlFor="inspection-required"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Inspection Required
                </label>
              </div>

              {/* Additional Inspection Fields */}
              {inspectionRequired && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 p-4 border rounded-lg bg-muted/30">
                  <FormField
                    control={form.control}
                    name="inspector_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Inspector Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter inspector name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="inspection_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Inspection Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={(date) => {
                                if (date) {
                                  const utcDate = new Date(Date.UTC(
                                    date.getFullYear(),
                                    date.getMonth(),
                                    date.getDate()
                                  ));
                                  field.onChange(utcDate);
                                }
                              }}
                              disabled={[{ after: new Date() }]}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="inspection_report_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Inspection Report Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter report number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Discrepancy Handling Note */}
              {fields.some((_, index) => form.watch(`items.${index}.quantity_rejected`) > 0) && (
                <Alert className="border-orange-200 bg-orange-50">
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                  <AlertDescription>
                    Items with rejections will automatically create a discrepancy report.
                    The supplier will be notified for return/credit note processing.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Attachments Section */}
        <Card>
          <CardHeader>
            <CardTitle>Attachments and Documentation</CardTitle>
          </CardHeader>
          <CardContent>
            <AttachmentsSection
              from="goods-receipts"
              files={files}
              setFiles={setFiles}
            />
            <Alert className="mt-4">
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Required:</strong> Delivery Note/Packing List • Carrier Receipt/POD<br />
                <strong>Optional:</strong> Quality Certificates • Photos • Inspection Reports
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Info className="h-4 w-4" />
                <span>Save as draft allows editing later. Complete receipt updates inventory.</span>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => window.history.back()}
                  disabled={submitting}
                >
                  Cancel
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  disabled={submitting || !selectedPO || fields.length === 0}
                  onClick={() => {
                    // Save as draft logic would go here
                    toast({
                      title: "Info",
                      description: "Save as draft functionality will be implemented",
                    });
                  }}
                >
                  {submitting ? (
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Saving...</>
                  ) : (
                    "Save as Draft"
                  )}
                </Button>

                <Button
                  type="submit"
                  disabled={submitting || !selectedPO || fields.length === 0}
                >
                  {submitting ? (
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Creating...</>
                  ) : (
                    "Complete Receipt"
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}