"use client";

import { UseFormReturn, useWatch } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { SupplierFormData } from "@/schemas/purchase-orders/supplier.schema";
import { useEffect } from "react";

interface AddressSectionProps {
  form: UseFormReturn<SupplierFormData>;
}

interface AddressFieldsProps {
  form: UseFormReturn<SupplierFormData>;
  prefix: "address.billing_address" | "address.shipping_address";
}

function AddressFields({ form, prefix }: AddressFieldsProps) {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name={`${prefix}.address_line_1`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Address Line 1</FormLabel>
            <FormControl>
              <Input {...field} placeholder="Enter address line 1" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name={`${prefix}.address_line_2`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Address Line 2</FormLabel>
            <FormControl>
              <Input {...field} placeholder="Enter address line 2 (optional)" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name={`${prefix}.city`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter city" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={`${prefix}.state`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>State</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter state" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name={`${prefix}.country`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Country</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter country code (e.g., SG)" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={`${prefix}.postal_code`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Postal Code</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter postal code" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}

export function AddressSection({ form }: AddressSectionProps) {
  const sameAsBilling = useWatch({
    control: form.control,
    name: "address.same_as_billing",
  });

  const billingAddress = useWatch({
    control: form.control,
    name: "address.billing_address",
  });

  // Keep shipping address in sync when 'same_as_billing' is ON
  useEffect(() => {
    if (sameAsBilling && billingAddress) {
      form.setValue("address.shipping_address", billingAddress, {
        shouldValidate: false,
        shouldDirty: true,
      });
    }
  }, [sameAsBilling, billingAddress, form]);

  return (
    <div className="space-y-6">
      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Address</CardTitle>
        </CardHeader>
        <CardContent>
          <AddressFields form={form} prefix="address.billing_address" />
        </CardContent>
      </Card>

      {/* Shipping Address */}
      <Card>
        <CardHeader>
          <CardTitle>Shipping Address</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="address.same_as_billing"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                <div>
                  <FormLabel>Same as Billing Address</FormLabel>
                </div>
                <FormControl>
                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
              </FormItem>
            )}
          />

          {!sameAsBilling && (
            <AddressFields
              form={form}
              prefix="address.shipping_address"
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}