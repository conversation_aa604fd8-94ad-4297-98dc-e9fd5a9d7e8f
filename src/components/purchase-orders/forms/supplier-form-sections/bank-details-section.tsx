"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PlusCircle, Trash2 } from "lucide-react";
import type { SupplierFormData, BankDetailsFormData } from "@/schemas/purchase-orders/supplier.schema";

interface BankDetailsSectionProps {
  form: UseFormReturn<SupplierFormData>;
}

export function BankDetailsSection({ form }: BankDetailsSectionProps) {
  const bankDetails = form.watch("bank_details") || [];

  const addBankDetails = () => {
    const newBankDetails: BankDetailsFormData = {
      bank_name: "",
      account_number: "",
      swift_code: "",
      account_type: "",
    };
    form.setValue("bank_details", [...bankDetails, newBankDetails]);
  };

  const removeBankDetails = (index: number) => {
    const updatedBankDetails = bankDetails.filter((_, i) => i !== index);
    form.setValue("bank_details", updatedBankDetails);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Bank Details</CardTitle>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addBankDetails}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Bank Account
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {bankDetails.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-8">
            No bank accounts added yet. Click "Add Bank Account" to add one.
          </p>
        ) : (
          bankDetails.map((_, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`bank_details.${index}.bank_name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter bank name"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`bank_details.${index}.account_number`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Number *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter account number"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`bank_details.${index}.swift_code`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SWIFT Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter SWIFT code"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`bank_details.${index}.account_type`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Type *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select account type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CURRENT">Current Account</SelectItem>
                              <SelectItem value="SAVINGS">Savings Account</SelectItem>
                              <SelectItem value="CHECKING">Checking Account</SelectItem>
                              <SelectItem value="BUSINESS">Business Account</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-end mt-4">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeBankDetails(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </CardContent>
    </Card>
  );
}