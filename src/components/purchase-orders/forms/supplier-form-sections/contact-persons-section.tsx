"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { PhoneInput } from "@/components/ui/phone-input";
import { PlusCircle, Trash2 } from "lucide-react";
import type { SupplierFormData, ContactPersonFormData } from "@/schemas/purchase-orders/supplier.schema";

interface ContactPersonsSectionProps {
  form: UseFormReturn<SupplierFormData>;
}

export function ContactPersonsSection({ form }: ContactPersonsSectionProps) {
  const contactPersons = form.watch("contact_persons") || [];

  const addContactPerson = () => {
    const newContact: ContactPersonFormData = {
      name: "",
      title: "",
      email: "",
      phone_number: "",
      is_primary: contactPersons.length === 0, // First contact is primary by default
    };
    form.setValue("contact_persons", [...contactPersons, newContact]);
  };

  const removeContactPerson = (index: number) => {
    const updatedContacts = contactPersons.filter((_, i) => i !== index);
    // If we removed the primary contact, make the first one primary
    if (contactPersons[index].is_primary && updatedContacts.length > 0) {
      updatedContacts[0].is_primary = true;
    }
    form.setValue("contact_persons", updatedContacts);
  };

  const updateContactPerson = (index: number, value: boolean) => {
    const updated = contactPersons.map((contact, i) => ({
      ...contact,
      is_primary: i === index ? value : false,
    }));

    form.setValue("contact_persons", updated);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Contact Persons</CardTitle>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addContactPerson}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Contact
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {contactPersons.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-8">
            No contact persons added yet. Click "Add Contact" to add one.
          </p>
        ) : (
          contactPersons.map((_, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`contact_persons.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter contact name"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`contact_persons.${index}.title`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter title"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`contact_persons.${index}.email`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email *</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter email address"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`contact_persons.${index}.phone_number`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <PhoneInput
                            placeholder="Enter Phone Number"
                            international
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex items-center justify-between mt-4">
                  <FormField
                    control={form.control}
                    name={`contact_persons.${index}.is_primary`}
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={(checked) => updateContactPerson(index, checked)}
                          />
                        </FormControl>
                        <FormLabel>Primary Contact</FormLabel>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContactPerson(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </CardContent>
    </Card>
  );
}