"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { CurrencyDisplay } from "@/components/purchase-orders/ui";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { usePurchaseOrderForm } from "@/hooks/purchase-orders/use-purchase-order-form";
import { BasicInformationSection } from "@/components/purchase-orders/forms/sections/basic-information-section";
import { SupplierInformationSection } from "@/components/purchase-orders/forms/sections/supplier-information-section";
import { FinancialInformationSection } from "@/components/purchase-orders/forms/sections/financial-information-section";
import { DeliveryInformationSection } from "@/components/purchase-orders/forms/sections/delivery-information-section";
import { AttachmentsSection } from "@/components/purchase-orders/forms/sections/attachments-section";
import type { PurchaseOrde<PERSON> } from "@/types/purchase-orders/purchase-orders.types";
import { LineItemsTable } from "@/components/purchase-orders/forms/sections/line-items-table";
import { FileWithPreview } from "@/types/general.types";

interface PurchaseOrderFormProps {
  isEditing?: boolean;
  purchaseOrderId?: string;
  initialData?: PurchaseOrder;
  attachments?: FileWithPreview[];
  loadPurchaseOrder?: (purchaseOrderId: string) => void;
}

export function PurchaseOrderForm({
  isEditing = false,
  purchaseOrderId,
  initialData,
  attachments,
  loadPurchaseOrder
}: PurchaseOrderFormProps) {
  const {
    form,
    fields,
    watchedCurrency,
    loading,
    submitting,
    files,
    setFiles,
    items,
    options,
    actions,
    calculations
  } = usePurchaseOrderForm(isEditing, purchaseOrderId, initialData, attachments, loadPurchaseOrder);

  const totals = calculations.calculateTotals();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <BasicInformationSection
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Supplier Information</CardTitle>
          </CardHeader>
          <CardContent>
            <SupplierInformationSection
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Financial Information</CardTitle>
          </CardHeader>
          <CardContent>
            <FinancialInformationSection
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Line Items</CardTitle>
          </CardHeader>
          <CardContent>
            <LineItemsTable
              form={form}
              fields={fields}
              options={options}
              calculations={calculations}
              onAdd={(item) => items.append(item)}
              onEdit={(item) => {
                const index = fields.findIndex((p) => p.field_id === item.field_id)
                if (index !== -1) items.update(index, item)
              }}
              onRemove={(index) => items.remove(index)}
            />

            {form.formState.errors.items && (
              <p className="text-red-500 text-sm mt-2">
                At least one line item is required, and all fields must be valid.
              </p>
            )}

            {/* Totals Summary */}
            <div className="mt-6 flex justify-end">
              <div className="w-80 space-y-2">
                <Separator />
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <CurrencyDisplay
                    amount={totals.subtotal}
                    currency={watchedCurrency}
                    showCurrency={false}
                  />
                </div>
                <div className="flex justify-between">
                  <span>Total Tax:</span>
                  <CurrencyDisplay
                    amount={totals.totalTax}
                    currency={watchedCurrency}
                    showCurrency={false}
                  />
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                  <span>Grand Total:</span>
                  <CurrencyDisplay
                    amount={totals.totalAmount}
                    currency={watchedCurrency}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Delivery Information</CardTitle>
          </CardHeader>
          <CardContent>
            <DeliveryInformationSection form={form} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Attachments</CardTitle>
          </CardHeader>
          <CardContent>
            <AttachmentsSection
              from="purchase-orders"
              files={files}
              setFiles={setFiles}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => window.history.back()}
            disabled={submitting}
          >
            Cancel
          </Button>

          <Button
            type="button"
            variant="outline"
            onClick={form.handleSubmit((data) => actions.handleSubmission(false, data))}
            disabled={submitting}
          >
            {submitting ? "Saving..." : "Save as Draft"}
          </Button>

          <Button
            type="button"
            onClick={form.handleSubmit((data) => actions.handleSubmission(true, data))}
            disabled={submitting}
          >
            {submitting ? "Submitting..." : "Submit for Approval"}
          </Button>
        </div>
      </form>
    </Form>
  );
}