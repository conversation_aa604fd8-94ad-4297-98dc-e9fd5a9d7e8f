"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, X, FileText, Image, File } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { FileWithPreview } from "@/types/general.types";
import { deletePODocument } from "@/services/purchase-orders";

const ACCEPTED_FILE_TYPES = [
  { mime: "application/pdf", ext: ".pdf", label: "PDF" },
  { mime: "application/vnd.openxmlformats-officedocument.wordprocessingml.document", ext: ".docx", label: "DOCX" },
  { mime: "application/msword", ext: ".doc", label: "DOC" },
];

interface AttachmentsSectionProps {
  from: "purchase-orders" | "goods-receipts"
  files: FileWithPreview[];
  setFiles: (files: FileWithPreview[]) => void;
}

export function AttachmentsSection({ from, files, setFiles }: AttachmentsSectionProps) {
  const [dragActive, setDragActive] = useState(false);

  const handleFiles = async (newFiles: File[]) => {
    const maxFiles = 10;
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
    const availableSlots = maxFiles - files.length;

    if (availableSlots <= 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: `Maximum ${maxFiles} files already reached for files Documents`,
      });
      return;
    }

    const validFiles: FileWithPreview[] = [];

    for (const file of newFiles) {
      if (validFiles.length >= availableSlots) {
        toast({
          variant: "destructive",
          title: "Error",
          description: `Maximum ${maxFiles} files limit reached`,
        });
        break;
      }

      if (!ACCEPTED_FILE_TYPES.some(type => type.mime === file.type)) {
        toast({
          variant: "destructive",
          title: "Unsupported file type",
          description: `File ${file.name} is not a supported format.`,
        });
        break;
      }

      if (file.size > MAX_FILE_SIZE) {
        toast({
          variant: "destructive",
          title: "Error",
          description: `File ${file.name} exceeds ${formatFileSize(MAX_FILE_SIZE)} limit`,
        });
        break;
      }

      validFiles.push(Object.assign(file, {
        isExisting: false
      }));
    }

    if (validFiles.length > 0) {
      const updatedFiles = [...files, ...validFiles];
      setFiles(updatedFiles)
    }
  };

  const removeFile = async (index: number) => {
    const fileToRemove = files[index];

    if (!fileToRemove) return;

    // Handle existing file removal (requires API call)
    if (fileToRemove.isExisting && fileToRemove.path) {
      let response: { success: boolean; error?: string } = { success: false };

      if (from === 'purchase-orders') {
        response = await deletePODocument(fileToRemove.path);
      }

      if (!response.success) {
        toast({
          variant: "destructive",
          title: "Error",
          description: `Error removing file: ${response.error || 'Unknown error'}`,
        });
        return;
      }

      toast({
        title: "Success",
        description: `The selected file has been deleted successfully.`,
      });
    }

    // Update form state by removing the file
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFiles(droppedFiles);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return Image;
    if (type === 'application/pdf') return FileText;
    return File;
  };

  return (
    <div className="space-y-4">
      {/* Upload Zone */}
      <Card
        className={`border-2 border-dashed transition-colors 
          ${dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-muted-foreground/50"}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrag}
      >
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Upload className="h-10 w-10 text-muted-foreground mb-4" />
          <p className="text-lg font-medium mb-2">Drop files here or click to upload</p>
          <p className="text-sm text-muted-foreground mb-4">
            Support for {ACCEPTED_FILE_TYPES.map(t => t.label).join(', ')} files up to 10MB each
          </p>
          <Input
            type="file"
            multiple
            accept={ACCEPTED_FILE_TYPES.map(type => type.ext).join(',')}
            className="hidden"
            id="file-upload"
            onChange={(e) => handleFiles(Array.from(e.target.files || []))}
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => document.getElementById('file-upload')?.click()}
          >
            <Upload className="mr-2 h-4 w-4" />
            Choose Files
          </Button>
        </CardContent>
      </Card>

      {/* File List */}
      {files?.length > 0 && (
        <>
          {files?.map((file, index) => {
            const FileIcon = getFileIcon(file.type);
            return (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 flex-1">
                      <FileIcon className="h-6 w-6 text-muted-foreground flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{file.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </>
      )}
    </div >
  );
}