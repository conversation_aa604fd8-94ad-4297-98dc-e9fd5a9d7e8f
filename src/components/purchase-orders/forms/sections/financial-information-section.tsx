"use client";

import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import SearchInput from "@/components/pages/general/data-table-search-input";
import { Check, CheckIcon, ChevronLef<PERSON>, ChevronRight, ChevronsUpDown, XIcon } from "lucide-react";
import type { BudgetRevisionView } from "@/types/budgets/budgets.types";
import type { PurchaseOrderFormData } from "@/schemas/purchase-orders";
import type { CurrencyOption, DropdownOption } from "@/types/general.types";
import { searchBudgets, searchSpecificBudgets } from "@/services/budgets/budget.service";

interface FinancialInformationSectionProps {
  form: UseFormReturn<PurchaseOrderFormData>;
  options: {
    currencies: CurrencyOption[];
    departments: DropdownOption[];
  };
}

export function FinancialInformationSection({ form, options }: FinancialInformationSectionProps) {
  // budget revisions search
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [searchResults, setSearchResults] = useState<BudgetRevisionView[]>([]);
  const [selectedBudget, setSelectedBudget] = useState<BudgetRevisionView | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  // department options
  const [searchTerm, setSearchTerm] = useState("");
  const filteredDepartment = options?.departments.filter((department) =>
    department.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    const budgetRevisionId = form.getValues("budget_revision_id");

    const init = async () => {
      if (budgetRevisionId) {
        const result = await searchSpecificBudgets(budgetRevisionId);
        if (result) {
          setSelectedBudget(result);
        }
      }
    };

    if (form.watch("budget_revision_id")) {
      init();
    }
  }, [])

  const onBudgetSearch = async (pageToLoad = page) => {
    setIsSearching(true);

    const result = await searchBudgets(searchQuery, pageToLoad, "DEPARTMENT");
    if (result.success) {
      setSearchResults(result.budget);
      setPage(result.currentPage);
      setTotalPages(result.totalPages);
    }
    setIsSearching(false);
  }

  useEffect(() => {
    onBudgetSearch();
  }, [searchQuery])

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      onBudgetSearch(newPage);
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <FormField
        control={form.control}
        name="currency_code"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Currency *</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.currencies.map((currency) => (
                  <SelectItem key={currency.value} value={currency.value}>
                    {currency.symbol} {currency.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="exchange_rate"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Exchange Rate</FormLabel>
            <FormControl>
              <Input
                type="number"
                step="0.0001"
                min="0"
                placeholder="1.0000"
                {...field}
                onChange={(e) => field.onChange(parseFloat(e.target.value) || 1)}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="project_reference"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Project Reference</FormLabel>
            <FormControl>
              <Input
                placeholder="Enter project reference"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="department_code"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Department Code</FormLabel>
            <FormControl>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full flex justify-between"
                  >
                    {field.value
                      ? options.departments.find(
                        (department) => department.value === field.value
                      )?.label
                      : "Select department"}
                    <ChevronsUpDown className="opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-[--radix-popover-trigger-width]">
                  <Command>
                    <CommandInput
                      placeholder="Search Place of Registration..."
                      onInput={(e) =>
                        setSearchTerm(e.currentTarget.value)
                      }
                      value={searchTerm}
                    />
                    <CommandList>
                      {filteredDepartment.length === 0 ? (
                        <CommandEmpty>No country found.</CommandEmpty>
                      ) : (
                        <CommandGroup>
                          {filteredDepartment.map((department) => (
                            <CommandItem
                              key={department.value}
                              value={department.value}
                              onSelect={() => {
                                field.onChange(department.value);
                              }}
                            >
                              {department.label}
                              <Check
                                className={`ml-auto ${field.value === department.value
                                  ? "opacity-100"
                                  : "opacity-0"
                                  }`}
                              />
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="col-span-2 space-y-4">
        <FormField
          control={form.control}
          name="budget_revision_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Budget Revisions</FormLabel>
              <FormControl>
                <div className="space-y-2">
                  <div className="relative">
                    <SearchInput onSearch={setSearchQuery} value={searchQuery} placeholder="Search budget..." />
                  </div>

                  {selectedBudget && (
                    <div
                      key={selectedBudget.latest_approval_id}
                      className={`flex flex-col items-start gap-1 p-3 cursor-pointer bg-green-50 border rounded-md border-green-200`}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <span className="font-medium">{selectedBudget.budget_code}</span>
                        {selectedBudget.format && (
                          <Badge variant="secondary" className="text-xs">
                            {selectedBudget.format}
                          </Badge>
                        )}
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="ml-auto hover:bg-red-500"
                          onClick={() => {
                            setSelectedBudget(null); // Clear selected budget
                            form.setValue('budget_revision_id', ""); // Clear form field
                          }}
                        >
                          <XIcon className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="text-sm text-gray-500">{selectedBudget.latest_title}</div>
                    </div>
                  )}
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[30%]">Budget Code</TableHead>
                        <TableHead className="w-[20%]">Entity Type</TableHead>
                        <TableHead className="w-[40%]">Title</TableHead>
                        <TableHead className="w-[10%]">Selected</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isSearching ? (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center">
                            <LoadingSpinner />
                          </TableCell>
                        </TableRow>
                      ) : searchResults.length > 0 ? (
                        searchResults.map((result) => (
                          <TableRow
                            key={result.latest_approval_id}
                            onClick={() => {
                              field.onChange(result.latest_approval_id);
                              setSelectedBudget(result);
                            }}
                            className={`text-sm hover:bg-gray-50 cursor-pointer`}
                          >
                            <TableCell className="flex items-center gap-2">
                              <span>{result.budget_code}</span>
                              {result.format && (
                                <Badge variant="secondary" className="text-xs">
                                  {result.format}
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>{result.entity_type}</TableCell>
                            <TableCell className="whitespace-normal break-words max-w-xs">
                              {result.latest_title}
                            </TableCell>
                            <TableCell>
                              {field.value === result.latest_approval_id && (
                                <CheckIcon size={16} className="text-green-500 stroke-[3]" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center">
                            {searchQuery
                              ? "No budget found. Try a different search term."
                              : "Type to search for budget."}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="outline"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page - 1);
                      }}
                      disabled={page <= 1}
                    >
                      <span className="sr-only">Go to previous page</span>
                      <ChevronLeft />
                    </Button>
                    <Button
                      variant="outline"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page + 1);
                      }}
                      disabled={page >= totalPages}
                    >
                      <span className="sr-only">Go to next page</span>
                      <ChevronRight />
                    </Button>
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}