"use client";

import { UseFormReturn } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon, Check, ChevronsUpDown } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import type { PurchaseOrderFormData } from "@/schemas/purchase-orders";
import type { DropdownOption } from "@/types/general.types";
import { useState } from "react";

interface BasicInformationSectionProps {
  form: UseFormReturn<PurchaseOrderFormData>;
  options: {
    entities: DropdownOption[];
  };
}

export function BasicInformationSection({ form, options }: BasicInformationSectionProps) {
  const poDate = form.watch("po_date");
  const [searchTerm, setSearchTerm] = useState("")
  const filteredEntities = options?.entities.filter((entities) =>
    entities.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <FormField
        control={form.control}
        name="entity_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Entity *</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full flex justify-between"
                >
                  {field.value
                    ? options.entities.find(
                      (entities) => entities.value === field.value
                    )?.label
                    : "Select entities"}
                  <ChevronsUpDown className="opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="p-0 w-[--radix-popover-trigger-width]">
                <Command>
                  <CommandInput
                    placeholder="Search Place of Registration..."
                    onInput={(e) =>
                      setSearchTerm(e.currentTarget.value)
                    }
                    value={searchTerm}
                  />
                  <CommandList>
                    {filteredEntities.length === 0 ? (
                      <CommandEmpty>No country found.</CommandEmpty>
                    ) : (
                      <CommandGroup>
                        {filteredEntities.map((entities) => (
                          <CommandItem
                            key={entities.value}
                            value={entities.value}
                            onSelect={() => {
                              field.onChange(entities.value);
                            }}
                          >
                            {entities.label}
                            <Check
                              className={`ml-auto ${field.value === entities.value
                                ? "opacity-100"
                                : "opacity-0"
                                }`}
                            />
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="po_date"
        render={({ field }) => (
          <FormItem>
            <FormLabel>PO Date *</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full pl-3 text-left font-normal",
                      !field.value && "text-muted-foreground"
                    )}
                  >
                    {field.value ? (
                      format(field.value, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={(date) => {
                    if (date) {
                      const utcDate = new Date(Date.UTC(
                        date.getFullYear(),
                        date.getMonth(),
                        date.getDate()
                      ));
                      field.onChange(utcDate);
                    }
                  }}
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem className="md:col-span-2">
            <FormLabel>Title *</FormLabel>
            <FormControl>
              <Input
                placeholder="Enter purchase order title"
                {...field}
                maxLength={255}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem className="md:col-span-2">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Enter purchase order description"
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="expected_delivery_date"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Expected Delivery Date</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full pl-3 text-left font-normal",
                      !field.value && "text-muted-foreground"
                    )}
                  >
                    {field.value ? (
                      format(field.value, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={(date) => {
                    if (date) {
                      const utcDate = new Date(Date.UTC(
                        date.getFullYear(),
                        date.getMonth(),
                        date.getDate()
                      ));
                      field.onChange(utcDate);
                    }
                  }}
                  disabled={[{ before: poDate || new Date() }]}
                />
              </PopoverContent>
            </Popover>
            {poDate && (
              <p className="text-xs text-muted-foreground mt-1">
                Must be after PO date ({format(poDate, "PP")})
              </p>
            )}
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}