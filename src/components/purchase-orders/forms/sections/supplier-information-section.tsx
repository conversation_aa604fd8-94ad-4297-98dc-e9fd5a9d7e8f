"use client";

import { UseFormReturn } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Card, CardContent } from "@/components/ui/card";
import { useEffect, useState } from "react";
import type { Supplier } from "@/types/purchase-orders/purchase-orders.types";
import type { PurchaseOrderFormData } from "@/schemas/purchase-orders";
import type { DropdownOption } from "@/types/general.types";
import { getSupplierById } from "@/services/purchase-orders/suppliers.service";
import { SupplierSelectionAsync } from "@/components/purchase-orders/ui/supplier-selection-async";

interface SupplierInformationSectionProps {
  form: UseFormReturn<PurchaseOrderFormData>;
  options: {
    suppliers: DropdownOption[];
  };
}

export function SupplierInformationSection({ form, options }: SupplierInformationSectionProps) {
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [loadingSupplier, setLoadingSupplier] = useState(false);

  const watchedSupplierId = form.watch("supplier_id");

  useEffect(() => {
    const loadSupplierDetails = async (supplierId: string) => {
      setLoadingSupplier(true);

      const supplier = await getSupplierById(supplierId);
      if (supplier) {
        setSelectedSupplier(supplier);
        if (supplier.currency_code) {
          // Auto-populate currency and exchange rate from supplier
          form.setValue("currency_code", supplier.currency_code);
        }
      }
      setLoadingSupplier(false);
    };

    if (watchedSupplierId) {
      loadSupplierDetails(watchedSupplierId);
    } else {
      setSelectedSupplier(null);
    }
  }, [watchedSupplierId]);

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="supplier_id"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Supplier *</FormLabel>
            <FormControl>
              <SupplierSelectionAsync
                value={field.value}
                onValueChange={field.onChange}
                placeholder="Search for supplier by name or code..."
                className="w-full"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Supplier Details Display */}
      {selectedSupplier && (
        <Card>
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Supplier Details</h4>

            {loadingSupplier ? (
              <div className="text-sm text-muted-foreground">Loading supplier details...</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Name:</span>
                  <div className="text-muted-foreground">{selectedSupplier.name}</div>
                </div>

                <div>
                  <span className="font-medium">Display Name:</span>
                  <div className="text-muted-foreground">
                    {selectedSupplier.display_name || "--"}
                  </div>
                </div>

                <div>
                  <span className="font-medium">Email:</span>
                  <div className="text-muted-foreground">{selectedSupplier.email || "--"}</div>
                </div>

                <div>
                  <span className="font-medium">Phone:</span>
                  <div className="text-muted-foreground">
                    {selectedSupplier.phone || "--"}
                  </div>
                </div>

                <div>
                  <span className="font-medium">Payment Terms:</span>
                  <div className="text-muted-foreground">
                    {selectedSupplier.payment_terms ? `${selectedSupplier.payment_terms} days` : "--"}
                  </div>
                </div>

                <div>
                  <span className="font-medium">Currency:</span>
                  <div className="text-muted-foreground">{selectedSupplier.currency_code || "--"}</div>
                </div>

                {selectedSupplier.billing_address && (
                  <div className="md:col-span-2">
                    <span className="font-medium">Billing Address:</span>
                    <div className="text-muted-foreground">
                      {selectedSupplier.billing_address.address_line_1 || selectedSupplier.billing_address.street_address}<br />
                      {selectedSupplier.billing_address.address_line_2 && (
                        <>{selectedSupplier.billing_address.address_line_2}<br /></>
                      )}
                      {selectedSupplier.billing_address.city}
                      {selectedSupplier.billing_address.state && `, ${selectedSupplier.billing_address.state}`}
                      {selectedSupplier.billing_address.postal_code && ` ${selectedSupplier.billing_address.postal_code}`}<br />
                      {selectedSupplier.billing_address.country}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}