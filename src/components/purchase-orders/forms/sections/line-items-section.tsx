"use client";

import { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { nanoid } from "nanoid";
import { FormField, FormItem, FormControl, FormMessage, Form, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { CurrencyDisplay } from "@/components/purchase-orders/ui";
import { purchaseOrderItemSchema, type PurchaseOrderItemFormData } from "@/schemas/purchase-orders";
import type { DropdownOption, GlAccountOption, TaxCodeOption } from "@/types/general.types";

interface LineItemsSectionProps {
  currency_code: string;
  options: {
    uoms: DropdownOption[];
    taxCodes: TaxCodeOption[];
    glAccounts: GlAccountOption[];
    budgetItems: DropdownOption[];
  };
  calculations: {
    calculateLineTotal: (item: { quantity: number; unit_price: number; tax_rate: number }) => number;
  };
  open: boolean
  onClose: () => void
  onSave: (data: PurchaseOrderItemFormData) => void
  defaultValues?: PurchaseOrderItemFormData;
}

export function LineItemsSection({
  currency_code,
  options,
  calculations,
  open,
  onClose,
  onSave,
  defaultValues
}: LineItemsSectionProps) {
  const form = useForm<PurchaseOrderItemFormData>({
    resolver: zodResolver(purchaseOrderItemSchema),
    mode: 'onChange',
  })

  useEffect(() => {
    if (open) {
      form.reset(defaultValues || {
        field_id: nanoid(),
        item_code: "",
        description: "",
        quantity: 0,
        uom: "",
        unit_price: 0,
        discount_percentage: 0,
        tax_code: "",
        tax_rate: 0,
        gl_account_code: "",
        budget_item_id: ""
      })
    }
  }, [open])

  const onSubmit = (data: PurchaseOrderItemFormData) => {
    onSave(data)
    onClose()
  }

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
        <SheetHeader>
          <SheetTitle>
            Add or edit Line Item for this purchase order
          </SheetTitle>
          <SheetDescription>Fill in the details below.</SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form className="overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="item_code"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Item Code</FormLabel>
                    <FormControl>
                      <Input placeholder="Item Code" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Description"
                        className="min-h-[60px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Quantity"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="uom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Of Measure</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="UOM" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {options.uoms.map((uom) => (
                          <SelectItem key={uom.value} value={uom.value}>
                            {uom.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Unit Price"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discount_percentage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>discount_percentage</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tax_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Code</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Auto-populate tax rate
                        const taxCode = options.taxCodes.find(t => t.value === value);
                        if (taxCode) {
                          form.setValue(`tax_rate`, taxCode.rate);
                        }
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Tax" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No Tax</SelectItem>
                        {options.taxCodes.map((tax) => (
                          <SelectItem key={tax.value} value={tax.value}>
                            {tax.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tax_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Rate</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        readOnly
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormItem>
                <FormLabel>Line Total</FormLabel>
                <div className="border border-input bg-background rounded-md px-3 py-1 h-9 flex items-center">
                  <CurrencyDisplay
                    amount={calculations.calculateLineTotal(form.getValues())}
                    currency={currency_code}
                    showCurrency={false}
                  />
                </div>
              </FormItem>

              <FormField
                control={form.control}
                name="gl_account_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gl Account</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="GL Account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No GL Account</SelectItem>
                        {options.glAccounts.map((group) => (
                          <SelectGroup key={group.group}>
                            <SelectLabel>{group.group}</SelectLabel>
                            {group.list.map((item) => (
                              <SelectItem key={item.value} value={item.value}>
                                {item.label}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="budget_item_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget Item</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Budget Item" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No Budget Item</SelectItem>
                        {options.budgetItems.length === 0 ? (
                          <SelectItem value="warning" disabled>
                            Please select a budget revision to see items
                          </SelectItem>
                        ) : (
                          options.budgetItems.map((item) => (
                            <SelectItem key={item.value} value={item.value}>
                              {item.label}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
              <Button type="button" onClick={form.handleSubmit(onSubmit)}>Save</Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}