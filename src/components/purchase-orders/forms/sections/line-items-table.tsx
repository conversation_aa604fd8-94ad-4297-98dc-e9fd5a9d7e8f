"use client";

import { useState } from "react";
import { UseFormReturn, FieldArrayWithId } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CurrencyDisplay } from "@/components/purchase-orders/ui";
import { Pencil, Plus, Trash2 } from "lucide-react";
import type { PurchaseOrderFormData, PurchaseOrderItemFormData } from "@/schemas/purchase-orders";
import type { DropdownOption, GlAccountOption, TaxCodeOption } from "@/types/general.types";
import { LineItemsSection } from "@/components/purchase-orders/forms/sections/line-items-section";

interface LineItemsTableProps {
    form: UseFormReturn<PurchaseOrderFormData>;
    fields: FieldArrayWithId<PurchaseOrderFormData, "items", "field_id">[];
    options: {
        uoms: DropdownOption[];
        taxCodes: TaxCodeOption[];
        glAccounts: GlAccountOption[];
        budgetItems: DropdownOption[];
    };
    calculations: {
        calculateLineTotal: (item: { quantity: number; unit_price: number; tax_rate: number }) => number;
    };
    onAdd: (item: PurchaseOrderItemFormData) => void;
    onEdit: (item: PurchaseOrderItemFormData) => void;
    onRemove: (index: number) => void;
}

export function LineItemsTable({
    form,
    fields,
    options,
    calculations,
    onAdd,
    onEdit,
    onRemove,
}: LineItemsTableProps) {
    const [modalOpen, setModalOpen] = useState(false)
    const [editingItem, setEditingItem] = useState<PurchaseOrderItemFormData | undefined>(undefined)

    const currency = form.watch("currency_code");

    return (
        <div className="grid grid-cols-1 gap-4">
            <div className="flex justify-end">
                <div className="flex gap-2">
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => { setEditingItem(undefined); setModalOpen(true) }}
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Line Item
                    </Button>
                </div>
            </div>

            <Table className="rounded-lg overflow-x-auto">
                <TableHeader>
                    <TableRow>
                        <TableHead>Line #</TableHead>
                        <TableHead>Item Code</TableHead>
                        <TableHead>Description *</TableHead>
                        <TableHead>Quantity *</TableHead>
                        <TableHead>UOM *</TableHead>
                        <TableHead>Unit Price *</TableHead>
                        <TableHead>Tax Code *</TableHead>
                        <TableHead>Tax Rate *</TableHead>
                        <TableHead>Line Total</TableHead>
                        <TableHead>GL Account</TableHead>
                        <TableHead>Budget Item</TableHead>
                        <TableHead>Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {fields.map((field, index) => (
                        <TableRow key={field.field_id}>
                            <TableCell className="text-center font-medium">{index + 1}</TableCell>
                            <TableCell>{form.watch(`items.${index}.item_code`) ?? "--"}</TableCell>
                            <TableCell>{form.watch(`items.${index}.description`) ?? "--"}</TableCell>
                            <TableCell>{form.watch(`items.${index}.quantity`) ?? "--"}</TableCell>
                            <TableCell>{form.watch(`items.${index}.uom`) ?? "--"}</TableCell>
                            <TableCell>{form.watch(`items.${index}.unit_price`) ?? "--"}</TableCell>
                            <TableCell>{form.watch(`items.${index}.tax_code`) ?? "--"}</TableCell>
                            <TableCell>{form.watch(`items.${index}.tax_rate`) ?? "--"}</TableCell>
                            <TableCell>
                                <CurrencyDisplay
                                    amount={calculations.calculateLineTotal(field)}
                                    currency={currency}
                                    showCurrency={false}
                                />
                            </TableCell>
                            <TableCell>
                                {form.watch(`items.${index}.gl_account_code`)
                                    ? options.glAccounts
                                        .flatMap(group => group.list)
                                        .find(item => item.value === form.watch(`items.${index}.gl_account_code`))?.label
                                    : "--"}
                            </TableCell>
                            <TableCell>
                                {(form.watch(`items.${index}.budget_item_id`) && form.watch(`items.${index}.budget_item_id`) !== "none")
                                    ? options.budgetItems.find(item => item.value === form.watch(`items.${index}.budget_item_id`))?.label
                                    : "--"}
                            </TableCell>
                            <TableCell>
                                <Button type='button' variant="ghost" size="icon" onClick={() => { setEditingItem(field); setModalOpen(true) }}>
                                    <Pencil className="h-4 w-4 text-blue-500" />
                                </Button>
                                <Button type='button' variant="ghost" size="icon" onClick={() => onRemove(index)}>
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>

            <LineItemsSection
                currency_code={currency}
                options={options}
                calculations={calculations}
                open={modalOpen}
                onClose={() => setModalOpen(false)}
                onSave={(data) => {
                    editingItem ? onEdit(data) : onAdd(data)
                }}
                defaultValues={editingItem}
            />
        </div>
    );
}