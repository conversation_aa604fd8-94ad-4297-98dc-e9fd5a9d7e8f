"use client";

import { useState } from 'react';
import { Check, ChevronsUpDown, Building2 } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import type { DropdownOption } from "@/types/general.types";

interface SupplierSelectionProps {
  suppliers: DropdownOption[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function SupplierSelection({
  suppliers,
  value,
  onValueChange,
  placeholder = "Select supplier...",
  className,
  disabled = false,
}: SupplierSelectionProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const selectedSupplier = suppliers.find(supplier => supplier.value === value);

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
          disabled={disabled}
        >
          {selectedSupplier ? (
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span className="truncate">{selectedSupplier.label}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="start">
        <Command>
          <CommandInput 
            placeholder="Search suppliers..." 
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandEmpty>No supplier found.</CommandEmpty>
          <CommandGroup className="max-h-64 overflow-auto">
            {filteredSuppliers.map((supplier) => (
              <CommandItem
                key={supplier.value}
                value={supplier.value}
                onSelect={() => {
                  onValueChange(supplier.value === value ? "" : supplier.value);
                  setOpen(false);
                }}
                className="flex items-center gap-3 p-3"
              >
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {supplier.label}
                  </p>
                </div>
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === supplier.value ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}