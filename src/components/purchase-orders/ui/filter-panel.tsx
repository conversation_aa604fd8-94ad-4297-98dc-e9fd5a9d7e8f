"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronDown, ChevronUp, Filter, X, CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import type { PurchaseOrdersTableFilters } from "@/types/purchase-orders/purchase-orders.types";
import { getActiveSuppliers } from "@/services/purchase-orders/suppliers.service";
import { getEntities, getDepartments } from "@/services/purchase-orders/purchase-orders.service";
import type { DropdownOption } from "@/types/general.types";

interface FilterPanelProps {
  filters: PurchaseOrdersTableFilters;
  onFiltersChange: (filters: PurchaseOrdersTableFilters) => void;
  onReset: () => void;
}

export function FilterPanel({ filters, onFiltersChange, onReset }: FilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [suppliers, setSuppliers] = useState<DropdownOption[]>([]);
  const [entities, setEntities] = useState<DropdownOption[]>([]);
  const [departments, setDepartments] = useState<DropdownOption[]>([]);

  useEffect(() => {
    loadFilterOptions();
  }, []);

  const loadFilterOptions = async () => {
    const [suppliersData, entitiesData, departmentsData] = await Promise.all([
      getActiveSuppliers(),
      getEntities(),
      getDepartments(),
    ]);
    setSuppliers(suppliersData);
    setEntities(entitiesData);
    setDepartments(departmentsData);
  };

  const handleStatusChange = (status: string, checked: boolean) => {
    const newStatuses = checked
      ? [...filters.status, status as any]
      : filters.status.filter(s => s !== status);
    onFiltersChange({ ...filters, status: newStatuses });
  };

  const handleSupplierChange = (supplierId: string, checked: boolean) => {
    const newSuppliers = checked
      ? [...filters.supplier_ids, supplierId]
      : filters.supplier_ids.filter(id => id !== supplierId);
    onFiltersChange({ ...filters, supplier_ids: newSuppliers });
  };

  const handleEntityChange = (entityId: string, checked: boolean) => {
    const newEntities = checked
      ? [...filters.entity_ids, entityId]
      : filters.entity_ids.filter(id => id !== entityId);
    onFiltersChange({ ...filters, entity_ids: newEntities });
  };

  const handleDepartmentChange = (deptCode: string, checked: boolean) => {
    const newDepartments = checked
      ? [...filters.department_codes, deptCode]
      : filters.department_codes.filter(code => code !== deptCode);
    onFiltersChange({ ...filters, department_codes: newDepartments });
  };

  const statuses = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'SUBMITTED', label: 'Submitted' },
    { value: 'APPROVED', label: 'Approved' },
    { value: 'SENT', label: 'Sent' },
    { value: 'PARTIALLY_RECEIVED', label: 'Partially Received' },
    { value: 'RECEIVED', label: 'Received' },
    { value: 'CANCELLED', label: 'Cancelled' },
    { value: 'CLOSED', label: 'Closed' },
  ];

  const activeFiltersCount =
    filters.status.length +
    filters.supplier_ids.length +
    filters.entity_ids.length +
    filters.department_codes.length +
    (filters.date_from ? 1 : 0) +
    (filters.date_to ? 1 : 0) +
    (filters.amount_min ? 1 : 0) +
    (filters.amount_max ? 1 : 0);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <CardTitle>Filters</CardTitle>
            {activeFiltersCount > 0 && (
              <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-medium text-white bg-primary rounded-full">
                {activeFiltersCount}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
              >
                <X className="mr-1 h-4 w-4" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Status Filter */}
          <div className="space-y-2">
            <Label>Status</Label>
            <ScrollArea className="h-32 rounded-md border p-2">
              <div className="space-y-2">
                {statuses.map(status => (
                  <div key={status.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status.value}`}
                      checked={filters.status.includes(status.value as any)}
                      onCheckedChange={(checked) => handleStatusChange(status.value, !!checked)}
                    />
                    <Label
                      htmlFor={`status-${status.value}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {status.label}
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Supplier Filter */}
          <div className="space-y-2">
            <Label>Suppliers</Label>
            <ScrollArea className="h-32 rounded-md border p-2">
              <div className="space-y-2">
                {suppliers.map(supplier => (
                  <div key={supplier.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`supplier-${supplier.value}`}
                      checked={filters.supplier_ids.includes(supplier.value)}
                      onCheckedChange={(checked) => handleSupplierChange(supplier.value, !!checked)}
                    />
                    <Label
                      htmlFor={`supplier-${supplier.value}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {supplier.label}
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Date Range Filter */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>From Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.date_from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.date_from ? (
                      format(new Date(filters.date_from), "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.date_from ? new Date(filters.date_from) : undefined}
                    onSelect={(date) =>
                      onFiltersChange({
                        ...filters,
                        date_from: date ? format(date, "yyyy-MM-dd") : undefined
                      })
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>To Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.date_to && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.date_to ? (
                      format(new Date(filters.date_to), "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.date_to ? new Date(filters.date_to) : undefined}
                    onSelect={(date) =>
                      onFiltersChange({
                        ...filters,
                        date_to: date ? format(date, "yyyy-MM-dd") : undefined
                      })
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Amount Range Filter */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Min Amount</Label>
              <Input
                type="number"
                placeholder="0"
                value={filters.amount_min || ""}
                onChange={(e) =>
                  onFiltersChange({
                    ...filters,
                    amount_min: e.target.value ? parseFloat(e.target.value) : undefined
                  })
                }
              />
            </div>

            <div className="space-y-2">
              <Label>Max Amount</Label>
              <Input
                type="number"
                placeholder="999999"
                value={filters.amount_max || ""}
                onChange={(e) =>
                  onFiltersChange({
                    ...filters,
                    amount_max: e.target.value ? parseFloat(e.target.value) : undefined
                  })
                }
              />
            </div>
          </div>

          {/* Entity Filter */}
          <div className="space-y-2">
            <Label>Entities</Label>
            <ScrollArea className="h-24 rounded-md border p-2">
              <div className="space-y-2">
                {entities.map(entity => (
                  <div key={entity.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`entity-${entity.value}`}
                      checked={filters.entity_ids.includes(entity.value)}
                      onCheckedChange={(checked) => handleEntityChange(entity.value, !!checked)}
                    />
                    <Label
                      htmlFor={`entity-${entity.value}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {entity.label}
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Department Filter */}
          <div className="space-y-2">
            <Label>Departments</Label>
            <ScrollArea className="h-24 rounded-md border p-2">
              <div className="space-y-2">
                {departments.map(dept => (
                  <div key={dept.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`dept-${dept.value}`}
                      checked={filters.department_codes.includes(dept.value)}
                      onCheckedChange={(checked) => handleDepartmentChange(dept.value, !!checked)}
                    />
                    <Label
                      htmlFor={`dept-${dept.value}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {dept.label}
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      )}
    </Card>
  );
}