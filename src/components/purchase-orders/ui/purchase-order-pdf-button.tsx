"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";

interface PurchaseOrderPDFButtonProps {
  purchaseOrder: PurchaseOrder;
  className?: string;
}

export function PurchaseOrderPDFButton({
  purchaseOrder,
  className
}: PurchaseOrderPDFButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleDownload = async () => {
    try {
      setIsGenerating(true);
      
      // Import the PDF generator utility dynamically
      const { generatePurchaseOrderPDF } = await import("@/utils/purchase-order-pdf-generator");
      
      // Generate and download the PDF
      await generatePurchaseOrderPDF(purchaseOrder);
      
      toast({
        title: "Success",
        description: "Purchase Order PDF has been downloaded successfully",
      });
    } catch (error) {
      console.error("Failed to generate Purchase Order PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate Purchase Order PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={isGenerating}
      variant="outline"
      size="sm"
      className={className}
    >
      <Download className="h-4 w-4" />
      {isGenerating ? "Generating..." : "Download PDF"}
    </Button>
  );
}