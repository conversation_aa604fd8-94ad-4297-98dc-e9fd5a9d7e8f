import { useState } from 'react';
import { FileText, Download, Eye, X, Image, FileSpreadsheet, File } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';

interface DocumentFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
  uploadedBy: string;
}

interface DocumentPreviewProps {
  document: DocumentFile;
  className?: string;
}

const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) return Image;
  if (type.includes('spreadsheet') || type.includes('excel')) return FileSpreadsheet;
  if (type === 'application/pdf') return FileText;
  return File;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export function DocumentPreview({ document, className }: DocumentPreviewProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const FileIcon = getFileIcon(document.type);
  
  const canPreview = document.type.startsWith('image/') || document.type === 'application/pdf';

  const handleDownload = () => {
    const link = globalThis.document.createElement('a');
    link.href = document.url;
    link.download = document.name;
    globalThis.document.body.appendChild(link);
    link.click();
    globalThis.document.body.removeChild(link);
  };

  return (
    <div className={cn("border rounded-lg p-4 bg-white", className)}>
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0">
          <FileIcon className="h-8 w-8 text-gray-400" />
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {document.name}
          </h4>
          <p className="text-xs text-gray-500 mt-1">
            {formatFileSize(document.size)} • Uploaded by {document.uploadedBy}
          </p>
          <p className="text-xs text-gray-500">
            {new Date(document.uploadedAt).toLocaleDateString()}
          </p>
        </div>

        <div className="flex items-center gap-2">
          {canPreview && (
            <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh]">
                <DialogHeader>
                  <DialogTitle className="flex items-center justify-between">
                    {document.name}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsPreviewOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </DialogTitle>
                </DialogHeader>
                <div className="flex-1 overflow-auto">
                  {document.type.startsWith('image/') ? (
                    <img
                      src={document.url}
                      alt={document.name}
                      className="max-w-full h-auto mx-auto"
                    />
                  ) : document.type === 'application/pdf' ? (
                    <iframe
                      src={document.url}
                      className="w-full h-96 border-0"
                      title={document.name}
                    />
                  ) : null}
                </div>
              </DialogContent>
            </Dialog>
          )}
          
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}