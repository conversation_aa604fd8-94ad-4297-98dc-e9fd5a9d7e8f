import { Circle, HelpCircle } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import type { SupplierStatus } from '@/types/purchase-orders/purchase-orders.types';

interface SupplierStatusBadgeProps {
  status: SupplierStatus; // should be `SupplierStatus | null` to match handling
  className?: string;
}

// Ensure this matches your actual enum values
const statusConfig: { [key in Exclude<SupplierStatus, null>]: { color: string; label: string } } = {
  ACTIVE: {
    color: "fill-green-500 text-green-500",
    label: "Active"
  },
  INACTIVE: {
    color: "fill-gray-500 text-gray-500",
    label: "Inactive"
  },
  SUSPENDED: {
    color: "fill-amber-500 text-amber-500",
    label: "Suspended"
  },
  BLACKLISTED: {
    color: "fill-red-500 text-red-500",
    label: "Blacklisted"
  }
};

export function SupplierStatusBadge({ status, className }: SupplierStatusBadgeProps) {
  const config = status ? statusConfig[status] : null;

  if (!config) {
    return (
      <Button
        type="button"
        variant="outline"
        className={cn(
          "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 text-muted-foreground cursor-default hover:bg-transparent",
          className
        )}
      >
        <HelpCircle className="h-4 w-4" />
        Unknown
      </Button>
    );
  }

  return (
    <Button
      type="button"
      variant="outline"
      className={cn(
        "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
        className
      )}
    >
      <Circle className={cn("h-2 w-2", config.color)} />
      {config.label}
    </Button>
  );
}