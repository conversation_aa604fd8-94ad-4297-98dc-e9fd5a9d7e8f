"use client";

import { useState, useEffect, useCallback } from 'react';
import { Check, ChevronsUpDown, Building2, Loader2 } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import type { DropdownOption } from "@/types/general.types";
import { searchSuppliersForDropdown, getSupplierById } from "@/services/purchase-orders/suppliers.service";
import { useDebounce } from "@/hooks/use-debounce";

interface SupplierSelectionAsyncProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function SupplierSelectionAsync({
  value,
  onValueChange,
  placeholder = "Search for supplier...",
  className,
  disabled = false,
}: SupplierSelectionAsyncProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [suppliers, setSuppliers] = useState<DropdownOption[]>([]);
  const [selectedSupplier, setSelectedSupplier] = useState<DropdownOption | null>(null);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(false);
  
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Load selected supplier details when value changes
  useEffect(() => {
    const loadSelectedSupplier = async () => {
      if (value && !selectedSupplier) {
        setInitialLoading(true);
        try {
          const supplier = await getSupplierById(value);
          if (supplier) {
            setSelectedSupplier({
              value: supplier.id,
              label: `${supplier.supplier_code ? supplier.supplier_code + ' - ' : ''}${supplier.display_name || supplier.name}${supplier.status === 'INACTIVE' ? ' (Inactive)' : ''}`
            });
          }
        } catch (error) {
          console.error("Error loading selected supplier:", error);
        } finally {
          setInitialLoading(false);
        }
      } else if (!value) {
        setSelectedSupplier(null);
      }
    };

    loadSelectedSupplier();
  }, [value]);

  // Search suppliers when search term changes
  useEffect(() => {
    const searchSuppliers = async () => {
      if (debouncedSearchTerm.length < 2) {
        setSuppliers([]);
        return;
      }

      setLoading(true);
      try {
        const results = await searchSuppliersForDropdown(debouncedSearchTerm);
        setSuppliers(results);
      } catch (error) {
        console.error("Error searching suppliers:", error);
        setSuppliers([]);
      } finally {
        setLoading(false);
      }
    };

    searchSuppliers();
  }, [debouncedSearchTerm]);

  const handleSelect = useCallback((supplierId: string) => {
    const supplier = suppliers.find(s => s.value === supplierId);
    if (supplier) {
      setSelectedSupplier(supplier);
      onValueChange(supplierId);
      setOpen(false);
      setSearchTerm("");
    }
  }, [suppliers, onValueChange]);

  const handleClear = useCallback(() => {
    setSelectedSupplier(null);
    onValueChange("");
    setSearchTerm("");
  }, [onValueChange]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
          disabled={disabled || initialLoading}
        >
          {initialLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-muted-foreground">Loading...</span>
            </div>
          ) : selectedSupplier ? (
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span className="truncate">{selectedSupplier.label}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput 
            placeholder="Type at least 2 characters to search..." 
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandList>
            {searchTerm.length < 2 ? (
              <CommandEmpty>Type at least 2 characters to search suppliers</CommandEmpty>
            ) : loading ? (
              <CommandEmpty>
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Searching suppliers...
                </div>
              </CommandEmpty>
            ) : suppliers.length === 0 ? (
              <CommandEmpty>No suppliers found</CommandEmpty>
            ) : (
              <CommandGroup>
                {suppliers.map((supplier) => (
                  <CommandItem
                    key={supplier.value}
                    value={supplier.value}
                    onSelect={() => handleSelect(supplier.value)}
                    className="flex items-center gap-3 p-3"
                  >
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {supplier.label}
                      </p>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedSupplier?.value === supplier.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}