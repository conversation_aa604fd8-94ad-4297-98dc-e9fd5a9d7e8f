import { CheckCircle, Clock, XCircle, AlertCircle } from 'lucide-react';
import { cn } from "@/lib/utils";

interface ApprovalStep {
  id: string;
  name: string;
  status: 'pending' | 'approved' | 'rejected' | 'current';
  approver?: string;
  approvedAt?: string;
  comments?: string;
}

interface ApprovalStatusProps {
  steps: ApprovalStep[];
  className?: string;
}

const statusConfig = {
  pending: {
    icon: Clock,
    color: "text-gray-400",
    bgColor: "bg-gray-100",
  },
  current: {
    icon: AlertCircle,
    color: "text-blue-600",
    bgColor: "bg-blue-100",
  },
  approved: {
    icon: CheckCircle,
    color: "text-green-600",
    bgColor: "bg-green-100",
  },
  rejected: {
    icon: XCircle,
    color: "text-red-600",
    bgColor: "bg-red-100",
  },
};

export function ApprovalStatus({ steps, className }: ApprovalStatusProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {steps.map((step, index) => {
        const config = statusConfig[step.status];
        const Icon = config.icon;
        const isLast = index === steps.length - 1;

        return (
          <div key={step.id} className="flex items-start gap-4">
            {/* Icon and Line */}
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full",
                  config.bgColor
                )}
              >
                <Icon className={cn("h-4 w-4", config.color)} />
              </div>
              {!isLast && (
                <div className="mt-2 h-6 w-0.5 bg-gray-200" />
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h4 className="text-sm font-medium text-gray-900">
                  {step.name}
                </h4>
                <span
                  className={cn(
                    "text-xs px-2 py-1 rounded-full capitalize",
                    config.bgColor,
                    config.color
                  )}
                >
                  {step.status}
                </span>
              </div>

              {step.approver && (
                <p className="text-sm text-gray-600 mt-1">
                  Approver: {step.approver}
                </p>
              )}

              {step.approvedAt && (
                <p className="text-xs text-gray-500 mt-1">
                  {new Date(step.approvedAt).toLocaleString()}
                </p>
              )}

              {step.comments && (
                <p className="text-sm text-gray-700 mt-2 p-2 bg-gray-50 rounded">
                  {step.comments}
                </p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}