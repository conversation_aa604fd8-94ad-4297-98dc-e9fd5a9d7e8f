import { Circle } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import type { POStatus } from '@/types/purchase-orders/purchase-orders.types';

interface POStatusBadgeProps {
  status: POStatus | string | undefined;
  className?: string;
}

const statusConfig: Record<string, { color: string; label: string }> = {
  DRAFT: {
    color: "fill-gray-500 text-gray-500",
    label: "Draft"
  },
  SUBMITTED: {
    color: "fill-blue-500 text-blue-500",
    label: "Submitted"
  },
  PENDING_APPROVAL: {
    color: "fill-yellow-500 text-yellow-500",
    label: "Submitted"
  },
  APPROVED: {
    color: "fill-green-500 text-green-500",
    label: "Approved"
  },
  SENT: {
    color: "fill-purple-500 text-purple-500",
    label: "Sent"
  },
  PARTIALLY_RECEIVED: {
    color: "fill-amber-500 text-amber-500",
    label: "Partially Received"
  },
  RECEIVED: {
    color: "fill-emerald-500 text-emerald-500",
    label: "Received"
  },
  CANCELLED: {
    color: "fill-red-500 text-red-500",
    label: "Cancelled"
  },
  CLOSED: {
    color: "fill-slate-500 text-slate-500",
    label: "Closed"
  },
};

export function POStatusBadge({ status, className }: POStatusBadgeProps) {
  // Handle case where status is undefined first
  if (!status) {
    return (
      <Button
        variant="outline"
        className={cn(
          "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
          className
        )}
      >
        <Circle className={cn("h-2 w-2", "fill-gray-400 text-gray-400")} />
        Unknown
      </Button>
    );
  }

  const config = statusConfig[status];

  // Handle case where status is not found in config
  if (!config) {
    return (
      <Button
        variant="outline"
        className={cn(
          "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
          className
        )}
      >
        <Circle className={cn("h-2 w-2", "fill-gray-400 text-gray-400")} />
        {status || "Unknown"}
      </Button>
    );
  }

  return (
    <Button
      variant="outline"
      className={cn(
        "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
        className
      )}
    >
      <Circle className={cn("h-2 w-2", config.color)} />
      {config.label}
    </Button>
  );
}