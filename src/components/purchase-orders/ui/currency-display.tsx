import { cn } from "@/lib/utils";

interface CurrencyDisplayProps {
  amount: number | undefined | null;
  currency: string | undefined;
  className?: string;
  showCurrency?: boolean;
}

const currencySymbols: Record<string, string> = {
  USD: "$",
  EUR: "€",
  GBP: "£",
  SGD: "S$",
  MYR: "RM",
  JPY: "¥",
  CNY: "¥",
  AUD: "A$",
  CAD: "C$",
  CHF: "CHF",
  HKD: "HK$",
  INR: "₹",
  KRW: "₩",
  THB: "฿",
  TWD: "NT$",
};

export function CurrencyDisplay({ 
  amount, 
  currency, 
  className,
  showCurrency = true
}: CurrencyDisplayProps) {
  // Handle null/undefined values
  if (amount === null || amount === undefined) {
    return <span className={cn("font-medium text-gray-400", className)}>-</span>;
  }
  
  const safeCurrency = currency || 'SGD';
  const symbol = currencySymbols[safeCurrency] || safeCurrency;
  
  const formatAmount = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  return (
    <span className={cn("font-medium", className)}>
      {symbol}{formatAmount(amount)}
      {showCurrency && safeCurrency !== symbol && (
        <span className="ml-1 text-muted-foreground text-sm">
          {safeCurrency}
        </span>
      )}
    </span>
  );
}