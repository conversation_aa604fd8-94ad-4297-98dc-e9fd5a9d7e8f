// Quick Actions Component
// Provides quick access to common user and role management actions

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  UserPlus, 
  ShieldPlus, 
  Download, 
  Upload, 
  Settings,
  Zap,
  Mail,
  Users,
  Shield
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { PermissionGuard } from '@/components/rbac'

export function QuickActions() {
  const { toast } = useToast()
  
  const [createUserDialog, setCreateUserDialog] = useState(false)
  const [createRoleDialog, setCreateRoleDialog] = useState(false)
  const [bulkActionDialog, setBulkActionDialog] = useState(false)
  
  // Form states
  const [newUser, setNewUser] = useState({
    email: '',
    full_name: '',
    role_name: '',
    send_invitation: true
  })
  
  const [newRole, setNewRole] = useState({
    name: '',
    description: ''
  })

  const handleCreateUser = async () => {
    try {
      // Validation
      if (!newUser.email || !newUser.full_name) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields',
          variant: 'destructive'
        })
        return
      }

      // API call to create user
      toast({
        title: 'User Created',
        description: `${newUser.full_name} has been created successfully.`
      })
      
      setCreateUserDialog(false)
      setNewUser({
        email: '',
        full_name: '',
        role_name: '',
        send_invitation: true
      })
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to create user',
        variant: 'destructive'
      })
    }
  }

  const handleCreateRole = async () => {
    try {
      // Validation
      if (!newRole.name) {
        toast({
          title: 'Validation Error',
          description: 'Please enter a role name',
          variant: 'destructive'
        })
        return
      }

      // API call to create role
      toast({
        title: 'Role Created',
        description: `Role "${newRole.name}" has been created successfully.`
      })
      
      setCreateRoleDialog(false)
      setNewRole({
        name: '',
        description: ''
      })
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to create role',
        variant: 'destructive'
      })
    }
  }

  const handleBulkAction = (action: string) => {
    toast({
      title: 'Bulk Action',
      description: `${action} operation initiated`
    })
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button>
            <Zap className="mr-2 h-4 w-4" />
            Quick Actions
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>User Management</DropdownMenuLabel>
          
          <PermissionGuard module="SYSTEM" action="CREATE">
            <DropdownMenuItem onClick={() => setCreateUserDialog(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Create New User
            </DropdownMenuItem>
          </PermissionGuard>
          
          <PermissionGuard module="SYSTEM" action="CREATE">
            <DropdownMenuItem onClick={() => setCreateRoleDialog(true)}>
              <ShieldPlus className="mr-2 h-4 w-4" />
              Create New Role
            </DropdownMenuItem>
          </PermissionGuard>
          
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Bulk Operations</DropdownMenuLabel>
          
          <PermissionGuard module="SYSTEM" action="UPDATE">
            <DropdownMenuItem onClick={() => setBulkActionDialog(true)}>
              <Users className="mr-2 h-4 w-4" />
              Bulk User Actions
            </DropdownMenuItem>
          </PermissionGuard>
          
          <PermissionGuard module="SYSTEM" action="CREATE">
            <DropdownMenuItem onClick={() => handleBulkAction('Import Users')}>
              <Upload className="mr-2 h-4 w-4" />
              Import Users
            </DropdownMenuItem>
          </PermissionGuard>
          
          <PermissionGuard module="SYSTEM" action="EXPORT">
            <DropdownMenuItem onClick={() => handleBulkAction('Export Users')}>
              <Download className="mr-2 h-4 w-4" />
              Export Users
            </DropdownMenuItem>
          </PermissionGuard>
          
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Communications</DropdownMenuLabel>
          
          <PermissionGuard module="SYSTEM" action="UPDATE">
            <DropdownMenuItem onClick={() => handleBulkAction('Send Notifications')}>
              <Mail className="mr-2 h-4 w-4" />
              Send Notifications
            </DropdownMenuItem>
          </PermissionGuard>
          
          <DropdownMenuSeparator />
          
          <PermissionGuard module="SYSTEM" action="UPDATE">
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              System Settings
            </DropdownMenuItem>
          </PermissionGuard>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Create User Dialog */}
      <Dialog open={createUserDialog} onOpenChange={setCreateUserDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
            <DialogDescription>
              Add a new user to the system. An invitation email will be sent if enabled.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name *</Label>
              <Input
                id="fullName"
                placeholder="John Doe"
                value={newUser.full_name}
                onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
              />
            </div>
            
            {/* User type field removed - all users created here are ADMIN type */}
            
            <div className="space-y-2">
              <Label htmlFor="roleName">Initial Role (Optional)</Label>
              <Input
                id="roleName"
                placeholder="Role name"
                value={newUser.role_name}
                onChange={(e) => setNewUser({ ...newUser, role_name: e.target.value })}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateUserDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateUser}>
              <UserPlus className="mr-2 h-4 w-4" />
              Create User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Role Dialog */}
      <Dialog open={createRoleDialog} onOpenChange={setCreateRoleDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
            <DialogDescription>
              Define a new role with specific permissions for your organization.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="roleName">Role Name *</Label>
              <Input
                id="roleName"
                placeholder="MANAGER"
                value={newRole.name}
                onChange={(e) => setNewRole({ ...newRole, name: e.target.value.toUpperCase() })}
              />
              <p className="text-xs text-muted-foreground">
                Use uppercase letters, numbers, and underscores only
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="roleDescription">Description</Label>
              <Textarea
                id="roleDescription"
                placeholder="Describe the role's purpose and responsibilities..."
                value={newRole.description}
                onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateRoleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateRole}>
              <ShieldPlus className="mr-2 h-4 w-4" />
              Create Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Actions Dialog */}
      <Dialog open={bulkActionDialog} onOpenChange={setBulkActionDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Bulk User Actions</DialogTitle>
            <DialogDescription>
              Perform actions on multiple users at once.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" onClick={() => handleBulkAction('Activate Users')}>
                <UserPlus className="mr-2 h-4 w-4" />
                Activate
              </Button>
              <Button variant="outline" onClick={() => handleBulkAction('Deactivate Users')}>
                <UserPlus className="mr-2 h-4 w-4" />
                Deactivate
              </Button>
              <Button variant="outline" onClick={() => handleBulkAction('Assign Roles')}>
                <Shield className="mr-2 h-4 w-4" />
                Assign Roles
              </Button>
              <Button variant="outline" onClick={() => handleBulkAction('Send Invites')}>
                <Mail className="mr-2 h-4 w-4" />
                Send Invites
              </Button>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setBulkActionDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}