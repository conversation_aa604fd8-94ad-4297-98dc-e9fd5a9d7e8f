"use client";
import { FC } from "react";
import * as React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Loader2,
  Upload,
  X,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useBatchProcessingColumns } from "@/components/pages/batch-processing/columns";
import { ApplicationService } from "@/services/applications/application.service";
import { ApplicationInterface, mtype, statuses, types } from "@/types/applications/applications.types";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { checkExportStatus, ExportJob, startExport } from "@/services/export";
import { useInterval } from "@/hooks/use-interval";
import { toast } from "@/hooks/use-toast";
import SearchInput from "@/components/pages/general/data-table-search-input";

export const updateBatchPContext = React.createContext({
  setBatchApplicationsUpdate: () => { }
});

export const BatchProcessingTable: FC = () => {
  const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
  const [searchTerm, setSearchTerm] = React.useState<string>();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [pageSize, setPageSize] = React.useState<number>(10);
  const [page, setPage] = React.useState<number>(1);
  const [totalCount, setTotalCount] = React.useState<number>(0);
  const [totalPages, setTotalPages] = React.useState<number>(0);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);
  const [applications, setApplications] = React.useState<ApplicationInterface[]>([]);
  const [isExporting, setIsExporting] = React.useState(false);
  const [exportJobId, setExportJobId] = React.useState<string | null>(null);
  const [exportStatus, setExportStatus] = React.useState<ExportJob | null>(null);
  const { columns, selectedRows, setSelectedRows } = useBatchProcessingColumns();
  const [selectAllRows, setSelectAllRows] = React.useState<Set<string>>(new Set());

  React.useEffect(() => {
    loadMembershipTypes();
  }, []);

  React.useEffect(() => {
    loadApplications();
  }, [pageSize, searchTerm, subFilters]);

  const loadMembershipTypes = async () => {
    setLoading(true);
    setError(null);
    try {
      const membershipTypes = await ApplicationService.getMembershipTypes();
      if (membershipTypes.length > 0) {
        mtype.length = 0;
        membershipTypes.forEach(type => {
          mtype.push({
            value: type.id,
            label: type.name,
          });
        });
      } else {
        setError('No membership types found.');
      }
    } catch {
      setError("An unexpected error occurred, please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const loadApplications = async (pageToLoad = page) => {
    setLoading(true);
    setError(null);

    const { applications, success, total, currentPage, allApplicationIds } = await ApplicationService.getAllApplications(
      { subFilters },
      pageToLoad,
      pageSize,
      searchTerm
    );

    if (success && applications) {
      setApplications(applications);
      setSelectAllRows(new Set(allApplicationIds));
      setTotalCount(total);
      setPage(currentPage);

      // Calculate total pages based on the current pageSize
      const calculatedTotalPages = Math.ceil(total / pageSize);
      setTotalPages(calculatedTotalPages);

      // If the current page is out of bounds, reset to the last valid page
      if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
        setPage(calculatedTotalPages);
      }
    } else {
      setError("Failed to fetch applications, please try again later.");
    }

    setLoading(false);
  };

  const handleStatusFilter = (selectedMembershipTypes: string[], selectedAppStatus: string[], selectedAppType: string[]) => {
    setSubFilters(prev => ({
      ...prev,
      membership_type: selectedMembershipTypes,
      application_status: selectedAppStatus,
      application_type: selectedAppType
    }));
  };

  const resetFilters = () => {
    setSubFilters({});
    setColumnFilters([]);
    setSearchTerm(undefined);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      loadApplications(newPage);
    }
  };

  const handleLimitChange = (value: string) => {
    setPageSize(Number(value));
  };

  const handleBatchUpdate = () => {
    loadApplications();
  };

  const handleExport = async () => {
    try {
      setIsExporting(true)
      setExportStatus(null)

      const data = await startExport(Array.from(selectedRows))
      setExportJobId(data.jobId)
      setExportStatus({ status: 'PENDING' })

    } catch (error) {
      console.error('Error starting export:', error)
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: "There was an error starting the export process."
      })
      setIsExporting(false)
    }
  };

  useInterval(
    async () => {
      if (!exportJobId) return

      try {
        const data = await checkExportStatus(exportJobId)
        setExportStatus(data)

        // Stop polling when completed or failed
        if (data.status === 'COMPLETED' || data.status === 'FAILED') {
          setExportJobId(null)

          // Auto download when completed
          if (data.status === 'COMPLETED' && data.file_url) {
            window.location.href = data.file_url
            setIsExporting(false)
          }
        }
      } catch (error) {
        console.error('Error checking export status:', error)
      }
    },
    exportJobId ? 2000 : null
  )

  const handleSelectAllRows = () => {
    // Set selected rows to the same Set as selectAllRows
    setSelectedRows(new Set(selectAllRows));
  };

  const table = useReactTable({
    data: applications,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <updateBatchPContext.Provider value={{ setBatchApplicationsUpdate: handleBatchUpdate }}>
      <div className="w-full space-y-4">
        {error && (
          <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
            {error}
          </div>
        )}

        {selectedRows.size !== 0 && (
          <div className="mb-2 p-2 text-black-600 bg-blue-100 border border-blue-300 rounded flex items-center text-sm">
            <span className="mr-1">{selectedRows.size} rows selected,</span>
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleSelectAllRows();
              }}
              className="underline cursor-pointer"
            >
              Select all {totalCount} rows.
            </a>
          </div>
        )}

        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Batch Processing
            </h1>
            <p className="text-sm text-gray-600">
              Process multiple applications efficiently with bulk actions and streamlined workflows.
            </p>
          </div>
          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center"
                  disabled={loading || selectedRows.size === 0}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </DialogTrigger>

              <DialogContent className="rounded-xl p-8">
                <DialogHeader>
                  <DialogTitle>Are you sure you want to export?</DialogTitle>
                  <DialogDescription>
                    {selectedRows.size} row{selectedRows.size !== 1 ? 's' : ''} selected to export.
                  </DialogDescription>
                </DialogHeader>

                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="button" variant="secondary">
                      Close
                    </Button>
                  </DialogClose>
                  <DialogClose asChild>
                    <Button
                      variant="default"
                      onClick={handleExport}
                      className="ml-2 rounded-md"
                    >
                      Confirm Export
                    </Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
              <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search programmes..." />

              {table.getColumn("membership_type_name") && (
                <DataTableFacetedFilter
                  column={table.getColumn("membership_type_name")}
                  title="Membership Type"
                  options={mtype}
                  onFilterChange={(selectedMembershipTypes) => handleStatusFilter(selectedMembershipTypes, subFilters.application_status, subFilters.application_type)}
                />
              )}
              {table.getColumn("application_status") && (
                <DataTableFacetedFilter
                  column={table.getColumn("application_status")}
                  title="Status"
                  options={statuses}
                  onFilterChange={(selectedAppStatus) => handleStatusFilter(subFilters.membership_type, selectedAppStatus, subFilters.application_type)}
                />
              )}
              {table.getColumn("application_type") && (
                <DataTableFacetedFilter
                  column={table.getColumn("application_type")}
                  title="Type"
                  options={types}
                  onFilterChange={(selectedAppType) => handleStatusFilter(subFilters.membership_type, subFilters.application_status, selectedAppType)}
                />
              )}
              {/* Reset Filters Button */}
              {(Object.keys(subFilters).length > 0 || searchTerm) && (
                <Button
                  variant="ghost"
                  onClick={resetFilters}
                  className="h-8 px-2 lg:px-3"
                >
                  Reset
                  <X className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>

            <DataTableViewOptions table={table} />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length}>
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="text-center">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between px-2">
            <div className="flex-1 text-sm text-muted-foreground">
              {selectedRows.size} of {totalCount} row(s) selected.
            </div>
            <div className="flex items-center space-x-6 lg:space-x-8">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Rows per page</p>
                <Select
                  value={pageSize.toString()}
                  onValueChange={handleLimitChange}
                  disabled={loading}
                >
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder={pageSize} />
                  </SelectTrigger>
                  <SelectContent side="top">
                    {[10, 20, 30, 40, 50].map((size) => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                Page {page} of {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  className="hidden h-8 w-8 p-0 lg:flex"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(1);
                  }}
                  disabled={page <= 1}
                >
                  <span className="sr-only">Go to first page</span>
                  <ChevronsLeft />
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(page - 1);
                  }}
                  disabled={page <= 1}
                >
                  <span className="sr-only">Go to previous page</span>
                  <ChevronLeft />
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(page + 1);
                  }}
                  disabled={page >= totalPages}
                >
                  <span className="sr-only">Go to next page</span>
                  <ChevronRight />
                </Button>
                <Button
                  variant="outline"
                  className="hidden h-8 w-8 p-0 lg:flex"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(totalPages);
                  }}
                  disabled={page >= totalPages}
                >
                  <span className="sr-only">Go to last page</span>
                  <ChevronsRight />
                </Button>
              </div>
            </div>
          </div>

          <Dialog open={isExporting} onOpenChange={setIsExporting}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Exporting Applications</DialogTitle>
              </DialogHeader>
              <div className="py-6">
                {exportStatus?.status === 'PENDING' && (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Preparing export...</span>
                  </div>
                )}
                {exportStatus?.status === 'PROCESSING' && (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Processing data...</span>
                  </div>
                )}
                {exportStatus?.status === 'COMPLETED' && (
                  <div className="text-green-600">
                    Export completed! Downloading file...
                  </div>
                )}
                {exportStatus?.status === 'FAILED' && (
                  <div className="text-red-600">
                    Export failed: {exportStatus.error}
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>

      </div>
    </updateBatchPContext.Provider>
  );
};