"use client";

import { FC, useEffect, useState, useRef } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { ColumnDef } from "@tanstack/react-table";
import { CustomerStatusBadge } from "@/components/customer-management/ui/customer-status-badge";
import type { Customer, CustomerType, CustomerCategory } from "@/types/customer-management/customer-management.types";
import { getAllCustomers } from "@/services/customer-management/customers.service";
import { 
  MoreHorizontal, 
  Edit, 
  Eye, 
  CircleCheck, 
  CircleX, 
  AlertCircle, 
  Ban,
  ChevronRight,
  Ch<PERSON>ronLeft,
  Chevrons<PERSON>eft,
  ChevronsRight,
  Download,
  Loader2,
  X,
  Trash2
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

interface CustomersTableProps {
  onEdit?: (customerId: string) => void;
  onView?: (customerId: string) => void;
  onDelete?: (customerId: string) => void;
  onStatusChange?: (customerId: string, newStatus: string) => void;
  onBulkStatusChange?: (customerIds: string[], newStatus: string) => void;
}

// Helper functions for formatting
const formatCustomerType = (type: CustomerType): string => {
  const typeLabels: Record<CustomerType, string> = {
    INDIVIDUAL: 'Individual',
    COMPANY: 'Company',
    GOVERNMENT: 'Government',
    NON_PROFIT: 'Non-Profit',
    EDUCATIONAL: 'Educational'
  };
  return typeLabels[type] || type;
};

const formatCustomerCategory = (category?: CustomerCategory): string => {
  if (!category) return '-';
  const categoryLabels: Record<CustomerCategory, string> = {
    REGULAR: 'Regular',
    VIP: 'VIP',
    CORPORATE: 'Corporate',
    RESELLER: 'Reseller',
    DISTRIBUTOR: 'Distributor'
  };
  return categoryLabels[category] || category;
};

const getCategoryColor = (category?: CustomerCategory): string => {
  if (!category) return 'default';
  const colors: Record<CustomerCategory, string> = {
    REGULAR: 'bg-blue-100 text-blue-800',
    VIP: 'bg-purple-100 text-purple-800',
    CORPORATE: 'bg-green-100 text-green-800',
    RESELLER: 'bg-orange-100 text-orange-800',
    DISTRIBUTOR: 'bg-indigo-100 text-indigo-800'
  };
  return colors[category] || 'default';
};

export const CustomersTable: FC<CustomersTableProps> = ({ 
  onEdit, 
  onView,
  onDelete, 
  onStatusChange,
  onBulkStatusChange 
}) => {
  const { toast } = useToast();
  const [subFilters, setSubFilters] = useState<{ [key: string]: string[] }>({});
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchInput, setSearchInput] = useState<string>("");
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [pageSize, setPageSize] = useState<number>(10);
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  
  // Create refs for cleanup
  const abortControllerRef = useRef<AbortController | null>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Define columns with actions
  const columns: ColumnDef<Customer>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "customer_code",
      header: "Customer Code",
      cell: ({ row }) => <div className="font-medium">{row.getValue("customer_code")}</div>,
    },
    {
      accessorKey: "name",
      header: "Customer Name",
      cell: ({ row }) => <div>{row.getValue("name")}</div>,
    },
    {
      accessorKey: "customer_type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.getValue("customer_type") as CustomerType;
        return <div>{formatCustomerType(type)}</div>;
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.getValue("category") as CustomerCategory | undefined;
        return category ? (
          <Badge className={getCategoryColor(category)} variant="secondary">
            {formatCustomerCategory(category)}
          </Badge>
        ) : (
          <span className="text-gray-400">-</span>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as any;
        return <CustomerStatusBadge status={status} />;
      },
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => <div className="truncate max-w-[200px]">{row.getValue("email")}</div>,
    },
    {
      accessorKey: "phone",
      header: "Phone",
      cell: ({ row }) => <div>{row.getValue("phone") || '-'}</div>,
    },
    {
      accessorKey: "credit_limit",
      header: "Credit Limit",
      cell: ({ row }) => {
        const amount = row.getValue("credit_limit") as number | undefined;
        return (
          <div className="text-right">
            {amount ? `$${amount.toLocaleString()}` : '-'}
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const customer = row.original;
        const currentStatus = customer.status;
        
        // Define available status transitions
        const statusTransitions = {
          ACTIVE: ['INACTIVE', 'SUSPENDED', 'BLACKLISTED'],
          INACTIVE: ['ACTIVE', 'SUSPENDED', 'BLACKLISTED'],
          SUSPENDED: ['ACTIVE', 'INACTIVE', 'BLACKLISTED'],
          BLACKLISTED: ['ACTIVE', 'INACTIVE', 'SUSPENDED']
        };
        
        const availableTransitions = statusTransitions[currentStatus] || [];
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onEdit?.(customer.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Customer
              </DropdownMenuItem>
              {/* View Details - Not fully implemented yet
              <DropdownMenuItem onClick={() => onView?.(customer.id)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              */}
              <DropdownMenuSeparator />
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <ChevronRight className="mr-2 h-4 w-4" />
                  Change Status
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  {availableTransitions.includes('ACTIVE') && (
                    <DropdownMenuItem onClick={() => onStatusChange?.(customer.id, 'ACTIVE')}>
                      <CircleCheck className="mr-2 h-4 w-4 text-green-500" />
                      Activate
                    </DropdownMenuItem>
                  )}
                  {availableTransitions.includes('INACTIVE') && (
                    <DropdownMenuItem onClick={() => onStatusChange?.(customer.id, 'INACTIVE')}>
                      <CircleX className="mr-2 h-4 w-4 text-gray-500" />
                      Deactivate
                    </DropdownMenuItem>
                  )}
                  {availableTransitions.includes('SUSPENDED') && (
                    <DropdownMenuItem onClick={() => onStatusChange?.(customer.id, 'SUSPENDED')}>
                      <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                      Suspend
                    </DropdownMenuItem>
                  )}
                  {availableTransitions.includes('BLACKLISTED') && (
                    <DropdownMenuItem onClick={() => onStatusChange?.(customer.id, 'BLACKLISTED')}>
                      <Ban className="mr-2 h-4 w-4 text-red-500" />
                      Blacklist
                    </DropdownMenuItem>
                  )}
                </DropdownMenuSubContent>
              </DropdownMenuSub>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete?.(customer.id)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Customer
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  useEffect(() => {
    // Cancel previous request if any
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this effect
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    // Clear previous search timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Debounce search
    if (searchTerm) {
      searchTimeoutRef.current = setTimeout(() => {
        if (!abortController.signal.aborted) {
          loadCustomers(page, abortController.signal);
        }
      }, 300);
    } else {
      // Load immediately if no search term
      loadCustomers(page, abortController.signal);
    }

    // Cleanup function
    return () => {
      abortController.abort();
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [page, pageSize, searchTerm, subFilters]);

  const loadCustomers = async (pageToLoad = page, signal?: AbortSignal) => {
    // Don't set loading if aborted
    if (signal?.aborted) return;
    
    setLoading(true);
    setError(null);

    try {
      const { customers: customersData, success, total } = await getAllCustomers(
        { subFilters },
        pageToLoad,
        pageSize,
        searchTerm
      );

      // Check if aborted before setting state
      if (signal?.aborted) return;

      if (success && customersData) {
        setCustomers(customersData);
        setTotalCount(total);
        setPage(pageToLoad);
        setTotalPages(Math.ceil(total / pageSize));
      } else {
        setError("Failed to fetch customers");
      }
    } catch (error: any) {
      if (error?.name !== 'AbortError' && !signal?.aborted) {
        setError("An error occurred while fetching customers");
      }
    } finally {
      if (!signal?.aborted) {
        setLoading(false);
      }
    }
  };

  const table = useReactTable({
    data: customers,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    pageCount: totalPages,
    autoResetPageIndex: false,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex: page - 1,
        pageSize: pageSize,
      },
    },
  });

  const handleSearch = (value: string) => {
    setSearchInput(value);
    setIsSearching(true);
    
    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set new timeout for search
    searchTimeoutRef.current = setTimeout(() => {
      setSearchTerm(value);
      setIsSearching(false);
    }, 500);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      loadCustomers(newPage);
    }
  };

  const handleLimitChange = (value: string) => {
    setPageSize(Number(value));
  };

  const handleExport = async () => {
    try {
      toast({
        title: "Export Started",
        description: "Your customer list is being exported...",
      });
      // TODO: Implement actual export functionality
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to export data",
      });
    }
  };

  const handleBulkStatusChange = (newStatus: string) => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const selectedIds = selectedRows.map(row => row.original.id);
    
    if (selectedIds.length === 0) {
      toast({
        variant: "destructive",
        title: "No Selection",
        description: "Please select customers to change status",
      });
      return;
    }

    onBulkStatusChange?.(selectedIds, newStatus);
    setRowSelection({});
  };

  const resetFilters = () => {
    setSubFilters({});
    setColumnFilters([]);
    setSearchTerm("");
    setSearchInput("");
    setIsSearching(false);
    toast({
      title: "Success",
      description: "Filters cleared"
    });
  };

  const selectedRowCount = Object.keys(rowSelection).length;

  return (
    <div className="w-full space-y-4">
      {error && (
        <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
              {isSearching && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-500" />
              )}
              <Input
                placeholder="Search by code, name, email..."
                value={searchInput}
                onChange={(e) => handleSearch(e.target.value)}
                className="h-8 w-[150px] lg:w-[250px] pl-10 pr-10"
                disabled={loading}
              />
            </div>

            {/* Reset Filters Button */}
            {(searchInput) && (
              <Button
                variant="ghost"
                onClick={resetFilters}
                className="h-8 px-2 lg:px-3"
              >
                Reset
                <X className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {selectedRowCount > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    Bulk Actions ({selectedRowCount})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleBulkStatusChange('ACTIVE')}>
                    <CircleCheck className="mr-2 h-4 w-4 text-green-500" />
                    Set Active
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkStatusChange('INACTIVE')}>
                    <CircleX className="mr-2 h-4 w-4 text-gray-500" />
                    Set Inactive
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkStatusChange('SUSPENDED')}>
                    <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                    Suspend
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkStatusChange('BLACKLISTED')}>
                    <Ban className="mr-2 h-4 w-4 text-red-500" />
                    Blacklist
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={loading || customers.length === 0}
              className="h-8"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <DataTableViewOptions table={table} />
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={columns.length}>
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                        <span className="text-sm text-gray-600">Loading customers...</span>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-gray-500">No customers found</p>
                      <p className="text-sm text-gray-400">
                        {searchTerm || Object.keys(subFilters).length > 0
                          ? "Try adjusting your search or filters"
                          : "No customers have been added yet"}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between px-2">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of {totalCount} row(s) selected.
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <Select
                value={pageSize.toString()}
                onValueChange={handleLimitChange}
                disabled={loading}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {page} of {totalPages || 1}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(1);
                }}
                disabled={page <= 1 || loading}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(page - 1);
                }}
                disabled={page <= 1 || loading}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(page + 1);
                }}
                disabled={page >= totalPages || loading}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(totalPages);
                }}
                disabled={page >= totalPages || loading}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};