// User Management Statistics Component
// Displays comprehensive user statistics with real-time data

'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Users, 
  UserCheck, 
  UserX, 
  Shield,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw
} from 'lucide-react'
import { UserManagementClientService } from '@/services/user-management'
import type { UserWithRole } from '@/types/user-management.types'

interface UserStatistics {
  total: number
  active: number
  inactive: number
  usersWithRoles: number
  usersWithoutRoles: number
  activePercentage: number
}

export function UserManagementStats() {
  const [users, setUsers] = useState<UserWithRole[]>([])
  const [stats, setStats] = useState<UserStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await UserManagementClientService.getUsersWithRoles()
      
      if (response.success) {
        setUsers(response.data)
        const userStats = UserManagementClientService.getUserStatistics(response.data)
        setStats(userStats)
        setLastUpdated(new Date())
      } else {
        setError(response.error || 'Failed to fetch user data')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Error fetching user stats:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleRefresh = () => {
    fetchData()
  }

  if (loading) {
    return <UserStatsLoadingSkeleton />
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-destructive">
              <UserX className="h-5 w-5" />
              <span className="font-medium">Failed to load user statistics</span>
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (!stats) {
    return null
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats.total,
      description: 'All user accounts',
      icon: Shield,
      trend: stats.total > 0 ? 'stable' : 'none',
      color: 'default'
    },
    {
      title: 'Active Users',
      value: stats.active,
      description: `${stats.activePercentage}% of total users`,
      icon: UserCheck,
      trend: stats.activePercentage >= 80 ? 'up' : stats.activePercentage >= 60 ? 'stable' : 'down',
      color: stats.activePercentage >= 80 ? 'default' : stats.activePercentage >= 60 ? 'secondary' : 'destructive'
    },
    {
      title: 'Inactive Users',
      value: stats.inactive,
      description: 'Disabled or suspended accounts',
      icon: UserX,
      trend: stats.inactive === 0 ? 'up' : stats.inactive <= 2 ? 'stable' : 'down',
      color: stats.inactive === 0 ? 'default' : stats.inactive <= 2 ? 'secondary' : 'destructive'
    },
    {
      title: 'Assigned Roles',
      value: stats.usersWithRoles,
      description: `${stats.usersWithoutRoles} without roles`,
      icon: Activity,
      trend: stats.usersWithoutRoles === 0 ? 'up' : stats.usersWithoutRoles <= 2 ? 'stable' : 'down',
      color: stats.usersWithoutRoles === 0 ? 'default' : stats.usersWithoutRoles <= 2 ? 'secondary' : 'destructive'
    }
  ]

  return (
    <div className="space-y-4">
      {/* Header with refresh */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-semibold">User Statistics</h3>
          <p className="text-sm text-muted-foreground">
            {lastUpdated && `Last updated: ${lastUpdated.toLocaleTimeString()}`}
          </p>
        </div>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : stat.trend === 'down' ? TrendingDown : null
          
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  {TrendIcon && (
                    <TrendIcon className={`h-4 w-4 ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`} />
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
                {stat.color !== 'default' && (
                  <Badge 
                    variant={stat.color as any} 
                    className="mt-2 text-xs"
                  >
                    {stat.trend === 'up' && 'Excellent'}
                    {stat.trend === 'stable' && 'Good'}
                    {stat.trend === 'down' && 'Needs Attention'}
                  </Badge>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Additional Insights */}
      {stats.usersWithoutRoles > 0 && (
        <Card className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-amber-600 dark:text-amber-400" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-amber-900 dark:text-amber-100">
                  Action Required
                </p>
                <p className="text-sm text-amber-700 dark:text-amber-200">
                  {stats.usersWithoutRoles} user(s) do not have assigned roles. Consider assigning appropriate roles for proper access control.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function UserStatsLoadingSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </div>
        <Skeleton className="h-9 w-20" />
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-12 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}