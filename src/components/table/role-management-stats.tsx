// Role Management Statistics Component
// Displays comprehensive role statistics with real-time data

'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Shield, 
  ShieldCheck, 
  ShieldX, 
  Users,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'
import { UserManagementClientService } from '@/services/user-management'
import type { Role } from '@/types/user-management.types'

interface RoleStatistics {
  total: number
  active: number
  inactive: number
  activePercentage: number
}

export function RoleManagementStats() {
  const [roles, setRoles] = useState<Role[]>([])
  const [stats, setStats] = useState<RoleStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await UserManagementClientService.getRoles()
      
      if (response.success) {
        setRoles(response.data)
        const roleStats = UserManagementClientService.getRoleStatistics(response.data)
        setStats(roleStats)
        setLastUpdated(new Date())
      } else {
        setError(response.error || 'Failed to fetch role data')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Error fetching role stats:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleRefresh = () => {
    fetchData()
  }

  if (loading) {
    return <RoleStatsLoadingSkeleton />
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-destructive">
              <ShieldX className="h-5 w-5" />
              <span className="font-medium">Failed to load role statistics</span>
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (!stats) {
    return null
  }

  const statCards = [
    {
      title: 'Total Roles',
      value: stats.total,
      description: 'All system roles',
      icon: Shield,
      trend: stats.total > 0 ? 'stable' : 'none',
      color: 'default'
    },
    {
      title: 'Active Roles',
      value: stats.active,
      description: `${stats.activePercentage}% of total roles`,
      icon: ShieldCheck,
      trend: stats.activePercentage >= 80 ? 'up' : stats.activePercentage >= 60 ? 'stable' : 'down',
      color: stats.activePercentage >= 80 ? 'default' : stats.activePercentage >= 60 ? 'secondary' : 'destructive'
    },
    {
      title: 'Inactive Roles',
      value: stats.inactive,
      description: 'Disabled or deprecated',
      icon: ShieldX,
      trend: stats.inactive === 0 ? 'up' : stats.inactive <= 2 ? 'stable' : 'down',
      color: stats.inactive === 0 ? 'default' : stats.inactive <= 2 ? 'secondary' : 'destructive'
    }
  ]

  return (
    <div className="space-y-4">
      {/* Header with refresh */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-semibold">Role Statistics</h3>
          <p className="text-sm text-muted-foreground">
            {lastUpdated && `Last updated: ${lastUpdated.toLocaleTimeString()}`}
          </p>
        </div>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : stat.trend === 'down' ? TrendingDown : null
          
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  {TrendIcon && (
                    <TrendIcon className={`h-4 w-4 ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`} />
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
                {stat.color !== 'default' && (
                  <Badge 
                    variant={stat.color as any} 
                    className="mt-2 text-xs"
                  >
                    {stat.trend === 'up' && 'Excellent'}
                    {stat.trend === 'stable' && 'Good'}
                    {stat.trend === 'down' && 'Needs Attention'}
                  </Badge>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Role Configuration Insights */}
      {stats.inactive > 0 && (
        <Card className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-amber-900 dark:text-amber-100">
                  Role Management Notice
                </p>
                <p className="text-sm text-amber-700 dark:text-amber-200">
                  {stats.inactive} role(s) are currently inactive. Review and clean up unused roles to maintain security best practices.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function RoleStatsLoadingSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </div>
        <Skeleton className="h-9 w-20" />
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-12 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}