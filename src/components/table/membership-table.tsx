"use client";
import { FC } from "react";
import * as React from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    X,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { columns } from "@/components/pages/members/columns";
import { ApplicationService } from "@/services/applications/application.service";
import { MemberService } from "@/services/members/member.service";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { mtype } from "@/types/applications/applications.types";
import { Membership, statuses } from "@/types/members/table.types";
import SearchInput from "@/components/pages/general/data-table-search-input";

export const MembershipsTable: FC = () => {
    const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
    const [searchTerm, setSearchTerm] = React.useState<string>();
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [loading, setLoading] = React.useState<boolean>(true);
    const [error, setError] = React.useState<string | null>(null);
    const [memberships, setMemberships] = React.useState<Membership[]>([]);

    React.useEffect(() => {
        loadMembershipTypes();
    }, []);

    React.useEffect(() => {
        loadMemberships();
    }, [pageSize, searchTerm, subFilters]);

    const loadMembershipTypes = async () => {
        setLoading(true);
        setError(null);
        try {
            const membershipTypes = await ApplicationService.getMembershipTypes();
            if (membershipTypes.length > 0) {
                mtype.length = 0;
                membershipTypes.forEach(type => {
                    mtype.push({
                        value: type.id,
                        label: type.name,
                    });
                });
            } else {
                setError('No membership types found');
            }
        } catch (error) {
            console.error('An unexpected error occurred while fetching membership types:', error);
            setError('An unexpected error occurred');
        } finally {
            setLoading(false);
        }
    };

    const loadMemberships = async (pageToLoad = page) => {
        setLoading(true);
        setError(null);

        const {
            memberships,
            success,
            total,
            currentPage
        } = await MemberService.getAllMemberships(
            { subFilters },
            pageToLoad,
            pageSize,
            searchTerm
        );

        if (success && memberships) {
            setMemberships(memberships);
            setTotalCount(total);
            setPage(currentPage);

            // Calculate total pages based on the current pageSize
            const calculatedTotalPages = Math.ceil(total / pageSize);
            setTotalPages(calculatedTotalPages);

            if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
                setPage(calculatedTotalPages);
            }
        } else {
            setError("Failed to fetch memberships, please try again later.");
        }

        setLoading(false);
    };

    const handleStatusFilter = (selectedMembershipTypes: string[], selectedAppStatus: string[]) => {
        setSubFilters(prev => ({
            ...prev,
            membership_type: selectedMembershipTypes,
            membership_status: selectedAppStatus
        }));
    };

    const handleSearch = (term: string) => {
        setSearchTerm(term);
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm(undefined);
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadMemberships(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
    };

    const table = useReactTable({
        data: memberships,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    return (
        <div className="w-full space-y-4">
            {error && (
                <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
                    {error}
                </div>
            )}

            <div className="flex items-center justify-between mb-6">
                <div className="space-y-1">
                    <h1 className="text-2xl font-bold tracking-tight">
                        Members
                    </h1>
                    <p className="text-sm text-muted-foreground">
                        Manage and view all members and their membership details
                    </p>
                </div>
            </div>

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <SearchInput onSearch={handleSearch} value={searchTerm} placeholder="Search memberships..." />

                        {table.getColumn("membership_type_name") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("membership_type_name")}
                                title="Membership Type"
                                options={mtype}
                                onFilterChange={(selectedMembershipTypes) => handleStatusFilter(selectedMembershipTypes, subFilters.membership_status)}
                            />
                        )}
                        {table.getColumn("membership_status") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("membership_status")}
                                title="Status"
                                options={statuses}
                                onFilterChange={(selectedAppStatus) => handleStatusFilter(subFilters.membership_type, selectedAppStatus)}
                            />
                        )}

                        {/* Reset Filters Button */}
                        {(Object.keys(subFilters).length > 0 || searchTerm) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <DataTableViewOptions table={table} />
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="text-center">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        Total {totalCount} row(s)
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={loading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div >
    );
};