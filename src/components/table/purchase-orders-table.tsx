"use client";

import { FC, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePurchaseOrderColumns } from "@/components/pages/purchase-orders/columns";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import type { PurchaseOrder, PurchaseOrdersSummary } from "@/types/purchase-orders/purchase-orders.types";
import {
  getAllPurchaseOrders,
  getPurchaseOrdersSummary,
  getEntities
} from "@/services/purchase-orders/purchase-orders.service";
import { getActiveSuppliers } from "@/services/purchase-orders/suppliers.service";
import SearchInput from "@/components/pages/general/data-table-search-input";

interface PurchaseOrdersTableProps {
  onSummaryUpdate: (summary: PurchaseOrdersSummary) => void;
}

export const PurchaseOrdersTable: FC<PurchaseOrdersTableProps> = ({
  onSummaryUpdate
}) => {
  const [subFilters, setSubFilters] = useState<{ [key: string]: string[] }>({});
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [pageSize, setPageSize] = useState<number>(10);
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const { columns, selectedRows, setSelectedRows } = usePurchaseOrderColumns();

  // Filter options
  const [suppliers, setSuppliers] = useState<{ value: string; label: string }[]>([]);
  const [entities, setEntities] = useState<{ value: string; label: string }[]>([]);

  const statusOptions = [
    { value: "DRAFT", label: "Draft" },
    { value: "SUBMITTED", label: "Submitted" },
    { value: "APPROVED", label: "Approved" },
    { value: "SENT", label: "Sent" },
    { value: "PARTIALLY_RECEIVED", label: "Partially Received" },
    { value: "RECEIVED", label: "Received" },
    { value: "CANCELLED", label: "Cancelled" },
    { value: "CLOSED", label: "Closed" }
  ];

  useEffect(() => {
    fetchOptions();
    loadPurchaseOrders();
    loadSummary();
  }, [pageSize, searchTerm, subFilters]);

  const fetchOptions = async () => {
    try {
      const [suppliersData, entitiesData] = await Promise.all([
        getActiveSuppliers(),
        getEntities()
      ]);

      setSuppliers(suppliersData);
      setEntities(entitiesData);
    } catch (error) {
      console.error("Error fetching filter options:", error);
    }
  };

  const loadPurchaseOrders = async (pageToLoad = page) => {
    setLoading(true);
    setError(null);

    try {
      const { purchaseOrders: poData, success, total, currentPage } = await getAllPurchaseOrders(
        { subFilters },
        pageToLoad,
        pageSize,
        searchTerm
      );

      if (success && poData) {
        setPurchaseOrders(poData);
        setTotalCount(total);
        setPage(currentPage);

        const calculatedTotalPages = Math.ceil(total / pageSize);
        setTotalPages(calculatedTotalPages);

        if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
          setPage(calculatedTotalPages);
        }
      } else {
        setError("Failed to fetch purchase orders");
      }
    } catch (error) {
      setError("An error occurred while fetching purchase orders");
    } finally {
      setLoading(false);
    }
  };

  const loadSummary = async () => {
    try {
      const summaryData = await getPurchaseOrdersSummary();
      onSummaryUpdate(summaryData);
    } catch (error) {
      console.error("Error loading summary:", error);
    }
  };

  const handleStatusFilter = (selectedStatuses: string[], selectedSuppliers: string[], selectedEntities: string[]) => {
    setSubFilters(prev => ({
      ...prev,
      status: selectedStatuses,
      supplier_id: selectedSuppliers,
      entity_id: selectedEntities
    }));
  };

  const resetFilters = () => {
    setSubFilters({});
    setColumnFilters([]);
    setSearchTerm("");
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      loadPurchaseOrders(newPage);
    }
  };

  const handleLimitChange = (value: string) => {
    setPageSize(Number(value));
  };

  const table = useReactTable({
    data: purchaseOrders,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const selectedRowCount = Object.keys(rowSelection).length;

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative max-w-sm">
            <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search purchase orders..." />
          </div>

          {table.getColumn("status") && (
            <DataTableFacetedFilter
              column={table.getColumn("status")}
              title="Status"
              options={statusOptions}
              onFilterChange={(values: string[]) => handleStatusFilter(values, subFilters.supplier_id || [], subFilters.entity_id || [])}
            />
          )}

          {table.getColumn("supplier_name") && (
            <DataTableFacetedFilter
              column={table.getColumn("supplier_name")}
              title="Supplier"
              options={suppliers}
              onFilterChange={(values: string[]) => handleStatusFilter(subFilters.status || [], values, subFilters.entity_id || [])}
            />
          )}

          {table.getColumn("entity_id") && (
            <DataTableFacetedFilter
              column={table.getColumn("entity_id")}
              title="Entity"
              options={entities}
              onFilterChange={(values: string[]) => handleStatusFilter(subFilters.status || [], subFilters.supplier_id || [], values)}
            />
          )}

          {/* Reset Filters Button */}
          {(Object.keys(subFilters).length > 0 || searchTerm) && (
            <Button
              variant="ghost"
              onClick={resetFilters}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {selectedRowCount > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {selectedRowCount} of {purchaseOrders.length} row(s) selected
              </span>
              <Button variant="outline" size="sm">
                Export Selected
              </Button>
            </div>
          )}
          <DataTableViewOptions table={table} />
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-red-500">
                  {error}
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No purchase orders found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between px-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {selectedRows.size} of {totalCount} row(s) selected.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={pageSize.toString()}
              onValueChange={handleLimitChange}
              disabled={loading}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {page} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={(e) => {
                e.preventDefault();
                handlePageChange(1);
              }}
              disabled={page <= 1}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.preventDefault();
                handlePageChange(page - 1);
              }}
              disabled={page <= 1}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.preventDefault();
                handlePageChange(page + 1);
              }}
              disabled={page >= totalPages}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={(e) => {
                e.preventDefault();
                handlePageChange(totalPages);
              }}
              disabled={page >= totalPages}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};