// System Ownership Transfer Widget
// Displays ownership transfer status and controls for system owners

'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Crown, 
  ArrowRightLeft, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  <PERSON>fresh<PERSON><PERSON>,
  Send,
  Shield
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { UserManagementClientService } from '@/services/user-management'
import type { OwnershipTransfer } from '@/types/user-management.types'

export function OwnershipTransferWidget() {
  const { toast } = useToast()
  
  const [isSystemOwner, setIsSystemOwner] = useState(false)
  const [transferStatus, setTransferStatus] = useState<OwnershipTransfer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Dialog states
  const [initiateDialog, setInitiateDialog] = useState(false)
  const [respondDialog, setRespondDialog] = useState(false)
  const [cancelDialog, setCancelDialog] = useState(false)
  
  // Form states
  const [targetUserId, setTargetUserId] = useState('')
  const [transferMessage, setTransferMessage] = useState('')
  const [responseReason, setResponseReason] = useState('')

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if current user is system owner
      const ownerResponse = await UserManagementClientService.checkPermission('SYSTEM', 'UPDATE')
      setIsSystemOwner(ownerResponse)

      if (ownerResponse) {
        // Get ownership transfer status (this would be an API call)
        // For now, mock the response
        setTransferStatus(null) // No active transfer
      }
    } catch (err) {
      setError('Failed to fetch ownership status')
      console.error('Error fetching ownership data:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleInitiateTransfer = async () => {
    try {
      if (!targetUserId.trim()) {
        toast({
          title: 'Validation Error',
          description: 'Please enter a target user ID',
          variant: 'destructive'
        })
        return
      }

      // API call to initiate transfer
      toast({
        title: 'Transfer Initiated',
        description: 'Ownership transfer has been initiated successfully. The target user has 7 days to respond.'
      })
      
      setInitiateDialog(false)
      setTargetUserId('')
      setTransferMessage('')
      fetchData() // Refresh data
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to initiate ownership transfer',
        variant: 'destructive'
      })
    }
  }

  const handleRespondToTransfer = async (action: 'ACCEPT' | 'REJECT') => {
    try {
      // API call to respond to transfer
      toast({
        title: `Transfer ${action === 'ACCEPT' ? 'Accepted' : 'Rejected'}`,
        description: `You have ${action === 'ACCEPT' ? 'accepted' : 'rejected'} the ownership transfer.`
      })
      
      setRespondDialog(false)
      setResponseReason('')
      fetchData() // Refresh data
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to respond to transfer',
        variant: 'destructive'
      })
    }
  }

  const handleCancelTransfer = async () => {
    try {
      // API call to cancel transfer
      toast({
        title: 'Transfer Cancelled',
        description: 'The ownership transfer has been cancelled.'
      })
      
      setCancelDialog(false)
      fetchData() // Refresh data
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to cancel transfer',
        variant: 'destructive'
      })
    }
  }

  const calculateTimeRemaining = (expiresAt: string) => {
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diff = expiry.getTime() - now.getTime()
    
    if (diff <= 0) return { expired: true, text: 'Expired' }
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    return { 
      expired: false, 
      text: days > 0 ? `${days}d ${hours}h` : `${hours}h`,
      percentage: Math.max(0, Math.min(100, (diff / (7 * 24 * 60 * 60 * 1000)) * 100))
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-muted rounded w-48"></div>
            <div className="h-8 bg-muted rounded w-32"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">Failed to load ownership status</span>
            </div>
            <Button variant="outline" size="sm" onClick={fetchData}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!isSystemOwner && !transferStatus) {
    return null // Don't show widget for non-owners without pending transfers
  }

  return (
    <Card className="border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50 dark:border-amber-900 dark:from-amber-950 dark:to-orange-950">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-amber-600 dark:text-amber-400" />
            <CardTitle className="text-amber-900 dark:text-amber-100">
              System Ownership
            </CardTitle>
          </div>
          <Button variant="outline" size="sm" onClick={fetchData}>
            <RefreshCw className="mr-2 h-3 w-3" />
            Refresh
          </Button>
        </div>
        <CardDescription className="text-amber-700 dark:text-amber-200">
          {isSystemOwner 
            ? 'You are the current system owner with full administrative privileges'
            : 'You have a pending ownership transfer'}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Status */}
        {isSystemOwner && !transferStatus && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="default" className="bg-amber-100 text-amber-800 hover:bg-amber-100">
                <Crown className="mr-1 h-3 w-3" />
                System Owner
              </Badge>
              <div className="text-sm text-amber-700 dark:text-amber-200">
                Full system access and control
              </div>
            </div>
            
            <Dialog open={initiateDialog} onOpenChange={setInitiateDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="border-amber-300 text-amber-700 hover:bg-amber-100">
                  <ArrowRightLeft className="mr-2 h-4 w-4" />
                  Transfer Ownership
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Initiate Ownership Transfer</DialogTitle>
                  <DialogDescription>
                    Transfer system ownership to another user. This action will grant them full administrative privileges.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="targetUser">Target User ID</Label>
                    <Input
                      id="targetUser"
                      placeholder="Enter user ID..."
                      value={targetUserId}
                      onChange={(e) => setTargetUserId(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="message">Transfer Message (Optional)</Label>
                    <Textarea
                      id="message"
                      placeholder="Add a message for the new owner..."
                      value={transferMessage}
                      onChange={(e) => setTransferMessage(e.target.value)}
                      rows={3}
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setInitiateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleInitiateTransfer}>
                    <Send className="mr-2 h-4 w-4" />
                    Initiate Transfer
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}

        {/* Active Transfer Status */}
        {transferStatus && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={
                      transferStatus.transfer_status === 'PENDING' ? 'default' :
                      transferStatus.transfer_status === 'ACCEPTED' ? 'secondary' :
                      'destructive'
                    }
                  >
                    {transferStatus.transfer_status === 'PENDING' && <Clock className="mr-1 h-3 w-3" />}
                    {transferStatus.transfer_status === 'ACCEPTED' && <CheckCircle className="mr-1 h-3 w-3" />}
                    {transferStatus.transfer_status === 'EXPIRED' && <XCircle className="mr-1 h-3 w-3" />}
                    {transferStatus.transfer_status}
                  </Badge>
                  <span className="text-sm font-medium">Ownership Transfer</span>
                </div>
                
                {transferStatus.transfer_message && (
                  <p className="text-sm text-muted-foreground">
                    "{transferStatus.transfer_message}"
                  </p>
                )}
              </div>
              
              <div className="flex gap-2">
                {transferStatus.transfer_status === 'PENDING' && isSystemOwner && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setCancelDialog(true)}
                    className="text-destructive hover:text-destructive"
                  >
                    Cancel Transfer
                  </Button>
                )}
                
                {transferStatus.transfer_status === 'PENDING' && !isSystemOwner && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setRespondDialog(true)}
                  >
                    Respond
                  </Button>
                )}
              </div>
            </div>
            
            {/* Time Remaining */}
            {transferStatus.transfer_status === 'PENDING' && transferStatus.transfer_expires_at && (
              <div className="space-y-2">
                {(() => {
                  const timeRemaining = calculateTimeRemaining(transferStatus.transfer_expires_at)
                  return (
                    <>
                      <div className="flex items-center justify-between text-sm">
                        <span>Time Remaining</span>
                        <span className={timeRemaining.expired ? 'text-destructive' : 'text-muted-foreground'}>
                          {timeRemaining.text}
                        </span>
                      </div>
                      {!timeRemaining.expired && (
                        <Progress value={timeRemaining.percentage} className="h-2" />
                      )}
                    </>
                  )
                })()}
              </div>
            )}
          </div>
        )}

        {/* Transfer Response Dialog */}
        <Dialog open={respondDialog} onOpenChange={setRespondDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Respond to Ownership Transfer</DialogTitle>
              <DialogDescription>
                You have been invited to become the system owner. This will grant you full administrative privileges.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reason">Response Reason (Optional)</Label>
                <Textarea
                  id="reason"
                  placeholder="Add a reason for your response..."
                  value={responseReason}
                  onChange={(e) => setResponseReason(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
            
            <DialogFooter className="gap-2">
              <Button variant="outline" onClick={() => setRespondDialog(false)}>
                Cancel
              </Button>
              <Button 
                variant="outline" 
                onClick={() => handleRespondToTransfer('REJECT')}
                className="text-destructive hover:text-destructive"
              >
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
              <Button onClick={() => handleRespondToTransfer('ACCEPT')}>
                <CheckCircle className="mr-2 h-4 w-4" />
                Accept
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Cancel Confirmation Dialog */}
        <AlertDialog open={cancelDialog} onOpenChange={setCancelDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Cancel Ownership Transfer</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to cancel the ownership transfer? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Keep Transfer</AlertDialogCancel>
              <AlertDialogAction onClick={handleCancelTransfer} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                Cancel Transfer
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  )
}