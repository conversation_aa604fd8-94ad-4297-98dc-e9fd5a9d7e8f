"use client"

import { FC, useEffect, useState } from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    X,
    Plus,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import SearchInput from "@/components/pages/general/data-table-search-input";
import { columns } from "@/components/pages/budgets/columns";
import { useRouter } from "next/navigation";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import type { BudgetRevisionView } from "@/types/budgets/budgets.types";
import { getAllBudgets, getBudgetTypeNFormat } from "@/services/budgets/budget.service";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";

export const ArchivedBudgetTable: FC = () => {
    const [subFilters, setSubFilters] = useState<{ [key: string]: string[] }>({});
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = useState({});
    const [pageSize, setPageSize] = useState<number>(10);
    const [page, setPage] = useState<number>(1);
    const [totalCount, setTotalCount] = useState<number>(0);
    const [totalPages, setTotalPages] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [budgets, setBudgets] = useState<BudgetRevisionView[]>([]);
    const router = useRouter();
    const [budgetType, setBudgetType] = useState<{ value: string; label: string }[]>([]);
    const [budgetFormat, setBudgetFormat] = useState<{ value: string; label: string }[]>([]);

    useEffect(() => {
        fetchOptions();
        loadBudgets();
    }, [pageSize, searchTerm, subFilters]);

    const fetchOptions = async () => {
        const { budgetType, budgetFormat } = await getBudgetTypeNFormat();

        setBudgetType(
            budgetType.map((item: any) => ({ value: item.code, label: item.label }))
        );
        setBudgetFormat(
            budgetFormat.map((item: any) => ({ value: item.code, label: item.label }))
        );
    };

    const loadBudgets = async (pageToLoad = page) => {
        setLoading(true);
        setError(null);

        const { BudgetRevisionView, success, total, currentPage } = await getAllBudgets(
            { subFilters },
            pageToLoad,
            pageSize,
            true,
            searchTerm
        );

        if (success && BudgetRevisionView) {
            setBudgets(BudgetRevisionView);
            setTotalCount(total);
            setPage(currentPage);

            // Calculate total pages based on the current pageSize
            const calculatedTotalPages = Math.ceil(total / pageSize);
            setTotalPages(calculatedTotalPages);

            // If the current page is out of bounds, reset to the last valid page
            if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
                setPage(calculatedTotalPages);
            }
        } else {
            setError("Failed to fetch budgets");
        }

        setLoading(false);
    };

    const handleStatusFilter = (selectedType: string[], selectedFormat: string[]) => {
        setSubFilters(prev => ({
            ...prev,
            type: selectedType,
            format: selectedFormat
        }));
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm('');
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadBudgets(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
    };

    const table = useReactTable({
        data: budgets,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    const handleNewBudget = () => {
        // TODO
        router.push("/budgets/add-budget");
    };

    return (
        <div className="w-full space-y-4">
            {error && (
                <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
                    {error}
                </div>
            )}

            <div className="flex items-center justify-between mb-4">
                <div className="space-y-1">
                    <h1 className="text-lg font-semibold md:text-2xl">
                        Archived Budgets
                    </h1>
                    <p className="text-sm text-gray-600">
                        Review and manage previously allocated budgets in your organisation.
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button className="flex items-center" type="button" onClick={handleNewBudget}>
                        <Plus className="h-4 w-4 mr-1" />
                        Add Budget
                    </Button>
                </div>
            </div>

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <div className="relative">
                            <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search archived budgets..." />
                        </div>
                        {table.getColumn("type") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("type")}
                                title="Type"
                                options={budgetType}
                                onFilterChange={(selectedType) => handleStatusFilter(selectedType, subFilters.format)}
                            />
                        )}
                        {table.getColumn("format") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("format")}
                                title="Format"
                                options={budgetFormat}
                                onFilterChange={(selectedFormat) => handleStatusFilter(subFilters.type, selectedFormat)}
                            />
                        )}
                        {/* Reset Filters Button */}
                        {(Object.keys(subFilters).length > 0 || searchTerm) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <DataTableViewOptions table={table} />
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="text-center">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        Total {totalCount} row(s)
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={loading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};