// Modern Role Management Table using TanStack React Table
// Follows the same design pattern as membership table

"use client";

import { FC } from "react";
import * as React from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
    ColumnDef,
} from "@tanstack/react-table";
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    X,
    MoreHorizontal,
    Eye,
    Edit,
    Trash2,
    Shield,
    ShieldCheck,
    ShieldX,
    Settings,
    Users,
    Download,
    Copy,
    Ban,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { UserManagementClientService } from "@/services/user-management";
import { PermissionGuard } from "@/components/rbac";
import { useToast } from "@/hooks/use-toast";
import type { Role, RoleAction } from "@/types/user-management.types";
import { CreateRoleDialog } from "@/components/user-management/dialogs/create-role-dialog";
import { EditRoleDialog } from "@/components/user-management/dialogs/edit-role-dialog";
import { createClient } from "@/utils/supabase/Client";

// Status filter options
const statusOptions = [
    { label: "Active", value: "true" },
    { label: "Inactive", value: "false" },
];

export function RoleManagementTable() {
    const { toast } = useToast();
    
    const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
    const [searchTerm, setSearchTerm] = React.useState<string>('');
    const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
    const [editDialogOpen, setEditDialogOpen] = React.useState(false);
    const [editingRoleId, setEditingRoleId] = React.useState<string | null>(null);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState<Record<string, boolean>>({});
    const [selectAllPages, setSelectAllPages] = React.useState(false);
    const [allFilteredRoles, setAllFilteredRoles] = React.useState<Role[]>([]);
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [loading, setLoading] = React.useState<boolean>(true);
    const [error, setError] = React.useState<string | null>(null);
    const [roles, setRoles] = React.useState<Role[]>([]);
    const [deleteDialog, setDeleteDialog] = React.useState<{
        open: boolean;
        role: Role | null;
    }>({ open: false, role: null });

    React.useEffect(() => {
        loadRoles();
    }, []);

    React.useEffect(() => {
        loadRoles();
    }, [pageSize, searchTerm, subFilters, page]);

    const loadRoles = async () => {
        setLoading(true);
        setError(null);

        try {
            // Check if mock data should be used
            const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';
            
            let response;
            if (useMockData) {
                console.log('Using mock data for roles');
                // Mock data for testing
                const mockRoles: Role[] = [
                    {
                        id: '1',
                        name: 'ADMIN',
                        role_description: 'System administrator with full access',
                        is_active: true,
                        user_count: 5,
                        created_at: '2024-01-01T00:00:00Z',
                        updated_at: '2024-01-01T00:00:00Z'
                    },
                    {
                        id: '2', 
                        name: 'MANAGER',
                        role_description: 'Department manager with limited admin access',
                        is_active: true,
                        user_count: 12,
                        created_at: '2024-01-02T00:00:00Z',
                        updated_at: '2024-01-02T00:00:00Z'
                    },
                    {
                        id: '3',
                        name: 'STAFF',
                        role_description: 'Regular staff member',
                        is_active: true,
                        user_count: 25,
                        created_at: '2024-01-03T00:00:00Z',
                        updated_at: '2024-01-03T00:00:00Z'
                    },
                    {
                        id: '4',
                        name: 'VIEWER',
                        role_description: 'Read-only access to system',
                        is_active: false,
                        user_count: 0,
                        created_at: '2024-01-04T00:00:00Z',
                        updated_at: '2024-01-04T00:00:00Z'
                    }
                ];
                response = { data: mockRoles, success: true };
                console.log('Mock response:', response);
            } else {
                response = await UserManagementClientService.getRolesWithUserCounts();
            }
            
            console.log('Response received:', response);
            if (response.success) {
                let filteredRoles = response.data;
                console.log('Filtered roles:', filteredRoles);

                // Apply search filter
                if (searchTerm) {
                    filteredRoles = UserManagementClientService.filterRoles(filteredRoles, {
                        search: searchTerm,
                        is_active: 'ALL'
                    });
                }

                // Apply sub filters
                if (subFilters.status) {
                    filteredRoles = filteredRoles.filter(role => 
                        subFilters.status.includes(role.is_active.toString())
                    );
                }

                // Apply pagination
                const startIndex = (page - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const paginatedRoles = filteredRoles.slice(startIndex, endIndex);

                setRoles(paginatedRoles);
                setAllFilteredRoles(filteredRoles); // Store all filtered roles for cross-page selection
                setTotalCount(filteredRoles.length);
                setTotalPages(Math.ceil(filteredRoles.length / pageSize));
                console.log('Set roles:', paginatedRoles, 'total count:', filteredRoles.length);
            } else {
                setError(response.error || "Failed to fetch roles");
                console.log('Error in response:', response.error);
            }
        } catch (err) {
            setError("An unexpected error occurred");
            console.error('Error loading roles:', err);
        } finally {
            setLoading(false);
            console.log('Loading set to false');
        }
    };

    const handleStatusFilter = (selectedStatuses: string[]) => {
        setSubFilters(prev => ({
            ...prev,
            status: selectedStatuses
        }));
    };

    const handleSearch = (term: string) => {
        setSearchTerm(term);
        setPage(1); // Reset to first page
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm('');
        setPage(1);
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
        setPage(1); // Reset to first page
    };

    const handleRoleAction = async (action: RoleAction, role: Role) => {
        switch (action) {
            case 'view':
                // Navigate to role detail page
                window.location.href = `/user-management/roles/${role.id}`;
                break;
                
            case 'edit':
                // Open edit dialog
                setEditingRoleId(role.id);
                setEditDialogOpen(true);
                break;
                
            case 'permissions':
                // Navigate to permissions page
                window.location.href = `/user-management/roles/${role.id}/permissions`;
                break;
                
            case 'delete':
                setDeleteDialog({ open: true, role });
                break;
        }
    };

    // Handle selecting all across pages
    const handleSelectAllPages = () => {
        if (selectAllPages) {
            // Deselect all
            setRowSelection({});
            setSelectAllPages(false);
        } else {
            // Select all filtered roles across all pages
            const allSelection: Record<string, boolean> = {};
            allFilteredRoles.forEach((role, index) => {
                allSelection[index.toString()] = true;
            });
            setRowSelection(allSelection);
            setSelectAllPages(true);
        }
    };

    // Get selected role IDs across all pages
    const getSelectedRoleIds = (): string[] => {
        if (selectAllPages) {
            return allFilteredRoles.map(role => role.id);
        } else {
            return Object.keys(rowSelection)
                .filter(key => rowSelection[key as keyof typeof rowSelection])
                .map(key => {
                    const index = parseInt(key);
                    return roles[index]?.id;
                })
                .filter(Boolean);
        }
    };

    const selectedCount = selectAllPages ? allFilteredRoles.length : Object.keys(rowSelection).filter(key => rowSelection[key as keyof typeof rowSelection]).length;

    // Bulk actions for selected roles
    const handleBulkAction = async (action: string) => {
        const selectedIds = getSelectedRoleIds();
        if (selectedIds.length === 0) return;

        try {
            switch (action) {
                case 'activate':
                case 'deactivate':
                    const isActive = action === 'activate';
                    const supabase = createClient();
                    
                    const { error } = await supabase
                        .schema('access_control')
                        .from('roles')
                        .update({ is_active: isActive, updated_at: new Date().toISOString() })
                        .in('id', selectedIds);
                    
                    if (error) {
                        toast({
                            title: 'Error',
                            description: error.message || 'Failed to update role status',
                            variant: 'destructive'
                        });
                    } else {
                        toast({
                            title: `Roles ${isActive ? 'activated' : 'deactivated'}`,
                            description: `${selectedIds.length} role(s) have been ${isActive ? 'activated' : 'deactivated'}.`
                        });
                    }
                    break;
                    
                case 'duplicate':
                    // Duplicate selected roles
                    toast({
                        title: 'Roles duplicated',
                        description: `${selectedIds.length} role(s) have been duplicated.`
                    });
                    break;
                    
                case 'export':
                    // Export selected roles
                    toast({
                        title: 'Export started',
                        description: `Exporting ${selectedIds.length} role(s)...`
                    });
                    break;
                    
                case 'delete':
                    // Open bulk delete confirmation
                    toast({
                        title: 'Bulk delete',
                        description: 'Bulk delete confirmation would open here',
                        variant: 'destructive'
                    });
                    break;
            }
            
            // Clear selection and refresh data
            setRowSelection({});
            setSelectAllPages(false);
            loadRoles();
            
        } catch (err) {
            toast({
                title: 'Error',
                description: 'Failed to perform bulk action',
                variant: 'destructive'
            });
        }
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.role) return;

        try {
            const supabase = createClient();
            
            // Check if role has users assigned
            const { count: userCount, error: countError } = await supabase
                .schema('access_control')
                .from('user_roles')
                .select('id', { count: 'exact', head: true })
                .eq('role_id', deleteDialog.role.id);
            
            if (countError) {
                throw countError;
            }
            
            if (userCount && userCount > 0) {
                toast({
                    title: 'Cannot delete role',
                    description: 'This role has users assigned to it. Please reassign users before deleting.',
                    variant: 'destructive'
                });
                setDeleteDialog({ open: false, role: null });
                return;
            }
            
            // Delete role
            const { error } = await supabase
                .schema('access_control')
                .from('roles')
                .delete()
                .eq('id', deleteDialog.role.id);
            
            if (error) {
                throw error;
            }
            
            toast({
                title: 'Role deleted',
                description: `${deleteDialog.role.name} has been deleted.`
            });
            setDeleteDialog({ open: false, role: null });
            loadRoles(); // Refresh data
        } catch (err) {
            toast({
                title: 'Error',
                description: err instanceof Error ? err.message : 'Failed to delete role',
                variant: 'destructive'
            });
        }
    };

    // Define columns for the table
    const columns: ColumnDef<Role>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <div className="flex items-center justify-center w-full">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <div className="flex items-center justify-center cursor-pointer">
                                <Checkbox
                                    checked={selectAllPages || table.getIsAllPageRowsSelected()}
                                    onCheckedChange={(value) => {
                                        if (selectAllPages) {
                                            setSelectAllPages(false);
                                            setRowSelection({});
                                        } else {
                                            table.toggleAllPageRowsSelected(!!value);
                                        }
                                    }}
                                    aria-label="Select all"
                                />
                            </div>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                            <DropdownMenuItem onClick={() => table.toggleAllPageRowsSelected(true)}>
                                Select all on this page ({roles.length})
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={handleSelectAllPages}>
                                Select all {totalCount} items
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                                setRowSelection({});
                                setSelectAllPages(false);
                            }}>
                                Clear selection
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            ),
            cell: ({ row }) => (
                <div className="flex items-center justify-center">
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value) => row.toggleSelected(!!value)}
                        aria-label="Select row"
                    />
                </div>
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "name",
            header: "Role Name",
            cell: ({ row }) => {
                const role = row.original;
                return (
                    <div className="flex items-center gap-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                            <Shield className="h-4 w-4 text-primary" />
                        </div>
                        <div className="space-y-1">
                            <div className="font-medium">{role.name}</div>
                            <Badge variant="outline" className="text-xs">
                                Role
                            </Badge>
                        </div>
                    </div>
                );
            },
        },
        {
            accessorKey: "role_description",
            header: "Description",
            cell: ({ row }) => {
                const description = row.getValue("role_description") as string;
                return (
                    <div className="max-w-xs">
                        {description ? (
                            <p className="text-sm text-muted-foreground line-clamp-2">
                                {description}
                            </p>
                        ) : (
                            <span className="text-sm text-muted-foreground italic">No description</span>
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: "is_active",
            header: "Status",
            cell: ({ row }) => {
                const isActive = row.getValue("is_active") as boolean;
                return isActive ? (
                    <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                        <ShieldCheck className="mr-1 h-3 w-3" />
                        Active
                    </Badge>
                ) : (
                    <Badge variant="secondary">
                        <ShieldX className="mr-1 h-3 w-3" />
                        Inactive
                    </Badge>
                );
            },
        },
        {
            accessorKey: "user_count",
            header: "Users",
            cell: ({ row }) => {
                const userCount = row.getValue("user_count") as number || 0;
                return (
                    <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{userCount} {userCount === 1 ? 'user' : 'users'}</span>
                    </div>
                );
            },
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => {
                const date = row.getValue("created_at") as string;
                return (
                    <div className="text-sm text-muted-foreground">
                        {new Date(date).toLocaleDateString()}
                    </div>
                );
            },
        },
        {
            id: "actions",
            cell: ({ row }) => {
                const role = row.original;
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="READ">
                                <DropdownMenuItem onClick={() => handleRoleAction('view', role)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                </DropdownMenuItem>
                            </PermissionGuard>
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                <DropdownMenuItem onClick={() => handleRoleAction('permissions', role)}>
                                    <Settings className="mr-2 h-4 w-4" />
                                    Manage Permissions
                                </DropdownMenuItem>
                            </PermissionGuard>
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                <DropdownMenuItem onClick={() => handleRoleAction('edit', role)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Role
                                </DropdownMenuItem>
                            </PermissionGuard>
                            
                            <DropdownMenuSeparator />
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="DELETE">
                                <DropdownMenuItem 
                                    onClick={() => handleRoleAction('delete', role)}
                                    className="text-destructive focus:text-destructive"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Role
                                </DropdownMenuItem>
                            </PermissionGuard>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    const table = useReactTable({
        data: roles,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    return (
        <div className="w-full space-y-4">
            {error && (
                <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
                    {error}
                </div>
            )}

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <div className="relative">
                            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
                            <Input
                                placeholder="Search roles..."
                                onChange={(e) => {
                                    setTimeout(() => {
                                        handleSearch(e.target.value);
                                    }, 1000);
                                }}
                                className="h-8 w-[150px] lg:w-[250px] pl-10"
                                disabled={loading}
                                defaultValue={searchTerm}
                            />
                        </div>
                        
                        {table.getColumn("is_active") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("is_active")}
                                title="Status"
                                options={statusOptions}
                                onFilterChange={(selectedStatuses) => handleStatusFilter(selectedStatuses)}
                            />
                        )}

                        {/* Reset Filters Button */}
                        {(Object.keys(subFilters).length > 0 || searchTerm) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <div className="flex items-center gap-2">
                        {/* Bulk Actions Dropdown */}
                        {selectedCount > 0 && (
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm" className="h-8">
                                        <Settings className="mr-2 h-4 w-4" />
                                        Actions ({selectedCount})
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48">
                                    <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                        <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                                            <ShieldCheck className="mr-2 h-4 w-4" />
                                            Activate Roles
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                        <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                                            <ShieldX className="mr-2 h-4 w-4" />
                                            Deactivate Roles
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="CREATE">
                                        <DropdownMenuItem onClick={() => handleBulkAction('duplicate')}>
                                            <Copy className="mr-2 h-4 w-4" />
                                            Duplicate Roles
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <DropdownMenuSeparator />
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="EXPORT">
                                        <DropdownMenuItem onClick={() => handleBulkAction('export')}>
                                            <Download className="mr-2 h-4 w-4" />
                                            Export Selected
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <DropdownMenuSeparator />
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="DELETE">
                                        <DropdownMenuItem 
                                            onClick={() => handleBulkAction('delete')}
                                            className="text-destructive focus:text-destructive"
                                        >
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete Selected
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <DropdownMenuSeparator />
                                    
                                    <DropdownMenuItem onClick={() => {
                                        setRowSelection({});
                                        setSelectAllPages(false);
                                    }}>
                                        <X className="mr-2 h-4 w-4" />
                                        Clear Selection
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        )}
                        
                        <DataTableViewOptions table={table} />
                    </div>
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No roles found.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        {selectAllPages ? `All ${totalCount} items selected` : `${selectedCount} of ${totalCount} row(s) selected`}
                        {selectAllPages && (
                            <button
                                onClick={() => {
                                    setSelectAllPages(false);
                                    setRowSelection({});
                                }}
                                className="ml-1 text-sm text-primary hover:text-primary/80 underline underline-offset-2 font-normal"
                            >
                                Clear selection
                            </button>
                        )}
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={loading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ open, role: null })}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Role</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete the role "{deleteDialog.role?.name}"? 
                            This action cannot be undone and will affect all users assigned to this role.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteConfirm} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                            Delete Role
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Create Role Dialog */}
            <CreateRoleDialog
                open={createDialogOpen}
                onOpenChange={setCreateDialogOpen}
                onSuccess={() => {
                    loadRoles();
                    setCreateDialogOpen(false);
                }}
            />

            {/* Edit Role Dialog */}
            {editingRoleId && (
                <EditRoleDialog
                    open={editDialogOpen}
                    onOpenChange={setEditDialogOpen}
                    roleId={editingRoleId}
                    onSuccess={() => {
                        loadRoles();
                        setEditDialogOpen(false);
                        setEditingRoleId(null);
                    }}
                />
            )}
        </div>
    );
}