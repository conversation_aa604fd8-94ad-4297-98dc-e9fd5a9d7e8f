// Modern User Management Table using TanStack React Table
// Follows the same design pattern as membership table

"use client";

import * as React from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
    ColumnDef,
} from "@tanstack/react-table";
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    X,
    MoreHorizontal,
    Eye,
    Edit,
    Trash2,
    UserCheck,
    UserX,
    Mail,
    Phone,
    Crown,
    Download,
    Upload,
    Shield,
    Ban,
    Settings,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { UserManagementClientService } from "@/services/user-management";
import { PermissionGuard } from "@/components/rbac";
import { useToast } from "@/hooks/use-toast";
import type { UserWithRole, UserAction } from "@/types/user-management.types";
import { CreateUserDialog } from "@/components/user-management/dialogs/create-user-dialog";
import { EditUserDialog } from "@/components/user-management/dialogs/edit-user-dialog";
import { createClient } from "@/utils/supabase/Client";

// Status filter options
const statusOptions = [
    { label: "Active", value: "true" },
    { label: "Inactive", value: "false" },
];

// Role filter options (will be populated dynamically)
const roleOptions: { label: string; value: string }[] = [];

export function UserManagementTable() {
    const { toast } = useToast();
    
    const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
    const [searchTerm, setSearchTerm] = React.useState<string>('');
    const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
    const [editDialogOpen, setEditDialogOpen] = React.useState(false);
    const [editingUserId, setEditingUserId] = React.useState<string | null>(null);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState<Record<string, boolean>>({});
    const [selectAllPages, setSelectAllPages] = React.useState(false);
    const [allFilteredUsers, setAllFilteredUsers] = React.useState<UserWithRole[]>([]);
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [loading, setLoading] = React.useState<boolean>(true);
    const [error, setError] = React.useState<string | null>(null);
    const [users, setUsers] = React.useState<UserWithRole[]>([]);
    const [deleteDialog, setDeleteDialog] = React.useState<{
        open: boolean;
        user: UserWithRole | null;
    }>({ open: false, user: null });

    React.useEffect(() => {
        loadUsers();
        loadRoles();
    }, []);

    React.useEffect(() => {
        loadUsers();
    }, [pageSize, searchTerm, subFilters, page]);

    const loadRoles = async () => {
        try {
            const response = await UserManagementClientService.getRoles();
            if (response.success) {
                roleOptions.length = 0;
                response.data.forEach(role => {
                    roleOptions.push({
                        value: role.name,
                        label: role.name,
                    });
                });
            }
        } catch (error) {
            console.error('Error loading roles:', error);
        }
    };

    const loadUsers = async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await UserManagementClientService.getUsersWithRoles();
            
            if (response.success) {
                let filteredUsers = response.data;

                // Apply search filter
                if (searchTerm) {
                    filteredUsers = UserManagementClientService.filterUsers(filteredUsers, {
                        search: searchTerm,
                        role: 'ALL',
                        status: 'ALL'
                    });
                }

                // Apply sub filters
                if (subFilters.role) {
                    filteredUsers = filteredUsers.filter(user => 
                        subFilters.role.includes(user.role_name || 'NO_ROLE')
                    );
                }

                if (subFilters.status) {
                    filteredUsers = filteredUsers.filter(user => 
                        subFilters.status.includes(user.status.toString())
                    );
                }

                // Apply pagination
                const startIndex = (page - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

                setUsers(paginatedUsers);
                setAllFilteredUsers(filteredUsers); // Store all filtered users for cross-page selection
                setTotalCount(filteredUsers.length);
                setTotalPages(Math.ceil(filteredUsers.length / pageSize));
            } else {
                setError(response.error || "Failed to fetch users");
            }
        } catch (err) {
            setError("An unexpected error occurred");
            console.error('Error loading users:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleStatusFilter = (selectedRoles: string[], selectedStatuses: string[]) => {
        setSubFilters(prev => ({
            ...prev,
            role: selectedRoles,
            status: selectedStatuses
        }));
    };

    const handleSearch = (term: string) => {
        setSearchTerm(term);
        setPage(1); // Reset to first page
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm('');
        setPage(1);
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
        setPage(1); // Reset to first page
    };

    const handleUserAction = async (action: UserAction, user: UserWithRole) => {
        switch (action) {
            case 'view':
                // Navigate to user detail page
                window.location.href = `/user-management/users/${user.id}`;
                break;
                
            case 'edit':
                // Open edit dialog
                setEditingUserId(user.id);
                setEditDialogOpen(true);
                break;
                
            case 'delete':
                setDeleteDialog({ open: true, user });
                break;
                
            case 'activate':
            case 'deactivate':
                try {
                    const newStatus = action === 'activate';
                    const supabase = createClient();
                    
                    const { error } = await supabase
                        .from('user_meta')
                        .update({ status: newStatus, updated_at: new Date().toISOString() })
                        .eq('id', user.id);
                    
                    if (error) {
                        throw error;
                    }
                    
                    toast({
                        title: `User ${newStatus ? 'activated' : 'deactivated'}`,
                        description: `${user.full_name || user.email} has been ${newStatus ? 'activated' : 'deactivated'}.`
                    });
                    loadUsers(); // Refresh data
                } catch (err) {
                    toast({
                        title: 'Error',
                        description: 'Failed to update user status',
                        variant: 'destructive'
                    });
                }
                break;
        }
    };

    // Handle selecting all across pages
    const handleSelectAllPages = () => {
        if (selectAllPages) {
            // Deselect all
            setRowSelection({});
            setSelectAllPages(false);
        } else {
            // Select all filtered users across all pages
            const allSelection: Record<string, boolean> = {};
            allFilteredUsers.forEach((user, index) => {
                allSelection[index.toString()] = true;
            });
            setRowSelection(allSelection);
            setSelectAllPages(true);
        }
    };

    // Get selected user IDs across all pages
    const getSelectedUserIds = (): string[] => {
        if (selectAllPages) {
            return allFilteredUsers.map(user => user.id);
        } else {
            return Object.keys(rowSelection)
                .filter(key => rowSelection[key as keyof typeof rowSelection])
                .map(key => {
                    const index = parseInt(key);
                    return users[index]?.id;
                })
                .filter(Boolean);
        }
    };

    const selectedCount = selectAllPages ? allFilteredUsers.length : Object.keys(rowSelection).filter(key => rowSelection[key as keyof typeof rowSelection]).length;

    // Bulk actions for selected users
    const handleBulkAction = async (action: string) => {
        const selectedIds = getSelectedUserIds();
        if (selectedIds.length === 0) return;

        try {
            switch (action) {
                case 'activate':
                case 'deactivate':
                    const status = action === 'activate';
                    const result = await UserManagementClientService.bulkUpdateUserStatus(selectedIds, status);
                    
                    if (result.success) {
                        toast({
                            title: `Users ${status ? 'activated' : 'deactivated'}`,
                            description: `${selectedIds.length} user(s) have been ${status ? 'activated' : 'deactivated'}.`
                        });
                    } else {
                        toast({
                            title: 'Error',
                            description: result.error || 'Failed to update user status',
                            variant: 'destructive'
                        });
                    }
                    break;
                    
                case 'assign_role':
                    // Open role assignment dialog
                    toast({
                        title: 'Role assignment',
                        description: 'Role assignment dialog would open here'
                    });
                    break;
                    
                case 'export':
                    // Export selected users
                    toast({
                        title: 'Export started',
                        description: `Exporting ${selectedIds.length} user(s)...`
                    });
                    break;
                    
                case 'delete':
                    // Open bulk delete confirmation
                    toast({
                        title: 'Bulk delete',
                        description: 'Bulk delete confirmation would open here',
                        variant: 'destructive'
                    });
                    break;
            }
            
            // Clear selection and refresh data
            setRowSelection({});
            setSelectAllPages(false);
            loadUsers();
            
        } catch (err) {
            toast({
                title: 'Error',
                description: 'Failed to perform bulk action',
                variant: 'destructive'
            });
        }
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.user) return;

        try {
            const supabase = createClient();
            
            // Delete user - this will cascade to user_roles and other related tables
            const { error } = await supabase
                .from('user_meta')
                .delete()
                .eq('id', deleteDialog.user.id);
            
            if (error) {
                throw error;
            }
            
            toast({
                title: 'User deleted',
                description: `${deleteDialog.user.full_name || deleteDialog.user.email} has been deleted.`
            });
            setDeleteDialog({ open: false, user: null });
            loadUsers(); // Refresh data
        } catch (err) {
            toast({
                title: 'Error',
                description: err instanceof Error ? err.message : 'Failed to delete user',
                variant: 'destructive'
            });
        }
    };

    // Define columns for the table
    const columns: ColumnDef<UserWithRole>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <div className="flex items-center justify-center w-full">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <div className="flex items-center justify-center cursor-pointer">
                                <Checkbox
                                    checked={selectAllPages || table.getIsAllPageRowsSelected()}
                                    onCheckedChange={(value) => {
                                        if (selectAllPages) {
                                            setSelectAllPages(false);
                                            setRowSelection({});
                                        } else {
                                            table.toggleAllPageRowsSelected(!!value);
                                        }
                                    }}
                                    aria-label="Select all"
                                />
                            </div>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                            <DropdownMenuItem onClick={() => table.toggleAllPageRowsSelected(true)}>
                                Select all on this page ({users.length})
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={handleSelectAllPages}>
                                Select all {totalCount} items
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                                setRowSelection({});
                                setSelectAllPages(false);
                            }}>
                                Clear selection
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            ),
            cell: ({ row }) => (
                <div className="flex items-center justify-center">
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value) => row.toggleSelected(!!value)}
                        aria-label="Select row"
                    />
                </div>
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "full_name",
            header: "User",
            cell: ({ row }) => {
                const user = row.original;
                return (
                    <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                            <AvatarFallback>
                                {user.full_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                            </AvatarFallback>
                        </Avatar>
                        <div className="space-y-1">
                            <div className="flex items-center gap-2">
                                <span className="font-medium">{user.full_name || 'No Name'}</span>
                                {user.is_system_owner && (
                                    <Badge variant="secondary" className="bg-amber-100 text-amber-800 border-amber-300">
                                        <Crown className="mr-1 h-3 w-3" />
                                        System Owner
                                    </Badge>
                                )}
                            </div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                    </div>
                );
            },
        },
        {
            accessorKey: "role_name",
            header: "Role",
            cell: ({ row }) => {
                const roleName = row.getValue("role_name") as string;
                return roleName ? (
                    <Badge variant="outline">{roleName}</Badge>
                ) : (
                    <span className="text-sm text-muted-foreground">No Role</span>
                );
            },
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => {
                const status = row.getValue("status") as boolean;
                return status ? (
                    <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                        <UserCheck className="mr-1 h-3 w-3" />
                        Active
                    </Badge>
                ) : (
                    <Badge variant="secondary">
                        <UserX className="mr-1 h-3 w-3" />
                        Inactive
                    </Badge>
                );
            },
        },
        {
            accessorKey: "contact",
            header: "Contact",
            cell: ({ row }) => {
                const user = row.original;
                return (
                    <div className="space-y-1">
                        {user.email && (
                            <div className="flex items-center gap-1 text-sm">
                                <Mail className="h-3 w-3" />
                                <span className="truncate max-w-32">{user.email}</span>
                            </div>
                        )}
                        {user.phone_number && (
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <Phone className="h-3 w-3" />
                                <span>{user.phone_number}</span>
                            </div>
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => {
                const date = row.getValue("created_at") as string;
                return (
                    <div className="text-sm text-muted-foreground">
                        {new Date(date).toLocaleDateString()}
                    </div>
                );
            },
        },
        {
            id: "actions",
            cell: ({ row }) => {
                const user = row.original;
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="READ">
                                <DropdownMenuItem onClick={() => handleUserAction('view', user)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                </DropdownMenuItem>
                            </PermissionGuard>
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                <DropdownMenuItem onClick={() => handleUserAction('edit', user)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit User
                                </DropdownMenuItem>
                            </PermissionGuard>
                            
                            <DropdownMenuSeparator />
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                <DropdownMenuItem 
                                    onClick={() => handleUserAction(user.status ? 'deactivate' : 'activate', user)}
                                >
                                    {user.status ? (
                                        <>
                                            <UserX className="mr-2 h-4 w-4" />
                                            Deactivate
                                        </>
                                    ) : (
                                        <>
                                            <UserCheck className="mr-2 h-4 w-4" />
                                            Activate
                                        </>
                                    )}
                                </DropdownMenuItem>
                            </PermissionGuard>
                            
                            <PermissionGuard module="USER_MANAGEMENT" action="DELETE">
                                <DropdownMenuItem 
                                    onClick={() => handleUserAction('delete', user)}
                                    className="text-destructive focus:text-destructive"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete User
                                </DropdownMenuItem>
                            </PermissionGuard>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    const table = useReactTable({
        data: users,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    return (
        <div className="w-full space-y-4">
            {error && (
                <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
                    {error}
                </div>
            )}

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <div className="relative">
                            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
                            <Input
                                placeholder="Search users..."
                                onChange={(e) => {
                                    setTimeout(() => {
                                        handleSearch(e.target.value);
                                    }, 1000);
                                }}
                                className="h-8 w-[150px] lg:w-[250px] pl-10"
                                disabled={loading}
                                defaultValue={searchTerm}
                            />
                        </div>
                        
                        {table.getColumn("role_name") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("role_name")}
                                title="Role"
                                options={roleOptions}
                                onFilterChange={(selectedRoles) => handleStatusFilter(selectedRoles, subFilters.status)}
                            />
                        )}
                        
                        {table.getColumn("status") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("status")}
                                title="Status"
                                options={statusOptions}
                                onFilterChange={(selectedStatuses) => handleStatusFilter(subFilters.role, selectedStatuses)}
                            />
                        )}

                        {/* Reset Filters Button */}
                        {(Object.keys(subFilters).length > 0 || searchTerm) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <div className="flex items-center gap-2">
                        {/* Bulk Actions Dropdown */}
                        {selectedCount > 0 && (
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm" className="h-8">
                                        <Settings className="mr-2 h-4 w-4" />
                                        Actions ({selectedCount})
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48">
                                    <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                        <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                                            <UserCheck className="mr-2 h-4 w-4" />
                                            Activate Users
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                        <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                                            <UserX className="mr-2 h-4 w-4" />
                                            Deactivate Users
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="UPDATE">
                                        <DropdownMenuItem onClick={() => handleBulkAction('assign_role')}>
                                            <Shield className="mr-2 h-4 w-4" />
                                            Assign Role
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <DropdownMenuSeparator />
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="EXPORT">
                                        <DropdownMenuItem onClick={() => handleBulkAction('export')}>
                                            <Download className="mr-2 h-4 w-4" />
                                            Export Selected
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <DropdownMenuSeparator />
                                    
                                    <PermissionGuard module="USER_MANAGEMENT" action="DELETE">
                                        <DropdownMenuItem 
                                            onClick={() => handleBulkAction('delete')}
                                            className="text-destructive focus:text-destructive"
                                        >
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete Selected
                                        </DropdownMenuItem>
                                    </PermissionGuard>
                                    
                                    <DropdownMenuSeparator />
                                    
                                    <DropdownMenuItem onClick={() => {
                                        setRowSelection({});
                                        setSelectAllPages(false);
                                    }}>
                                        <X className="mr-2 h-4 w-4" />
                                        Clear Selection
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        )}
                        
                        <DataTableViewOptions table={table} />
                    </div>
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No users found.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        {selectAllPages ? `All ${totalCount} items selected` : `${selectedCount} of ${totalCount} row(s) selected`}
                        {selectAllPages && (
                            <button
                                onClick={() => {
                                    setSelectAllPages(false);
                                    setRowSelection({});
                                }}
                                className="ml-1 text-sm text-primary hover:text-primary/80 underline underline-offset-2 font-normal"
                            >
                                Clear selection
                            </button>
                        )}
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={loading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ open, user: null })}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete User</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete {deleteDialog.user?.full_name || deleteDialog.user?.email}? 
                            This action cannot be undone and will remove all associated data.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteConfirm} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                            Delete User
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Create User Dialog */}
            <CreateUserDialog
                open={createDialogOpen}
                onOpenChange={setCreateDialogOpen}
                onSuccess={() => {
                    loadUsers();
                    setCreateDialogOpen(false);
                }}
            />

            {/* Edit User Dialog */}
            {editingUserId && (
                <EditUserDialog
                    open={editDialogOpen}
                    onOpenChange={setEditDialogOpen}
                    userId={editingUserId}
                    onSuccess={() => {
                        loadUsers();
                        setEditDialogOpen(false);
                        setEditingUserId(null);
                    }}
                />
            )}
        </div>
    );
}