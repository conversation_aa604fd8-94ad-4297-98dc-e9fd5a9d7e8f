"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { format } from "date-fns";
import type { Database } from "@/types/database.types";
import { ReportService } from "@/services/report.service";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { DataTablePagination } from "@/components/ui/data-table-pagination";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import SearchInput from "@/components/pages/general/data-table-search-input";

type ExportJob = Database['public']['Tables']['export_jobs']['Row'];
type ExportType = Database['public']['Enums']['export_type'];

interface ReportTableProps {
  exportType: ExportType;
}

export function ReportTable({ exportType }: ReportTableProps) {
  const { toast } = useToast();
  const [reports, setReports] = useState<ExportJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState<string | null>(null);
  const [bulkDownloading, setBulkDownloading] = useState(false);

  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const columns: ColumnDef<ExportJob>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "created_at",
      header: "Date Created",
      cell: ({ row }) => format(new Date(row.getValue("created_at")), 'yyyy-MM-dd HH:mm:ss'),
    },
    {
      accessorKey: "filename",
      header: "File Name",
      cell: ({ row }) => row.getValue("filename"),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;

        const getApplicationStatusStyle = (status: string) => {
          switch (status) {
            case 'COMPLETED':
              return 'bg-green-200 text-black';
            case 'FAILED':
              return 'bg-red-200 text-black';
            default:
              return 'bg-yellow-200 text-black';
          }
        };

        return (
          <div className="flex items-center">
            <button className={`px-4 py-1 rounded-3xl ${getApplicationStatusStyle(status)}`}
              style={{ textTransform: "capitalize" }}>
              {status.toLowerCase()}
            </button>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const report = row.original;
        return (
          <div className="flex items-center gap-2">
            {report.status === 'COMPLETED' && report.filename && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownload(report)}
                disabled={downloading === report.id}
              >
                <Download className="h-4 w-4 mr-2" />
                {downloading === report.id ? 'Downloading...' : 'Download'}
              </Button>
            )}
            {report.status === 'FAILED' && (
              <span className="text-red-500 text-sm">
                {report.error || 'Export failed'}
              </span>
            )}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: reports,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  useEffect(() => {
    fetchReports();
  }, [exportType]);

  const fetchReports = async () => {
    try {
      const data = await ReportService.fetchReports(exportType);
      setReports(data);
    } catch {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch reports",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (report: ExportJob) => {
    if (!report.filename) return;

    setDownloading(report.id);
    try {
      await ReportService.downloadReport(report);
    } catch {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to download report",
      });
    } finally {
      setDownloading(null);
    }
  };

  const handleBulkDownload = async () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const completedReports = selectedRows
      .map(row => row.original)
      .filter(report => report.status === 'COMPLETED' && report.filename);

    if (completedReports.length === 0) return;

    setBulkDownloading(true);
    try {
      await ReportService.downloadMultipleReports(completedReports);
    } catch {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to download selected reports",
      });
    } finally {
      setBulkDownloading(false);
    }
  };

  const selectedCount = table.getSelectedRowModel().rows.length;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <SearchInput onSearch={(event) => table.getColumn("filename")?.setFilterValue(event)} value={(table.getColumn("filename")?.getFilterValue() as string) ?? ""} placeholder="Filter by filename..." />
          {selectedCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkDownload}
              disabled={bulkDownloading}
              className="h-8 px-3 text-sm"
            >
              <Download className="h-3.5 w-3.5 mr-2" />
              {bulkDownloading
                ? 'Downloading...'
                : `Download Selected (${selectedCount})`}
            </Button>
          )}
        </div>
        <DataTableViewOptions table={table} />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length}>
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-center">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
