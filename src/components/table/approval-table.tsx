"use client";
import { FC } from "react";
import * as React from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    X,
    Download,
    Loader2,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { columns } from "@/components/pages/approvals/columns";
import { ApprovalService } from "@/services/approvals/approval.service";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { 
    ApprovalTableRow, 
    ApprovalFilters, 
    APPROVAL_STATUSES, 
    MODULE_TYPES 
} from "@/types/approvals/approval.types";
import { useToast } from "@/hooks/use-toast";

export const ApprovalTable: FC = () => {
    const { toast } = useToast();
    const [mounted, setMounted] = React.useState(false);
    const [filters, setFilters] = React.useState<ApprovalFilters>({});
    const [searchTerm, setSearchTerm] = React.useState<string>('');
    const [searchInput, setSearchInput] = React.useState<string>('');
    const [isSearching, setIsSearching] = React.useState<boolean>(false);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [loading, setLoading] = React.useState<boolean>(true);
    const [error, setError] = React.useState<string | null>(null);
    const [approvals, setApprovals] = React.useState<ApprovalTableRow[]>([]);
    const searchRequestIdRef = React.useRef<number>(0);

    React.useEffect(() => {
        setMounted(true);
    }, []);

    React.useEffect(() => {
        loadApprovals();
    }, []);

    React.useEffect(() => {
        if (mounted) {
            loadApprovals();
        }
    }, [filters, pageSize, mounted]);

    const loadApprovals = async (pageToLoad = page) => {
        setLoading(true);
        setError(null);

        try {
            const response = await ApprovalService.getAllApprovalRequests(
                filters,
                pageToLoad,
                pageSize,
                searchTerm
            );

            setApprovals(response.data);
            setTotalCount(response.total);
            setPage(response.page);
            setTotalPages(response.totalPages);

            if (pageToLoad > response.totalPages && response.totalPages > 0) {
                setPage(response.totalPages);
            }
        } catch (error) {
            console.error('Error loading approvals:', error);
            setError("Failed to fetch approval requests. Please try again later.");
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to load approval requests"
            });
        } finally {
            setLoading(false);
        }
    };

    const performSearch = async (searchValue: string, requestId: number) => {
        try {
            const response = await ApprovalService.getAllApprovalRequests(
                filters,
                1, // Reset to first page for new search
                pageSize,
                searchValue
            );

            // Only update if this is still the latest search request
            if (requestId === searchRequestIdRef.current) {
                setApprovals(response.data);
                setTotalCount(response.total);
                setPage(response.page);
                setTotalPages(response.totalPages);
                setSearchTerm(searchValue);
                setIsSearching(false);
            }
        } catch (error) {
            // Only show error if this is still the latest search request
            if (requestId === searchRequestIdRef.current) {
                console.error('Error searching approvals:', error);
                setIsSearching(false);
                toast({
                    variant: "destructive",
                    title: "Search Error",
                    description: "Failed to search approval requests"
                });
            }
        }
    };

    const handleStatusFilter = (selectedStatuses: string[], selectedModuleTypes: string[]) => {
        setFilters(prev => ({
            ...prev,
            status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
            module_type: selectedModuleTypes.length > 0 ? selectedModuleTypes : undefined
        }));
    };

    const handleSearch = (value: string) => {
        setSearchInput(value);
        
        // Increment request ID for new search
        searchRequestIdRef.current += 1;
        const currentRequestId = searchRequestIdRef.current;
        
        // Set searching state immediately
        setIsSearching(true);
        
        // Perform search in background
        performSearch(value, currentRequestId);
    };

    const resetFilters = () => {
        setFilters({});
        setColumnFilters([]);
        setSearchTerm('');
        setSearchInput('');
        setIsSearching(false);
        searchRequestIdRef.current += 1; // Cancel any pending searches
        toast({
            title: "Success",
            description: "Filters cleared"
        });
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadApprovals(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
    };

    const handleExport = async () => {
        try {
            // This would be implemented based on your export requirements
            toast({
                title: "Information",
                description: "Export functionality not yet implemented"
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to export data"
            });
        }
    };

    const table = useReactTable({
        data: approvals,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        manualPagination: true,
        manualSorting: true,
        manualFiltering: true,
        pageCount: totalPages,
        autoResetPageIndex: false,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
            pagination: {
                pageIndex: page - 1,
                pageSize: pageSize,
            },
        },
    });

    return (
        <div className="w-full space-y-4">
            {error && (
                <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
                    {error}
                </div>
            )}

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <div className="relative">
                            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
                            {isSearching && (
                                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-500" />
                            )}
                            <Input
                                placeholder="Search by request number, module type..."
                                value={searchInput}
                                onChange={(e) => handleSearch(e.target.value)}
                                className="h-8 w-[150px] lg:w-[250px] pl-10 pr-10"
                                disabled={loading}
                            />
                        </div>
                        
                        {table.getColumn("status") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("status")}
                                title="Status"
                                options={[...APPROVAL_STATUSES]}
                                onFilterChange={(selectedStatuses) => handleStatusFilter(selectedStatuses, filters.module_type || [])}
                            />
                        )}
                        
                        {table.getColumn("module_type") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("module_type")}
                                title="Module Type"
                                options={[...MODULE_TYPES]}
                                onFilterChange={(selectedModuleTypes) => handleStatusFilter(filters.status || [], selectedModuleTypes)}
                            />
                        )}

                        {/* Reset Filters Button */}
                        {(Object.keys(filters).length > 0 || searchInput) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleExport}
                            disabled={loading || approvals.length === 0}
                            className="h-8"
                        >
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                        <DataTableViewOptions table={table} />
                    </div>
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <div className="flex items-center justify-center py-8">
                                            <div className="flex items-center space-x-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                                <span className="text-sm text-gray-600">Loading approval requests...</span>
                                            </div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="text-center">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        <div className="flex flex-col items-center justify-center space-y-2">
                                            <p className="text-gray-500">No approval requests found</p>
                                            <p className="text-sm text-gray-400">
                                                {searchTerm || Object.keys(filters).length > 0
                                                    ? "Try adjusting your search or filters"
                                                    : "No approval requests have been submitted yet"}
                                            </p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        {table.getFilteredSelectedRowModel().rows.length} of {totalCount} row(s) selected.
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={loading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages || 1}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1 || loading}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1 || loading}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages || loading}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages || loading}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};