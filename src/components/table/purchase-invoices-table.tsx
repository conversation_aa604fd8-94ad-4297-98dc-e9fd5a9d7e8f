"use client";

import { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
  RowSelectionState,
} from "@tanstack/react-table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Loader2,
  Plus,
  Upload,
  Download,
  X,
  Filter,
  CreditCard,
  Clock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  Di<PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { usePurchaseInvoiceColumns } from "@/components/purchase-invoices/columns";
import {
  getPurchaseInvoices,
  exportPurchaseInvoices,
  getInvoiceStatuses,
} from "@/services/purchase-invoices/purchase-invoices.service";
import type {
  PurchaseInvoice,
  PurchaseInvoiceFilters,
  PurchaseInvoiceSummary,
  PurchaseInvoiceStatus,
  PaymentStatus,
} from "@/types/purchase-invoices/purchase-invoices.types";

interface PurchaseInvoicesTableProps {
  summary: PurchaseInvoiceSummary;
  onSummaryUpdate: (summary: PurchaseInvoiceSummary) => void;
}

export function PurchaseInvoicesTable({ summary, onSummaryUpdate }: PurchaseInvoicesTableProps) {
  const [data, setData] = useState<PurchaseInvoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  
  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  
  // Filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<PurchaseInvoiceStatus[]>([]);
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<PaymentStatus[]>([]);
  const [supplierId, setSupplierId] = useState("");
  const [entityId, setEntityId] = useState("");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [showOverdueOnly, setShowOverdueOnly] = useState(false);
  const [requiresThreeWayMatch, setRequiresThreeWayMatch] = useState(false);
  
  // Pagination state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // Export state
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<string | null>(null);

  const columns = usePurchaseInvoiceColumns();

  const filters: PurchaseInvoiceFilters = useMemo(() => ({
    search: searchTerm || undefined,
    status: statusFilter.length > 0 ? statusFilter : undefined,
    payment_status: paymentStatusFilter.length > 0 ? paymentStatusFilter : undefined,
    supplier_id: supplierId || undefined,
    entity_id: entityId || undefined,
    date_from: dateFrom || undefined,
    date_to: dateTo || undefined,
    is_overdue: showOverdueOnly || undefined,
    requires_three_way_match: requiresThreeWayMatch || undefined,
    page,
    limit: pageSize,
    sort_by: sorting[0]?.id,
    sort_order: sorting[0]?.desc ? "desc" : "asc",
  }), [searchTerm, statusFilter, paymentStatusFilter, supplierId, entityId, dateFrom, dateTo, showOverdueOnly, requiresThreeWayMatch, page, pageSize, sorting]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    manualPagination: true,
    manualSorting: true,
    pageCount: totalPages,
  });

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getPurchaseInvoices(filters);
      setData(response.data);
      setTotalCount(response.total);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load purchase invoices");
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setExportStatus("Preparing export...");
      
      const exportData = await exportPurchaseInvoices(filters);
      
      setExportStatus("Generating file...");
      
      // Create CSV content
      const csvContent = [
        // Headers
        "Invoice #,Supplier,PO Number,Invoice Date,Due Date,Total Amount,Outstanding,Status,Payment Status",
        // Data rows
        ...exportData.invoices.map(invoice => [
          invoice.invoice_number,
          invoice.supplier?.name || "",
          invoice.purchase_order?.po_number || "",
          invoice.invoice_date,
          invoice.due_date || "",
          invoice.total_amount,
          invoice.outstanding_amount || 0,
          invoice.status,
          invoice.payment_status,
        ].join(","))
      ].join("\n");

      // Download file
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `purchase-invoices-${new Date().toISOString().split("T")[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      
      setExportStatus("Export completed successfully!");
      setTimeout(() => setExportStatus(null), 3000);
    } catch (err) {
      setExportStatus("Export failed. Please try again.");
      setTimeout(() => setExportStatus(null), 5000);
    } finally {
      setIsExporting(false);
    }
  };

  const handleBulkPayment = () => {
    const selectedInvoices = Object.keys(rowSelection).map(index => data[parseInt(index)]);
    const approvedInvoices = selectedInvoices.filter(inv => 
      inv.status === "APPROVED" && (inv.outstanding_amount || 0) > 0
    );
    
    if (approvedInvoices.length === 0) {
      alert("Please select approved invoices with outstanding amounts for payment.");
      return;
    }

    const invoiceIds = approvedInvoices.map(inv => inv.id).join(",");
    window.location.href = `/purchase-invoices/payment?invoice_ids=${invoiceIds}`;
  };

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter([]);
    setPaymentStatusFilter([]);
    setSupplierId("");
    setEntityId("");
    setDateFrom("");
    setDateTo("");
    setShowOverdueOnly(false);
    setRequiresThreeWayMatch(false);
    setPage(1);
  };

  const statusOptions = [
    { value: "RECEIVED", label: "Received" },
    { value: "PENDING_APPROVAL", label: "Pending Approval" },
    { value: "APPROVED", label: "Approved" },
    { value: "PAID", label: "Paid" },
    { value: "DISPUTED", label: "Disputed" },
    { value: "CANCELLED", label: "Cancelled" },
  ];

  const paymentStatusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "PARTIAL", label: "Partial" },
    { value: "COMPLETED", label: "Completed" },
    { value: "FAILED", label: "Failed" },
  ];

  const selectedRowCount = Object.keys(rowSelection).length;
  const selectedInvoices = Object.keys(rowSelection).map(index => data[parseInt(index)]);
  const payableInvoices = selectedInvoices.filter(inv => 
    inv.status === "APPROVED" && (inv.outstanding_amount || 0) > 0
  );

  return (
    <div className="w-full space-y-4">
      {error && (
        <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
          {error}
        </div>
      )}

      {selectedRowCount > 0 && (
        <div className="mb-2 p-2 text-black-600 bg-blue-100 border border-blue-300 rounded flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <span>{selectedRowCount} rows selected</span>
            <Button
              variant="link"
              size="sm"
              onClick={() => setRowSelection({})}
              className="h-auto p-0 text-sm underline"
            >
              Clear selection
            </Button>
          </div>
          {payableInvoices.length > 0 && (
            <Button
              size="sm"
              onClick={handleBulkPayment}
              className="flex items-center gap-2"
            >
              <CreditCard className="h-4 w-4" />
              Process Payment ({payableInvoices.length})
            </Button>
          )}
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
            <Input
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-8 w-[150px] lg:w-[250px] pl-10"
            />
          </div>

          <DataTableFacetedFilter
            title="Status"
            options={statusOptions}
            onFilterChange={(values) => {
              setStatusFilter(values as PurchaseInvoiceStatus[]);
            }}
          />

          <DataTableFacetedFilter
            title="Payment"
            options={paymentStatusOptions}
            onFilterChange={(values) => {
              setPaymentStatusFilter(values as PaymentStatus[]);
            }}
          />

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowOverdueOnly(!showOverdueOnly)}
            className={`h-8 border-dashed ${showOverdueOnly ? "bg-primary text-primary-foreground" : ""}`}
          >
            <Clock className="mr-2 h-4 w-4" />
            Overdue only
          </Button>

          {(searchTerm || statusFilter.length > 0 || paymentStatusFilter.length > 0 || showOverdueOnly) && (
            <Button
              variant="ghost"
              onClick={resetFilters}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {Object.keys(rowSelection).length > 0 && (
            <span className="text-sm text-muted-foreground">
              {Object.keys(rowSelection).length} of {data.length} row(s) selected
            </span>
          )}
          <Button
            variant="outline"
            size="sm"
            disabled={loading || data.length === 0}
            className="h-8"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <DataTableViewOptions table={table} />
        </div>
      </div>

      {exportStatus && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
          {exportStatus}
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="whitespace-nowrap">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading purchase invoices...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="hover:bg-muted/50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="whitespace-nowrap">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No purchase invoices found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          {selectedRowCount > 0 ? (
            `${selectedRowCount} of ${totalCount} row(s) selected.`
          ) : (
            `Showing ${Math.min(pageSize, totalCount)} of ${totalCount} results`
          )}
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={`${pageSize}`}
              onValueChange={(value) => {
                setPageSize(Number(value));
                setPage(1);
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((size) => (
                  <SelectItem key={size} value={`${size}`}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {page} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setPage(1)}
              disabled={page === 1}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setPage(totalPages)}
              disabled={page === totalPages}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}