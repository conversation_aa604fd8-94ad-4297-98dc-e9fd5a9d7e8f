"use client";

import { FC } from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    flexRender,
} from "@tanstack/react-table";
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
    Loader2,
    Upload,
    X,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { paymentMethod, paymentStatus } from "@/types/finance/receipts.types";
import { useReceiptsTablePage } from "@/hooks/table/use-receipts-table";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogTrigger,
    DialogFooter,
    DialogClose,
} from "@/components/ui/dialog";
import SearchInput from "@/components/pages/general/data-table-search-input";

export const ReceiptsTable: FC = () => {
    const {
        table,
        loading,
        error,
        page,
        subFilters,
        searchTerm,
        totalCount,
        totalPages,
        pageSize,
        isExporting,
        exportStatus,
        columns,
        selectedRows,
        setSearchTerm,
        handleSelectAllRows,
        setIsExporting,
        handleStatusFilter,
        resetFilters,
        handlePageChange,
        handleLimitChange,
        handleExport,
    } = useReceiptsTablePage();

    return (
        <div className="w-full space-y-4">
            {error && (
                <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
                    {error}
                </div>
            )}

            {selectedRows.size !== 0 && (
                <div className="mb-2 p-2 text-black-600 bg-blue-100 border border-blue-300 rounded flex items-center text-sm">
                    <span className="mr-1">{selectedRows.size} rows selected,</span>
                    <a
                        href="#"
                        onClick={(e) => {
                            e.preventDefault();
                            handleSelectAllRows();
                        }}
                        className="underline cursor-pointer"
                    >
                        Select all {totalCount} rows.
                    </a>
                </div>
            )}

            <div className="flex items-center justify-between mb-4">
                <Dialog>
                    <DialogTrigger asChild>
                        <Button
                            variant="outline"
                            className="flex items-center"
                            disabled={loading || selectedRows.size === 0}
                        >
                            <Upload className="mr-2 h-4 w-4" />
                            Export
                        </Button>
                    </DialogTrigger>

                    <DialogContent className="rounded-xl p-8">
                        <DialogHeader>
                            <DialogTitle>Are you sure you want to export?</DialogTitle>
                            <DialogDescription>
                                {selectedRows.size} row{selectedRows.size !== 1 ? 's' : ''} selected to export.
                            </DialogDescription>
                        </DialogHeader>

                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">
                                    Close
                                </Button>
                            </DialogClose>
                            <DialogClose asChild>
                                <Button
                                    variant="default"
                                    onClick={handleExport}
                                    className="ml-2 rounded-md"
                                >
                                    Confirm Export
                                </Button>
                            </DialogClose>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>

            <div className="border-b mb-4"></div>

            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search receipts..." />

                        {table.getColumn("payment_method") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("payment_method")}
                                title="Payment Method"
                                options={paymentMethod}
                                onFilterChange={(selectedPaymentMethod) => handleStatusFilter(selectedPaymentMethod, subFilters.payment_status)}
                            />
                        )}
                        {table.getColumn("payment_status") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("payment_status")}
                                title="Payment Status"
                                options={paymentStatus}
                                onFilterChange={(selectedPaymentStatus) => handleStatusFilter(subFilters.payment_method, selectedPaymentStatus)}
                            />
                        )}
                        {/* Reset Filters Button */}
                        {(Object.keys(subFilters).length > 0 || searchTerm) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <DataTableViewOptions table={table} />
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="text-center">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        {selectedRows.size} of {totalCount} row(s) selected.
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={loading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>

                <Dialog open={isExporting} onOpenChange={setIsExporting}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Exporting Receipts</DialogTitle>
                        </DialogHeader>
                        <div className="py-6">
                            {exportStatus?.status === 'PENDING' && (
                                <div className="flex items-center gap-2">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Preparing export...</span>
                                </div>
                            )}
                            {exportStatus?.status === 'PROCESSING' && (
                                <div className="flex items-center gap-2">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Processing data...</span>
                                </div>
                            )}
                            {exportStatus?.status === 'COMPLETED' && (
                                <div className="text-green-600">
                                    Export completed! Downloading file...
                                </div>
                            )}
                            {exportStatus?.status === 'FAILED' && (
                                <div className="text-red-600">
                                    Export failed: {exportStatus.error}
                                </div>
                            )}
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </div>
    );
};