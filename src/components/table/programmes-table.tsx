"use client";
import { FC, useEffect, useState } from "react";
import * as React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  X,
  Plus,
  RotateCcw,
  PlusCircle,
} from "lucide-react"
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { columns } from "@/components/pages/programmes/columns";
import { toast } from "@/hooks/use-toast";
import { createClient } from "@/utils/supabase/Client";
import { useRouter } from "next/navigation";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import { getAllProgramme, getStaffData } from "@/services/programmes/events-client.service";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import SearchInput from "@/components/pages/general/data-table-search-input";
import { ProgrammeList, statuses, types } from "@/types/programmes/programme.types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { RerunProgrammeDialog } from "@/components/programmes/rerun-programme-dialog";
import { DropdownOption } from "@/types/general.types";

export const ProgrammesTable: FC = () => {
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pageSize, setPageSize] = useState<number>(10);
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [subFilters, setSubFilters] = useState<{ [key: string]: string[] }>({});
  const [searchTerm, setSearchTerm] = useState<string>();
  const [programme, setProgramme] = useState<ProgrammeList[]>([]);
  const [staff, setStaff] = useState<DropdownOption[]>([]);

  useEffect(() => {
    fetchOptions();
    // Reset to page 1 when filters or sorting change
    if (sorting.length > 0 || Object.keys(subFilters).length > 0 || searchTerm) {
      setPage(1);
      loadProgramme(1);
    } else {
      loadProgramme();
    }
  }, [pageSize, searchTerm, subFilters, sorting]);

  const loadProgramme = async (pageToLoad = page) => {
    setLoading(true);
    setError(null);

    // Map column names for sorting (frontend column ID to database column)
    const columnMapping: { [key: string]: string } = {
      'programme_code': 'programme_code',
      'name': 'name',
      'type': 'type',
      'status': 'status',
      'staff_in_charge_name': 'staff_in_charge_name',
      'coordinator_name': 'coordinator_name',
      'programmeDate': 'created_at' // For now, sort by created_at for programme date
    };

    // Get the first sorting item if exists
    let sortBy = sorting.length > 0 ? sorting[0].id : undefined;
    const sortDirection = sorting.length > 0 ? (sorting[0].desc ? 'desc' : 'asc') : undefined;

    // Map the column name if it exists in the mapping
    if (sortBy && columnMapping[sortBy]) {
      sortBy = columnMapping[sortBy];
    }

    const { programme, success, total, currentPage } = await getAllProgramme(
      { subFilters },
      pageToLoad,
      pageSize,
      false,
      false,
      searchTerm,
      sortBy,
      sortDirection
    );

    if (success && programme) {
      setProgramme(programme);
      setTotalCount(total);
      setPage(currentPage);

      // Calculate total pages based on the current pageSize
      const calculatedTotalPages = Math.ceil(total / pageSize);
      setTotalPages(calculatedTotalPages);

      // If the current page is out of bounds, reset to the last valid page
      if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
        setPage(calculatedTotalPages);
      }
    } else {
      setError("Failed to fetch Programmes");
    }

    setLoading(false);
  };

  const fetchOptions = async () => {
    const allStaffData = await getStaffData();

    setStaff(
      allStaffData.map((item) => ({ value: item.id, label: item.name }))
    );
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      loadProgramme(newPage);
    }
  };

  const handleLimitChange = (value: string) => {
    setPageSize(Number(value));
  };

  const resetFilters = () => {
    setSubFilters({});
    setColumnFilters([]);
    setSearchTerm(undefined);
  };

  const handleStatusFilter = (selectedFilters: string[], columnId?: string) => {
    if (!columnId) return;

    setSubFilters(prev => {
      const newFilters = { ...prev };

      if (selectedFilters.length > 0) {
        newFilters[columnId] = selectedFilters;
      } else {
        // If empty, delete the key instead of setting to undefined
        delete newFilters[columnId];
      }

      return newFilters;
    });
  };

  const table = useReactTable({
    data: programme,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    manualSorting: true, // Enable manual sorting for server-side
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  const handleNewProgramme = () => {
    router.push("/programmes/add-programme");
  };

  const [isRerunDialogOpen, setIsRerunDialogOpen] = useState<boolean>(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

  const handleRerun = () => {
    setIsRerunDialogOpen(true);
    // Close the dropdown when opening the dialog
    setIsDropdownOpen(false);
  };

  const handleRerunConfirm = async (programme: ProgrammeList) => {
    try {
      setIsRerunDialogOpen(false);
      // Show loading toast
      toast({
        title: "Creating re-run...",
        description: "Creating a re-run of the programme. Please wait.",
      });

      // Call the Supabase function to clone the programme
      const supabase = await createClient();
      const { data, error } = await supabase
        .schema('programme')
        .rpc('clone_programme_api', {
          source_programme_id: programme.id
        });

      if (error) {
        throw error;
      }

      // The function returns the new clone programme id
      const newProgrammeId = data;

      // Show success toast
      toast({
        title: "Re-run Created",
        description: "Programme has been successfully re-run. Navigating to details page.",
        variant: "default",
      });

      // Navigate to the programme details page
      router.push(`/programmes/${newProgrammeId}/edit`);
    } catch (error) {
      console.error("Error creating re-run:", error);
      toast({
        title: "Error",
        description: "Failed to create re-run. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle dialog state change
  const handleDialogOpenChange = (open: boolean) => {
    setIsRerunDialogOpen(open);
    // Ensure dropdown is closed when dialog state changes
    if (!open) {
      setIsDropdownOpen(false);
    }
  };

  return (
    <div className="w-full space-y-4">
      {error && (
        <div className="mb-4 p-4 text-red-600 bg-red-100 border border-red-300 rounded">
          {error}
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        <div className="space-y-1">
          <h1 className="text-lg font-semibold md:text-2xl">
            Programmes
          </h1>
          <p className="text-sm text-gray-600">
            Create, manage, and monitor all your programmes in one place.
          </p>
        </div>
        <div className="flex gap-2">
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button className="flex items-center">
                <Plus className="h-4 w-4 mr-1" />
                Add Programme
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleNewProgramme}>
                <PlusCircle className="h-4 w-4 mr-2" />
                New Programme
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleRerun}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Re-run
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Re-run Dialog */}
      <RerunProgrammeDialog
        open={isRerunDialogOpen}
        onOpenChange={handleDialogOpenChange}
        onConfirm={handleRerunConfirm}
      />

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search programmes..." />
            {table.getColumn("type") && (
              <DataTableFacetedFilter
                column={table.getColumn("type")}
                title="Type"
                options={types}
                onFilterChange={handleStatusFilter}
                columnId="type"
              />
            )}

            {table.getColumn("status") && (
              <DataTableFacetedFilter
                column={table.getColumn("status")}
                title="Status"
                options={statuses}
                onFilterChange={handleStatusFilter}
                columnId="status"
              />
            )}

            {table.getColumn("staff_in_charge_name") && (
              <DataTableFacetedFilter
                column={table.getColumn("staff_in_charge_name")}
                title="Staff in Charge"
                options={staff}
                onFilterChange={handleStatusFilter}
                columnId="staff_in_charge_id"
              />
            )}

            {/* Reset Filters Button */}
            {(Object.keys(subFilters).length > 0 || searchTerm) && (
              <Button
                variant="ghost"
                onClick={resetFilters}
                className="h-8 px-2 lg:px-3"
              >
                Reset
                <X className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>

          <DataTableViewOptions table={table} />
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={columns.length}>
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="text-center">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between px-2">
          <div className="flex-1 text-sm text-muted-foreground">
            Total {totalCount} row(s)
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <Select
                value={pageSize.toString()}
                onValueChange={handleLimitChange}
                disabled={loading}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {page} of {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(1);
                }}
                disabled={page <= 1}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(page - 1);
                }}
                disabled={page <= 1}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(page + 1);
                }}
                disabled={page >= totalPages}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(totalPages);
                }}
                disabled={page >= totalPages}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};