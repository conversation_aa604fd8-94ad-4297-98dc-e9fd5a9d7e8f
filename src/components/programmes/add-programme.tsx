"use client"

import React from "react";
import { Form<PERSON>rovider } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { CalendarIcon, CheckIcon, ChevronLeft, ChevronRight, XIcon } from "lucide-react";
import { format } from "date-fns";
import { useAddProgramme } from "@/hooks/programmes/use-add-programme";
import SearchInput from "@/components/pages/general/data-table-search-input";

export default function AddProgramme() {
    const {
        form,
        isLoading,
        isSubmitting,
        venue,
        department,
        filteredStaff,
        committees,
        scheduleFields,
        isSharing,
        isSearching,
        searchQuery,
        setSearchQuery,
        page,
        totalPages,
        searchResults,
        selectedBudget,
        setSelectedBudget,
        handlePageChange,
        handleDepartmentChange,
        handleSubmit,
        addDateTime,
        removeDateTime,
    } = useAddProgramme();

    if (isLoading) {
        return <LoadingSpinner />;
    }

    return (
        <>
            <FormProvider {...form}>
                <form className="space-y-6">
                    <Card className="mt-4 p-8">
                        <h1 className="text-xl font-semibold mb-4">Programme Details</h1>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                                control={form.control}
                                name="programmeType"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Programme Type</FormLabel>
                                        <FormControl>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select Programme Type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="COURSE">Course</SelectItem>
                                                    <SelectItem value="EVENT">Event</SelectItem>
                                                    <SelectItem value="SEMINAR">Seminar</SelectItem>
                                                    <SelectItem value="CONFERENCE">
                                                        Conferences
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Programme Name</FormLabel>
                                        <FormControl>
                                                                        <Input
                                placeholder="Enter programme name"
                                maxLength={255}
                                {...field}
                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="programmeFormat"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Programme Format</FormLabel>
                                        <FormControl>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select programme format" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="IN-PERSON">In Person</SelectItem>
                                                    <SelectItem value="VIRTUAL">Virtual</SelectItem>
                                                    <SelectItem value="HYBRID">Hybird</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="venueId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Programme Venue</FormLabel>
                                        <FormControl>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select programme venue" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {venue.length > 0 ? (
                                                        venue
                                                            .map((venue) => (
                                                                <SelectItem key={venue.id} value={venue.id}>
                                                                    {venue.name}
                                                                </SelectItem>
                                                            ))
                                                    ) : (
                                                        <SelectItem value="no-options" disabled>
                                                            No venue available
                                                        </SelectItem>
                                                    )}
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="col-span-full">
                                <FormField
                                    control={form.control}
                                    name="description"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Programme Description</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="Enter programme description"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="col-span-full rounded-md bg-gray-100 p-2 flex items-center">
                                <h2 className="text-md text-gray-500 font-medium ">
                                    Date & Time
                                </h2>
                            </div>

                            <div className="col-span-full">
                                {scheduleFields.map((dateTime, index) => (
                                    <div key={dateTime.fieldId}>
                                        <div className="flex items-center mb-6">
                                            <span className="text-lg font-semibold text-gray-700">{`Day ${index + 1}`}</span>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                            <FormField
                                                control={form.control}
                                                name={`schedules.${index}.date`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>Date</FormLabel>
                                                        <FormControl>
                                                            <Popover>
                                                                <PopoverTrigger asChild>
                                                                    <div className="relative">
                                                                        <Input
                                                                            type="text"
                                                                            readOnly
                                                                            className="pointer-events-none"
                                                                            placeholder="Pick a date"
                                                                            value={field.value ? format(field.value, "PPP") : ""}
                                                                        />
                                                                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                                    </div>
                                                                </PopoverTrigger>
                                                                <PopoverContent>
                                                                    <Calendar
                                                                        mode="single"
                                                                        selected={field.value}
                                                                        onSelect={(date) => {
                                                                            if (date) {
                                                                                const utcDate = new Date(Date.UTC(
                                                                                    date.getFullYear(),
                                                                                    date.getMonth(),
                                                                                    date.getDate()
                                                                                ));
                                                                                field.onChange(utcDate);
                                                                            }
                                                                        }}
                                                                        disabled={[{ before: new Date() }]}
                                                                    />
                                                                </PopoverContent>
                                                            </Popover>
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name={`schedules.${index}.startTime`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>Start Time</FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="time"
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />

                                            <FormField
                                                control={form.control}
                                                name={`schedules.${index}.endTime`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel>End Time</FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="time"
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>

                                        <div className="flex items-center justify-end mt-4 space-x-4">
                                            {index === scheduleFields.length - 1 && (
                                                <Button type="button" onClick={addDateTime}>
                                                    Add
                                                </Button>
                                            )}
                                            {scheduleFields.length > 1 && (
                                                <Button
                                                    type="button"
                                                    onClick={() => removeDateTime(index)}
                                                    variant='destructive'
                                                >
                                                    Remove
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Border line */}
                        <div className="col-span-full border-b border-gray-300 my-4" />

                        <div className="col-span-full mb-4">
                            <h3 className="text-lg font-medium mb-3">Department Information</h3>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                                <FormField
                                    control={form.control}
                                    name="departmentId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Organizing Department</FormLabel>
                                            <FormControl>
                                                <Select
                                                    onValueChange={(value) => {
                                                        handleDepartmentChange(value);
                                                        field.onChange(value);
                                                        //reset field
                                                        form.setValue("staffInChargeId", "");
                                                    }}
                                                    value={field.value}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select Department" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {department.length > 0 ? (
                                                            department
                                                                .map((department) => (
                                                                    <SelectItem key={department.id} value={department.id}>
                                                                        {department.name}
                                                                    </SelectItem>
                                                                ))
                                                        ) : (
                                                            <SelectItem value="no-options" disabled>
                                                                No department available
                                                            </SelectItem>
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="staffInChargeId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Staff in Charge</FormLabel>
                                            <FormControl>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select Staff" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {
                                                            filteredStaff.length === 0 ? (
                                                                <SelectItem value="none" disabled>
                                                                    {form.watch("departmentId") ? "No staff found for this department" : "Select the department first to view staff"}
                                                                </SelectItem>
                                                            ) : (
                                                                filteredStaff
                                                                    .map((staffMember) => (
                                                                        <SelectItem key={staffMember.id} value={staffMember.id}>
                                                                            {staffMember.name}
                                                                        </SelectItem>
                                                                    ))
                                                            )
                                                        }
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <div className="col-span-full mb-4">
                            <h3 className="text-lg font-medium mb-3">Committee Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="committeeId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Committee</FormLabel>
                                            <FormControl>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select Committee (Optional)" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {committees.length > 0 ? (
                                                            <>
                                                                <SelectItem value="none">None</SelectItem>

                                                                {committees.map((committee) => (
                                                                    <SelectItem key={committee.id} value={committee.id}>
                                                                        {committee.name}
                                                                    </SelectItem>
                                                                ))}
                                                            </>
                                                        ) : (
                                                            <SelectItem value="no-options" disabled>
                                                                No committees available
                                                            </SelectItem>
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="secondary_committeeId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Secondary Committee</FormLabel>
                                            <FormControl>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select Committee (Optional)" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {committees.length > 0 ? (
                                                            <>
                                                                <SelectItem value="none">None</SelectItem>

                                                                {committees.map((committee) => (
                                                                    <SelectItem key={committee.id} value={committee.id}>
                                                                        {committee.name}
                                                                    </SelectItem>
                                                                ))}
                                                            </>
                                                        ) : (
                                                            <SelectItem value="no-options" disabled>
                                                                No committees available
                                                            </SelectItem>
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <div className="col-span-full mb-4">
                            <FormField
                                control={form.control}
                                name="remark"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Remark</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="Enter remarks here..."
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="flex items-center space-x-2 mb-4">
                            <FormField
                                name="thirdPartyCheck"
                                control={form.control}
                                render={({ field }) => (
                                    <Checkbox
                                        checked={field.value}
                                        onCheckedChange={(value) => field.onChange(!!value)}
                                    />
                                )}
                            />
                            <label>Sharing with a third party?</label>
                        </div>

                        {isSharing && (
                            <FormField
                                control={form.control}
                                name="thirdPartyEntity"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Third Party Entity</FormLabel>
                                        <FormControl>
                                                                        <Input
                                placeholder="Enter the third party entity name"
                                maxLength={255}
                                {...field}
                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        )}
                    </Card>

                    <Card className="mt-4 p-8 space-y-2">
                        <h1 className="text-xl font-semibold mb-4">Programme Budget</h1>

                        <div className="relative">
                            <SearchInput onSearch={setSearchQuery} value={searchQuery} placeholder="Search budget..." />
                        </div>

                        {selectedBudget && (
                            <div
                                key={selectedBudget.latest_approval_id}
                                className={`flex flex-col items-start gap-1 p-3 cursor-pointer bg-green-50 border rounded-md border-green-200`}
                            >
                                <div className="flex items-center gap-2 w-full">
                                    <span className="font-medium">{selectedBudget.budget_code}</span>
                                    {selectedBudget.format && (
                                        <Badge variant="secondary" className="text-xs">
                                            {selectedBudget.format}
                                        </Badge>
                                    )}
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="ml-auto hover:bg-red-500"
                                        onClick={() => {
                                            setSelectedBudget(null); // Clear selected budget
                                            form.setValue('budget_revision_id', ""); // Clear form field
                                        }}
                                    >
                                        <XIcon className="h-4 w-4" />
                                    </Button>
                                </div>
                                <div className="text-sm text-gray-500">{selectedBudget.latest_title}</div>
                            </div>
                        )}

                        <FormField
                            control={form.control}
                            name="budget_revision_id"
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <div>
                                            <Table>
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead className="w-[30%]">Budget Code</TableHead>
                                                        <TableHead className="w-[20%]">Entity Type</TableHead>
                                                        <TableHead className="w-[40%]">Title</TableHead>
                                                        <TableHead className="w-[10%]">Selected</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {isSearching ? (
                                                        <TableRow>
                                                            <TableCell colSpan={4} className="text-center">
                                                                <LoadingSpinner />
                                                            </TableCell>
                                                        </TableRow>
                                                    ) : searchResults.length > 0 ? (
                                                        searchResults.map((result) => (
                                                            <TableRow
                                                                key={result.latest_approval_id}
                                                                onClick={() => {
                                                                    field.onChange(result.latest_approval_id);
                                                                    setSelectedBudget(result);
                                                                }}
                                                                className={`text-sm hover:bg-gray-50 cursor-pointer`}
                                                            >
                                                                <TableCell className="flex items-center gap-2">
                                                                    <span>{result.budget_code}</span>
                                                                    {result.format && (
                                                                        <Badge variant="secondary" className="text-xs">
                                                                            {result.format}
                                                                        </Badge>
                                                                    )}
                                                                </TableCell>
                                                                <TableCell>{result.entity_type}</TableCell>
                                                                <TableCell className="whitespace-normal break-words max-w-xs">
                                                                    {result.latest_title}
                                                                </TableCell>
                                                                <TableCell>
                                                                    {field.value === result.latest_approval_id && (
                                                                        <CheckIcon size={16} className="text-green-500 stroke-[3]" />
                                                                    )}
                                                                </TableCell>
                                                            </TableRow>
                                                        ))
                                                    ) : (
                                                        <TableRow>
                                                            <TableCell colSpan={4} className="text-center">
                                                                {searchQuery
                                                                    ? "No budget found. Try a different search term."
                                                                    : "Type to search for budget."}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}
                                                </TableBody>
                                            </Table>
                                            <div className="flex items-center justify-end gap-2">
                                                <Button
                                                    variant="outline"
                                                    className="h-8 w-8 p-0"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(page - 1);
                                                    }}
                                                    disabled={page <= 1}
                                                >
                                                    <span className="sr-only">Go to previous page</span>
                                                    <ChevronLeft />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    className="h-8 w-8 p-0"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(page + 1);
                                                    }}
                                                    disabled={page >= totalPages}
                                                >
                                                    <span className="sr-only">Go to next page</span>
                                                    <ChevronRight />
                                                </Button>
                                            </div>
                                        </div>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </Card>
                </form>
            </FormProvider>

            <div className="flex justify-end mt-6 space-x-6">
                <Dialog>
                    <DialogTrigger asChild>
                        <Button>Create Programme</Button>
                    </DialogTrigger>

                    <DialogContent>
                        <DialogTitle>Confirm Programme Creation</DialogTitle>
                        <DialogDescription>
                            Once created, the budget format cannot be changed. Are you sure you want to continue?
                        </DialogDescription>

                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">
                                    Cancel
                                </Button>
                            </DialogClose>
                            <Button onClick={handleSubmit} disabled={isSubmitting}>Submit</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </>
    );
}