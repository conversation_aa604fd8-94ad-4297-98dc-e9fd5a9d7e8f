"use client";

import * as React from "react";
import { ProgrammeList, ProgrammeRegistrationsInterface } from "@/types/programmes/programme.types";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { CheckIcon, AlertCircle, Users } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Alert,
  AlertDescription,
} from "@/components/ui/alert";
import { getAllProgramme, transferRegistration, getProgrammeRegistrationsCount } from "@/services/programmes/events-client.service";
import { toast } from "@/hooks/use-toast";

interface TransferRegistrationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  registration: ProgrammeRegistrationsInterface;
  currentProgramme: {
    id: string;
    name: string;
    programme_code: string;
  };
  onSuccess: () => void;
}

export const TransferRegistrationDialog: React.FC<TransferRegistrationDialogProps> = ({
  open,
  onOpenChange,
  registration,
  currentProgramme,
  onSuccess,
}) => {
  const [searchQuery, setSearchQuery] = React.useState<string>("");
  const [searchResults, setSearchResults] = React.useState<ProgrammeList[]>([]);
  const [selectedProgramme, setSelectedProgramme] = React.useState<ProgrammeList | null>(null);
  const [isSearching, setIsSearching] = React.useState<boolean>(false);
  const [isTransferring, setIsTransferring] = React.useState<boolean>(false);
  const [showConfirmation, setShowConfirmation] = React.useState<boolean>(false);
  const [participantCounts, setParticipantCounts] = React.useState<Record<string, number>>({});

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setSearchQuery("");
      setSearchResults([]);
      setSelectedProgramme(null);
      setShowConfirmation(false);
      setParticipantCounts({});
    }
  }, [open]);

  // Fetch participant counts for search results
  React.useEffect(() => {
    const fetchCounts = async () => {
      if (searchResults.length === 0) return;
      
      const counts: Record<string, number> = {};
      await Promise.all(
        searchResults.map(async (programme) => {
          try {
            const count = await getProgrammeRegistrationsCount(programme.id);
            counts[programme.id] = count;
          } catch (error) {
            counts[programme.id] = 0;
          }
        })
      );
      setParticipantCounts(counts);
    };

    fetchCounts();
  }, [searchResults]);

  // Search for programmes
  const handleProgrammeSearch = (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }
    
    setIsSearching(true);

    // Debounce search to avoid too many requests
    setTimeout(async () => {
      try {
        const { programme, success } = await getAllProgramme(
          { subFilters: {} },
          1,  // page
          10, // limit
          true,
          false,
          query, // search term
        );

        if (success && programme) {
          // Filter out the current programme from results
          const filteredResults = programme.filter(p => p.id !== currentProgramme.id);
          setSearchResults(filteredResults);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        console.error("Error searching programmes:", error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300);
  };

  // Select a programme
  const handleSelectProgramme = (programme: ProgrammeList) => {
    setSelectedProgramme(programme);
  };

  // Handle initial transfer button click
  const handleTransferClick = () => {
    if (selectedProgramme) {
      setShowConfirmation(true);
    }
  };

  // Handle final confirmation
  const handleConfirmTransfer = async () => {
    if (!selectedProgramme) return;

    setIsTransferring(true);
    try {
      const result = await transferRegistration(registration.id, currentProgramme.id, selectedProgramme.id);
      
      if (result.success) {
        toast({
          title: "Transfer Successful",
          description: `Registration has been transferred to ${selectedProgramme.name}`,
        });
        onSuccess();
        onOpenChange(false);
      } else {
        toast({
          title: "Transfer Failed",
          description: result.error || "Failed to transfer registration. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error transferring registration:", error);
      toast({
        title: "Transfer Failed",
        description: "An error occurred while transferring the registration.",
        variant: "destructive",
      });
    } finally {
      setIsTransferring(false);
    }
  };

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Transfer Registration</DialogTitle>
          <DialogDescription>
            Transfer this registration to a different programme. Search by programme name or code.
          </DialogDescription>
        </DialogHeader>

        {!showConfirmation ? (
          <div className="mt-4 space-y-4">
            {/* Current Programme Info */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-gray-700 mb-1">Current Programme</p>
              <div className="space-y-1">
                <p className="text-sm font-semibold">{currentProgramme.name}</p>
                <p className="text-xs text-gray-500">Code: {currentProgramme.programme_code}</p>
              </div>
            </div>

            {/* Search Input */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
              <Input
                placeholder="Search by programme name or code..."
                className="w-full pl-10"
                value={searchQuery}
                onChange={(e) => handleProgrammeSearch(e.target.value)}
              />
            </div>

            {/* Search Results */}
            <div className="max-h-80 overflow-y-auto border rounded-md">
              {isSearching ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="divide-y">
                  {searchResults.map((programme) => (
                    <div
                      key={programme.id}
                      className={`p-4 relative hover:bg-gray-100 cursor-pointer transition-colors ${
                        selectedProgramme?.id === programme.id ? 'bg-green-50 border-l-4 border-green-500' : ''
                      }`}
                      onClick={() => handleSelectProgramme(programme)}
                    >
                      <div className="pr-8">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1 mr-2">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-sm" title={programme.name}>
                                {truncateText(programme.name || '', 60)}
                              </span>
                              {programme.type && (
                                <Badge variant="secondary" className="text-xs shrink-0">
                                  {programme.type}
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-gray-500">
                              Code: {programme.programme_code}
                            </div>
                          </div>
                          {selectedProgramme?.id === programme.id && (
                            <div className="absolute right-4 top-4 text-green-500">
                              <CheckIcon size={20} className="stroke-[3]" />
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-600">
                          <div className="flex items-center gap-1">
                            <Users size={14} />
                            <span>{participantCounts[programme.id] || 0} participants</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : searchQuery ? (
                <div className="p-8 text-center text-gray-500">
                  No programmes found. Try a different search term.
                </div>
              ) : (
                <div className="p-8 text-center text-gray-500">
                  Type to search for programmes by name or code.
                </div>
              )}
            </div>

            {/* Info text showing selection state */}
            <div className="text-sm text-gray-600">
              {selectedProgramme ?
                `Selected: ${truncateText(selectedProgramme.name || '', 50)}` :
                "Select a programme to enable the transfer button."}
            </div>
          </div>
        ) : (
          /* Confirmation Screen */
          <div className="mt-4 space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please confirm the transfer details below. This action cannot be undone.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm font-medium text-gray-700 mb-2">Transfer Details</p>
                <div className="space-y-3">
                  <div>
                    <p className="text-xs text-gray-500">From:</p>
                    <p className="text-sm font-medium">{currentProgramme.name}</p>
                    <p className="text-xs text-gray-500">Code: {currentProgramme.programme_code}</p>
                  </div>
                  <div className="border-t pt-3">
                    <p className="text-xs text-gray-500">To:</p>
                    <p className="text-sm font-medium">{selectedProgramme?.name}</p>
                    <p className="text-xs text-gray-500">Code: {selectedProgramme?.programme_code}</p>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <p className="text-sm font-medium text-yellow-800 mb-1">Registration Information</p>
                <p className="text-xs text-yellow-700">
                  Contact Person: {registration.contact_persons?.name || 'N/A'}
                </p>
                <p className="text-xs text-yellow-700">
                  Registration Type: {registration.registration_type || 'N/A'}
                </p>
                <p className="text-xs text-yellow-700">
                  Number of Participants: {registration.participants?.[0]?.count || 0}
                </p>
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="mt-6">
          {!showConfirmation ? (
            <>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleTransferClick} 
                disabled={!selectedProgramme}
              >
                Transfer Registration
              </Button>
            </>
          ) : (
            <>
              <Button 
                variant="outline" 
                onClick={handleCancelConfirmation}
                disabled={isTransferring}
              >
                Back
              </Button>
              <Button 
                variant="destructive"
                onClick={handleConfirmTransfer}
                disabled={isTransferring}
              >
                {isTransferring ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Transferring...
                  </>
                ) : (
                  'Confirm Transfer'
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};