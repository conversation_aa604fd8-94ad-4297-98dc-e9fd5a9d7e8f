"use client";

import * as React from "react";
import { ProgrammeList } from "@/types/programmes/programme.types";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { CheckIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { getAllProgramme } from "@/services/programmes/events-client.service";

interface RerunProgrammeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (programme: ProgrammeList) => void;
}

export const RerunProgrammeDialog: React.FC<RerunProgrammeDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
}) => {
  const [searchQuery, setSearchQuery] = React.useState<string>("");
  const [searchResults, setSearchResults] = React.useState<ProgrammeList[]>([]);
  const [selectedProgramme, setSelectedProgramme] = React.useState<ProgrammeList | null>(null);
  const [isSearching, setIsSearching] = React.useState<boolean>(false);

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setSearchQuery("");
      setSearchResults([]);
      setSelectedProgramme(null);
    }
  }, [open]);

  // Search for programmes
  const handleProgrammeSearch = (query: string) => {
    setSearchQuery(query);
    setIsSearching(true);

    // Debounce search to avoid too many requests
    setTimeout(async () => {
      try {
        // Reuse the getAllProgramme
        const { programme, success } = await getAllProgramme(
          { subFilters: {} }, // empty handling
          1,  // page
          10, // limit
          true,
          false,
          query, // search term
        );

        if (success && programme) {
          console.log('Parent programmes from search:', programme);
          setSearchResults(programme);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        console.error("Error searching programmes:", error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300);
  };

  // Select a programme
  const handleSelectProgramme = (programme: ProgrammeList) => {
    setSelectedProgramme(programme);
    setSearchQuery(programme.name || ""); // Update search input with selected programme name
  };

  // Handle confirmation
  const handleConfirm = () => {
    if (selectedProgramme) {
      onConfirm(selectedProgramme);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Re-run Programme</DialogTitle>
          <DialogDescription>
            Search and select an existing programme to re-run. You must select a programme to proceed.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 space-y-4">
          {/* Search Input */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
            <Input
              placeholder="Search for a programme..."
              className="w-full pl-10"
              value={searchQuery}
              onChange={(e) => handleProgrammeSearch(e.target.value)}
            />
          </div>

          {/* Search Results */}
          <div className="max-h-60 overflow-y-auto border rounded-md">
            {isSearching ? (
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="divide-y">
                {searchResults.map((programme) => (
                  <div
                    key={programme.id}
                    className={`p-3 relative hover:bg-gray-100 cursor-pointer ${selectedProgramme?.id === programme.id ? 'bg-green-50 border border-green-200' : ''}`}
                    onClick={() => handleSelectProgramme(programme)}
                  >
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{programme.name}</span>
                      {programme.type && (
                        <Badge
                          variant="secondary"
                          className="text-xs"
                        >
                          {programme.type}
                        </Badge>
                      )}
                      {selectedProgramme?.id === programme.id && (
                        <div className="absolute right-3 text-green-500">
                          <CheckIcon size={16} className="stroke-[3]" />
                        </div>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {programme.programme_code}
                    </div>
                  </div>
                ))}
              </div>
            ) : searchQuery ? (
              <div className="p-4 text-center text-gray-500">
                No programmes found. Try a different search term.
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                Type to search for programmes.
              </div>
            )}
          </div>

          {/* Info text showing selection state */}
          <div className="text-sm text-gray-500 mt-2">
            {selectedProgramme ?
              "Programme selected. Click Re-run to continue." :
              "Select a programme to enable the Re-run button."}
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={!selectedProgramme}>
            Create Re-run
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
