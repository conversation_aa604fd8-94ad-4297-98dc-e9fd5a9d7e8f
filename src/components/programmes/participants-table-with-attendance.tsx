"use client";

import { useState, useMemo } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import SearchInput from "@/components/pages/general/data-table-search-input";
import { SimpleFilter } from "@/components/attendance/simple-filter";
import { useBulkCheckIn } from "@/hooks/attendance/use-bulk-checkin-simple";
import { ParticipantWithAttendance } from "@/types/attendance/attendance.types";
import { QuickCheckInButton } from "@/components/attendance/quick-checkin-button";
import {
  User<PERSON>he<PERSON>,
  UserX,
  Clock,
  Award,
  MoreHorizontal,
  History,
  CheckCircle,
  XCircle,
  AlertCircle,
  Shield,
  Timer
} from "lucide-react";
import { cn } from "@/lib/utils";
import { AttendanceConfiguration } from "@/types/attendance/attendance.types";
import { sendParticipantCertificate, updateParticipantCertificateNumber } from "@/services/programmes/participant-server.service";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";

interface ParticipantsTableWithAttendanceProps {
  programmeId: string;
  participants: ParticipantWithAttendance[];
  isLoading: boolean;
  onQuickCheckIn: (participantId: string) => void;
  onViewHistory: (participantId: string) => void;
  isParticipantCertificateVisible: boolean;
  isCheckingIn?: boolean;
  searchTerm?: string;
  onSearchChange?: (term: string) => void;
  onRefresh?: () => void;
  attendanceConfig?: AttendanceConfiguration | null;
}

// Attendance status badge component
function AttendanceStatusBadge({
  status,
  showIcon = true,
  size = "default"
}: {
  status: string;
  showIcon?: boolean;
  size?: "sm" | "default";
}) {
  const getStatusConfig = (status: string) => {
    switch (status?.toLowerCase()) {
      case "present":
        return {
          variant: "default" as const,
          className: "bg-green-100 text-green-800 border-green-200 hover:bg-green-100",
          icon: <CheckCircle className="h-3 w-3" />,
          label: "Present"
        };
      case "absent":
        return {
          variant: "outline" as const,
          className: "bg-red-100 text-red-800 border-red-200 hover:bg-red-100",
          icon: <XCircle className="h-3 w-3" />,
          label: "Absent"
        };
      case "late":
        return {
          variant: "outline" as const,
          className: "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-100",
          icon: <Timer className="h-3 w-3" />,
          label: "Late"
        };
      case "excused":
        return {
          variant: "outline" as const,
          className: "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-100",
          icon: <Shield className="h-3 w-3" />,
          label: "Excused"
        };
      case "partial":
        return {
          variant: "outline" as const,
          className: "bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-100",
          icon: <AlertCircle className="h-3 w-3" />,
          label: "Partial"
        };
      default:
        return {
          variant: "outline" as const,
          className: "",
          icon: <XCircle className="h-3 w-3" />,
          label: status || "Unknown"
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge
      variant={config.variant}
      className={cn(
        "font-medium gap-1",
        size === "sm" && "text-xs px-2 py-0.5",
        config.className
      )}
    >
      {showIcon && config.icon}
      {config.label}
    </Badge>
  );
}

// Progress bar for attendance percentage
function AttendanceProgressBar({
  percentage,
  certificateEligible
}: {
  percentage: number;
  certificateEligible: boolean;
}) {
  const getColor = () => {
    if (certificateEligible) return "bg-green-500";
    if (percentage >= 80) return "bg-blue-500";
    if (percentage >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <div className="flex items-center gap-2 min-w-24">
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div
          className={cn("h-2 rounded-full transition-all duration-300", getColor())}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      <span className="text-xs font-medium min-w-8">
        {percentage}%
      </span>
      {certificateEligible && (
        <Award className="h-3 w-3 text-green-600" />
      )}
    </div>
  );
}

export function ParticipantsTableWithAttendance({
  programmeId,
  participants,
  isLoading,
  onQuickCheckIn,
  onViewHistory,
  isParticipantCertificateVisible,
  isCheckingIn = false,
  searchTerm = "",
  onSearchChange,
  onRefresh,
  attendanceConfig
}: ParticipantsTableWithAttendanceProps) {
  const [internalSearchTerm, setInternalSearchTerm] = useState(searchTerm);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [certificateFilter, setCertificateFilter] = useState<string[]>([]);
  const [certificateDialogOpen, setCertificateDialogOpen] = useState(false);
  const [selectedParticipantId, setSelectedParticipantId] = useState<string>("");
  const [certificateNumber, setCertificateNumber] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Bulk selection management
  const {
    selectedParticipants,
    toggleParticipantSelection,
    selectAllParticipants,
    selectNoneParticipants,
    getSelectionSummary
  } = useBulkCheckIn({ programmeId });

  // Filter participants
  const filteredParticipants = useMemo(() => {
    let filtered = participants;

    // Search filter
    const searchValue = onSearchChange ? searchTerm : internalSearchTerm;
    if (searchValue) {
      const normalizedSearch = searchValue.toLowerCase();
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(normalizedSearch) ||
        p.email.toLowerCase().includes(normalizedSearch) ||
        p.contact_no?.toLowerCase().includes(normalizedSearch)
      );
    }

    // Status filter
    if (statusFilter.length > 0) {
      filtered = filtered.filter(p =>
        statusFilter.includes(p.current_attendance_status)
      );
    }

    // Certificate filter
    if (certificateFilter.length > 0) {
      filtered = filtered.filter(p => {
        if (certificateFilter.includes("eligible")) return p.certificate_eligible;
        if (certificateFilter.includes("not_eligible")) return !p.certificate_eligible;
        return true;
      });
    }

    return filtered;
  }, [participants, internalSearchTerm, searchTerm, statusFilter, certificateFilter, onSearchChange]);

  const selectionSummary = getSelectionSummary(participants);

  // Handle search
  const handleSearch = (term: string) => {
    if (onSearchChange) {
      onSearchChange(term);
    } else {
      setInternalSearchTerm(term);
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      selectAllParticipants(filteredParticipants);
    } else {
      selectNoneParticipants();
    }
  };

  // Filter options
  const statusOptions = [
    { value: "present", label: "Present", icon: CheckCircle },
    { value: "absent", label: "Absent", icon: XCircle },
    { value: "late", label: "Late", icon: Timer },
    { value: "excused", label: "Excused", icon: Shield },
    { value: "partial", label: "Partial", icon: AlertCircle }
  ];

  const certificateOptions = [
    { value: "eligible", label: "Certificate Eligible", icon: Award },
    { value: "not_eligible", label: "Not Eligible", icon: XCircle }
  ];

  const allSelected = filteredParticipants.length > 0 &&
    filteredParticipants.every(p => selectedParticipants.includes(p.id));
  const someSelected = filteredParticipants.some(p => selectedParticipants.includes(p.id));

  const handleParticipantCertificate = async (participantId: string) => {
    // Start toast
    toast({
      title: "Queuing Certificate Email...",
      description: "Please wait while we queue the participant's certificate for sending.",
    });

    const result = await sendParticipantCertificate(participantId, programmeId);

    if (!result.success) {
      // Error toast
      toast({
        title: "Failed to Queue Certificate",
        description: result.error || "An unexpected error occurred while queuing the email.",
        variant: "destructive",
      });
      return;
    }

    // Success toast
    toast({
      title: "Certificate Queued",
      description: "The participant's certificate email has been queued for sending.",
    });
  };

  const handleOpenCertificateDialog = (participantId: string, certificate_no?: string) => {
    setSelectedParticipantId(participantId);
    setCertificateDialogOpen(true);
    setCertificateNumber(certificate_no ?? "");
    setError("");
  };

  const handleParticipantCertificateNumber = async () => {
    if (!certificateNumber.trim()) {
      setError("Please enter a certificate number.");
      return;
    }

    setError("");

    toast({
      title: "Updating Certificate Number",
      description: "Please wait while we save the certificate number...",
    });

    const result = await updateParticipantCertificateNumber(
      selectedParticipantId,
      certificateNumber.trim()
    );

    if (!result.success) {
      toast({
        title: "Update Failed",
        description: result.error || "An unexpected error occurred.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Certificate Number Updated",
      description: "The certificate number has been saved successfully.",
    });

    setCertificateDialogOpen(false);
    setCertificateNumber("");
    setSelectedParticipantId("");

    onRefresh?.();
  };

  return (
    <div className="space-y-4 p-4">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <SearchInput
            onSearch={handleSearch}
            value={onSearchChange ? searchTerm : internalSearchTerm}
            placeholder="Search participants..."
          />

          <SimpleFilter
            title="Status"
            options={statusOptions}
            selectedValues={statusFilter}
            onSelectionChange={setStatusFilter}
          />

          <SimpleFilter
            title="Certificate"
            options={certificateOptions}
            selectedValues={certificateFilter}
            onSelectionChange={setCertificateFilter}
          />

          {(statusFilter.length > 0 || certificateFilter.length > 0 || internalSearchTerm) && (
            <Button
              variant="ghost"
              onClick={() => {
                setStatusFilter([]);
                setCertificateFilter([]);
                setInternalSearchTerm("");
                onSearchChange?.("");
              }}
              className="h-8 px-2 lg:px-3"
            >
              Reset
            </Button>
          )}
        </div>

        {/* Selection Summary */}
        {selectionSummary.total > 0 && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{selectionSummary.total} selected</span>
            <Badge variant="outline">
              {selectionSummary.canCheckIn} can check in
            </Badge>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="w-12">
                <Checkbox
                  checked={allSelected}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead className="font-medium">Name</TableHead>
              <TableHead className="font-medium">Email</TableHead>
              <TableHead className="font-medium hidden md:table-cell">Contact</TableHead>
              <TableHead className="font-medium">Attendance Status</TableHead>
              <TableHead className="font-medium">Attendance %</TableHead>
              <TableHead className="font-medium hidden lg:table-cell">Last Check-in</TableHead>
              <TableHead className="font-medium">Certificate Number</TableHead>
              <TableHead className="font-medium">Certificate Status</TableHead>
              <TableHead className="font-medium">Quick Action</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <LoadingSpinner />
                </TableCell>
              </TableRow>
            ) : filteredParticipants.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                  No participants found
                </TableCell>
              </TableRow>
            ) : (
              filteredParticipants.map((participant, index) => (
                <TableRow
                  key={participant.id}
                  className={cn(
                    "hover:bg-muted/50 transition-colors",
                    index % 2 === 0 ? 'bg-background' : 'bg-muted/20',
                    selectedParticipants.includes(participant.id) && "bg-blue-50"
                  )}
                >
                  <TableCell>
                    <Checkbox
                      checked={selectedParticipants.includes(participant.id)}
                      onCheckedChange={() => toggleParticipantSelection(participant.id)}
                    />
                  </TableCell>

                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span className="font-semibold">{participant.name}</span>
                      <span className="text-xs text-muted-foreground hidden sm:block">
                        {participant.salutation}
                      </span>
                    </div>
                  </TableCell>

                  <TableCell className="text-sm">
                    {participant.email}
                  </TableCell>

                  <TableCell className="text-sm hidden md:table-cell">
                    {participant.contact_no}
                  </TableCell>

                  <TableCell>
                    <AttendanceStatusBadge
                      status={participant.current_attendance_status}
                    />
                  </TableCell>

                  <TableCell>
                    <AttendanceProgressBar
                      percentage={participant.attendance_percentage}
                      certificateEligible={participant.certificate_eligible}
                    />
                  </TableCell>

                  <TableCell className="text-sm text-muted-foreground hidden lg:table-cell">
                    {participant.last_check_in_time ? (
                      new Date(participant.last_check_in_time).toLocaleString()
                    ) : (
                      "Never"
                    )}
                  </TableCell>

                  <TableCell>
                    <p>{participant.certificate_no || "-"}</p>
                  </TableCell>

                  <TableCell>
                    <p>{participant.certificate_status || "-"}</p>
                  </TableCell>

                  <TableCell>
                    <QuickCheckInButton
                      participantId={participant.id}
                      programmeId={programmeId}
                      currentStatus={participant.current_attendance_status}
                      onSuccess={() => {
                        onRefresh?.();
                      }}
                      size="small"
                      disabled={isCheckingIn}
                      requireCheckout={attendanceConfig?.require_checkout || false}
                      canCheckOut={participant.can_check_out}
                    />
                  </TableCell>

                  <TableCell>
                    <DropdownMenu
                      open={openDropdownId === participant.id}
                      onOpenChange={(open) => setOpenDropdownId(open ? participant.id : null)}
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            onViewHistory(participant.id);
                            setOpenDropdownId(null);
                          }}
                        >
                          <History className="h-4 w-4 mr-2" />
                          View History
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            handleParticipantCertificate(participant.id);
                            setOpenDropdownId(null);
                          }}
                          disabled={!isParticipantCertificateVisible || !participant.certificate_no}
                        >
                          <Award className="h-4 w-4 mr-2" />
                          Send Completion Certificate
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            handleOpenCertificateDialog(participant.id, participant.certificate_no);
                            setOpenDropdownId(null);
                          }}
                          disabled={participant.certificate_status === 'SENT'}
                        >
                          <UserCheck className="h-4 w-4 mr-2" />
                          Update Certificate Number
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Certificate Number Dialog */}
      <AlertDialog open={certificateDialogOpen} onOpenChange={setCertificateDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Update Certificate Number</AlertDialogTitle>
            <AlertDialogDescription>
              Enter the certificate number for this participant. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="mt-4 space-y-2">
            <Input
              placeholder="Enter certificate number"
              value={certificateNumber}
              onChange={(e) => {
                setCertificateNumber(e.target.value);
                if (error) setError(""); // Clear error on input
              }}
              className={cn(error && "border-red-500 focus:ring-red-500")}
            />
            {error && (
              <p className="text-sm text-red-600">{error}</p>
            )}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setCertificateDialogOpen(false);
              setCertificateNumber("");
              setError("");
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleParticipantCertificateNumber}
              disabled={!certificateNumber.trim()}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Footer */}
      <div className="flex items-center justify-between px-2 text-sm text-muted-foreground">
        <div>
          Showing {filteredParticipants.length} of {participants.length} participants
        </div>

        {selectionSummary.total > 0 && (
          <div className="flex items-center gap-4">
            <span>{selectionSummary.total} selected</span>
            <span>{selectionSummary.canCheckIn} can check in</span>
          </div>
        )}
      </div>
    </div>
  );
}