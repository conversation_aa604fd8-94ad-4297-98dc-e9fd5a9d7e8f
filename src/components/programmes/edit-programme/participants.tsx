"use client";
import {
    Table,
    TableHeader,
    TableRow,
    TableHead,
    TableBody,
    TableCell
} from "@/components/ui/table";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { Card } from "@/components/ui/card";
import { getProgrammeParticipants } from "@/services/programmes/events-client.service";
import { ProgrammeParticipantInterface, registrationType } from "@/types/programmes/programme.types";
import { getCoreRowModel, useReactTable, flexRender, getSortedRowModel, ColumnFiltersState } from "@tanstack/react-table";
import { columnsEnhanced } from "@/components/pages/programme-participants/columns-enhanced";
import { DataTableViewOptions } from "@/components/pages/general/data-table-view-options";
import React from "react";
import { DataTableFacetedFilter } from "@/components/pages/general/data-table-faceted-filter";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, X } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import SearchInput from "@/components/pages/general/data-table-search-input";

interface ProgrammeParticipantListProps {
    programmeId: string;
}

export const ProgrammeParticipantListComponent: React.FC<ProgrammeParticipantListProps> = ({
    programmeId
}) => {
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
    const [searchTerm, setSearchTerm] = React.useState<string>('');
    const [isLoading, setIsLoading] = useState(true);
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [participants, setParticipants] = useState<ProgrammeParticipantInterface[]>([]);

    React.useEffect(() => {
        setPage(1);
        loadData();
    }, [pageSize, searchTerm, subFilters, programmeId]);

    const loadData = async (pageToLoad = page) => {
        if (!programmeId) {
            toast({
                title: "Error",
                description: "Event ID is required",
                variant: "destructive"
            });
            setIsLoading(false);
            return;
        }

        try {
            const { participants, success, total, currentPage } = await getProgrammeParticipants(
                programmeId,
                { subFilters },
                pageToLoad,
                pageSize,
                true,
                searchTerm
            );

            if (success && participants) {
                setParticipants(participants);
                setTotalCount(total);
                setPage(currentPage);

                // Calculate total pages based on the current pageSize
                const calculatedTotalPages = Math.ceil(total / pageSize);
                setTotalPages(calculatedTotalPages);

                // If the current page is out of bounds, reset to the last valid page
                if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
                    setPage(calculatedTotalPages);
                }
            }

        } catch (error) {
            console.error("Error loading data:", error);
            toast({
                title: "Error",
                description: "Failed to load programme data",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm('');
    };

    const handleStatusFilter = (selectedRegistrationType: string[]) => {
        setSubFilters(prev => ({
            ...prev,
            registration_type: selectedRegistrationType,
        }));
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadData(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
    };

    const table = useReactTable({
        data: participants,
        columns: columnsEnhanced,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        state: {
            columnFilters,
        },
        initialState: {
            columnVisibility: {
                participant_id: false,
                identification_type: false,
                identification_no: false,
                nationality: false,
            },
        },
    });

    return (
        <Card className="p-8 mt-4">
            <div className="w-full space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search participants..." />
                        {table.getColumn("registration_type") && (
                            <DataTableFacetedFilter
                                column={table.getColumn("registration_type")}
                                title="Registration Type"
                                options={registrationType}
                                onFilterChange={(selectedRegistrationType) => handleStatusFilter(selectedRegistrationType)}
                            />
                        )}
                        {/* Reset Filters Button */}
                        {(Object.keys(subFilters).length > 0 || searchTerm) && (
                            <Button
                                variant="ghost"
                                onClick={resetFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <X className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>

                    <DataTableViewOptions table={table} />
                </div>

                <div className="rounded-md border overflow-hidden">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id} className="bg-muted/50">
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} className="font-medium">
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {isLoading ? (
                                <TableRow>
                                    <TableCell colSpan={columnsEnhanced.length}>
                                        <LoadingSpinner />
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row, index) => (
                                    <TableRow 
                                        key={row.id} 
                                        data-state={row.getIsSelected() && "selected"}
                                        className={`
                                            ${index % 2 === 0 ? 'bg-background' : 'bg-muted/20'}
                                            hover:bg-muted/40 transition-colors cursor-default
                                        `}
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="py-3">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columnsEnhanced.length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        Total {totalCount} row(s)
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={handleLimitChange}
                                disabled={isLoading}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top">
                                    {[10, 20, 30, 40, 50].map((size) => (
                                        <SelectItem key={size} value={size.toString()}>
                                            {size}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </Card >
    );
};
