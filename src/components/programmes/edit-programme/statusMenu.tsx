"use client"

import { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from "@/components/ui/form";
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { CalendarIcon, Check } from "lucide-react";
import type { ProgrammeDetails } from "@/types/programmes/programme.types";
import { useToast } from "@/hooks/use-toast";
import { updateProgrammeStatus } from "@/services/programmes/events-server.service";

const formSchema = z.object({
    status: z.enum(['PUBLISHED', 'UNPUBLISHED']),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
}).superRefine((data, ctx) => {
    if (data.status === 'PUBLISHED') {
        if (!data.startDate) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Start date is required.",
                path: ["periodFrom"],
            });
        }
        if (!data.endDate) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "End date is required.",
                path: ["periodFrom"],
            });
        }
    }
});

type FormData = z.infer<typeof formSchema>;

interface ProgrammeStatusMenuProps {
    programmeDetails: ProgrammeDetails | null,
    disableUpdateStatus: boolean,
    onProgrammeUpdate: () => void;
}

export const ProgrammeStatusMenu: React.FC<ProgrammeStatusMenuProps> = ({
    programmeDetails,
    disableUpdateStatus,
    onProgrammeUpdate
}) => {
    const initialStatus = programmeDetails?.programme?.status ? programmeDetails?.programme?.status : undefined;
    const [open, setOpen] = useState(false);
    const { toast } = useToast();
    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        mode: "onChange",
        defaultValues: {
            status: undefined,
            startDate: programmeDetails?.programme?.publish_start_date ? new Date(programmeDetails?.programme?.publish_start_date) : undefined,
            endDate: programmeDetails?.programme?.publish_end_date ? new Date(programmeDetails?.programme?.publish_end_date) : undefined
        }
    });
    const selectedStatus = form.watch('status');

    useEffect(() => {
        if (selectedStatus === 'PUBLISHED' && !form.getValues('startDate')) {
            form.setValue('startDate', new Date());
        }
    }, [selectedStatus, form]);

    const handleUpdate = async (data: FormData) => {
        try {
            if (!programmeDetails || !programmeDetails.programme?.id) {
                throw new Error("Programme ID is required.");
            }

            if (data.status === 'PUBLISHED') {
                await updateProgrammeStatus(programmeDetails.programme?.id, data.status, data.startDate, data.endDate);
            } else {
                await updateProgrammeStatus(programmeDetails.programme?.id, data.status);
            }

            setOpen(false);
            toast({
                title: "Success",
                description: "Event update successful.",
            });
            form.reset();
            onProgrammeUpdate();
        } catch {
            toast({
                title: "Error",
                description: "An unexpected error occurred. Please try again.",
                variant: "destructive"
            });
        }
    };

    const renderDropdownOptions = () => {
        const options = [
            { value: "PUBLISHED", label: "Published" },
            { value: "UNPUBLISHED", label: "Unpublished" }
        ];

        if (initialStatus === "UNPUBLISHED" || initialStatus === "DRAFT") {
            return options.filter(option => option.value === "PUBLISHED");
        }
        return options;
    };

    return (
        <AlertDialog open={open} onOpenChange={setOpen}>
            <AlertDialogTrigger asChild>
                <Button
                    type="button"
                    disabled={!disableUpdateStatus}
                    onClick={() => setOpen(true)}
                >
                    <Check className="h-4 w-4" />
                    <span>Update Status</span>
                </Button>
            </AlertDialogTrigger>

            <AlertDialogContent className="rounded-xl p-8">
                <AlertDialogHeader>
                    <AlertDialogTitle>Update Status</AlertDialogTitle>
                    <AlertDialogDescription>Please select the status for the event.</AlertDialogDescription>
                </AlertDialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleUpdate)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                                <FormItem className="sm:col-span-2">
                                    <FormLabel>Status</FormLabel>
                                    <FormControl>
                                        <Select
                                            onValueChange={field.onChange}
                                            {...field}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {renderDropdownOptions().map(option => (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {selectedStatus === 'PUBLISHED' && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="startDate"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Start Date</FormLabel>
                                            <FormControl>
                                                <Popover modal={true}>
                                                    <PopoverTrigger asChild>
                                                        <div className="relative">
                                                            <Input
                                                                type="text"
                                                                readOnly
                                                                className="pointer-events-none"
                                                                placeholder="Pick a date"
                                                                value={field.value ? format(field.value, "PPP") : ""}
                                                            />
                                                            <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                        </div>
                                                    </PopoverTrigger>
                                                    <PopoverContent>
                                                        <Calendar
                                                            mode="single"
                                                            selected={field.value}
                                                            onSelect={(date) => {
                                                                if (date) {
                                                                    const utcDate = new Date(Date.UTC(
                                                                        date.getFullYear(),
                                                                        date.getMonth(),
                                                                        date.getDate()
                                                                    ));
                                                                    field.onChange(utcDate);
                                                                }
                                                            }}
                                                            startMonth={new Date()}
                                                            defaultMonth={field.value}
                                                            disabled={[{ before: new Date() }]}
                                                        />
                                                    </PopoverContent>
                                                </Popover>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="endDate"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>End Date</FormLabel>
                                            <FormControl>
                                                <Popover modal={true}>
                                                    <PopoverTrigger asChild>
                                                        <div className="relative">
                                                            <Input
                                                                type="text"
                                                                readOnly
                                                                className="pointer-events-none"
                                                                placeholder="Pick a date"
                                                                value={field.value ? format(field.value, "PPP") : ""}
                                                            />
                                                            <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                        </div>
                                                    </PopoverTrigger>
                                                    <PopoverContent>
                                                        <Calendar
                                                            mode="single"
                                                            selected={field.value}
                                                            onSelect={(date) => {
                                                                if (date) {
                                                                    const utcDate = new Date(Date.UTC(
                                                                        date.getFullYear(),
                                                                        date.getMonth(),
                                                                        date.getDate()
                                                                    ));
                                                                    field.onChange(utcDate);
                                                                }
                                                            }}
                                                            startMonth={new Date()}
                                                            defaultMonth={field.value}
                                                            disabled={[{ before: new Date() }]}
                                                        />
                                                    </PopoverContent>
                                                </Popover>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </>
                        )}

                        <AlertDialogFooter>
                            <AlertDialogCancel asChild>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setOpen(false)}
                                >
                                    Cancel
                                </Button>
                            </AlertDialogCancel>
                            <Button
                                type="submit"
                                disabled={!form.formState.isValid}
                            >
                                Save
                            </Button>
                        </AlertDialogFooter>
                    </form>
                </Form>
            </AlertDialogContent>
        </AlertDialog>
    );
};