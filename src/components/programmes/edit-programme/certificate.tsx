"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Upload, X, FileText } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { FileWithPreview } from "@/types/programmes/programme.types";
import { deleteProgrammeCertificate } from "@/services/programmes/events-client.service";
import { formatFileSize } from "@/lib/utils";

const ACCEPTED_FILE_TYPES = [
    { mime: "application/pdf", ext: ".pdf", label: "PDF" },
];

const MAX_FILE_SIZE = 4.5 * 1024 * 1024; // 4.5MB

interface ProgrammeCertificateProps {
    programmeId: string;
    certificate: FileWithPreview | null;
    setCertificate: (certificate: FileWithPreview | undefined) => void;
    isEditing: boolean;
}

export function ProgrammeCertificateComponent({ programmeId, certificate, setCertificate, isEditing }: ProgrammeCertificateProps) {
    const [dragActive, setDragActive] = useState(false);

    const handleFile = async (file: File) => {
        // Check file type
        if (!ACCEPTED_FILE_TYPES.some(type => type.mime === file.type)) {
            toast({
                variant: "destructive",
                title: "Unsupported file type",
                description: `File ${file.name} is not a supported format.`,
            });
            return;
        }

        // Check file size
        if (file.size > MAX_FILE_SIZE) {
            toast({
                variant: "destructive",
                title: "File too large",
                description: `File ${file.name} exceeds ${formatFileSize(MAX_FILE_SIZE)} limit.`,
            });
            return;
        }

        const previewUrl = URL.createObjectURL(file);

        const fileWithPreview = Object.assign(file, {
            isExisting: false,
            preview: previewUrl
        });

        setCertificate(fileWithPreview);
    };

    const removeCertificate = async () => {
        if (!certificate) return;

        // Handle existing file removal from Supabase
        if (certificate.isExisting && certificate.path) {
            const response = await deleteProgrammeCertificate(programmeId, certificate.path);

            if (!response.success) {
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: `Error removing certificate: ${response.error || 'Unknown error'}`,
                });
                return;
            }

            toast({
                title: "Success",
                description: `The certificate has been deleted successfully.`,
            });
        }

        // Clear the state
        setCertificate(undefined);
    };

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        const droppedFile = e.dataTransfer.files?.[0];
        if (droppedFile) {
            handleFile(droppedFile);
        }
    };

    return (
        <Card className="p-8 mt-4 space-y-4">
            {/* Upload Zone */}
            <Card
                className={`border-2 border-dashed transition-colors ${isEditing
                    ? dragActive
                        ? "border-primary bg-primary/5"
                        : "border-muted-foreground/25 hover:border-muted-foreground/50"
                    : "border-muted-foreground/10 bg-muted/50 cursor-not-allowed"
                    }`}
                onDragEnter={isEditing ? handleDrag : undefined}
                onDragLeave={isEditing ? handleDrag : undefined}
                onDragOver={isEditing ? handleDrag : undefined}
                onDrop={isEditing ? handleDrop : undefined}
            >
                <CardContent className="flex flex-col items-center justify-center py-8">
                    <Upload className="h-10 w-10 text-muted-foreground mb-4" />
                    <p className="text-lg font-medium mb-2">Drop files here or click to upload</p>
                    <p className="text-sm text-muted-foreground mb-4">
                        Support for {ACCEPTED_FILE_TYPES.map(t => t.label).join(', ')} files up to 4.5MB
                    </p>
                    <Input
                        type="file"
                        accept={ACCEPTED_FILE_TYPES.map((type) => type.ext).join(",")}
                        className="hidden"
                        id="certificate-upload"
                        onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleFile(file);
                        }}
                        value={undefined}
                    />
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('certificate-upload')?.click()}
                        disabled={!isEditing}
                    >
                        <Upload className="mr-2 h-4 w-4" />
                        Choose Files
                    </Button>

                    {!isEditing && (
                        <p className="text-sm text-muted-foreground mt-4">
                            Upload certificate template here.
                        </p>
                    )}
                </CardContent>
            </Card>

            {/* File List */}
            {certificate && (
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3 flex-1">
                                <FileText className="h-6 w-6 text-muted-foreground" />
                                <div className="flex-1 min-w-0">
                                    <p className="font-medium truncate">{certificate.name}</p>
                                    <p className="text-sm text-muted-foreground">
                                        {formatFileSize(certificate.size)}
                                    </p>
                                </div>
                            </div>
                            {isEditing && (
                                <AlertDialog>
                                    <AlertDialogTrigger>
                                        <Button type="button" variant="ghost" size="sm">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>Are you sure you want to delete this certificate template?</AlertDialogTitle>
                                            <AlertDialogDescription>
                                                This action cannot be undone and will affect all participants who currently have this certificate assigned.
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                                            <AlertDialogAction
                                                className="bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90"
                                                onClick={removeCertificate}
                                            >
                                                Delete
                                            </AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )}
        </Card>
    );
}