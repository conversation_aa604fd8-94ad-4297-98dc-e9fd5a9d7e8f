"use client"

import * as React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { MultiSelect } from "@/components/ui/multi-select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { CalendarIcon, CirclePlus } from "lucide-react";
import { format } from "date-fns";
import { ComplexBudgetView } from "@/components/budgets/complex-view";
import { EventBudgetView } from "@/components/budgets/simple-view";
import { ProgrammeParticipantListComponent } from "@/components/programmes/edit-programme/participants";
import { ProgrammeRegistrationListComponent } from "@/components/programmes/edit-programme/registrations";
import { ProgrammeStatusMenu } from "@/components/programmes/edit-programme/statusMenu";
import { useEditProgramme } from "@/hooks/programmes/use-edit-programme";
import { ProgrammeCertificateComponent } from "@/components/programmes/edit-programme/certificate";
import AdminPanelLayout from "@/components/layout/admin-panel-layout";

interface EditProgrammeProps {
  programmeId: string;
}

export default function EditProgramme({ programmeId }: EditProgrammeProps) {
  const id = programmeId;

  const {
    form,
    programmeDetails,
    pointType,
    profitSharingData,
    scheduleFields,
    pointFields,
    isPointTypeSelected,
    handleImageChange,
    setCertificate,
    addDateTime,
    removeDateTime,
    addProgrammePoint,
    removeProgrammePoint,
    simpleItemData,
    complexItemData,
    selectedBudgetVersion,
    setSelectedBudgetVersion,
    budgetVersion,
    venue,
    department,
    filteredStaff,
    committees,
    membershipTypes,
    taxRate,
    terminateValue,
    setTerminateValue,
    isLoading,
    isReadOnly,
    disableUpdateStatus,
    isDeleting,
    isArchiving,
    handleSubmit,
    handleTerminate,
    handleReviseBudget,
    loadFormData,
    onDelete,
    handleArchiveProgramme,
  } = useEditProgramme(id);

  const breadcrumbs = {
    items: [
      {
        title: "Programmes",
        href: "/programmes",
      },
      {
        title: programmeDetails?.programme?.name || 'Loading...',
        href: `/programmes/${programmeDetails?.programme?.id}`,
      },
      {
        title: "Edit Programme",
        isCurrentPage: true,
      },
    ],
  };

  if (!id) {
    return <div>No programme ID provided</div>;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isLoading && !programmeDetails) {
    return <div>Programme not found</div>;
  }

  return (
    <AdminPanelLayout breadcrumbs={breadcrumbs}>
      <Form {...form}>
        <form className="space-y-8">
          <div className="mb-8">
            <div className="relative w-full h-[300px] rounded-lg overflow-hidden">
              <FormField
                control={form.control}
                name="image"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        disabled={isReadOnly}
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageChange(file);
                        }}
                        value={undefined}
                        className="hidden"
                        id="file-upload"
                      />
                    </FormControl>
                    <label
                      htmlFor="file-upload"
                      className={`block relative w-full h-[300px] group ${isReadOnly ? 'pointer-events-none' : 'cursor-pointer'}`}
                    >
                      <img
                        src={field.value?.preview || '/images/auth-bg.jpg'}
                        alt={field.value?.name || ''}
                        className="object-cover w-full h-full rounded-t-lg"
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white text-lg font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-t-lg">
                        Upload
                      </div>
                    </label>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-t from-black to-transparent"></div>

              <div className="absolute bottom-4 left-4 right-4">
                <FormField
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <p className="text-white text-xl font-semibold">{field.value}</p>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        </form>
      </Form>

      <Tabs defaultValue="detail">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="detail">Details</TabsTrigger>
          <TabsTrigger value="budget">Budgets</TabsTrigger>
          <TabsTrigger value="registrations">Registrations</TabsTrigger>
          <TabsTrigger value="participants">Participants</TabsTrigger>
          <TabsTrigger value="certificate">Certificate</TabsTrigger>
        </TabsList>


        <TabsContent value="detail">
          <Form {...form}>
            <form className="space-y-8">
              {programmeDetails?.programme?.status === 'PUBLISHED' && (
                <Card className="p-8 mt-4">
                  <div className="col-span-2">
                    <h1 className="text-lg font-semibold pb-2 mb-4">Schedule Programme Publishing</h1>
                    <div className="mb-4 mt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex flex-col">
                          <label className="text-sm uppercase font-bold text-gray-400 block">
                            Start Date
                          </label>
                          <span className="text-sm font-semibold block">
                            {programmeDetails.programme?.publish_start_date}
                          </span>
                        </div>
                        <div className="flex flex-col">
                          <label className="text-sm uppercase font-bold text-gray-400 block">
                            End Date
                          </label>
                          <span className="text-sm font-semibold block">
                            {programmeDetails.programme?.publish_end_date}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              <Card className="p-8 mt-4">
                <div className="col-span-2">
                  <h1 className="text-lg font-semibold pb-2 mb-4">Registration Date</h1>
                  <div className="mb-4 mt-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col">
                        <FormField
                          control={form.control}
                          name="registrationStartDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Start Date</FormLabel>
                              <FormControl>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    {isReadOnly ? (
                                      <Input
                                        type="text"
                                        readOnly
                                        disabled={isReadOnly}
                                        value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                      />
                                    ) : (
                                      <div className="relative">
                                        <Input
                                          type="text"
                                          readOnly
                                          className="pointer-events-none"
                                          placeholder="Pick a date"
                                          value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                        />
                                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                      </div>
                                    )}
                                  </PopoverTrigger>
                                  <PopoverContent>
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={(date) => {
                                        if (date) {
                                          const utcDate = new Date(Date.UTC(
                                            date.getFullYear(),
                                            date.getMonth(),
                                            date.getDate()
                                          ));
                                          field.onChange(utcDate);
                                        }
                                      }}
                                      startMonth={new Date()}
                                      defaultMonth={field.value}
                                    />
                                  </PopoverContent>
                                </Popover>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex flex-col">
                        <FormField
                          control={form.control}
                          name="registrationEndDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>End Date</FormLabel>
                              <FormControl>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    {isReadOnly ? (
                                      <Input
                                        type="text"
                                        readOnly
                                        disabled={isReadOnly}
                                        value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                      />
                                    ) : (
                                      <div className="relative">
                                        <Input
                                          type="text"
                                          readOnly
                                          className="pointer-events-none"
                                          placeholder="Pick a date"
                                          value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                        />
                                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                      </div>
                                    )}
                                  </PopoverTrigger>
                                  <PopoverContent>
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={(date) => {
                                        if (date) {
                                          const utcDate = new Date(Date.UTC(
                                            date.getFullYear(),
                                            date.getMonth(),
                                            date.getDate()
                                          ));
                                          field.onChange(utcDate);
                                        }
                                      }}
                                      startMonth={new Date()}
                                      defaultMonth={field.value}
                                      disabled={(() => {
                                        const startDate = form.getValues("registrationStartDate");
                                        return startDate ? [{ before: startDate }] : undefined;
                                      })()}
                                    />
                                  </PopoverContent>
                                </Popover>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-8 mt-4">
                <h1 className="text-lg font-semibold pb-2 mb-4">Programme Details</h1>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm uppercase font-bold text-gray-400 block">
                        Programme Code
                      </label>
                      <span className="text-sm font-semibold block">
                        {programmeDetails?.programme?.programme_code}
                      </span>
                    </div>

                    <div>
                      <label className="text-sm uppercase font-bold text-gray-400 block">
                        Programme Type
                      </label>
                      <span className="text-sm font-semibold block">
                        {programmeDetails?.programme?.type}
                      </span>
                    </div>

                    <div>
                      <label className="text-sm uppercase font-bold text-gray-400 block">
                        Programme Status
                      </label>
                      <span className="text-sm font-semibold block">
                        {programmeDetails?.programme?.is_archived ? 'ARCHIVED' : programmeDetails?.programme?.status}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="col-span-full">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Programme Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter programme name"
                                {...field}
                                disabled={isReadOnly}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="col-span-full">
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Programme Description</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Enter programme description"
                                className="resize-y"
                                {...field}
                                disabled={isReadOnly}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="venueId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Programme Venue</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                              disabled={isReadOnly}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select programme venue" />
                              </SelectTrigger>
                              <SelectContent>
                                {venue.length > 0 ? (
                                  venue
                                    .map((venue) => (
                                      <SelectItem key={venue.id} value={venue.id}>
                                        {venue.name}
                                      </SelectItem>
                                    ))
                                ) : (
                                  <SelectItem value="no-options" disabled>
                                    No venue available
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxParticipants"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Participants</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter maximum number of participants"
                              step="0"
                              min="0"
                              inputMode="numeric"
                              {...field}
                              disabled={isReadOnly}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Date & Time Section */}
                  <div>
                    <div className="rounded-md bg-gray-100 p-2 flex items-center">
                      <h2 className="text-md text-gray-500 font-medium">Date & Time</h2>
                    </div>
                    {scheduleFields.map((dateTime, index) => (
                      <div key={dateTime.fieldId}>
                        <div className="flex items-center mb-2 mt-4">
                          <span className="text-lg font-semibold text-gray-700">{`Day ${index + 1}`}</span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name={`schedules.${index}.date`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Date</FormLabel>
                                <FormControl>
                                  <Popover>
                                    <PopoverTrigger asChild>
                                      {isReadOnly ? (
                                        <Input
                                          type="text"
                                          readOnly
                                          disabled={isReadOnly}
                                          value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                        />
                                      ) : (
                                        <div className="relative">
                                          <Input
                                            type="text"
                                            readOnly
                                            className="pointer-events-none"
                                            placeholder="Pick a date"
                                            value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                          />
                                          <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                        </div>
                                      )}
                                    </PopoverTrigger>
                                    <PopoverContent>
                                      <Calendar
                                        mode="single"
                                        selected={field.value ?? undefined}
                                        onSelect={(date) => {
                                          if (date) {
                                            const utcDate = new Date(Date.UTC(
                                              date.getFullYear(),
                                              date.getMonth(),
                                              date.getDate()
                                            ));
                                            field.onChange(utcDate);
                                          }
                                        }}
                                      />
                                    </PopoverContent>
                                  </Popover>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`schedules.${index}.startTime`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Start Time</FormLabel>
                                <FormControl>
                                  <Input
                                    type="time"
                                    disabled={isReadOnly}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`schedules.${index}.endTime`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>End Time</FormLabel>
                                <FormControl>
                                  <Input
                                    type="time"
                                    disabled={isReadOnly}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {!isReadOnly && (
                          <div className="flex items-center justify-end mt-4 space-x-4">
                            {index === scheduleFields.length - 1 && (
                              <Button type="button" onClick={addDateTime}>
                                Add
                              </Button>
                            )}
                            {scheduleFields.length > 1 && (
                              <Button
                                type="button"
                                onClick={() => removeDateTime(index, dateTime.id ?? undefined)}
                                variant="destructive"
                              >
                                Remove
                              </Button>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Pricing Section */}
                  <div>
                    <div className="rounded-md bg-gray-100 p-2 flex items-center mt-4 mb-4">
                      <h2 className="text-md text-gray-500 font-medium">Pricing</h2>
                    </div>

                    {programmeDetails?.programmePricing && programmeDetails.programmePricing.length > 0 ? (
                      programmeDetails.programmePricing.map((pricing, index) => (
                        <div key={pricing.id} className="space-y-4 mb-6 pb-6 border-b last:border-b-0 last:pb-0">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <FormField
                              control={form.control}
                              name={`programmePricing.${index}.name`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Price Type Name</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Enter price type name"
                                      {...field}
                                      disabled={isReadOnly}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div>
                              <FormLabel>Price</FormLabel>
                              <div className="flex items-center h-10 px-3 border rounded-md bg-gray-100 mt-1">
                                <span className="mr-1">{pricing.currency || 'SGD'}</span>
                                <span className="text-sm font-semibold">{pricing.amount || 0}</span>
                              </div>
                            </div>

                            <div>
                              <FormLabel>Price with GST</FormLabel>
                              <div className="flex items-center h-10 px-3 border rounded-md bg-gray-100">
                                <span className="mr-1">{pricing.currency || 'SGD'}</span>
                                <span className="text-sm font-semibold">
                                  {(taxRate && pricing.amount) ? (pricing.amount * (1 + taxRate / 100)).toFixed(2) : '--'}
                                </span>
                              </div>
                            </div>
                          </div>

                          <FormField
                            control={form.control}
                            name={`programmePricing.${index}.requires_membership`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Member Price?</FormLabel>
                                <FormControl>
                                  <RadioGroup
                                    className="flex flex-row gap-4"
                                    onValueChange={(value) => field.onChange(value === "true")}
                                    defaultValue={String(field.value)}
                                    disabled={isReadOnly}
                                  >
                                    <FormItem className="flex items-center space-x-2">
                                      <FormControl>
                                        <RadioGroupItem value="true" id="option-yes" />
                                      </FormControl>
                                      <FormLabel className="font-normal">Yes</FormLabel>
                                    </FormItem>
                                    <FormItem className="flex items-center space-x-2">
                                      <FormControl>
                                        <RadioGroupItem value="false" id="option-no" />
                                      </FormControl>
                                      <FormLabel className="font-normal">No</FormLabel>
                                    </FormItem>
                                  </RadioGroup>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {form.watch(`programmePricing.${index}.requires_membership`) && (
                            <FormField
                              control={form.control}
                              name={`programmePricing.${index}.target_membership`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Membership Types</FormLabel>
                                  <FormControl>
                                    <MultiSelect
                                      options={membershipTypes}
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                      placeholder="Select Membership Types"
                                      maxCount={4}
                                      disabled={isReadOnly}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          <FormField
                            control={form.control}
                            name={`programmePricing.${index}.description`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Description</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter price type description"
                                    className="resize-y"
                                    {...field}
                                    disabled={isReadOnly}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      ))
                    ) : (
                      <div className="mt-4 text-sm text-gray-500">No pricing information available.</div>
                    )}
                  </div>

                  <div className="border-b border-gray-300 my-2" />

                  {/* Department Section */}
                  <div>
                    <h3 className="text-lg font-medium mb-3">Department Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                      <FormField
                        control={form.control}
                        name="departmentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Organizing Department</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  //reset field
                                  form.setValue("staffInChargeId", "");
                                }}
                                value={field.value}
                                disabled={isReadOnly}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select Department" />
                                </SelectTrigger>
                                <SelectContent>
                                  {department.length > 0 ? (
                                    department
                                      .map((department) => (
                                        <SelectItem key={department.id} value={department.id}>
                                          {department.name}
                                        </SelectItem>
                                      ))
                                  ) : (
                                    <SelectItem value="no-options" disabled>
                                      No department available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="staffInChargeId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff in Charge</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={isReadOnly}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select Staff" />
                                </SelectTrigger>
                                <SelectContent>
                                  {
                                    filteredStaff.length === 0 ? (
                                      <SelectItem value="none" disabled>
                                        {form.watch("departmentId") ? "No staff found for this department" : "Select the department first to view staff"}
                                      </SelectItem>
                                    ) : (
                                      filteredStaff
                                        .map((staffMember) => (
                                          <SelectItem key={staffMember.id} value={staffMember.id}>
                                            {staffMember.name}
                                          </SelectItem>
                                        ))
                                    )
                                  }
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Committee Section */}
                  <div>
                    <h3 className="text-lg font-medium mb-3">Committee Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="committeeId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Committee</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={isReadOnly}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select Committee (Optional)" />
                                </SelectTrigger>
                                <SelectContent>
                                  {committees.length > 0 ? (
                                    <>
                                      <SelectItem value="none">None</SelectItem>

                                      {committees.map((committee) => (
                                        <SelectItem key={committee.id} value={committee.id}>
                                          {committee.name}
                                        </SelectItem>
                                      ))}
                                    </>
                                  ) : (
                                    <SelectItem value="no-options" disabled>
                                      No committees available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="secondary_committeeId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Secondary Committee</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={isReadOnly}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select Committee (Optional)" />
                                </SelectTrigger>
                                <SelectContent>
                                  {committees.length > 0 ? (
                                    <>
                                      <SelectItem value="none">None</SelectItem>

                                      {committees.map((committee) => (
                                        <SelectItem key={committee.id} value={committee.id}>
                                          {committee.name}
                                        </SelectItem>
                                      ))}
                                    </>
                                  ) : (
                                    <SelectItem value="no-options" disabled>
                                      No committees available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm uppercase font-bold text-gray-400 block">
                      Remark
                    </label>
                    <div
                      className="w-full border border-gray-300 rounded-md p-2"
                      style={{ minHeight: "40px", overflowY: "auto" }}
                    >
                      {programmeDetails?.programme?.remarks}
                    </div>
                  </div>

                  <div className="border-b border-gray-300 mt-7 mb-7" />

                  {/* Profit Sharing Section */}
                  <div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col">
                        <label className="text-sm uppercase font-bold text-gray-400 block">
                          Profit Sharing
                        </label>
                        <span className="text-sm font-semibold block">
                          {profitSharingData?.has_profit_sharing ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <label className="text-sm uppercase font-bold text-gray-400 block">
                          Entity Name
                        </label>
                        <span className="text-sm font-semibold block">
                          {profitSharingData?.has_profit_sharing
                            ? profitSharingData?.profit_sharing_entity || '-'
                            : '-'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="border-b border-gray-300 mt-7 mb-7" />

                  {/* Other Information Section */}
                  <div>
                    <div className="rounded-md bg-gray-100 p-2 flex items-center mb-4">
                      <h2 className="text-md text-gray-500 font-medium">Other Information</h2>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col">
                        <label className="text-sm uppercase font-bold text-gray-400 block">
                          Prepared By
                        </label>
                        <span className="text-sm font-semibold block">
                          {profitSharingData?.prepared_by_name
                            ? profitSharingData?.prepared_by_name
                            : '-'}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <label className="text-sm uppercase font-bold text-gray-400 block">
                          Approved By
                        </label>
                        <span className="text-sm font-semibold block">
                          {profitSharingData?.approved_by_name
                            ? profitSharingData?.approved_by_name
                            : '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-8 mt-4">
                <h1 className="text-lg font-semibold pb-2 mb-4">Programme Points</h1>
                <div className="col-span-full">
                  {pointFields.map((entry, index) => (
                    <div key={entry.fieldId}>
                      {/* Point Type Selector */}
                      <div className="mb-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex flex-col">
                            <FormField
                              control={form.control}
                              name={`programmePoint.${index}.point_type_id`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Point Type</FormLabel>
                                  <FormControl>
                                    <Select
                                      value={field.value ?? ''}
                                      onValueChange={field.onChange}
                                      disabled={isReadOnly}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select Point Type" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {(pointType).length > 0 ? (
                                          (pointType)
                                            .map((type) => (
                                              <SelectItem key={type.id} value={type.id} disabled={isPointTypeSelected(type.id, index)}>
                                                {type.name}
                                              </SelectItem>
                                            ))
                                        ) : (
                                          <SelectItem value="no-options" disabled>
                                            No point types available
                                          </SelectItem>
                                        )}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="flex flex-col">
                            <FormField
                              control={form.control}
                              name={`programmePoint.${index}.points`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Points</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="Enter points"
                                      step="0"
                                      min="0"
                                      value={field.value ?? ''}
                                      onChange={field.onChange}
                                      inputMode="numeric"
                                      disabled={isReadOnly}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Add/Remove Buttons */}
                        {!isReadOnly && (
                          <div className="flex items-center space-x-4 mt-8">
                            {index === pointFields.length - 1 && (
                              <Button type="button" onClick={addProgrammePoint}>
                                Add
                              </Button>
                            )}
                            <Button variant="destructive" type="button" onClick={() => removeProgrammePoint(index, entry.id ?? undefined)}>
                              Remove
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </form >
          </Form >
        </TabsContent>

        <TabsContent value="budget">
          <Card className="p-8 mt-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <h1 className="text-lg font-semibold">Programme Budget</h1>
                {budgetVersion.find(b => b.id === selectedBudgetVersion)?.is_archived ? (
                  <Badge className="bg-gray-500 hover:bg-gray-400">Archived</Badge>
                ) : budgetVersion.find(b => b.id === selectedBudgetVersion)?.status === 'PENDING_APPROVAL' ? (
                  <Badge className="bg-yellow-500 hover:bg-yellow-300">Pending Approval</Badge>
                ) : budgetVersion.find(b => b.id === selectedBudgetVersion)?.status === 'APPROVED' ? (
                  <Badge className="bg-green-500 hover:bg-green-300">Approved</Badge>
                ) : budgetVersion.find(b => b.id === selectedBudgetVersion)?.status === 'REJECTED' ? (
                  <Badge className="bg-red-500 hover:bg-red-300">Rejected</Badge>
                ) : null}
                <div>
                  <Select
                    onValueChange={(value) => setSelectedBudgetVersion(value)}
                    value={selectedBudgetVersion}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a version" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetVersion.map((budget) => {
                        const isDraft = budget.status === "DRAFT";

                        return (
                          <SelectItem
                            key={budget.id}
                            value={budget.id}
                            disabled={isDraft}
                            className={isDraft ? "opacity-50 cursor-not-allowed" : ""}
                          >
                            v{budget.version}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {!budgetVersion.find(b => b.id === selectedBudgetVersion)?.is_archived && (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button type="button" variant="outline"><CirclePlus /> Revise Budget</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Revise Budget</DialogTitle>
                      <DialogDescription>
                        Revising the budget requires a new approval process.
                        This action will redirect you to the Budget Review page to revise the budget.
                        Do you want to continue?
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <DialogClose asChild>
                        <Button type="button" variant="secondary">
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button type="button" onClick={handleReviseBudget}>Continue</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Conditional rendering based on ProgrammeType */}
            {complexItemData != null ? (
              <ComplexBudgetView
                incomeEntries={complexItemData?.incomeEntries ?? []}
                expenditureEntries={complexItemData?.expenditureEntries ?? []}
                summary={complexItemData?.summary || null}
              />
            ) : (
              <EventBudgetView
                incomeEntries={simpleItemData?.incomeEntries ?? []}
                expenditureEntries={simpleItemData?.expenditureEntries ?? []}
                summary={simpleItemData?.summary || null}
              />
            )}
          </Card>
        </TabsContent>

        <TabsContent value="registrations">
          <ProgrammeRegistrationListComponent programmeId={id} />
        </TabsContent>

        <TabsContent value="participants">
          <ProgrammeParticipantListComponent programmeId={id} />
        </TabsContent>

        <TabsContent value="certificate">
          <ProgrammeCertificateComponent
            programmeId={id}
            certificate={form.getValues('certificate') ?? null}
            setCertificate={setCertificate}
            isEditing={!isReadOnly}
          />
        </TabsContent>
      </Tabs>

      {(isReadOnly && !programmeDetails?.programme?.is_archived) && (
        <div className="flex justify-start mt-4">
          <Dialog>
            <DialogTrigger asChild>
              <Button type="button" variant="destructive">Archive</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Archive Programme</DialogTitle>
                <DialogDescription>
                  Are you sure you want to archive this programme? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="flex justify-end">
                <DialogClose asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button
                  variant="destructive"
                  onClick={handleArchiveProgramme}
                  disabled={isArchiving}
                >
                  {isArchiving ? 'Archiving' : 'Archive'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      )}

      {!isReadOnly && (
        <div className="flex justify-between mt-4">
          {programmeDetails?.programme?.status === "DRAFT" ? (
            // Delete Programme Dialog
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="destructive">Delete</Button>
              </DialogTrigger>

              <DialogContent>
                <DialogTitle>Delete Programme</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this programme? This action cannot be undone.
                </DialogDescription>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="button" variant="secondary">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    variant="destructive"
                    onClick={onDelete}
                    disabled={isDeleting}
                  >
                    Delete
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          ) : (
            // Terminate Programme Dialog
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="destructive">Terminate</Button>
              </DialogTrigger>

              <DialogContent className="p-6 rounded-lg shadow-lg max-w-md w-full">
                <DialogHeader className="mb-4">
                  <DialogTitle className="text-lg font-semibold text-gray-900">
                    Terminate Programme
                  </DialogTitle>
                  <DialogDescription className="text-sm text-gray-600">
                    Are you sure you want to terminate this event?
                  </DialogDescription>
                </DialogHeader>

                <div>
                  <label
                    htmlFor="confirmation-input"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Please type <span className="font-bold">&quot;terminate&quot;</span> to confirm:
                  </label>
                  <input
                    type="text"
                    id="confirmation-input"
                    className="p-2 border border-gray-300 rounded-md w-full focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:outline-none"
                    value={terminateValue}
                    onChange={(e) => setTerminateValue(e.target.value)}
                    placeholder="Type here..."
                  />
                </div>

                <DialogFooter className="flex justify-end">
                  <DialogClose asChild>
                    <Button type="button" variant="secondary">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    variant="destructive"
                    onClick={handleTerminate}
                    disabled={terminateValue !== "terminate"}
                    className="disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    Terminate
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}

          <div className="flex justify-end">
            <Button type="button" onClick={() => handleSubmit()} variant="outline" className="mr-2">
              Save
            </Button>
            <ProgrammeStatusMenu programmeDetails={programmeDetails} disableUpdateStatus={disableUpdateStatus} onProgrammeUpdate={loadFormData} />
          </div>
        </div>
      )}
    </AdminPanelLayout>
  );
}