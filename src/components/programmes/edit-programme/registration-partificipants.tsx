"use client";
import {
    Table,
    TableHeader,
    TableRow,
    TableHead,
    TableBody,
    TableCell
} from "@/components/ui/table";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { Card } from "@/components/ui/card";
import { getProgrammeParticipants } from "@/services/programmes/events-client.service";
import { ProgrammeParticipantInterface } from "@/types/programmes/programme.types";
import { getCoreRowModel, useReactTable, flexRender, getSortedRowModel, ColumnFiltersState } from "@tanstack/react-table";
import React from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import SearchInput from "@/components/pages/general/data-table-search-input";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";

interface ProgrammeRegistrationParticipantsProps {
    programmeId: string;
}

export const ProgrammeRegistrationParticipants: React.FC<ProgrammeRegistrationParticipantsProps> = ({
    programmeId
}) => {
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [searchTerm, setSearchTerm] = React.useState<string>('');
    const [isLoading, setIsLoading] = useState(true);
    const pageSize = 10;
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [participants, setParticipants] = useState<ProgrammeParticipantInterface[]>([]);

    React.useEffect(() => {
        setPage(1);
        loadData();
    }, [searchTerm, programmeId]);

    const loadData = async (pageToLoad = page) => {
        if (!programmeId) {
            toast({
                title: "Error",
                description: "Event ID is required",
                variant: "destructive"
            });
            setIsLoading(false);
            return;
        }

        try {
            const { participants, success, total, currentPage } = await getProgrammeParticipants(
                programmeId,
                { subFilters: {} },
                pageToLoad,
                pageSize,
                false,
                searchTerm
            );

            if (success && participants) {
                setParticipants(participants);
                setTotalCount(total);
                setPage(currentPage);

                // Calculate total pages based on the current pageSize
                const calculatedTotalPages = Math.ceil(total / pageSize);
                setTotalPages(calculatedTotalPages);

                // If the current page is out of bounds, reset to the last valid page
                if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
                    setPage(calculatedTotalPages);
                }
            }

        } catch (error) {
            console.error("Error loading data:", error);
            toast({
                title: "Error",
                description: "Failed to load participants data",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadData(newPage);
        }
    };

    const columns: ColumnDef<ProgrammeParticipantInterface>[] = [
        {
            accessorKey: "participant_id",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="ID" />
            ),
            cell: ({ row, table }) => {
                const { pageIndex, pageSize } = table.getState().pagination;
                const rowIndex = row.index;
                const number = pageIndex * pageSize + rowIndex + 1;

                return (
                    <div className="flex text-center">
                        <span data-user-id={row.original.participant_id}>{number}</span>
                    </div>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "salutation",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Salutation" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("salutation")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Salutation" },
        },
        {
            accessorKey: "name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Name" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("name")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Name" },
        },
        {
            accessorKey: "designation",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Designation" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("designation")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Designation" },
        },
        {
            accessorKey: "email",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Email" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("email")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Email" },
        },
        {
            accessorKey: "contact_no",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Contact No." />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("contact_no")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Contact No" },
        },
        {
            accessorKey: "identification_type",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Identification Type" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("identification_type")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Identification Type" },
        },
        {
            accessorKey: "identification_no",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Identification No" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("identification_no")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Identification No" },
        },
        {
            accessorKey: "nationality",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Nationality" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center truncate">{row.getValue("nationality")}</div>
            ),
            sortingFn: "text",
            meta: { label: "Nationality" },
        },
    ];

    const table = useReactTable({
        data: participants,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        state: {
            columnFilters,
        },
    });

    return (
        <Card className="p-4">
            <div className="w-full space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <SearchInput onSearch={setSearchTerm} value={searchTerm} placeholder="Search participants..." />
                    </div>
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {isLoading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.length}>
                                        <LoadingSpinner />
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className="text-center">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        Total {totalCount} row(s)
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {page} of {totalPages}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page - 1);
                                }}
                                disabled={page <= 1}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft />
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(page + 1);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight />
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={(e) => {
                                    e.preventDefault();
                                    handlePageChange(totalPages);
                                }}
                                disabled={page >= totalPages}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </Card >
    );
};
