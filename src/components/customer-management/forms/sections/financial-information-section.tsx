"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { CustomerFormData } from "@/schemas/customer-management";

interface FinancialInformationSectionProps {
  form: UseFormReturn<CustomerFormData>;
}

export function FinancialInformationSection({ form }: FinancialInformationSectionProps) {
  // Common payment terms options
  const paymentTermsOptions = [
    { value: "NET_7", label: "Net 7 Days" },
    { value: "NET_14", label: "Net 14 Days" },
    { value: "NET_30", label: "Net 30 Days" },
    { value: "NET_45", label: "Net 45 Days" },
    { value: "NET_60", label: "Net 60 Days" },
    { value: "NET_90", label: "Net 90 Days" },
    { value: "PREPAYMENT", label: "Prepayment Required" },
    { value: "COD", label: "Cash on Delivery" },
    { value: "CUSTOM", label: "Custom Terms" },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Financial Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="payment_terms"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Terms</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment terms" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {paymentTermsOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="credit_limit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Credit Limit</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    type="number" 
                    step="0.01"
                    placeholder="0.00" 
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? undefined : parseFloat(value));
                    }}
                    value={field.value ?? ""}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="rounded-lg bg-muted p-4">
          <h4 className="text-sm font-medium mb-2">Payment Terms Information</h4>
          <p className="text-sm text-muted-foreground">
            Set the payment terms for this customer. This will be used as the default payment terms 
            for sales orders and invoices. The credit limit determines the maximum outstanding balance 
            allowed for this customer.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}