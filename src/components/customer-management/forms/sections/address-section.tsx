"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle, Trash2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import type { CustomerFormData, CustomerAddressFormData } from "@/schemas/customer-management";

interface AddressSectionProps {
  form: UseFormReturn<CustomerFormData>;
}

export function AddressSection({ form }: AddressSectionProps) {
  const shippingAddresses = form.watch("shipping_addresses") || [];

  const addShippingAddress = () => {
    const newAddress: CustomerAddressFormData = {
      street_address: "",
      city: "",
      postal_code: "",
      country: "",
    };
    form.setValue("shipping_addresses", [...shippingAddresses, new<PERSON>ddress]);
  };

  const removeShippingAddress = (index: number) => {
    const updatedAddresses = shippingAddresses.filter((_, i) => i !== index);
    form.setValue("shipping_addresses", updatedAddresses);
  };

  const updateShippingAddress = (index: number, field: keyof CustomerAddressFormData, value: string) => {
    const updatedAddresses = [...shippingAddresses];
    updatedAddresses[index] = { ...updatedAddresses[index], [field]: value };
    form.setValue("shipping_addresses", updatedAddresses);
  };

  return (
    <div className="space-y-4">
      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Address</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="billing_address.street_address"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <FormLabel>Street Address *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter street address" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="billing_address.city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter city" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="billing_address.postal_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Postal Code *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter postal code" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="billing_address.country"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <FormLabel>Country *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter country" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Shipping Addresses */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Shipping Addresses</CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addShippingAddress}
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Shipping Address
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {shippingAddresses.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-8">
              No shipping addresses added yet. Click "Add Shipping Address" to add one.
            </p>
          ) : (
            shippingAddresses.map((address, index) => (
              <Card key={index}>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="text-sm font-medium">Shipping Address {index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeShippingAddress(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="col-span-2 space-y-2">
                      <Label>Street Address *</Label>
                      <Input
                        value={address.street_address}
                        onChange={(e) => updateShippingAddress(index, "street_address", e.target.value)}
                        placeholder="Enter street address"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>City *</Label>
                      <Input
                        value={address.city}
                        onChange={(e) => updateShippingAddress(index, "city", e.target.value)}
                        placeholder="Enter city"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Postal Code *</Label>
                      <Input
                        value={address.postal_code}
                        onChange={(e) => updateShippingAddress(index, "postal_code", e.target.value)}
                        placeholder="Enter postal code"
                      />
                    </div>

                    <div className="col-span-2 space-y-2">
                      <Label>Country *</Label>
                      <Input
                        value={address.country}
                        onChange={(e) => updateShippingAddress(index, "country", e.target.value)}
                        placeholder="Enter country"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
}