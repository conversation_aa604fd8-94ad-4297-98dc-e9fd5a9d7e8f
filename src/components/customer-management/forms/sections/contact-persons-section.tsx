"use client";

import { UseFormReturn } from "react-hook-form";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { PlusCircle, Trash2 } from "lucide-react";
import type { CustomerFormData, CustomerContactPersonFormData } from "@/schemas/customer-management";

interface ContactPersonsSectionProps {
  form: UseFormReturn<CustomerFormData>;
}

export function ContactPersonsSection({ form }: ContactPersonsSectionProps) {
  const contactPersons = form.watch("contact_persons") || [];
  const customerType = form.watch("customer_type");

  const addContactPerson = () => {
    const newContact: CustomerContactPersonFormData = {
      name: "",
      title: "",
      email: "",
      phone: "",
      is_primary: contactPersons.length === 0, // First contact is primary by default
    };
    form.setValue("contact_persons", [...contactPersons, newContact]);
  };

  const removeContactPerson = (index: number) => {
    const updatedContacts = contactPersons.filter((_, i) => i !== index);
    // If we removed the primary contact, make the first one primary
    if (contactPersons[index].is_primary && updatedContacts.length > 0) {
      updatedContacts[0].is_primary = true;
    }
    form.setValue("contact_persons", updatedContacts);
  };

  const updateContactPerson = (index: number, field: keyof CustomerContactPersonFormData, value: any) => {
    const updatedContacts = [...contactPersons];
    updatedContacts[index] = { ...updatedContacts[index], [field]: value };
    
    // If setting as primary, unset all others
    if (field === "is_primary" && value === true) {
      updatedContacts.forEach((contact, i) => {
        if (i !== index) contact.is_primary = false;
      });
    }
    
    form.setValue("contact_persons", updatedContacts);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Contact Persons</CardTitle>
            {customerType === "INDIVIDUAL" && (
              <p className="text-sm text-muted-foreground mt-1">
                Optional for individual customers
              </p>
            )}
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addContactPerson}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Contact
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {contactPersons.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-8">
            {customerType === "INDIVIDUAL" 
              ? "No contact persons added. This is optional for individual customers."
              : "At least one contact person is required. Click \"Add Contact\" to add one."
            }
          </p>
        ) : (
          contactPersons.map((contact, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Name *</Label>
                    <Input
                      value={contact.name}
                      onChange={(e) => updateContactPerson(index, "name", e.target.value)}
                      placeholder="Enter contact name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Title</Label>
                    <Input
                      value={contact.title || ""}
                      onChange={(e) => updateContactPerson(index, "title", e.target.value)}
                      placeholder="Enter title"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Email *</Label>
                    <Input
                      type="email"
                      value={contact.email}
                      onChange={(e) => updateContactPerson(index, "email", e.target.value)}
                      placeholder="Enter email address"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Phone</Label>
                    <Input
                      value={contact.phone || ""}
                      onChange={(e) => updateContactPerson(index, "phone", e.target.value)}
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={contact.is_primary}
                      onCheckedChange={(checked) => updateContactPerson(index, "is_primary", checked)}
                    />
                    <Label>Primary Contact</Label>
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContactPerson(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </CardContent>
    </Card>
  );
}