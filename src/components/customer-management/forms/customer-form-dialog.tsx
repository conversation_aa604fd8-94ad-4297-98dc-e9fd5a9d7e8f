"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { customerSchema } from "@/schemas/customer-management";
import { type CustomerFormData } from "@/schemas/customer-management";
import { createCustomer, updateCustomer, getCustomerById } from "@/services/customer-management/customers-server.service";
import { AlertTriangle, X } from "lucide-react";

// Import form sections
import { BasicInformationSection } from "./sections/basic-information-section";
import { AddressSection } from "./sections/address-section";
import { ContactPersonsSection } from "./sections/contact-persons-section";
import { FinancialInformationSection } from "./sections/financial-information-section";

interface CustomerFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customerId?: string;
  onSuccess?: () => void;
}

export function CustomerFormDialog({
  open,
  onOpenChange,
  customerId,
  onSuccess,
}: CustomerFormDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});
  const [showValidationAlert, setShowValidationAlert] = useState(false);
  const isEditMode = !!customerId;

  const form = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      status: "ACTIVE",
      is_gst_registered: false,
      contact_persons: [],
      shipping_addresses: [],
      customer_type: "COMPANY" as const, // Default customer type
    },
  });

  useEffect(() => {
    if (open && customerId) {
      loadCustomer();
    } else if (!open) {
      form.reset();
      setValidationErrors({});
      setShowValidationAlert(false);
    }
  }, [open, customerId]);

  const loadCustomer = async () => {
    if (!customerId) return;
    
    setLoading(true);
    try {
      const result = await getCustomerById(customerId);
      if (result.success && result.data) {
        const customer = result.data;
        // Map database fields to form fields
        form.reset({
          customer_code: customer.customer_code || '',
          name: customer.name,
          display_name: customer.display_name || '',
          registration_number: customer.registration_number || '',
          tax_registration_number: customer.tax_registration_number || '',
          customer_type: customer.customer_type,
          category: customer.category || undefined,
          status: customer.status,
          email: customer.email,
          phone: customer.phone || '',
          website: customer.website || '',
          billing_address: customer.billing_address || undefined,
          shipping_addresses: customer.shipping_addresses || [],
          payment_terms: customer.payment_terms || '',
          credit_limit: customer.credit_limit ?? undefined,
          is_gst_registered: customer.is_gst_registered,
          contact_persons: customer.contact_persons || [],
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load customer details",
          variant: "destructive",
        });
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load customer details",
        variant: "destructive",
      });
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to map field names to their respective tabs
  const getFieldTab = (fieldName: string): string => {
    const fieldTabMap: Record<string, string> = {
      // Basic Info tab
      name: "Basic Info",
      display_name: "Basic Info",
      registration_number: "Basic Info",
      tax_registration_number: "Basic Info",
      customer_type: "Basic Info",
      category: "Basic Info",
      status: "Basic Info",
      email: "Basic Info",
      phone: "Basic Info",
      website: "Basic Info",
      is_gst_registered: "Basic Info",
      
      // Address tab
      billing_address: "Addresses",
      shipping_addresses: "Addresses",
      
      // Contacts tab
      contact_persons: "Contacts",
      
      // Financial tab
      payment_terms: "Financial",
      credit_limit: "Financial",
    };
    
    return fieldTabMap[fieldName] || "Unknown";
  };

  const onSubmit = async (data: CustomerFormData) => {
    setSaving(true);
    
    // Validate form and collect errors by tab
    const isValid = await form.trigger();
    
    if (!isValid) {
      const errors = form.formState.errors;
      const errorsByTab: Record<string, string[]> = {};
      
      // Process errors and group by tab
      Object.entries(errors).forEach(([fieldName, error]) => {
        const tab = getFieldTab(fieldName);
        if (!errorsByTab[tab]) {
          errorsByTab[tab] = [];
        }
        
        // Create user-friendly field names
        const friendlyFieldName = fieldName
          .replace(/_/g, ' ')
          .replace(/\b\w/g, l => l.toUpperCase());
        
        const errorMessage = error?.message || `${friendlyFieldName} is required`;
        errorsByTab[tab].push(errorMessage);
      });
      
      setValidationErrors(errorsByTab);
      setShowValidationAlert(true);
      setSaving(false);
      return;
    }
    
    try {
      const result = isEditMode
        ? await updateCustomer(customerId, data)
        : await createCustomer(data);

      if (result.success) {
        toast({
          title: "Success",
          description: isEditMode 
            ? "Customer updated successfully" 
            : `Customer created successfully${('customerCode' in result && result.customerCode) ? ` with code: ${result.customerCode}` : ''}`,
        });
        setShowValidationAlert(false);
        onSuccess?.();
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save customer",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? "Edit Customer" : "Add New Customer"}</DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? "Update the customer information below." 
              : "Fill in the details to create a new customer."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <ScrollArea className="h-[calc(90vh-200px)] pr-4">
              {showValidationAlert && Object.keys(validationErrors).length > 0 && (
                <Alert variant="destructive" className="mb-4 relative">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle className="pr-8">Please review and complete the form</AlertTitle>
                  <AlertDescription>
                    <div className="mt-2 space-y-2">
                      {Object.entries(validationErrors).map(([tab, errors]) => (
                        <div key={tab}>
                          <strong className="text-sm">{tab}: </strong>
                          <ul className="list-disc list-inside ml-2">
                            {errors.map((error, index) => (
                              <li key={index} className="text-sm">{error}</li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </AlertDescription>
                  <Button
                    type="button"
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2 h-auto p-1"
                    onClick={() => setShowValidationAlert(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </Alert>
              )}
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="address">Addresses</TabsTrigger>
                  <TabsTrigger value="contacts">Contacts</TabsTrigger>
                  <TabsTrigger value="financial">Financial</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 mt-4">
                  <BasicInformationSection form={form} isEditMode={isEditMode} />
                </TabsContent>

                <TabsContent value="address" className="space-y-4 mt-4">
                  <AddressSection form={form} />
                </TabsContent>

                <TabsContent value="contacts" className="space-y-4 mt-4">
                  <ContactPersonsSection form={form} />
                </TabsContent>

                <TabsContent value="financial" className="space-y-4 mt-4">
                  <FinancialInformationSection form={form} />
                </TabsContent>
              </Tabs>
            </ScrollArea>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading || saving}
              >
                Cancel
              </Button>
              <Button 
                type="button" 
                disabled={loading || saving}
                onClick={async () => {
                  // Manually trigger validation and submission
                  const isValid = await form.trigger(undefined, { shouldFocus: false });
                  
                  if (!isValid) {
                    const errors = form.formState.errors;
                    const errorsByTab: Record<string, string[]> = {};
                    
                    // Helper function to process nested errors
                    const processErrors = (obj: any, prefix: string = '') => {
                      Object.entries(obj).forEach(([key, value]: [string, any]) => {
                        const fieldPath = prefix ? `${prefix}.${key}` : key;
                        
                        if (value && typeof value === 'object' && value.message) {
                          // This is an error object
                          const tab = getFieldTab(fieldPath.split('.')[0]);
                          if (!errorsByTab[tab]) {
                            errorsByTab[tab] = [];
                          }
                          
                          // Create user-friendly field names
                          const friendlyFieldName = fieldPath
                            .replace(/\./g, ' ')
                            .replace(/_/g, ' ')
                            .replace(/\b\w/g, l => l.toUpperCase());
                          
                          const errorMessage = value.message || `${friendlyFieldName} is required`;
                          errorsByTab[tab].push(errorMessage);
                        } else if (value && typeof value === 'object' && !Array.isArray(value)) {
                          // Recursively process nested objects
                          processErrors(value, fieldPath);
                        }
                      });
                    };
                    
                    // Process all errors including nested ones
                    processErrors(errors);
                    
                    setValidationErrors(errorsByTab);
                    setShowValidationAlert(true);
                  } else {
                    // If valid, submit the form
                    form.handleSubmit(onSubmit)();
                  }
                }}
              >
                {saving ? "Saving..." : isEditMode ? "Update Customer" : "Create Customer"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}