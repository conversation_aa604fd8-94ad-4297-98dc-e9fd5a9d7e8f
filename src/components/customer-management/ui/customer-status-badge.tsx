import { Circle } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import type { CustomerStatus } from '@/types/customer-management/customer-management.types';

interface CustomerStatusBadgeProps {
  status: CustomerStatus;
  className?: string;
}

const statusConfig: Record<CustomerStatus, { color: string; label: string }> = {
  ACTIVE: {
    color: "fill-green-500 text-green-500",
    label: "Active"
  },
  INACTIVE: {
    color: "fill-gray-500 text-gray-500",
    label: "Inactive"
  },
  SUSPENDED: {
    color: "fill-amber-500 text-amber-500",
    label: "Suspended"
  },
  BLACKLISTED: {
    color: "fill-red-500 text-red-500",
    label: "Blacklisted"
  }
};

export function CustomerStatusBadge({ status, className }: CustomerStatusBadgeProps) {
  const config = statusConfig[status];

  return (
    <Button
      variant="outline"
      className={cn(
        "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
        className
      )}
    >
      <Circle className={cn("h-2 w-2", config.color)} />
      {config.label}
    </Button>
  );
}