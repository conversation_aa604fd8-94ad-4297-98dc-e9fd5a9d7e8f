"use client";

import { FC, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Loader2, 
  Building, 
  Mail, 
  Phone, 
  Globe, 
  CreditCard, 
  Calendar,
  History,
  User,
  Clock,
  Edit,
  Settings,
  MapPin,
  Receipt,
  TrendingUp
} from "lucide-react";
import { CustomerStatusBadge } from "@/components/customer-management/ui/customer-status-badge";
import { formatCurrency } from "@/lib/utils";
import type { Customer, CustomerPerformance } from "@/types/customer-management";

interface CustomerDetailProps {
  customerId: string;
  onEditClick?: () => void;
  onStatusChangeClick?: () => void;
}

export const CustomerDetail: FC<CustomerDetailProps> = ({
  customerId,
  onEditClick,
  onStatusChangeClick,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [performance, setPerformance] = useState<CustomerPerformance | null>(null);
  const [statusHistory, setStatusHistory] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("details");

  useEffect(() => {
    if (customerId) {
      loadCustomerDetails();
    }
  }, [customerId]);

  const loadCustomerDetails = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API calls when services are implemented
      // const [customerData, performanceData, historyData] = await Promise.all([
      //   getCustomerById(customerId),
      //   getCustomerPerformance(customerId),
      //   getCustomerStatusHistory(customerId),
      // ]);

      // Mock data for now
      const mockCustomer: Customer = {
        id: customerId,
        customer_code: "CUST-001",
        name: "Acme Corporation",
        display_name: "Acme Corp",
        registration_number: "REG123456",
        tax_registration_number: "TAX789012",
        customer_type: "COMPANY",
        category: "VIP",
        status: "ACTIVE",
        email: "<EMAIL>",
        phone: "****** 567 8900",
        website: "https://www.acmecorp.com",
        billing_address: {
          street_address: "123 Business Ave",
          city: "New York",
          state: "NY",
          postal_code: "10001",
          country: "United States"
        },
        shipping_addresses: [{
          street_address: "456 Warehouse St",
          city: "New York",
          state: "NY",
          postal_code: "10002",
          country: "United States"
        }],
        payment_terms: "Net 30",
        credit_limit: 100000,
        is_gst_registered: true,
        contact_persons: [{
          id: "1",
          name: "John Smith",
          title: "Procurement Manager",
          email: "<EMAIL>",
          phone: "****** 567 8901",
          is_primary: true
        }],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const mockPerformance: CustomerPerformance = {
        totalOrders: 45,
        totalAmount: 250000,
        onTimePaymentRate: 92.5,
        averagePaymentDays: 28,
        lastOrderDate: new Date().toISOString(),
        totalInvoices: 42,
        outstandingAmount: 15000
      };

      setCustomer(mockCustomer);
      setPerformance(mockPerformance);
    } catch (error) {
      console.error("Error loading customer details:", error);
    } finally {
      setLoading(false);
    }
  };

  const getCustomerTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      INDIVIDUAL: "Individual",
      COMPANY: "Company",
      GOVERNMENT: "Government",
      NON_PROFIT: "Non-Profit",
      EDUCATIONAL: "Educational"
    };
    return labels[type] || type;
  };

  const getCategoryLabel = (category?: string) => {
    if (!category) return "Not specified";
    const labels: Record<string, string> = {
      REGULAR: "Regular",
      VIP: "VIP",
      CORPORATE: "Corporate",
      RESELLER: "Reseller",
      DISTRIBUTOR: "Distributor"
    };
    return labels[category] || category;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Customer not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div>
          <h2 className="text-3xl font-bold">{customer.display_name || customer.name}</h2>
          <p className="text-muted-foreground">Code: {customer.customer_code}</p>
        </div>
        <div className="flex items-center gap-2">
          <CustomerStatusBadge status={customer.status} />
          <Button variant="outline" size="sm" onClick={onEditClick}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline" size="sm" onClick={onStatusChangeClick}>
            <Settings className="mr-2 h-4 w-4" />
            Change Status
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold">Company Information</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{getCustomerTypeLabel(customer.customer_type)}</span>
                </div>
                <div className="text-sm">
                  <span className="text-muted-foreground">Category:</span> {getCategoryLabel(customer.category)}
                </div>
                {customer.registration_number && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">Registration #:</span> {customer.registration_number}
                  </div>
                )}
                {customer.tax_registration_number && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">Tax Registration #:</span> {customer.tax_registration_number}
                  </div>
                )}
                <div className="text-sm">
                  <span className="text-muted-foreground">GST Registered:</span> {customer.is_gst_registered ? "Yes" : "No"}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Contact Information</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <a href={`mailto:${customer.email}`} className="text-sm hover:underline">
                    {customer.email}
                  </a>
                </div>
                {customer.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{customer.phone}</span>
                  </div>
                )}
                {customer.website && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <a 
                      href={customer.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm hover:underline"
                    >
                      {customer.website}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Financial Information */}
          <div className="space-y-4">
            <h3 className="font-semibold">Financial Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Payment Terms: {customer.payment_terms || "Not specified"}</span>
                </div>
                {customer.credit_limit && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">Credit Limit:</span> {formatCurrency(customer.credit_limit)}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Addresses */}
          {(customer.billing_address || (customer.shipping_addresses && customer.shipping_addresses.length > 0)) && (
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Addresses
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {customer.billing_address && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Billing Address</h4>
                      <div className="text-sm space-y-1">
                        <p>{customer.billing_address.street_address}</p>
                        <p>{customer.billing_address.city}, {customer.billing_address.state} {customer.billing_address.postal_code}</p>
                        <p>{customer.billing_address.country}</p>
                      </div>
                    </div>
                  )}
                  {customer.shipping_addresses && customer.shipping_addresses.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Shipping Addresses</h4>
                      {customer.shipping_addresses.map((address, index) => (
                        <div key={index} className="text-sm space-y-1 pb-2">
                          {customer.shipping_addresses!.length > 1 && (
                            <p className="font-medium">Address {index + 1}</p>
                          )}
                          <p>{address.street_address}</p>
                          <p>{address.city}, {address.state} {address.postal_code}</p>
                          <p>{address.country}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Contact Persons */}
          {customer.contact_persons && customer.contact_persons.length > 0 && (
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Contact Persons
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {customer.contact_persons.map((contact) => (
                    <div key={contact.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{contact.name}</span>
                        {contact.is_primary && (
                          <Badge variant="secondary" className="text-xs">Primary</Badge>
                        )}
                      </div>
                      {contact.title && (
                        <p className="text-sm text-muted-foreground">{contact.title}</p>
                      )}
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3 text-muted-foreground" />
                          <a href={`mailto:${contact.email}`} className="text-xs hover:underline">
                            {contact.email}
                          </a>
                        </div>
                        {contact.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs">{contact.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Timestamps */}
          <Separator />
          <div className="flex items-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Created: {new Date(customer.created_at).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Updated: {new Date(customer.updated_at).toLocaleDateString()}</span>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="performance" className="space-y-6">
          {performance ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center gap-2">
                    <Receipt className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Total Orders</span>
                  </div>
                  <p className="text-2xl font-bold">{performance.totalOrders}</p>
                </div>
                
                <div className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Total Revenue</span>
                  </div>
                  <p className="text-2xl font-bold">{formatCurrency(performance.totalAmount)}</p>
                </div>
                
                <div className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">On-Time Payment Rate</span>
                  </div>
                  <p className="text-2xl font-bold">{performance.onTimePaymentRate.toFixed(1)}%</p>
                </div>
                
                <div className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Avg Payment Days</span>
                  </div>
                  <p className="text-2xl font-bold">{performance.averagePaymentDays}</p>
                </div>
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <h4 className="font-semibold">Invoice Summary</h4>
                  <div className="space-y-1">
                    <div className="text-sm">
                      <span className="text-muted-foreground">Total Invoices:</span> {performance.totalInvoices}
                    </div>
                    <div className="text-sm">
                      <span className="text-muted-foreground">Outstanding Amount:</span> {formatCurrency(performance.outstandingAmount)}
                    </div>
                  </div>
                </div>
                
                {performance.lastOrderDate && (
                  <div className="space-y-2">
                    <h4 className="font-semibold">Recent Activity</h4>
                    <div className="text-sm">
                      <span className="text-muted-foreground">Last Order:</span> {new Date(performance.lastOrderDate).toLocaleDateString()}
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No performance data available</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="history" className="space-y-6">
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <History className="h-4 w-4" />
              Status Change History
            </h3>
            
            {/* Current Status */}
            <div className="border rounded-lg p-4 bg-muted/50">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CustomerStatusBadge status={customer.status} />
                    <span className="text-sm font-medium">Current Status</span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>Last updated: {new Date(customer.updated_at).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Timeline placeholder */}
            <div className="text-center py-8 text-muted-foreground">
              <p className="text-sm">Full status history timeline will be available when status tracking is implemented.</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};