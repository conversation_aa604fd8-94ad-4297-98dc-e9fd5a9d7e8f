"use client"

import { forwardR<PERSON>, useI<PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>ffect, useState } from "react"
import { useInvoicesEditModal } from "@/hooks/finance/edit-invoices"
import { format } from "date-fns"
import { getGLAccountsByEntity } from "@/services/finance/invoices.service"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import {
    Table,
    TableBody,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Calendar } from "@/components/ui/calendar"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { CalendarIcon, Download } from "lucide-react"
import type { invoiceDetails } from "@/types/finance/invoices.types"
import { EditInvoiceChildRef } from "@/app/(private)/(sales_invoices)/invoices/edit/page"

interface InvoicesEditModalProps {
    invoice: invoiceDetails
    taxRate: number
    paymentOptions: { code: string, label: string }[]
    invoiceEntities: { id: string, name: string }[]
    fetchInvoice: () => Promise<void>
}

const PAYMENT_TERMS_OPTIONS = [
    { value: "IMMEDIATE", label: "Immediate" },
    { value: "COD", label: "C.O.D." },
    { value: "NET7", label: "NET 7" },
    { value: "NET14", label: "NET 14" },
    { value: "NET30", label: "NET 30" },
];

export const InvoicesEditModal = forwardRef<EditInvoiceChildRef, InvoicesEditModalProps>(({ invoice, taxRate, paymentOptions, invoiceEntities, fetchInvoice }, ref) => {
    const {
        form,
        invoiceItemFields,
        addinvoiceItems,
        removeInvoiceItem,
        receiptFields,
        addReceipt,
        removeReceipt,
        setItemsToDelete,
        setReceiptsToDelete,
        isPdfLoading,
        calculateAllPricing,
        onSubmit,
        downloadPdf,
    } = useInvoicesEditModal({ invoice, taxRate, fetchInvoice })

    const [glAccounts, setGlAccounts] = useState<{ account_code: string, account_name: string }[]>([]);
    const selectedEntity = form.watch('entities');
    
    useEffect(() => {
        if (selectedEntity) {
            getGLAccountsByEntity(selectedEntity).then(accounts => {
                setGlAccounts(accounts);
            });
        } else {
            setGlAccounts([]);
        }
    }, [selectedEntity]);

    useImperativeHandle(ref, () => ({
        onSubmit,
    }));

    return (
        <Card className="mt-4 p-8">
            <Form {...form}>
                <form className="space-y-6">
                    <h4 className="text-lg font-medium">Invoice Details</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                            control={form.control}
                            name={'entities'}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Entity</FormLabel>
                                    <FormControl>
                                        <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                            disabled={true}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select entities" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {invoiceEntities.map((item) => (
                                                    <SelectItem key={item.id} value={item.id}>
                                                        {item.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <FormField
                                control={form.control}
                                name={`attentionTo`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Attention To</FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="Enter attention to"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name={`billTo`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Bill To</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="Enter your bill to here"
                                                className="min-h-[200px]"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="space-y-4">
                            <FormItem>
                                <FormLabel>Invoice Number</FormLabel>
                                <Input value={invoice.invoice_number || "--"} disabled />
                                <FormMessage />
                            </FormItem>

                            <FormField
                                control={form.control}
                                name={`issueDate`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Issue Date</FormLabel>
                                        <FormControl>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <div className="relative">
                                                        <Input
                                                            type="text"
                                                            readOnly
                                                            className="pointer-events-none"
                                                            placeholder="Pick a date"
                                                            value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                        />
                                                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                    </div>
                                                </PopoverTrigger>
                                                <PopoverContent>
                                                    <Calendar
                                                        mode="single"
                                                        selected={field.value}
                                                        onSelect={(date) => {
                                                            if (date) {
                                                                const utcDate = new Date(Date.UTC(
                                                                    date.getFullYear(),
                                                                    date.getMonth(),
                                                                    date.getDate()
                                                                ));
                                                                field.onChange(utcDate);
                                                            }
                                                        }}
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name={`paymentTerms`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Terms</FormLabel>
                                        <FormControl>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select payment terms" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {PAYMENT_TERMS_OPTIONS.map((option) => (
                                                        <SelectItem key={option.value} value={option.value}>
                                                            {option.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name={`dueDate`}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Due Date</FormLabel>
                                        <FormControl>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <div className="relative">
                                                        <Input
                                                            type="text"
                                                            readOnly
                                                            className="pointer-events-none"
                                                            placeholder="Pick a date"
                                                            value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                        />
                                                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                    </div>
                                                </PopoverTrigger>
                                                <PopoverContent>
                                                    <Calendar
                                                        mode="single"
                                                        selected={field.value}
                                                        onSelect={(date) => {
                                                            if (date) {
                                                                const utcDate = new Date(Date.UTC(
                                                                    date.getFullYear(),
                                                                    date.getMonth(),
                                                                    date.getDate()
                                                                ));
                                                                field.onChange(utcDate);
                                                            }
                                                        }}
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>

                    <FormField
                        control={form.control}
                        name={`title`}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Title</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter invoice title"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <Separator />

                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Item Details</h4>

                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>GL Code</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Quantity</TableHead>
                                    <TableHead>Unit Price</TableHead>
                                    <TableHead>Tax Rate</TableHead>
                                    <TableHead>Tax Amount</TableHead>
                                    <TableHead>Line Total</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {invoiceItemFields.map((field, index) => (
                                    <TableRow key={field.field_id}>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`invoice_items.${index}.gl_account_code`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Select
                                                                onValueChange={field.onChange}
                                                                value={field.value}
                                                                disabled={!selectedEntity}
                                                            >
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder={selectedEntity ? "Select GL Code" : "Select entity first"} />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {glAccounts.map((account) => (
                                                                        <SelectItem key={account.account_code} value={account.account_code}>
                                                                            {account.account_code} - {account.account_name}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`invoice_items.${index}.description`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Textarea
                                                                placeholder="Description"
                                                                {...field}
                                                                className="resize-none"
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`invoice_items.${index}.price_type`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input
                                                                placeholder="Type"
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`invoice_items.${index}.quantity`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                {...field}
                                                                onChange={(e) => {
                                                                    field.onChange(e);
                                                                    calculateAllPricing(index);
                                                                }}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`invoice_items.${index}.unit_price`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                {...field}
                                                                onChange={(e) => {
                                                                    field.onChange(e);
                                                                    calculateAllPricing(index);
                                                                }}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            {form.watch(`invoice_items.${index}.tax_rate`)?.toFixed(2)}
                                        </TableCell>
                                        <TableCell>
                                            {form.watch(`invoice_items.${index}.tax_amount`)?.toFixed(2)}
                                        </TableCell>
                                        <TableCell>
                                            {form.watch(`invoice_items.${index}.line_total`)?.toFixed(2)}
                                        </TableCell>
                                        <TableCell>
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => {
                                                    removeInvoiceItem(index);
                                                    if (field.id) {
                                                        setItemsToDelete((prev) => [...prev, field.id!]);
                                                    }
                                                }}
                                            >
                                                Remove
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        <Button type="button" onClick={addinvoiceItems}>
                            Add Item
                        </Button>

                        <Table>
                            <TableFooter className="border-none">
                                <TableRow>
                                    <TableCell colSpan={8}>Sub Total</TableCell>
                                    <TableCell className="text-right">{form.watch('subtotal')?.toFixed(2) || '--'}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell colSpan={8}>Total Tax Amount</TableCell>
                                    <TableCell className="text-right">{form.watch('totalTax')?.toFixed(2) || '--'}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell colSpan={8}>Grand Total</TableCell>
                                    <TableCell className="text-right">{form.watch('grandTotal')?.toFixed(2) || '--'}</TableCell>
                                </TableRow>
                            </TableFooter>
                        </Table>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Status</FormLabel>
                                    <FormControl>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="DRAFT">Draft</SelectItem>
                                                <SelectItem value="UNPAID">Unpaid</SelectItem>
                                                <SelectItem value="SENT">Sent</SelectItem>
                                                <SelectItem value="PARTIALLY_PAID">Partially Paid</SelectItem>
                                                <SelectItem value="PAID">Paid</SelectItem>
                                                <SelectItem value="VOID">Void</SelectItem>
                                                <SelectItem value="OVERDUE">Overdue</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="notes"
                            render={({ field }) => (
                                <FormItem className="sm:col-span-3">
                                    <FormLabel>Notes</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder="Enter your notes here" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <Separator />

                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Payment Receipt</h4>

                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Receipt No</TableHead>
                                    <TableHead>Date Of Payment</TableHead>
                                    <TableHead>Amount Paid</TableHead>
                                    <TableHead>Payment Mode</TableHead>
                                    <TableHead>Reference</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {receiptFields.map((field, index) => (
                                    <TableRow key={field.field_id}>
                                        <TableCell>
                                            {form.watch(`receipts.${index}.receiptNo`)}
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`receipts.${index}.dateOfPayment`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            {!receiptFields[index].canEdit ? (
                                                                <Input
                                                                    type="text"
                                                                    placeholder="Pick a date"
                                                                    disabled
                                                                    value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                                />
                                                            ) : (
                                                                <Popover>
                                                                    <PopoverTrigger asChild>
                                                                        <div className="relative">
                                                                            <Input
                                                                                type="text"
                                                                                readOnly
                                                                                className="pointer-events-none"
                                                                                placeholder="Pick a date"
                                                                                value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                                            />
                                                                            <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                                        </div>
                                                                    </PopoverTrigger>
                                                                    <PopoverContent>
                                                                        <Calendar
                                                                            mode="single"
                                                                            selected={field.value || undefined}
                                                                            onSelect={(date) => {
                                                                                if (date) {
                                                                                    const utcDate = new Date(Date.UTC(
                                                                                        date.getFullYear(),
                                                                                        date.getMonth(),
                                                                                        date.getDate()
                                                                                    ));
                                                                                    field.onChange(utcDate);
                                                                                }
                                                                            }}
                                                                            disabled={[{ after: new Date() }]}
                                                                        />
                                                                    </PopoverContent>
                                                                </Popover>
                                                            )}
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`receipts.${index}.amountPaid`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                placeholder="Enter the amount paid"
                                                                {...field}
                                                                disabled={!receiptFields[index].canEdit}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`receipts.${index}.paymentMode`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Select
                                                                onValueChange={field.onChange}
                                                                defaultValue={field.value}
                                                                disabled={!receiptFields[index].canEdit}
                                                            >
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder="Select Payment Mode" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {paymentOptions.map((option) => (
                                                                        <SelectItem key={option.code} value={option.code}>
                                                                            {option.label}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`receipts.${index}.referenceNo`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input
                                                                placeholder="Enter your reference"
                                                                {...field}
                                                                disabled={!receiptFields[index].canEdit}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell className="flex flex-wrap items-center gap-2">
                                            {field.id ? (
                                                <>
                                                    <AlertDialog>
                                                        <AlertDialogTrigger asChild>
                                                            <Button
                                                                type="button"
                                                                variant="destructive"
                                                                size="sm"
                                                            >
                                                                Remove
                                                            </Button>
                                                        </AlertDialogTrigger>
                                                        <AlertDialogContent>
                                                            <AlertDialogHeader>
                                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                                <AlertDialogDescription>
                                                                    Removing this payment receipt will affect the invoice's payment records and may recalculate the remaining balance.
                                                                    This action cannot be undone.
                                                                </AlertDialogDescription>
                                                            </AlertDialogHeader>
                                                            <AlertDialogFooter>
                                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                                <AlertDialogAction
                                                                    onClick={() => {
                                                                        removeReceipt(index);
                                                                        setReceiptsToDelete((prev) => [...prev, field.id!]);
                                                                    }}
                                                                >
                                                                    Remove
                                                                </AlertDialogAction>
                                                            </AlertDialogFooter>
                                                        </AlertDialogContent>
                                                    </AlertDialog>

                                                    <Button
                                                        variant="outline"
                                                        className="border-gray-300"
                                                        onClick={() => downloadPdf(field.field_id, field.id)}
                                                        disabled={isPdfLoading[field.field_id]}
                                                    >
                                                        <Download />
                                                        {isPdfLoading[field.field_id] ? 'Processing...' : 'Download PDF'}
                                                    </Button>
                                                </>
                                            ) : (
                                                <Button
                                                    type="button"
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => removeReceipt(index)}
                                                >
                                                    Remove
                                                </Button>
                                            )}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        <Button type="button" onClick={addReceipt}>
                            Add Payment Receipt
                        </Button>
                    </div>
                </form>
            </Form>
        </Card>
    )
})