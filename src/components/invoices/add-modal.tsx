"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import {
    Table,
    TableBody,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Calendar } from "@/components/ui/calendar"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { CalendarIcon } from "lucide-react"
import { useInvoicesAddModal } from "@/hooks/finance/add-invoices";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { getGLAccountsByEntity } from "@/services/finance/invoices.service";

interface InvoicesAddModalProps {
    taxRate: number
    invoiceEntities: { id: string, name: string }[]
}

const PAYMENT_TERMS_OPTIONS = [
    { value: "IMMEDIATE", label: "Immediate" },
    { value: "COD", label: "C.O.D." },
    { value: "NET7", label: "NET 7" },
    { value: "NET14", label: "NET 14" },
    { value: "NET30", label: "NET 30" },
];

export function AddInvoice({ taxRate, invoiceEntities }: InvoicesAddModalProps) {
    const {
        form,
        invoiceItemFields,
        addinvoiceItems,
        removeInvoiceItem,
        calculateAllPricing,
        onSubmit,
        isSubmitting,
    } = useInvoicesAddModal({ taxRate })
    
    const [glAccounts, setGlAccounts] = useState<{ account_code: string, account_name: string }[]>([]);
    const selectedEntity = form.watch('entities');
    
    useEffect(() => {
        if (selectedEntity) {
            getGLAccountsByEntity(selectedEntity).then(accounts => {
                setGlAccounts(accounts);
            });
        } else {
            setGlAccounts([]);
        }
    }, [selectedEntity]);

    return (
        <div className="p-4">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-semibold">Add Invoice</h1>
                </div>
                <Button onClick={onSubmit} disabled={isSubmitting}>{isSubmitting ? 'Submitting' : 'Submit'}</Button>
            </div>
            <Card className="mt-4 p-8">
                <Form {...form}>
                    <form className="space-y-6">
                        <h4 className="text-lg font-medium">Invoice Details</h4>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                                control={form.control}
                                name={'entities'}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Entity</FormLabel>
                                        <FormControl>
                                            <Select
                                                onValueChange={field.onChange}
                                                defaultValue={field.value}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select entities" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {invoiceEntities.map((item) => (
                                                        <SelectItem key={item.id} value={item.id}>
                                                            {item.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name={`attentionTo`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Attention To</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Enter attention to"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name={`billTo`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Bill To</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="Enter your bill to here"
                                                    className="min-h-[200px]"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name={`issueDate`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Issue Date</FormLabel>
                                            <FormControl>
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <div className="relative">
                                                            <Input
                                                                type="text"
                                                                readOnly
                                                                className="pointer-events-none"
                                                                placeholder="Pick a date"
                                                                value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                            />
                                                            <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                        </div>
                                                    </PopoverTrigger>
                                                    <PopoverContent>
                                                        <Calendar
                                                            mode="single"
                                                            selected={field.value}
                                                            onSelect={(date) => {
                                                                if (date) {
                                                                    const utcDate = new Date(Date.UTC(
                                                                        date.getFullYear(),
                                                                        date.getMonth(),
                                                                        date.getDate()
                                                                    ));
                                                                    field.onChange(utcDate);
                                                                }
                                                            }}
                                                        />
                                                    </PopoverContent>
                                                </Popover>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name={`paymentTerms`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Terms</FormLabel>
                                            <FormControl>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    defaultValue={field.value}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select payment terms" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {PAYMENT_TERMS_OPTIONS.map((option) => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name={`dueDate`}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Due Date</FormLabel>
                                            <FormControl>
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <div className="relative">
                                                            <Input
                                                                type="text"
                                                                readOnly
                                                                className="pointer-events-none"
                                                                placeholder="Pick a date"
                                                                value={field.value ? format(field.value, "dd-MM-yyyy") : ""}
                                                            />
                                                            <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                        </div>
                                                    </PopoverTrigger>
                                                    <PopoverContent>
                                                        <Calendar
                                                            mode="single"
                                                            selected={field.value}
                                                            onSelect={(date) => {
                                                                if (date) {
                                                                    const utcDate = new Date(Date.UTC(
                                                                        date.getFullYear(),
                                                                        date.getMonth(),
                                                                        date.getDate()
                                                                    ));
                                                                    field.onChange(utcDate);
                                                                }
                                                            }}
                                                        />
                                                    </PopoverContent>
                                                </Popover>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <FormField
                            control={form.control}
                            name={`title`}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Title</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="Enter invoice title"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <Separator />

                        <div className="space-y-4">
                            <h4 className="text-lg font-medium">Item Details</h4>

                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>GL Code</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Quantity</TableHead>
                                        <TableHead>Unit Price</TableHead>
                                        <TableHead>Tax Rate</TableHead>
                                        <TableHead>Tax Amount</TableHead>
                                        <TableHead>Line Total</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {invoiceItemFields.map((field, index) => (
                                        <TableRow key={field.id}>
                                            <TableCell>
                                                <FormField
                                                    control={form.control}
                                                    name={`invoice_items.${index}.gl_account_code`}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormControl>
                                                                <Select
                                                                    onValueChange={field.onChange}
                                                                    value={field.value}
                                                                    disabled={!selectedEntity}
                                                                >
                                                                    <SelectTrigger>
                                                                        <SelectValue placeholder={selectedEntity ? "Select GL Code" : "Select entity first"} />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        {glAccounts.map((account) => (
                                                                            <SelectItem key={account.account_code} value={account.account_code}>
                                                                                {account.account_code} - {account.account_name}
                                                                            </SelectItem>
                                                                        ))}
                                                                    </SelectContent>
                                                                </Select>
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormField
                                                    control={form.control}
                                                    name={`invoice_items.${index}.description`}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormControl>
                                                                <Textarea
                                                                    placeholder="Description"
                                                                    {...field}
                                                                    className="resize-none"
                                                                />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormField
                                                    control={form.control}
                                                    name={`invoice_items.${index}.price_type`}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormControl>
                                                                <Input
                                                                    placeholder="Type"
                                                                    {...field}
                                                                />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormField
                                                    control={form.control}
                                                    name={`invoice_items.${index}.quantity`}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormControl>
                                                                <Input
                                                                    type="number"
                                                                    {...field}
                                                                    onChange={(e) => {
                                                                        field.onChange(e);
                                                                        calculateAllPricing(index);
                                                                    }}
                                                                />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormField
                                                    control={form.control}
                                                    name={`invoice_items.${index}.unit_price`}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormControl>
                                                                <Input
                                                                    type="number"
                                                                    {...field}
                                                                    onChange={(e) => {
                                                                        field.onChange(e);
                                                                        calculateAllPricing(index);
                                                                    }}
                                                                />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                {form.watch(`invoice_items.${index}.tax_rate`)?.toFixed(2)}
                                            </TableCell>
                                            <TableCell>
                                                {form.watch(`invoice_items.${index}.tax_amount`)?.toFixed(2)}
                                            </TableCell>
                                            <TableCell>
                                                {form.watch(`invoice_items.${index}.line_total`)?.toFixed(2)}
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    type="button"
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => removeInvoiceItem(index)}
                                                >
                                                    Remove
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>

                            <Button type="button" onClick={addinvoiceItems}>
                                Add Item
                            </Button>

                            <Table>
                                <TableFooter className="border-none">
                                    <TableRow>
                                        <TableCell colSpan={8}>Sub Total</TableCell>
                                        <TableCell className="text-right">{form.watch('subtotal')?.toFixed(2) || '--'}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell colSpan={8}>Total Tax Amount</TableCell>
                                        <TableCell className="text-right">{form.watch('totalTax')?.toFixed(2) || '--'}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell colSpan={8}>Grand Total</TableCell>
                                        <TableCell className="text-right">{form.watch('grandTotal')?.toFixed(2) || '--'}</TableCell>
                                    </TableRow>
                                </TableFooter>
                            </Table>
                        </div>

                        <Separator />

                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <FormField
                                control={form.control}
                                name="notes"
                                render={({ field }) => (
                                    <FormItem className="sm:col-span-3">
                                        <FormLabel>Notes</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder="Enter your notes here" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </form>
                </Form>
            </Card>
        </div>
    );
}
