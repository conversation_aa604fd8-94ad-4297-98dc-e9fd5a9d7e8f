import { useEffect, useState } from "react";
import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { getOptionLabel } from "@/services/system-options.service";

export const WorkingExperienceSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const [addressCountry, setAddressCountry] = useState<Record<number, string>>({})

    useEffect(() => {
        const fetchTitles = async () => {
            const titles: Record<number, string> = {};

            await Promise.all(
                applicationDetail.workExperience?.map(async (workExp, idx) => {
                    if (workExp.address.country) {
                        const label = await getOptionLabel(workExp.address.country, 'country');
                        titles[idx] = label;
                    }
                }) || []
            );

            setAddressCountry(titles);
        };

        fetchTitles();
    }, [applicationDetail.workExperience]);

    const groupedContent = applicationDetail.workExperience?.map((workingExperience, index) => {
        const sections = [
            {
                header: `Working Experience ${index + 1}`,
                items: [
                    { label: 'Period From', value: workingExperience?.period_from || '--' },
                    { label: 'Period To', value: workingExperience?.period_to || '--' },
                    { label: 'Designation', value: workingExperience?.designation || '--' },
                    { label: 'Duties', value: workingExperience?.duties || '--' }
                ]
            },
            {
                items: [
                    { label: 'Company Name', value: workingExperience?.company.name || '--' }
                ]
            },
            {
                items: [
                    {
                        label: 'Company Address',
                        value: (
                            [
                                workingExperience?.address?.line1 || '--',
                                workingExperience?.address?.line2 || '--',
                                workingExperience?.address?.city || '--',
                                workingExperience?.address?.state || '--',
                                workingExperience?.address?.postalCode || '--',
                                addressCountry[index]
                            ].filter(part => part).join(", ")
                        ) || '--'
                    }
                ]
            }
        ];

        return sections;
    })?.flat() || [];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};
