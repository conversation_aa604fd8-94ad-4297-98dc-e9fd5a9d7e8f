import { useEffect, useState } from "react";
import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { getOptionLabel } from "@/services/system-options.service";

export const ProjectSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const [addressCountry, setAddressCountry] = useState<Record<number, string>>({})

    useEffect(() => {
        const fetchTitles = async () => {
            const titles: Record<number, string> = {};

            await Promise.all(
                applicationDetail.projects?.map(async (project, idx) => {
                    if (project.address.country) {
                        const label = await getOptionLabel(project.address.country, 'country');
                        titles[idx] = label;
                    }
                }) || []
            );

            setAddressCountry(titles);
        };

        fetchTitles();
    }, [applicationDetail.projects]);


    const groupedContent = applicationDetail.projects?.map((project, index) => {
        const sections = [
            {
                header: `Project ${index + 1}`,
                items: [
                    { label: 'Involvement From', value: project?.involvement_from || '--' },
                    { label: 'Involvement To', value: project?.involvement_to || '--' },
                    { label: 'Project Title', value: project?.title || '--' },
                    { label: 'Project Reference No', value: project?.reference_no || '--' },
                    { label: 'Duration', value: project?.duration || '--' },
                    { label: 'Position Held', value: project?.position_held || '--' },
                    { label: 'Employment Type', value: project?.work_type || '--' },
                    { label: 'Details', value: project?.details || '--' },
                    { label: 'Supervising Professional Engineer Name', value: project?.spe_name || '--' }
                ]
            },
            {
                items: [
                    { label: 'Company Name', value: project?.company.name || '--' }
                ]
            },
            {
                items: [
                    {
                        label: 'Address',
                        value: (
                            [
                                project?.address?.line1 || '--',
                                project?.address?.line2 || '--',
                                project?.address?.city || '--',
                                project?.address?.state || '--',
                                project?.address?.postalCode || '--',
                                addressCountry[index]
                            ].filter(part => part).join(", ")
                        ) || '--'
                    }
                ]
            },
            {
                items: [
                    {
                        label: 'Total Duration',
                        value: project
                            ? `${project.total_year || 0} Year(s) and ${project.total_month || 0} Month(s)`
                            : '--'
                    }
                ]
            }
        ];

        return sections;
    })?.flat() || [];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};