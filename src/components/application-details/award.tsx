import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";

export const AwardSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const groupedContent = applicationDetail.awards?.map((award, index) => {

        return {
            header: `Other Engineering Affiliation, Awards & Distinctions ${index + 1}`,
            items: [
                { label: 'Record Type', value: award.record_type || "--" },
                { label: 'Record Number', value: award.record_number || "--" },
                { label: 'Date of Election', value: award.date_of_election || "--" },
                { label: 'Details', value: award.detail || "--" },
                { label: 'Abbreviation', value: award.abbreviation || "--" },
                { label: 'Expire DATE', value: award.expiry_date || "--" },
            ].map(item => ({
                ...item,
                value: item.value !== undefined && item.value !== null ? item.value : '--'
            }))
        };
    }) || [];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};