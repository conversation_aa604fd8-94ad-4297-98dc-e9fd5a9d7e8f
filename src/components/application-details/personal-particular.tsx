import { useEffect, useState } from "react";
import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { getLanguageLabels, getOptionLabel } from "@/services/system-options.service";

export const PersonalParticularSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
  const [processedData, setProcessedData] = useState<any>(null);

  useEffect(() => {
    const processData = async () => {
      if (!applicationDetail) return;
      const profile = applicationDetail.personalInfo;

      const updated = {
        ...profile,
        title: profile?.title ? await getOptionLabel(profile.title, 'salutation') : '--',
        identification_type: profile?.identification_type ? await getOptionLabel(profile.identification_type, 'id_type') : '--',
        spoken_languages: profile?.spoken_languages ? await getLanguageLabels(profile.spoken_languages) : '--',
        written_languages: profile?.written_languages ? await getLanguageLabels(profile.written_languages) : '--',
      };

      setProcessedData(updated);
    };

    processData();
  }, [applicationDetail]);

  if (!applicationDetail || !processedData) return <LoadingSpinner />;

  const profile = processedData;

  const groupedContent = [
    {
      header: '',
      items: [
        { label: 'Full Name', value: profile?.full_name },
        { label: 'Date of Birth', value: profile?.date_of_birth },
        { label: 'Place of Birth', value: profile?.place_of_birth },
        { label: 'Title', value: profile?.title },
        { label: 'Identification Type', value: profile?.identification_type },
        { label: 'Identification No', value: profile?.identification_no },
        { label: 'Nationality', value: profile?.nationality },
        { label: 'Gender', value: profile?.gender },
        { label: 'Aces Member', value: profile?.is_aces_member === true ? 'Yes' : profile.is_aces_member === false ? 'No' : '--' },
        { label: 'Language Spoken', value: profile?.spoken_languages },
        { label: 'Language Written', value: profile?.written_languages },
      ].map(item => ({
        ...item,
        value: item.value !== undefined && item.value !== null ? item.value : '--'
      }))
    }
  ];

  return <SectionBox title={title} groupedContent={groupedContent} />;
};
