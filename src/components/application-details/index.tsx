"use client"

import { type AppointmentDetail, type ApplicationDetail, ApplicationSectionsConfig } from "@/types/applications/applications.types"
import { Button } from "@/components/ui/button"
import { Check } from 'lucide-react'
import { useState } from "react"
import { Documents } from "@/components/member-details/documents"
import { DocumentService } from "@/services/document.service"
import { useToast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/application-details/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ApplicationService } from "@/services/applications/application.service"
import { SectionBoxProps } from "@/app/(private)/(application)/applications/[id]/interface"
import { ApplicationSection } from "@/components/application-details/application"
import { PersonalParticularSection } from "@/components/application-details/personal-particular"
import { IndustriesSection } from "@/components/application-details/industries"
import { EmploymentSection } from "@/components/application-details/employment"
import { EducationSection } from "@/components/application-details/education"
import { WorkingExperienceSection } from "@/components/application-details/working-experience"
import { AwardSection } from "@/components/application-details/award"
import { SponsorshipSection } from "@/components/application-details/sponsorship"
import { NomineeSection } from "@/components/application-details/nominee"
import { CriminalRecordSection } from "@/components/application-details/criminal-record"
import { ProjectSection } from "@/components/application-details/project"
import { ContactInfoSection } from "@/components/application-details/contact-info"
import { AppointmentDetails } from "@/components/application-details/appointment"
import { CompanyParticularSection } from "@/components/application-details/company-particular"
import { FormSection, FormSectionType } from "@/types/application-form.types"

const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'success'
        case 'rejected':
            return 'destructive'
        case 'submitted_pending_payment':
            return 'secondary'
        case 'paid_pending_review':
            return 'outline'
        default:
            return 'secondary'
    }
}

const ActionMenu = ({ applicationDetail, onUpdate }: { applicationDetail: ApplicationDetail; onUpdate?: (updatedApplication: ApplicationDetail) => void }) => {
    const initialStatus =
        applicationDetail?.applications?.status === "APPROVED" || applicationDetail?.applications?.status === "REJECTED"
            ? applicationDetail.applications.status
            : "";
    const [open, setOpen] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState<"APPROVED" | "REJECTED" | "">(initialStatus);
    const [comments, setComments] = useState(applicationDetail?.applications?.comment || "");
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const { toast } = useToast();

    const handleUpdate = async () => {
        try {
            setErrorMessage(null);

            const application = applicationDetail?.applications;
            if (!application || !application.id) {
                setErrorMessage("No Application Data.");
                return;
            }

            // Validation: Ensure a valid status is selected
            if (!selectedStatus) {
                throw new Error("Please select a valid status.");
            }

            // Check for no changes
            if (
                selectedStatus === application.status &&
                comments === application.comment
            ) {
                setErrorMessage("No changes were made.");
                return;
            }

            // Perform the update
            const result = await ApplicationService.updateApplications(application.id, selectedStatus, comments);

            if (result) {
                toast({
                    title: "Success",
                    description: "Application Update successful.",
                });

                if (onUpdate) {
                    onUpdate({
                        ...applicationDetail,
                        applications: {
                            ...application,
                            status: selectedStatus,
                            comment: comments,
                        },
                    });
                }
            } else {
                setErrorMessage("Failed to update applications, please try again later.");
            }

            setOpen(false);
        } catch {
            setErrorMessage("An unexpected error occurred, please try again later.");
        }
    };

    return (
        <div className="flex justify-end mt-6">
            {/* Approve / Reject Buttons */}
            <div className="flex space-x-2">
                <Button
                    variant="outline"
                    className="flex items-center space-x-2"
                    onClick={() => {
                        setOpen(true);
                    }}
                >
                    <Check className="h-4 w-4" />
                    <span>Update Review Status</span>
                </Button>
            </div>

            {/* Dialog for Status Update */}
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogTrigger asChild>
                    {/* Hidden Trigger - buttons handle the dialog open state */}
                </DialogTrigger>
                <DialogContent className="rounded-xl p-8">
                    <DialogHeader>
                        <DialogTitle>Update Status and Add Comments</DialogTitle>
                        <DialogDescription>Please update the status and add your comments.</DialogDescription>
                    </DialogHeader>
                    <div className="mt-4">
                        <Label className="block mb-2 text-sm font-semibold">Status</Label>
                        <select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value as "APPROVED" | "REJECTED" | "")}
                            className="w-full border rounded p-2 text-sm"
                        >
                            <option value="" disabled>
                                Please select a status
                            </option>
                            <option value="APPROVED">Approved</option>
                            <option value="REJECTED">Rejected</option>
                        </select>
                    </div>
                    <div className="mt-4">
                        <Label className="block mb-2 text-sm font-semibold">Comments</Label>
                        <Textarea
                            value={comments}
                            onChange={(e) => setComments(e.target.value)}
                            className="w-full border rounded p-2"
                            rows={4}
                            placeholder="Add comments (optional)"
                        />
                    </div>

                    {errorMessage && (
                        <div className="mt-4 text-red-500 text-sm">
                            <strong>Error:</strong> {errorMessage}
                        </div>
                    )}

                    <div className="flex justify-end mt-4">
                        <Button variant="outline" onClick={() => setOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            variant="default"
                            onClick={handleUpdate}
                            disabled={!selectedStatus}
                            className="ml-2"
                        >
                            Save
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export const SectionBox: React.FC<SectionBoxProps> = ({ title, groupedContent }) => {
    const renderValue = (value: string | number | Date | null | undefined) => {
        if (value === null || value === undefined) {
            return "N/A";
        }
        if (typeof value === "number") {
            return value.toLocaleString();
        }
        if (value instanceof Date || !isNaN(Date.parse(value))) {
            const parsedDate = new Date(value);
            return parsedDate.toLocaleDateString();
        }
        return value;
    };

    return (
        <div className="border border-gray-300 rounded-md p-4 mb-4">
            <h2 className="text-lg font-semibold pb-2 mb-4">{title}</h2>
            {groupedContent && groupedContent.length > 0 ? (
                groupedContent.map((group, groupIndex) => (
                    <div key={groupIndex} className="mb-4 last:mb-0">
                        {group.header && (
                            <h3 className="text-md font-semibold uppercase text-gray-800 mb-4">
                                {group.header}
                            </h3>
                        )}
                        <div className="grid grid-cols-2 gap-y-4">
                            {group.items.map((item, index) => {
                                // Custom Layout for specific titles
                                if (title === "Other Engineering Affiliation, Awards & Distinctions") {
                                    if (item.label === "Record Type") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <span className="text-sm uppercase font-bold text-gray-400 block">
                                                    {item.label}
                                                </span>
                                                <span className="text-sm font-semibold block">
                                                    {renderValue(item.value)}
                                                </span>
                                            </div>
                                        );
                                    }
                                    if (item.label === "Details") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <div className="space-y-1">
                                                    <span className="text-sm uppercase font-bold text-gray-400 block">
                                                        Details
                                                    </span>
                                                    <div
                                                        className="w-full border border-gray-300 rounded-md p-2"
                                                        style={{ minHeight: "40px", overflowY: "auto" }} // Adjust minHeight as needed
                                                    >
                                                        {renderValue(item.value)}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    }
                                    // Add more custom conditions as needed...
                                }

                                if (title === "Company Nominees") {
                                    if (item.label === "Details") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <div className="space-y-1">
                                                    <span className="text-sm uppercase font-bold text-gray-400 block">
                                                        Details
                                                    </span>
                                                    <div
                                                        className="w-full border border-gray-300 rounded-md p-2"
                                                        style={{ minHeight: "40px", overflowY: "auto" }}
                                                    >
                                                        {renderValue(item.value)}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    }
                                    // Add more custom conditions as needed...
                                }
                                if (title === "Criminal Records") {
                                    if (item.label === "Declaration") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <span className="text-sm uppercase font-bold text-gray-400 block">
                                                    {item.label}
                                                </span>
                                                <span className="text-sm font-semibold block">
                                                    {renderValue(item.value)}
                                                </span>
                                            </div>
                                        );
                                    }
                                    if (item.label === "Details") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <div className="space-y-1">
                                                    <span className="text-sm uppercase font-bold text-gray-400 block">
                                                        Details
                                                    </span>
                                                    <div
                                                        className="w-full border border-gray-300 rounded-md p-2"
                                                        style={{ minHeight: "40px", overflowY: "auto" }} // Adjust minHeight as needed
                                                    >
                                                        {renderValue(item.value)}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    }
                                }
                                if (
                                    title ===
                                    "Details of Post-Graduate Design of Construction Experience"
                                ) {
                                    if (item.label === "Details") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <div className="space-y-1">
                                                    <span className="text-sm uppercase font-bold text-gray-400 block">
                                                        Details
                                                    </span>
                                                    <div
                                                        className="w-full border border-gray-300 rounded-md p-2"
                                                        style={{ minHeight: "40px", overflowY: "auto" }} // Adjust minHeight as needed
                                                    >
                                                        {renderValue(item.value)}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    }

                                    if (item.label === "employment Type") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <span className="text-sm uppercase font-bold text-gray-400 block">
                                                    {item.label}
                                                </span>
                                                <span className="text-sm font-semibold block">
                                                    {renderValue(item.value)}
                                                </span>
                                            </div>
                                        );
                                    }

                                    if (item.label === "Employer Name") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <span className="text-sm uppercase font-bold text-gray-400 block">
                                                    {item.label}
                                                </span>
                                                <span className="text-sm font-semibold block">
                                                    {renderValue(item.value)}
                                                </span>
                                            </div>
                                        );
                                    }

                                }
                                if (
                                    title ===
                                    "Working Experience"
                                ) {

                                    if (item.label === "Duties") {
                                        return (
                                            <div key={item.label} className="col-span-2">
                                                <span className="text-sm uppercase font-bold text-gray-400 block">
                                                    {item.label}
                                                </span>
                                                <span className="text-sm font-semibold block">
                                                    {renderValue(item.value)}
                                                </span>
                                            </div>
                                        );
                                    }

                                }
                                // Add more titles and their specific layouts as needed...

                                // Default rendering for all other items
                                return (
                                    <div key={index} className="flex flex-col">
                                        <span className="text-sm uppercase font-bold text-gray-400 block">
                                            {item.label}
                                        </span>
                                        <span className="text-sm font-semibold block">
                                            {renderValue(item.value)}
                                        </span>
                                    </div>
                                );
                            })}
                        </div>

                        {groupIndex < groupedContent.length - 1 && (
                            <div className="border-t border-gray-300 my-4"></div>
                        )}
                    </div>
                ))
            ) : (
                <div className="text-gray-500">
                    <p>No data is available for this section.</p>
                </div>
            )}
        </div>
    );
};

export function ApplicationDetails({ application, formSection, appointment, tabSection }: {
    application: ApplicationDetail,
    formSection: FormSection[],
    appointment: AppointmentDetail[],
    tabSection: ApplicationSectionsConfig
}) {
    const [isDownloading, setIsDownloading] = useState(false);
    const [ApplicationData, setApplicationData] = useState<ApplicationDetail>(application);
    const { toast } = useToast();

    const handleDownloadDocument = async (documentId: string) => {
        try {
            setIsDownloading(true)
            const document = ApplicationData.documents.find(doc => doc.id === documentId)
            if (!document) throw new Error("Document not found")

            await DocumentService.downloadDocument(documentId, document.path)
            toast({
                title: "Success",
                description: "Document downloaded successfully",
            })
        } catch (error) {
            console.error("Download error:", error)
            toast({
                title: "Error",
                description: "Failed to download document",
                variant: "destructive",
            })
        } finally {
            setIsDownloading(false)
        }
    }

    const handleDownloadAllDocuments = async () => {
        try {
            setIsDownloading(true)
            await DocumentService.downloadAllDocuments(ApplicationData.documents)
            toast({
                title: "Success",
                description: "All documents downloaded successfully",
            })
        } catch (error) {
            console.error("Bulk download error:", error)
            toast({
                title: "Error",
                description: "Failed to download documents",
                variant: "destructive",
            })
        } finally {
            setIsDownloading(false)
        }
    }

    return (
        <div className="p-4">
            <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-4">
                    <Avatar>
                        <AvatarImage src="/images/placeholder-avatar.png" alt={`${ApplicationData.personalInfo.full_name}`} />
                        <AvatarFallback>
                            {`${ApplicationData.personalInfo.full_name?.[0] || ''}`}
                        </AvatarFallback>
                    </Avatar>
                    <div>
                        <p className="text-lg font-semibold">
                            {" "}
                            {`${ApplicationData.personalInfo.full_name}`}
                        </p>
                        <p className="text-sm text-gray-500">
                            Application No:{" "}
                            {ApplicationData.applications.referenceNo || "N/A"}
                        </p>
                    </div>
                    <Badge
                        variant={getStatusVariant(ApplicationData.applications.status)}
                        className="mb-4 rounded-xl text-sm"
                    >
                        {ApplicationData.applications.status.charAt(0).toUpperCase() + ApplicationData.applications.status.slice(1)}
                    </Badge>
                </div>
                <ActionMenu applicationDetail={ApplicationData} onUpdate={(updatedApplication) => setApplicationData(updatedApplication)} />
            </div>

            <Tabs defaultValue="details" className="w-full">
                <TabsList className="flex justify-start space-x-4 border-b border-gray-300">
                    {Object.entries(tabSection).map(([key, isVisible]) => {
                        if (!isVisible) return null;
                        return <TabsTrigger key={key} value={key}>{key.charAt(0).toUpperCase() + key.slice(1)}</TabsTrigger>;
                    })}
                </TabsList>

                <TabsContent value="details">
                    <ApplicationSection
                        title="Application Details"
                        applicationDetail={ApplicationData}
                    />

                    {formSection.some(formSection => formSection.type === FormSectionType.PERSONAL) && (
                        <>
                            <PersonalParticularSection
                                title="Personal Particular"
                                applicationDetail={ApplicationData}
                            />
                            <ContactInfoSection
                                title="Contact Info"
                                applicationDetail={ApplicationData}
                            />
                        </>
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.COMPANY) && (
                        <CompanyParticularSection
                            title="Company Particular"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.INDUSTRY) && (
                        <IndustriesSection
                            title="Industries"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.EMPLOYMENT) && (
                        <EmploymentSection
                            title="Employment"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.EDUCATION) && (
                        <EducationSection
                            title="Education"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.WORK_EXPERIENCE) && (
                        <WorkingExperienceSection
                            title="Working Experience"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.AWARDS) && (
                        <AwardSection
                            title="Other Engineering Affiliation, Awards & Distinctions"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.SPONSORSHIP) && (
                        <SponsorshipSection
                            title="Sponsorship"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.COMPANY_NOMINEES) && (
                        <NomineeSection
                            title="Company Nominees"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.CRIMINAL_RECORD) && (
                        <CriminalRecordSection
                            title="Criminal Records"
                            applicationDetail={ApplicationData}
                        />
                    )}
                    {formSection.some(formSection => formSection.type === FormSectionType.POST_GRAD_EXPERIENCE) && (
                        <ProjectSection
                            title="Details of Post-Graduate Design of Construction Experience"
                            applicationDetail={ApplicationData}
                        />
                    )}
                </TabsContent>

                <TabsContent value="appointment">
                    <AppointmentDetails appointment={appointment} />
                </TabsContent>

                <TabsContent
                    value="documents"
                >
                    <Documents
                        documents={ApplicationData.documents}
                        onDownload={handleDownloadDocument}
                        onDownloadAll={handleDownloadAllDocuments}
                        isDownloading={isDownloading}
                    />
                </TabsContent>

            </Tabs>
        </div>
    )
}