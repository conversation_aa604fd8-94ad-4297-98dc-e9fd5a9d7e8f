import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";

export const IndustriesSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const profileData = applicationDetail.personalInfo;

    const groupedContent = [
        {
            header: '',
            items: [
                { label: 'Industries Group', value: profileData?.industries.group || '--' },
                { label: 'Industries Name', value: profileData?.industries.name || '--' },
            ].map(item => ({
                ...item,
                value: item.value !== undefined && item.value !== null ? item.value : '--'
            }))
        },
    ];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};