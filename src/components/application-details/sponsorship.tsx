import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";

export const SponsorshipSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const groupedContent = applicationDetail.sponsorship?.map((sponsorship, index) => {
        return {
            header: `Sponsorship ${index + 1}`,
            items: [
                { label: 'Type', value: sponsorship?.type || '--' },
                { label: 'Name', value: sponsorship?.name || '--' },
                { label: 'Grade', value: sponsorship?.grade || '--' },
                { label: 'Date', value: sponsorship?.date || '--' },
            ].map(item => ({
                ...item,
                value: item.value !== undefined && item.value !== null ? item.value : '--'
            }))
        };
    }) || [];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};