import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";

export const CriminalRecordSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {

  const criminalrecord = applicationDetail.criminalRecords || {}

  const groupedContent = [
    {
      header: `Criminal Records`,
      items: [
        { label: 'Declaration', value: criminalrecord?.declaration ? 'Yes' : 'No' },
        { label: 'Details', value: criminalrecord?.details || '--' },
      ].map(item => ({
        ...item,
        value: item.value !== undefined && item.value !== null ? item.value : '--'
      }))
    }
  ]

  return <SectionBox title={title} groupedContent={groupedContent} />;
};