import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";

export const EducationSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {

    const groupedContent = applicationDetail.education?.map((education, index) => {
        return {
            header: `Education ${index + 1}`,
            items: [
                { label: 'Period From', value: education?.period_from || '--' },
                { label: 'Period To', value: education?.period_to || '--' },
                { label: 'Learning Institute Attended', value: education?.other_institute ? education?.other_institute : education?.institute.name || '--' },
                { label: 'Country', value: education.institute.country || '--' },
                { label: 'Course of Study', value: education?.other_course ? education.other_course : education?.instituteCourse.name || '--' },
                { label: 'Degree, Diploma, Certificate Awarded', value: education.instituteCourse.category || '--' },
                { label: 'Date Of Graduation', value: education?.date_of_graduation || '--' },
            ].map(item => ({
                ...item,
                value: item.value !== undefined && item.value !== null ? item.value : 'N/A'
            }))
        };
    }) || [];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};