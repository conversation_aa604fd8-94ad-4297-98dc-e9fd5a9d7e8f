import { useEffect, useState } from "react";
import type { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { getOptionLabel } from "@/services/system-options.service";

export const EmploymentSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const [addressCountry, setAddressCountry] = useState('--')
    const [preferredCountry, setPreferredCountry] = useState('--')
    const employment = applicationDetail.employment;

    useEffect(() => {
        const fetchCountryLabels = async () => {
            if (employment?.company.address.country) {
                const label = await getOptionLabel(employment?.company.address.country, 'country')
                setAddressCountry(label)
            }
            if (employment?.company.preferredAddress?.country) {
                const label = await getOptionLabel(employment?.company.preferredAddress?.country, 'country')
                setPreferredCountry(label)
            }
        }

        fetchCountryLabels()
    }, [employment?.company.address, employment?.company.preferredAddress])

    const groupedContent = [
        {
            items: [
                { label: 'Company Name', value: employment?.company.name || '--' },
                { label: 'Designation', value: employment?.designation || '--' },
                { label: 'Telephone (Office)', value: employment?.company.phone || '--' },
                { label: 'Email Address', value: employment?.company.email || '--' },
                {
                    label: 'Address',
                    value: (
                        [
                            employment?.company.address.line1 || '--',
                            employment?.company.address.line2 || '--',
                            employment?.company.address.city || '--',
                            employment?.company.address.state || '--',
                            employment?.company.address.postalCode || '--',
                            addressCountry
                        ]
                            .filter(part => part)
                            .join(", ")
                    ) || '--'
                }
            ]
        },
    ];

    if (!employment?.company.isSameAddress) {
        groupedContent.push({
            items: [
                {
                    label: 'Preferred Mailing Address',
                    value: (
                        [
                            employment?.company.preferredAddress?.line1 || '--',
                            employment?.company.preferredAddress?.line2 || '--',
                            employment?.company.preferredAddress?.city || '--',
                            employment?.company.preferredAddress?.state || '--',
                            employment?.company.preferredAddress?.postalCode || '--',
                            preferredCountry
                        ]
                            .filter(part => part)
                            .join(", ")
                    ) || '--'
                }
            ]
        });
    }

    return <SectionBox title={title} groupedContent={groupedContent} />;
};
