import { useEffect, useState } from "react";
import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { getOptionLabel } from "@/services/system-options.service";

export const ContactInfoSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const [addressCountry, setAddressCountry] = useState('--')
    const profile = applicationDetail.personalInfo;

    useEffect(() => {
        const fetchCountryLabels = async () => {
            if (profile.address.country) {
                const label = await getOptionLabel(profile.address.country, 'country')
                setAddressCountry(label)
            }
        }

        fetchCountryLabels()
    }, [profile.address])

    const groupedContent = [
        {
            header: '',
            items: [
                { label: 'Email', value: profile?.email || '--' },
                { label: 'Mobile Number', value: profile?.mobile_number || '--' },
                { label: 'Phone Number', value: profile?.home_number || '--' },
                {
                    label: 'Address',
                    value: [
                        profile?.address.line1 || '--',
                        profile?.address.line2 || '--',
                        profile?.address.city || '--',
                        profile?.address.state || '--',
                        profile?.address.postalCode || '--',
                        addressCountry
                    ].filter(part => part !== undefined && part !== null).join(", ") || '--'
                }
            ].map(item => ({
                ...item,
                value: item.value !== undefined && item.value !== null ? item.value : '--'
            }))
        },
    ];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};