"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AppointmentDetail } from "@/types/applications/applications.types"
import { format } from 'date-fns';

interface AppointmentProps {
    appointment: AppointmentDetail[]
}

export function AppointmentDetails({ appointment }: AppointmentProps) {
    const sortedAppointments = appointment
        ? [...appointment].sort((a, b) => new Date(b.scheduled_at).getTime() - new Date(a.scheduled_at).getTime())
        : [];

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Appointment</CardTitle>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Type</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Location</TableHead>
                            <TableHead>Scheduled At</TableHead>
                            <TableHead>Duration (Minutes)</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedAppointments.length > 0 ? (
                            sortedAppointments.map((apm, index) => (
                                <TableRow key={`appointment-${index}`}>
                                    <TableCell>{apm.appointment_type}</TableCell>
                                    <TableCell>{apm.status}</TableCell>
                                    <TableCell>{apm.location}</TableCell>
                                    <TableCell>
                                        {format(new Date(apm.scheduled_at), 'yyyy-MM-dd HH:mm:ss')}
                                    </TableCell>
                                    <TableCell>{apm.duration_minutes.toString()}</TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={5} align="center">
                                    No appointment available.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
}