import { useEffect, useState } from "react";
import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { getOptionLabel } from "@/services/system-options.service";

export const CompanyParticularSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const [addressCountry, setAddressCountry] = useState('--')
    const [placeOfRegistration, setPlaceOfRegistration] = useState('--')
    const profile = applicationDetail.personalInfo;

    useEffect(() => {
        const fetchCountryLabels = async () => {
            if (profile.address.country) {
                const label = await getOptionLabel(profile.address.country, 'country')
                setAddressCountry(label)
            }
            if (applicationDetail.personalInfo.nationality) {
                const label = await getOptionLabel(applicationDetail.personalInfo.nationality, 'country')
                setPlaceOfRegistration(label)
            }
        }

        fetchCountryLabels()
    }, [profile.address.country, applicationDetail.personalInfo.nationality])

    const groupedContent = [
        {
            header: '',
            items: [
                { label: 'Company Name', value: profile?.full_name },
                { label: 'Telephone Number (Office)', value: profile.mobile_number },
                { label: 'Company Email', value: profile?.email },
                { label: 'Place Of Registration', value: placeOfRegistration },
                { label: 'Date Of Registration', value: profile?.date_of_birth },
                { label: 'Registration No', value: profile?.identification_no },
                {
                    label: 'Address',
                    value: [
                        profile?.address.line1 || '--',
                        profile?.address.line2 || '--',
                        profile?.address.line3 || '--',
                        profile?.address.city || '--',
                        profile?.address.state || '--',
                        profile?.address.postalCode || '--',
                        addressCountry
                    ].filter(part => part !== undefined && part !== null).join(", ") || '--'
                }
            ].map(item => ({
                ...item,
                value: item.value !== undefined && item.value !== null ? item.value : '--'
            }))
        }
    ];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};
