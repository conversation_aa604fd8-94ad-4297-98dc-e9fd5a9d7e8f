import { useEffect, useState } from "react";
import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";
import { getOptionLabel } from "@/services/system-options.service";

export const NomineeSection: React.FC<applicationDetailGeneralSectionProps> = ({ title, applicationDetail }) => {
    const [nomineeTitles, setNomineeTitles] = useState<Record<number, string>>({})

    useEffect(() => {
        const fetchTitles = async () => {
            const titles: Record<number, string> = {};

            await Promise.all(
                applicationDetail.nominees?.map(async (nominee, idx) => {
                    if (nominee.title) {
                        const label = await getOptionLabel(nominee.title, 'salutation');
                        titles[idx] = label;
                    }
                }) || []
            );

            setNomineeTitles(titles);
        };

        fetchTitles();
    }, [applicationDetail.nominees]);

    const groupedContent = applicationDetail.nominees?.map((nominees, index) => {
        const education = nominees.education;
        const award = nominees.award;

        return [
            {
                header: `Company Nominees ${index + 1}`,
                items: [
                    { label: 'Full Name', value: nominees?.full_name || '--' },
                    { label: 'Title', value: nomineeTitles[index] || '--' },
                    { label: 'Date Of Birth', value: nominees?.date_of_birth || '--' },
                    { label: 'Mobile Number', value: nominees?.phone_number || '--' },
                    { label: 'Email', value: nominees?.email || '--' },
                ].map(item => ({
                    ...item,
                    value: item.value !== undefined && item.value !== null ? item.value : '--'
                }))
            },
            {
                header: "Engineering Education",
                items: [
                    { label: 'Period From', value: education?.period_from || '--' },
                    { label: 'Period To', value: education?.period_to || '--' },
                    { label: 'Learning Institute Attended', value: education?.other_institute ? education?.other_institute : education?.institute.name || '--' },
                    { label: 'Country', value: education?.institute.country || '--' },
                    { label: 'Course of Study', value: education?.other_course ? education.other_course : education?.instituteCourse.name || '--' },
                    { label: 'Degree, Diploma, Certificate Awarded', value: education?.instituteCourse.category || '--' },
                    { label: 'Date Of Graduation', value: education?.date_of_graduation || '--' },
                ].map(item => ({
                    ...item,
                    value: item.value !== undefined && item.value !== null ? item.value : '--',
                })),
            },
            {
                header: 'Other Engineering Affiliation, Awards & Distinctions',
                items: [
                    { label: 'Record Type', value: award?.record_type || '--' },
                    { label: 'Record Number', value: award?.record_number || "--" },
                    { label: 'Date Of Election', value: award?.date_of_election || '--' },
                    { label: 'Details', value: award?.detail || '--' },
                    { label: 'Abbreviation', value: award?.abbreviation || '--' },
                    { label: 'Expire DATE', value: award?.expiry_date || '--' },
                ].map(item => ({
                    ...item,
                    value: item.value !== undefined && item.value !== null ? item.value : '--',
                })),
            },
        ];
    })?.flat() || [];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};