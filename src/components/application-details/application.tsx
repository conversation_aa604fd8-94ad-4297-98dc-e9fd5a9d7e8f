import { applicationDetailGeneralSectionProps } from "@/app/(private)/(application)/applications/[id]/interface";
import { SectionBox } from "@/components/application-details";

export const ApplicationSection: React.FC<applicationDetailGeneralSectionProps> = ({
    title,
    applicationDetail,
}) => {
    const application = applicationDetail.applications;

    const groupedContent = [
        {
            header: '',
            items: [
                { label: 'Reference No', value: application?.referenceNo || '--' },
                { label: 'Submission At', value: application?.submittedAt || '--' },
                { label: 'Status', value: application?.status || '--' },
                { label: 'Approved Or Rejected At', value: application?.approvedOrRejectedAt || '--' },
                { label: 'Type', value: application?.type || '--' },
                { label: 'Membership Type', value: application?.membershipNames || '--' },
            ].map(item => ({ ...item, value: item.value ?? '--' }))
        }
    ];

    return <SectionBox title={title} groupedContent={groupedContent} />;
};