"use client"

import { useRB<PERSON> } from "@/hooks/rbac/use-rbac"
import { SIDEBAR_MODULE_MAPPING, SIDEBAR_GROUP_MODULES } from "@/config/sidebar-permissions"
import { ACTIONS } from "@/lib/rbac-utils"
import { NavMain } from "./nav-main"
import { LucideIcon } from "lucide-react"
import { useMemo } from "react"

interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: Array<{
    title: string
    url: string
  }>
}

interface NavMainWithPermissionsProps {
  items: NavItem[]
  groupLabel?: string
}

export function NavMainWithPermissions({ items, groupLabel }: NavMainWithPermissionsProps) {
  const { getCachedPermission } = useRBAC()
  
  // Check if at least one item in the group is accessible
  const isGroupAccessible = useMemo(() => {
    if (!groupLabel || !SIDEBAR_GROUP_MODULES[groupLabel as keyof typeof SIDEBAR_GROUP_MODULES]) {
      return true // Show groups without permission requirements
    }
    
    const groupModules = SIDEBAR_GROUP_MODULES[groupLabel as keyof typeof SIDEBAR_GROUP_MODULES]
    return groupModules.some(module => getCachedPermission(module, ACTIONS.READ))
  }, [groupLabel, getCachedPermission])
  
  // Filter items based on permissions
  const filteredItems = useMemo(() => {
    return items.filter(item => {
      const requiredModule = SIDEBAR_MODULE_MAPPING[item.url as keyof typeof SIDEBAR_MODULE_MAPPING]
      
      // If no module is required (like Dashboard), show the item
      if (!requiredModule) return true
      
      // Check if user has READ permission for the module
      const hasMainPermission = getCachedPermission(requiredModule, ACTIONS.READ)
      
      // If the item has sub-items, filter them as well
      if (item.items && item.items.length > 0) {
        const accessibleSubItems = item.items.filter(subItem => {
          const subModule = SIDEBAR_MODULE_MAPPING[subItem.url as keyof typeof SIDEBAR_MODULE_MAPPING]
          return !subModule || getCachedPermission(subModule, ACTIONS.READ)
        })
        
        // Only show the parent if at least one sub-item is accessible
        if (accessibleSubItems.length > 0) {
          item.items = accessibleSubItems
          return true
        }
        
        // If no sub-items are accessible but parent is, show parent only
        return hasMainPermission
      }
      
      return hasMainPermission
    })
  }, [items, getCachedPermission])
  
  // Don't render anything if the group is not accessible
  if (!isGroupAccessible || filteredItems.length === 0) {
    return null
  }
  
  return <NavMain items={filteredItems} groupLabel={groupLabel} />
}