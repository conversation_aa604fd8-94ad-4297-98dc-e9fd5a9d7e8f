"use client"

import * as React from "react"
import {
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Home,
  FileUser,
  Users,
  Receipt,
  FileText,
  FileEdit,
  Calculator,
  CheckCircle,
  ShoppingCart,
  Mail,
  UserCheck,
  BarChart,
} from "lucide-react"
import { CalendarDays as CalendarDaysIcon } from "lucide-react"
import { IdCard } from "lucide-react"
import { NavMainWithPermissions } from "@/components/layout/nav-main-with-permissions"
import { NavUser } from "@/components/layout/nav-user"
import { TeamSwitcher } from "@/components/layout/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useUser } from "@/hooks/use-user"
import { UserService } from "@/services/user.service"

const navigationData = {
  teams: [
    {
      name: "IES System",
      logo: GalleryVerticalEnd,
      plan: "v1.0.2",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: Home,
      isActive: true,
      items: [],
    },
  ],
  navProcurement: [
    {
      title: "Purchase Orders",
      url: "/purchase-orders",
      icon: ShoppingCart,
      items: [],
    },
    {
      title: "Purchase Invoices",
      url: "/purchase-invoices",
      icon: FileText,
      items: [],
    },
  ],
  navSalesAR: [
    {
      title: "Customer Management",
      url: "/customers",
      icon: UserCheck,
      items: [],
    },
    {
      title: "Sales Invoices",
      url: "/invoices",
      icon: FileText,
      items: [
        {
          title: "Reports",
          url: "/invoices/reports",
        },
      ],
    },
    {
      title: "Customer Receipts",
      url: "/receipts",
      icon: Receipt,
      items: [
        {
          title: "Reports",
          url: "/receipts/reports",
        },
      ],
    },
    {
      title: "Customer Credit Notes",
      url: "/credit-notes",
      icon: FileEdit,
      items: [],
    },
  ],
  navFinanceControlling: [
    {
      title: "Budget Management",
      url: "/budgets",
      icon: Calculator,
      items: [{
        title: "Archived Budgets",
        url: "/budgets/archived"
      }],
    },
    {
      title: "Approvals",
      url: "/approvals",
      icon: CheckCircle,
      items: [],
    },
  ],
  navCRM: [
    {
      title: "Applications",
      url: "/applications",
      icon: FileUser,
      items: [
        {
          title: "Batch Processing",
          url: "/batch-processing",
        },
        {
          title: "Reports",
          url: "/applications/reports",
        }

      ],
    },
    {
      title: "Memberships/Registries",
      url: "/members",
      icon: IdCard,
      items: [
        {
          title: "PDU Points Submission",
          url: "#",
        },
      ],
    },
    {
      title: "Email Blast",
      url: "/email-blast",
      icon: Mail,
      items: [
        {
          title: "Campaigns",
          url: "/email-blast",
        },
        {
          title: "Recipient Lists",
          url: "/email-blast/lists",
        },
        {
          title: "Templates",
          url: "/email-blast/templates",
        },
        {
          title: "Reports",
          url: "/email-blast/reports",
        },
      ],
    },
  ],
  navProgramme: [
    {
      title: "Programmes",
      url: "/programmes",
      icon: CalendarDaysIcon,
      items: [{
        title: "Archived Programmes",
        url: "/programmes/archived"
      }],
    },
  ],
  navReports: [
    {
      title: "Reports",
      url: "/reports",
      icon: BarChart,
      items: [
        {
          title: "Attendance Reports",
          url: "/reports/attendance",
        },
      ],
    },
  ],
  navSystem: [
    {
      title: "User Management",
      url: "/user-management",
      icon: Users,
      items: [
        {
          title: "Overview",
          url: "/user-management",
        },
        {
          title: "Users",
          url: "/user-management/users",
        },
        {
          title: "Roles",
          url: "/user-management/roles",
        },
        {
          title: "Audit Logs",
          url: "/user-management/audit",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Design Engineering",
      url: "#",
      icon: Frame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: PieChart,
    },
    {
      name: "Travel",
      url: "#",
      icon: Map,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, loading } = useUser()
  const userProfile = UserService.formatUserProfile(user)

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={navigationData.teams} switchable={false} />
      </SidebarHeader>
      <SidebarContent>
        <NavMainWithPermissions items={navigationData.navMain} />
        <NavMainWithPermissions groupLabel="CRM" items={navigationData.navCRM} />
        <NavMainWithPermissions groupLabel="Programme" items={navigationData.navProgramme} />
        {/* <NavMainWithPermissions groupLabel="Reports" items={navigationData.navReports} /> */}
        <NavMainWithPermissions groupLabel="Finance/Controlling" items={navigationData.navFinanceControlling} />
        <NavMainWithPermissions groupLabel="Procurement" items={navigationData.navProcurement} />
        <NavMainWithPermissions groupLabel="Sales & Accounts Receivable" items={navigationData.navSalesAR} />
        <NavMainWithPermissions groupLabel="System" items={navigationData.navSystem} />
      </SidebarContent>
      <SidebarFooter>
        {!loading && userProfile && <NavUser user={userProfile} />}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
