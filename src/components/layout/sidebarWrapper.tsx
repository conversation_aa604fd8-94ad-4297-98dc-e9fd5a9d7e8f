"use client";

import { usePathname } from "next/navigation";
import { SidebarProvider } from "@/components/ui/sidebar";

export default function SidebarWrapper({
    children,
    defaultOpen,
}: {
    children: React.ReactNode;
    defaultOpen: boolean;
}) {
    const pathname = usePathname();

    const excludedPages = [
        "/login",
        "/first-login",
        "/new-credentials",
        "/forgot-password",
        "/auth"
    ];

    if (excludedPages.some(route => pathname.startsWith(route))) {
        return <>{children}</>;
    }

    return <SidebarProvider defaultOpen={defaultOpen}>{children}</SidebarProvider>;
}