"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  ChevronDown,
  Shield,
  Plus,
  Trash2,
  Info,
  Users,
  FileText,
  Calendar,
  DollarSign,
  Settings,
  BarChart,
  Bell,
  PiggyBank,
} from "lucide-react"
import { createClient } from "@/utils/supabase/Client"
import type { Module, ModulePermission, Permission } from "@/types/user-management.types"

interface ModulePermissionCardProps {
  module: Module
  permission?: ModulePermission
  onPermissionChange: (changes: Partial<ModulePermission>) => void
}

interface MembershipType {
  id: string
  name: string
}

const MODULE_ICONS: Record<string, any> = {
  MEMBERSHIP: Users,
  APPLICATION: FileText,
  PROGRAMME: Calendar,
  FINANCE: DollarSign,
  SYSTEM: Settings,
  REPORTS: BarChart,
  CALENDAR: Calendar,
  NOTIFICATION: Bell,
  BUDGET: PiggyBank,
}

const ACTION_LABELS: Record<keyof Permission, string> = {
  can_create: "Create",
  can_read: "Read",
  can_update: "Update",
  can_delete: "Delete",
  can_review: "Review",
  can_approve: "Approve",
  can_export: "Export",
}

const ACTION_DESCRIPTIONS: Record<keyof Permission, string> = {
  can_create: "Create new records",
  can_read: "View and read records",
  can_update: "Edit existing records",
  can_delete: "Delete records",
  can_review: "Review submissions",
  can_approve: "Approve requests",
  can_export: "Export data",
}

export function ModulePermissionCard({
  module,
  permission,
  onPermissionChange,
}: ModulePermissionCardProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [membershipTypes, setMembershipTypes] = useState<MembershipType[]>([])
  const [selectedMembershipTypes, setSelectedMembershipTypes] = useState<string[]>(
    permission?.accessible_membership_types || []
  )
  const [loadingMembershipTypes, setLoadingMembershipTypes] = useState(false)

  const Icon = MODULE_ICONS[module.module_name] || Shield
  const requiresMembershipFilter = ["MEMBERSHIP", "APPLICATION"].includes(module.module_name)

  useEffect(() => {
    if (requiresMembershipFilter && isOpen) {
      fetchMembershipTypes()
    }
  }, [requiresMembershipFilter, isOpen])

  const fetchMembershipTypes = async () => {
    try {
      setLoadingMembershipTypes(true)
      const supabase = createClient()
      const { data, error } = await supabase
        .from("membershiptype")
        .select("id, name")
        .eq("isActive", true)
        .order("name")

      if (error) {
        console.error("Error fetching membership types:", error)
        return
      }

      setMembershipTypes(data || [])
    } catch (err) {
      console.error("Error fetching membership types:", err)
    } finally {
      setLoadingMembershipTypes(false)
    }
  }

  const handleToggleAll = (enabled: boolean) => {
    const changes: Partial<ModulePermission> = {
      can_create: enabled,
      can_read: enabled,
      can_update: enabled,
      can_delete: enabled,
      can_review: enabled,
      can_approve: enabled,
      can_export: enabled,
    }
    onPermissionChange(changes)
  }

  const handlePermissionToggle = (action: keyof Permission, enabled: boolean) => {
    onPermissionChange({ [action]: enabled })
  }

  const handleMembershipTypeChange = (typeId: string) => {
    const newTypes = selectedMembershipTypes.includes(typeId)
      ? selectedMembershipTypes.filter(id => id !== typeId)
      : [...selectedMembershipTypes, typeId]
    
    setSelectedMembershipTypes(newTypes)
    onPermissionChange({ accessible_membership_types: newTypes })
  }

  const handleAddAllMembershipTypes = () => {
    const allTypeIds = membershipTypes.map(type => type.id)
    setSelectedMembershipTypes(allTypeIds)
    onPermissionChange({ accessible_membership_types: allTypeIds })
  }

  const handleRemoveAllMembershipTypes = () => {
    setSelectedMembershipTypes([])
    onPermissionChange({ accessible_membership_types: [] })
  }

  // Count active permissions
  const activePermissions = Object.entries(ACTION_LABELS).filter(
    ([key]) => permission?.[key as keyof Permission]
  ).length

  const hasAnyPermission = activePermissions > 0

  return (
    <Card className={hasAnyPermission ? "border-primary/20" : ""}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-primary/10">
                  <Icon className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-base">{module.module_name.replace(/_/g, " ")}</CardTitle>
                  <CardDescription className="text-sm">
                    {module.module_description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {hasAnyPermission && (
                  <Badge variant="secondary" className="text-xs">
                    {activePermissions} permission{activePermissions !== 1 ? "s" : ""}
                  </Badge>
                )}
                <ChevronDown className={`h-4 w-4 text-muted-foreground transition-transform ${isOpen ? "rotate-180" : ""}`} />
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="space-y-6 pt-0">
            {/* Quick Actions */}
            <div className="flex items-center justify-between pb-4 border-b">
              <Label className="text-sm font-medium">Quick Actions</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleToggleAll(true)}
                >
                  Grant All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleToggleAll(false)}
                >
                  Revoke All
                </Button>
              </div>
            </div>

            {/* Permission Toggles */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Object.entries(ACTION_LABELS).map(([action, label]) => (
                <div key={action} className="flex items-start space-x-3">
                  <Switch
                    id={`${module.module_id}-${action}`}
                    checked={permission?.[action as keyof Permission] || false}
                    onCheckedChange={(checked) => handlePermissionToggle(action as keyof Permission, checked)}
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor={`${module.module_id}-${action}`}
                      className="text-sm font-medium cursor-pointer"
                    >
                      {label}
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      {ACTION_DESCRIPTIONS[action as keyof Permission]}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Membership Type Filter (for MEMBERSHIP and APPLICATION modules only) */}
            {requiresMembershipFilter && (
              <div className="space-y-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Membership Type Access</Label>
                    <p className="text-xs text-muted-foreground">
                      Restrict access to specific membership types
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAddAllMembershipTypes}
                      disabled={loadingMembershipTypes}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRemoveAllMembershipTypes}
                      disabled={loadingMembershipTypes}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear All
                    </Button>
                  </div>
                </div>

                {loadingMembershipTypes ? (
                  <div className="text-sm text-muted-foreground">Loading membership types...</div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {membershipTypes.map((type) => (
                      <div key={type.id} className="flex items-center space-x-2">
                        <Switch
                          id={`${module.module_id}-membership-${type.id}`}
                          checked={selectedMembershipTypes.includes(type.id)}
                          onCheckedChange={() => handleMembershipTypeChange(type.id)}
                        />
                        <Label
                          htmlFor={`${module.module_id}-membership-${type.id}`}
                          className="text-sm cursor-pointer"
                        >
                          {type.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}

                {selectedMembershipTypes.length === 0 && (
                  <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                    <Info className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      No membership type restrictions. Access applies to all membership types.
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}