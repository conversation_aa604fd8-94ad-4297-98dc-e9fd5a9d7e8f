"use client"

import { useState } from "react"
import { ModulePermissionCard } from "./module-permission-card"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Search } from "lucide-react"
import type { Module, ModulePermission } from "@/types/user-management.types"

interface PermissionMatrixProps {
  modules: Module[]
  permissions: ModulePermission[]
  onPermissionChange: (moduleId: string, permission: Partial<ModulePermission>) => void
}

export function PermissionMatrix({
  modules,
  permissions,
  onPermissionChange,
}: PermissionMatrixProps) {
  const [searchTerm, setSearchTerm] = useState("")

  // Group modules by category
  const modulesByCategory = modules.reduce((acc, module) => {
    if (!acc[module.module_category]) {
      acc[module.module_category] = []
    }
    acc[module.module_category].push(module)
    return acc
  }, {} as Record<string, Module[]>)

  // Filter modules based on search
  const filteredModulesByCategory = Object.entries(modulesByCategory).reduce((acc, [category, categoryModules]) => {
    const filteredModules = categoryModules.filter(module => {
      const searchLower = searchTerm.toLowerCase()
      return (
        module.module_name.toLowerCase().includes(searchLower) ||
        module.module_description?.toLowerCase().includes(searchLower) ||
        category.toLowerCase().includes(searchLower)
      )
    })
    
    if (filteredModules.length > 0) {
      acc[category] = filteredModules
    }
    
    return acc
  }, {} as Record<string, Module[]>)

  const getCategoryTitle = (category: string) => {
    const titles: Record<string, string> = {
      CORE: "Core Modules",
      ADMIN: "Administration",
      ANALYTICS: "Analytics & Reporting",
      FINANCE: "Financial Management",
      GENERAL: "General",
    }
    return titles[category] || category
  }

  const getCategoryDescription = (category: string) => {
    const descriptions: Record<string, string> = {
      CORE: "Essential system modules for daily operations",
      ADMIN: "System administration and configuration",
      ANALYTICS: "Reporting and data analysis tools",
      FINANCE: "Financial tracking and management",
      GENERAL: "General purpose modules",
    }
    return descriptions[category] || ""
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <Card>
        <CardHeader>
          <CardTitle>Module Permissions</CardTitle>
          <CardDescription>
            Configure permissions for each module. Use the toggle switches to grant or revoke permissions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search modules..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Permission Matrix by Category */}
      {Object.entries(filteredModulesByCategory).map(([category, categoryModules]) => (
        <div key={category} className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">{getCategoryTitle(category)}</h3>
            <p className="text-sm text-muted-foreground">{getCategoryDescription(category)}</p>
          </div>
          
          <div className="grid gap-4">
            {categoryModules.map((module) => {
              const permission = permissions.find(p => p.module_id === module.module_id)
              
              return (
                <ModulePermissionCard
                  key={module.module_id}
                  module={module}
                  permission={permission}
                  onPermissionChange={(changes) => onPermissionChange(module.module_id, changes)}
                />
              )
            })}
          </div>
        </div>
      ))}

      {/* No Results */}
      {Object.keys(filteredModulesByCategory).length === 0 && searchTerm && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">
              No modules found matching "{searchTerm}"
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}