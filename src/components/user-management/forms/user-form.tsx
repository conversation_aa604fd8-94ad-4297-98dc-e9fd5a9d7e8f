"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { UserManagementClientService } from "@/services/user-management/user-management-client.service";
import type { UserWithRole, Role } from "@/types/user-management.types";
import { Loader2, AlertCircle } from "lucide-react";

// Zod schema for user form validation
const userFormSchema = z.object({
  email: z.string().email("Invalid email address").min(1, "Email is required"),
  full_name: z.string().min(2, "Full name must be at least 2 characters"),
  phone_number: z.string().optional(),
  department: z.string().optional(),
  status: z.boolean().default(true),
  roles: z.array(z.string()).default([]),
  password: z.string().min(8, "Password must be at least 8 characters").optional(),
  confirmPassword: z.string().optional(),
}).refine((data) => {
  // Only validate password match if password is provided
  if (data.password && data.confirmPassword) {
    return data.password === data.confirmPassword;
  }
  return true;
}, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
}).refine((data) => {
  // Require password for new users (create mode)
  if (!data.password && isCreateMode) {
    return false;
  }
  return true;
}, {
  message: "Password is required for new users",
  path: ["password"],
});

type UserFormData = z.infer<typeof userFormSchema>;

interface UserFormProps {
  user?: UserWithRole;
  onSubmit: (data: UserFormData) => Promise<void>;
  onCancel: () => void;
  mode: "create" | "edit";
}

// This will be set properly in the component
let isCreateMode = false;

export function UserForm({ user, onSubmit, onCancel, mode }: UserFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loadingRoles, setLoadingRoles] = useState(true);

  // Set the global flag for validation
  isCreateMode = mode === "create";

  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      email: user?.email || "",
      full_name: user?.full_name || "",
      phone_number: user?.phone_number || "",
      department: "",
      status: user?.status ?? true,
      roles: [],
      password: "",
      confirmPassword: "",
    },
  });

  // Set the initial roles after form initialization to avoid infinite loops
  useEffect(() => {
    if (user?.role_name && mode === "edit") {
      form.setValue("roles", [user.role_name]);
    }
  }, [user?.role_name, mode, form]);

  // Load available roles
  useEffect(() => {
    loadRoles();
  }, []);

  const loadRoles = async () => {
    setLoadingRoles(true);
    try {
      const result = await UserManagementClientService.getRoles();
      if (result.success && result.data) {
        setRoles(result.data.filter(role => role.is_active));
      } else {
        setError("Failed to load roles");
      }
    } catch (err) {
      setError("Failed to load roles");
    } finally {
      setLoadingRoles(false);
    }
  };

  const handleSubmit = async (data: UserFormData) => {
    setLoading(true);
    setError(null);
    try {
      await onSubmit(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const roleOptions = roles.map(role => ({
    value: role.name,
    label: role.name.replace(/_/g, " "),
  }));

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                    disabled={mode === "edit"}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="John Doe" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="phone_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="+65 1234 5678" />
                </FormControl>
                <FormDescription>Optional contact number</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="department"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Department</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="e.g., Finance, HR" />
                </FormControl>
                <FormDescription>Optional department name</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {mode === "create" && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" placeholder="••••••••" />
                  </FormControl>
                  <FormDescription>Minimum 8 characters</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" placeholder="••••••••" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <FormField
          control={form.control}
          name="roles"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Roles</FormLabel>
              <FormControl>
                {loadingRoles ? (
                  <div className="flex items-center justify-center p-2 border rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <MultiSelect
                    options={roleOptions}
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Select roles"
                    maxCount={3}
                  />
                )}
              </FormControl>
              <FormDescription>
                Assign one or more roles to this user
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Active Status</FormLabel>
                <FormDescription>
                  Inactive users cannot log in to the system
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {mode === "create" ? "Create User" : "Update User"}
          </Button>
        </div>
      </form>
    </Form>
  );
}