"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { UserForm } from "../forms/user-form";
import { createClient } from "@/utils/supabase/Client";
import type { UserWithRole, UpdateUserRequest, AssignRoleRequest } from "@/types/user-management.types";
import { Loader2 } from "lucide-react";

interface EditUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
  onSuccess?: () => void;
}

export function EditUserDialog({
  open,
  onOpenChange,
  userId,
  onSuccess,
}: EditUserDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<UserWithRole | null>(null);

  useEffect(() => {
    if (open && userId) {
      loadUser();
    }
  }, [open, userId]);

  const loadUser = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      
      // Get user with role using the RPC function
      const { data, error } = await supabase.rpc('get_users_with_roles');
      
      if (error) {
        throw new Error(error.message);
      }

      // Find the specific user
      const userData = data?.find((u: UserWithRole) => u.id === userId);
      
      if (userData) {
        setUser(userData);
      } else {
        throw new Error("User not found");
      }
    } catch (error) {
      console.error("Error loading user:", error);
      toast({
        title: "Error loading user",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async (data: any) => {
    if (!user) return;

    setLoading(true);
    try {
      const supabase = createClient();
      
      // Update user metadata
      const updateRequest: UpdateUserRequest = {
        full_name: data.full_name,
        phone_number: data.phone_number,
        status: data.status,
        metadata: {
          department: data.department,
        },
      };

      // Update user
      const { error: updateError } = await supabase.rpc('update_user', {
        user_id: userId,
        updates: updateRequest,
      });

      if (updateError) {
        throw new Error(updateError.message);
      }

      // Update role if changed
      if (data.roles[0] !== user.role_name) {
        const assignRoleRequest: AssignRoleRequest = {
          user_id: userId,
          role_name: data.roles[0],
          reason: "Role updated via user edit",
        };

        const { error: roleError } = await supabase.rpc('assign_role_to_user', assignRoleRequest);

        if (roleError) {
          throw new Error(roleError.message);
        }
      }

      toast({
        title: "User updated successfully",
        description: `${data.full_name}'s information has been updated.`,
      });

      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error updating user:", error);
      toast({
        title: "Error updating user",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (loading && !user) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update user information and role assignments.
          </DialogDescription>
        </DialogHeader>
        
        {user && (
          <UserForm
            mode="edit"
            user={user}
            onSubmit={handleUpdateUser}
            onCancel={handleCancel}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}