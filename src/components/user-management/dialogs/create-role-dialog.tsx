"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogDescription,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { RoleForm } from "../forms/role-form";
import { createClient } from "@/utils/supabase/Client";
import type { CreateRoleRequest } from "@/types/user-management.types";

interface CreateRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateRoleDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateRoleDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleCreateRole = async (data: any) => {
    setLoading(true);
    try {
      const supabase = createClient();
      
      // Prepare the role request
      const createRoleRequest: CreateRoleRequest = {
        name: data.name,
        role_description: data.role_description,
      };

      // Insert role directly since we don't have a specific RPC function for this
      const { data: result, error } = await supabase
        .schema('access_control')
        .from('roles')
        .insert({
          name: createRoleRequest.name,
          role_description: createRoleRequest.role_description,
          is_active: data.is_active,
        })
        .select()
        .single();

      if (error) {
        // Check for duplicate role name
        if (error.code === '23505') {
          throw new Error("A role with this name already exists");
        }
        throw new Error(error.message);
      }

      toast({
        title: "Role created successfully",
        description: `${data.name} role has been created.`,
      });

      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error creating role:", error);
      toast({
        title: "Error creating role",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Create New Role</DialogTitle>
          <DialogDescription>
            Define a new role with specific permissions for users in your system.
          </DialogDescription>
        </DialogHeader>
        
        <RoleForm
          mode="create"
          onSubmit={handleCreateRole}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}