"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { UserForm } from "../forms/user-form";
import { createClient } from "@/utils/supabase/Client";
import type { CreateUserRequest } from "@/types/user-management.types";

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateUserDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateUserDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleCreateUser = async (data: any) => {
    setLoading(true);
    try {
      const supabase = createClient();
      
      // Prepare the user request
      const createUserRequest: CreateUserRequest = {
        email: data.email,
        full_name: data.full_name,
        password: data.password,
        phone_number: data.phone_number,
        role_name: data.roles[0], // For now, we'll use the first role
        send_invitation: false,
      };

      // Call the RPC function to create user
      const { data: result, error } = await supabase.rpc('create_admin_user', createUserRequest);

      if (error) {
        throw new Error(error.message);
      }

      toast({
        title: "User created successfully",
        description: `${data.full_name} has been added to the system.`,
      });

      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error creating user:", error);
      toast({
        title: "Error creating user",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New User</DialogTitle>
          <DialogDescription>
            Add a new user to the system. They will receive an email invitation to set up their account.
          </DialogDescription>
        </DialogHeader>
        
        <UserForm
          mode="create"
          onSubmit={handleCreateUser}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}