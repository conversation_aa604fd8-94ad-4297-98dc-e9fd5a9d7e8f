"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { RoleForm } from "../forms/role-form";
import { createClient } from "@/utils/supabase/Client";
import type { Role } from "@/types/user-management.types";
import { Loader2 } from "lucide-react";

interface EditRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  roleId: string;
  onSuccess?: () => void;
}

export function EditRoleDialog({
  open,
  onOpenChange,
  roleId,
  onSuccess,
}: EditRoleDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState<Role | null>(null);

  useEffect(() => {
    if (open && roleId) {
      loadRole();
    }
  }, [open, roleId]);

  const loadRole = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      
      // Get role by ID
      const { data, error } = await supabase
        .schema('access_control')
        .from('roles')
        .select('*')
        .eq('id', roleId)
        .single();
      
      if (error) {
        throw new Error(error.message);
      }

      if (data) {
        setRole(data);
      } else {
        throw new Error("Role not found");
      }
    } catch (error) {
      console.error("Error loading role:", error);
      toast({
        title: "Error loading role",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateRole = async (data: any) => {
    if (!role) return;

    setLoading(true);
    try {
      const supabase = createClient();
      
      // Update role (only description and status can be updated)
      const { error } = await supabase
        .schema('access_control')
        .from('roles')
        .update({
          role_description: data.role_description,
          is_active: data.is_active,
          updated_at: new Date().toISOString(),
        })
        .eq('id', roleId);

      if (error) {
        throw new Error(error.message);
      }

      toast({
        title: "Role updated successfully",
        description: `${role.name} has been updated.`,
      });

      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        title: "Error updating role",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (loading && !role) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-xl">
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Edit Role</DialogTitle>
          <DialogDescription>
            Update role description and status. Role names cannot be changed.
          </DialogDescription>
        </DialogHeader>
        
        {role && (
          <RoleForm
            mode="edit"
            role={role}
            onSubmit={handleUpdateRole}
            onCancel={handleCancel}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}