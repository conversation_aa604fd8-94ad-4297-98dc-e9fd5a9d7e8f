"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON>Footer, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { approveInvoice } from "@/services/purchase-invoices/purchase-invoices.service";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

interface ApproveInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: PurchaseInvoice | null;
  onSuccess?: () => void;
}

export function ApproveInvoiceDialog({
  open,
  onOpenChange,
  invoice,
  onSuccess
}: ApproveInvoiceDialogProps) {
  const [comments, setComments] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleApprove = async () => {
    if (!invoice) return;

    try {
      setIsLoading(true);
      
      const result = await approveInvoice(invoice.id, comments || undefined);
      
      toast({
        title: "Invoice Approved",
        description: `Invoice ${invoice.invoice_number} has been approved successfully.`,
        variant: "default",
      });

      // Reset form
      setComments("");
      
      // Close dialog and trigger refresh
      onOpenChange(false);
      onSuccess?.();
      
    } catch (error) {
      console.error("Error approving invoice:", error);
      toast({
        title: "Approval Failed",
        description: error instanceof Error ? error.message : "Failed to approve invoice. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setComments("");
    onOpenChange(false);
  };

  if (!invoice) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Approve Purchase Invoice
          </DialogTitle>
          <DialogDescription>
            You are about to approve invoice <strong>{invoice.invoice_number}</strong> for{" "}
            <strong>{invoice.supplier?.name}</strong> with amount{" "}
            <strong>
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: invoice.currency_code || 'USD',
              }).format(invoice.total_amount)}
            </strong>.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Invoice Summary */}
          <div className="p-4 bg-muted/50 rounded-lg space-y-2">
            <h4 className="font-medium">Invoice Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Invoice Number:</span>
                <p className="font-medium">{invoice.invoice_number}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Supplier:</span>
                <p className="font-medium">{invoice.supplier?.name}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Amount:</span>
                <p className="font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: invoice.currency_code || 'USD',
                  }).format(invoice.total_amount)}
                </p>
              </div>
              <div>
                <span className="text-muted-foreground">Invoice Date:</span>
                <p className="font-medium">
                  {new Date(invoice.invoice_date).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Approval Comments */}
          <div className="space-y-2">
            <Label htmlFor="comments">Approval Comments (Optional)</Label>
            <Textarea
              id="comments"
              placeholder="Add any comments about this approval..."
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              These comments will be recorded in the approval history.
            </p>
          </div>

          {/* Warning */}
          <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800">Please verify before approving:</p>
              <ul className="text-yellow-700 mt-1 space-y-1">
                <li>• Invoice details are accurate and complete</li>
                <li>• Goods/services have been received satisfactorily</li>
                <li>• Amount matches purchase order (if applicable)</li>
                <li>• Budget allocation is available</li>
              </ul>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleApprove}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Approving...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve Invoice
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}