"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Send, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { submitInvoiceForApproval } from "@/services/purchase-invoices/purchase-invoices.service";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

interface SubmitForApprovalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: PurchaseInvoice | null;
  onSuccess?: () => void;
}

export function SubmitForApprovalDialog({
  open,
  onOpenChange,
  invoice,
  onSuccess
}: SubmitForApprovalDialogProps) {
  const [comments, setComments] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!invoice) return;

    try {
      setIsLoading(true);
      
      const result = await submitInvoiceForApproval(invoice.id, comments || undefined);
      
      toast({
        title: "Submitted for Approval",
        description: `Invoice ${invoice.invoice_number} has been submitted for approval.`,
        variant: "default",
      });

      // Reset form
      setComments("");
      
      // Close dialog and trigger refresh
      onOpenChange(false);
      onSuccess?.();
      
    } catch (error) {
      console.error("Error submitting invoice for approval:", error);
      toast({
        title: "Submission Failed",
        description: error instanceof Error ? error.message : "Failed to submit invoice for approval. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setComments("");
    onOpenChange(false);
  };

  if (!invoice) return null;

  // Check for validation issues
  const validationIssues = [];
  if (!invoice.supplier_id) validationIssues.push("Supplier is required");
  if (!invoice.entity_id) validationIssues.push("Entity is required");
  if (!invoice.items || invoice.items.length === 0) validationIssues.push("At least one line item is required");
  if (invoice.total_amount <= 0) validationIssues.push("Invoice amount must be greater than zero");

  const hasValidationIssues = validationIssues.length > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5 text-blue-600" />
            Submit for Approval
          </DialogTitle>
          <DialogDescription>
            Submit invoice <strong>{invoice.invoice_number}</strong> for{" "}
            <strong>{invoice.supplier?.name}</strong> to the approval workflow.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Invoice Summary */}
          <div className="p-4 bg-muted/50 rounded-lg space-y-2">
            <h4 className="font-medium">Invoice Summary</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Invoice Number:</span>
                <p className="font-medium">{invoice.invoice_number}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Supplier:</span>
                <p className="font-medium">{invoice.supplier?.name}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Amount:</span>
                <p className="font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: invoice.currency_code || 'USD',
                  }).format(invoice.total_amount)}
                </p>
              </div>
              <div>
                <span className="text-muted-foreground">Line Items:</span>
                <p className="font-medium">{invoice.items?.length || 0}</p>
              </div>
            </div>
          </div>

          {/* Validation Issues */}
          {hasValidationIssues && (
            <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-red-800">Please fix these issues before submitting:</p>
                <ul className="text-red-700 mt-1 space-y-1">
                  {validationIssues.map((issue, index) => (
                    <li key={index}>• {issue}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Submission Comments */}
          <div className="space-y-2">
            <Label htmlFor="comments">Submission Comments (Optional)</Label>
            <Textarea
              id="comments"
              placeholder="Add any notes for the approver..."
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              These comments will be visible to approvers in the approval workflow.
            </p>
          </div>

          {/* Submission Checklist */}
          {!hasValidationIssues && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Submission Checklist:</Label>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Invoice details are complete and accurate</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>All line items have been reviewed</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Supporting documents are attached (if required)</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>GL accounts and cost centers are correctly assigned</span>
                </div>
              </div>
            </div>
          )}

          {/* Process Information */}
          {!hasValidationIssues && (
            <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800">What happens next:</p>
                <ul className="text-blue-700 mt-1 space-y-1">
                  <li>• Invoice status will change to "Pending Approval"</li>
                  <li>• Appropriate approvers will be notified</li>
                  <li>• You'll receive notifications on approval status changes</li>
                  <li>• Invoice cannot be edited while in approval workflow</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isLoading || hasValidationIssues}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Submitting...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Submit for Approval
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}