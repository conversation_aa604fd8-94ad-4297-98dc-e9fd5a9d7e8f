"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { XCircle, AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { rejectInvoice } from "@/services/purchase-invoices/purchase-invoices.service";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

interface RejectInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: PurchaseInvoice | null;
  onSuccess?: () => void;
}

export function RejectInvoiceDialog({
  open,
  onOpenChange,
  invoice,
  onSuccess
}: RejectInvoiceDialogProps) {
  const [reason, setReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleReject = async () => {
    if (!invoice) return;

    if (!reason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for rejecting this invoice.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      const result = await rejectInvoice(invoice.id, reason);
      
      toast({
        title: "Invoice Rejected",
        description: `Invoice ${invoice.invoice_number} has been rejected and returned for revision.`,
        variant: "default",
      });

      // Reset form
      setReason("");
      
      // Close dialog and trigger refresh
      onOpenChange(false);
      onSuccess?.();
      
    } catch (error) {
      console.error("Error rejecting invoice:", error);
      toast({
        title: "Rejection Failed",
        description: error instanceof Error ? error.message : "Failed to reject invoice. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setReason("");
    onOpenChange(false);
  };

  if (!invoice) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <XCircle className="h-5 w-5 text-red-600" />
            Reject Purchase Invoice
          </DialogTitle>
          <DialogDescription>
            You are about to reject invoice <strong>{invoice.invoice_number}</strong> for{" "}
            <strong>{invoice.supplier?.name}</strong>. This will return it to the submitter for revision.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Invoice Summary */}
          <div className="p-4 bg-muted/50 rounded-lg space-y-2">
            <h4 className="font-medium">Invoice Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Invoice Number:</span>
                <p className="font-medium">{invoice.invoice_number}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Supplier:</span>
                <p className="font-medium">{invoice.supplier?.name}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Amount:</span>
                <p className="font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: invoice.currency_code || 'USD',
                  }).format(invoice.total_amount)}
                </p>
              </div>
              <div>
                <span className="text-muted-foreground">Invoice Date:</span>
                <p className="font-medium">
                  {new Date(invoice.invoice_date).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Rejection Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">Reason for Rejection <span className="text-red-500">*</span></Label>
            <Textarea
              id="reason"
              placeholder="Please provide a detailed reason for rejecting this invoice..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={4}
              required
            />
            <p className="text-xs text-muted-foreground">
              This reason will be sent to the submitter and recorded in the approval history.
            </p>
          </div>

          {/* Common Rejection Reasons */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Common reasons (click to use):</Label>
            <div className="flex flex-wrap gap-2">
              {[
                "Incomplete supporting documentation",
                "Invoice amount doesn't match PO",
                "Goods/services not yet received",
                "Budget not available",
                "Incorrect GL account coding",
                "Duplicate invoice detected",
                "Pricing discrepancy"
              ].map((commonReason) => (
                <Button
                  key={commonReason}
                  variant="outline"
                  size="sm"
                  type="button"
                  onClick={() => setReason(commonReason)}
                  className="text-xs"
                >
                  {commonReason}
                </Button>
              ))}
            </div>
          </div>

          {/* Warning */}
          <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-red-800">This action will:</p>
              <ul className="text-red-700 mt-1 space-y-1">
                <li>• Change invoice status to "Returned"</li>
                <li>• Send notification to the submitter</li>
                <li>• Record rejection in approval history</li>
                <li>• Allow invoice to be edited and resubmitted</li>
              </ul>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleReject}
            disabled={isLoading || !reason.trim()}
            variant="destructive"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Rejecting...
              </>
            ) : (
              <>
                <XCircle className="mr-2 h-4 w-4" />
                Reject Invoice
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}