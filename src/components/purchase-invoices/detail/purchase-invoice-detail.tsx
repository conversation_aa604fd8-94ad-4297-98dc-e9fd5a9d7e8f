"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { PurchaseInvoiceStatusBadge } from "@/components/purchase-invoices/ui/purchase-invoice-status-badge";
import { PaymentStatusBadge } from "@/components/purchase-invoices/ui/payment-status-badge";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { 
  Edit, 
  Printer, 
  Mail, 
  DollarSign, 
  History, 
  Copy, 
  X, 
  FileDown,
  CalendarDays,
  Building2,
  User,
  Clock,
  FileText,
  Upload,
  Download,
  Eye,
  AlertCircle,
  CheckCircle,
  XCircle,
  Package,
  CreditCard,
  TrendingUp,
  Receipt,
  FileCheck,
  GitMerge,
  Send
} from "lucide-react";
import type { PurchaseInvoice, ApprovalHistoryItem } from "@/types/purchase-invoices/purchase-invoices.types";
import { format } from "date-fns";
import { getApprovalHistory } from "@/services/purchase-invoices/purchase-invoices.service";
import { ApproveInvoiceDialog } from "@/components/purchase-invoices/dialogs/approve-invoice-dialog";
import { RejectInvoiceDialog } from "@/components/purchase-invoices/dialogs/reject-invoice-dialog";
import { SubmitForApprovalDialog } from "@/components/purchase-invoices/dialogs/submit-for-approval-dialog";
import { useToast } from "@/hooks/use-toast";

interface PurchaseInvoiceDetailProps {
  purchaseInvoice: PurchaseInvoice;
  onEdit?: () => void;
  onPrint?: () => void;
  onEmail?: () => void;
  onPay?: () => void;
  onCancel?: () => void;
  onClone?: () => void;
  onMatchPO?: () => void;
  onRefresh?: () => void;
}

export function PurchaseInvoiceDetail({ 
  purchaseInvoice,
  onEdit,
  onPrint,
  onEmail,
  onPay,
  onCancel,
  onClone,
  onMatchPO,
  onRefresh
}: PurchaseInvoiceDetailProps) {
  const [activeTab, setActiveTab] = useState("overview");
  const [approvalHistory, setApprovalHistory] = useState<ApprovalHistoryItem[]>([]);
  const [isLoadingApprovalHistory, setIsLoadingApprovalHistory] = useState(false);
  
  // Dialog states
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  
  const { toast } = useToast();

  const canEdit = ["SUBMITTED", "RECEIVED", "RETURNED"].includes(purchaseInvoice.status);
  const canSubmitForApproval = ["RECEIVED", "RETURNED"].includes(purchaseInvoice.status);
  const canApprove = purchaseInvoice.status === "PENDING_APPROVAL";
  const canPay = purchaseInvoice.status === "APPROVED" && purchaseInvoice.payment_status !== "COMPLETED";
  const canCancel = !["CANCELLED", "PAID"].includes(purchaseInvoice.status);
  const canMatchPO = purchaseInvoice.purchase_order_id && !purchaseInvoice.three_way_match;

  // Load approval history
  useEffect(() => {
    if (activeTab === "approval") {
      loadApprovalHistory();
    }
  }, [activeTab, purchaseInvoice.id]);

  const loadApprovalHistory = async () => {
    try {
      setIsLoadingApprovalHistory(true);
      const history = await getApprovalHistory(purchaseInvoice.id);
      setApprovalHistory(history);
    } catch (error) {
      console.error("Error loading approval history:", error);
      toast({
        title: "Error",
        description: "Failed to load approval history",
        variant: "destructive",
      });
    } finally {
      setIsLoadingApprovalHistory(false);
    }
  };

  const handleDialogSuccess = () => {
    // Refresh the parent component and reload approval history
    onRefresh?.();
    loadApprovalHistory();
  };

  const mockDocuments = [
    {
      id: "1",
      file_name: "Invoice_" + purchaseInvoice.invoice_number + ".pdf",
      file_type: "application/pdf",
      file_size: 245760,
      file_url: "/mock-document.pdf",
      uploaded_at: "2024-01-15T10:30:00Z",
      uploaded_by: "John Smith"
    }
  ];

  const mockThreeWayMatch = {
    invoice_id: purchaseInvoice.id,
    po_id: purchaseInvoice.purchase_order_id,
    gr_id: "gr-001",
    match_status: "MATCHED" as const,
    total_variance: 0,
    quantity_variance: 0,
    price_variance: 0,
    tax_variance: 0,
    tolerance_exceeded: false,
    requires_approval: false,
    match_date: "2024-01-15T14:30:00Z",
    matched_by: "System",
    notes: "Automatic three-way match completed successfully",
    line_matches: purchaseInvoice.items?.map(item => ({
      line_number: item.line_number,
      description: item.description,
      invoice_quantity: item.quantity,
      invoice_price: item.unit_price,
      po_quantity: item.po_quantity || item.quantity,
      po_price: item.unit_price,
      received_quantity: item.received_quantity || item.quantity,
      quantity_variance: 0,
      price_variance: 0,
      total_variance: 0,
      match_status: "MATCHED" as const,
      tolerance_status: "WITHIN" as const
    })) || []
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: purchaseInvoice.currency_code || 'USD',
    }).format(amount);
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'BANK_TRANSFER': return 'Bank Transfer';
      case 'CHEQUE': return 'Cheque';
      case 'CASH': return 'Cash';
      case 'ONLINE': return 'Online Payment';
      case 'CREDIT_CARD': return 'Credit Card';
      default: return method;
    }
  };

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'MATCHED': return 'text-green-600';
      case 'PARTIALLY_MATCHED': return 'text-yellow-600';
      case 'NOT_MATCHED': return 'text-red-600';
      case 'DISPUTED': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  const getMatchStatusIcon = (status: string) => {
    switch (status) {
      case 'MATCHED': return <CheckCircle className="h-4 w-4" />;
      case 'PARTIALLY_MATCHED': return <AlertCircle className="h-4 w-4" />;
      case 'NOT_MATCHED': return <XCircle className="h-4 w-4" />;
      case 'DISPUTED': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-4">
                <h2 className="text-2xl font-bold">{purchaseInvoice.invoice_number}</h2>
                <PurchaseInvoiceStatusBadge status={purchaseInvoice.status} />
                <PaymentStatusBadge status={purchaseInvoice.payment_status} />
                {purchaseInvoice.days_overdue && purchaseInvoice.days_overdue > 0 && (
                  <Badge variant="destructive" className="gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {purchaseInvoice.days_overdue} days overdue
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">
                Purchase Invoice • {purchaseInvoice.supplier?.name}
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {canEdit && (
                <Button variant="outline" size="sm" onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              )}

              {canSubmitForApproval && (
                <Button variant="outline" size="sm" onClick={() => setShowSubmitDialog(true)}>
                  <Send className="mr-2 h-4 w-4" />
                  Submit for Approval
                </Button>
              )}
              
              <Button variant="outline" size="sm" onClick={onPrint}>
                <Printer className="mr-2 h-4 w-4" />
                Print
              </Button>
              
              <Button variant="outline" size="sm" onClick={onEmail}>
                <Mail className="mr-2 h-4 w-4" />
                Email
              </Button>
              
              {canApprove && (
                <Button variant="outline" size="sm" onClick={() => setShowApproveDialog(true)}>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </Button>
              )}

              {canApprove && (
                <Button variant="outline" size="sm" onClick={() => setShowRejectDialog(true)}>
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              )}
              
              {canPay && (
                <Button variant="outline" size="sm" onClick={onPay}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Pay
                </Button>
              )}

              {canMatchPO && (
                <Button variant="outline" size="sm" onClick={onMatchPO}>
                  <GitMerge className="mr-2 h-4 w-4" />
                  Match PO
                </Button>
              )}
              
              <Button variant="outline" size="sm" onClick={onClone}>
                <Copy className="mr-2 h-4 w-4" />
                Clone
              </Button>
              
              {canCancel && (
                <Button variant="outline" size="sm" onClick={onCancel}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Summary Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center gap-3">
              <Building2 className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Supplier</p>
                <p className="font-medium">
                  {purchaseInvoice.supplier?.display_name || purchaseInvoice.supplier?.name || '-'}
                </p>
                <p className="text-sm text-muted-foreground">
                  {purchaseInvoice.supplier?.supplier_code || '-'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Invoice Amount</p>
                <p className="font-medium text-lg">
                  <CurrencyDisplay amount={purchaseInvoice.total_amount} currency={purchaseInvoice.currency_code} />
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <CalendarDays className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Invoice Date</p>
                <p className="font-medium">
                  {format(new Date(purchaseInvoice.invoice_date), 'dd MMM yyyy')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Receipt className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Outstanding Amount</p>
                <p className={`font-medium ${(purchaseInvoice.outstanding_amount || 0) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  <CurrencyDisplay amount={purchaseInvoice.outstanding_amount || 0} currency={purchaseInvoice.currency_code} />
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Section */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="approval">Approval</TabsTrigger>
          <TabsTrigger value="three-way-match">Three-Way Match</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Purchase Invoice Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h4 className="font-semibold mb-4">Basic Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Invoice Number</label>
                      <p className="font-medium">{purchaseInvoice.invoice_number}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <div className="pt-1">
                        <PurchaseInvoiceStatusBadge status={purchaseInvoice.status} />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Invoice Date</label>
                      <p className="font-medium">{format(new Date(purchaseInvoice.invoice_date), 'dd MMM yyyy')}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Currency</label>
                      <p className="font-medium">{purchaseInvoice.currency_code}</p>
                    </div>
                    {purchaseInvoice.reference_number && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Reference Number</label>
                        <p className="font-medium">{purchaseInvoice.reference_number}</p>
                      </div>
                    )}
                    {purchaseInvoice.supplier_reference && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Supplier Reference</label>
                        <p className="font-medium">{purchaseInvoice.supplier_reference}</p>
                      </div>
                    )}
                    {purchaseInvoice.due_date && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                        <p className={`font-medium ${purchaseInvoice.days_overdue && purchaseInvoice.days_overdue > 0 ? 'text-red-600' : ''}`}>
                          {format(new Date(purchaseInvoice.due_date), 'dd MMM yyyy')}
                          {purchaseInvoice.days_overdue && purchaseInvoice.days_overdue > 0 && (
                            <span className="ml-2 text-red-500 text-sm">({purchaseInvoice.days_overdue} days overdue)</span>
                          )}
                        </p>
                      </div>
                    )}
                    {purchaseInvoice.received_date && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Received Date</label>
                        <p className="font-medium">{format(new Date(purchaseInvoice.received_date), 'dd MMM yyyy')}</p>
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Supplier Information */}
                <div>
                  <h4 className="font-semibold mb-4">Supplier Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Supplier Name</label>
                      <p className="font-medium">{purchaseInvoice.supplier?.name || '-'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Supplier Code</label>
                      <p className="font-medium">{purchaseInvoice.supplier?.supplier_code || '-'}</p>
                    </div>
                    {purchaseInvoice.supplier?.email && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Email</label>
                        <p className="font-medium">{purchaseInvoice.supplier.email}</p>
                      </div>
                    )}
                    {purchaseInvoice.supplier?.phone && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Phone</label>
                        <p className="font-medium">{purchaseInvoice.supplier.phone}</p>
                      </div>
                    )}
                    {purchaseInvoice.supplier?.payment_terms && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Payment Terms</label>
                        <p className="font-medium">{purchaseInvoice.supplier.payment_terms} days</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Purchase Order Information */}
                {purchaseInvoice.purchase_order && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-semibold mb-4">Purchase Order Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">PO Number</label>
                          <p className="font-medium">{purchaseInvoice.purchase_order.po_number}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">PO Date</label>
                          <p className="font-medium">{format(new Date(purchaseInvoice.purchase_order.po_date), 'dd MMM yyyy')}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">PO Amount</label>
                          <p className="font-medium">
                            <CurrencyDisplay amount={purchaseInvoice.purchase_order.total_amount} currency={purchaseInvoice.currency_code} />
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">PO Status</label>
                          <p className="font-medium">{purchaseInvoice.purchase_order.status}</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {purchaseInvoice.notes && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Notes</label>
                      <p className="font-medium mt-1">{purchaseInvoice.notes}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Line Items */}
            <Card>
              <CardHeader>
                <CardTitle>Line Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Line #</TableHead>
                        <TableHead>Item Code</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Unit Price</TableHead>
                        <TableHead className="text-right">Discount</TableHead>
                        <TableHead className="text-right">Tax Rate</TableHead>
                        <TableHead className="text-right">Line Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {purchaseInvoice.items?.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.line_number}</TableCell>
                          <TableCell>{item.item_code || '-'}</TableCell>
                          <TableCell>{item.description}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">
                            <CurrencyDisplay 
                              amount={item.unit_price} 
                              currency={purchaseInvoice.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                          <TableCell className="text-right">
                            {item.discount_percentage ? `${item.discount_percentage}%` : 
                             item.discount_amount ? formatCurrency(item.discount_amount) : '-'}
                          </TableCell>
                          <TableCell className="text-right">{item.tax_percentage}%</TableCell>
                          <TableCell className="text-right">
                            <CurrencyDisplay 
                              amount={item.line_total} 
                              currency={purchaseInvoice.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Totals */}
                <div className="flex justify-end mt-6">
                  <div className="w-80 space-y-2">
                    <Separator />
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <CurrencyDisplay 
                        amount={purchaseInvoice.total_amount - purchaseInvoice.tax_amount}
                        currency={purchaseInvoice.currency_code}
                        showCurrency={false}
                      />
                    </div>
                    {purchaseInvoice.discount_amount && purchaseInvoice.discount_amount > 0 && (
                      <div className="flex justify-between">
                        <span>Discount:</span>
                        <CurrencyDisplay 
                          amount={purchaseInvoice.discount_amount}
                          currency={purchaseInvoice.currency_code}
                          showCurrency={false}
                        />
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span>Total Tax:</span>
                      <CurrencyDisplay 
                        amount={purchaseInvoice.tax_amount}
                        currency={purchaseInvoice.currency_code}
                        showCurrency={false}
                      />
                    </div>
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total Amount:</span>
                      <CurrencyDisplay 
                        amount={purchaseInvoice.total_amount} 
                        currency={purchaseInvoice.currency_code}
                      />
                    </div>
                    <div className="flex justify-between text-red-600 font-medium">
                      <span>Outstanding:</span>
                      <CurrencyDisplay 
                        amount={purchaseInvoice.outstanding_amount || 0} 
                        currency={purchaseInvoice.currency_code}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Payments Tab */}
        <TabsContent value="payments">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Payment History</CardTitle>
                {canPay && (
                  <Button onClick={onPay}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Process Payment
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {purchaseInvoice.payments && purchaseInvoice.payments.length > 0 ? (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Payment Number</TableHead>
                        <TableHead>Payment Date</TableHead>
                        <TableHead>Payment Method</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {purchaseInvoice.payments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell className="font-medium">
                            {payment.payment_number}
                          </TableCell>
                          <TableCell>
                            {format(new Date(payment.payment_date), 'dd MMM yyyy')}
                          </TableCell>
                          <TableCell>
                            {getPaymentMethodLabel(payment.payment_method)}
                          </TableCell>
                          <TableCell className="text-right font-medium">
                            <CurrencyDisplay 
                              amount={payment.amount} 
                              currency={purchaseInvoice.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                          <TableCell>{payment.reference_number || '-'}</TableCell>
                          <TableCell>
                            <PaymentStatusBadge status={payment.status} />
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  
                  {/* Payment Summary */}
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-muted-foreground">Total Invoice Amount:</span>
                        <p className="text-lg font-bold">
                          <CurrencyDisplay amount={purchaseInvoice.total_amount} currency={purchaseInvoice.currency_code} />
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Total Paid:</span>
                        <p className="text-lg font-bold">
                          <CurrencyDisplay 
                            amount={purchaseInvoice.payments.reduce((sum, payment) => sum + payment.amount, 0)} 
                            currency={purchaseInvoice.currency_code} 
                          />
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Outstanding Amount:</span>
                        <p className="text-lg font-bold text-red-600">
                          <CurrencyDisplay 
                            amount={purchaseInvoice.outstanding_amount || 0} 
                            currency={purchaseInvoice.currency_code} 
                          />
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CreditCard className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No payments found for this invoice</p>
                  {canPay && (
                    <Button className="mt-4" onClick={onPay}>
                      Process First Payment
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Approval Tab */}
        <TabsContent value="approval">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Approval Workflow</CardTitle>
                <div className="flex gap-2">
                  {canSubmitForApproval && (
                    <Button variant="outline" onClick={() => setShowSubmitDialog(true)}>
                      <Send className="mr-2 h-4 w-4" />
                      Submit for Approval
                    </Button>
                  )}
                  {canApprove && (
                    <>
                      <Button variant="outline" onClick={() => setShowRejectDialog(true)}>
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject
                      </Button>
                      <Button onClick={() => setShowApproveDialog(true)}>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Approve
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Current Status */}
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold">Current Status</h4>
                      <p className="text-sm text-muted-foreground">Invoice approval status</p>
                    </div>
                    <PurchaseInvoiceStatusBadge status={purchaseInvoice.status} />
                  </div>
                </div>

                {/* Approval History */}
                <div>
                  <h4 className="font-semibold mb-4">Approval History</h4>
                  {isLoadingApprovalHistory ? (
                    <div className="text-center py-8">
                      <div className="w-6 h-6 border-2 border-muted border-t-foreground rounded-full animate-spin mx-auto mb-4" />
                      <p className="text-muted-foreground">Loading approval history...</p>
                    </div>
                  ) : approvalHistory.length > 0 ? (
                    <div className="space-y-4">
                      {approvalHistory.map((entry) => (
                        <div key={entry.id} className="flex gap-4 pb-4 border-b last:border-b-0">
                          <div className="flex-shrink-0">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              entry.action === 'APPROVED' ? 'bg-green-100' : 
                              entry.action === 'REJECTED' ? 'bg-red-100' : 
                              entry.action === 'RETURNED' ? 'bg-orange-100' :
                              'bg-blue-100'
                            }`}>
                              {entry.action === 'APPROVED' ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : entry.action === 'REJECTED' ? (
                                <XCircle className="h-4 w-4 text-red-600" />
                              ) : entry.action === 'RETURNED' ? (
                                <XCircle className="h-4 w-4 text-orange-600" />
                              ) : (
                                <History className="h-4 w-4 text-blue-600" />
                              )}
                            </div>
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <p className="font-medium">{entry.action}</p>
                              <span className="text-sm text-muted-foreground">
                                {format(new Date(entry.approval_date), 'dd MMM yyyy, HH:mm')}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              by {entry.approver_name} • Level {entry.approval_level}
                            </p>
                            {entry.comments && (
                              <p className="text-sm text-gray-600">{entry.comments}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <History className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      <p>No approval history available</p>
                      {canSubmitForApproval && (
                        <Button className="mt-4" onClick={() => setShowSubmitDialog(true)}>
                          Submit for Approval
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Three-Way Match Tab */}
        <TabsContent value="three-way-match">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Three-Way Match</CardTitle>
                {canMatchPO && (
                  <Button onClick={onMatchPO}>
                    <GitMerge className="mr-2 h-4 w-4" />
                    Perform Match
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {purchaseInvoice.purchase_order_id ? (
                <div className="space-y-6">
                  {/* Match Status */}
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-semibold">Match Status</h4>
                        <p className="text-sm text-muted-foreground">PO vs Invoice vs Receipt comparison</p>
                      </div>
                      <div className={`flex items-center gap-2 ${getMatchStatusColor(mockThreeWayMatch.match_status)}`}>
                        {getMatchStatusIcon(mockThreeWayMatch.match_status)}
                        <span className="font-medium">{mockThreeWayMatch.match_status}</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-muted-foreground">Total Variance:</span>
                        <p className={`text-lg font-bold ${mockThreeWayMatch.total_variance === 0 ? 'text-green-600' : 'text-red-600'}`}>
                          <CurrencyDisplay amount={mockThreeWayMatch.total_variance} currency={purchaseInvoice.currency_code} />
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Quantity Variance:</span>
                        <p className={`text-lg font-bold ${mockThreeWayMatch.quantity_variance === 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {mockThreeWayMatch.quantity_variance}
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Price Variance:</span>
                        <p className={`text-lg font-bold ${mockThreeWayMatch.price_variance === 0 ? 'text-green-600' : 'text-red-600'}`}>
                          <CurrencyDisplay amount={mockThreeWayMatch.price_variance} currency={purchaseInvoice.currency_code} />
                        </p>
                      </div>
                    </div>

                    {mockThreeWayMatch.match_date && (
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-sm text-muted-foreground">
                          Matched on {format(new Date(mockThreeWayMatch.match_date), 'dd MMM yyyy, HH:mm')} by {mockThreeWayMatch.matched_by}
                        </p>
                        {mockThreeWayMatch.notes && (
                          <p className="text-sm text-gray-600 mt-1">{mockThreeWayMatch.notes}</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Line-by-Line Match Details */}
                  <div>
                    <h4 className="font-semibold mb-4">Line Item Matching</h4>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Line #</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Invoice Qty</TableHead>
                            <TableHead className="text-right">PO Qty</TableHead>
                            <TableHead className="text-right">Received Qty</TableHead>
                            <TableHead className="text-right">Invoice Price</TableHead>
                            <TableHead className="text-right">PO Price</TableHead>
                            <TableHead className="text-right">Variance</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {mockThreeWayMatch.line_matches.map((match) => (
                            <TableRow key={match.line_number}>
                              <TableCell>{match.line_number}</TableCell>
                              <TableCell>{match.description}</TableCell>
                              <TableCell className="text-right">{match.invoice_quantity}</TableCell>
                              <TableCell className="text-right">{match.po_quantity || '-'}</TableCell>
                              <TableCell className="text-right">{match.received_quantity || '-'}</TableCell>
                              <TableCell className="text-right">
                                <CurrencyDisplay 
                                  amount={match.invoice_price} 
                                  currency={purchaseInvoice.currency_code}
                                  showCurrency={false}
                                />
                              </TableCell>
                              <TableCell className="text-right">
                                <CurrencyDisplay 
                                  amount={match.po_price || 0} 
                                  currency={purchaseInvoice.currency_code}
                                  showCurrency={false}
                                />
                              </TableCell>
                              <TableCell className={`text-right ${match.total_variance === 0 ? 'text-green-600' : 'text-red-600'}`}>
                                <CurrencyDisplay 
                                  amount={match.total_variance} 
                                  currency={purchaseInvoice.currency_code}
                                  showCurrency={false}
                                />
                              </TableCell>
                              <TableCell>
                                <div className={`flex items-center gap-1 ${getMatchStatusColor(match.match_status)}`}>
                                  {getMatchStatusIcon(match.match_status)}
                                  <span className="text-sm">{match.match_status}</span>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No purchase order linked to this invoice</p>
                  <p className="text-sm">Three-way matching is not available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Attached Documents</CardTitle>
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {mockDocuments.length > 0 ? (
                <div className="space-y-4">
                  {mockDocuments.map((document) => (
                    <div key={document.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="font-medium">{document.file_name}</p>
                          <div className="text-sm text-muted-foreground">
                            {(document.file_size / 1024).toFixed(0)} KB • 
                            Uploaded by {document.uploaded_by} on {format(new Date(document.uploaded_at), 'dd MMM yyyy')}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No documents attached to this invoice</p>
                  <Button className="mt-4" variant="outline">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload First Document
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Approval Dialogs */}
      <ApproveInvoiceDialog
        open={showApproveDialog}
        onOpenChange={setShowApproveDialog}
        invoice={purchaseInvoice}
        onSuccess={handleDialogSuccess}
      />

      <RejectInvoiceDialog
        open={showRejectDialog}
        onOpenChange={setShowRejectDialog}
        invoice={purchaseInvoice}
        onSuccess={handleDialogSuccess}
      />

      <SubmitForApprovalDialog
        open={showSubmitDialog}
        onOpenChange={setShowSubmitDialog}
        invoice={purchaseInvoice}
        onSuccess={handleDialogSuccess}
      />
    </div>
  );
}