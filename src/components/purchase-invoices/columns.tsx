"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ArrowUpDown, MoreHorizontal, Eye, Edit, Trash2, CreditCard, CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";
import { format } from "date-fns";
import Link from "next/link";

const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case "RECEIVED":
      return "secondary";
    case "PENDING_APPROVAL":
      return "outline";
    case "APPROVED":
      return "default";
    case "PAID":
      return "default";
    case "DISPUTED":
      return "destructive";
    case "CANCELLED":
      return "destructive";
    default:
      return "secondary";
  }
};

const getPaymentStatusBadge = (status: string) => {
  switch (status) {
    case "PENDING":
      return { label: "Pending", variant: "outline" as const, icon: null };
    case "PARTIAL":
      return { label: "Partial", variant: "secondary" as const, icon: null };
    case "COMPLETED":
      return { label: "Paid", variant: "default" as const, icon: CheckCircle };
    case "FAILED":
      return { label: "Failed", variant: "destructive" as const, icon: XCircle };
    default:
      return { label: status, variant: "secondary" as const, icon: null };
  }
};

export const usePurchaseInvoiceColumns = (): ColumnDef<PurchaseInvoice>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: "row_number",
    header: "#",
    cell: ({ row, table }) => {
      const { pageIndex, pageSize } = table.getState().pagination;
      return <div className="text-center">{pageIndex * pageSize + row.index + 1}</div>;
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "invoice_number",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent"
        >
          Invoice #
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const invoice = row.original;
      return (
        <Link
          href={`/purchase-invoices/${invoice.id}`}
          className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
        >
          {invoice.invoice_number}
        </Link>
      );
    },
  },
  {
    accessorKey: "supplier.name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent"
        >
          Supplier
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const supplier = row.original.supplier;
      return (
        <div className="space-y-1">
          <div className="font-medium">{supplier?.name}</div>
          <div className="text-sm text-muted-foreground">{supplier?.supplier_code}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "purchase_order.po_number",
    header: "PO Number",
    cell: ({ row }) => {
      const po = row.original.purchase_order;
      return po ? (
        <Link
          href={`/purchase-orders/${po.id}`}
          className="text-blue-600 hover:text-blue-800 hover:underline"
        >
          {po.po_number}
        </Link>
      ) : (
        <span className="text-gray-500">-</span>
      );
    },
  },
  {
    accessorKey: "invoice_date",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent"
        >
          Invoice Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return format(new Date(row.getValue("invoice_date")), "MMM dd, yyyy");
    },
  },
  {
    accessorKey: "due_date",
    header: "Due Date",
    cell: ({ row }) => {
      const dueDate = row.original.due_date;
      if (!dueDate) return <span className="text-gray-500">-</span>;

      const due = new Date(dueDate);
      const today = new Date();
      const daysUntilDue = Math.ceil((due.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      
      let textColor = "text-gray-600";
      if (daysUntilDue < 0 && row.original.outstanding_amount && row.original.outstanding_amount > 0) {
        textColor = "text-red-600 font-medium";
      } else if (daysUntilDue <= 7 && row.original.outstanding_amount && row.original.outstanding_amount > 0) {
        textColor = "text-amber-600 font-medium";
      }

      return (
        <div className={textColor}>
          {format(due, "MMM dd, yyyy")}
          {daysUntilDue < 0 && row.original.outstanding_amount && row.original.outstanding_amount > 0 && (
            <div className="text-xs text-red-500">{Math.abs(daysUntilDue)} days overdue</div>
          )}
          {daysUntilDue > 0 && daysUntilDue <= 7 && row.original.outstanding_amount && row.original.outstanding_amount > 0 && (
            <div className="text-xs text-amber-500">Due in {daysUntilDue} days</div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "total_amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent text-right"
        >
          Total Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("total_amount"));
      const currency = row.original.currency_code || "USD";
      return (
        <div className="text-right font-medium">
          {new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: currency,
          }).format(amount)}
        </div>
      );
    },
  },
  {
    accessorKey: "outstanding_amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent text-right"
        >
          Outstanding
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = row.original.outstanding_amount || 0;
      const currency = row.original.currency_code || "USD";
      return (
        <div className="text-right">
          <div
            className={`font-medium ${
              amount > 0 ? "text-red-600" : "text-green-600"
            }`}
          >
            {new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: currency,
            }).format(amount)}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge variant={getStatusBadgeVariant(status)}>
          {status.replace(/_/g, " ")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "payment_status",
    header: "Payment",
    cell: ({ row }) => {
      const paymentStatus = row.original.payment_status;
      const badge = getPaymentStatusBadge(paymentStatus);
      const IconComponent = badge.icon;
      
      return (
        <Badge variant={badge.variant} className="flex items-center gap-1">
          {IconComponent && <IconComponent className="h-3 w-3" />}
          {badge.label}
        </Badge>
      );
    },
  },
  {
    accessorKey: "match_status",
    header: "Match",
    cell: ({ row }) => {
      const matchStatus = row.original.match_status;
      if (!matchStatus) return <span className="text-gray-500">-</span>;
      
      const getMatchBadge = (status: string) => {
        switch (status) {
          case "MATCHED":
            return { label: "Matched", variant: "default" as const, icon: CheckCircle };
          case "PARTIALLY_MATCHED":
            return { label: "Partial", variant: "secondary" as const, icon: AlertTriangle };
          case "NOT_MATCHED":
            return { label: "No Match", variant: "outline" as const, icon: XCircle };
          case "DISPUTED":
            return { label: "Disputed", variant: "destructive" as const, icon: AlertTriangle };
          default:
            return { label: status, variant: "secondary" as const, icon: null };
        }
      };

      const badge = getMatchBadge(matchStatus);
      const IconComponent = badge.icon;
      
      return (
        <Badge variant={badge.variant} className="flex items-center gap-1">
          {IconComponent && <IconComponent className="h-3 w-3" />}
          {badge.label}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    enableHiding: false,
    cell: ({ row }) => {
      const invoice = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(invoice.invoice_number)}
            >
              Copy invoice number
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/purchase-invoices/${invoice.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View details
              </Link>
            </DropdownMenuItem>
            {(invoice.status === "RECEIVED" || invoice.status === "PENDING_APPROVAL") && (
              <DropdownMenuItem asChild>
                <Link href={`/purchase-invoices/${invoice.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
            )}
            {invoice.purchase_order_id && (
              <DropdownMenuItem asChild>
                <Link href={`/purchase-invoices/${invoice.id}/match`}>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Three-way Match
                </Link>
              </DropdownMenuItem>
            )}
            {invoice.status === "APPROVED" && (invoice.outstanding_amount || 0) > 0 && (
              <DropdownMenuItem asChild>
                <Link href={`/purchase-invoices/payment?invoice_id=${invoice.id}`}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Process Payment
                </Link>
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            {invoice.status === "RECEIVED" && (
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];