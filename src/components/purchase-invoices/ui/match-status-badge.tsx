import { Badge } from "@/components/ui/badge";
import { MatchStatus } from "@/types/purchase-invoices/purchase-invoices.types";
import { cn } from "@/lib/utils";

interface MatchStatusBadgeProps {
  status: MatchStatus;
  className?: string;
}

export function MatchStatusBadge({ status, className }: MatchStatusBadgeProps) {
  const getStatusColor = (status: MatchStatus) => {
    switch (status) {
      case "MATCHED": 
        return "bg-green-100 text-green-800 border-green-200";
      case "PARTIALLY_MATCHED": 
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "NOT_MATCHED": 
        return "bg-red-100 text-red-800 border-red-200";
      case "DISPUTED": 
        return "bg-purple-100 text-purple-800 border-purple-200";
      default: 
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusLabel = (status: MatchStatus) => {
    switch (status) {
      case "MATCHED": return "Matched";
      case "PARTIALLY_MATCHED": return "Partial";
      case "NOT_MATCHED": return "Not Matched";
      case "DISPUTED": return "Disputed";
      default: return status;
    }
  };

  return (
    <Badge className={cn(getStatusColor(status), className)} variant="outline">
      {getStatusLabel(status)}
    </Badge>
  );
}