"use client";

import { cn } from "@/lib/utils";

interface CurrencyDisplayProps {
  amount: number;
  currency: string;
  className?: string;
  showCurrency?: boolean;
  showSymbol?: boolean;
}

export function CurrencyDisplay({ 
  amount, 
  currency, 
  className,
  showCurrency = true,
  showSymbol = true
}: CurrencyDisplayProps) {
  const formatCurrency = (amount: number, currency: string) => {
    try {
      const formatter = new Intl.NumberFormat('en-US', {
        style: showSymbol ? 'currency' : 'decimal',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
      
      const formatted = formatter.format(amount);
      
      if (showCurrency && !showSymbol) {
        return `${formatted} ${currency}`;
      }
      
      return formatted;
    } catch (error) {
      // Fallback if currency is not supported
      return `${amount.toFixed(2)} ${showCurrency ? currency : ''}`;
    }
  };

  return (
    <span className={cn("font-medium", className)}>
      {formatCurrency(amount, currency)}
    </span>
  );
}