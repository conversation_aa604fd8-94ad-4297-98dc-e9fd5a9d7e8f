import { Badge } from "@/components/ui/badge";
import type { PaymentBatchStatus } from "@/types/purchase-invoices/payment-processing.types";

interface PaymentBatchStatusBadgeProps {
  status: PaymentBatchStatus;
  className?: string;
}

export function PaymentBatchStatusBadge({ status, className }: PaymentBatchStatusBadgeProps) {
  const getStatusConfig = (status: PaymentBatchStatus) => {
    switch (status) {
      case "DRAFT":
        return { variant: "secondary" as const, label: "Draft" };
      case "PENDING_APPROVAL":
        return { variant: "outline" as const, label: "Pending Approval" };
      case "APPROVED":
        return { variant: "default" as const, label: "Approved" };
      case "PROCESSING":
        return { variant: "default" as const, label: "Processing" };
      case "COMPLETED":
        return { variant: "success" as const, label: "Completed" };
      case "FAILED":
        return { variant: "destructive" as const, label: "Failed" };
      case "CANCELLED":
        return { variant: "outline" as const, label: "Cancelled" };
      default:
        return { variant: "secondary" as const, label: status };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  );
}