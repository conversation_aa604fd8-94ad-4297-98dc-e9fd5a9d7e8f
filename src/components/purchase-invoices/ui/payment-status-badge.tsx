import { Badge } from "@/components/ui/badge";
import type { PaymentStatus } from "@/types/purchase-invoices/purchase-invoices.types";

interface PaymentStatusBadgeProps {
  status: PaymentStatus;
  className?: string;
}

export function PaymentStatusBadge({ status, className }: PaymentStatusBadgeProps) {
  const getStatusConfig = (status: PaymentStatus) => {
    switch (status) {
      case "PENDING":
        return { variant: "secondary" as const, label: "Pending" };
      case "COMPLETED":
        return { variant: "success" as const, label: "Completed" };
      case "FAILED":
        return { variant: "destructive" as const, label: "Failed" };
      case "CANCELLED":
        return { variant: "outline" as const, label: "Cancelled" };
      default:
        return { variant: "secondary" as const, label: status };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  );
}