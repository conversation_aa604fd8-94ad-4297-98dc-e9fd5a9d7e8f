import { Badge } from "@/components/ui/badge";
import type { PurchaseInvoiceStatus } from "@/types/purchase-invoices/purchase-invoices.types";

interface PurchaseInvoiceStatusBadgeProps {
  status: PurchaseInvoiceStatus;
  className?: string;
}

export function PurchaseInvoiceStatusBadge({ status, className }: PurchaseInvoiceStatusBadgeProps) {
  const getStatusConfig = (status: PurchaseInvoiceStatus) => {
    switch (status) {
      case "SUBMITTED":
        return { variant: "secondary" as const, label: "Submitted" };
      case "RECEIVED":
        return { variant: "secondary" as const, label: "Received" };
      case "PENDING_APPROVAL":
        return { variant: "default" as const, label: "Pending Approval" };
      case "APPROVED":
        return { variant: "success" as const, label: "Approved" };
      case "PAID":
        return { variant: "success" as const, label: "Paid" };
      case "DISPUTED":
        return { variant: "destructive" as const, label: "Disputed" };
      case "CANCELLED":
        return { variant: "outline" as const, label: "Cancelled" };
      default:
        return { variant: "secondary" as const, label: status };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge variant={config.variant} className={className}>
      {config.label}
    </Badge>
  );
}