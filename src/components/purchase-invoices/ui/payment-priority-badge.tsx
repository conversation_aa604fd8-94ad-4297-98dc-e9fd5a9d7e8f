import { Badge } from "@/components/ui/badge";
import type { PaymentPriority } from "@/types/purchase-invoices/payment-processing.types";
import { AlertTriangle, Clock, CheckCircle2 } from "lucide-react";

interface PaymentPriorityBadgeProps {
  priority: PaymentPriority;
  className?: string;
  showIcon?: boolean;
}

export function PaymentPriorityBadge({ priority, className, showIcon = false }: PaymentPriorityBadgeProps) {
  const getPriorityConfig = (priority: PaymentPriority) => {
    switch (priority) {
      case "HIGH":
        return { 
          variant: "destructive" as const, 
          label: "High Priority", 
          icon: AlertTriangle,
          color: "text-red-600"
        };
      case "MEDIUM":
        return { 
          variant: "secondary" as const, 
          label: "Medium Priority", 
          icon: Clock,
          color: "text-yellow-600"
        };
      case "LOW":
        return { 
          variant: "outline" as const, 
          label: "Low Priority", 
          icon: CheckCircle2,
          color: "text-green-600"
        };
      default:
        return { 
          variant: "secondary" as const, 
          label: priority, 
          icon: Clock,
          color: "text-gray-600"
        };
    }
  };

  const config = getPriorityConfig(priority);
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className={className}>
      {showIcon && <Icon className="mr-1 h-3 w-3" />}
      {config.label}
    </Badge>
  );
}