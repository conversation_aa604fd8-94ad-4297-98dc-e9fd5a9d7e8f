"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { usePurchaseInvoiceForm } from "@/hooks/purchase-invoices/use-purchase-invoice-form";
import { BasicInformationSection } from "./sections/basic-information-section";
import { SupplierInformationSection } from "./sections/supplier-information-section";
import { LineItemsSection } from "./sections/line-items-section";
import { ThreeWayMatchSection } from "./sections/three-way-match-section";
import { DocumentUploadSection } from "./sections/document-upload-section";
import { CurrencyDisplay } from "@/components/purchase-invoices/ui/currency-display";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

interface PurchaseInvoiceFormProps {
  isEditing?: boolean;
  invoiceId?: string;
  initialData?: PurchaseInvoice;
}

export function PurchaseInvoiceForm({ 
  isEditing = false, 
  invoiceId, 
  initialData 
}: PurchaseInvoiceFormProps) {
  const {
    form,
    fields,
    loading,
    submitting,
    options,
    actions,
    calculations
  } = usePurchaseInvoiceForm(isEditing, invoiceId, initialData);

  const totals = calculations.calculateTotals();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <BasicInformationSection 
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        {/* Supplier Information */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Information</CardTitle>
          </CardHeader>
          <CardContent>
            <SupplierInformationSection 
              form={form}
              options={options}
              actions={actions}
            />
          </CardContent>
        </Card>

        {/* Three-Way Match */}
        <Card>
          <CardHeader>
            <CardTitle>Three-Way Match</CardTitle>
          </CardHeader>
          <CardContent>
            <ThreeWayMatchSection 
              form={form}
            />
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card>
          <CardHeader>
            <CardTitle>Line Items</CardTitle>
          </CardHeader>
          <CardContent>
            <LineItemsSection 
              form={form}
              fields={fields}
              actions={actions}
              calculations={calculations}
            />
            
            {/* Totals Summary */}
            {fields.length > 0 && (
              <div className="mt-6 flex justify-end">
                <div className="w-80 space-y-2">
                  <Separator />
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <CurrencyDisplay 
                      amount={totals.subtotal} 
                      currency={form.watch("currency_code")}
                      showCurrency={false}
                    />
                  </div>
                  <div className="flex justify-between">
                    <span>Total Tax:</span>
                    <CurrencyDisplay 
                      amount={totals.totalTax} 
                      currency={form.watch("currency_code")}
                      showCurrency={false}
                    />
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Invoice Total:</span>
                    <CurrencyDisplay 
                      amount={totals.totalAmount} 
                      currency={form.watch("currency_code")}
                    />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Document Upload */}
        <Card>
          <CardHeader>
            <CardTitle>Supporting Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <DocumentUploadSection 
              form={form}
              invoiceId={invoiceId}
              onUploadProgress={(progress) => console.log('Upload progress:', progress)}
              onUploadComplete={(files) => console.log('Upload complete:', files)}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-end gap-4">
              <Button 
                type="button" 
                variant="outline"
                onClick={() => window.history.back()}
                disabled={submitting}
              >
                Cancel
              </Button>
              
              <Button 
                type="button" 
                variant="outline"
                onClick={form.handleSubmit(actions.onSaveDraft)}
                disabled={submitting}
              >
                {submitting ? "Saving..." : "Save as Draft"}
              </Button>
              
              <Button 
                type="button"
                onClick={form.handleSubmit(actions.onSubmit)}
                disabled={submitting}
              >
                {submitting 
                  ? (isEditing ? "Updating..." : "Creating...") 
                  : (isEditing ? "Update Purchase Invoice" : "Create Purchase Invoice")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}