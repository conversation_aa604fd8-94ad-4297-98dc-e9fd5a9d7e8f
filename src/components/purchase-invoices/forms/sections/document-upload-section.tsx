"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Upload,
  File,
  Image,
  FileText,
  X,
  Eye,
  Download,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";

interface DocumentUploadSectionProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
  invoiceId?: string;
  onUploadProgress?: (progress: number) => void;
  onUploadComplete?: (files: UploadedFile[]) => void;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
  status: 'uploading' | 'completed' | 'error';
  progress?: number;
  thumbnailUrl?: string;
}

const ALLOWED_FILE_TYPES = {
  'application/pdf': { icon: FileText, label: 'PDF', color: 'bg-red-100 text-red-800' },
  'image/jpeg': { icon: Image, label: 'JPEG', color: 'bg-blue-100 text-blue-800' },
  'image/jpg': { icon: Image, label: 'JPG', color: 'bg-blue-100 text-blue-800' },
  'image/png': { icon: Image, label: 'PNG', color: 'bg-green-100 text-green-800' },
  'image/gif': { icon: Image, label: 'GIF', color: 'bg-purple-100 text-purple-800' },
  'image/webp': { icon: Image, label: 'WebP', color: 'bg-orange-100 text-orange-800' },
} as const;

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILES = 5;

export function DocumentUploadSection({ 
  form, 
  invoiceId,
  onUploadProgress,
  onUploadComplete 
}: DocumentUploadSectionProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const validateFile = (file: File): string | null => {
    if (!Object.keys(ALLOWED_FILE_TYPES).includes(file.type)) {
      return `File type ${file.type} is not supported. Please upload PDF or image files.`;
    }
    if (file.size > MAX_FILE_SIZE) {
      return `File size exceeds 10MB limit. Please choose a smaller file.`;
    }
    if (uploadedFiles.length >= MAX_FILES) {
      return `Maximum ${MAX_FILES} files allowed. Please remove some files first.`;
    }
    return null;
  };

  const uploadToSupabase = async (file: File): Promise<UploadedFile> => {
    // Simulate file upload - In real implementation, this would connect to Supabase Storage
    const fileId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      // Simulate upload progress
      const uploadFile: UploadedFile = {
        id: fileId,
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file), // Temporary URL for preview
        uploadedAt: new Date().toISOString(),
        status: 'uploading',
        progress: 0,
      };

      setUploadedFiles(prev => [...prev, uploadFile]);

      const interval = setInterval(() => {
        setUploadedFiles(prev => 
          prev.map(f => 
            f.id === fileId 
              ? { ...f, progress: Math.min((f.progress || 0) + 10, 100) }
              : f
          )
        );
      }, 100);

      setTimeout(() => {
        clearInterval(interval);
        
        // Simulate successful upload
        const completedFile: UploadedFile = {
          ...uploadFile,
          status: 'completed',
          progress: 100,
          url: `https://example.supabase.co/storage/v1/object/purchase-invoices/${invoiceId || 'draft'}/${fileId}`,
          thumbnailUrl: file.type.startsWith('image/') 
            ? URL.createObjectURL(file) 
            : undefined,
        };

        setUploadedFiles(prev => 
          prev.map(f => f.id === fileId ? completedFile : f)
        );
        
        onUploadProgress?.(100);
        resolve(completedFile);
      }, 1500);
    });
  };

  const handleFileUpload = async (files: FileList | File[]) => {
    setUploadError(null);
    setIsUploading(true);
    
    const fileArray = Array.from(files);
    const errors: string[] = [];
    const validFiles: File[] = [];

    // Validate all files first
    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push(file);
      }
    });

    if (errors.length > 0) {
      setUploadError(errors.join('\n'));
      setIsUploading(false);
      return;
    }

    try {
      // Upload files sequentially
      const uploadedResults: UploadedFile[] = [];
      for (const file of validFiles) {
        const result = await uploadToSupabase(file);
        uploadedResults.push(result);
      }
      
      onUploadComplete?.(uploadedResults);
    } catch (error) {
      console.error('Upload error:', error);
      setUploadError('Failed to upload files. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
    // Reset input value to allow re-selecting the same file
    e.target.value = '';
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => {
      const newFiles = prev.filter(f => f.id !== fileId);
      // Update form data if needed
      return newFiles;
    });
  };

  const previewFile = (file: UploadedFile) => {
    if (file.type.startsWith('image/')) {
      window.open(file.url, '_blank');
    } else if (file.type === 'application/pdf') {
      window.open(file.url, '_blank');
    }
  };

  const downloadFile = (file: UploadedFile) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = (fileType: string) => {
    const config = ALLOWED_FILE_TYPES[fileType as keyof typeof ALLOWED_FILE_TYPES];
    return config?.icon || File;
  };

  const getFileTypeLabel = (fileType: string) => {
    const config = ALLOWED_FILE_TYPES[fileType as keyof typeof ALLOWED_FILE_TYPES];
    return config?.label || 'Unknown';
  };

  const getFileTypeColor = (fileType: string) => {
    const config = ALLOWED_FILE_TYPES[fileType as keyof typeof ALLOWED_FILE_TYPES];
    return config?.color || 'bg-gray-100 text-gray-800';
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card 
        className={`border-2 border-dashed transition-colors duration-200 ${
          isDragOver 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <CardContent className="p-8">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <Upload className={`h-12 w-12 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Upload Invoice Documents</h3>
              <p className="text-sm text-gray-600">
                Drag and drop files here, or click to browse
              </p>
              <p className="text-xs text-gray-500">
                Supports: PDF, JPEG, PNG, GIF, WebP (max 10MB, up to {MAX_FILES} files)
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 justify-center items-center">
              <Button
                type="button"
                variant="outline"
                disabled={isUploading}
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                <Upload className="mr-2 h-4 w-4" />
                {isUploading ? 'Uploading...' : 'Choose Files'}
              </Button>
              
              <input
                id="file-upload"
                type="file"
                multiple
                accept={Object.keys(ALLOWED_FILE_TYPES).join(',')}
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Error */}
      {uploadError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="whitespace-pre-line">
            {uploadError}
          </AlertDescription>
        </Alert>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Uploaded Documents ({uploadedFiles.length})</h4>
                <div className="text-sm text-gray-500">
                  Total size: {formatFileSize(uploadedFiles.reduce((sum, file) => sum + file.size, 0))}
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                {uploadedFiles.map((file) => {
                  const FileIcon = getFileIcon(file.type);
                  
                  return (
                    <div key={file.id} className="flex items-center space-x-3 p-3 rounded-lg border">
                      {/* File Icon & Preview */}
                      <div className="flex-shrink-0">
                        {file.thumbnailUrl ? (
                          <img 
                            src={file.thumbnailUrl} 
                            alt={file.name}
                            className="h-10 w-10 rounded object-cover cursor-pointer hover:opacity-80"
                            onClick={() => previewFile(file)}
                          />
                        ) : (
                          <div className="h-10 w-10 bg-gray-100 rounded flex items-center justify-center">
                            <FileIcon className="h-5 w-5 text-gray-600" />
                          </div>
                        )}
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium truncate">{file.name}</p>
                          <Badge className={getFileTypeColor(file.type)}>
                            {getFileTypeLabel(file.type)}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center space-x-4 mt-1">
                          <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                          
                          {file.status === 'uploading' && (
                            <div className="flex items-center space-x-2 flex-1 max-w-xs">
                              <Progress value={file.progress || 0} className="h-2" />
                              <span className="text-xs text-gray-500">{file.progress || 0}%</span>
                            </div>
                          )}
                          
                          {file.status === 'completed' && (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-xs text-green-600">Uploaded</span>
                            </div>
                          )}
                          
                          {file.status === 'error' && (
                            <div className="flex items-center space-x-1">
                              <AlertCircle className="h-4 w-4 text-red-500" />
                              <span className="text-xs text-red-600">Failed</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-1">
                        {file.status === 'completed' && (
                          <>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => previewFile(file)}
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => downloadFile(file)}
                              className="h-8 w-8 p-0"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(file.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Upload Guidelines */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="space-y-1">
              <h4 className="text-sm font-medium text-blue-900">Document Upload Guidelines</h4>
              <ul className="text-xs text-blue-800 space-y-1 list-disc list-inside">
                <li>Upload original invoice documents (PDF preferred for best quality)</li>
                <li>Include supporting documents: delivery notes, contracts, purchase orders</li>
                <li>Ensure documents are clearly readable and properly oriented</li>
                <li>File names should be descriptive (e.g., "Invoice_ABC123_2024.pdf")</li>
                <li>Documents will be stored securely and accessible throughout approval workflow</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}