"use client";

import { Card, CardContent } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";

interface DropdownOption {
  value: string;
  label: string;
  code?: string;
}

interface BasicInformationSectionProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
  options: {
    entities: DropdownOption[];
    currencies: DropdownOption[];
  };
}

export function BasicInformationSection({ 
  form, 
  options 
}: BasicInformationSectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Entity Selection */}
      <FormField
        control={form.control}
        name="entity_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Entity *</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select entity" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.entities.map((entity) => (
                  <SelectItem key={entity.value} value={entity.value}>
                    {entity.label}
                    {entity.code && (
                      <span className="text-muted-foreground ml-2">
                        ({entity.code})
                      </span>
                    )}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Currency */}
      <FormField
        control={form.control}
        name="currency_code"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Currency *</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.currencies.map((currency) => (
                  <SelectItem key={currency.value} value={currency.value}>
                    {currency.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Invoice Number */}
      <FormField
        control={form.control}
        name="invoice_number"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Invoice Number *</FormLabel>
            <FormControl>
              <Input 
                placeholder="Enter invoice number" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Supplier Reference */}
      <FormField
        control={form.control}
        name="supplier_reference"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Supplier Reference</FormLabel>
            <FormControl>
              <Input 
                placeholder="Enter supplier reference" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Invoice Date */}
      <FormField
        control={form.control}
        name="invoice_date"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Invoice Date *</FormLabel>
            <FormControl>
              <Input 
                type="date" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Due Date */}
      <FormField
        control={form.control}
        name="due_date"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Due Date</FormLabel>
            <FormControl>
              <Input 
                type="date" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Reference Number */}
      <FormField
        control={form.control}
        name="reference_number"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Reference Number</FormLabel>
            <FormControl>
              <Input 
                placeholder="Enter reference number" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Notes - Full Width */}
      <div className="md:col-span-2">
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Enter additional notes" 
                  className="min-h-[100px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}