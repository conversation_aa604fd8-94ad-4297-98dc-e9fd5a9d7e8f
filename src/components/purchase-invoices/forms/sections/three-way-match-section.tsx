"use client";

import { Card, CardContent } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, CheckCircle } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";

interface ThreeWayMatchSectionProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
}

export function ThreeWayMatchSection({ form }: ThreeWayMatchSectionProps) {
  const hasPurchaseOrder = form.watch("purchase_order_id");
  const performThreeWayMatch = form.watch("perform_three_way_match");

  return (
    <div className="space-y-4">
      {/* Three-Way Match Option */}
      <FormField
        control={form.control}
        name="perform_three_way_match"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
                disabled={!hasPurchaseOrder}
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel>
                Perform Three-Way Match
              </FormLabel>
              <FormDescription>
                {hasPurchaseOrder 
                  ? "Automatically verify invoice against purchase order and goods receipt"
                  : "Select a purchase order to enable three-way matching"
                }
              </FormDescription>
            </div>
          </FormItem>
        )}
      />

      {/* Information Card */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            {performThreeWayMatch && hasPurchaseOrder ? (
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            ) : (
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            )}
            <div className="space-y-2">
              <h4 className="font-medium">
                {performThreeWayMatch && hasPurchaseOrder
                  ? "Three-Way Match Enabled"
                  : "Three-Way Match Information"
                }
              </h4>
              
              {performThreeWayMatch && hasPurchaseOrder ? (
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>
                    The system will automatically match this invoice against:
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Purchase Order quantities and prices</li>
                    <li>Goods Receipt quantities (if available)</li>
                    <li>Invoice quantities and prices</li>
                  </ul>
                  <p className="mt-2">
                    Any variances outside tolerance levels will require approval.
                  </p>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>
                    Three-way matching helps ensure accuracy by comparing:
                  </p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Purchase Order (what was ordered)</li>
                    <li>Goods Receipt (what was received)</li>
                    <li>Invoice (what is being billed)</li>
                  </ul>
                  {!hasPurchaseOrder && (
                    <p className="mt-2 text-yellow-600">
                      Select a purchase order above to enable this feature.
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}