"use client";

import { Card, CardContent } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { UseFormReturn } from "react-hook-form";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";

interface DropdownOption {
  value: string;
  label: string;
  code?: string;
  email?: string;
  phone?: string;
  payment_terms?: number;
  status?: string;
}

interface PurchaseOrderOption extends DropdownOption {
  po_number?: string;
  total_amount?: number;
  items?: any[];
}

interface SupplierInformationSectionProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
  options: {
    suppliers: DropdownOption[];
    purchaseOrders: PurchaseOrderOption[];
  };
  actions: {
    populateFromPO: () => void;
  };
}

export function SupplierInformationSection({ 
  form, 
  options,
  actions
}: SupplierInformationSectionProps) {
  const selectedSupplier = form.watch("supplier_id");
  const selectedPO = form.watch("purchase_order_id");

  const selectedSupplierInfo = options.suppliers.find(s => s.value === selectedSupplier);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Supplier Selection */}
        <div className="md:col-span-2">
          <FormField
            control={form.control}
            name="supplier_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Supplier *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {options.suppliers.map((supplier) => (
                      <SelectItem key={supplier.value} value={supplier.value}>
                        <div className="flex flex-col">
                          <span>{supplier.label}</span>
                          <span className="text-sm text-muted-foreground">
                            {supplier.code}
                            {supplier.email && ` • ${supplier.email}`}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Purchase Order Selection */}
        <div className="md:col-span-2">
          <FormField
            control={form.control}
            name="purchase_order_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Purchase Order (Optional)</FormLabel>
                <div className="flex gap-2">
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                    disabled={!selectedSupplier}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={
                          selectedSupplier 
                            ? "Select purchase order" 
                            : "Select supplier first"
                        } />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {options.purchaseOrders.map((po) => (
                        <SelectItem key={po.value} value={po.value}>
                          {po.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  {selectedPO && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={actions.populateFromPO}
                      className="whitespace-nowrap"
                    >
                      Populate Items
                    </Button>
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Supplier Information Display */}
      {selectedSupplierInfo && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Supplier Code:</span>
                <span className="ml-2 text-muted-foreground">
                  {selectedSupplierInfo.code}
                </span>
              </div>
              
              {selectedSupplierInfo.email && (
                <div>
                  <span className="font-medium">Email:</span>
                  <span className="ml-2 text-muted-foreground">
                    {selectedSupplierInfo.email}
                  </span>
                </div>
              )}
              
              {selectedSupplierInfo.phone && (
                <div>
                  <span className="font-medium">Phone:</span>
                  <span className="ml-2 text-muted-foreground">
                    {selectedSupplierInfo.phone}
                  </span>
                </div>
              )}
              
              {selectedSupplierInfo.payment_terms && (
                <div>
                  <span className="font-medium">Payment Terms:</span>
                  <span className="ml-2 text-muted-foreground">
                    {selectedSupplierInfo.payment_terms} days
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}