"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { Trash2, Plus } from "lucide-react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";
import { GLAccountSelect } from "../components/gl-account-select";
import { BudgetItemSelect } from "../components/budget-item-select";

interface LineItemsSectionProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
  fields: any[];
  actions: {
    addLineItem: () => void;
    removeLineItem: (index: number) => void;
  };
  calculations: {
    calculateLineTotal: (index: number) => number;
  };
}

export function LineItemsSection({ 
  form, 
  fields, 
  actions,
  calculations
}: LineItemsSectionProps) {
  return (
    <div className="space-y-4">
      {/* Add Item Button */}
      <div className="flex justify-end">
        <Button
          type="button"
          variant="outline"
          onClick={actions.addLineItem}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Line Item
        </Button>
      </div>

      {fields.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              No line items added yet. Click "Add Line Item" to get started.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">#</TableHead>
                <TableHead className="min-w-[200px]">Description</TableHead>
                <TableHead className="w-24">Qty</TableHead>
                <TableHead className="w-32">Unit Price</TableHead>
                <TableHead className="w-32">Discount %</TableHead>
                <TableHead className="w-24">Tax %</TableHead>
                <TableHead className="w-32">Line Total</TableHead>
                <TableHead className="w-16">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fields.map((field, index) => (
                <TableRow key={field.id}>
                  {/* Line Number */}
                  <TableCell>
                    <div className="flex items-center justify-center">
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                  </TableCell>

                  {/* Description */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea 
                              placeholder="Item description"
                              className="min-h-[80px] resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Quantity */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input 
                              type="number"
                              min="0"
                              step="0.01"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Unit Price */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.unit_price`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input 
                              type="number"
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Discount Percentage */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.discount_percentage`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input 
                              type="number"
                              min="0"
                              max="100"
                              step="0.01"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Tax Percentage */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.tax_percentage`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input 
                              type="number"
                              min="0"
                              max="100"
                              step="0.01"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Line Total */}
                  <TableCell>
                    <div className="text-right font-medium">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: form.watch("currency_code") || 'USD'
                      }).format(calculations.calculateLineTotal(index))}
                    </div>
                  </TableCell>

                  {/* Actions */}
                  <TableCell>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => actions.removeLineItem(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Optional Fields Row */}
      {fields.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium">Additional Item Details (Optional)</h4>
          {fields.map((field, index) => (
            <Card key={field.id}>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name={`items.${index}.item_code`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Item Code</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter item code"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <GLAccountSelect
                    form={form}
                    name={`items.${index}.gl_account_code`}
                    label="GL Account"
                    placeholder="Select GL account..."
                    onAccountSelect={(account) => {
                      console.log('GL Account selected for line', index + 1, ':', account);
                    }}
                  />

                  <FormField
                    control={form.control}
                    name={`items.${index}.cost_center`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cost Center</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter cost center"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <BudgetItemSelect
                    form={form}
                    name={`items.${index}.budget_item_id`}
                    amountFieldName={`items.${index}.line_total`}
                    label="Budget Item"
                    placeholder="Select budget item..."
                    onBudgetSelect={(budgetItem) => {
                      console.log('Budget item selected for line', index + 1, ':', budgetItem);
                    }}
                    onBudgetValidation={(isValid, availableAmount) => {
                      console.log('Budget validation for line', index + 1, ':', { isValid, availableAmount });
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}