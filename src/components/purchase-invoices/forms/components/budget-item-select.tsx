"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Check, 
  ChevronsUpDown, 
  Search,
  Wallet,
  AlertTriangle,
  AlertCircle,
  DollarSign,
  TrendingUp,
  Building
} from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { cn } from "@/lib/utils";
import { 
  searchBudgetItems, 
  checkBudgetAvailability,
  type BudgetItem 
} from "@/services/budget/budget-items.service";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";

interface BudgetItemSelectProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
  name: string;
  amountFieldName?: string; // Field name for the amount to check against budget
  label?: string;
  placeholder?: string;
  required?: boolean;
  onBudgetSelect?: (budgetItem: BudgetItem | null) => void;
  onBudgetValidation?: (isValid: boolean, availableAmount: number) => void;
}

export function BudgetItemSelect({
  form,
  name,
  amountFieldName,
  label = "Budget Item",
  placeholder = "Search budget items...",
  required = false,
  onBudgetSelect,
  onBudgetValidation,
}: BudgetItemSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [budgetValidation, setBudgetValidation] = useState<{
    isValid: boolean;
    availableAmount: number;
    requestedAmount: number;
  } | null>(null);

  // Load initial budget items on mount
  useEffect(() => {
    loadBudgetItems("");
  }, []);

  // Search budget items when search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== undefined) {
        loadBudgetItems(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const selectedBudgetItem = useMemo(() => {
    const currentValue = form.watch(name as any);
    return budgetItems.find(item => item.id === currentValue) || null;
  }, [budgetItems, form.watch(name as any)]);

  // Validate budget when selection or amount changes
  useEffect(() => {
    if (selectedBudgetItem && amountFieldName) {
      const formValues = form.getValues();
      const requestedAmount = (formValues as any)[amountFieldName] || 0;
      if (requestedAmount > 0) {
        validateBudgetAvailability(selectedBudgetItem.id, requestedAmount);
      }
    }
  }, [selectedBudgetItem, amountFieldName, form.watch(name as any)]);

  const loadBudgetItems = async (search: string) => {
    try {
      setLoading(true);
      setError(null);
      const results = await searchBudgetItems(search, 100);
      setBudgetItems(results);
    } catch (err) {
      console.error('Error loading budget items:', err);
      setError('Failed to load budget items');
      setBudgetItems([]);
    } finally {
      setLoading(false);
    }
  };

  const validateBudgetAvailability = async (budgetItemId: string, requestedAmount: number) => {
    try {
      const result = await checkBudgetAvailability(budgetItemId, requestedAmount);
      const validation = {
        isValid: result.available,
        availableAmount: result.availableAmount,
        requestedAmount,
      };
      setBudgetValidation(validation);
      onBudgetValidation?.(result.available, result.availableAmount);
    } catch (err) {
      console.error('Error validating budget:', err);
      setBudgetValidation(null);
      onBudgetValidation?.(false, 0);
    }
  };

  const handleSelect = (budgetItem: BudgetItem) => {
    form.setValue(name as any, budgetItem.id);
    setOpen(false);
    onBudgetSelect?.(budgetItem);
    
    // Validate immediately if amount is set
    if (amountFieldName) {
      const formValues = form.getValues();
      const requestedAmount = (formValues as any)[amountFieldName] || 0;
      if (requestedAmount > 0) {
        validateBudgetAvailability(budgetItem.id, requestedAmount);
      }
    }
  };

  const handleClear = () => {
    form.setValue(name as any, "");
    setBudgetValidation(null);
    onBudgetSelect?.(null);
    onBudgetValidation?.(false, 0);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getBudgetUsagePercentage = (budgetItem: BudgetItem) => {
    const totalUsed = budgetItem.spent_amount + budgetItem.committed_amount;
    const totalBudget = budgetItem.budgeted_amount;
    return totalBudget > 0 ? (totalUsed / totalBudget) * 100 : 0;
  };

  const getBudgetStatusColor = (percentage: number) => {
    if (percentage < 70) return "bg-green-500";
    if (percentage < 90) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'OPERATIONAL': 'bg-blue-100 text-blue-800',
      'CAPITAL': 'bg-purple-100 text-purple-800',
      'TRAVEL': 'bg-green-100 text-green-800',
      'TRAINING': 'bg-orange-100 text-orange-800',
      'SUPPLIES': 'bg-gray-100 text-gray-800',
      'SERVICES': 'bg-indigo-100 text-indigo-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <FormField
      control={form.control}
      name={name as any}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    "justify-between",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {selectedBudgetItem ? (
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <Wallet className="h-4 w-4 text-gray-500 flex-shrink-0" />
                      <span className="truncate">{selectedBudgetItem.item_code} - {selectedBudgetItem.item_name}</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4 text-gray-400" />
                      <span>{placeholder}</span>
                    </div>
                  )}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-[500px] p-0" align="start">
              <Command>
                <CommandInput
                  placeholder="Search budget items..."
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                />
                <CommandList>
                  {loading && (
                    <div className="p-4 text-center text-sm text-gray-500">
                      Loading budget items...
                    </div>
                  )}
                  
                  {error && (
                    <div className="p-4 text-center text-sm text-red-600 flex items-center justify-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      {error}
                    </div>
                  )}
                  
                  {!loading && !error && budgetItems.length === 0 && (
                    <CommandEmpty>No budget items found.</CommandEmpty>
                  )}
                  
                  {!loading && !error && budgetItems.length > 0 && (
                    <CommandGroup>
                      {budgetItems.map((budgetItem) => {
                        const usagePercentage = getBudgetUsagePercentage(budgetItem);
                        
                        return (
                          <CommandItem
                            key={budgetItem.id}
                            value={budgetItem.item_code}
                            onSelect={() => handleSelect(budgetItem)}
                            className="flex items-center gap-3 p-3"
                          >
                            <Check
                              className={cn(
                                "h-4 w-4",
                                selectedBudgetItem?.id === budgetItem.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium font-mono text-sm">
                                  {budgetItem.item_code}
                                </span>
                                <Badge className={getCategoryColor(budgetItem.category)}>
                                  {budgetItem.category}
                                </Badge>
                                {budgetItem.available_amount <= 0 && (
                                  <Badge variant="destructive">No Budget</Badge>
                                )}
                              </div>
                              <div className="text-sm text-gray-900 truncate mb-1">
                                {budgetItem.item_name}
                              </div>
                              
                              {/* Budget Information */}
                              <div className="space-y-1">
                                <div className="flex items-center justify-between text-xs">
                                  <span className="text-gray-500">Available:</span>
                                  <span className={cn(
                                    "font-medium",
                                    budgetItem.available_amount > 0 ? "text-green-600" : "text-red-600"
                                  )}>
                                    {formatCurrency(budgetItem.available_amount)}
                                  </span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                                    <div
                                      className={cn(
                                        "h-1.5 rounded-full transition-all",
                                        getBudgetStatusColor(usagePercentage)
                                      )}
                                      style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                                    />
                                  </div>
                                  <span className="text-xs text-gray-500">
                                    {usagePercentage.toFixed(0)}%
                                  </span>
                                </div>
                              </div>
                              
                              {/* Entity Information */}
                              {budgetItem.entity && (
                                <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                                  <Building className="h-3 w-3" />
                                  {budgetItem.entity.name}
                                </div>
                              )}
                            </div>
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          
          {/* Selected Budget Item Details */}
          {selectedBudgetItem && (
            <Card className="mt-2">
              <CardContent className="p-3">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <Wallet className="h-4 w-4 text-gray-500 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium truncate">
                          {selectedBudgetItem.item_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          Code: {selectedBudgetItem.item_code} • Category: {selectedBudgetItem.category}
                        </div>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={handleClear}
                      className="text-red-600 hover:text-red-700 ml-2"
                    >
                      Clear
                    </Button>
                  </div>
                  
                  {/* Budget Summary */}
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="font-medium text-blue-900">
                        {formatCurrency(selectedBudgetItem.budgeted_amount)}
                      </div>
                      <div className="text-blue-600">Budgeted</div>
                    </div>
                    <div className="text-center p-2 bg-orange-50 rounded">
                      <div className="font-medium text-orange-900">
                        {formatCurrency(selectedBudgetItem.spent_amount + selectedBudgetItem.committed_amount)}
                      </div>
                      <div className="text-orange-600">Used</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="font-medium text-green-900">
                        {formatCurrency(selectedBudgetItem.available_amount)}
                      </div>
                      <div className="text-green-600">Available</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Budget Validation Alert */}
          {budgetValidation && (
            <Alert variant={budgetValidation.isValid ? "default" : "destructive"}>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {budgetValidation.isValid ? (
                  <span className="text-green-700">
                    Sufficient budget available. Requested: {formatCurrency(budgetValidation.requestedAmount)}, 
                    Available: {formatCurrency(budgetValidation.availableAmount)}
                  </span>
                ) : (
                  <span>
                    Insufficient budget! Requested: {formatCurrency(budgetValidation.requestedAmount)}, 
                    Available: {formatCurrency(budgetValidation.availableAmount)}
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}
          
          <FormMessage />
        </FormItem>
      )}
    />
  );
}