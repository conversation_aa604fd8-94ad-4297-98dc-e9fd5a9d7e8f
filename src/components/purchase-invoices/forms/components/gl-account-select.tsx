"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Check, 
  ChevronsUpDown, 
  Search,
  Building2,
  Hash,
  AlertCircle
} from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { cn } from "@/lib/utils";
import { searchGLAccounts, type GLAccount } from "@/services/finance/gl-accounts.service";
import type { PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";

interface GLAccountSelectProps {
  form: UseFormReturn<PurchaseInvoiceFormData>;
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  onAccountSelect?: (account: GLAccount | null) => void;
}

export function GLAccountSelect({
  form,
  name,
  label = "GL Account",
  placeholder = "Search GL accounts...",
  required = false,
  onAccountSelect,
}: GLAccountSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [accounts, setAccounts] = useState<GLAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load initial accounts on mount
  useEffect(() => {
    loadAccounts("");
  }, []);

  // Search accounts when search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== undefined) {
        loadAccounts(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadAccounts = async (search: string) => {
    try {
      setLoading(true);
      setError(null);
      const results = await searchGLAccounts(search, 100);
      setAccounts(results);
    } catch (err) {
      console.error('Error loading GL accounts:', err);
      setError('Failed to load GL accounts');
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  const selectedAccount = useMemo(() => {
    const currentValue = form.watch(name as any);
    return accounts.find(account => account.account_code === currentValue) || null;
  }, [accounts, form.watch(name as any)]);

  const handleSelect = (account: GLAccount) => {
    form.setValue(name as any, account.account_code);
    setOpen(false);
    onAccountSelect?.(account);
  };

  const handleClear = () => {
    form.setValue(name as any, "");
    onAccountSelect?.(null);
  };

  const formatAccountDisplay = (account: GLAccount) => {
    return `${account.account_code} - ${account.account_name}`;
  };

  const getAccountTypeColor = (accountType: string) => {
    const colors: Record<string, string> = {
      'ASSET': 'bg-blue-100 text-blue-800',
      'LIABILITY': 'bg-red-100 text-red-800',
      'EQUITY': 'bg-green-100 text-green-800',
      'REVENUE': 'bg-purple-100 text-purple-800',
      'EXPENSE': 'bg-orange-100 text-orange-800',
      'COST_OF_GOODS_SOLD': 'bg-yellow-100 text-yellow-800',
    };
    return colors[accountType] || 'bg-gray-100 text-gray-800';
  };

  return (
    <FormField
      control={form.control}
      name={name as any}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    "justify-between",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {selectedAccount ? (
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <Hash className="h-4 w-4 text-gray-500 flex-shrink-0" />
                      <span className="truncate">{formatAccountDisplay(selectedAccount)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4 text-gray-400" />
                      <span>{placeholder}</span>
                    </div>
                  )}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-[400px] p-0" align="start">
              <Command>
                <CommandInput
                  placeholder="Search GL accounts..."
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                />
                <CommandList>
                  {loading && (
                    <div className="p-4 text-center text-sm text-gray-500">
                      Loading accounts...
                    </div>
                  )}
                  
                  {error && (
                    <div className="p-4 text-center text-sm text-red-600 flex items-center justify-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      {error}
                    </div>
                  )}
                  
                  {!loading && !error && accounts.length === 0 && (
                    <CommandEmpty>No GL accounts found.</CommandEmpty>
                  )}
                  
                  {!loading && !error && accounts.length > 0 && (
                    <CommandGroup>
                      {accounts.map((account) => (
                        <CommandItem
                          key={account.id}
                          value={account.account_code}
                          onSelect={() => handleSelect(account)}
                          className="flex items-center gap-3 p-3"
                        >
                          <Check
                            className={cn(
                              "h-4 w-4",
                              selectedAccount?.id === account.id ? "opacity-100" : "opacity-0"
                            )}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium font-mono text-sm">
                                {account.account_code}
                              </span>
                              <Badge className={getAccountTypeColor(account.account_type)}>
                                {account.account_type.replace('_', ' ')}
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-600 truncate">
                              {account.account_name}
                            </div>
                            {account.description && (
                              <div className="text-xs text-gray-500 truncate mt-1">
                                {account.description}
                              </div>
                            )}
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          
          {/* Selected Account Details */}
          {selectedAccount && (
            <Card className="mt-2">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <Building2 className="h-4 w-4 text-gray-500 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-medium truncate">
                        {selectedAccount.account_name}
                      </div>
                      <div className="text-xs text-gray-500">
                        Code: {selectedAccount.account_code} • Type: {selectedAccount.account_type.replace('_', ' ')}
                      </div>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleClear}
                    className="text-red-600 hover:text-red-700 ml-2"
                  >
                    Clear
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
          
          <FormMessage />
        </FormItem>
      )}
    />
  );
}