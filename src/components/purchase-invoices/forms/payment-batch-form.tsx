"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, CreditCard, DollarSign, AlertTriangle, Info, CheckCircle2, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import type { 
  CreatePaymentBatchInput, 
  BankAccount, 
  PaymentPriority,
  CreatePaymentInvoiceSelectionInput 
} from "@/types/purchase-invoices/payment-processing.types";
import type { PurchaseInvoice, PaymentMethod } from "@/types/purchase-invoices/purchase-invoices.types";

const paymentBatchSchema = z.object({
  batch_name: z.string().min(1, "Batch name is required"),
  entity_id: z.string().min(1, "Entity is required"),
  currency_code: z.string().min(1, "Currency is required"),
  payment_method: z.enum(["BANK_TRANSFER", "CHEQUE", "CASH", "ONLINE", "CREDIT_CARD"]),
  payment_date: z.date({ required_error: "Payment date is required" }),
  value_date: z.date().optional(),
  bank_account_id: z.string().min(1, "Bank account is required"),
  priority: z.enum(["HIGH", "MEDIUM", "LOW"]),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentBatchFormData = z.infer<typeof paymentBatchSchema>;

interface PaymentBatchFormProps {
  selectedInvoices: PurchaseInvoice[];
  bankAccounts: BankAccount[];
  onSubmit: (data: CreatePaymentBatchInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

interface InvoiceSelection extends CreatePaymentInvoiceSelectionInput {
  invoice: PurchaseInvoice;
  original_amount: number;
  early_discount_available: number;
  net_payment_amount: number;
}

export function PaymentBatchForm({ 
  selectedInvoices, 
  bankAccounts, 
  onSubmit, 
  onCancel, 
  loading = false 
}: PaymentBatchFormProps) {
  const [invoiceSelections, setInvoiceSelections] = useState<InvoiceSelection[]>([]);
  const [currencyRates, setCurrencyRates] = useState<{ [key: string]: number }>({});
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  const form = useForm<PaymentBatchFormData>({
    resolver: zodResolver(paymentBatchSchema),
    defaultValues: {
      batch_name: `Payment Batch ${format(new Date(), 'yyyy-MM-dd')}`,
      payment_method: "BANK_TRANSFER",
      payment_date: new Date(),
      priority: "MEDIUM",
    },
  });

  // Initialize invoice selections
  useEffect(() => {
    const selections: InvoiceSelection[] = selectedInvoices.map(invoice => {
      const originalAmount = invoice.outstanding_amount || invoice.total_amount;
      const earlyDiscountAvailable = invoice.early_payment_discount || 0;
      
      return {
        invoice_id: invoice.id,
        payment_amount: originalAmount,
        apply_early_discount: earlyDiscountAvailable > 0,
        withholding_tax_amount: 0,
        currency_rate: 1.0,
        reference_number: invoice.supplier_reference || "",
        notes: "",
        invoice,
        original_amount: originalAmount,
        early_discount_available: earlyDiscountAvailable,
        net_payment_amount: originalAmount - (earlyDiscountAvailable || 0),
      };
    });
    
    setInvoiceSelections(selections);
    
    // Set default currency from first invoice
    if (selectedInvoices.length > 0) {
      form.setValue("currency_code", selectedInvoices[0].currency_code);
    }
    
    // Generate default batch name
    const supplierNames = Array.from(new Set(selectedInvoices.map(inv => inv.supplier?.name)));
    if (supplierNames.length === 1) {
      form.setValue("batch_name", `Payment to ${supplierNames[0]} - ${format(new Date(), 'MMM dd, yyyy')}`);
    }
    
  }, [selectedInvoices, form]);

  // Filter bank accounts by selected currency
  const availableBankAccounts = bankAccounts.filter(
    account => account.currency_code === form.watch("currency_code")
  );

  const handleInvoiceSelectionChange = (index: number, field: keyof InvoiceSelection, value: any) => {
    setInvoiceSelections(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      
      // Recalculate net payment amount when payment amount or early discount changes
      if (field === "payment_amount" || field === "apply_early_discount") {
        const selection = updated[index];
        const discountAmount = selection.apply_early_discount ? selection.early_discount_available : 0;
        updated[index].net_payment_amount = selection.payment_amount - discountAmount;
      }
      
      return updated;
    });
  };

  const removeInvoiceSelection = (index: number) => {
    setInvoiceSelections(prev => prev.filter((_, i) => i !== index));
  };

  const totalPaymentAmount = invoiceSelections.reduce((sum, sel) => sum + sel.payment_amount, 0);
  const totalDiscountAmount = invoiceSelections.reduce((sum, sel) => 
    sum + (sel.apply_early_discount ? sel.early_discount_available : 0), 0
  );
  const netTotalAmount = totalPaymentAmount - totalDiscountAmount;

  const handleSubmit = async (data: PaymentBatchFormData) => {
    if (invoiceSelections.length === 0) {
      form.setError("batch_name", { message: "At least one invoice must be selected" });
      return;
    }

    const submitData: CreatePaymentBatchInput = {
      ...data,
      payment_date: data.payment_date.toISOString(),
      value_date: data.value_date?.toISOString(),
      invoice_selections: invoiceSelections.map(sel => ({
        invoice_id: sel.invoice_id,
        payment_amount: sel.payment_amount,
        apply_early_discount: sel.apply_early_discount,
        withholding_tax_amount: sel.withholding_tax_amount,
        currency_rate: sel.currency_rate,
        reference_number: sel.reference_number,
        notes: sel.notes,
      })),
    };

    await onSubmit(submitData);
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="mr-2 h-5 w-5" />
            Create Payment Batch
          </CardTitle>
          <CardDescription>
            Configure payment batch settings and review selected invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="batch_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Batch Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter batch name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="HIGH">High Priority</SelectItem>
                          <SelectItem value="MEDIUM">Medium Priority</SelectItem>
                          <SelectItem value="LOW">Low Priority</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD - US Dollar</SelectItem>
                          <SelectItem value="EUR">EUR - Euro</SelectItem>
                          <SelectItem value="GBP">GBP - British Pound</SelectItem>
                          <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="payment_method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Method</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                          <SelectItem value="CHEQUE">Cheque</SelectItem>
                          <SelectItem value="ONLINE">Online Payment</SelectItem>
                          <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="payment_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Payment Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="bank_account_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Account</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select bank account" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableBankAccounts.map((account) => (
                            <SelectItem key={account.id} value={account.id}>
                              <div className="flex flex-col">
                                <span>{account.account_name}</span>
                                <span className="text-xs text-muted-foreground">
                                  {account.bank_name} • {account.account_number} • {formatCurrency(account.available_balance, account.currency_code)}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {availableBankAccounts.length === 0 && (
                        <FormDescription className="text-amber-600">
                          No bank accounts available for selected currency
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Advanced Options */}
              <div className="space-y-4">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  className="text-sm"
                >
                  {showAdvancedOptions ? "Hide" : "Show"} Advanced Options
                </Button>

                {showAdvancedOptions && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-muted/50">
                    <FormField
                      control={form.control}
                      name="value_date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Value Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a value date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date() || date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            Date when funds will be debited from account
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="reference_number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reference Number</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Enter reference number" />
                          </FormControl>
                          <FormDescription>
                            Optional reference for tracking
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder="Enter any additional notes..." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Invoice Selections */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Selected Invoices ({invoiceSelections.length})</h3>
                  <div className="text-sm text-muted-foreground">
                    Total: {formatCurrency(netTotalAmount, form.watch("currency_code"))}
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Supplier</TableHead>
                        <TableHead>Original Amount</TableHead>
                        <TableHead>Payment Amount</TableHead>
                        <TableHead>Early Discount</TableHead>
                        <TableHead>Net Amount</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invoiceSelections.map((selection, index) => (
                        <TableRow key={selection.invoice.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{selection.invoice.invoice_number}</div>
                              <div className="text-sm text-muted-foreground">
                                {selection.invoice.supplier_reference}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{selection.invoice.supplier?.name}</TableCell>
                          <TableCell>
                            {formatCurrency(selection.original_amount, selection.invoice.currency_code)}
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              step="0.01"
                              value={selection.payment_amount}
                              onChange={(e) => handleInvoiceSelectionChange(index, "payment_amount", parseFloat(e.target.value) || 0)}
                              className="w-24"
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                checked={selection.apply_early_discount}
                                onCheckedChange={(checked) => handleInvoiceSelectionChange(index, "apply_early_discount", checked)}
                                disabled={selection.early_discount_available === 0}
                              />
                              <span className={selection.early_discount_available > 0 ? "text-green-600" : "text-muted-foreground"}>
                                {formatCurrency(selection.early_discount_available, selection.invoice.currency_code)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {formatCurrency(selection.net_payment_amount, selection.invoice.currency_code)}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeInvoiceSelection(index)}
                              className="text-destructive hover:text-destructive"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Summary */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{invoiceSelections.length}</div>
                        <div className="text-sm text-muted-foreground">Invoices</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {formatCurrency(totalPaymentAmount, form.watch("currency_code"))}
                        </div>
                        <div className="text-sm text-muted-foreground">Total Amount</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {formatCurrency(totalDiscountAmount, form.watch("currency_code"))}
                        </div>
                        <div className="text-sm text-muted-foreground">Total Discounts</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {formatCurrency(netTotalAmount, form.watch("currency_code"))}
                        </div>
                        <div className="text-sm text-muted-foreground">Net Payment</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading || invoiceSelections.length === 0}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {loading ? "Creating..." : "Create Payment Batch"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}