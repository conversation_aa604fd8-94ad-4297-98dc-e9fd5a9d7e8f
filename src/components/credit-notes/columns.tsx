"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ArrowUpDown, MoreHorizontal, Eye, Edit, Trash2, DollarSign } from "lucide-react";
import { CreditNote } from "@/types/credit-notes/credit-notes.types";
import { format } from "date-fns";
import Link from "next/link";

const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case "DRAFT":
      return "secondary";
    case "PENDING_APPROVAL":
      return "outline";
    case "APPROVED":
      return "default";
    case "APPLIED":
      return "default";
    case "PARTIALLY_APPLIED":
      return "outline";
    case "EXPIRED":
      return "destructive";
    case "CANCELLED":
      return "destructive";
    default:
      return "secondary";
  }
};

const getApplicationMethodBadge = (method: string) => {
  switch (method) {
    case "AUTO_APPLY":
      return { label: "Auto Apply", variant: "default" as const };
    case "MANUAL":
      return { label: "Manual", variant: "outline" as const };
    case "HOLD":
      return { label: "Hold", variant: "secondary" as const };
    default:
      return { label: method, variant: "secondary" as const };
  }
};

export const useCreditNoteColumns = (): ColumnDef<CreditNote>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: "row_number",
    header: "#",
    cell: ({ row, table }) => {
      const { pageIndex, pageSize } = table.getState().pagination;
      return <div className="text-center">{pageIndex * pageSize + row.index + 1}</div>;
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "invoice_number",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent"
        >
          Credit Note #
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const creditNote = row.original;
      return (
        <Link
          href={`/credit-notes/${creditNote.id}`}
          className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
        >
          {creditNote.invoice_number}
        </Link>
      );
    },
  },
  {
    accessorKey: "customer.name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent"
        >
          Customer
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const customer = row.original.customer;
      return (
        <div className="space-y-1">
          <div className="font-medium">{customer?.name}</div>
          <div className="text-sm text-muted-foreground">{customer?.customer_code}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "invoice_date",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent"
        >
          Issue Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return format(new Date(row.getValue("invoice_date")), "MMM dd, yyyy");
    },
  },
  {
    accessorKey: "total_amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent text-right"
        >
          Credit Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("total_amount"));
      const currency = row.original.currency_code || "USD";
      return (
        <div className="text-right font-medium">
          {new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: currency,
          }).format(amount)}
        </div>
      );
    },
  },
  {
    accessorKey: "unapplied_amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium hover:bg-transparent text-right"
        >
          Unapplied
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = row.original.unapplied_amount || 0;
      const currency = row.original.currency_code || "USD";
      return (
        <div className="text-right">
          <div
            className={`font-medium ${
              amount > 0 ? "text-green-600" : "text-gray-500"
            }`}
          >
            {new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: currency,
            }).format(amount)}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge variant={getStatusBadgeVariant(status)}>
          {status.replace(/_/g, " ")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "application_method",
    header: "Application",
    cell: ({ row }) => {
      const method = row.original.application_method;
      if (!method) return <div className="text-gray-500">-</div>;
      
      const badge = getApplicationMethodBadge(method);
      return <Badge variant={badge.variant}>{badge.label}</Badge>;
    },
  },
  {
    accessorKey: "expiry_date",
    header: "Expires",
    cell: ({ row }) => {
      const expiryDate = row.original.expiry_date;
      if (!expiryDate) {
        return <div className="text-gray-500">No expiry</div>;
      }

      const expiry = new Date(expiryDate);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      let textColor = "text-gray-600";
      if (daysUntilExpiry < 0) {
        textColor = "text-red-600 font-medium";
      } else if (daysUntilExpiry <= 30) {
        textColor = "text-amber-600 font-medium";
      }

      return (
        <div className={textColor}>
          {format(expiry, "MMM dd, yyyy")}
          {daysUntilExpiry < 0 && (
            <div className="text-xs text-red-500">Expired</div>
          )}
          {daysUntilExpiry > 0 && daysUntilExpiry <= 30 && (
            <div className="text-xs text-amber-500">{daysUntilExpiry} days</div>
          )}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    enableHiding: false,
    cell: ({ row }) => {
      const creditNote = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(creditNote.invoice_number)}
            >
              Copy credit note number
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/credit-notes/${creditNote.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View details
              </Link>
            </DropdownMenuItem>
            {creditNote.status === "DRAFT" && (
              <DropdownMenuItem asChild>
                <Link href={`/credit-notes/${creditNote.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
            )}
            {(creditNote.unapplied_amount || 0) > 0 && (
              <DropdownMenuItem asChild>
                <Link href={`/credit-notes/apply?credit_note_id=${creditNote.id}`}>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Apply Credit
                </Link>
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            {creditNote.status === "DRAFT" && (
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];