"use client";

import { Badge } from "@/components/ui/badge";
import type { InvoiceStatus } from "@/types/credit-notes/credit-notes.types";

interface CreditNoteStatusBadgeProps {
  status: InvoiceStatus;
  className?: string;
}

const getStatusBadgeVariant = (status: InvoiceStatus) => {
  switch (status) {
    case "DRAFT":
      return "secondary";
    case "PENDING_APPROVAL":
      return "outline";
    case "APPROVED":
      return "default";
    case "APPLIED":
      return "default";
    case "PARTIALLY_APPLIED":
      return "outline";
    case "EXPIRED":
      return "destructive";
    case "CANCELLED":
      return "destructive";
    default:
      return "secondary";
  }
};

const getStatusLabel = (status: InvoiceStatus) => {
  return status.replace(/_/g, " ");
};

export function CreditNoteStatusBadge({ status, className }: CreditNoteStatusBadgeProps) {
  return (
    <Badge variant={getStatusBadgeVariant(status)} className={className}>
      {getStatusLabel(status)}
    </Badge>
  );
}