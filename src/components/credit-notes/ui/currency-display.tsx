"use client";

interface CurrencyDisplayProps {
  amount: number;
  currency: string;
  showCurrency?: boolean;
  className?: string;
}

export function CurrencyDisplay({ 
  amount, 
  currency, 
  showCurrency = true,
  className = ""
}: CurrencyDisplayProps) {
  const formattedAmount = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount || 0);

  if (!showCurrency) {
    return (
      <span className={className}>
        {(amount || 0).toFixed(2)}
      </span>
    );
  }

  return (
    <span className={className}>
      {formattedAmount}
    </span>
  );
}