"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { CurrencyDisplay } from "@/components/credit-notes/ui/currency-display";
import { CreditNoteStatusBadge } from "@/components/credit-notes/ui/credit-note-status-badge";
import { ApplyCreditDialog } from "./apply-credit-dialog";
import { 
  Edit, 
  Printer, 
  Mail, 
  DollarSign, 
  History, 
  Copy, 
  X, 
  FileDown,
  CalendarDays,
  Building,
  User,
  Clock,
  FileText,
  Upload,
  Download,
  Eye,
  AlertCircle
} from "lucide-react";
import type { CreditNote } from "@/types/credit-notes/credit-notes.types";
import { format } from "date-fns";

interface CreditNoteDetailProps {
  creditNote: CreditNote;
  onEdit?: () => void;
  onPrint?: () => void;
  onEmail?: () => void;
  onApplyCredit?: () => void;
  onReverse?: () => void;
  onCancel?: () => void;
  onClone?: () => void;
  onRefresh?: () => void;
}

export function CreditNoteDetail({ 
  creditNote,
  onEdit,
  onPrint,
  onEmail,
  onApplyCredit,
  onReverse,
  onCancel,
  onClone,
  onRefresh
}: CreditNoteDetailProps) {
  const [activeTab, setActiveTab] = useState("overview");
  const [showApplyCreditDialog, setShowApplyCreditDialog] = useState(false);

  const canEdit = creditNote.status === "DRAFT";
  const canApplyCredit = (creditNote.unapplied_amount || 0) > 0 && creditNote.status === "APPROVED";
  const canCancel = !["CANCELLED", "EXPIRED"].includes(creditNote.status);
  const canReverse = creditNote.status === "APPLIED" || creditNote.status === "PARTIALLY_APPLIED";

  // Mock data for demonstration - in real app, these would come from the API
  const mockAuditTrail = [
    {
      id: "1",
      action: "CREATED",
      description: "Credit note created",
      user: "John Smith",
      timestamp: creditNote.created_at,
      details: "Initial credit note creation"
    },
    {
      id: "2", 
      action: "APPROVED",
      description: "Credit note approved",
      user: "Jane Doe",
      timestamp: "2024-01-16T09:15:00Z",
      details: "Approved for customer application"
    }
  ];

  const mockDocuments = [
    {
      id: "1",
      name: "Credit Note.pdf",
      size: 245760,
      type: "application/pdf",
      url: "/mock-document.pdf",
      uploadedAt: "2024-01-15T10:30:00Z",
      uploadedBy: "John Smith"
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: creditNote.currency_code || 'USD',
    }).format(amount);
  };

  const getCreditReasonLabel = (reason?: string) => {
    switch (reason) {
      case 'RETURN': return 'Product Return';
      case 'PRICING_ADJUSTMENT': return 'Pricing Adjustment';
      case 'SERVICE_ISSUE': return 'Service Issue';
      case 'PROMOTIONAL': return 'Promotional Credit';
      case 'OTHER': return 'Other';
      default: return reason || '-';
    }
  };

  const getApplicationMethodLabel = (method?: string) => {
    switch (method) {
      case 'AUTO_APPLY': return 'Auto Apply to Outstanding';
      case 'MANUAL': return 'Manual Application';
      case 'HOLD': return 'Hold for Customer Instruction';
      default: return method || '-';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-4">
                <h2 className="text-2xl font-bold">{creditNote.invoice_number}</h2>
                <CreditNoteStatusBadge status={creditNote.status} />
                {creditNote.expiry_date && new Date(creditNote.expiry_date) < new Date() && (
                  <Badge variant="destructive" className="gap-1">
                    <AlertCircle className="h-3 w-3" />
                    Expired
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">
                Credit Note • {creditNote.customer?.name}
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {canEdit && (
                <Button variant="outline" size="sm" onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              )}
              
              <Button variant="outline" size="sm" onClick={onPrint}>
                <Printer className="mr-2 h-4 w-4" />
                Print
              </Button>
              
              <Button variant="outline" size="sm" onClick={onEmail}>
                <Mail className="mr-2 h-4 w-4" />
                Email
              </Button>
              
              {canApplyCredit && (
                <Button variant="outline" size="sm" onClick={() => setShowApplyCreditDialog(true)}>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Apply Credit
                </Button>
              )}
              
              <Button variant="outline" size="sm" onClick={onClone}>
                <Copy className="mr-2 h-4 w-4" />
                Clone
              </Button>
              
              {canReverse && (
                <Button variant="outline" size="sm" onClick={onReverse}>
                  <X className="mr-2 h-4 w-4" />
                  Reverse
                </Button>
              )}
              
              {canCancel && (
                <Button variant="outline" size="sm" onClick={onCancel}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Summary Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center gap-3">
              <Building className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Customer</p>
                <p className="font-medium">
                  {creditNote.customer?.display_name || creditNote.customer?.name || '-'}
                </p>
                <p className="text-sm text-muted-foreground">
                  {creditNote.customer?.customer_code || '-'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Credit Amount</p>
                <p className="font-medium text-lg">
                  <CurrencyDisplay amount={creditNote.total_amount} currency={creditNote.currency_code} />
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <CalendarDays className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Issue Date</p>
                <p className="font-medium">
                  {format(new Date(creditNote.invoice_date), 'dd MMM yyyy')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unapplied Amount</p>
                <p className={`font-medium ${(creditNote.unapplied_amount || 0) > 0 ? 'text-green-600' : 'text-gray-500'}`}>
                  <CurrencyDisplay amount={creditNote.unapplied_amount || 0} currency={creditNote.currency_code} />
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Section */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="applications">Applications</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid gap-6">
            {/* Credit Note Information */}
            <Card>
              <CardHeader>
                <CardTitle>Credit Note Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h4 className="font-semibold mb-4">Basic Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Credit Note Number</label>
                      <p className="font-medium">{creditNote.invoice_number}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <div className="pt-1">
                        <CreditNoteStatusBadge status={creditNote.status} />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Issue Date</label>
                      <p className="font-medium">{format(new Date(creditNote.invoice_date), 'dd MMM yyyy')}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Currency</label>
                      <p className="font-medium">{creditNote.currency_code}</p>
                    </div>
                    {creditNote.reference_number && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Reference Number</label>
                        <p className="font-medium">{creditNote.reference_number}</p>
                      </div>
                    )}
                    {creditNote.due_date && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                        <p className="font-medium">{format(new Date(creditNote.due_date), 'dd MMM yyyy')}</p>
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Credit Details */}
                <div>
                  <h4 className="font-semibold mb-4">Credit Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Credit Reason</label>
                      <p className="font-medium">{getCreditReasonLabel(creditNote.credit_reason)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Application Method</label>
                      <p className="font-medium">{getApplicationMethodLabel(creditNote.application_method)}</p>
                    </div>
                    {creditNote.expiry_date && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Expiry Date</label>
                        <p className={`font-medium ${new Date(creditNote.expiry_date) < new Date() ? 'text-red-600' : ''}`}>
                          {format(new Date(creditNote.expiry_date), 'dd MMM yyyy')}
                          {new Date(creditNote.expiry_date) < new Date() && (
                            <span className="ml-2 text-red-500 text-sm">(Expired)</span>
                          )}
                        </p>
                      </div>
                    )}
                    {creditNote.original_invoice_id && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Original Invoice</label>
                        <p className="font-medium">{creditNote.original_invoice?.invoice_number || creditNote.original_invoice_id}</p>
                      </div>
                    )}
                  </div>
                </div>

                {creditNote.notes && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Notes</label>
                      <p className="font-medium mt-1">{creditNote.notes}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Line Items */}
            <Card>
              <CardHeader>
                <CardTitle>Line Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Line #</TableHead>
                        <TableHead>Item Code</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Unit Price</TableHead>
                        <TableHead className="text-right">Discount</TableHead>
                        <TableHead className="text-right">Tax Rate</TableHead>
                        <TableHead className="text-right">Line Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {creditNote.items?.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.line_number}</TableCell>
                          <TableCell>{item.item_code || '-'}</TableCell>
                          <TableCell>{item.description}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">
                            <CurrencyDisplay 
                              amount={item.unit_price} 
                              currency={creditNote.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                          <TableCell className="text-right">
                            {item.discount_percentage ? `${item.discount_percentage}%` : 
                             item.discount_amount ? formatCurrency(item.discount_amount) : '-'}
                          </TableCell>
                          <TableCell className="text-right">{item.tax_percentage}%</TableCell>
                          <TableCell className="text-right">
                            <CurrencyDisplay 
                              amount={item.line_total} 
                              currency={creditNote.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Totals */}
                <div className="flex justify-end mt-6">
                  <div className="w-80 space-y-2">
                    <Separator />
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <CurrencyDisplay 
                        amount={creditNote.items?.reduce((sum, item) => sum + (item.line_total - item.tax_amount), 0) || 0}
                        currency={creditNote.currency_code}
                        showCurrency={false}
                      />
                    </div>
                    <div className="flex justify-between">
                      <span>Total Tax:</span>
                      <CurrencyDisplay 
                        amount={creditNote.items?.reduce((sum, item) => sum + item.tax_amount, 0) || 0}
                        currency={creditNote.currency_code}
                        showCurrency={false}
                      />
                    </div>
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total Credit:</span>
                      <CurrencyDisplay 
                        amount={creditNote.total_amount} 
                        currency={creditNote.currency_code}
                      />
                    </div>
                    <div className="flex justify-between text-green-600 font-medium">
                      <span>Unapplied:</span>
                      <CurrencyDisplay 
                        amount={creditNote.unapplied_amount || 0} 
                        currency={creditNote.currency_code}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Applications Tab */}
        <TabsContent value="applications">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Credit Applications</CardTitle>
                {canApplyCredit && (
                  <Button onClick={() => setShowApplyCreditDialog(true)}>
                    <DollarSign className="mr-2 h-4 w-4" />
                    Apply Credit
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {creditNote.applications && creditNote.applications.length > 0 ? (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice Number</TableHead>
                        <TableHead>Invoice Date</TableHead>
                        <TableHead className="text-right">Invoice Amount</TableHead>
                        <TableHead className="text-right">Applied Amount</TableHead>
                        <TableHead>Application Date</TableHead>
                        <TableHead>Applied By</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {creditNote.applications.map((application) => (
                        <TableRow key={application.id}>
                          <TableCell className="font-medium">
                            {application.invoice?.invoice_number || '-'}
                          </TableCell>
                          <TableCell>
                            {application.invoice?.invoice_date ? 
                              format(new Date(application.invoice.invoice_date), 'dd MMM yyyy') : '-'}
                          </TableCell>
                          <TableCell className="text-right">
                            <CurrencyDisplay 
                              amount={application.invoice?.total_amount || 0} 
                              currency={creditNote.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                          <TableCell className="text-right font-medium">
                            <CurrencyDisplay 
                              amount={application.amount_applied} 
                              currency={creditNote.currency_code}
                              showCurrency={false}
                            />
                          </TableCell>
                          <TableCell>
                            {format(new Date(application.application_date), 'dd MMM yyyy')}
                          </TableCell>
                          <TableCell>{application.created_by}</TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  
                  {/* Application Summary */}
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-muted-foreground">Total Credit Amount:</span>
                        <p className="text-lg font-bold">
                          <CurrencyDisplay amount={creditNote.total_amount} currency={creditNote.currency_code} />
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Total Applied:</span>
                        <p className="text-lg font-bold">
                          <CurrencyDisplay 
                            amount={creditNote.applications.reduce((sum, app) => sum + app.amount_applied, 0)} 
                            currency={creditNote.currency_code} 
                          />
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Remaining Balance:</span>
                        <p className="text-lg font-bold text-green-600">
                          <CurrencyDisplay 
                            amount={creditNote.unapplied_amount || 0} 
                            currency={creditNote.currency_code} 
                          />
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <DollarSign className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No applications found for this credit note</p>
                  {canApplyCredit && (
                    <Button className="mt-4" onClick={() => setShowApplyCreditDialog(true)}>
                      Apply Credit to Invoice
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
            </CardHeader>
            <CardContent>
              {mockAuditTrail.length > 0 ? (
                <div className="space-y-4">
                  {mockAuditTrail.map((entry) => (
                    <div key={entry.id} className="flex gap-4 pb-4 border-b last:border-b-0">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <History className="h-4 w-4 text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{entry.description}</p>
                          <span className="text-sm text-muted-foreground">
                            {format(new Date(entry.timestamp), 'dd MMM yyyy, HH:mm')}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          by {entry.user}
                        </p>
                        {entry.details && (
                          <p className="text-sm text-gray-600">{entry.details}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <History className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No history available for this credit note</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Attached Documents</CardTitle>
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {mockDocuments.length > 0 ? (
                <div className="space-y-4">
                  {mockDocuments.map((document) => (
                    <div key={document.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="font-medium">{document.name}</p>
                          <div className="text-sm text-muted-foreground">
                            {(document.size / 1024).toFixed(0)} KB • 
                            Uploaded by {document.uploadedBy} on {format(new Date(document.uploadedAt), 'dd MMM yyyy')}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No documents attached to this credit note</p>
                  <Button className="mt-4" variant="outline">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload First Document
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Apply Credit Dialog */}
      <ApplyCreditDialog
        open={showApplyCreditDialog}
        onOpenChange={setShowApplyCreditDialog}
        creditNote={creditNote}
        onSuccess={() => {
          setShowApplyCreditDialog(false);
          onRefresh?.();
        }}
      />
    </div>
  );
}