"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { CurrencyDisplay } from "@/components/credit-notes/ui/currency-display";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useToast } from "@/hooks/use-toast";
import { getOutstandingInvoices, applyCreditNote } from "@/services/credit-notes/credit-notes.service";
import type { CreditNote, InvoiceInfo, CreditApplicationInput } from "@/types/credit-notes/credit-notes.types";
import { format } from "date-fns";

interface ApplyCreditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  creditNote: CreditNote;
  onSuccess?: () => void;
}

interface InvoiceWithApplication extends InvoiceInfo {
  applicationAmount: number;
  isSelected: boolean;
}

export function ApplyCreditDialog({ 
  open, 
  onOpenChange, 
  creditNote, 
  onSuccess 
}: ApplyCreditDialogProps) {
  const { toast } = useToast();
  const [invoices, setInvoices] = useState<InvoiceWithApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [applying, setApplying] = useState(false);

  useEffect(() => {
    if (open && creditNote.customer_id) {
      loadOutstandingInvoices();
    }
  }, [open, creditNote.customer_id]);

  const loadOutstandingInvoices = async () => {
    try {
      setLoading(true);
      const data = await getOutstandingInvoices(creditNote.customer_id);
      
      const invoicesWithApplication = data.map(invoice => ({
        ...invoice,
        applicationAmount: 0,
        isSelected: false,
      }));
      
      setInvoices(invoicesWithApplication);
    } catch (error) {
      console.error("Error loading outstanding invoices:", error);
      toast({
        title: "Error",
        description: "Failed to load outstanding invoices",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSelectInvoice = (invoiceId: string, checked: boolean) => {
    setInvoices(prev => prev.map(invoice => {
      if (invoice.id === invoiceId) {
        const maxAmount = Math.min(
          invoice.outstanding_amount || 0,
          creditNote.unapplied_amount || 0
        );
        return {
          ...invoice,
          isSelected: checked,
          applicationAmount: checked ? maxAmount : 0,
        };
      }
      return invoice;
    }));
  };

  const handleAmountChange = (invoiceId: string, amount: number) => {
    setInvoices(prev => prev.map(invoice => {
      if (invoice.id === invoiceId) {
        const maxAmount = Math.min(
          invoice.outstanding_amount || 0,
          creditNote.unapplied_amount || 0
        );
        return {
          ...invoice,
          applicationAmount: Math.min(Math.max(0, amount), maxAmount),
        };
      }
      return invoice;
    }));
  };

  const totalApplicationAmount = invoices
    .filter(invoice => invoice.isSelected)
    .reduce((sum, invoice) => sum + invoice.applicationAmount, 0);

  const remainingCreditAmount = (creditNote.unapplied_amount || 0) - totalApplicationAmount;

  const handleApply = async () => {
    const applications: CreditApplicationInput[] = invoices
      .filter(invoice => invoice.isSelected && invoice.applicationAmount > 0)
      .map(invoice => ({
        invoice_id: invoice.id,
        amount: invoice.applicationAmount,
      }));

    if (applications.length === 0) {
      toast({
        title: "Warning",
        description: "Please select at least one invoice to apply credit to",
        variant: "destructive",
      });
      return;
    }

    try {
      setApplying(true);
      await applyCreditNote({
        credit_note_id: creditNote.id,
        applications,
      });

      toast({
        title: "Success",
        description: `Credit applied to ${applications.length} invoice(s)`,
      });

      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error("Error applying credit:", error);
      toast({
        title: "Error",
        description: "Failed to apply credit note",
        variant: "destructive",
      });
    } finally {
      setApplying(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Apply Credit Note</DialogTitle>
          <DialogDescription>
            Apply credit from {creditNote.invoice_number} to outstanding invoices for {creditNote.customer?.name}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          {/* Credit Note Summary */}
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm text-muted-foreground">Credit Note</Label>
                <p className="font-medium">{creditNote.invoice_number}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Available Credit</Label>
                <p className="font-medium">
                  <CurrencyDisplay amount={creditNote.unapplied_amount || 0} currency={creditNote.currency_code} />
                </p>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : invoices.length > 0 ? (
            <div className="space-y-4">
              <h4 className="font-medium">Outstanding Invoices</h4>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">Apply</TableHead>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Outstanding</TableHead>
                    <TableHead className="text-right">Apply Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell>
                        <Checkbox
                          checked={invoice.isSelected}
                          onCheckedChange={(checked) => 
                            handleSelectInvoice(invoice.id, checked as boolean)
                          }
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        {invoice.invoice_number}
                      </TableCell>
                      <TableCell>
                        {format(new Date(invoice.invoice_date), 'dd MMM yyyy')}
                      </TableCell>
                      <TableCell className="text-right">
                        <CurrencyDisplay 
                          amount={invoice.outstanding_amount || 0} 
                          currency={creditNote.currency_code}
                          showCurrency={false}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        {invoice.isSelected ? (
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            max={Math.min(
                              invoice.outstanding_amount || 0,
                              creditNote.unapplied_amount || 0
                            )}
                            value={invoice.applicationAmount}
                            onChange={(e) => 
                              handleAmountChange(invoice.id, parseFloat(e.target.value) || 0)
                            }
                            className="w-24 h-8"
                          />
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Application Summary */}
              <div className="border-t pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-muted-foreground">Total Application Amount</Label>
                    <p className="font-medium">
                      <CurrencyDisplay amount={totalApplicationAmount} currency={creditNote.currency_code} />
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Remaining Credit</Label>
                    <p className="font-medium">
                      <CurrencyDisplay amount={remainingCreditAmount} currency={creditNote.currency_code} />
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>No outstanding invoices found for this customer</p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={applying}>
            Cancel
          </Button>
          <Button onClick={handleApply} disabled={applying || totalApplicationAmount === 0}>
            {applying ? "Applying..." : `Apply Credit (${formatCurrency(totalApplicationAmount)})`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  function formatCurrency(amount: number) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: creditNote.currency_code || 'USD',
    }).format(amount);
  }
}