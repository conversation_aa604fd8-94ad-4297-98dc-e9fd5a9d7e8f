"use client";

import { UseFormReturn } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { CreditNoteFormData, DropdownOption } from "@/types/credit-notes/credit-notes.types";

interface CustomerInformationSectionProps {
  form: UseFormReturn<CreditNoteFormData>;
  options: {
    customers: DropdownOption[];
  };
}

export function CustomerInformationSection({ form, options }: CustomerInformationSectionProps) {
  const selectedCustomerId = form.watch("customer_id");
  const selectedCustomer = options.customers.find(c => c.value === selectedCustomerId);

  return (
    <div className="space-y-6">
      {/* Customer Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="customer_id"
          render={({ field }) => (
            <FormItem className="md:col-span-2">
              <FormLabel>Customer *</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {options.customers.map((customer) => (
                    <SelectItem key={customer.value} value={customer.value}>
                      {customer.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Customer Details Display */}
      {selectedCustomer && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Customer Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Customer Code:</span>
                <p className="text-muted-foreground">{selectedCustomer.code || "N/A"}</p>
              </div>
              <div>
                <span className="font-medium">Name:</span>
                <p className="text-muted-foreground">{selectedCustomer.label}</p>
              </div>
              {selectedCustomer.email && (
                <div>
                  <span className="font-medium">Email:</span>
                  <p className="text-muted-foreground">{selectedCustomer.email}</p>
                </div>
              )}
              {selectedCustomer.phone && (
                <div>
                  <span className="font-medium">Phone:</span>
                  <p className="text-muted-foreground">{selectedCustomer.phone}</p>
                </div>
              )}
              {selectedCustomer.creditBalance !== undefined && (
                <div>
                  <span className="font-medium">Current Credit Balance:</span>
                  <p className={`font-medium ${selectedCustomer.creditBalance > 0 ? 'text-green-600' : 'text-gray-600'}`}>
                    ${selectedCustomer.creditBalance.toFixed(2)}
                  </p>
                </div>
              )}
              {selectedCustomer.outstandingBalance !== undefined && (
                <div>
                  <span className="font-medium">Outstanding Balance:</span>
                  <p className={`font-medium ${selectedCustomer.outstandingBalance > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                    ${selectedCustomer.outstandingBalance.toFixed(2)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}