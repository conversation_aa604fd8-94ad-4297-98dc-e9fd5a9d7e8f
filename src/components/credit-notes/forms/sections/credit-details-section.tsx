"use client";

import { UseFormReturn } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import type { CreditNoteFormData, DropdownOption } from "@/types/credit-notes/credit-notes.types";

interface CreditDetailsSectionProps {
  form: UseFormReturn<CreditNoteFormData>;
  options: {
    creditReasons: DropdownOption[];
    applicationMethods: DropdownOption[];
    invoices: DropdownOption[];
  };
}

export function CreditDetailsSection({ form, options }: CreditDetailsSectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Credit Reason */}
      <FormField
        control={form.control}
        name="credit_reason"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Credit Reason *</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select credit reason" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.creditReasons.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Application Method */}
      <FormField
        control={form.control}
        name="application_method"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Application Method *</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select application method" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.applicationMethods.map((method) => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Original Invoice */}
      <FormField
        control={form.control}
        name="original_invoice_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Original Invoice (Optional)</FormLabel>
            <Select onValueChange={(value) => field.onChange(value === "NONE" ? undefined : value)} value={field.value || "NONE"}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select original invoice" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="NONE">None</SelectItem>
                {options.invoices.map((invoice) => (
                  <SelectItem key={invoice.value} value={invoice.value}>
                    {invoice.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Expiry Date */}
      <FormField
        control={form.control}
        name="expiry_date"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Expiry Date (Optional)</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full pl-3 text-left font-normal",
                      !field.value && "text-muted-foreground"
                    )}
                  >
                    {field.value ? (
                      format(new Date(field.value), "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={field.value ? new Date(field.value) : undefined}
                  onSelect={(date) => field.onChange(date?.toISOString().split('T')[0])}
                  disabled={(date) =>
                    date < new Date()
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Application Restrictions */}
      <FormField
        control={form.control}
        name="application_restrictions"
        render={({ field }) => (
          <FormItem className="md:col-span-2">
            <FormLabel>Application Restrictions (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Enter any restrictions on how this credit can be applied"
                className="min-h-[80px]"
                {...field}
                value={field.value || ""}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}