"use client";

import { UseFormReturn, FieldArrayWithId } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Trash2 } from "lucide-react";
import { CurrencyDisplay } from "@/components/credit-notes/ui/currency-display";
import type { CreditNoteFormData } from "@/types/credit-notes/credit-notes.types";

interface LineItemsSectionProps {
  form: UseFormReturn<CreditNoteFormData>;
  fields: FieldArrayWithId<CreditNoteFormData, "items", "id">[];
  actions: {
    addLineItem: () => void;
    removeLineItem: (index: number) => void;
  };
  calculations: {
    calculateLineTotal: (index: number) => number;
  };
}

export function LineItemsSection({ 
  form, 
  fields, 
  actions, 
  calculations 
}: LineItemsSectionProps) {
  const currency = form.watch("currency_code");

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Line Items</h4>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={actions.addLineItem}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Line Item
          </Button>
        </div>
      </div>

      {/* Line Items Table */}
      <div className="border rounded-lg overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">Line #</TableHead>
              <TableHead className="w-[100px]">Item Code</TableHead>
              <TableHead className="min-w-[200px]">Description *</TableHead>
              <TableHead className="w-[100px]">Quantity *</TableHead>
              <TableHead className="w-[120px]">Unit Price *</TableHead>
              <TableHead className="w-[100px]">Discount %</TableHead>
              <TableHead className="w-[100px]">Discount $</TableHead>
              <TableHead className="w-[100px]">Tax %</TableHead>
              <TableHead className="w-[120px]">Line Total</TableHead>
              <TableHead className="w-[150px]">GL Account</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fields.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="text-center text-muted-foreground py-8">
                  No line items added. Click "Add Line Item" to get started.
                </TableCell>
              </TableRow>
            ) : (
              fields.map((field, index) => (
                <TableRow key={field.id}>
                  {/* Line Number */}
                  <TableCell className="font-medium">
                    {index + 1}
                  </TableCell>

                  {/* Item Code */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.item_code`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Item code"
                              {...field}
                              value={field.value || ""}
                              className="h-8"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Description */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="Description"
                              {...field}
                              className="min-h-[32px] h-8 resize-none"
                              rows={1}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Quantity */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              className="h-8"
                              min="0"
                              step="0.01"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Unit Price */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.unit_price`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              className="h-8"
                              min="0"
                              step="0.01"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Discount Percentage */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.discount_percentage`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="0"
                              {...field}
                              value={field.value || ""}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              className="h-8"
                              min="0"
                              max="100"
                              step="0.01"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Discount Amount */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.discount_amount`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="0.00"
                              {...field}
                              value={field.value || ""}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              className="h-8"
                              min="0"
                              step="0.01"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Tax Percentage */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.tax_percentage`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              className="h-8"
                              min="0"
                              max="100"
                              step="0.01"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Line Total */}
                  <TableCell>
                    <div className="text-right font-medium">
                      <CurrencyDisplay 
                        amount={calculations.calculateLineTotal(index)} 
                        currency={currency}
                        showCurrency={false}
                      />
                    </div>
                  </TableCell>

                  {/* GL Account Code */}
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`items.${index}.gl_account_code`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="GL Account"
                              {...field}
                              value={field.value || ""}
                              className="h-8"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  {/* Remove Button */}
                  <TableCell>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => actions.removeLineItem(index)}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {fields.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p className="text-sm">At least one line item is required.</p>
        </div>
      )}
    </div>
  );
}