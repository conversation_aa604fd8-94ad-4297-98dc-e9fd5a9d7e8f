"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useCreditNoteForm } from "@/hooks/credit-notes/use-credit-note-form";
import { BasicInformationSection } from "./sections/basic-information-section";
import { CustomerInformationSection } from "./sections/customer-information-section";
import { CreditDetailsSection } from "./sections/credit-details-section";
import { LineItemsSection } from "./sections/line-items-section";
import { CurrencyDisplay } from "@/components/credit-notes/ui/currency-display";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import type { CreditNote } from "@/types/credit-notes/credit-notes.types";

interface CreditNoteFormProps {
  isEditing?: boolean;
  creditNoteId?: string;
  initialData?: CreditNote;
}

export function CreditNoteForm({ 
  isEditing = false, 
  creditNoteId, 
  initialData 
}: CreditNoteFormProps) {
  const {
    form,
    fields,
    loading,
    submitting,
    options,
    actions,
    calculations
  } = useCreditNoteForm(isEditing, creditNoteId, initialData);

  const totals = calculations.calculateTotals();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <BasicInformationSection 
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <CustomerInformationSection 
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        {/* Credit Details */}
        <Card>
          <CardHeader>
            <CardTitle>Credit Details</CardTitle>
          </CardHeader>
          <CardContent>
            <CreditDetailsSection 
              form={form}
              options={options}
            />
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card>
          <CardHeader>
            <CardTitle>Line Items</CardTitle>
          </CardHeader>
          <CardContent>
            <LineItemsSection 
              form={form}
              fields={fields}
              actions={actions}
              calculations={calculations}
            />
            
            {/* Totals Summary */}
            <div className="mt-6 flex justify-end">
              <div className="w-80 space-y-2">
                <Separator />
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <CurrencyDisplay 
                    amount={totals.subtotal} 
                    currency={form.watch("currency_code")}
                    showCurrency={false}
                  />
                </div>
                <div className="flex justify-between">
                  <span>Total Tax:</span>
                  <CurrencyDisplay 
                    amount={totals.totalTax} 
                    currency={form.watch("currency_code")}
                    showCurrency={false}
                  />
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                  <span>Credit Total:</span>
                  <CurrencyDisplay 
                    amount={totals.totalAmount} 
                    currency={form.watch("currency_code")}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-end gap-4">
              <Button 
                type="button" 
                variant="outline"
                onClick={() => window.history.back()}
                disabled={submitting}
              >
                Cancel
              </Button>
              
              <Button 
                type="button" 
                variant="outline"
                onClick={form.handleSubmit(actions.onSaveDraft)}
                disabled={submitting}
              >
                {submitting ? "Saving..." : "Save as Draft"}
              </Button>
              
              <Button 
                type="button"
                onClick={form.handleSubmit(actions.onSubmit)}
                disabled={submitting}
              >
                {submitting ? "Creating..." : "Create Credit Note"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}