// RBAC Components and Hooks Export Index
// This file provides a single entry point for all RBAC-related imports

// Context and Hooks
export { RBACProvider, useRBACContext, usePermissions, usePermissionCheck, useAccessibleData } from '@/contexts/rbac-context'
export { useRBAC } from '@/hooks/rbac/use-rbac'

// Components
export { 
  PermissionGuard, 
  ModuleGuard, 
  ActionGuard, 
  withPermission 
} from './permission-guard'

export { 
  PermissionButton,
  CreateButton,
  EditButton, 
  DeleteButton,
  ExportButton,
  ApproveButton,
  ReviewButton
} from './permission-button'

export { 
  MembershipTypeFilter, 
  AccessibleMembershipTypeBadges 
} from './membership-type-filter'

// Utilities
export { 
  MODULES, 
  ACTIONS,
  checkServerPermission,
  getServerAccessibleMembershipTypes,
  getServerAccessibleMemberships,
  checkMultiplePermissions,
  filterByAccessibleMembershipTypes,
  generateMembershipTypeFilter,
  isUser<PERSON><PERSON><PERSON>,
  getUserPermissionSummary
} from '@/lib/rbac-utils'

export type { Module, Action } from '@/lib/rbac-utils'