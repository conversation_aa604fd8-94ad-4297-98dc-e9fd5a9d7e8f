'use client'

import { useState, useEffect } from 'react'
import { useAccessibleData } from '@/contexts/rbac-context'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'

interface AccessibleMembershipType {
  id: string
  name: string
  abbr: string
  group: string
}

interface MembershipTypeFilterProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  showAllOption?: boolean
  disabled?: boolean
  className?: string
}

export const MembershipTypeFilter = ({
  value,
  onValueChange,
  placeholder = "Select membership type",
  showAllOption = true,
  disabled = false,
  className
}: MembershipTypeFilterProps) => {
  const { getAccessibleMemberships } = useAccessibleData()
  const [membershipTypes, setMembershipTypes] = useState<AccessibleMembershipType[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadMembershipTypes()
  }, [])

  const loadMembershipTypes = async () => {
    try {
      setLoading(true)
      const types = await getAccessibleMemberships()
      setMembershipTypes(types)
    } catch (error) {
      console.error('Error loading membership types:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleValueChange = (newValue: string) => {
    if (onValueChange) {
      onValueChange(newValue === 'all' ? '' : newValue)
    }
  }

  if (loading) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Loading..." />
        </SelectTrigger>
      </Select>
    )
  }

  return (
    <Select 
      value={value || (showAllOption ? 'all' : '')} 
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {showAllOption && (
          <SelectItem value="all">All Types</SelectItem>
        )}
        {membershipTypes.map((type) => (
          <SelectItem key={type.id} value={type.id}>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{type.abbr}</Badge>
              {type.name}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

// Component to display accessible membership types as badges
export const AccessibleMembershipTypeBadges = () => {
  const { getAccessibleMemberships } = useAccessibleData()
  const [membershipTypes, setMembershipTypes] = useState<AccessibleMembershipType[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadMembershipTypes()
  }, [])

  const loadMembershipTypes = async () => {
    try {
      setLoading(true)
      const types = await getAccessibleMemberships()
      setMembershipTypes(types)
    } catch (error) {
      console.error('Error loading membership types:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <Badge variant="outline">Loading...</Badge>
  }

  if (membershipTypes.length === 0) {
    return <Badge variant="destructive">No Access</Badge>
  }

  return (
    <div className="flex flex-wrap gap-1">
      {membershipTypes.map((type) => (
        <Badge key={type.id} variant="secondary">
          {type.abbr}
        </Badge>
      ))}
    </div>
  )
}