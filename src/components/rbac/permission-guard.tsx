'use client'

import { ReactNode } from 'react'
import { useRBACContext } from '@/contexts/rbac-context'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface PermissionGuardProps {
  module: string
  action: string
  membershipTypeId?: string
  children: ReactNode
  fallback?: ReactNode
  showLoading?: boolean
  requireAll?: boolean // If true, user must have ALL specified permissions
}

export const PermissionGuard = ({ 
  module, 
  action, 
  membershipTypeId, 
  children, 
  fallback = null,
  showLoading = true,
  requireAll = false
}: PermissionGuardProps) => {
  const { getCachedPermission, loading } = useRBACContext()
  
  // Use cached permissions for immediate response
  const allowed = getCachedPermission(module, action)

  if (loading && showLoading) {
    return <LoadingSpinner />
  }

  if (loading && !showLoading) {
    return null
  }
  
  if (!allowed) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

// Specialized components for common use cases
interface ModuleGuardProps {
  module: string
  children: ReactNode
  fallback?: ReactNode
}

export const ModuleGuard = ({ module, children, fallback }: ModuleGuardProps) => (
  <PermissionGuard module={module} action="READ" fallback={fallback}>
    {children}
  </PermissionGuard>
)

interface ActionGuardProps {
  module: string
  action: string
  children: ReactNode
  fallback?: ReactNode
  membershipTypeId?: string
}

export const ActionGuard = ({ module, action, children, fallback, membershipTypeId }: ActionGuardProps) => (
  <PermissionGuard 
    module={module} 
    action={action} 
    membershipTypeId={membershipTypeId}
    fallback={fallback}
  >
    {children}
  </PermissionGuard>
)

// Higher-order component version
export function withPermission<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  module: string,
  action: string,
  fallback?: ReactNode
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGuard module={module} action={action} fallback={fallback}>
        <WrappedComponent {...props} />
      </PermissionGuard>
    )
  }
}