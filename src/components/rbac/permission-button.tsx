'use client'

import { ReactNode } from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import { useRBACContext } from '@/contexts/rbac-context'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface PermissionButtonProps extends Omit<ButtonProps, 'onClick'> {
  module: string
  action: string
  membershipTypeId?: string
  onClick?: () => void | Promise<void>
  children: ReactNode
  showTooltip?: boolean
  tooltipContent?: string
  hideIfNoPermission?: boolean
}

export const PermissionButton = ({
  module,
  action,
  membershipTypeId,
  onClick,
  children,
  showTooltip = true,
  tooltipContent,
  hideIfNoPermission = false,
  disabled,
  ...buttonProps
}: PermissionButtonProps) => {
  const { getCachedPermission, loading } = useRBACContext()
  
  // Use cached permissions for immediate response
  const allowed = getCachedPermission(module, action)

  const handleClick = () => {
    if (allowed && onClick) {
      onClick()
    }
  }

  const isDisabled = loading || disabled || !allowed
  const shouldHide = hideIfNoPermission && !allowed && !loading

  if (shouldHide) {
    return null
  }

  const button = (
    <Button
      {...buttonProps}
      disabled={isDisabled}
      onClick={handleClick}
    >
      {loading ? 'Loading...' : children}
    </Button>
  )

  if (showTooltip && !allowed && !loading) {
    const tooltip = tooltipContent || `You don't have permission to ${action.toLowerCase()} ${module.toLowerCase()}`
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {button}
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return button
}

// Specialized permission buttons for common actions
interface CreateButtonProps extends Omit<PermissionButtonProps, 'action'> {
  module: string
}

export const CreateButton = ({ module, children = 'Create', ...props }: CreateButtonProps) => (
  <PermissionButton action="CREATE" module={module} {...props}>
    {children}
  </PermissionButton>
)

export const EditButton = ({ module, children = 'Edit', ...props }: CreateButtonProps) => (
  <PermissionButton action="UPDATE" module={module} {...props}>
    {children}
  </PermissionButton>
)

export const DeleteButton = ({ module, children = 'Delete', ...props }: CreateButtonProps) => (
  <PermissionButton 
    action="DELETE" 
    module={module} 
    variant="destructive"
    {...props}
  >
    {children}
  </PermissionButton>
)

export const ExportButton = ({ module, children = 'Export', ...props }: CreateButtonProps) => (
  <PermissionButton action="EXPORT" module={module} {...props}>
    {children}
  </PermissionButton>
)

export const ApproveButton = ({ module, children = 'Approve', ...props }: CreateButtonProps) => (
  <PermissionButton action="APPROVE" module={module} {...props}>
    {children}
  </PermissionButton>
)

export const ReviewButton = ({ module, children = 'Review', ...props }: CreateButtonProps) => (
  <PermissionButton action="REVIEW" module={module} {...props}>
    {children}
  </PermissionButton>
)