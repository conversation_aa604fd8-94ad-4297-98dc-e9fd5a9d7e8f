import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Member } from "@/types/members/member-details"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExternalLink } from 'lucide-react'
import { formatDate } from "@/lib/utils"

interface ApplicationHistoryProps {
  applications: Member['applications']
}

const getStatusVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case 'approved':
      return 'success'
    case 'rejected':
      return 'destructive'
    case 'submitted_pending_payment':
      return 'secondary'
    case 'paid_pending_review':
      return 'outline'
    default:
      return 'secondary'
  }
}

export function ApplicationHistory({ applications }: ApplicationHistoryProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Application History</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Reference No</TableHead>
              <TableHead>Comments</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {applications.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center">
                  No application history found
                </TableCell>
              </TableRow>
            ) : (
              applications.map((application) => (
                <TableRow key={application.id}>
                  <TableCell>{application.submitted_at ? formatDate(application.submitted_at) : '--'}</TableCell>
                  <TableCell className="capitalize">
                    {application.type?.toLowerCase()}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={application.status ? getStatusVariant(application.status) : 'secondary'}
                      className="rounded-full"
                    >
                      {application.status?.replace(/_/g, ' ') || '--'}
                    </Badge>
                  </TableCell>
                  <TableCell>{application.reference_no || '--'}</TableCell>
                  <TableCell>{application.comment || '--'}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(`/applications/${application.id}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

