'use server';

import { createClient } from '@/utils/supabase/server'

export async function getApplicationHistory(membership_id: string) {
    const supabase = await createClient();

    try {
        // Fetch membership data
        const { data: membershipData, error: membershipError } = await supabase
            .from("memberships")
            .select("id, user_id, membership_type")
            .eq("id", membership_id)
            .single();

        if (membershipError || !membershipData) {
            throw new Error(
                `Error fetching membership data: ${membershipError?.message || "No membership found"}`
            );
        }

        const { id, user_id, membership_type } = membershipData;

        // Fetch new application data
        const { data: newApplicationData, error: newApplicationError } = await supabase
            .from("applications")
            .select("id, status, type, submitted_at, approved_or_rejected_at")
            .match({
                user_id,
                membership_type,
                type: "NEW",
            })
            .neq("status", "DRAFT")
            .single();

        if (newApplicationError) {
            throw new Error(
                `Error fetching new application data: ${newApplicationError.message}`
            );
        }

        if (newApplicationData != null) {
            // Fetch application history
            const { data: applicationHistory, error: applicationHistoryError } = await supabase
                .from("applications")
                .select("id, status, type, submitted_at, approved_or_rejected_at")
                .eq("membership_id", id)
                .neq("status", "DRAFT");

            if (applicationHistoryError) {
                throw new Error(
                    `Error fetching application history: ${applicationHistoryError.message}`
                );
            }

            // Combine new application data with application history
            const combinedData = [
                ...(newApplicationData ? [newApplicationData] : []),
                ...(applicationHistory || []),
            ];

            return combinedData;
        }

        return newApplicationData;
    } catch (error) {
        console.error("Unexpected error in getApplicationHistory:", error);
        throw error;
    }
}

export async function getDocuments(membership_id?: string, application_id?: string) {
    const supabase = await createClient();
    try {
        let profileId: string;

        // Fetch profile_id either from membership or application
        if (membership_id) {
            const { data: membershipData, error: membershipError } = await supabase
                .from("memberships")
                .select("profile_id")
                .eq("id", membership_id)
                .single();

            if (membershipError || !membershipData) {
                throw new Error(
                    `Error fetching membership data: ${membershipError?.message || "No membership found"}`
                );
            }
            profileId = membershipData.profile_id;
        } else if (application_id) {
            const { data: applicationData, error: applicationError } = await supabase
                .from("applications")
                .select("profile_id")
                .eq("id", application_id)
                .single();

            if (applicationError || !applicationData) {
                throw new Error(
                    `Error fetching application data: ${applicationError?.message || "No application found"}`
                );
            }
            profileId = applicationData.profile_id;
        } else {
            throw new Error("Either membership_id or application_id must be provided");
        }

        // Fetch all required data
        const [
            { data: documentData, error: documentError },
            { data: applicationData, error: applicationError },
            { data: declarationData, error: declarationError }
        ] = await Promise.all([
            supabase
                .from("documents")
                .select("upload_date, path, type")
                .eq("profile_id", profileId),
            supabase
                .from("applications")
                .select("submitted_at, submitted_applicant_form_path, submitted_pe_form_path, submitted_sponsorship_form_path")
                .eq("profile_id", profileId)
                .single(),
            supabase
                .from("declarations")
                .select("date, form_path")
                .eq("profile_id", profileId)
        ]);

        // Check for errors
        if (documentError) throw new Error(`Error fetching documents data: ${documentError.message}`);
        if (applicationError) throw new Error(`Error fetching application data: ${applicationError.message}`);
        if (declarationError) throw new Error(`Error fetching declaration data: ${declarationError.message}`);

        // Helper function to get public URL
        const getPublicUrl = (path: string | null | undefined) => {
            if (!path) return undefined;
            // Remove bucket name from path if it exists
            const cleanPath = path.replace('application_documents/', '');
            const { data } = supabase.storage.from('application_documents').getPublicUrl(cleanPath);
            return data.publicUrl;
        };

        // Construct the consolidated documents array
        const docs = [
            {
                type: 'Application Form',
                path: getPublicUrl(applicationData?.submitted_applicant_form_path),
                date: applicationData?.submitted_at
                    ? new Date(applicationData.submitted_at).toISOString()
                    : undefined,
            },
            {
                type: 'PE Form',
                path: getPublicUrl(applicationData?.submitted_pe_form_path),
                date: applicationData?.submitted_at
                    ? new Date(applicationData.submitted_at).toISOString()
                    : undefined,
            },
            {
                type: 'Sponsorship Form',
                path: getPublicUrl(applicationData?.submitted_sponsorship_form_path),
                date: applicationData?.submitted_at
                    ? new Date(applicationData.submitted_at).toISOString()
                    : undefined,
            },
            ...(declarationData?.map((declaration, index) => ({
                type: `Declaration Form ${index + 1}`,
                path: getPublicUrl(declaration.form_path),
                date: declaration.date ? new Date(declaration.date).toISOString() : undefined,
            })) || []),
            ...(documentData?.map((doc, index) => ({
                type: doc.type || `Document ${index + 1}`,
                path: getPublicUrl(doc.path),
                date: doc.upload_date ? new Date(doc.upload_date).toISOString() : undefined,
            })) || []),
        ].filter(doc => doc.path);

        return docs;
    } catch (error) {
        console.error("Unexpected error in getDocuments:", error);
        throw error;
    }
}