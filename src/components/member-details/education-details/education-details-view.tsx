"use client"

import { DetailViewWrapper } from "@/components/member-details/common/detail-view-wrapper"
import type { EducationDetails } from "@/types/members/member-details"

interface EducationDetailsViewProps {
  data: EducationDetails
  onEdit?: () => void
  allowEdit?: boolean
}

export function EducationDetailsView({ data, onEdit, allowEdit = true }: EducationDetailsViewProps) {
  const instituteName = data?.institute.name ? data?.institute.name : data.other_institute
  const instituteCountry = data.other_institute ? data?.institute.country : 'Other'
  const courseName = data?.instituteCourse.name ? data?.instituteCourse.name : data.other_course
  const courseCategory = data.other_course ? data?.instituteCourse.category : 'Other'

  return (
    <DetailViewWrapper title={instituteName || "--"} onEdit={onEdit} allowEdit={allowEdit}>
      <div className="grid gap-2 md:grid-cols-2">
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Institute Name</p>
          <p className="text-sm text-muted-foreground">{instituteName || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Institute Country</p>
          <p className="text-sm text-muted-foreground">{instituteCountry || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Course Name</p>
          <p className="text-sm text-muted-foreground">{courseName || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Course Category</p>
          <p className="text-sm text-muted-foreground">{courseCategory || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Study Period</p>
          <p className="text-sm text-muted-foreground">{data?.period_from && data?.period_to ? `${data.period_from} - ${data.period_to}` : "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Date Of Graduation</p>
          <p className="text-sm text-muted-foreground">{data?.date_of_graduation || "--"}</p>
        </div>
      </div>
    </DetailViewWrapper>

  )
} 