"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import type { Member, EducationDetails } from "@/types/members/member-details"
import { EducationDetailsView } from "@/components/member-details/education-details/education-details-view"
import { EducationForm } from "@/components/member-details/education-details/education-form"
import { deleteEducation } from "@/services/members/member-server.service"

interface EducationDetailsProps {
  member: Member
  onUpdate: (updatedMember: Member) => void
  allowEdit?: boolean
}

export function EducationDetails({ member, onUpdate, allowEdit = true }: EducationDetailsProps) {
  const [selectedEducation, setSelectedEducation] = useState<EducationDetails | null>(null)
  const [mode, setMode] = useState<'view' | 'edit' | 'add' | null>(null)
  const isSheetOpen = mode !== null

  const handleAdd = () => {
    setSelectedEducation(null)
    setMode('add')
  }

  const handleView = (data: EducationDetails) => {
    setSelectedEducation(data)
    setMode('view')
  }

  const handleSave = async (updatedEducation: EducationDetails) => {
    onUpdate({
      ...member,
      education: member.education.some((edu) => edu.id === updatedEducation.id)
        ? member.education.map((edu) => edu.id === updatedEducation.id ? updatedEducation : edu
        ) : [...member.education, updatedEducation],
    })

    setSelectedEducation(null)
    setMode(null)
  }

  const handleDelete = async (id: string) => {
    const result = await deleteEducation(id)
    if (result.error) {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Success",
      description: "Education delete successfully",
    })
    onUpdate({
      ...member,
      education: member.education.filter((edu) => edu.id !== id),
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Education</CardTitle>
          <Button variant="ghost" onClick={handleAdd}>Add</Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Institute Name</TableHead>
                <TableHead>Course Name</TableHead>
                <TableHead>Period</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {member.education && member.education.length > 0 ? (
                member.education.map((edu, index) => (
                  <TableRow key={`education-${index}`}>
                    <TableCell>{edu.institute.name ?? edu.other_institute}</TableCell>
                    <TableCell>{edu.instituteCourse.name ?? edu.other_course}</TableCell>
                    <TableCell>
                      {(edu.period_from && edu.period_to)
                        ? `${edu.period_from} - ${edu.period_to}`
                        : edu.period_from
                          ? `From ${edu.period_from}`
                          : edu.period_to
                            ? `Until ${edu.period_to}`
                            : '--'}
                    </TableCell>
                    <TableCell className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(edu)}
                      >
                        View
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Are you absolutely sure?</DialogTitle>
                            <DialogDescription>
                              This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button type="button" variant="secondary">
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button variant="destructive" type="submit" onClick={() => handleDelete(edu.id)}>Delete</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    No education available.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Sheet open={isSheetOpen} onOpenChange={(open) => !open && setMode(null)}>
        <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
          <SheetHeader>
            <SheetTitle>Education Details</SheetTitle>
          </SheetHeader>
          <div className="overflow-auto">
            {(selectedEducation && mode === 'view') ? (
              <EducationDetailsView
                data={selectedEducation}
                onEdit={() => setMode('edit')}
                allowEdit={allowEdit}
              />
            ) : (mode === 'add' || mode === 'edit') ? (
              <EducationForm
                data={selectedEducation}
                profileId={member.profileId}
                onSave={handleSave}
                onCancel={() => setMode(null)}
              />
            ) : null}
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
} 