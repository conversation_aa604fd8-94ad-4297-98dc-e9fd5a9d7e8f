"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { CalendarIcon } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import type { EducationDetails } from "@/types/members/member-details"
import { useSystemOptions } from "@/hooks/use-system-options"
import { InstituteCourseOption } from "@/types/members/options"
import { updateEducation } from "@/services/members/member-server.service"
import { toast } from "@/hooks/use-toast"

interface EducationFormProps {
  data: EducationDetails | null
  profileId: string
  onSave: (data: EducationDetails) => void
  onCancel: () => void
}

const educationFormSchema = z.object({
  id: z.string().optional(),
  periodFrom: z.date().optional(),
  periodTo: z.date().optional(),
  learningInstitute: z.string().min(1, "Learning Institute is required"),
  learningInstituteOthers: z
    .string()
    .optional()
    .refine((value) => !value || value.length <= 250, {
      message: "Must be 250 characters or less",
    }),
  courseOfStudy: z.string().min(1, "Course of Study is required"),
  courseOfStudyOthers: z
    .string()
    .optional()
    .refine((value) => !value || value.length <= 250, {
      message: "Must be 250 characters or less",
    }),
  dateOfGraduation: z.date().optional(),
  nomineeId: z.string().optional(),
}).superRefine((data, ctx) => {
  if (!data.periodFrom) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Period From is required.",
      path: ["periodFrom"],
    });
  }

  if (!data.periodTo) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Period To is required.",
      path: ["periodTo"],
    });
  }

  if (!data.dateOfGraduation) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Date Of Graduation is required.",
      path: ["dateOfGraduation"],
    });
  }

  if (data.courseOfStudy === "other" && (!data.courseOfStudyOthers || data.courseOfStudyOthers.trim().length === 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Please specify the Course of Study.",
      path: ["courseOfStudyOthers"],
    });
  }

  if (data.learningInstitute === "other" && (!data.learningInstituteOthers || data.learningInstituteOthers.trim().length === 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Please specify the Learning Institute Attended.",
      path: ["learningInstituteOthers"],
    });
  }
});

export type EducationFormData = z.infer<typeof educationFormSchema>;

export function EducationForm({ data, profileId, onSave, onCancel }: EducationFormProps) {
  const form = useForm<EducationFormData>({
    resolver: zodResolver(educationFormSchema),
    mode: "onChange",
    defaultValues: {
      id: data?.id || "",
      periodFrom: data?.period_from ? new Date(data.period_from) : undefined,
      periodTo: data?.period_to ? new Date(data?.period_to) : undefined,
      learningInstitute: data?.other_institute ? "other" : data?.institute_id || "",
      learningInstituteOthers: data?.other_institute || undefined,
      courseOfStudy: data?.other_course ? "other" : data?.icourse_id || "",
      courseOfStudyOthers: data?.other_course || undefined,
      dateOfGraduation: data?.date_of_graduation ? new Date(data.date_of_graduation) : undefined,
    },
  })
  const { options, isLoading, error } = useSystemOptions()
  const [selectedInstitute, setSelectedInstitute] = useState<{
    name?: string;
    country?: string;
  } | null>(data ? {
    name: data.institute.name ?? undefined,
    country: data.institute.country ?? undefined
  } : null);

  const [selectedInstituteCourse, setSelectedInstituteCourse] = useState<{
    name?: string;
    category?: string;
  } | null>(data ? {
    name: data.instituteCourse.name ?? undefined,
    category: data.instituteCourse.category ?? undefined
  } : null);

  const onSubmit = async (formData: EducationFormData) => {
    const result = await updateEducation(profileId, formData)
    if (result.success && result.newEduId) {
      toast({
        title: "Success",
        description: "Education updated successfully",
      })
      const formattedData = {
        id: result.newEduId,
        profile_id: profileId,
        institute_id: formData.learningInstitute,
        icourse_id: formData.courseOfStudy,
        other_institute: formData.learningInstituteOthers ?? null,
        other_course: formData.courseOfStudyOthers ?? null,
        period_from: formData.periodFrom?.toISOString().slice(0, 10) || null,
        period_to: formData.periodTo?.toISOString().slice(0, 10) || null,
        date_of_graduation: formData.dateOfGraduation?.toISOString().slice(0, 10) || null,
        nominee_id: null,
        institute: {
          name: selectedInstitute?.name,
          country: selectedInstitute?.country
        },
        instituteCourse: {
          name: selectedInstituteCourse?.name,
          category: selectedInstituteCourse?.category
        }
      }
      onSave(formattedData);
    } else {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center p-6">
          <LoadingSpinner />
        </CardContent>
      </Card>
    )
  }

  if (error || !options) {
    return (
      <Card>
        <CardContent className="text-center text-red-500 p-6">
          Failed to load form options
        </CardContent>
      </Card>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`periodFrom`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Period From</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                        endMonth={new Date()}
                        disabled={[{ after: new Date() }]}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`periodTo`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Period To</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`learningInstitute`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Learning Institute Attended</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      const selected = options?.institute.find(course => course.id === value);
                      if (selected) {
                        setSelectedInstitute({
                          name: selected.name,
                          country: selected.country,
                        });
                      } else {
                        setSelectedInstitute(null);
                      }
                    }}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Institute" />
                    </SelectTrigger>
                    <SelectContent>
                      {options?.institute.map((institute) => (
                        <SelectItem key={institute.id} value={institute.id}>
                          {institute.name}
                        </SelectItem>
                      ))}
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`courseOfStudy`}
            render={({ field }) => {

              // Group filtered courses by category
              const coursesByCategory = (options?.instituteCourse ?? []).reduce((acc, course) => {
                const category = course.category ?? "Uncategorized";
                if (!acc[category]) {
                  acc[category] = [];
                }
                acc[category].push(course);
                return acc;
              }, {} as Record<string, InstituteCourseOption[]>);

              return (
                <>
                  <FormItem>
                    <FormLabel>Course of Study</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          const selected = options?.instituteCourse.find((course) => course.id === value);
                          if (selected) {
                            setSelectedInstituteCourse({
                              name: selected.name,
                              category: selected.category,
                            });
                          } else {
                            setSelectedInstituteCourse(null);
                          }
                        }}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Course" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(coursesByCategory).map(([category, courses]) => (
                            <SelectGroup key={category}>
                              <SelectLabel>{category}</SelectLabel>
                              {courses.map((course) => (
                                <SelectItem key={course.id} value={course.id}>
                                  {course.name}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          ))}
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </>
              );
            }}
          />

          {form.watch('learningInstitute') === "other" && (
            <FormField
              control={form.control}
              name={`learningInstituteOthers`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Learning Institute Attended (Other)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter Learning Institute Attended (Other)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {form.watch('courseOfStudy') === "other" && (
            <FormField
              control={form.control}
              name={`courseOfStudyOthers`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Course of Study (Other)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter Course Of Study (Other)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name={`dateOfGraduation`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date Of Graduation</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  )
} 