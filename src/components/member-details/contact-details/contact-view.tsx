"use client"

import { useEffect, useState } from "react"
import type { PersonalInfo } from "@/types/members/member-details"
import { getOptionLabel } from "@/services/system-options.service"

interface ContactDetailsDisplayProps {
    personalInfo: PersonalInfo
}

export function ContactDetailsDisplay({ personalInfo }: ContactDetailsDisplayProps) {
    const [addressCountry, setAddressCountry] = useState('--')

    const address = personalInfo.address

    useEffect(() => {
        const fetchCountryLabel = async () => {
            if (address?.country) {
                const label = await getOptionLabel(address.country, 'country')
                setAddressCountry(label)
            }
        }

        fetchCountryLabel()
    }, [address?.country])

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Phone Number</p>
                <p className="text-sm text-muted-foreground">{personalInfo.home_number || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Mobile Number</p>
                <p className="text-sm text-muted-foreground">{personalInfo.mobile_number || '--'}</p>
            </div>
            <div className="space-y-1 md:col-span-2">
                <p className="text-sm font-medium leading-none">Email</p>
                <p className="text-sm text-muted-foreground">{personalInfo.email || '--'}</p>
            </div>
            <div className="space-y-1 md:col-span-2">
                <p className="text-sm font-medium leading-none">Address</p>
                <p className="text-sm text-muted-foreground">
                    {address.line1 || '--'}
                    {address.line2 && `, ${address.line2}`}
                    <br />
                    {address.city || '--'}, {address.state || '--'} {address.postalCode || '--'}
                    <br />
                    {addressCountry}
                </p>
            </div>
        </div>
    )
}
