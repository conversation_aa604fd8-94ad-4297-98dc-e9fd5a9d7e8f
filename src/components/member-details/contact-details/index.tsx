"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import type { Member, PersonalInfo } from "@/types/members/member-details"
import { ContactDetailsForm } from "@/components/member-details/contact-details/contact-form"
import { ContactDetailsDisplay } from "@/components/member-details/contact-details/contact-view"

interface ContactDetailsProps {
    member: Member
    onUpdate: (updatedMember: Member) => void
    allowEdit?: boolean
}

export function ContactDetails({ member, onUpdate, allowEdit }: ContactDetailsProps) {
    const [isEditing, setisEditing] = useState(false)

    const onSave = async (updatedContactDetails: Partial<PersonalInfo>) => {
        onUpdate({
            ...member,
            personalInfo: {
                ...member.personalInfo,
                ...updatedContactDetails
            }
        })
        setisEditing(false)
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Contact Details</CardTitle>
                {(!isEditing && allowEdit) && (
                    <div className="space-x-2">
                        <Button variant="ghost" onClick={() => setisEditing(true)}>
                            Edit
                        </Button>
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {isEditing ? (
                    <ContactDetailsForm
                        data={member.personalInfo}
                        profileId={member.profileId}
                        onSave={onSave}
                        onCancel={() => setisEditing(false)}
                    />
                ) : (
                    <ContactDetailsDisplay personalInfo={member.personalInfo} />
                )}
            </CardContent>
        </Card>
    )
}

