"use client"

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronDown } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import type { PersonalInfo } from "@/types/members/member-details"
import { Input } from "@/components/ui/input"
import { useSystemOptions } from "@/hooks/use-system-options"
import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { updateContactDetails } from "@/services/members/member-server.service"
import { isValidPhoneNumber } from "react-phone-number-input"
import { PhoneInput } from "@/components/ui/phone-input"

interface contactDetailsFormProps {
    data: PersonalInfo | null
    profileId: string
    onSave: (data: Partial<PersonalInfo>) => void
    onCancel: () => void
}

const addressSchema = z.object({
    line1: z.string().min(1, 'Address Line 1 is required'),
    line2: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postalCode: z.string().min(1, 'Postal Code is required'),
    country: z.string().min(1, 'Country is required')
})

const contactDetailsSchema = z.object({
    phone: z
        .string()
        .refine(isValidPhoneNumber, { message: "Invalid phone number format" })
        .or(z.literal("")),
    mobile: z
        .string()
        .min(1, "Mobile Number is required")
        .refine(isValidPhoneNumber, { message: "Invalid mobile number format" }),
    email: z.coerce
        .string()
        .min(1, "Email is required")
        .refine(
            (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
            "Invalid email address"
        ),
    address_id: z.string().optional(),
    address: addressSchema
})

export type ContactDetailsFormData = z.infer<typeof contactDetailsSchema>

export function ContactDetailsForm({ data, profileId, onSave, onCancel }: contactDetailsFormProps) {
    const form = useForm<ContactDetailsFormData>({
        resolver: zodResolver(contactDetailsSchema),
        mode: "onChange",
        defaultValues: {
            phone: data?.home_number || "",
            mobile: data?.mobile_number || "",
            email: data?.email || "",
            address_id: data?.address_id || "",
            address: {
                line1: data?.address.line1 || "",
                line2: data?.address.line2 || "",
                city: data?.address.city || "",
                state: data?.address.state || "",
                postalCode: data?.address.postalCode || "",
                country: data?.address.country || ""
            }
        },
    })
    const { options, isLoading, error } = useSystemOptions()
    const [searchTerm, setSearchTerm] = useState("")

    const filteredCountries = options?.countries.filter((country) =>
        country.label.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const onSubmit = async (formData: ContactDetailsFormData) => {
        const result = await updateContactDetails(profileId, formData)
        if (result.success && result.addressId) {
            toast({
                title: "Success",
                description: "Contact Detials updated successfully",
            })
            const formattedValue = {
                ...data,
                home_number: formData.phone,
                mobile_number: formData.mobile,
                email: formData.email,
                address_id: result.addressId,
                address: {
                    line1: formData.address.line1,
                    line2: formData.address.line2 || null,
                    city: formData.address.city || null,
                    state: formData.address.state || null,
                    postalCode: formData.address.postalCode,
                    country: formData.address.country,
                }
            }
            onSave(formattedValue)
        } else {
            toast({
                title: "Error",
                description: result.error,
                variant: "destructive",
            })
        }
    }

    if (isLoading) {
        return (
            <Card>
                <CardContent className="flex justify-center items-center p-6">
                    <LoadingSpinner />
                </CardContent>
            </Card>
        )
    }

    if (error || !options) {
        return (
            <Card>
                <CardContent className="text-center text-red-500 p-6">
                    Failed to load form options
                </CardContent>
            </Card>
        )
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Phone Number</FormLabel>
                                <FormControl>
                                    <PhoneInput
                                        placeholder="Enter Telephone Number"
                                        international
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="mobile"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Mobile Number
                                </FormLabel>
                                <FormControl>
                                    <PhoneInput
                                        placeholder="Enter Mobile Number"
                                        international
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Personal Email

                                </FormLabel>
                                <FormControl>
                                    <Input
                                        type="email"
                                        placeholder="Enter Personal Email"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="address.line1"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Address Line 1</FormLabel>
                                <FormControl>
                                    <Input {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.line2"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Address Line 2</FormLabel>
                                <FormControl>
                                    <Input {...field} value={field.value ?? ""} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.city"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>City</FormLabel>
                                <FormControl>
                                    <Input {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.state"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>State</FormLabel>
                                <FormControl>
                                    <Input {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.postalCode"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Postal Code</FormLabel>
                                <FormControl>
                                    <Input {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.country"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Country</FormLabel>
                                <Popover modal>
                                    <PopoverTrigger asChild>
                                        <FormControl>
                                            <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                                                <span className="truncate">
                                                    {field.value
                                                        ? options.countries.find((c) => c.code === field.value)?.label
                                                        : "Select Country"}
                                                </span>
                                                <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                                            </Button>
                                        </FormControl>
                                    </PopoverTrigger>
                                    <PopoverContent className="p-0">
                                        <Command>
                                            <CommandInput
                                                placeholder="Search Country..."
                                                value={searchTerm}
                                                onInput={(e) => setSearchTerm(e.currentTarget.value)}
                                            />
                                            <CommandList>
                                                {filteredCountries?.length === 0 ? (
                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                ) : (
                                                    <CommandGroup>
                                                        {filteredCountries?.map((country) => (
                                                            <CommandItem
                                                                key={country.code}
                                                                value={country.label}
                                                                onSelect={() => field.onChange(country.code)}
                                                            >
                                                                {country.label}
                                                                <Check
                                                                    className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                                                        }`}
                                                                />
                                                            </CommandItem>
                                                        ))}
                                                    </CommandGroup>
                                                )}
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                </div>
            </form>
        </Form>
    )
} 