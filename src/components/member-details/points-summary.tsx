import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Mock data for points summary
const pointsSummary = [
  { type: "PDU (PEB)", points: 20 },
  { type: "CPD (BOA)", points: 15 },
  { type: "STU (Safety)", points: 10 },
  { type: "STU (FI)", points: 5 },
  { type: "CPD (SILA)", points: 8 },
  { type: "CET Hours", points: 12 },
  { type: "PDU", points: 18 },
  { type: "SDU", points: 7 },
  { type: "PDU (SCEM)", points: 14 },
  { type: "PDU (CEng)", points: 22 },
  { type: "STU (M & E)", points: 9 },
  { type: "PDU (IPTC)", points: 11 },
  { type: "STU (LEI)", points: 6 },
  { type: "PDU (QECP)", points: 13 },
  { type: "STU (Structural)", points: 16 },
  { type: "PDU (ABC)", points: 19 },
]

export function PointsSummary() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Points Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="border-[0.5px] border-border rounded-md overflow-hidden">
          <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 [&>*]:border-[0.5px] [&>*]:border-border">
            {pointsSummary.map((point, index) => (
              <div 
                key={index} 
                className="flex justify-between items-center p-2"
              >
                <span className="font-medium">{point.type}</span>
                <span className="font-semibold">{point.points}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

