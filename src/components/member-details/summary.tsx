import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { AvatarPlaceholder } from "@/components/ui/avatar-placeholder"
import { type Member } from "@/types/members/member-details"
import { getOptionLabel } from "@/services/system-options.service"

interface SummaryProps {
  member: Member
}

// Helper function to get appropriate color for status
const getStatusColor = (status?: string | null): string => {
  if (!status) return 'text-gray-500';

  switch (status.toUpperCase()) {
    case "ACTIVE":
      return 'text-green-600';
    case "INACTIVE":
      return 'text-yellow-500';
    case "SUSPENDED":
      return 'text-red-500';
    case "EXPIRED":
      return 'text-gray-500';
    case "TERMINATED":
      return 'text-red-700';
    case "PENDING_RENEWAL":
      return 'text-amber-500';
    case "PENDING_RE_APPLICATION":
      return 'text-purple-500';
    default:
      return 'text-blue-500';
  }
};

// Helper function to format the status text
export const getFormattedStatus = (status?: string | null): string => {
  if (!status) return 'Unknown';

  // Capitalize first letter of each word
  return status.split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

export function Summary({ member }: SummaryProps) {
  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '--';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Determine if this is a membership or registry based on group
  const isMembership = member.memberships?.membershipType?.group === 'MBR';
  const cardTitle = isMembership ? 'Membership Information' : 'Registry Information';
  const typeLabel = isMembership ? 'Membership Type' : 'Registry Type';
  const idLabel = isMembership ? 'Member ID' : 'Registry ID';

  const [nationality, setNationality] = useState('--')

  useEffect(() => {
    const fetchCountryLabel = async () => {
      if (member.personalInfo.nationality) {
        const label = await getOptionLabel(member.personalInfo.nationality, 'country')
        setNationality(label)
      }
    }

    fetchCountryLabel()
  }, [member.personalInfo.nationality])

  return (
    <div className="grid gap-6 lg:grid-cols-3 md:grid-cols-2">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start space-x-6">
            <AvatarPlaceholder size="md" />
            <div className="grid gap-y-3 flex-1">
              <div className="grid grid-cols-2">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">
                  Mobile Number
                </span>
                <span
                  className="text-sm text-gray-900 font-semibold break-words text-right"
                  title={member.personalInfo.mobile_number || '--'}
                >
                  {member.personalInfo.mobile_number || '--'}
                </span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">
                  Email
                </span>
                <span
                  className="text-sm text-gray-900 font-semibold break-words text-right"
                  title={member.personalInfo.email || '--'}
                >
                  {member.personalInfo.email || '--'}
                </span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">
                  Organization
                </span>
                <span
                  className="text-sm text-gray-900 font-semibold break-words text-right"
                  title={member.employment?.company.name || '--'}
                >
                  {member.employment?.company.name || '--'}
                </span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">
                  Nationality
                </span>
                <span
                  className="text-sm text-gray-900 font-semibold break-words text-right"
                  title={nationality}
                >
                  {nationality}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{cardTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-y-3">
            <div className="grid grid-cols-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">Type</span>
              <span className="text-sm text-gray-900 font-semibold break-words text-right">
                {member.memberships?.membershipType?.name || '--'}
              </span>
            </div>
            <div className="grid grid-cols-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">{idLabel}</span>
              <span className="text-sm text-gray-900 font-semibold break-words text-right">
                {member.memberships?.membership_number || '--'}
              </span>
            </div>
            <div className="grid grid-cols-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">Status</span>
              <span className={`text-sm font-bold break-words text-right ${getStatusColor(member.memberships?.status)}`}>
                {getFormattedStatus(member.memberships?.status)}
              </span>
            </div>
            <div className="grid grid-cols-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">Member Since</span>
              <span className="text-sm text-gray-900 font-semibold break-words text-right">
                {formatDate(member.memberships?.member_since)}
              </span>
            </div>
            <div className="grid grid-cols-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">Start Date</span>
              <span className="text-sm text-gray-900 font-semibold break-words text-right">
                {formatDate(member.memberships?.start_date)}
              </span>
            </div>
            <div className="grid grid-cols-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide whitespace-nowrap">End Date</span>
              <span className="text-sm text-gray-900 font-semibold break-words text-right">
                {formatDate(member.memberships?.end_date)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Account Information</CardTitle>
          {member.accountInfo?.hasAccount && (
            <Button variant="outline" size="sm">
              Reset Password
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {!member.accountInfo?.hasAccount ? (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <p className="text-sm text-muted-foreground mb-2">No User Account</p>
              <p className="text-xs text-gray-500">This member does not have a portal account yet.</p>
            </div>
          ) : (
            <div className="grid gap-y-3">
              <div className="grid grid-cols-2">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Portal Account Status</span>
                <span className={`text-sm font-bold break-words text-right ${
                  member.accountInfo.status ? 'text-green-600' : 'text-red-600'
                }`}>
                  {member.accountInfo.status ? 'Active' : 'Inactive'}
                </span>
              </div>
              {member.accountInfo.email && (
                <div className="grid grid-cols-2">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Account Email</span>
                  <span className="text-sm text-gray-900 font-semibold break-words text-right">
                    {member.accountInfo.email}
                  </span>
                </div>
              )}
              <div className="grid grid-cols-2">
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Last Login</span>
                <span className="text-sm text-gray-900 font-semibold break-words text-right">
                  {member.accountInfo.lastSignInAt 
                    ? new Date(member.accountInfo.lastSignInAt).toLocaleString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                      })
                    : 'Never'
                  }
                </span>
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Recent Login History</p>
                <div className="flex justify-center items-center py-2">
                  <p className="text-sm text-gray-500">No login history available.</p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="lg:col-span-3 md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Activity</CardTitle>
          <Button variant="ghost" size="sm">
            View more
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-6">
            <p className="text-sm text-muted-foreground">No activity recorded yet.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}