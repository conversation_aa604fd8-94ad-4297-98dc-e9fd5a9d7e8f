"use client"

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns";
import { Button } from "@/components/ui/button"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command"
import { Input } from "@/components/ui/input"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { CalendarIcon, Check, ChevronDown } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import type { EmploymentDetails } from "@/types/members/member-details"

import { isValidPhoneNumber } from "react-phone-number-input";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { PhoneInput } from "@/components/ui/phone-input";
import { useSystemOptions } from "@/hooks/use-system-options";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { updateEmploymentDetails } from "@/services/members/member-server.service";

interface employmentFormProps {
    data: EmploymentDetails | null
    profileId: string
    onSave: (data: EmploymentDetails) => void
    onCancel: () => void
}

const addressSchema = z.object({
    line1: z.string().min(1, 'Address Line 1 is required'),
    line2: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postalCode: z.string().min(1, 'Postal Code is required'),
    country: z.string().min(1, 'Country is required')
})

const optionalAddressSchema = z.object({
    line1: z.string().optional(),
    line2: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional()
})

const EmploymentSchema = z.object({
    id: z.string().optional(),
    designation: z.string().min(1, "Designation is required"),
    startDate: z.date().optional(),
    company_id: z.string().optional(),
    companyName: z.string().min(1, "Company name is required"),
    phone: z
        .string()
        .min(1, "Company contact number is required")
        .refine(isValidPhoneNumber, { message: "Invalid phone number format" }),
    email: z.coerce
        .string()
        .min(1, "Email is required")
        .refine(
            (val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
            "Invalid email address"
        ),
    address: addressSchema,
    isSameAddress: z.boolean(),
    preferredAddress: optionalAddressSchema
}).superRefine((data, ctx) => {
    if (!(data.startDate instanceof Date) || isNaN(data.startDate.getTime())) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Employment Start Date is required.",
            path: ["startDate"],
        });
    }

    if (!data.isSameAddress) {
        if (!data.preferredAddress?.line1) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Address line 1 is required",
                path: ["preferredAddress", "line1"],
            });
        }
        if (!data.preferredAddress?.postalCode) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Postal code is required",
                path: ["preferredAddress", "postalCode"],
            });
        }
        if (!data.preferredAddress?.country) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Country is required",
                path: ["preferredAddress", "country"],
            });
        }
    }
})

export type EmploymentsFormData = z.infer<typeof EmploymentSchema>;

export function EmploymentForm({ data, profileId, onSave, onCancel }: employmentFormProps) {
    const form = useForm<EmploymentsFormData>({
        resolver: zodResolver(EmploymentSchema),
        mode: "onChange",
        defaultValues: {
            id: data?.id || "",
            designation: data?.designation || "",
            startDate: data?.start_date ? new Date(data.start_date) : undefined,
            company_id: data?.company_id || "",
            companyName: data?.company.name || "",
            phone: data?.company.phone || "",
            email: data?.company.email || "",
            address: {
                line1: data?.company.address.line1 || "",
                line2: data?.company.address.line2 || "",
                city: data?.company.address.city || "",
                state: data?.company.address.state || "",
                postalCode: data?.company.address.postalCode || "",
                country: data?.company.address.country || ""
            },
            isSameAddress: data?.company.isSameAddress ?? false,
            preferredAddress: {
                line1: data?.company.preferredAddress?.line1 || "",
                line2: data?.company.preferredAddress?.line2 || "",
                city: data?.company.preferredAddress?.city || "",
                state: data?.company.preferredAddress?.state || "",
                postalCode: data?.company.preferredAddress?.postalCode || "",
                country: data?.company.preferredAddress?.country || ""
            }
        },
    })
    const { options, isLoading, error } = useSystemOptions()
    const [searchTerm, setSearchTerm] = useState("")

    const filteredCountries = options?.countries.filter((country) =>
        country.label.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const onSubmit = async (formData: EmploymentsFormData) => {
        const { preferredAddress, ...rest } = formData;
        const cleanedFormData = {
            ...rest,
            preferredAddress: formData.isSameAddress
                ? {
                    line1: undefined,
                    line2: undefined,
                    city: undefined,
                    state: undefined,
                    postalCode: undefined,
                    country: undefined
                }
                : preferredAddress
        }

        const result = await updateEmploymentDetails(profileId, cleanedFormData)
        if (result.success && result.employmentId) {
            toast({
                title: "Success",
                description: "Employment updated successfully",
            })
            const formattedData = {
                id: result.employmentId.id,
                profile_id: profileId,
                company_id: result.employmentId.company_id,
                designation: formData.designation,
                start_date: formData.startDate?.toISOString().slice(0, 10) || null,
                company: {
                    name: formData.companyName,
                    phone: formData.phone,
                    email: formData.email,
                    isSameAddress: formData.isSameAddress,
                    address: {
                        line1: formData.address.line1,
                        line2: formData.address.line2,
                        city: formData.address.city,
                        state: formData.address.state,
                        postalCode: formData.address.postalCode,
                        country: formData.address.country
                    },
                    ...(formData.isSameAddress ? {}
                        : {
                            preferredAddress: {
                                line1: formData.preferredAddress?.line1 || "",
                                line2: formData.preferredAddress?.line2 || "",
                                city: formData.preferredAddress?.city || "",
                                state: formData.preferredAddress?.state || "",
                                postalCode: formData.preferredAddress?.postalCode || "",
                                country: formData.preferredAddress?.country || ""
                            }
                        })
                }
            }
            onSave(formattedData)
        } else {
            toast({
                title: "Error",
                description: result.error,
                variant: "destructive",
            })
        }
    }

    if (isLoading) {
        return (
            <Card>
                <CardContent className="flex justify-center items-center p-6">
                    <LoadingSpinner />
                </CardContent>
            </Card>
        )
    }

    if (error || !options) {
        return (
            <Card>
                <CardContent className="text-center text-red-500 p-6">
                    Failed to load form options
                </CardContent>
            </Card>
        )
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="mb-4 space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="companyName"
                            render={({ field }) => (
                                <FormItem className="sm:col-span-full">
                                    <FormLabel>
                                        Company Name
                                    </FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter Company Name"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="designation"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>
                                        Designation
                                    </FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter Designation"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="startDate"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>
                                        Employment Start Date
                                        
                                    </FormLabel>
                                    <FormControl>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <div className="relative">
                                                    <Input
                                                        type="text"
                                                        readOnly
                                                        className="pointer-events-none"
                                                        placeholder="Pick a date"
                                                        value={
                                                            field.value ? format(field.value, "PPP") : ""
                                                        }
                                                    />
                                                    <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                                </div>
                                            </PopoverTrigger>
                                            <PopoverContent>
                                                <Calendar
                                                    mode="single"
                                                    selected={field.value}
                                                    onSelect={(date) => {
                                                        if (date) {
                                                            const utcDate = new Date(
                                                                Date.UTC(
                                                                    date.getFullYear(),
                                                                    date.getMonth(),
                                                                    date.getDate()
                                                                )
                                                            );
                                                            field.onChange(utcDate);
                                                        }
                                                    }}
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>
                                        Tel(Office)
                                        
                                    </FormLabel>
                                    <FormControl>
                                        <PhoneInput
                                            placeholder="Enter Telephone Number"
                                            international
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>
                                        Company Email
                                    </FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter Company Email"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <div className="sm:col-span-full mt-2 bg-gray-100 p-2 rounded-md">
                        <h3 className="text-sm font-medium leading-6 text-gray-400">
                            Company Address
                        </h3>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="address.line1"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>
                                        Address line 1
                                    </FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter Address line 1"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="address.line2"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Address line 2</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter Address line 2"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="address.city"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>City</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter City"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="address.state"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>State / Region</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter State / Region"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="address.postalCode"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>
                                        Postal Code
                                    </FormLabel>
                                    <FormControl>
                                        <Input
                                            type="text"
                                            placeholder="Enter Postal Code"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="address.country"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Nationality</FormLabel>
                                    <Popover modal>
                                        <PopoverTrigger asChild>
                                            <FormControl>
                                                <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                                                    <span className="truncate">
                                                        {field.value
                                                            ? options.countries.find((c) => c.code === field.value)?.label
                                                            : "Select Nationality"}
                                                    </span>
                                                    <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                                                </Button>
                                            </FormControl>
                                        </PopoverTrigger>
                                        <PopoverContent className="p-0">
                                            <Command>
                                                <CommandInput
                                                    placeholder="Search nationality..."
                                                    value={searchTerm}
                                                    onInput={(e) => setSearchTerm(e.currentTarget.value)}
                                                />
                                                <CommandList>
                                                    {filteredCountries?.length === 0 ? (
                                                        <CommandEmpty>No country found.</CommandEmpty>
                                                    ) : (
                                                        <CommandGroup>
                                                            {filteredCountries?.map((country) => (
                                                                <CommandItem
                                                                    key={country.code}
                                                                    value={country.label}
                                                                    onSelect={() => field.onChange(country.code)}
                                                                >
                                                                    {country.label}
                                                                    <Check
                                                                        className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                                                            }`}
                                                                    />
                                                                </CommandItem>
                                                            ))}
                                                        </CommandGroup>
                                                    )}
                                                </CommandList>
                                            </Command>
                                        </PopoverContent>
                                    </Popover>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <FormField
                        control={form.control}
                        name="isSameAddress"
                        render={({ field }) => (
                            <FormItem className="flex items-end space-x-2">
                                <FormControl>
                                    <Checkbox
                                        id="terms"
                                        checked={field.value}
                                        onCheckedChange={(checked) =>
                                            field.onChange(Boolean(checked))
                                        }
                                    />
                                </FormControl>
                                <FormLabel
                                    htmlFor="terms"
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    Mailing Address is same as Company Address
                                </FormLabel>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {!form.watch("isSameAddress") && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div className="sm:col-span-full mt-2 bg-gray-100 p-2 rounded-md">
                                <h3 className="text-sm font-medium leading-6 text-gray-400">
                                    Prefered Mailing Address
                                </h3>
                            </div>
                            <FormField
                                control={form.control}
                                name="preferredAddress.line1"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>
                                            Street line 1
                                            
                                        </FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                placeholder="Enter Street line 1"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="preferredAddress.line2"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Street line 2</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                placeholder="Enter Street line 2"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="preferredAddress.city"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>City</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                placeholder="Enter City"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="preferredAddress.state"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>State / Region</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                placeholder="Enter State / Region"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="preferredAddress.postalCode"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>
                                            Postal Code
                                        </FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                placeholder="Enter Postal Code"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="preferredAddress.country"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Nationality</FormLabel>
                                        <Popover modal>
                                            <PopoverTrigger asChild>
                                                <FormControl>
                                                    <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                                                        <span className="truncate">
                                                            {field.value
                                                                ? options.countries.find((c) => c.code === field.value)?.label
                                                                : "Select Nationality"}
                                                        </span>
                                                        <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                                                    </Button>
                                                </FormControl>
                                            </PopoverTrigger>
                                            <PopoverContent className="p-0">
                                                <Command>
                                                    <CommandInput
                                                        placeholder="Search nationality..."
                                                        value={searchTerm}
                                                        onInput={(e) => setSearchTerm(e.currentTarget.value)}
                                                    />
                                                    <CommandList>
                                                        {filteredCountries?.length === 0 ? (
                                                            <CommandEmpty>No country found.</CommandEmpty>
                                                        ) : (
                                                            <CommandGroup>
                                                                {filteredCountries?.map((country) => (
                                                                    <CommandItem
                                                                        key={country.code}
                                                                        value={country.label}
                                                                        onSelect={() => field.onChange(country.code)}
                                                                    >
                                                                        {country.label}
                                                                        <Check
                                                                            className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                                                                }`}
                                                                        />
                                                                    </CommandItem>
                                                                ))}
                                                            </CommandGroup>
                                                        )}
                                                    </CommandList>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    )}
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                </div>
            </form>
        </Form>
    )
} 