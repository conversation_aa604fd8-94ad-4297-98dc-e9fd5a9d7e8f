"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { EmploymentForm } from "@/components/member-details/employment/employment-form"
import type { Member, EmploymentDetails } from "@/types/members/member-details"
import { EmploymentDetailsDisplay } from "@/components/member-details/employment/employment-view"

interface EmploymentDetailsProps {
    member: Member
    onUpdate: (updatedMember: Member) => void
    allowEdit?: boolean
}

export function EmploymentDetails({ member, onUpdate, allowEdit }: EmploymentDetailsProps) {
    const [isEditing, setisEditing] = useState(false)

    const handleSave = (updatedEmployment: EmploymentDetails) => {
        onUpdate({
            ...member,
            employment: updatedEmployment
        })
        setisEditing(false)
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Employment Details</CardTitle>
                {(!isEditing && allowEdit) && (
                    <div className="space-x-2">
                        <Button variant="ghost" onClick={() => setisEditing(true)}>
                            Edit
                        </Button>
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {isEditing ? (
                    <EmploymentForm
                        data={member.employment}
                        profileId={member.profileId}
                        onSave={handleSave}
                        onCancel={() => setisEditing(false)}
                    />
                ) : member.employment ? (
                    <EmploymentDetailsDisplay employment={member.employment} />
                ) : (
                    <p>No employment details available.</p>
                )}
            </CardContent>
        </Card>
    )
}

