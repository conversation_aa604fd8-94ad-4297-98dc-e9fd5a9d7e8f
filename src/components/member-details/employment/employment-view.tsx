"use client"

import { getOptionLabel } from "@/services/system-options.service"
import type { EmploymentDetails } from "@/types/members/member-details"
import { useEffect, useState } from "react"

interface EmploymentDetailsDisplayProps {
    employment: EmploymentDetails
}

export function EmploymentDetailsDisplay({ employment }: EmploymentDetailsDisplayProps) {
    const [addressCountry, setAddressCountry] = useState('--')
    const [preferredCountry, setPreferredCountry] = useState('--')

    const address = employment.company.address
    const preferred = employment.company.preferredAddress

    useEffect(() => {
        const fetchCountryLabels = async () => {
            if (address?.country) {
                const label = await getOptionLabel(address.country, 'country')
                setAddressCountry(label)
            }
            if (preferred?.country) {
                const label = await getOptionLabel(preferred.country, 'country')
                setPreferredCountry(label)
            }
        }

        fetchCountryLabels()
    }, [address, preferred])

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Company Name</p>
                <p className="text-sm text-muted-foreground">{employment.company.name || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Designation</p>
                <p className="text-sm text-muted-foreground">{employment.designation || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Telephone (Office)</p>
                <p className="text-sm text-muted-foreground">{employment.company.phone || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Email Address</p>
                <p className="text-sm text-muted-foreground">{employment.company.email || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Company Address</p>
                <p className="text-sm text-muted-foreground">
                    {address?.line1 || '--'}
                    {address?.line2 && <>, {address.line2}</>}
                    <br />
                    {address?.city || '--'}, {address?.state || '--'} {address?.postalCode || '--'}
                    <br />
                    {addressCountry}
                </p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Preferred Mailing Address</p>
                <p className="text-sm text-muted-foreground">
                    {employment.company.isSameAddress ? (
                        'Same as Company Address'
                    ) : preferred ? (
                        <>
                            {preferred.line1 || '--'}
                            {preferred.line2 && <>, {preferred.line2}</>}
                            <br />
                            {preferred.city || '--'}, {preferred.state || '--'} {preferred.postalCode || '--'}
                            <br />
                            {preferredCountry}
                        </>
                    ) : (
                        '--'
                    )}
                </p>
            </div>
        </div>
    )
}
