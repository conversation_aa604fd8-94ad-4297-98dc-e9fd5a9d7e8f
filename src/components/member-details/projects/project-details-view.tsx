"use client"

import { DetailViewWrapper } from "@/components/member-details/common/detail-view-wrapper"
import { getOptionLabel } from "@/services/system-options.service"
import { ProjectDetails } from "@/types/members/member-details"
import { useEffect, useState } from "react"

interface ProjectDetailsViewProps {
  data: ProjectDetails
  onEdit?: () => void
  allowEdit?: boolean
}

export function ProjectDetailsView({ data, onEdit, allowEdit = true }: ProjectDetailsViewProps) {
  const [addressCountry, setAddressCountry] = useState('--')

  const address = data.address

  useEffect(() => {
    const fetchCountryLabel = async () => {
      if (address?.country) {
        const label = await getOptionLabel(address.country, 'country')
        setAddressCountry(label)
      }
    }

    fetchCountryLabel()
  }, [address?.country])

  return (
    <DetailViewWrapper title={data?.title || "--"} onEdit={onEdit} allowEdit={allowEdit}>
      <div className="space-y-6">
        <div>
          <p className="text-sm text-muted-foreground">Reference No: {data?.reference_no || "--"}</p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium">Project Details</h4>
            <p className="text-sm whitespace-pre-wrap">{data?.details || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Position Held</h4>
            <p className="text-sm">{data?.position_held || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Cost Of Project</h4>
            <p className="text-sm">{data?.cost_of_project || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Project Period</h4>
            <p className="text-sm">{data?.involvement_from || "--"} - {data.is_ongoing ? 'Ongoing' : data.involvement_to || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Total Duration</h4>
            <p className="text-sm">{data?.total_year || "--"} years, {data?.total_month || "--"} months</p>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium">Supervising Professional Engineer Details</h4>

          <div>
            <h4 className="font-medium">Name</h4>
            <p className="text-sm">{data?.spe_name || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Number</h4>
            <p className="text-sm">{data?.spe_number || "--"}</p>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium">Employer</h4>

          <div>
            <p className="font-medium text-muted-foreground">Company Name</p>
            <p className="text-sm">{data?.company?.name || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Work Type</h4>
            <p className="text-sm">{data?.work_type || "--"}</p>
          </div>

          <div>
            <h4 className="font-medium">Employment Start Date</h4>
            <p className="text-sm">{data?.employment_start_date || "--"}</p>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium">Company Information</h4>

          <div className="space-y-4">
            <div>
              <p className="font-medium text-muted-foreground">Main Address</p>
              <p className="text-sm">
                {address.line1 || "--"}
                {address.line2 && <>, {address.line2}</>}
                <br />
                {address.city || "--"}, {address.state || "--"} {address.postalCode || "--"}
                <br />
                {addressCountry}
              </p>
            </div>
          </div>
        </div>
      </div>
    </DetailViewWrapper>
  )
} 