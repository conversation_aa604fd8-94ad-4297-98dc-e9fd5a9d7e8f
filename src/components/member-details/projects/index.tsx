"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import type { Member, ProjectDetails } from "@/types/members/member-details"
import { ProjectDetailsView } from "@/components/member-details/projects/project-details-view"
import { ProjectForm } from "@/components/member-details/projects/project-form"
import { deleteProject } from "@/services/members/member-server.service"

interface ProjectsProps {
  member: Member
  onUpdate: (updatedMember: Member) => void
  allowEdit?: boolean
}

export function Projects({ member, onUpdate, allowEdit = true }: ProjectsProps) {
  const [selectedProject, setSelectedProject] = useState<ProjectDetails | null>(null)
  const [mode, setMode] = useState<'view' | 'edit' | 'add' | null>(null);
  const isSheetOpen = mode !== null;

  const handleAdd = () => {
    setSelectedProject(null)
    setMode('add')
  }

  const handleView = (data: ProjectDetails) => {
    setSelectedProject(data)
    setMode('view')
  }

  const handleSave = (updatedProject: ProjectDetails) => {
    onUpdate({
      ...member,
      projects: member.projects.some((project) => project.id === updatedProject.id)
        ? member.projects.map((project) => project.id === updatedProject.id ? updatedProject : project
        ) : [...member.projects, updatedProject]
    })
    setSelectedProject(null)
    setMode(null)
  }

  const handleDelete = async (id: string) => {
    const result = await deleteProject(id)
    if (result.error) {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Success",
      description: "Project experience delete successfully",
    })
    onUpdate({
      ...member,
      projects: member.projects.filter((prj) => prj.id !== id),
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Projects</CardTitle>
          <Button variant="ghost" onClick={handleAdd}>Add</Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Reference No</TableHead>
                <TableHead>Position</TableHead>
                <TableHead>Period</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {member.projects && member.projects.length > 0 ? (
                member.projects.map((project, index) => (
                  <TableRow key={`project-${index}`}>
                    <TableCell>{project.title || '--'}</TableCell>
                    <TableCell>{project.reference_no || '--'}</TableCell>
                    <TableCell>{project.position_held || '--'}</TableCell>
                    <TableCell>
                      {(project.involvement_from && project.involvement_to)
                        ? `${project.involvement_from} - ${project.involvement_to}`
                        : (project.involvement_from && project.is_ongoing)
                          ? `${project.involvement_from} - Ongoing`
                          : '--'}
                    </TableCell>
                    <TableCell className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(project)}
                      >
                        View
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Are you absolutely sure?</DialogTitle>
                            <DialogDescription>
                              This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button type="button" variant="secondary">
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button variant="destructive" type="submit" onClick={() => handleDelete(project.id)}>Delete</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No projects available.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Sheet open={isSheetOpen} onOpenChange={(open) => !open && setMode(null)}>
        <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
          <SheetHeader>
            <SheetTitle>Project Details</SheetTitle>
          </SheetHeader>
          <div className="overflow-auto">
            {(selectedProject && mode === 'view') ? (
              <ProjectDetailsView
                data={selectedProject}
                onEdit={() => setMode('edit')}
                allowEdit={allowEdit}
              />
            ) : (mode === 'add' || mode === 'edit') ? (
              <ProjectForm
                data={selectedProject}
                profileId={member.profileId}
                onSave={handleSave}
                onCancel={() => setMode(null)}
              />
            ) : null}
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
} 