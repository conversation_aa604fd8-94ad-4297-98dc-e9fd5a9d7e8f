"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { CalendarIcon, Check, ChevronDown } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import type { ProjectDetails } from "@/types/members/member-details"
import { useSystemOptions } from "@/hooks/use-system-options"
import { updateProjectDetails } from "@/services/members/member-server.service"

interface ProjectFormProps {
  data: ProjectDetails | null
  profileId: string
  onSave: (data: ProjectDetails) => void
  onCancel: () => void
}

const addressSchema = z.object({
  line1: z.string().min(1, 'Address Line 1 is required'),
  line2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().min(1, 'Postal Code is required'),
  country: z.string().min(1, 'Country is required')
})

const projectExperienceSchema = z.object({
  id: z.string().optional(),
  involvementFrom: z.date().optional(),
  involvementTo: z.date().optional(),
  onGoingExperience: z.boolean().optional(),
  title: z.string().min(1, "Project Title is required"),
  referenceNo: z.string().min(1, "Project Reference Number is required"),
  details: z
    .string()
    .min(10, "Duties & Nature of Work must be at least 10 characters.")
    .max(1000, "Duties & Nature of Work must not be longer than 1000 characters."),
  costOfProject: z.string().min(1, "Cost of Project is required"),
  duration: z.string().optional(),
  positionHeld: z.string().min(1, "Position Held is required"),
  employmentType: z.string().min(1, "Employment Type is required"),
  speName: z.string().min(1, "Supervising Professional Engineer Name is required"),
  speNumber: z.string().min(1, "Supervising Professional Engineer Number is required"),
  totalYear: z.number().optional(),
  totalMonth: z.number().optional(),
  employment_start_date: z.date().optional(),
  companyName: z.string().min(1, "Company Name is required"),
  address_id: z.string().optional(),
  address: addressSchema
})
  .superRefine((data, ctx) => {
    if (!data.involvementFrom) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Involvement From is required.",
        path: ["involvementFrom"],
      });
    }

    if (!data.involvementTo && (data.onGoingExperience === undefined || !data.onGoingExperience)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Involvement to is required.",
        path: ["involvementTo"],
      });
    }

    if (!data.employment_start_date) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Employment Start Date is required.",
        path: ["employment_start_date"],
      });
    }
  });

export type ProjectExperienceFormData = z.infer<typeof projectExperienceSchema>;

export function ProjectForm({ data, profileId, onSave, onCancel }: ProjectFormProps) {
  const form = useForm<ProjectExperienceFormData>({
    resolver: zodResolver(projectExperienceSchema),
    mode: "onChange",
    defaultValues: {
      id: data?.id || undefined,
      involvementFrom: data?.involvement_from ? new Date(data?.involvement_from) : undefined,
      involvementTo: data?.involvement_to ? new Date(data?.involvement_to) : undefined,
      onGoingExperience: data?.is_ongoing || undefined,
      title: data?.title || "",
      referenceNo: data?.reference_no || "",
      details: data?.details || "",
      costOfProject: data?.cost_of_project || "",
      duration: data?.duration || "0 Months",
      positionHeld: data?.position_held || "",
      employmentType: data?.work_type || "",
      speName: data?.spe_name || "",
      speNumber: data?.spe_number || "",
      totalYear: data?.total_year || undefined,
      totalMonth: data?.total_month || undefined,
      employment_start_date: data?.employment_start_date ? new Date(data.employment_start_date) : undefined,
      companyName: data?.company.name || "",
      address: {
        line1: data?.address.line1 || "",
        line2: data?.address.line2 || "",
        city: data?.address.city || "",
        state: data?.address.state || "",
        postalCode: data?.address.postalCode || "",
        country: data?.address.country || ""
      }
    },
  })
  const { options, isLoading, error } = useSystemOptions()
  const [searchTerm, setSearchTerm] = useState("")

  const filteredCountries = options?.countries.filter((country) =>
    country.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const calculateTotalDuration = (from: Date, to: Date) => {
    // Get year and month differences
    let years = to.getFullYear() - from.getFullYear();
    let months = to.getMonth() - from.getMonth();

    // Adjust for negative months
    if (months < 0) {
      years--;
      months += 12;
    }

    // Adjust for day of month
    if (to.getDate() < from.getDate()) {
      months--;
      if (months < 0) {
        years--;
        months += 12;
      }
    }

    return {
      totalYears: years,
      totalMonths: months
    };
  }

  const calculateAndSetDuration = (fromDate: Date | null, toDate: Date | null, isOngoing: boolean) => {
    if (isOngoing) {
      form.setValue("totalYear", undefined);
      form.setValue("totalMonth", undefined);
      form.setValue("duration", "Ongoing");
    }

    if (fromDate && toDate) {
      const { totalYears, totalMonths } = calculateTotalDuration(fromDate, toDate);
      const totalDuration = totalYears * 12 + totalMonths;

      form.setValue("totalYear", totalYears);
      form.setValue("totalMonth", totalMonths);
      form.setValue("duration", `${totalDuration} Months`);
    }
  }

  const handleDurationChange = (onGoing: boolean) => {
    const fromDate = form.getValues(`involvementFrom`) ?? null;
    const toDate = onGoing ? null : form.getValues(`involvementTo`) ?? null;

    calculateAndSetDuration(fromDate, toDate, onGoing);
  }

  const onSubmit = async (formData: ProjectExperienceFormData) => {
    const result = await updateProjectDetails(profileId, formData)
    if (result.success && result.newProjectId && result.newCompanyId) {
      toast({
        title: "Success",
        description: "Work experience updated successfully",
      })
      const formattedData = {
        id: result.newProjectId,
        profile_id: profileId,
        company_id: result.newCompanyId,
        involvement_from: formData.involvementFrom?.toISOString().slice(0, 10) || null,
        involvement_to: formData.onGoingExperience ? null : formData.involvementTo?.toISOString().slice(0, 10) || null,
        title: formData.title,
        reference_no: formData.referenceNo,
        position_held: formData.positionHeld,
        details: formData.details,
        duration: formData.duration || null,
        work_type: formData.employmentType,
        spe_name: formData.speName,
        spe_number: formData.speNumber,
        total_year: formData.totalYear || null,
        total_month: formData.totalMonth || null,
        employment_start_date: formData.employment_start_date?.toISOString().slice(0, 10) || null,
        is_ongoing: formData.onGoingExperience || null,
        cost_of_project: formData.costOfProject,
        form_path: data?.form_path || null,
        company: {
          name: formData.companyName,
        },
        address: {
          line1: formData.address.line1,
          line2: formData.address.line2 || null,
          city: formData.address.city || null,
          state: formData.address.state || null,
          postalCode: formData.address.postalCode,
          country: formData.address.country,
        }
      }
      onSave(formattedData)
    } else {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center p-6">
          <LoadingSpinner />
        </CardContent>
      </Card>
    )
  }

  if (error || !options) {
    return (
      <Card>
        <CardContent className="text-center text-red-500 p-6">
          Failed to load form options
        </CardContent>
      </Card>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name={`onGoingExperience`}
          render={({ field }) => (
            <FormItem className="flex items-end space-x-2">
              <FormControl>
                <Checkbox
                  id="onGoingExperience"
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    handleDurationChange(checked === true);
                  }}
                />
              </FormControl>
              <FormLabel
                htmlFor="onGoingExperience"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Ongoing Experience
              </FormLabel>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`involvementFrom`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Involvement From</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                            handleDurationChange(false);
                          }
                        }}
                        endMonth={new Date()}
                        disabled={[{ after: new Date() }]}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.watch(`onGoingExperience`) !== true && (
            <FormField
              control={form.control}
              name={`involvementTo`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Involvement To</FormLabel>
                  <FormControl>
                    <Popover modal>
                      <PopoverTrigger asChild>
                        <div className="relative">
                          <Input
                            type="text"
                            readOnly
                            className="pointer-events-none"
                            placeholder="Pick a date"
                            value={field.value ? format(field.value, "PPP") : ""}
                          />
                          <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent>
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            if (date) {
                              const utcDate = new Date(Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate()
                              ));
                              field.onChange(utcDate);
                              handleDurationChange(false);
                            }
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name={`title`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Title</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Project Title" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`referenceNo`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Reference Number</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Project Reference Number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`positionHeld`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Position Held</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Position Held" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`costOfProject`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cost of Project</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Cost of Project" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`details`}
            render={({ field }) => (
              <div className="sm:col-span-2">
                <FormItem>
                  <FormLabel>Duties & Nature of Work</FormLabel>
                  <FormDescription>Describe in details of the types of structural works supervised and the degree of responsibility in each engagement</FormDescription>
                  <FormControl>
                    <Textarea
                      rows={3}
                      placeholder="Tell Us More..."
                      {...field}
                      className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </div>
            )}
          />

          <FormField
            control={form.control}
            name={`duration`}
            render={({ field }) => (
              <div>
                <FormItem>
                  <FormLabel>Duration</FormLabel>
                  <FormControl>
                    <Input type="text" disabled={true} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </div>
            )}
          />

          <FormField
            control={form.control}
            name={`totalYear`} // just reference one, it's for structure
            render={() => {
              const totalYear = form.getValues(`totalYear`) ?? 0;
              const totalMonth = form.getValues(`totalMonth`) ?? 0;

              const displayValue = `${totalYear} year${totalYear !== 1 ? 's' : ''} ${totalMonth} month${totalMonth !== 1 ? 's' : ''}`;

              return (
                <FormItem>
                  <FormLabel>Experience</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      value={displayValue}
                      readOnly
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </div>

        <h4 className="text-md font-medium">
          Supervising Professional Engineer Details
        </h4>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`speName`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`speNumber`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Engineer Number</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Engineer Number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <h4 className="text-md font-medium">
          Employer
        </h4>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`companyName`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Company Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`employmentType`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Employment Type</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Title" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="FullTime">Full Time</SelectItem>
                      <SelectItem value="PartTime">Part Time</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`employment_start_date`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Employment Start Date</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <h4 className="text-md font-medium">
          Company Address
        </h4>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`address.line1`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address line 1</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Address line 1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`address.line2`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address line 2</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Address line 2" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`address.city`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>City</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter City" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`address.state`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>State / Region</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter State / Region" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`address.postalCode`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Postal Code</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Postal Code" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.country"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nationality</FormLabel>
                <Popover modal>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                        <span className="truncate">
                          {field.value
                            ? options.countries.find((c) => c.code === field.value)?.label
                            : "Select Nationality"}
                        </span>
                        <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="p-0">
                    <Command>
                      <CommandInput
                        placeholder="Search nationality..."
                        value={searchTerm}
                        onInput={(e) => setSearchTerm(e.currentTarget.value)}
                      />
                      <CommandList>
                        {filteredCountries?.length === 0 ? (
                          <CommandEmpty>No country found.</CommandEmpty>
                        ) : (
                          <CommandGroup>
                            {filteredCountries?.map((country) => (
                              <CommandItem
                                key={country.code}
                                value={country.label}
                                onSelect={() => field.onChange(country.code)}
                              >
                                {country.label}
                                <Check
                                  className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                    }`}
                                />
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  )
} 