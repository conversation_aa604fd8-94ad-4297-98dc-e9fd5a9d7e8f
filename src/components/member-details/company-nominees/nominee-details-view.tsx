"use client"

import type { NomineeDetails } from "@/types/members/member-details"
import { DetailViewWrapper } from "@/components/member-details/common/detail-view-wrapper"

interface NomineeDetailsViewProps {
  data: NomineeDetails
  onEdit?: () => void
  allowEdit?: boolean
}

export function NomineeDetailsView({ data, onEdit, allowEdit }: NomineeDetailsViewProps) {
  const instituteName = data?.education?.institute.name ? data?.education?.institute.name : data?.education?.other_institute
  const instituteCountry = data?.education?.other_institute ? data?.education?.institute.country : 'Other'
  const courseName = data?.education?.instituteCourse.name ? data?.education?.instituteCourse.name : data?.education?.other_course
  const courseCategory = data?.education?.other_course ? data?.education?.instituteCourse.category : 'Other'

  return (
    <DetailViewWrapper title={data?.full_name || "--"} onEdit={onEdit} allowEdit={allowEdit}>
      <div className="space-y-6">
        <div>
          <h3 className="font-semibold mb-2">Personal Information</h3>
          <div className="grid gap-2 md:grid-cols-2">
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">Name</p>
              <p className="text-sm text-muted-foreground">{data?.full_name || "--"}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">Position</p>
              <p className="text-sm text-muted-foreground">{data?.title || "--"}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">Email</p>
              <p className="text-sm text-muted-foreground">{data?.email || "--"}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">Phone</p>
              <p className="text-sm text-muted-foreground">{data?.phone_number || "--"}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium leading-none">Date of birth</p>
              <p className="text-sm text-muted-foreground">{data?.date_of_birth || "--"}</p>
            </div>
          </div>
        </div>

        {data?.education && (
          <div>
            <h3 className="font-semibold mb-2">Education</h3>
            <div className="grid gap-2 md:grid-cols-2">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Institute Name</p>
                <p className="text-sm text-muted-foreground">{instituteName || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Institute Country</p>
                <p className="text-sm text-muted-foreground">{instituteCountry || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Course Name</p>
                <p className="text-sm text-muted-foreground">{courseName || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Course Category</p>
                <p className="text-sm text-muted-foreground">{courseCategory || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Study Period</p>
                <p className="text-sm text-muted-foreground">
                  {data.education?.period_from && data.education?.period_to ? `${data.education.period_from} - ${data.education.period_to}` : "--"}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Date Of Graduation</p>
                <p className="text-sm text-muted-foreground">{data.education?.date_of_graduation || "--"}</p>
              </div>
            </div>
          </div>
        )}

        {data?.has_awards !== false && (
          <div>
            <h3 className="font-semibold mb-2">Award</h3>
            <div className="grid gap-2 md:grid-cols-2">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Affiliation / Registration No</p>
                <p className="text-sm text-muted-foreground">{data?.award?.record_number || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Record Type</p>
                <p className="text-sm text-muted-foreground">{data.award?.record_type || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Date of Election</p>
                <p className="text-sm text-muted-foreground">{data.award?.date_of_election || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Details</p>
                <p className="text-sm text-muted-foreground">{data.award?.detail || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Abbreviation</p>
                <p className="text-sm text-muted-foreground">{data.award?.abbreviation || "--"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Expire Date</p>
                <p className="text-sm text-muted-foreground">{data.award?.expiry_date || "--"}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </DetailViewWrapper>
  )
} 