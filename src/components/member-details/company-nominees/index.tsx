"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import type { Member, NomineeDetails } from "@/types/members/member-details"
import { NomineeDetailsView } from "@/components/member-details/company-nominees/nominee-details-view"
import { NomineeForm } from "@/components/member-details/company-nominees/nominee-form"
import { deleteNominee } from "@/services/members/member-server.service"

interface CompanyNomineesProps {
  member: Member
  onUpdate: (updatedMember: Member) => void
  allowEdit?: boolean
}

export function CompanyNominees({ member, onUpdate, allowEdit = true }: CompanyNomineesProps) {
  const [selectedNominee, setSelectedNominee] = useState<NomineeDetails | null>(null)
  const [mode, setMode] = useState<'view' | 'edit' | 'add' | null>(null);
  const isSheetOpen = mode !== null;

  const handleAdd = () => {
    setSelectedNominee(null)
    setMode('add')
  }

  const handleView = (data: NomineeDetails) => {
    setSelectedNominee(data)
    setMode('view')
  }

  const handleSave = (updatedNominee: NomineeDetails) => {
    onUpdate({
      ...member,
      nominees: member.nominees.some((nominee) => nominee.id === updatedNominee.id)
        ? member.nominees.map((nominee) => nominee.id === updatedNominee.id ? updatedNominee : nominee
        ) : [...member.nominees, updatedNominee]
    })
    setSelectedNominee(null)
    setMode(null)
  }

  const handleDelete = async (id: string) => {
    const result = await deleteNominee(id)
    if (result.error) {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Success",
      description: "Nominee delete successfully",
    })
    onUpdate({
      ...member,
      nominees: member.nominees.filter((nom) => nom.id !== id),
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Company Nominees</CardTitle>
          <Button variant="ghost" onClick={handleAdd}>Add</Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Position</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {member.nominees && member.nominees.length > 0 ? (
                member.nominees.map((nominee, index) => (
                  <TableRow key={`nominee-${index}`}>
                    <TableCell>{nominee.full_name || '--'}</TableCell>
                    <TableCell>{nominee.title || '--'}</TableCell>
                    <TableCell>{nominee.email || '--'}</TableCell>
                    <TableCell>{nominee.phone_number || '--'}</TableCell>
                    <TableCell className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(nominee)}
                      >
                        View
                      </Button>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Are you absolutely sure?</DialogTitle>
                            <DialogDescription>
                              This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button type="button" variant="secondary">
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button variant="destructive" type="submit" onClick={() => handleDelete(nominee.id)}>Delete</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No company nominees available.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Sheet open={isSheetOpen} onOpenChange={(open) => !open && setMode(null)}>
        <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
          <SheetHeader>
            <SheetTitle>Nominee Details</SheetTitle>
          </SheetHeader>
          <div className="overflow-auto">
            {(selectedNominee && mode === 'view') ? (
              <NomineeDetailsView
                data={selectedNominee}
                onEdit={() => setMode('edit')}
                allowEdit={allowEdit}
              />
            ) : (mode === 'add' || mode === 'edit') ? (
              <NomineeForm
                data={selectedNominee}
                profileId={member.profileId}
                onSave={handleSave}
                onCancel={() => setMode(null)}
              />
            ) : null}
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
} 