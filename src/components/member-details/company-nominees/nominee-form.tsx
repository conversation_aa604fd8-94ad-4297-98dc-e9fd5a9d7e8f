"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { isValidPhoneNumber } from "react-phone-number-input"
import { format } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { PhoneInput } from "@/components/ui/phone-input"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/hooks/use-toast"
import { CalendarIcon } from "lucide-react"
import type { NomineeDetails } from "@/types/members/member-details"
import type { InstituteCourseOption } from "@/types/members/options"
import { useSystemOptions } from "@/hooks/use-system-options"
import { updateNomineeDetails } from "@/services/members/member-server.service"

interface NomineeFormProps {
  data: NomineeDetails | null
  profileId: string
  onSave: (data: NomineeDetails) => void
  onCancel: () => void
}

const companyNomineeSchema = z.object({
  has_awards: z.boolean(),
  id: z.string().optional(),
  fullName: z.string().min(1, "Full name is required"),
  title: z.string().min(1, "Salutation is required"),
  dob: z.date().optional(),
  telephone: z
    .string()
    .refine(isValidPhoneNumber, { message: "Invalid telephone number format" })
    .or(z.literal("")),
  email: z.string().optional(),
  education_id: z.string().optional(),
  periodFrom: z.date().optional(),
  periodTo: z.date().optional(),
  learningInstitute: z.string().min(1, "Learning Institute is required"),
  learningInstituteOthers: z
    .string()
    .optional()
    .refine((value) => !value || value.length <= 250, {
      message: "Must be 250 characters or less",
    }),
  courseOfStudy: z.string().min(1, "Course of Study (If condition met) is required"),
  courseOfStudyOthers: z
    .string()
    .optional()
    .refine((value) => !value || value.length <= 250, {
      message: "Must be 250 characters or less",
    }),
  dateOfGraduation: z.date().optional(),
  award_id: z.string().optional(),
  recordType: z.enum(['AFFILIATION', 'AWARD']).optional(),
  recordNumber: z.string().optional(),
  dateOfElection: z.date().optional(),
  details: z.string().optional(),
  abbreviation: z.string().optional(),
  expiryDate: z.date().optional(),
})
  .superRefine((data, ctx) => {
    if (!data.periodFrom) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Period From is required.",
        path: ["periodFrom"],
      });
    }

    if (!data.periodTo) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Period To is required.",
        path: ["periodTo"],
      });
    }

    if (!data.dateOfGraduation) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Date Of Graduation is required.",
        path: ["dateOfGraduation"],
      });
    }

    if (data.courseOfStudy === "other" && (!data.courseOfStudyOthers || data.courseOfStudyOthers.trim().length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please specify the Course of Study",
        path: ["courseOfStudyOthers"],
      });
    }

    if (data.learningInstitute === "other" && (!data.learningInstituteOthers || data.learningInstituteOthers.trim().length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please specify the Learning Institute Attended",
        path: ["learningInstituteOthers"],
      });
    }

    if (data.dateOfElection && isNaN(data.dateOfElection.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid Date of Election Month.",
        path: ["dateOfElection"],
      });
    }

    if (data.expiryDate && isNaN(data.expiryDate.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid Expiry Date.",
        path: ["expiryDate"],
      });
    }

    if (data.has_awards !== false) {
      if (!data.recordType) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Record Type is required.",
          path: ["recordType"],
        });
      }

      if (data.recordType === 'AFFILIATION' && !data.recordNumber) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Affiliation / Registration No is required when Affiliation is selected.",
          path: ["recordNumber"],
        });
      }

      if (!data.dateOfElection) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Date of Election is required.",
          path: ["dateOfElection"],
        });
      }

      if (!data.details || data.details.length < 10 || data.details.length > 150) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: !data.details
            ? "Details are required."
            : "Details must be between 10 and 150 characters.",
          path: ["details"],
        });
      }
    }

  })

export type CompanyNomineeFormData = z.infer<typeof companyNomineeSchema>;

export function NomineeForm({ data, profileId, onSave, onCancel }: NomineeFormProps) {
  const form = useForm<CompanyNomineeFormData>({
    resolver: zodResolver(companyNomineeSchema),
    mode: "onChange",
    defaultValues: {
      has_awards: data?.has_awards ?? true,
      id: data?.id || undefined,
      fullName: data?.full_name || "",
      title: data?.title || "",
      dob: data?.date_of_birth ? new Date(data?.date_of_birth) : undefined,
      telephone: data?.phone_number || "",
      email: data?.email || "",
      education_id: data?.education?.id || "",
      periodFrom: data?.education?.period_from ? new Date(data.education.period_from) : undefined,
      periodTo: data?.education?.period_to ? new Date(data.education.period_to) : undefined,
      learningInstitute: data?.education?.other_institute ? "other" : data?.education?.institute_id || "",
      learningInstituteOthers: data?.education?.other_institute || undefined,
      courseOfStudy: data?.education?.other_course ? "other" : data?.education?.icourse_id || "",
      courseOfStudyOthers: data?.education?.other_course || undefined,
      dateOfGraduation: data?.education?.date_of_graduation ? new Date(data.education.date_of_graduation) : undefined,
      award_id: data?.award?.id || undefined,
      recordType: data?.award?.record_type || undefined,
      recordNumber: data?.award?.record_number || undefined,
      dateOfElection: data?.award?.date_of_election ? new Date(data.award.date_of_election) : undefined,
      details: data?.award?.detail || undefined,
      abbreviation: data?.award?.abbreviation || undefined,
      expiryDate: data?.award?.expiry_date ? new Date(data.award.expiry_date) : undefined
    },
  })
  const { options, isLoading, error } = useSystemOptions()
  const [selectedInstitute, setSelectedInstitute] = useState<{
    name?: string;
    country?: string;
  } | null>(data?.education ? {
    name: data?.education.institute.name ?? undefined,
    country: data?.education.institute.country ?? undefined
  } : null)
  const [selectedInstituteCourse, setSelectedInstituteCourse] = useState<{
    name?: string;
    category?: string;
  } | null>(data?.education ? {
    name: data?.education.instituteCourse.name ?? undefined,
    category: data?.education.instituteCourse.category ?? undefined
  } : null)

  const onSubmit = async (formData: CompanyNomineeFormData) => {
    const result = await updateNomineeDetails(profileId, formData)
    if (result.success && result.nomineeId) {
      toast({
        title: "Success",
        description: "Company nominee updated successfully",
      })
      const formattedData = {
        id: result.nomineeId.id,
        profile_id: profileId,
        full_name: formData.fullName,
        title: formData.title,
        phone_number_country_code: null,
        phone_number: formData.telephone,
        email: formData.email || null,
        date_of_birth: formData.dob?.toISOString().slice(0, 10) || null,
        has_awards: formData.has_awards,
        education: {
          id: result.nomineeId.eduId,
          profile_id: profileId,
          institute_id: formData.learningInstitute,
          icourse_id: formData.courseOfStudy,
          other_institute: formData.learningInstituteOthers ?? null,
          other_course: formData.courseOfStudyOthers ?? null,
          period_from: formData.periodFrom?.toISOString().slice(0, 10) || null,
          period_to: formData.periodTo?.toISOString().slice(0, 10) || null,
          date_of_graduation: formData.dateOfGraduation?.toISOString().slice(0, 10) || null,
          nominee_id: null,
          institute: {
            name: selectedInstitute?.name,
            country: selectedInstitute?.country
          },
          instituteCourse: {
            name: selectedInstituteCourse?.name,
            category: selectedInstituteCourse?.category
          }
        },
        award: {
          id: result.nomineeId.awardId,
          profile_id: profileId,
          record_type: formData.recordType || null,
          record_number: formData.recordNumber || null,
          date_of_election: formData.dateOfElection?.toISOString().slice(0, 10) || null,
          detail: formData.details || null,
          abbreviation: formData.abbreviation || null,
          expiry_date: formData.expiryDate?.toISOString().slice(0, 10) || null,
          nominee_id: null,
        }
      }
      onSave(formattedData)
    } else {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center p-6">
          <LoadingSpinner />
        </CardContent>
      </Card>
    )
  }

  if (error || !options) {
    return (
      <Card>
        <CardContent className="text-center text-red-500 p-6">
          Failed to load form options
        </CardContent>
      </Card>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <h4 className="text-md font-medium">
          Basic Details
        </h4>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`fullName`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full name</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Full Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`title`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Salutation</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Salutation" />
                    </SelectTrigger>
                    <SelectContent>
                      {options?.salutations.map((salutation) => (
                        <SelectItem key={salutation.code} value={salutation.code}>
                          {salutation.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`dob`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date of Birth</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                        endMonth={new Date()}
                        disabled={[{ after: new Date() }]}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`telephone`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telephone</FormLabel>
                <FormControl>
                  <PhoneInput placeholder="Enter Telephone Number" international {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`email`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="Enter Email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <h4 className="text-md font-medium">
          Education
        </h4>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`periodFrom`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Period From</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                        endMonth={new Date()}
                        disabled={[{ after: new Date() }]}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`periodTo`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Period To</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`learningInstitute`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Learning Institute Attended</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      const selected = options?.institute.find(course => course.id === value);
                      if (selected) {
                        setSelectedInstitute({
                          name: selected.name,
                          country: selected.country,
                        });
                      } else {
                        setSelectedInstitute(null);
                      }
                    }}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Institute" />
                    </SelectTrigger>
                    <SelectContent>
                      {options?.institute.map((institute) => (
                        <SelectItem key={institute.id} value={institute.id}>
                          {institute.name}
                        </SelectItem>
                      ))}
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`courseOfStudy`}
            render={({ field }) => {

              // Group filtered courses by category
              const coursesByCategory = (options?.instituteCourse ?? []).reduce((acc, course) => {
                const category = course.category ?? "Uncategorized";
                if (!acc[category]) {
                  acc[category] = [];
                }
                acc[category].push(course);
                return acc;
              }, {} as Record<string, InstituteCourseOption[]>);

              return (
                <>
                  <FormItem>
                    <FormLabel>Course of Study</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          const selected = options?.instituteCourse.find((course) => course.id === value);
                          if (selected) {
                            setSelectedInstituteCourse({
                              name: selected.name,
                              category: selected.category,
                            });
                          } else {
                            setSelectedInstituteCourse(null);
                          }
                        }}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Course" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(coursesByCategory).map(([category, courses]) => (
                            <SelectGroup key={category}>
                              <SelectLabel>{category}</SelectLabel>
                              {courses.map((course) => (
                                <SelectItem key={course.id} value={course.id}>
                                  {course.name}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          ))}
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </>
              );
            }}
          />

          {form.watch('learningInstitute') === "other" && (
            <FormField
              control={form.control}
              name={`learningInstituteOthers`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Learning Institute Attended (Other)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter Learning Institute Attended (Other)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {form.watch('courseOfStudy') === "other" && (
            <FormField
              control={form.control}
              name={`courseOfStudyOthers`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Course of Study (Other)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter Course Of Study (Other)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name={`dateOfGraduation`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date Of Graduation</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <h4 className="text-md font-medium">
          Award
        </h4>

        <FormField
          control={form.control}
          name={`has_awards`}
          render={({ field }) => (
            <FormItem className="flex items-end space-x-2">
              <FormControl>
                <Checkbox
                  id="has_awards"
                  checked={!field.value} // Reverse boolean
                  onCheckedChange={(checked) => field.onChange(!checked)}
                />
              </FormControl>
              <FormLabel
                htmlFor="has_awards"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                I do not have any other engineering, affiliation, awards & distinctions
              </FormLabel>
              <FormMessage />
            </FormItem>
          )}
        />

        {form.watch('has_awards') !== false && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name={`recordType`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Record Type</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Record Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="AFFILIATION">Affiliation (PE, ACES, TUCSS, RE/RTO etc.)</SelectItem>
                        <SelectItem value="AWARD">Awards</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`dateOfElection`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date of Election</FormLabel>
                  <FormControl>
                    <Popover modal>
                      <PopoverTrigger asChild>
                        <div className="relative">
                          <Input
                            type="text"
                            readOnly
                            className="pointer-events-none"
                            placeholder="Pick a date"
                            value={field.value ? format(field.value, "PPP") : ""}
                          />
                          <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent>
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            if (date) {
                              const utcDate = new Date(Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate()
                              ));
                              field.onChange(utcDate);
                            }
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('recordType') === "AFFILIATION" && (
              <FormField
                control={form.control}
                name={`recordNumber`}
                render={({ field }) => (
                  <FormItem className="sm:grid-cols-2">
                    <FormLabel>Affiliation / Registration No</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter Affiliation / Registration No" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name={`details`}
              render={({ field }) => (
                <FormItem className="sm:col-span-full">
                  <FormLabel>Details</FormLabel>
                  <FormControl>
                    <Textarea
                      rows={3}
                      placeholder="Provide details of your affiliation, award, or distinction here..."
                      {...field}
                      className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`abbreviation`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Abbreviation</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter Abbreviation"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`expiryDate`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry Date</FormLabel>
                  <FormControl>
                    <Popover modal>
                      <PopoverTrigger asChild>
                        <div className="relative">
                          <Input
                            type="text"
                            readOnly
                            className="pointer-events-none"
                            placeholder="Pick a date"
                            value={field.value ? format(field.value, "PPP") : ""}
                          />
                          <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent>
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            if (date) {
                              const utcDate = new Date(Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate()
                              ));
                              field.onChange(utcDate);
                            }
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  )
} 