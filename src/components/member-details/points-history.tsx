import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Mock data for points history
const pointsHistory = [
  { id: 1, date: "2023-12-01", type: "PDU (PEB)", points: 5, description: "Attended workshop" },
  { id: 2, date: "2023-11-15", type: "CPD (BOA)", points: 3, description: "Online course completion" },
  { id: 3, date: "2023-10-20", type: "STU (Safety)", points: 2, description: "Safety seminar" },
  { id: 4, date: "2023-09-05", type: "PDU", points: 4, description: "Conference participation" },
  { id: 5, date: "2023-08-12", type: "CET Hours", points: 6, description: "Training program" },
]

export function PointsHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Points History</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Point Type</TableHead>
              <TableHead>Points</TableHead>
              <TableHead>Description</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pointsHistory.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.date}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>{item.points}</TableCell>
                <TableCell>{item.description}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

