"use client"

import { But<PERSON> } from "@/components/ui/button"

interface DetailViewWrapperProps {
  title: string
  children: React.ReactNode
  onEdit?: () => void
  allowEdit?: boolean
}

export function DetailViewWrapper({
  title,
  children,
  onEdit,
  allowEdit = true,
}: DetailViewWrapperProps) {
  return (
    <div className="space-y-6">
      <h3 className="font-semibold mb-2">{title}</h3>
      {children}
      {onEdit && (
        <div className="flex justify-end">
          {allowEdit && (
            <Button variant="outline" size="sm" onClick={onEdit}>
              Edit
            </Button>
          )}
        </div>
      )}
    </div>
  )
} 