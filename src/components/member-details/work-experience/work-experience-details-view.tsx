"use client"

import { DetailViewWrapper } from "@/components/member-details/common/detail-view-wrapper"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { getOptionLabel } from "@/services/system-options.service"
import { WorkExperienceDetails } from "@/types/members/member-details"
import { useEffect, useState } from "react"

interface WorkExperienceDetailsViewProps {
  data: WorkExperienceDetails
  onEdit?: () => void
  allowEdit?: boolean
}

export function WorkExperienceDetailsView({ data, onEdit, allowEdit }: WorkExperienceDetailsViewProps) {
  const [loading, setLoading] = useState(true);
  const [labels, setLabels] = useState({
    addressCountryLabel: '--',
  });

  useEffect(() => {
    const fetchLabels = async () => {
      setLoading(true);
      const [
        addressCountryLabel
      ] = await Promise.all([
        data?.address.country ? getOptionLabel(data.address.country, 'country') : "--"
      ]);

      setLabels({
        addressCountryLabel,
      });
      setLoading(false);
    };

    fetchLabels();
  }, [data]);

  if (loading) {
    return (
      <LoadingSpinner />
    );
  }

  return (
    <DetailViewWrapper title={data?.designation || "--"} onEdit={onEdit} allowEdit={allowEdit}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-semibold text-lg">{data?.designation || "--"}</h3>
            <p className="text-sm text-muted-foreground">{data?.company.name || "--"}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium">Period</h4>
            <p className="text-sm">{data?.period_from ? `${data.period_from} - ${data.period_to}` : "--"}</p>
          </div>
          <div>
            <h4 className="font-medium">Duties</h4>
            <p className="text-sm">{data?.duties || "--"}</p>
          </div>
        </div>

        <div>
          <h4 className="font-medium">Company Address</h4>
          <p className="text-sm">
            {data?.address.line1 || "--"}
            {data?.address.line2 && `, ${data.address.line2}`}
            <br />
            {data?.address.city || "--"}, {data?.address.state || "--"} {data?.address.postalCode || "--"}
            <br />
            {labels.addressCountryLabel}
          </p>
        </div>
      </div>
    </DetailViewWrapper>
  )
} 