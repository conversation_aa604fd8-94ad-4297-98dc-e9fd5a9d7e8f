"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import type { Member, WorkExperienceDetails } from "@/types/members/member-details"
import { WorkExperienceForm } from "@/components/member-details/work-experience/work-experience-form"
import { WorkExperienceDetailsView } from "@/components/member-details/work-experience/work-experience-details-view"
import { deleteWorkExperience } from "@/services/members/member-server.service"

interface WorkExperienceProps {
  member: Member
  onUpdate: (updatedM<PERSON>ber: Member) => void
  allowEdit?: boolean
}

export function WorkExperience({ member, onUpdate, allowEdit }: WorkExperienceProps) {
  const [selectedExperience, setSelectedExperience] = useState<WorkExperienceDetails | null>(null)
  const [mode, setMode] = useState<'view' | 'edit' | 'add' | null>(null);
  const isSheetOpen = mode !== null;

  const handleAdd = () => {
    setSelectedExperience(null)
    setMode('add')
  }

  const handleView = (data: WorkExperienceDetails) => {
    setSelectedExperience(data)
    setMode('view')
  }

  const handleSave = async (updatedExperience: WorkExperienceDetails) => {
    onUpdate({
      ...member,
      workExperience: member.workExperience.some((exp) => exp.id === updatedExperience.id)
        ? member.workExperience.map((exp) =>
          exp.id === updatedExperience.id ? updatedExperience : exp
        ) : [...member.workExperience, updatedExperience],
    })
    setSelectedExperience(null)
    setMode(null)
  }

  const handleDelete = async (id: string) => {
    const result = await deleteWorkExperience(id)
    if (result.error) {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Success",
      description: "Work experience delete successfully",
    })
    onUpdate({
      ...member,
      workExperience: member.workExperience.filter((exp) => exp.id !== id),
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Work Experience</CardTitle>
          <Button variant="ghost" onClick={handleAdd}>Add</Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company</TableHead>
                <TableHead>Designation</TableHead>
                <TableHead>Period</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {member.workExperience && member.workExperience.length > 0 ? (
                member.workExperience.map((exp, index) => (
                  <TableRow key={`work-experience-${index}`}>
                    <TableCell>{exp.company.name || '--'}</TableCell>
                    <TableCell>{exp.designation || '--'}</TableCell>
                    <TableCell>
                      {(exp.period_from && exp.period_to)
                        ? `${exp.period_from} - ${exp.period_to}`
                        : exp.period_from
                          ? `From ${exp.period_from}`
                          : exp.period_to
                            ? `To ${exp.period_to}`
                            : '--'}
                    </TableCell>
                    <TableCell className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(exp)}
                      >
                        View
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Are you absolutely sure?</DialogTitle>
                            <DialogDescription>
                              This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button type="button" variant="secondary">
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button variant="destructive" type="submit" onClick={() => handleDelete(exp.id)}>Delete</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    No work experience available.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Sheet open={isSheetOpen} onOpenChange={(open) => !open && setMode(null)}>
        <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
          <SheetHeader>
            <SheetTitle>Work Experience Details</SheetTitle>
          </SheetHeader>
          <div className="overflow-auto">
            {(selectedExperience && mode === 'view') ? (
              <WorkExperienceDetailsView
                data={selectedExperience}
                onEdit={() => setMode('edit')}
                allowEdit={allowEdit}
              />
            ) : (mode === 'add' || mode === 'edit') ? (
              <WorkExperienceForm
                data={selectedExperience}
                profileId={member.profileId}
                onSave={handleSave}
                onCancel={() => setMode(null)}
              />
            ) : null}
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
} 