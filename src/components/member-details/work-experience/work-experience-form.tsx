"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { CalendarIcon, Check, ChevronDown } from "lucide-react"
import type { WorkExperienceDetails } from "@/types/members/member-details"
import { useSystemOptions } from "@/hooks/use-system-options"
import { toast } from "@/hooks/use-toast"
import { updateWorkExperienceDetails } from "@/services/members/member-server.service"

interface WorkExperienceFormProps {
  data: WorkExperienceDetails | null
  profileId: string
  onSave: (data: WorkExperienceDetails) => void
  onCancel: () => void
}

const addressSchema = z.object({
  line1: z.string().min(1, 'Address Line 1 is required'),
  line2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().min(1, 'Postal Code is required'),
  country: z.string().min(1, 'Country is required')
})

const workExperienceSchema = z.object({
  id: z.string().optional(),
  periodFrom: z.coerce.date({ required_error: 'Period From is required' }),
  periodTo: z.coerce.date({ required_error: 'Period To is required' }),
  designation: z.string().min(1, 'Designation is required'),
  duties: z.string().min(1, 'Duties are required'),
  companyName: z.string().min(1, 'Company Name is required'),
  address: addressSchema
})

export type WorkExperienceFormData = z.infer<typeof workExperienceSchema>;

export function WorkExperienceForm({ data, profileId, onSave, onCancel }: WorkExperienceFormProps) {
  const form = useForm<WorkExperienceFormData>({
    resolver: zodResolver(workExperienceSchema),
    mode: 'onChange',
    defaultValues: {
      id: data?.id || "",
      periodFrom: data?.period_from ? new Date(data?.period_from) : undefined,
      periodTo: data?.period_to ? new Date(data?.period_to) : undefined,
      designation: data?.designation || "",
      duties: data?.duties || "",
      companyName: data?.company.name || "",
      address: {
        line1: data?.address.line1 || "",
        line2: data?.address.line2 || "",
        city: data?.address.city || "",
        state: data?.address.state || "",
        postalCode: data?.address.postalCode || "",
        country: data?.address.country || ""
      }
    }
  });
  const { options, isLoading, error } = useSystemOptions()
  const [searchTerm, setSearchTerm] = useState("")

  const filteredCountries = options?.countries.filter((country) =>
    country.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const onSubmit = async (formData: WorkExperienceFormData) => {
    const result = await updateWorkExperienceDetails(profileId, formData)
    if (result.success && result.newWorkExperienceId && result.newCompanyId) {
      toast({
        title: "Success",
        description: "Work experience updated successfully",
      })
      const formattedData = {
        id: result.newWorkExperienceId,
        profile_id: profileId,
        company_id: result.newCompanyId,
        designation: formData.designation,
        duties: formData.duties,
        period_from: formData.periodFrom.toISOString().slice(0, 10),
        period_to: formData.periodTo.toISOString().slice(0, 10),
        company: {
          name: formData.companyName,
        },
        address: {
          line1: formData.address.line1,
          line2: formData.address.line2 || null,
          city: formData.address.city || null,
          state: formData.address.state || null,
          postalCode: formData.address.postalCode,
          country: formData.address.country,
        }
      }
      onSave(formattedData)
    } else {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center p-6">
          <LoadingSpinner />
        </CardContent>
      </Card>
    )
  }

  if (error || !options) {
    return (
      <Card>
        <CardContent className="text-center text-red-500 p-6">
          Failed to load form options
        </CardContent>
      </Card>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="duties"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Duties</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="designation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Designation</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="periodFrom"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Period From</FormLabel>
                <Popover modal>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start px-3 py-2 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      <span className="truncate">
                        {field.value ? format(field.value, "PPP") : "Pick a date"}
                      </span>
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={(date) => {
                        if (date) {
                          const utcDate = new Date(Date.UTC(
                            date.getFullYear(),
                            date.getMonth(),
                            date.getDate()
                          ));
                          field.onChange(utcDate);
                        }
                      }}
                      disabled={{ after: new Date() }}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="periodTo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Period To</FormLabel>
                <Popover modal>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start px-3 py-2 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      <span className="truncate">
                        {field.value ? format(field.value, "PPP") : "Pick a date"}
                      </span>
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={(date) => {
                        if (date) {
                          const utcDate = new Date(Date.UTC(
                            date.getFullYear(),
                            date.getMonth(),
                            date.getDate()
                          ));
                          field.onChange(utcDate);
                        }
                      }}
                      disabled={{ after: new Date() }}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="companyName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="address.line1"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address Line 1</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.line2"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address Line 2</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value ?? ""} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>City</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.state"
            render={({ field }) => (
              <FormItem>
                <FormLabel>State</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.postalCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Postal Code</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.country"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Country</FormLabel>
                <Popover modal>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                        <span className="truncate">
                          {field.value
                            ? options.countries.find((c) => c.code === field.value)?.label
                            : "Select Country"}
                        </span>
                        <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="p-0">
                    <Command>
                      <CommandInput
                        placeholder="Search Country..."
                        value={searchTerm}
                        onInput={(e) => setSearchTerm(e.currentTarget.value)}
                      />
                      <CommandList>
                        {filteredCountries?.length === 0 ? (
                          <CommandEmpty>No country found.</CommandEmpty>
                        ) : (
                          <CommandGroup>
                            {filteredCountries?.map((country) => (
                              <CommandItem
                                key={country.code}
                                value={country.label}
                                onSelect={() => field.onChange(country.code)}
                              >
                                {country.label}
                                <Check
                                  className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                    }`}
                                />
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  )
} 