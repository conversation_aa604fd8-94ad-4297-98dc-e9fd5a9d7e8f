"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { isValidPhoneNumber } from "react-phone-number-input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { PhoneInput } from "@/components/ui/phone-input"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import { toast } from "@/hooks/use-toast"
import { CalendarIcon, Check, ChevronsUpDown } from "lucide-react"
import type { PersonalInfo } from "@/types/members/member-details"
import { useSystemOptions } from "@/hooks/use-system-options"
import { updateCompanyInfo } from "@/services/members/member-server.service"

interface companyInformationFormProps {
    data: PersonalInfo | null
    profileId: string
    onSave: (data: Partial<PersonalInfo>) => void
    onCancel: () => void
}

const addressSchema = z.object({
    line1: z.string().min(1, 'Address Line 1 is required'),
    line2: z.string().optional(),
    line3: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postalCode: z.string().min(1, 'Postal Code is required'),
    country: z.string().min(1, 'Country is required')
})

const companyInformationSchema = z.object({
    fullName: z.string().min(1, "Company name is required"),
    mobile: z
        .string()
        .min(1, "Telephone Number is required")
        .refine(isValidPhoneNumber, { message: "Invalid telephone number format" }),
    email: z.coerce
        .string()
        .min(1, "Email is required")
        .refine(
            (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
            "Invalid email address"
        ),
    dob: z.date().optional(),
    nationality: z.string().optional(),
    nricPassport: z.string().optional(),
    address_id: z.string().optional(),
    address: addressSchema
})

export type CompanySchemaFormData = z.infer<typeof companyInformationSchema>

export function CompanyInformationForm({ data, profileId, onSave, onCancel }: companyInformationFormProps) {
    const form = useForm<CompanySchemaFormData>({
        resolver: zodResolver(companyInformationSchema),
        mode: "onChange",
        defaultValues: {
            fullName: data?.full_name || "",
            mobile: data?.mobile_number || "",
            email: data?.email || "",
            dob: data?.date_of_birth ? new Date(data.date_of_birth) : undefined,
            nationality: data?.nationality || "",
            nricPassport: data?.identification_no || "",
            address_id: data?.address_id || "",
            address: {
                line1: data?.address.line1 || "",
                line2: data?.address.line2 || "",
                line3: data?.address.line3 || "",
                city: data?.address.city || "",
                state: data?.address.state || "",
                postalCode: data?.address.postalCode || "",
                country: data?.address.country || ""
            }
        },
    });
    const { options, isLoading, error } = useSystemOptions()
    const [searchTerm, setSearchTerm] = useState("")

    const onSubmit = async (formData: CompanySchemaFormData) => {
        const result = await updateCompanyInfo(profileId, formData)
        if (result.success && data) {
            toast({
                title: "Success",
                description: "Company Information updated successfully",
            })
            const formattedValue = {
                full_name: formData.fullName,
                mobile_number: formData.mobile,
                email: formData.email,
                nationality: formData.nationality,
                date_of_birth: formData.dob?.toISOString().slice(0, 10),
                identification_no: formData.nricPassport,
                address_id: result.addressId,
                address: {
                    line1: formData.address.line1,
                    line2: formData.address.line2 || null,
                    city: formData.address.city || null,
                    state: formData.address.state || null,
                    postalCode: formData.address.postalCode,
                    country: formData.address.country,
                }
            }
            onSave(formattedValue)
        } else {
            toast({
                title: "Error",
                description: result.error,
                variant: "destructive",
            })
        }
    }

    if (isLoading) {
        return (
            <Card>
                <CardContent className="flex justify-center items-center p-6">
                    <LoadingSpinner />
                </CardContent>
            </Card>
        )
    }

    if (error || !options) {
        return (
            <Card>
                <CardContent className="text-center text-red-500 p-6">
                    Failed to load form options
                </CardContent>
            </Card>
        )
    }

    const filteredCountries = options?.countries.filter((country) =>
        country.label.toLowerCase().includes(searchTerm.toLowerCase())
    )

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="fullName"
                        render={({ field }) => (
                            <FormItem className="sm:col-span-2">
                                <FormLabel>
                                    Company Name
                                </FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter Company Name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="mobile"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Telephone Number (Office)</FormLabel>
                                <FormControl>
                                    <PhoneInput
                                        placeholder="Enter Telephone Number (office)"
                                        international
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Company Email</FormLabel>
                                <FormControl>
                                    <Input
                                        type="email"
                                        placeholder="Enter Company Email"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name={`nationality`}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Place of Registration</FormLabel>
                                <FormControl>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                className="w-full flex justify-between"
                                            >
                                                {field.value
                                                    ? options.countries.find(
                                                        (country) => country.code === field.value
                                                    )?.label
                                                    : "Select Place of Registration"}
                                                <ChevronsUpDown className="opacity-50" />
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="p-0">
                                            <Command>
                                                <CommandInput
                                                    placeholder="Search Place of Registration..."
                                                    onInput={(e) =>
                                                        setSearchTerm(e.currentTarget.value)
                                                    }
                                                    value={searchTerm}
                                                />
                                                <CommandList>
                                                    {filteredCountries.length === 0 ? (
                                                        <CommandEmpty>No country found.</CommandEmpty>
                                                    ) : (
                                                        <CommandGroup>
                                                            {filteredCountries.map((country) => (
                                                                <CommandItem
                                                                    key={country.code}
                                                                    value={country.label}
                                                                    onSelect={() => {
                                                                        field.onChange(country.code);
                                                                    }}
                                                                >
                                                                    {country.label}
                                                                    <Check
                                                                        className={`ml-auto ${field.value === country.code
                                                                            ? "opacity-100"
                                                                            : "opacity-0"
                                                                            }`}
                                                                    />
                                                                </CommandItem>
                                                            ))}
                                                        </CommandGroup>
                                                    )}
                                                </CommandList>
                                            </Command>
                                        </PopoverContent>
                                    </Popover>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="dob"
                        render={({ field }) => (
                            <FormItem >
                                <FormLabel>
                                    Date of Registration
                                </FormLabel>
                                <FormControl>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <div className="relative">
                                                <Input
                                                    type="text"
                                                    readOnly
                                                    className="pointer-events-none"
                                                    placeholder="Pick a date"
                                                    value={field.value ? format(field.value, "PPP") : ""}
                                                />
                                                <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                            </div>
                                        </PopoverTrigger>
                                        <PopoverContent>
                                            <Calendar
                                                mode="single"
                                                selected={field.value}
                                                onSelect={(date) => {
                                                    if (date) {
                                                        const utcDate = new Date(
                                                            Date.UTC(
                                                                date.getFullYear(),
                                                                date.getMonth(),
                                                                date.getDate()
                                                            )
                                                        );
                                                        field.onChange(utcDate);
                                                    }
                                                }}
                                                endMonth={new Date()}
                                                disabled={[{ after: new Date() }]}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="nricPassport"
                        render={({ field }) => (
                            <FormItem >
                                <FormLabel>Registration No</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter Registration No."
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="address.line1"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Address Line 1
                                </FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter Address Line 1" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.line2"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Address Line 2</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter Address Line 2" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.line3"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Address Line 3</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter Address Line 3" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.city"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>City</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter City" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.state"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>State / Region</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter State / Region" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="address.postalCode"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Postal Code
                                </FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter Postal Code" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name={`address.country`}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Country
                                </FormLabel>
                                <FormControl>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                className="w-full flex justify-between"
                                            >
                                                {field.value
                                                    ? options.countries.find(
                                                        (country) => country.code === field.value
                                                    )?.label
                                                    : "Select Country"}
                                                <ChevronsUpDown className="opacity-50" />
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="p-0">
                                            <Command>
                                                <CommandInput
                                                    placeholder="Search Country..."
                                                    onInput={(e) =>
                                                        setSearchTerm(e.currentTarget.value)
                                                    }
                                                    value={searchTerm}
                                                />
                                                <CommandList>
                                                    {filteredCountries.length === 0 ? (
                                                        <CommandEmpty>No country found.</CommandEmpty>
                                                    ) : (
                                                        <CommandGroup>
                                                            {filteredCountries.map((country) => (
                                                                <CommandItem
                                                                    key={country.code}
                                                                    value={country.label}
                                                                    onSelect={() => {
                                                                        field.onChange(country.code);
                                                                    }}
                                                                >
                                                                    {country.label}
                                                                    <Check
                                                                        className={`ml-auto ${field.value === country.code
                                                                            ? "opacity-100"
                                                                            : "opacity-0"
                                                                            }`}
                                                                    />
                                                                </CommandItem>
                                                            ))}
                                                        </CommandGroup>
                                                    )}
                                                </CommandList>
                                            </Command>
                                        </PopoverContent>
                                    </Popover>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                </div>
            </form>
        </Form>
    )
} 