"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import type { Member, PersonalInfo } from "@/types/members/member-details"
import { CompanyInformationForm } from "@/components/member-details/company-information/company-form"
import { CompanyInformationDetails } from "@/components/member-details/company-information/company-view"

interface CompanyInformationProps {
    member: Member
    onUpdate: (updatedMember: Member) => void
    allowEdit?: boolean
}

export function CompanyInformation({ member, onUpdate, allowEdit }: CompanyInformationProps) {
    const [isEditing, setisEditing] = useState(false)

    const onSave = async (updatedPersonalInfo: Partial<PersonalInfo>) => {
        onUpdate({
            ...member,
            personalInfo: {
                ...member.personalInfo,
                ...updatedPersonalInfo
            }
        })
        setisEditing(false)
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Company Information</CardTitle>
                {(!isEditing && allowEdit) && (
                    <div className="space-x-2">
                        <Button variant="ghost" onClick={() => setisEditing(true)}>
                            Edit
                        </Button>
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {isEditing ? (
                    <CompanyInformationForm
                        data={member.personalInfo}
                        profileId={member.profileId}
                        onSave={onSave}
                        onCancel={() => setisEditing(false)}
                    />
                ) : (
                    <CompanyInformationDetails companyInfo={member.personalInfo} />
                )}
            </CardContent>
        </Card>
    )
}

