"use client"

import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { getOptionLabel } from "@/services/system-options.service"
import type { PersonalInfo } from "@/types/members/member-details"
import { useEffect, useState } from "react";

interface CompanyInformationDetailsProps {
    companyInfo: PersonalInfo;
}

export function CompanyInformationDetails({ companyInfo }: CompanyInformationDetailsProps) {
    const [loading, setLoading] = useState(true);
    const [labels, setLabels] = useState({
        nationalityLabel: '--',
        addressCountry: '--',
    });

    // Fetch the labels asynchronously using useEffect
    useEffect(() => {
        const fetchLabels = async () => {
            setLoading(true);
            const [
                nationalityLabel,
                addressCountry,
            ] = await Promise.all([
                companyInfo.nationality ? getOptionLabel(companyInfo.nationality, 'country') : Promise.resolve('--'),
                companyInfo.address.country ? getOptionLabel(companyInfo.address.country, 'country') : Promise.resolve('--'),
            ]);

            setLabels({ nationalityLabel, addressCountry });
            setLoading(false);
        };

        fetchLabels();
    }, [companyInfo]);

    if (loading) {
        return (
            <LoadingSpinner />
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Company Name</p>
                <p className="text-sm text-muted-foreground">{companyInfo.full_name || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Telephone Number (office)</p>
                <p className="text-sm text-muted-foreground">{companyInfo.mobile_number || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Company Email</p>
                <p className="text-sm text-muted-foreground">{companyInfo.email || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Place Of Registration</p>
                <p className="text-sm text-muted-foreground">{labels.nationalityLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Date Of Registration</p>
                <p className="text-sm text-muted-foreground">{companyInfo.date_of_birth || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Registration No</p>
                <p className="text-sm text-muted-foreground">{companyInfo.identification_no || '--'}</p>
            </div>
            <div className="space-y-1 md:col-span-2">
                <p className="text-sm font-medium leading-none">Address</p>
                <p className="text-sm text-muted-foreground">
                    {companyInfo.address.line1 || '--'}
                    {companyInfo.address.line2 && `, ${companyInfo.address.line2}`}
                    {companyInfo.address.line3 && `, ${companyInfo.address.line3}`}
                    <br />
                    {companyInfo.address.city || '--'}, {companyInfo.address.state || '--'} {companyInfo.address.postalCode || '--'}
                    <br />
                    {labels.addressCountry}
                </p>
            </div>
        </div>
    );
}