"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Sheet, She<PERSON><PERSON>ontent, Sheet<PERSON>eader, SheetTitle } from "@/components/ui/sheet"
import type { Member, criminalRecordDetails } from "@/types/members/member-details"
import { CriminalRecordForm } from "@/components/member-details/criminal-records/criminal-record-form"

interface CriminalRecordsProps {
  member: Member
  onUpdate: (updatedMember: Member) => void
  allowEdit?: boolean
}

export function CriminalRecords({ member, onUpdate, allowEdit }: CriminalRecordsProps) {
  const [isEditing, setisEditing] = useState(false)

  const handleSave = (updatedCriminalRecord: criminalRecordDetails) => {
    onUpdate({
      ...member,
      criminalRecords: updatedCriminalRecord,
    })
    setisEditing(false)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Criminal Records</CardTitle>
        {(!isEditing && allowEdit) && (
          <div className="space-x-2">
            <Button variant="ghost" onClick={() => setisEditing(true)}>
              {member.criminalRecords ? 'Edit' : 'Add'}
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Sheet open={isEditing} onOpenChange={setisEditing}>
          <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
            <SheetHeader>
              <SheetTitle>Criminal records</SheetTitle>
            </SheetHeader>
            <div className="overflow-auto">
              <CriminalRecordForm
                data={member.criminalRecords}
                profileId={member.profileId}
                onSave={handleSave}
                onCancel={() => setisEditing(false)}
              />
            </div>
          </SheetContent>
        </Sheet>

        {member.criminalRecords ? (
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium leading-none">Declaration</p>
              <p className="text-sm text-muted-foreground">
                {member.criminalRecords.declaration === true ? 'Yes' : member.criminalRecords.declaration === false ? 'No' : '--'}
              </p>
            </div>

            <div>
              <p className="text-sm font-medium leading-none">Details</p>
              <p className="text-sm text-muted-foreground">
                {member.criminalRecords.details || '--'}
              </p>
            </div>
          </div>
        ) : (
          <p className="text-sm text-muted-foreground">No criminal records available.</p>
        )}
      </CardContent>
    </Card>
  )
}

