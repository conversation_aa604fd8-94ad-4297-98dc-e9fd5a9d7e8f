"use client"

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/hooks/use-toast"
import type { criminalRecordDetails } from "@/types/members/member-details"
import { updateCriminalRecords } from "@/services/members/member-server.service"

interface criminalRecordFormProps {
    data: criminalRecordDetails | null
    profileId: string
    onSave: (data: criminalRecordDetails) => void
    onCancel: () => void
}

const criminalRecordSchema = z
    .object({
        id: z.string().optional(),
        declaration: z.enum(["yes", "no"], {
            required_error: "Please select an option.",
        }),
        details: z.string().optional(),
    })
    .refine(
        (data) =>
            data.declaration === "no" ||
            (data.details && data.details.trim().length >= 10 && data.details.length <= 160),
        {
            message: "Details must be between 10 and 160 characters.",
            path: ["details"],
        }
    )

export type CriminalRecordFormData = z.infer<typeof criminalRecordSchema>

export function CriminalRecordForm({ data, profileId, onSave, onCancel }: criminalRecordFormProps) {
    const form = useForm<CriminalRecordFormData>({
        resolver: zodResolver(criminalRecordSchema),
        mode: "onChange",
        defaultValues: {
            id: data?.id || undefined,
            declaration: data?.declaration === true ? 'yes' : data?.declaration === false ? 'no' : undefined,
            details: data?.details || "",
        },
    })

    const onSubmit = async (formData: CriminalRecordFormData) => {
        const result = await updateCriminalRecords(profileId, formData)
        if (result.success && result.newCriminalRecordId) {
            toast({
                title: "Success",
                description: "Criminal Record updated successfully",
            })
            const formattedData = {
                id: result.newCriminalRecordId,
                profile_id: profileId,
                declaration: formData.declaration === 'yes' ? true : formData.declaration === 'no' ? false : null,
                details: formData.declaration === 'yes' ? formData.details || null : null
            }
            onSave(formattedData)
        } else {
            toast({
                title: "Error",
                description: result.error,
                variant: "destructive",
            })
        }
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="mt-6">
                    <h2 className="text-md font-medium text-gray-400">Declaration</h2>
                    <div className="mt-4">
                        <FormField
                            control={form.control}
                            name="declaration"
                            render={({ field }) => (
                                <FormItem className="sm:col-span-3 space-y-3">
                                    <FormLabel>
                                        Have you ever been convicted of a criminal offense?
                                    </FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={(value: "yes" | "no") => field.onChange(value)}
                                            value={field.value}
                                            className="flex space-x-4 mt-2"
                                        >
                                            {["yes", "no"].map((option) => (
                                                <FormItem
                                                    key={option}
                                                    className="flex items-center space-x-3 space-y-0"
                                                >
                                                    <FormControl>
                                                        <RadioGroupItem value={option} />
                                                    </FormControl>
                                                    <FormLabel className="font-normal ml-2">
                                                        {option.charAt(0).toUpperCase() + option.slice(1)}
                                                    </FormLabel>
                                                </FormItem>
                                            ))}
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                {form.watch("declaration") === "yes" && (
                    <div className="mt-8">
                        <FormField
                            control={form.control}
                            name="details"
                            render={({ field }) => (
                                <FormItem className="sm:col-span-full">
                                    <FormLabel>Details</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            rows={3}
                                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                            placeholder="Tell us more..."
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                )}

                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                </div>
            </form>
        </Form>
    )
} 