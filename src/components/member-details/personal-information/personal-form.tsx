"use client"

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { CalendarIcon, Check, ChevronDown } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import type { PersonalInfo } from "@/types/members/member-details"
import { Input } from "@/components/ui/input"
import { singaporeIdValidation } from "@/lib/identificationValidation"
import { useSystemOptions } from "@/hooks/use-system-options"
import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { MultiSelect } from "@/components/ui/multi-select"
import { updatePersonalInfo } from "@/services/members/member-server.service"

interface personalInformationFormProps {
    data: PersonalInfo | null
    profileId: string
    onSave: (data: Partial<PersonalInfo>) => void
    onCancel: () => void
}

const personalInformationSchema = z.object({
    fullName: z.string().min(1, "Full name is required"),
    title: z.string().min(1, "Salutation is required"),
    gender: z.string().optional(),
    dob: z.date().optional(),
    nationality: z.string().min(1, "Nationality is required"),
    placeOfBirth: z.string().optional(),
    idType: z.string().min(1, "ID Type is required"),
    idNumber: z.string().min(1, "NRIC/Passport No./Work Permit No./S Pass/Fin No. is required"),
    acesMember: z.boolean().optional(),
    languageWritten: z.array(z.string()).optional(),
    languageSpoken: z.array(z.string()).optional(),
    industrySector: z.string().optional(),
}).superRefine((data, ctx) => {
    const validationResult = singaporeIdValidation(data.idType, data.idNumber);

    if (typeof validationResult === "string") {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: validationResult,
            path: ["nricPassport"],
        });
    }

    if (!data.dob) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Date of Birth is required",
            path: ["dob"],
        });
    }

    if (!data.gender || (data.gender !== 'male' && data.gender !== 'female')) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "You need to select a valid gender.",
            path: ["gender"],
        });
    }
})

export type PersonalInformationFormData = z.infer<typeof personalInformationSchema>

export function PersonalInformationForm({ data, profileId, onSave, onCancel }: personalInformationFormProps) {
    const form = useForm<PersonalInformationFormData>({
        resolver: zodResolver(personalInformationSchema),
        mode: "onChange",
        defaultValues: {
            fullName: data?.full_name || "",
            title: data?.title || "",
            gender: data?.gender || undefined,
            dob: data?.date_of_birth ? new Date(data.date_of_birth) : undefined,
            nationality: data?.nationality || "",
            placeOfBirth: data?.place_of_birth || "",
            idType: data?.identification_type || "",
            idNumber: data?.identification_no || "",
            acesMember: data?.is_aces_member ?? undefined,
            languageWritten: data?.written_languages || [],
            languageSpoken: data?.spoken_languages || [],
            industrySector: data?.industry_id || ""
        },
    })
    const { options, isLoading, error } = useSystemOptions()
    const [searchTerm, setSearchTerm] = useState("")
    const [selectedIndustries, setSelectedIndustries] = useState<{
        name?: string;
        group?: string;
    } | null>(data ? {
        name: data.industries.name ?? undefined,
        group: data.industries.group ?? undefined
    } : null);

    const onSubmit = async (formData: PersonalInformationFormData) => {
        const result = await updatePersonalInfo(profileId, formData)
        if (result.success && data) {
            toast({
                title: "Success",
                description: "Personal Information updated successfully",
            })
            const formattedValue = {
                full_name: formData.fullName,
                title: formData.title,
                gender: formData.gender || null,
                date_of_birth: formData.dob?.toISOString().slice(0, 10) || null,
                nationality: formData.nationality,
                place_of_birth: formData.placeOfBirth || null,
                identification_type: formData.idType,
                identification_no: formData.idNumber,
                is_aces_member: formData.acesMember ?? null,
                spoken_languages: formData.languageSpoken || null,
                written_languages: formData.languageWritten || null,
                industry_id: formData.industrySector || null,
                industries: {
                    name: selectedIndustries?.name || "",
                    group: selectedIndustries?.group || "",
                }
            }
            onSave(formattedValue)
        } else {
            toast({
                title: "Error",
                description: result.error,
                variant: "destructive",
            })
        }
    }

    if (isLoading) {
        return (
            <Card>
                <CardContent className="flex justify-center items-center p-6">
                    <LoadingSpinner />
                </CardContent>
            </Card>
        )
    }

    if (error || !options) {
        return (
            <Card>
                <CardContent className="text-center text-red-500 p-6">
                    Failed to load form options
                </CardContent>
            </Card>
        )
    }

    const filteredCountries = options?.countries.filter((country) =>
        country.label.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const groupedIndustries = options?.industries.reduce((acc, industry) => {
        const group = industry.group;
        if (!acc[group]) {
            acc[group] = [];
        }
        acc[group].push(industry);
        return acc;
    }, {} as Record<string, typeof options.industries>)

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                            <FormItem >
                                <FormLabel>
                                    Salutation
                                </FormLabel>
                                <FormControl>
                                    <Select
                                        onValueChange={(value) => field.onChange(value)}
                                        value={field.value}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Salutation" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {options.salutations.map((salutation) => (
                                                <SelectItem
                                                    key={salutation.code}
                                                    value={salutation.code}
                                                >
                                                    {salutation.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="gender"
                        render={({ field }) => (
                            <FormItem className="space-y-4">
                                <FormLabel>
                                    Gender
                                </FormLabel>
                                <FormControl>
                                    <RadioGroup
                                        onValueChange={field.onChange}
                                        value={field.value}
                                        className="flex space-x-4"
                                    >
                                        <FormItem className="flex items-center space-x-3 space-y-0">
                                            <FormControl>
                                                <RadioGroupItem value="male" />
                                            </FormControl>
                                            <FormLabel className="font-normal ml-2 ">Male</FormLabel>
                                        </FormItem>

                                        <FormItem className="flex items-center space-x-3 space-y-0">
                                            <FormControl>
                                                <RadioGroupItem value="female" />
                                            </FormControl>
                                            <FormLabel className="font-normal ml-2 ">
                                                Female
                                            </FormLabel>
                                        </FormItem>
                                    </RadioGroup>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="fullName"
                        render={({ field }) => (
                            <FormItem className="sm:col-span-2">
                                <FormLabel>
                                    Full Name
                                </FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter Full Name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="dob"
                        render={({ field }) => (
                            <FormItem >
                                <FormLabel>
                                    Date of Birth
                                </FormLabel>
                                <FormControl>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <div className="relative">
                                                <Input
                                                    type="text"
                                                    readOnly
                                                    className="pointer-events-none"
                                                    placeholder="Pick a date"
                                                    value={field.value ? format(field.value, "PPP") : ""}
                                                />
                                                <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                                            </div>
                                        </PopoverTrigger>
                                        <PopoverContent>
                                            <Calendar
                                                mode="single"
                                                selected={field.value}
                                                onSelect={(date) => {
                                                    if (date) {
                                                        const utcDate = new Date(
                                                            Date.UTC(
                                                                date.getFullYear(),
                                                                date.getMonth(),
                                                                date.getDate()
                                                            )
                                                        );
                                                        field.onChange(utcDate);
                                                    }
                                                }}
                                                endMonth={new Date()}
                                                disabled={[{ after: new Date() }]}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="placeOfBirth"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Place Of Birth</FormLabel>
                                <Popover modal>
                                    <PopoverTrigger asChild>
                                        <FormControl>
                                            <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                                                <span className="truncate">
                                                    {field.value
                                                        ? options.countries.find((c) => c.code === field.value)?.label
                                                        : "Select Place Of Birth"}
                                                </span>
                                                <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                                            </Button>
                                        </FormControl>
                                    </PopoverTrigger>
                                    <PopoverContent className="p-0">
                                        <Command>
                                            <CommandInput
                                                placeholder="Search Place Of Birth..."
                                                value={searchTerm}
                                                onInput={(e) => setSearchTerm(e.currentTarget.value)}
                                            />
                                            <CommandList>
                                                {filteredCountries?.length === 0 ? (
                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                ) : (
                                                    <CommandGroup>
                                                        {filteredCountries?.map((country) => (
                                                            <CommandItem
                                                                key={country.code}
                                                                value={country.label}
                                                                onSelect={() => field.onChange(country.code)}
                                                            >
                                                                {country.label}
                                                                <Check
                                                                    className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                                                        }`}
                                                                />
                                                            </CommandItem>
                                                        ))}
                                                    </CommandGroup>
                                                )}
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="idType"
                        render={({ field }) => (
                            <FormItem >
                                <FormLabel>
                                    Identification Type
                                </FormLabel>
                                <FormControl>
                                    <Select
                                        onValueChange={field.onChange}
                                        {...field}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Identification Type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {options.idTypes.map((identification) => (
                                                <SelectItem
                                                    key={identification.code}
                                                    value={identification.code}
                                                >
                                                    {identification.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="idNumber"
                        render={({ field }) => (
                            <FormItem >
                                <FormLabel>
                                    NRIC/Passport No./Work Permit No./S Pass/Fin No.
                                </FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter NRIC/Passport No./Work Permit No./S Pass/Fin No."
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="nationality"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Nationality</FormLabel>
                                <Popover modal>
                                    <PopoverTrigger asChild>
                                        <FormControl>
                                            <Button variant="outline" className="w-full justify-start px-3 py-2 text-sm">
                                                <span className="truncate">
                                                    {field.value
                                                        ? options.countries.find((c) => c.code === field.value)?.label
                                                        : "Select Nationality"}
                                                </span>
                                                <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
                                            </Button>
                                        </FormControl>
                                    </PopoverTrigger>
                                    <PopoverContent className="p-0">
                                        <Command>
                                            <CommandInput
                                                placeholder="Search nationality..."
                                                value={searchTerm}
                                                onInput={(e) => setSearchTerm(e.currentTarget.value)}
                                            />
                                            <CommandList>
                                                {filteredCountries?.length === 0 ? (
                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                ) : (
                                                    <CommandGroup>
                                                        {filteredCountries?.map((country) => (
                                                            <CommandItem
                                                                key={country.code}
                                                                value={country.label}
                                                                onSelect={() => field.onChange(country.code)}
                                                            >
                                                                {country.label}
                                                                <Check
                                                                    className={`ml-auto ${field.value === country.code ? "opacity-100" : "opacity-0"
                                                                        }`}
                                                                />
                                                            </CommandItem>
                                                        ))}
                                                    </CommandGroup>
                                                )}
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="acesMember"
                        render={({ field }) => (
                            <FormItem className="space-y-4">
                                <FormLabel>
                                    ACES Member?
                                </FormLabel>
                                <FormControl>
                                    <RadioGroup
                                        onValueChange={(value) => {
                                            if (value === "yes") {
                                                field.onChange(true);
                                            } else if (value === "no") {
                                                field.onChange(false);
                                            } else {
                                                field.onChange(undefined);
                                            }
                                        }}
                                        value={
                                            field.value === true
                                                ? "yes"
                                                : field.value === false
                                                    ? "no"
                                                    : undefined
                                        }
                                        className="flex space-x-4"
                                    >
                                        <FormItem className="flex items-center space-x-3 space-y-0">
                                            <FormControl>
                                                <RadioGroupItem value="yes" />
                                            </FormControl>
                                            <FormLabel className="font-normal ml-2">Yes</FormLabel>
                                        </FormItem>
                                        <FormItem className="flex items-center space-x-3 space-y-0">
                                            <FormControl>
                                                <RadioGroupItem value="no" />
                                            </FormControl>
                                            <FormLabel className="font-normal ml-2">No</FormLabel>
                                        </FormItem>
                                    </RadioGroup>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="languageWritten"
                        render={({ field }) => {
                            {
                                const languages = options.languages.map((lang) => ({
                                    label: lang.label,
                                    value: lang.code,
                                }));

                                return (
                                    <FormItem >
                                        <FormLabel>Language Written</FormLabel>
                                        <FormControl>
                                            <MultiSelect
                                                options={languages}
                                                defaultValue={field.value}
                                                onValueChange={(selectedValues) =>
                                                    field.onChange(selectedValues)
                                                }
                                                placeholder="Select languages"
                                                maxCount={3}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )
                            }
                        }}
                    />

                    <FormField
                        control={form.control}
                        name="languageSpoken"
                        render={({ field }) => {
                            {
                                const languages = options.languages.map((lang) => ({
                                    label: lang.label,
                                    value: lang.code,
                                }));

                                return (
                                    <FormItem >
                                        <FormLabel>Language Spoken</FormLabel>
                                        <FormControl>
                                            <MultiSelect
                                                options={languages}
                                                defaultValue={field.value}
                                                onValueChange={(selectedValues) =>
                                                    field.onChange(selectedValues)
                                                }
                                                placeholder="Select languages"
                                                maxCount={3}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )
                            }
                        }}
                    />

                    <FormField
                        control={form.control}
                        name="industrySector"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Industry Sector</FormLabel>
                                <FormControl>
                                    <Select
                                        value={field.value}
                                        onValueChange={(selectedId) => {
                                            field.onChange(selectedId)
                                            const selectedGroup = options.industries.find((industry) => industry.id === selectedId)
                                            if (selectedGroup) {
                                                setSelectedIndustries({ name: selectedGroup.name, group: selectedGroup.group })
                                            }
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Industry Sector" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(groupedIndustries).map(([group, items]) => (
                                                <SelectGroup key={group}>
                                                    <SelectLabel>{group}</SelectLabel>
                                                    {items.map((industry) => (
                                                        <SelectItem key={industry.id} value={industry.id}>
                                                            {industry.name || "Unnamed Industry"}
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                </div>
            </form>
        </Form>
    )
} 