"use client"

import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { getOptionLabel, getLanguageLabels } from "@/services/system-options.service"
import type { PersonalInfo } from "@/types/members/member-details"
import { useEffect, useState } from "react";

interface PersonalInformationDetailsProps {
    personalInfo: PersonalInfo;
}

export function PersonalInformationDetails({ personalInfo }: PersonalInformationDetailsProps) {
    const [loading, setLoading] = useState(true);
    const [labels, setLabels] = useState({
        placeOfBirthLabel: '--',
        titleLabel: '--',
        idTypeLabel: '--',
        nationalityLabel: '--',
        genderLabel: '--',
        acesLabel: '--',
        writtenLangLabel: '--',
        spokenLangLabel: '--',
    });

    // Fetch the labels asynchronously using useEffect
    useEffect(() => {
        const fetchLabels = async () => {
            setLoading(true);
            const [
                placeOfBirthLabel,
                titleLabel,
                idType<PERSON>abel,
                nationalityLabel,
                genderLabel,
                acesLabel,
                writtenLangLabel,
                spokenLangLabel
            ] = await Promise.all([
                personalInfo.place_of_birth ? getOptionLabel(personalInfo.place_of_birth, 'country') : Promise.resolve('--'),
                personalInfo.title ? getOptionLabel(personalInfo.title, 'salutation') : Promise.resolve('--'),
                personalInfo.identification_type ? getOptionLabel(personalInfo.identification_type, 'id_type') : Promise.resolve('--'),
                personalInfo.nationality ? getOptionLabel(personalInfo.nationality, 'country') : Promise.resolve('--'),
                personalInfo.gender ? (personalInfo.gender === 'male' ? 'Male' : 'Female') : '--',
                typeof personalInfo.is_aces_member === 'boolean' ? (personalInfo.is_aces_member ? 'Yes' : 'No') : '--',
                personalInfo.written_languages ? getLanguageLabels(personalInfo.written_languages) : Promise.resolve('--'),
                personalInfo.spoken_languages ? getLanguageLabels(personalInfo.spoken_languages) : Promise.resolve('--'),
            ]);

            setLabels({
                placeOfBirthLabel,
                titleLabel,
                idTypeLabel,
                nationalityLabel,
                genderLabel,
                acesLabel,
                writtenLangLabel,
                spokenLangLabel,
            });
            setLoading(false);
        };

        fetchLabels();
    }, [personalInfo]);

    if (loading) {
        return (
            <LoadingSpinner />
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Full Name</p>
                <p className="text-sm text-muted-foreground">{personalInfo.full_name || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Date of Birth</p>
                <p className="text-sm text-muted-foreground">{personalInfo.date_of_birth || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Place of Birth</p>
                <p className="text-sm text-muted-foreground">{labels.placeOfBirthLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Title</p>
                <p className="text-sm text-muted-foreground">{labels.titleLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Identification Type</p>
                <p className="text-sm text-muted-foreground">{labels.idTypeLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Identification No</p>
                <p className="text-sm text-muted-foreground">{personalInfo.identification_no || '--'}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Nationality</p>
                <p className="text-sm text-muted-foreground">{labels.nationalityLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Gender</p>
                <p className="text-sm text-muted-foreground">{labels.genderLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">ACES Member</p>
                <p className="text-sm text-muted-foreground">{labels.acesLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Language Written</p>
                <p className="text-sm text-muted-foreground">{labels.writtenLangLabel}</p>
            </div>
            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Language Spoken</p>
                <p className="text-sm text-muted-foreground">{labels.spokenLangLabel}</p>
            </div>

            <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Industries name</p>
                <p className="text-sm text-muted-foreground">{personalInfo.industries.name || '--'}</p>
            </div>
        </div>
    );
}