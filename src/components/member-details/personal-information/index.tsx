"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import type { Member, PersonalInfo } from "@/types/members/member-details"
import { PersonalInformationForm } from "@/components/member-details/personal-information/personal-form"
import { PersonalInformationDetails } from "@/components/member-details/personal-information/personal-view"

interface PersonalInformationProps {
    member: Member
    onUpdate: (updatedMember: Member) => void
    allowEdit?: boolean
}

export function PersonalInformation({ member, onUpdate, allowEdit }: PersonalInformationProps) {
    const [isEditing, setisEditing] = useState(false)

    const onSave = async (updatedPersonalInfo: Partial<PersonalInfo>) => {
        onUpdate({
            ...member,
            personalInfo: {
                ...member.personalInfo,
                ...updatedPersonalInfo
            }
        })
        setisEditing(false)
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Personal Information</CardTitle>
                {(!isEditing && allowEdit) && (
                    <div className="space-x-2">
                        <Button variant="ghost" onClick={() => setisEditing(true)}>
                            Edit
                        </Button>
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {isEditing ? (
                    <PersonalInformationForm
                        data={member.personalInfo}
                        profileId={member.profileId}
                        onSave={onSave}
                        onCancel={() => setisEditing(false)}
                    />
                ) : (
                        <PersonalInformationDetails personalInfo={member.personalInfo} />
                )}
            </CardContent>
        </Card>
    )
}

