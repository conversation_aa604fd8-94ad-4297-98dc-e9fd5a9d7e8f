import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// This would typically come from your API
const auditLogs = [
  {
    id: 1,
    date: "2023-12-19 14:30:00",
    action: "Updated contact information",
    field: "Mobile Number",
    oldValue: "+6587654320",
    newValue: "+6587654321",
    user: "<EMAIL>",
  },
  {
    id: 2,
    date: "2023-12-18 11:20:00",
    action: "Updated employment details",
    field: "Designation",
    oldValue: "Junior Designer",
    newValue: "Graphic Designer",
    user: "<EMAIL>",
  },
]

export function AuditLogs({ memberId }: { memberId: string }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Audit Logs</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Action</TableHead>
              <TableHead>Field</TableHead>
              <TableHead>Old Value</TableHead>
              <TableHead>New Value</TableHead>
              <TableHead>User</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {auditLogs.map((log) => (
              <TableRow key={log.id}>
                <TableCell>{log.date}</TableCell>
                <TableCell>{log.action}</TableCell>
                <TableCell>{log.field}</TableCell>
                <TableCell>{log.oldValue}</TableCell>
                <TableCell>{log.newValue}</TableCell>
                <TableCell>{log.user}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

