import React from 'react'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data for activities
const activities = [
  { id: 1, action: "Logged in", date: "2023-12-20 09:15:00" },
  { id: 2, action: "Updated profile", date: "2023-12-19 14:30:00" },
  { id: 3, action: "Submitted application", date: "2023-12-18 11:45:00" },
  { id: 4, action: "Uploaded document", date: "2023-12-17 16:20:00" },
  { id: 5, action: "Changed password", date: "2023-12-16 10:05:00" },
  { id: 6, action: "Attended webinar", date: "2023-12-15 13:00:00" },
  { id: 7, action: "Renewed membership", date: "2023-12-14 09:30:00" },
  { id: 8, action: "Downloaded certificate", date: "2023-12-13 15:45:00" },
  { id: 9, action: "Registered for event", date: "2023-12-12 11:20:00" },
  { id: 10, action: "Completed survey", date: "2023-12-11 14:10:00" },
]

export function Activities() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Member Activities</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {activities.map((activity) => (
              <TableRow key={activity.id}>
                <TableCell>{activity.date}</TableCell>
                <TableCell>{activity.action}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

