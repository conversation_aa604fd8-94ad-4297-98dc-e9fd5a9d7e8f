"use client"

import { useState } from "react"
import type { TabConfig, EditPermissionConfig } from "@/types/members/member-details"
import { DEFAULT_TAB_CONFIG, DEFAULT_EDIT_CONFIG, MEMBER_TABS } from "@/config/member-details"
import { Summary } from "@/components/member-details/summary"
import { PersonalInformation } from "@/components/member-details/personal-information"
import { ContactDetails } from "@/components/member-details/contact-details"
import { EmploymentDetails } from "@/components/member-details/employment/index"
import { CompanyInformation } from "@/components/member-details/company-information"
import { WorkExperience } from "@/components/member-details/work-experience/index"
import { EducationDetails } from "@/components/member-details/education-details/index"
import { CompanyNominees } from "@/components/member-details/company-nominees/index"
import { Awards } from "@/components/member-details/awards/index"
import { Projects } from "@/components/member-details/projects/index"
import { CriminalRecords } from "@/components/member-details/criminal-records"
import { Sponsorship } from "@/components/member-details/sponsorship"
import { Documents } from "@/components/member-details/documents"
import { ApplicationHistory } from "@/components/member-details/application-history"
import { AuditLogs } from "@/components/member-details/audit-logs"
import { PointsSummary } from "@/components/member-details/points-summary"
import { PointsHistory } from "@/components/member-details/points-history"
import { Activities } from "@/components/member-details/activities"
import { Button } from "@/components/ui/button"
import { StatusBadge } from "@/components/ui/status-badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import { ChevronDown } from 'lucide-react'
import { type Member } from "@/types/members/member-details"
import { DocumentService } from "@/services/document.service"

// Helper function to map membership status to StatusBadge variant
const getMembershipStatusVariant = (status?: string | null): "active" | "inactive" | "suspended" | "expired" | "terminated" | "pending" | "pending_renewal" | "pending_re_application" => {
  if (!status) return "inactive"
  
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "active"
    case "INACTIVE":
      return "inactive"
    case "SUSPENDED":
      return "suspended"
    case "EXPIRED":
      return "expired"
    case "TERMINATED":
      return "terminated"
    case "PENDING_RENEWAL":
      return "pending_renewal"
    case "PENDING_RE_APPLICATION":
      return "pending_re_application"
    default:
      return "pending"
  }
}

// Helper function to get appropriate action text based on membership status
const getActionText = (status?: string | null): string => {
  if (!status) return "Enable Account"
  
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "Suspend Account"
    case "INACTIVE":
      return "Activate Account"
    case "SUSPENDED":
      return "Reactivate Account"
    case "EXPIRED":
      return "Renew Membership"
    case "TERMINATED":
      return "Restore Account"
    case "PENDING_RENEWAL":
      return "Process Renewal"
    case "PENDING_RE_APPLICATION":
      return "Process Re-Application"
    default:
      return "Update Status"
  }
}

// Helper function to get appropriate action description based on membership status
const getActionDescription = (status?: string | null): string => {
  if (!status) return "Activate this account."
  
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "Temporarily suspend this account."
    case "INACTIVE":
      return "Reactivate this account."
    case "SUSPENDED":
      return "Remove suspension from this account."
    case "EXPIRED":
      return "Renew the expired membership."
    case "TERMINATED":
      return "Restore access to this terminated account."
    case "PENDING_RENEWAL":
      return "Process the pending membership renewal."
    case "PENDING_RE_APPLICATION":
      return "Process the pending re-application request."
    default:
      return "Update the status of this account."
  }
}

export function MemberDetails({
  member,
  tabConfig = DEFAULT_TAB_CONFIG,
  editConfig = DEFAULT_EDIT_CONFIG
}: {
  member: Member
  tabConfig?: TabConfig
  editConfig?: EditPermissionConfig
}) {
  const [isDownloading, setIsDownloading] = useState(false)
  const [memberData, setMemberData] = useState<Member>(member)
  const { toast } = useToast()

  const handleDownloadDocument = async (documentId: string) => {
    try {
      setIsDownloading(true)
      const document = memberData.documents.find(doc => doc.id === documentId)
      if (!document) throw new Error("Document not found")

      await DocumentService.downloadDocument(documentId, document.path)
      toast({
        title: "Success",
        description: "Document downloaded successfully",
      })
    } catch (error) {
      console.error("Download error:", error)
      toast({
        title: "Error",
        description: "Failed to download document",
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
    }
  }

  const handleDownloadAllDocuments = async () => {
    try {
      setIsDownloading(true)
      await DocumentService.downloadAllDocuments(memberData.documents)
      toast({
        title: "Success",
        description: "All documents downloaded successfully",
      })
    } catch (error) {
      console.error("Bulk download error:", error)
      toast({
        title: "Error",
        description: "Failed to download documents",
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
    }
  }

  const visibleTabs = MEMBER_TABS.filter(tab => tabConfig[tab.id as keyof TabConfig])

  return (
    <div className="p-4">
      <div className="mb-4 space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-semibold">
            {memberData.personalInfo.full_name}
          </h1>
          <div className="flex items-center gap-4">
            <StatusBadge 
              variant={getMembershipStatusVariant(memberData.memberships?.status)}
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="border-gray-300">
                  Actions
                  <ChevronDown className="ml-2 h-4 w-4 text-muted-foreground" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[240px]">
                <DropdownMenuItem className="py-2">
                  <div>
                    <div className="font-medium">{getActionText(memberData.memberships?.status)}</div>
                    <div className="text-sm text-muted-foreground">
                      {getActionDescription(memberData.memberships?.status)}
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="py-2">
                  <div>
                    <div className="font-medium">Export Data</div>
                    <div className="text-sm text-muted-foreground">Download all data for this account.</div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="py-2">
                  <div>
                    <div className="font-medium">Send Email</div>
                    <div className="text-sm text-muted-foreground">Send an email to this member.</div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="py-2">
                  <div>
                    <div className="font-medium">Reset Password</div>
                    <div className="text-sm text-muted-foreground">Send a password reset link.</div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <Tabs defaultValue="summary">
        <div className="mb-4">
          <ScrollArea>
            <div className="w-full relative h-10">
              <TabsList className="flex absolute h-10 border-b border-gray-200 bg-transparent">
                {visibleTabs.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="rounded-none border-b-2 border-transparent px-4 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-blue-600 data-[state=active]:border-blue-600 data-[state=active]:text-blue-600 flex-shrink-0"
                  >
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>

        <TabsContent value="summary">
          <Summary member={memberData} />
        </TabsContent>

        <TabsContent value="profile">
          <div className="space-y-4">
            <PersonalInformation
              member={memberData}
              onUpdate={(updatedMember) => setMemberData(updatedMember)}
              allowEdit={editConfig.personalInformation}
            />
            <ContactDetails
              member={memberData}
              onUpdate={(updatedMember) => setMemberData(updatedMember)}
              allowEdit={editConfig.contactDetails}
            />
            <EmploymentDetails
              member={memberData}
              onUpdate={(updatedMember) => setMemberData(updatedMember)}
              allowEdit={editConfig.employmentDetails}
            />
          </div>
        </TabsContent>

        <TabsContent value="company">
          <CompanyInformation
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.companyInformation}
          />
        </TabsContent>

        <TabsContent value="workExperience">
          <WorkExperience
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.workExperience}
          />
        </TabsContent>

        <TabsContent value="educationDetails">
          <EducationDetails
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.educationDetails}
          />
        </TabsContent>

        <TabsContent value="companyNominees">
          <CompanyNominees
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.companyNominees}
          />
        </TabsContent>

        <TabsContent value="awards">
          <Awards
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.awards}
          />
        </TabsContent>

        <TabsContent value="projectDetails">
          <Projects
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.projectDetails}
          />
        </TabsContent>

        <TabsContent value="criminalRecords">
          <CriminalRecords
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.criminalRecords}
          />
        </TabsContent>

        <TabsContent value="sponsorship">
          <Sponsorship
            member={memberData}
            onUpdate={(updatedMember) => setMemberData(updatedMember)}
            allowEdit={editConfig.sponsorship}
          />
        </TabsContent>

        <TabsContent value="documents">
          <Documents
            documents={memberData.documents}
            onDownload={handleDownloadDocument}
            onDownloadAll={handleDownloadAllDocuments}
            isDownloading={isDownloading}
            allowEdit={editConfig.documents}
          />
        </TabsContent>

        <TabsContent value="points">
          <>
            <PointsSummary />
            <PointsHistory />
          </>
        </TabsContent>

        <TabsContent value="applicationHistory">
          <ApplicationHistory applications={memberData.applications} />
        </TabsContent>

        <TabsContent value="auditLogs">
          <AuditLogs memberId={memberData.profileId} />
        </TabsContent>

        <TabsContent value="activities">
          <Activities />
        </TabsContent>
      </Tabs>
    </div>
  )
}

