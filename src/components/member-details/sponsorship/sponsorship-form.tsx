"use client"

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
    Form,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { toast } from "@/hooks/use-toast"
import type { sponsorshipDetails } from "@/types/members/member-details"
import { Input } from "@/components/ui/input"
import { updateSponsorshipDetails } from "@/services/members/member-server.service"

interface sponsorshipFormProps {
    data: sponsorshipDetails[] | null
    profileId: string
    onSave: (data: sponsorshipDetails[]) => void
    onCancel: () => void
}

const sponsorshipSchema = z.object({
    proposerId: z.string().optional(),
    proposerName: z.string().min(1, "Name is required"),
    proposerGrade: z.string().min(1, "Grade Of Membership is required"),
    proposerMembershipID: z.string().min(1, "Membership ID is required"),
    proposerDate: z.date().optional(),
    seconderId: z.string().optional(),
    seconderName: z.string().min(1, "Name is required"),
    seconderGrade: z.string().min(1, "Grade Of Membership is required"),
    seconderMembershipID: z.string().min(1, "Membership ID is required"),
    seconderDate: z.date().optional(),
})

export type SponsorshipFormData = z.infer<typeof sponsorshipSchema>

export function SponsorshipForm({ data, profileId, onSave, onCancel }: sponsorshipFormProps) {
    const proposer = data?.find(s => s.type === 'PROPOSER');
    const seconder = data?.find(s => s.type === 'SECONDER');
    const form = useForm<SponsorshipFormData>({
        resolver: zodResolver(sponsorshipSchema),
        mode: "onChange",
        defaultValues: {
            proposerId: proposer?.id || undefined,
            proposerName: proposer?.name || "",
            proposerGrade: proposer?.grade || "",
            proposerMembershipID: proposer?.membership_id || "",
            proposerDate: proposer?.date ? new Date(proposer.date) : new Date(),
            seconderId: seconder?.id || undefined,
            seconderName: seconder?.name || "",
            seconderGrade: seconder?.grade || "",
            seconderMembershipID: seconder?.membership_id || "",
            seconderDate: seconder?.date ? new Date(seconder.date) : new Date()
        },
    });

    const onSubmit = async (formData: SponsorshipFormData) => {
        const result = await updateSponsorshipDetails(profileId, formData)
        if (result.success && result.sponsorshipData) {
            toast({
                title: "Success",
                description: "Sponsorship updated successfully",
            })
            onSave(result.sponsorshipData)
        } else {
            toast({
                title: "Error",
                description: result.error,
                variant: "destructive",
            })
        }
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <h4 className="text-md font-medium">
                    Proposer
                </h4>

                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        name="proposerName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Name</FormLabel>
                                <Input placeholder="Enter Name" {...field} />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        name="proposerGrade"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Grade of Membership</FormLabel>
                                <Input placeholder="Enter Grade of Membership" {...field} />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        name="proposerMembershipID"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Membership ID</FormLabel>
                                <Input placeholder="Enter Membership ID" {...field} />
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <h4 className="text-md font-medium">
                    Seconder
                </h4>

                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        name="seconderName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Name</FormLabel>
                                <Input placeholder="Enter Name" {...field} />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        name="seconderGrade"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Grade of Membership</FormLabel>
                                <Input placeholder="Enter Grade of Membership" {...field} />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        name="seconderMembershipID"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Membership ID</FormLabel>
                                <Input placeholder="Enter Membership ID" {...field} />
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                </div>
            </form>
        </Form>
    )
} 