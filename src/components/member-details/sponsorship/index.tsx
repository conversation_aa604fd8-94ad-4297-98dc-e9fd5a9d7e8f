"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle } from "@/components/ui/sheet"
import { SponsorshipForm } from "@/components/member-details/sponsorship/sponsorship-form"
import { SponsorshipDetailsView } from "@/components/member-details/sponsorship/sponsorship-details-view"
import type { Member, sponsorshipDetails } from "@/types/members/member-details"

interface SponsorshipProps {
  member: Member
  onUpdate: (updatedMember: Member) => void
  allowEdit?: boolean
}

export function Sponsorship({ member, onUpdate, allowEdit }: SponsorshipProps) {
  const [isEditing, setisEditing] = useState(false)

  const handleSave = (updatedSponsorship: sponsorshipDetails[]) => {
    onUpdate({
      ...member,
      sponsorship: updatedSponsorship,
    })
    setisEditing(false)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Sponsorship</CardTitle>
        {(!isEditing && allowEdit) && (
          <div className="space-x-2">
            <Button variant="ghost" onClick={() => setisEditing(true)}>
              {member.sponsorship && member.sponsorship.length > 0 ? 'Edit' : 'Add'}
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Sheet open={isEditing} onOpenChange={setisEditing}>
          <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
            <SheetHeader>
              <SheetTitle>Criminal records</SheetTitle>
            </SheetHeader>
            <SponsorshipForm
              data={member.sponsorship}
              profileId={member.profileId}
              onSave={handleSave}
              onCancel={() => setisEditing(false)}
            />
          </SheetContent>
        </Sheet>

        <SponsorshipDetailsView
          data={member.sponsorship}
        />
      </CardContent>
    </Card>
  )
}

