"use client"

import { sponsorshipDetails } from "@/types/members/member-details"

interface SponsorshipDetailsViewProps {
    data: sponsorshipDetails[]
}

export function SponsorshipDetailsView({ data }: SponsorshipDetailsViewProps) {
    return (
        <div>
            {data && data.length > 0 ? (
                data.map((sponsor, index) => (
                    <div key={index} className="mb-4 last:mb-0">
                        <h4 className="text-md font-medium mb-4">
                            {sponsor.type.charAt(0) + sponsor.type.slice(1).toLowerCase()}
                        </h4>
                        <div className="grid gap-2 md:grid-cols-2">
                            <div className="space-y-1">
                                <p className="text-sm font-medium leading-none">Name</p>
                                <p className="text-sm text-muted-foreground">{sponsor.name}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium leading-none">Grade</p>
                                <p className="text-sm text-muted-foreground">{sponsor.grade}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium leading-none">Date</p>
                                <p className="text-sm text-muted-foreground">{sponsor.date}</p>
                            </div>
                        </div>
                    </div>
                ))
            ) : (
                <p>No sponsorship available.</p>
            )}
        </div>
    )
}
