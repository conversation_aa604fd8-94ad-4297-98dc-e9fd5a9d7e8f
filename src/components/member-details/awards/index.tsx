"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import type { Member, AwardDetails } from "@/types/members/member-details"
import { AwardDetailsView } from "@/components/member-details/awards/award-details-view"
import { AwardForm } from "@/components/member-details/awards/award-form"
import { deleteAward } from "@/services/members/member-server.service"

interface AwardsProps {
  member: Member
  onUpdate: (updatedMember: Member) => void
  allowEdit?: boolean
}

export function Awards({ member, onUpdate, allowEdit }: AwardsProps) {
  const [selectedAward, setSelectedAward] = useState<AwardDetails | null>(null)
  const [mode, setMode] = useState<'view' | 'edit' | 'add' | null>(null);
  const isSheetOpen = mode !== null;

  const handleAdd = () => {
    setSelectedAward(null)
    setMode('add')
  }

  const handleView = (data: AwardDetails) => {
    setSelectedAward(data)
    setMode('view')
  }

  const handleSave = (updatedAward: AwardDetails) => {
    onUpdate({
      ...member,
      awards: member.awards.some((award) => award.id === updatedAward.id)
        ? member.awards.map((award) =>
          award.id === updatedAward.id ? updatedAward : award
        ) : [...member.awards, updatedAward],
    })
    setSelectedAward(null)
    setMode(null)
  }

  const handleDelete = async (id: string) => {
    const result = await deleteAward(id)
    if (result.error) {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Success",
      description: "Nominee delete successfully",
    })
    onUpdate({
      ...member,
      awards: member.awards.filter((awd) => awd.id !== id),
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>
            Other Engineering Affiliation, Awards & Distinctions
          </CardTitle>
          <Button variant="ghost" onClick={handleAdd}>Add</Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Record Type</TableHead>
                <TableHead>Date Of Election</TableHead>
                <TableHead>Expiry Date</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {member.awards && member.awards.length > 0 ? (
                member.awards.map((award, index) => (
                  <TableRow key={`award-${index}`}>
                    <TableCell>{award.record_type || '--'}</TableCell>
                    <TableCell>{award.date_of_election || '--'}</TableCell>
                    <TableCell>{award.expiry_date || '--'}</TableCell>
                    <TableCell className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(award)}
                      >
                        View
                      </Button>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Are you absolutely sure?</DialogTitle>
                            <DialogDescription>
                              This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button type="button" variant="secondary">
                                Cancel
                              </Button>
                            </DialogClose>
                            <Button variant="destructive" type="submit" onClick={() => handleDelete(award.id)}>Delete</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    No other engineering affiliation, awards & distinctions available.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Sheet open={isSheetOpen} onOpenChange={(open) => !open && setMode(null)}>
        <SheetContent className="flex flex-col pt-10 sm:max-w-[700px]">
          <SheetHeader>
            <SheetTitle>Award Details</SheetTitle>
          </SheetHeader>
          <div className="overflow-auto">
            {(selectedAward && mode === 'view') ? (
              <AwardDetailsView
                data={selectedAward}
                onEdit={() => setMode('edit')}
                allowEdit={allowEdit}
              />
            ) : (mode === 'add' || mode === 'edit') ? (
              <AwardForm
                data={selectedAward}
                profileId={member.profileId}
                onSave={handleSave}
                onCancel={() => setMode(null)}
              />
            ) : null}
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
} 