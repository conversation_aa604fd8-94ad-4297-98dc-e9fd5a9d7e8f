"use client"

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns";
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar";
import { Textarea } from "@/components/ui/textarea";
import { CalendarIcon } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import type { AwardDetails } from "@/types/members/member-details"
import { updateAward } from "@/services/members/member-server.service";

interface AwardFormProps {
  data: AwardDetails | null
  profileId: string
  onSave: (data: AwardDetails) => void
  onCancel: () => void
}

const awardFormSchema = z
  .object({
    id: z.string().optional(),
    recordType: z.enum(['AFFILIATION', 'AWARD']).optional(),
    recordNumber: z.string().optional(),
    dateOfElection: z.date().optional(),
    details: z.string().optional(),
    abbreviation: z.string().optional(),
    expiryDate: z.date().optional(),
    nomineeId: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (!data.recordType || !data.dateOfElection || !data.details || !data.abbreviation || !data.expiryDate) {
      if (!data.recordType || data.recordType.length < 1) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Record Type is required.",
          path: ["recordType"],
        });
      }

      if (data.recordType === 'AFFILIATION' && !data.recordNumber) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Affiliation / Registration No is required when Affiliation is selected.",
          path: ["recordNumber"],
        });
      }

      if (!data.details || data.details.length < 10 || data.details.length > 150) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Details must be between 10 and 150 characters.",
          path: ["details"],
        });
      }

      if (!(data.dateOfElection instanceof Date) || isNaN(data.dateOfElection.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Date of Election is required.",
          path: ["dateOfElection"],
        });
      }
    }
  });

export type AwardFormData = z.infer<typeof awardFormSchema>;

export function AwardForm({ data, profileId, onSave, onCancel }: AwardFormProps) {
  const form = useForm<AwardFormData>({
    resolver: zodResolver(awardFormSchema),
    mode: "onChange",
    defaultValues: {
      id: data?.id || undefined,
      recordType: data?.record_type || undefined,
      recordNumber: data?.record_number || undefined,
      dateOfElection: data?.date_of_election ? new Date(data.date_of_election) : undefined,
      details: data?.detail || undefined,
      abbreviation: data?.abbreviation || undefined,
      expiryDate: data?.expiry_date ? new Date(data.expiry_date) : undefined,
    },
  });

  const onSubmit = async (formData: AwardFormData) => {
    const result = await updateAward(profileId, formData)
    if (result.success && result.newAwardId) {
      toast({
        title: "Success",
        description: "Award updated successfully",
      })
      const formattedData = {
        id: result.newAwardId,
        profile_id: profileId,
        record_type: formData.recordType || null,
        record_number: formData.recordNumber || null,
        date_of_election: formData.dateOfElection?.toISOString().slice(0, 10) || null,
        detail: formData.details || null,
        abbreviation: formData.abbreviation || null,
        expiry_date: formData.expiryDate?.toISOString().slice(0, 10) || null,
        nominee_id: null
      }
      onSave(formattedData)
    } else {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      })
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name={`recordType`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Record Type</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Record Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AFFILIATION">Affiliation (PE, ACES, TUCSS, RE/RTO etc.)</SelectItem>
                      <SelectItem value="AWARD">Awards</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`dateOfElection`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date of Election</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.watch('recordType') === "AFFILIATION" && (
            <FormField
              control={form.control}
              name={`recordNumber`}
              render={({ field }) => (
                <FormItem className="sm:grid-cols-2">
                  <FormLabel>Affiliation / Registration No</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter Affiliation / Registration No" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name={`details`}
            render={({ field }) => (
              <FormItem className="sm:col-span-full">
                <FormLabel>Details</FormLabel>
                <FormControl>
                  <Textarea
                    rows={3}
                    placeholder="Provide details of your affiliation, award, or distinction here..."
                    {...field}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`abbreviation`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Abbreviation</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Enter Abbreviation"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`expiryDate`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Expiry Date</FormLabel>
                <FormControl>
                  <Popover modal>
                    <PopoverTrigger asChild>
                      <div className="relative">
                        <Input
                          type="text"
                          readOnly
                          className="pointer-events-none"
                          placeholder="Pick a date"
                          value={field.value ? format(field.value, "PPP") : ""}
                        />
                        <CalendarIcon className="absolute right-2 top-2 h-5 w-5 text-gray-500" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent>
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            const utcDate = new Date(Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate()
                            ));
                            field.onChange(utcDate);
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  )
} 