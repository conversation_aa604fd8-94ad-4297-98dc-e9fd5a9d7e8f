"use client"

import { DetailViewWrapper } from "@/components/member-details/common/detail-view-wrapper"
import { AwardDetails } from "@/types/members/member-details"

interface AwardDetailsViewProps {
  data: AwardDetails
  onEdit?: () => void
  allowEdit?: boolean
}

export function AwardDetailsView({ data, onEdit, allowEdit = true }: AwardDetailsViewProps) {
  return (
    <DetailViewWrapper title={data?.record_type || "--"} onEdit={onEdit} allowEdit={allowEdit}>
      <div className="grid gap-2 md:grid-cols-2">
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Affiliation / Registration No</p>
          <p className="text-sm text-muted-foreground">{data?.record_number || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Date of Election</p>
          <p className="text-sm text-muted-foreground">{data?.date_of_election || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Details</p>
          <p className="text-sm text-muted-foreground">{data?.detail || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Abbreviation</p>
          <p className="text-sm text-muted-foreground">{data?.abbreviation || "--"}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">Expire Date</p>
          <p className="text-sm text-muted-foreground">{data?.expiry_date || "--"}</p>
        </div>
      </div>
    </DetailViewWrapper>
  )
} 