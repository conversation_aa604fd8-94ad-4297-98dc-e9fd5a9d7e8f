"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EmailTemplate, TemplatePreviewData } from "@/types/email-blast/email-template.types";
import { emailTemplatesService } from "@/services/email-blast/email-templates.service";
import { useState } from "react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Send, TestTube } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Card } from "@/components/ui/card";
import { VariableChip } from "../ui/variable-chip";

interface TestTemplateDialogProps {
  template: EmailTemplate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TestTemplateDialog({
  template,
  open,
  onOpenChange
}: TestTemplateDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [recipientEmail, setRecipientEmail] = useState('');
  const [variableValues, setVariableValues] = useState<Record<string, any>>(() => {
    const defaultValues: Record<string, any> = {};
    template.variables.forEach(variable => {
      defaultValues[variable.key] = variable.defaultValue || '';
    });
    return defaultValues;
  });

  const handleSendTest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!recipientEmail.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a recipient email address',
        variant: 'destructive'
      });
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      toast({
        title: 'Error',
        description: 'Please enter a valid email address',
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);
      
      const previewData: TemplatePreviewData = {
        templateId: template.id,
        variableValues,
        recipientEmail: recipientEmail.trim()
      };

      await emailTemplatesService.sendTestEmail(previewData);
      
      toast({
        title: 'Test Email Sent',
        description: `Test email sent successfully to ${recipientEmail}`,
        variant: 'default'
      });
      
      onOpenChange(false);
    } catch (error) {
      console.error('Error sending test email:', error);
      toast({
        title: 'Error',
        description: 'Failed to send test email. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVariableChange = (key: string, value: any) => {
    setVariableValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const requiredVariables = template.variables.filter(v => v.required);
  const optionalVariables = template.variables.filter(v => !v.required);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Test Template
          </DialogTitle>
          <DialogDescription>
            Send a test email for "{template.name}" to verify how it looks and works.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSendTest} className="space-y-6">
          {/* Recipient Email */}
          <div className="space-y-2">
            <Label htmlFor="recipient">Recipient Email</Label>
            <Input
              id="recipient"
              type="email"
              value={recipientEmail}
              onChange={(e) => setRecipientEmail(e.target.value)}
              placeholder="Enter email address to receive test"
              required
            />
          </div>

          {/* Template Variables */}
          {template.variables.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Label>Template Variables</Label>
                <span className="text-sm text-muted-foreground">
                  ({template.variables.length} variables)
                </span>
              </div>

              {/* Required Variables */}
              {requiredVariables.length > 0 && (
                <Card className="p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <h4 className="text-sm font-medium">Required Variables</h4>
                    <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                      {requiredVariables.length}
                    </span>
                  </div>
                  {requiredVariables.map((variable) => (
                    <div key={variable.id} className="space-y-2">
                      <Label htmlFor={variable.key} className="flex items-center gap-2">
                        <VariableChip variable={variable} size="sm" />
                        {variable.description && (
                          <span className="text-xs text-muted-foreground">
                            {variable.description}
                          </span>
                        )}
                      </Label>
                      <Input
                        id={variable.key}
                        type={variable.type === 'number' ? 'number' : 
                              variable.type === 'email' ? 'email' : 
                              variable.type === 'date' ? 'date' : 'text'}
                        value={variableValues[variable.key] || ''}
                        onChange={(e) => handleVariableChange(variable.key, e.target.value)}
                        placeholder={variable.placeholder || `Enter ${variable.name}`}
                        required={variable.required}
                      />
                    </div>
                  ))}
                </Card>
              )}

              {/* Optional Variables */}
              {optionalVariables.length > 0 && (
                <Card className="p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <h4 className="text-sm font-medium">Optional Variables</h4>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {optionalVariables.length}
                    </span>
                  </div>
                  {optionalVariables.map((variable) => (
                    <div key={variable.id} className="space-y-2">
                      <Label htmlFor={variable.key} className="flex items-center gap-2">
                        <VariableChip variable={variable} size="sm" />
                        {variable.description && (
                          <span className="text-xs text-muted-foreground">
                            {variable.description}
                          </span>
                        )}
                      </Label>
                      <Input
                        id={variable.key}
                        type={variable.type === 'number' ? 'number' : 
                              variable.type === 'email' ? 'email' : 
                              variable.type === 'date' ? 'date' : 'text'}
                        value={variableValues[variable.key] || ''}
                        onChange={(e) => handleVariableChange(variable.key, e.target.value)}
                        placeholder={variable.placeholder || `Enter ${variable.name} (optional)`}
                      />
                    </div>
                  ))}
                </Card>
              )}
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Test Email
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}