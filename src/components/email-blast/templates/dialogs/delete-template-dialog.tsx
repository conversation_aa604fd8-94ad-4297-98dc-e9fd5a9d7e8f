"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { EmailTemplate } from "@/types/email-blast/email-template.types";
import { useEmailTemplate } from "@/hooks/email-blast/use-email-templates";
import { useState } from "react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface DeleteTemplateDialogProps {
  template: EmailTemplate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function DeleteTemplateDialog({
  template,
  open,
  onOpenChange,
  onSuccess
}: DeleteTemplateDialogProps) {
  const { deleteTemplate } = useEmailTemplate(template.id);
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    try {
      setLoading(true);
      const success = await deleteTemplate();
      if (success) {
        onOpenChange(false);
        onSuccess?.();
      }
    } catch (error) {
      console.error('Error deleting template:', error);
    } finally {
      setLoading(false);
    }
  };

  const canDelete = !template.isSystem;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Template</AlertDialogTitle>
          <AlertDialogDescription>
            {canDelete ? (
              <>
                Are you sure you want to delete the template <strong>"{template.name}"</strong>?
                <br />
                <br />
                This action cannot be undone. Any campaigns using this template will
                continue to work, but you won't be able to use this template for new campaigns.
              </>
            ) : (
              <>
                The template <strong>"{template.name}"</strong> is a system template and cannot be deleted.
                <br />
                <br />
                System templates are protected to ensure core functionality remains available.
                You can duplicate this template to create a custom version.
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
          {canDelete && (
            <AlertDialogAction
              onClick={handleDelete}
              disabled={loading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary" />
                  Deleting...
                </>
              ) : (
                'Delete Template'
              )}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}