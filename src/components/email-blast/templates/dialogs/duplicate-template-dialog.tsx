"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Description,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { EmailTemplate, TemplateDuplicateOptions } from "@/types/email-blast/email-template.types";
import { useEmailTemplate } from "@/hooks/email-blast/use-email-templates";
import { useState } from "react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Copy } from "lucide-react";

interface DuplicateTemplateDialogProps {
  template: EmailTemplate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (newTemplate: EmailTemplate) => void;
}

export function DuplicateTemplateDialog({
  template,
  open,
  onOpenChange,
  onSuccess
}: DuplicateTemplateDialogProps) {
  const { duplicateTemplate } = useEmailTemplate(template.id);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<TemplateDuplicateOptions>({
    name: `${template.name} (Copy)`,
    description: template.description ? `Copy of ${template.description}` : undefined,
    preserveVariables: true,
    copyTags: true
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      return;
    }

    try {
      setLoading(true);
      const newTemplate = await duplicateTemplate(formData);
      if (newTemplate) {
        onOpenChange(false);
        onSuccess?.(newTemplate);
        // Reset form
        setFormData({
          name: `${template.name} (Copy)`,
          description: template.description ? `Copy of ${template.description}` : undefined,
          preserveVariables: true,
          copyTags: true
        });
      }
    } catch (error) {
      console.error('Error duplicating template:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Copy className="h-5 w-5" />
            Duplicate Template
          </DialogTitle>
          <DialogDescription>
            Create a copy of "{template.name}" with your own settings.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Template Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter template name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value || undefined }))}
              placeholder="Enter template description"
              rows={2}
            />
          </div>

          <div className="space-y-3">
            <Label>Copy Options</Label>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="preserveVariables"
                checked={formData.preserveVariables}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, preserveVariables: !!checked }))
                }
              />
              <Label htmlFor="preserveVariables" className="text-sm font-normal">
                Preserve template variables ({template.variables.length} variables)
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="copyTags"
                checked={formData.copyTags}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, copyTags: !!checked }))
                }
              />
              <Label htmlFor="copyTags" className="text-sm font-normal">
                Copy tags ({template.tags?.length || 0} tags)
              </Label>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.name.trim()}>
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary" />
                  Duplicating...
                </>
              ) : (
                'Create Copy'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}