"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TemplateCategory, TemplateFormData, TemplateStatus } from "@/types/email-blast/email-template.types";
import { emailTemplatesService } from "@/services/email-blast/email-templates.service";
import { useState, useRef } from "react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Upload, FileText, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";

interface ImportTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (template: any) => void;
}

type ImportMethod = 'file' | 'url' | 'text';

export function ImportTemplateDialog({
  open,
  onOpenChange,
  onSuccess
}: ImportTemplateDialogProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [importMethod, setImportMethod] = useState<ImportMethod>('file');
  const [importData, setImportData] = useState({
    name: '',
    category: 'custom' as TemplateCategory,
    description: '',
    file: null as File | null,
    url: '',
    htmlContent: ''
  });
  const [preview, setPreview] = useState<string>('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: 'File Too Large',
          description: 'Please select a file smaller than 5MB',
          variant: 'destructive'
        });
        return;
      }
      
      setImportData(prev => ({ ...prev, file }));
      
      // Read file content for preview
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target?.result as string;
        setPreview(content.substring(0, 500) + (content.length > 500 ? '...' : ''));
      };
      reader.readAsText(file);
    }
  };

  const handleUrlLoad = async () => {
    if (!importData.url.trim()) return;
    
    try {
      setLoading(true);
      const response = await fetch(importData.url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const content = await response.text();
      setPreview(content.substring(0, 500) + (content.length > 500 ? '...' : ''));
      setImportData(prev => ({ ...prev, htmlContent: content }));
    } catch (error) {
      toast({
        title: 'Failed to Load URL',
        description: error instanceof Error ? error.message : 'Unable to load content from URL',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!importData.name.trim()) {
      toast({
        title: 'Name Required',
        description: 'Please enter a template name',
        variant: 'destructive'
      });
      return;
    }

    let htmlContent = '';
    
    try {
      setLoading(true);
      
      // Get content based on import method
      if (importMethod === 'file' && importData.file) {
        const reader = new FileReader();
        htmlContent = await new Promise((resolve, reject) => {
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.onerror = () => reject(new Error('Failed to read file'));
          reader.readAsText(importData.file!);
        });
      } else if (importMethod === 'url' && importData.url) {
        const response = await fetch(importData.url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        htmlContent = await response.text();
      } else if (importMethod === 'text') {
        htmlContent = importData.htmlContent;
      }

      if (!htmlContent.trim()) {
        toast({
          title: 'No Content',
          description: 'Please provide content to import',
          variant: 'destructive'
        });
        return;
      }

      // Create template data
      const templateData: TemplateFormData = {
        name: importData.name,
        description: importData.description || undefined,
        category: importData.category,
        status: TemplateStatus.DRAFT,
        subject: 'Imported Template Subject', // Default subject
        body: htmlContent,
        bodyHtml: htmlContent,
        variables: [], // Will be detected from content
        tags: ['imported']
      };

      const newTemplate = await emailTemplatesService.create(templateData);
      
      toast({
        title: 'Template Imported',
        description: `Template "${importData.name}" imported successfully`,
        variant: 'default'
      });
      
      onSuccess?.(newTemplate);
      onOpenChange(false);
      
      // Reset form
      setImportData({
        name: '',
        category: TemplateCategory.CUSTOM,
        description: '',
        file: null,
        url: '',
        htmlContent: ''
      });
      setPreview('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error importing template:', error);
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Failed to import template',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const categoryOptions = [
    { value: 'newsletter', label: 'Newsletter' },
    { value: 'promotional', label: 'Promotional' },
    { value: 'announcement', label: 'Announcement' },
    { value: 'welcome', label: 'Welcome' },
    { value: 'followup', label: 'Follow-up' },
    { value: 'event', label: 'Event' },
    { value: 'survey', label: 'Survey' },
    { value: 'notification', label: 'Notification' },
    { value: 'custom', label: 'Custom' }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Template
          </DialogTitle>
          <DialogDescription>
            Import an email template from various sources to get started quickly.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Template Name</Label>
              <Input
                id="name"
                value={importData.name}
                onChange={(e) => setImportData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter template name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={importData.category}
                onValueChange={(value) => setImportData(prev => ({ ...prev, category: value as TemplateCategory }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={importData.description}
              onChange={(e) => setImportData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter template description"
              rows={2}
            />
          </div>

          {/* Import Method Selection */}
          <div className="space-y-3">
            <Label>Import Method</Label>
            <div className="grid grid-cols-3 gap-2">
              <Button
                type="button"
                variant={importMethod === 'file' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setImportMethod('file')}
                className="justify-start"
              >
                <FileText className="mr-2 h-4 w-4" />
                File Upload
              </Button>
              <Button
                type="button"
                variant={importMethod === 'url' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setImportMethod('url')}
                className="justify-start"
              >
                <Upload className="mr-2 h-4 w-4" />
                From URL
              </Button>
              <Button
                type="button"
                variant={importMethod === 'text' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setImportMethod('text')}
                className="justify-start"
              >
                <AlertCircle className="mr-2 h-4 w-4" />
                Paste HTML
              </Button>
            </div>
          </div>

          {/* Import Content */}
          {importMethod === 'file' && (
            <div className="space-y-2">
              <Label htmlFor="file">HTML File</Label>
              <Input
                id="file"
                ref={fileInputRef}
                type="file"
                accept=".html,.htm,.txt"
                onChange={handleFileChange}
              />
              <p className="text-xs text-muted-foreground">
                Accepts HTML files up to 5MB
              </p>
            </div>
          )}

          {importMethod === 'url' && (
            <div className="space-y-2">
              <Label htmlFor="url">Template URL</Label>
              <div className="flex gap-2">
                <Input
                  id="url"
                  type="url"
                  value={importData.url}
                  onChange={(e) => setImportData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https://example.com/template.html"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleUrlLoad}
                  disabled={!importData.url.trim() || loading}
                >
                  {loading ? <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary" /> : 'Load'}
                </Button>
              </div>
            </div>
          )}

          {importMethod === 'text' && (
            <div className="space-y-2">
              <Label htmlFor="htmlContent">HTML Content</Label>
              <Textarea
                id="htmlContent"
                value={importData.htmlContent}
                onChange={(e) => {
                  setImportData(prev => ({ ...prev, htmlContent: e.target.value }));
                  setPreview(e.target.value.substring(0, 500) + (e.target.value.length > 500 ? '...' : ''));
                }}
                placeholder="Paste your HTML template content here..."
                rows={8}
                className="font-mono text-sm"
              />
            </div>
          )}

          {/* Preview */}
          {preview && (
            <Card className="p-4">
              <div className="space-y-2">
                <Label>Content Preview</Label>
                <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto whitespace-pre-wrap">
                  {preview}
                </pre>
              </div>
            </Card>
          )}

          {/* Warning */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Imported templates will be saved as drafts and may need editing before use.
              Template variables will need to be configured manually.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary" />
                  Importing...
                </>
              ) : (
                'Import Template'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}