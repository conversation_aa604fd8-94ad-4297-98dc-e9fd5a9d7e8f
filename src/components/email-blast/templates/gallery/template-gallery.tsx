"use client";

import { useState, useMemo } from "react";
import { EmailTemplate, TemplateListFilters } from "@/types/email-blast/email-template.types";
import { useEmailTemplates, useTemplateFilters } from "@/hooks/email-blast/use-email-templates";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
// import { DataTable } from "@/components/ui/data-table";
import { 
  Search, 
  Plus, 
  Grid3X3, 
  List, 
  Filter,
  SortAsc,
  Download,
  Upload,
  RefreshCw,
  X
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getTemplateColumns } from "./columns";
import { TemplateCard } from "../ui/template-card";
import { TemplateCategoryFilter } from "../ui/template-category-filter";
import { DeleteTemplateDialog } from "../dialogs/delete-template-dialog";
import { DuplicateTemplateDialog } from "../dialogs/duplicate-template-dialog";
import { TestTemplateDialog } from "../dialogs/test-template-dialog";
import { ImportTemplateDialog } from "../dialogs/import-template-dialog";
import { TemplatePreview } from "../ui/template-preview";
import { cn } from "@/lib/utils";
import Link from "next/link";

type ViewMode = 'grid' | 'table';
type SortOption = 'name' | 'updated' | 'created' | 'category' | 'status';

interface TemplateGalleryProps {
  initialFilters?: TemplateListFilters;
  showFilters?: boolean;
  showSearch?: boolean;
  showActions?: boolean;
  defaultViewMode?: ViewMode;
  className?: string;
}

export function TemplateGallery({
  initialFilters,
  showFilters = true,
  showSearch = true,
  showActions = true,
  defaultViewMode = 'grid',
  className
}: TemplateGalleryProps) {
  // State
  const [viewMode, setViewMode] = useState<ViewMode>(defaultViewMode);
  const [sortBy, setSortBy] = useState<SortOption>('updated');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  
  // Dialog states
  const [deleteDialog, setDeleteDialog] = useState<EmailTemplate | null>(null);
  const [duplicateDialog, setDuplicateDialog] = useState<EmailTemplate | null>(null);
  const [testDialog, setTestDialog] = useState<EmailTemplate | null>(null);
  const [importDialog, setImportDialog] = useState(false);
  const [previewDialog, setPreviewDialog] = useState<EmailTemplate | null>(null);

  // Hooks
  const { filters, setSearch, setCategory, setStatus, clearAllFilters, hasFilters } = useTemplateFilters();
  const { templates, loading, error, refresh } = useEmailTemplates({
    initialFilters: { ...initialFilters, ...filters, search: searchQuery }
  });

  // Sorted and filtered templates
  const sortedTemplates = useMemo(() => {
    let filtered = [...templates];

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'updated':
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
        case 'created':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'category':
          aValue = a.category;
          bValue = b.category;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [templates, sortBy, sortOrder]);

  // Handlers
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setSearch(value);
  };

  const handleRefresh = () => {
    refresh();
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    clearAllFilters();
  };

  // Dialog handlers
  const handleEdit = (template: EmailTemplate) => {
    // Navigation handled by Link components
  };

  const handleDelete = (template: EmailTemplate) => {
    setDeleteDialog(template);
  };

  const handleDuplicate = (template: EmailTemplate) => {
    setDuplicateDialog(template);
  };

  const handleTest = (template: EmailTemplate) => {
    setTestDialog(template);
  };

  const handlePreview = (template: EmailTemplate) => {
    setPreviewDialog(template);
  };

  const handleDialogSuccess = () => {
    refresh();
  };

  // Table columns (commented out until DataTable compatibility is fixed)
  // const columns = getTemplateColumns({
  //   onEdit: handleEdit,
  //   onDelete: handleDelete,
  //   onDuplicate: handleDuplicate,
  //   onTest: handleTest,
  //   onPreview: handlePreview
  // });

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">Failed to load templates</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Email Templates</h2>
          <p className="text-muted-foreground">
            Manage and organize your email templates
          </p>
        </div>

        {showActions && (
          <div className="flex items-center gap-2">
            <Button onClick={() => setImportDialog(true)} variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            
            <Button asChild>
              <Link href="/email-blast/templates/add">
                <Plus className="mr-2 h-4 w-4" />
                New Template
              </Link>
            </Button>
          </div>
        )}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search and View Controls */}
            <div className="flex items-center gap-4">
              {showSearch && (
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search templates..."
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2">
                {/* Sort */}
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="updated">Updated</SelectItem>
                    <SelectItem value="created">Created</SelectItem>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="category">Category</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  <SortAsc className={cn(
                    'h-4 w-4',
                    sortOrder === 'desc' && 'rotate-180'
                  )} />
                </Button>

                {/* View Mode */}
                <div className="flex border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('table')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                <Button variant="outline" size="sm" onClick={handleRefresh}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Filters:</span>
                </div>

                <Select value={filters.status || 'all'} onValueChange={(value) => setStatus(value === 'all' ? undefined : value as any)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>

                <TemplateCategoryFilter
                  selectedCategory={filters.category}
                  onCategoryChange={setCategory}
                  variant="select"
                  className="w-48"
                />

                {hasFilters && (
                  <Button variant="ghost" size="sm" onClick={handleClearFilters}>
                    <X className="mr-2 h-4 w-4" />
                    Clear Filters
                  </Button>
                )}
              </div>
            )}

            {/* Results Count */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>
                {loading ? 'Loading...' : `${sortedTemplates.length} templates found`}
              </span>
              
              {hasFilters && (
                <div className="flex items-center gap-2">
                  <span>Filters applied:</span>
                  {filters.category && (
                    <Badge variant="secondary" className="text-xs">
                      {filters.category}
                    </Badge>
                  )}
                  {filters.status && (
                    <Badge variant="secondary" className="text-xs">
                      {filters.status}
                    </Badge>
                  )}
                  {searchQuery && (
                    <Badge variant="secondary" className="text-xs">
                      "{searchQuery}"
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {loading ? (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center space-y-2">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto" />
              <p className="text-muted-foreground">Loading templates...</p>
            </div>
          </CardContent>
        </Card>
      ) : sortedTemplates.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center space-y-4">
              <div className="mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                <Grid3X3 className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">No templates found</h3>
                <p className="text-muted-foreground">
                  {hasFilters ? 'Try adjusting your filters or search terms.' : 'Get started by creating your first template.'}
                </p>
              </div>
              <div className="flex gap-2 justify-center">
                {hasFilters && (
                  <Button variant="outline" onClick={handleClearFilters}>
                    Clear Filters
                  </Button>
                )}
                {showActions && (
                  <Button asChild>
                    <Link href="/email-blast/templates/add">
                      <Plus className="mr-2 h-4 w-4" />
                      Create Template
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-fr">
          {sortedTemplates.map((template) => (
            <TemplateCard
              key={template.id}
              template={template}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onDuplicate={handleDuplicate}
              onTest={handleTest}
              onPreview={handlePreview}
              className="h-full"
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-muted-foreground">Table view temporarily disabled</p>
              <p className="text-sm text-muted-foreground">Please use grid view for now</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialogs */}
      {deleteDialog && (
        <DeleteTemplateDialog
          template={deleteDialog}
          open={!!deleteDialog}
          onOpenChange={(open) => !open && setDeleteDialog(null)}
          onSuccess={handleDialogSuccess}
        />
      )}

      {duplicateDialog && (
        <DuplicateTemplateDialog
          template={duplicateDialog}
          open={!!duplicateDialog}
          onOpenChange={(open) => !open && setDuplicateDialog(null)}
          onSuccess={handleDialogSuccess}
        />
      )}

      {testDialog && (
        <TestTemplateDialog
          template={testDialog}
          open={!!testDialog}
          onOpenChange={(open) => !open && setTestDialog(null)}
        />
      )}

      {importDialog && (
        <ImportTemplateDialog
          open={importDialog}
          onOpenChange={setImportDialog}
          onSuccess={handleDialogSuccess}
        />
      )}

      {previewDialog && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed inset-4 z-50 overflow-auto">
            <Card className="mx-auto max-w-4xl">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Template Preview</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setPreviewDialog(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <TemplatePreview
                  template={previewDialog}
                  showHeader={false}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}