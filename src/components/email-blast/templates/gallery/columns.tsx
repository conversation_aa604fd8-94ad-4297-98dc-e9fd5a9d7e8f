"use client";

import { ColumnDef } from "@tanstack/react-table";
import { EmailTemplate, TemplateStatus, TemplateCategory } from "@/types/email-blast/email-template.types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  MoreHorizontal, 
  ArrowUpDown, 
  Eye, 
  Edit, 
  Copy, 
  Send,
  Trash2,
  Crown,
  Calendar,
  User,
  Hash
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDistanceToNow, format } from "date-fns";
import { TemplateThumbnail } from "../ui/template-thumbnail";
import { CategoryChip } from "../ui/template-category-filter";
import { VariableList } from "../ui/variable-chip";
import Link from "next/link";

interface TemplateActionsProps {
  template: EmailTemplate;
  onEdit?: (template: EmailTemplate) => void;
  onDelete?: (template: EmailTemplate) => void;
  onDuplicate?: (template: EmailTemplate) => void;
  onTest?: (template: EmailTemplate) => void;
  onPreview?: (template: EmailTemplate) => void;
}

function TemplateActions({
  template,
  onEdit,
  onDelete,
  onDuplicate,
  onTest,
  onPreview
}: TemplateActionsProps) {
  const canEdit = !template.isSystem;
  const canDelete = !template.isSystem;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onPreview?.(template)}>
          <Eye className="mr-2 h-4 w-4" />
          Preview
        </DropdownMenuItem>
        
        <DropdownMenuItem asChild>
          <Link href={`/email-blast/templates/${template.id}`}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Link>
        </DropdownMenuItem>
        
        {canEdit && (
          <DropdownMenuItem asChild>
            <Link href={`/email-blast/templates/${template.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </DropdownMenuItem>
        )}
        
        <DropdownMenuItem onClick={() => onDuplicate?.(template)}>
          <Copy className="mr-2 h-4 w-4" />
          Duplicate
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => onTest?.(template)}>
          <Send className="mr-2 h-4 w-4" />
          Send Test
        </DropdownMenuItem>
        
        {canDelete && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => onDelete?.(template)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function StatusBadge({ status }: { status: TemplateStatus }) {
  const styles = {
    draft: 'bg-gray-100 text-gray-800 border-gray-200',
    active: 'bg-green-100 text-green-800 border-green-200',
    archived: 'bg-red-100 text-red-800 border-red-200'
  };

  return (
    <Badge variant="outline" className={styles[status]}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
}

export function getTemplateColumns(actions: {
  onEdit?: (template: EmailTemplate) => void;
  onDelete?: (template: EmailTemplate) => void;
  onDuplicate?: (template: EmailTemplate) => void;
  onTest?: (template: EmailTemplate) => void;
  onPreview?: (template: EmailTemplate) => void;
}): ColumnDef<EmailTemplate>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    
    {
      id: "thumbnail",
      header: "Preview",
      cell: ({ row }) => (
        <div className="w-16">
          <TemplateThumbnail
            template={row.original}
            size="sm"
            showActions={false}
            showOverlay={false}
            className="h-12 w-16"
          />
        </div>
      ),
      enableSorting: false,
    },
    
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Template Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const template = row.original;
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Link 
                href={`/email-blast/templates/${template.id}`}
                className="font-medium hover:underline"
              >
                {template.name}
              </Link>
              {template.isSystem && (
                <Crown className="h-4 w-4 text-amber-500" />
              )}
            </div>
            {template.description && (
              <p className="text-sm text-muted-foreground line-clamp-1">
                {template.description}
              </p>
            )}
          </div>
        );
      },
    },
    
    {
      accessorKey: "category",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Category
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <CategoryChip category={row.original.category} size="sm" />
      ),
    },
    
    {
      accessorKey: "status",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => <StatusBadge status={row.original.status} />,
    },
    
    {
      id: "variables",
      header: "Variables",
      cell: ({ row }) => {
        const variables = row.original.variables;
        if (variables.length === 0) {
          return <span className="text-muted-foreground text-sm">None</span>;
        }
        
        return (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              <Hash className="mr-1 h-3 w-3" />
              {variables.length}
            </Badge>
            {variables.length <= 2 ? (
              <VariableList variables={variables} size="sm" showType={false} />
            ) : (
              <div className="text-xs text-muted-foreground">
                {variables.slice(0, 2).map(v => v.name).join(', ')} +{variables.length - 2}
              </div>
            )}
          </div>
        );
      },
    },
    
    {
      id: "usage",
      header: "Usage",
      cell: ({ row }) => {
        const stats = row.original.usageStats;
        if (!stats) {
          return <span className="text-muted-foreground text-sm">No data</span>;
        }
        
        return (
          <div className="space-y-1 text-sm">
            <div>{stats.campaignsUsed} campaigns</div>
            <div className="text-muted-foreground">
              {stats.successRate.toFixed(1)}% success
            </div>
          </div>
        );
      },
    },
    
    {
      accessorKey: "createdBy",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created By
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.original.createdBy}</span>
        </div>
      ),
    },
    
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Last Updated
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const date = row.original.updatedAt;
        return (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div className="text-sm">
              <div>{formatDistanceToNow(date, { addSuffix: true })}</div>
              <div className="text-muted-foreground">
                {format(date, 'MMM d, yyyy')}
              </div>
            </div>
          </div>
        );
      },
    },
    
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <TemplateActions
          template={row.original}
          onEdit={actions.onEdit}
          onDelete={actions.onDelete}
          onDuplicate={actions.onDuplicate}
          onTest={actions.onTest}
          onPreview={actions.onPreview}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
  ];
}