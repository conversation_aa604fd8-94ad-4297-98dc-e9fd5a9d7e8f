"use client";

import { TemplateFormData } from "@/types/email-blast/email-template.types";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Mail, 
  Code, 
  Eye, 
  Palette,
  Type,
  FileText,
  Lightbulb
} from "lucide-react";
import { EmailEditor } from "../ui/email-editor/email-editor";
import { cn } from "@/lib/utils";

interface TemplateDesignSectionProps {
  data: TemplateFormData;
  onChange: (updates: Partial<TemplateFormData>) => void;
  className?: string;
}

export function TemplateDesignSection({ data, onChange, className }: TemplateDesignSectionProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {/* Email Subject */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Subject & Preview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Subject Line */}
          <div className="space-y-2">
            <Label htmlFor="subject">
              Subject Line <span className="text-destructive">*</span>
            </Label>
            <Input
              id="subject"
              value={data.subject}
              onChange={(e) => onChange({ subject: e.target.value })}
              placeholder="Enter email subject line..."
              required
            />
            <p className="text-xs text-muted-foreground">
              Keep it concise and compelling. Aim for 30-50 characters for best mobile display.
            </p>
          </div>

          {/* Preview Text */}
          <div className="space-y-2">
            <Label htmlFor="previewText">Preview Text</Label>
            <Input
              id="previewText"
              value={data.previewText || ''}
              onChange={(e) => onChange({ previewText: e.target.value })}
              placeholder="Text that appears in email previews..."
              maxLength={150}
            />
            <p className="text-xs text-muted-foreground">
              Optional text shown in email client previews. Usually 90-150 characters.
            </p>
          </div>

          {/* Subject Line Preview */}
          <div className="space-y-2">
            <Label>Subject Line Preview</Label>
            <div className="border rounded-md p-3 bg-muted/50">
              <div className="space-y-1">
                <div className="font-medium text-sm">
                  {data.subject || 'Subject line will appear here...'}
                </div>
                {data.previewText && (
                  <div className="text-xs text-muted-foreground">
                    {data.previewText}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Email Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Email Content
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="visual" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="visual" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Visual Editor
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                HTML/Text
              </TabsTrigger>
            </TabsList>

            <TabsContent value="visual" className="space-y-4">
              <div className="space-y-2">
                <Label>Email Content</Label>
                <EmailEditor
                  initialContent={data.bodyHtml || data.body}
                  variables={data.variables}
                  onChange={(content) => {
                    // Update both HTML and text versions
                    onChange({
                      bodyHtml: content,
                      body: content.replace(/<[^>]*>/g, '') // Strip HTML for text version
                    });
                  }}
                />
              </div>
            </TabsContent>

            <TabsContent value="code" className="space-y-6">
              {/* Text Version */}
              <div className="space-y-2">
                <Label htmlFor="body">
                  Text Version <span className="text-destructive">*</span>
                </Label>
                <Textarea
                  id="body"
                  value={data.body}
                  onChange={(e) => onChange({ body: e.target.value })}
                  placeholder="Enter the text version of your email..."
                  rows={12}
                  className="font-mono text-sm"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Plain text version for email clients that don't support HTML.
                </p>
              </div>

              {/* HTML Version */}
              <div className="space-y-2">
                <Label htmlFor="bodyHtml">HTML Version</Label>
                <Textarea
                  id="bodyHtml"
                  value={data.bodyHtml || ''}
                  onChange={(e) => onChange({ bodyHtml: e.target.value })}
                  placeholder="Enter HTML version (optional)..."
                  rows={12}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground">
                  Optional HTML version for rich formatting. If empty, text version will be used.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Content Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Content Guidelines
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Subject Line Tips */}
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Type className="h-4 w-4" />
                Subject Line Best Practices
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Keep it under 50 characters</li>
                <li>• Avoid spam trigger words</li>
                <li>• Use personalization variables</li>
                <li>• Create urgency or curiosity</li>
                <li>• A/B test different versions</li>
              </ul>
            </div>

            {/* Content Tips */}
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Email Content Tips
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Use clear, scannable formatting</li>
                <li>• Include a clear call-to-action</li>
                <li>• Keep paragraphs short</li>
                <li>• Use template variables for personalization</li>
                <li>• Test on multiple devices</li>
              </ul>
            </div>
          </div>

          {/* Variable Usage Reminder */}
          {data.variables.length > 0 && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-2">
                <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900">Template Variables Available</h5>
                  <p className="text-sm text-blue-700 mt-1">
                    You have {data.variables.length} variables defined. Use them in your content with the format: 
                    <code className="px-1 py-0.5 bg-blue-100 rounded text-xs ml-1">
                      {`{{variable_name}}`}
                    </code>
                  </p>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {data.variables.slice(0, 5).map((variable) => (
                      <code 
                        key={variable.id}
                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                      >
                        {`{{${variable.key}}}`}
                      </code>
                    ))}
                    {data.variables.length > 5 && (
                      <span className="text-sm text-blue-600">
                        +{data.variables.length - 5} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Statistics */}
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">{data.subject.length}</div>
              <div className="text-muted-foreground">Subject chars</div>
            </div>
            
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">{data.previewText?.length || 0}</div>
              <div className="text-muted-foreground">Preview chars</div>
            </div>
            
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">{data.body.split(/\s+/).length}</div>
              <div className="text-muted-foreground">Word count</div>
            </div>
            
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="font-medium">{data.body.length}</div>
              <div className="text-muted-foreground">Character count</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}