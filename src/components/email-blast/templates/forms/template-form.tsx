"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { EmailTemplate, TemplateFormData, TemplateCategory, TemplateStatus } from "@/types/email-blast/email-template.types";
import { useEmailTemplate } from "@/hooks/email-blast/use-email-templates";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Save, 
  Eye, 
  Send, 
  ArrowLeft,
  FileText,
  Palette,
  Settings,
  Hash,
  AlertCircle
} from "lucide-react";
import { TemplateInfoSection } from "./template-info-section";
import { TemplateDesignSection } from "./template-design-section";
import { TemplateVariablesSection } from "./template-variables-section";
import { TemplatePreview } from "../ui/template-preview";
import { TestTemplateDialog } from "../dialogs/test-template-dialog";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface TemplateFormProps {
  templateId?: string; // For editing existing template
  initialData?: Partial<TemplateFormData>;
  mode?: 'create' | 'edit';
  onSave?: (template: EmailTemplate) => void;
  onCancel?: () => void;
  className?: string;
}

export function TemplateForm({
  templateId,
  initialData,
  mode = templateId ? 'edit' : 'create',
  onSave,
  onCancel,
  className
}: TemplateFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { template, createTemplate, updateTemplate, loading } = useEmailTemplate(templateId || '');
  
  // State
  const [activeTab, setActiveTab] = useState('info');
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    description: '',
    category: 'custom' as TemplateCategory,
    status: 'draft' as TemplateStatus,
    subject: '',
    body: '',
    bodyHtml: '',
    previewText: '',
    variables: [],
    tags: [],
    metadata: {},
    ...initialData
  });
  const [isDirty, setIsDirty] = useState(false);
  const [testDialog, setTestDialog] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // Load existing template data for editing
  useEffect(() => {
    if (mode === 'edit' && template) {
      setFormData({
        name: template.name,
        description: template.description,
        category: template.category,
        status: template.status,
        subject: template.content.subject,
        body: template.content.body,
        bodyHtml: template.content.bodyHtml,
        previewText: template.content.previewText,
        variables: template.variables,
        tags: template.tags || [],
        metadata: template.metadata || {}
      });
    }
  }, [mode, template]);

  // Track changes
  useEffect(() => {
    if (mode === 'edit' && template) {
      const hasChanges = 
        formData.name !== template.name ||
        formData.description !== template.description ||
        formData.category !== template.category ||
        formData.status !== template.status ||
        formData.subject !== template.content.subject ||
        formData.body !== template.content.body ||
        formData.bodyHtml !== template.content.bodyHtml ||
        formData.previewText !== template.content.previewText ||
        JSON.stringify(formData.variables) !== JSON.stringify(template.variables) ||
        JSON.stringify(formData.tags) !== JSON.stringify(template.tags) ||
        JSON.stringify(formData.metadata) !== JSON.stringify(template.metadata);
      
      setIsDirty(hasChanges);
    } else if (mode === 'create') {
      const hasContent = 
        formData.name.trim() !== '' ||
        formData.subject.trim() !== '' ||
        formData.body.trim() !== '';
      
      setIsDirty(hasContent);
    }
  }, [formData, mode, template]);

  // Validation
  const validate = (): string[] => {
    const errors: string[] = [];
    
    if (!formData.name.trim()) {
      errors.push('Template name is required');
    }
    
    if (!formData.subject.trim()) {
      errors.push('Email subject is required');
    }
    
    if (!formData.body.trim()) {
      errors.push('Email body is required');
    }
    
    // Validate required variables
    const requiredVariables = formData.variables.filter(v => v.required);
    const bodyText = formData.body + (formData.bodyHtml || '');
    
    requiredVariables.forEach(variable => {
      const pattern = new RegExp(`\\{\\{${variable.key}\\}\\}`, 'g');
      if (!pattern.test(bodyText)) {
        errors.push(`Required variable {{${variable.key}}} is not used in the template`);
      }
    });
    
    return errors;
  };

  // Handlers
  const handleSave = async (saveAs: 'draft' | 'active' = 'draft') => {
    const errors = validate();
    
    if (errors.length > 0) {
      toast({
        title: 'Validation Error',
        description: errors[0],
        variant: 'destructive'
      });
      return;
    }

    try {
      const dataToSave = {
        ...formData,
        status: saveAs as TemplateStatus
      };

      let savedTemplate: EmailTemplate | null = null;

      if (mode === 'create') {
        savedTemplate = await createTemplate(dataToSave);
      } else {
        savedTemplate = await updateTemplate(dataToSave);
      }

      if (savedTemplate) {
        setIsDirty(false);
        onSave?.(savedTemplate);
        
        toast({
          title: 'Success',
          description: `Template ${mode === 'create' ? 'created' : 'updated'} successfully`,
          variant: 'default'
        });

        // Redirect to template detail page
        router.push(`/email-blast/templates/${savedTemplate.id}`);
      }
    } catch (error) {
      console.error('Error saving template:', error);
    }
  };

  const handleCancel = () => {
    if (isDirty) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to cancel?'
      );
      if (!confirmed) return;
    }
    
    onCancel?.();
    router.back();
  };

  const handleTestTemplate = () => {
    if (!formData.name || !formData.subject || !formData.body) {
      toast({
        title: 'Cannot Send Test',
        description: 'Please fill in template name, subject, and body before testing',
        variant: 'destructive'
      });
      return;
    }
    
    setTestDialog(true);
  };

  // Create template object for preview
  const previewTemplate: EmailTemplate = {
    id: templateId || 'preview',
    name: formData.name || 'Untitled Template',
    description: formData.description,
    category: formData.category,
    status: formData.status,
    content: {
      subject: formData.subject,
      body: formData.body,
      bodyHtml: formData.bodyHtml,
      previewText: formData.previewText
    },
    variables: formData.variables,
    tags: formData.tags,
    isSystem: false,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'Current User',
    updatedBy: 'Current User',
    metadata: formData.metadata
  };

  const validationErrors = validate();
  const canSave = validationErrors.length === 0;

  if (loading && mode === 'edit') {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center space-y-2">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto" />
            <p className="text-muted-foreground">Loading template...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {mode === 'create' ? 'Create Template' : 'Edit Template'}
            </h1>
            <p className="text-muted-foreground">
              {mode === 'create' 
                ? 'Create a new email template for your campaigns' 
                : `Editing: ${template?.name || 'Template'}`
              }
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Eye className="mr-2 h-4 w-4" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>

          {!isPreviewMode && (
            <>
              <Button
                variant="outline"
                onClick={handleTestTemplate}
                disabled={!canSave}
              >
                <Send className="mr-2 h-4 w-4" />
                Test
              </Button>

              <Button
                variant="outline"
                onClick={() => handleSave('draft')}
                disabled={loading || !canSave}
              >
                <Save className="mr-2 h-4 w-4" />
                Save as Draft
              </Button>

              <Button
                onClick={() => handleSave('active')}
                disabled={loading || !canSave}
              >
                <Save className="mr-2 h-4 w-4" />
                Save & Activate
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Card className="border-destructive">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-destructive flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-destructive">Please fix the following errors:</h4>
                <ul className="mt-2 text-sm text-destructive space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content */}
      {isPreviewMode ? (
        <Card>
          <CardHeader>
            <CardTitle>Template Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <TemplatePreview template={previewTemplate} showActions={false} />
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Information
            </TabsTrigger>
            <TabsTrigger value="design" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Design
            </TabsTrigger>
            <TabsTrigger value="variables" className="flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Variables
            </TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-6">
            <TemplateInfoSection
              data={formData}
              onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
            />
          </TabsContent>

          <TabsContent value="design" className="space-y-6">
            <TemplateDesignSection
              data={formData}
              onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
            />
          </TabsContent>

          <TabsContent value="variables" className="space-y-6">
            <TemplateVariablesSection
              data={formData}
              onChange={(updates) => setFormData(prev => ({ ...prev, ...updates }))}
            />
          </TabsContent>
        </Tabs>
      )}

      {/* Test Dialog */}
      {testDialog && (
        <TestTemplateDialog
          template={previewTemplate}
          open={testDialog}
          onOpenChange={setTestDialog}
        />
      )}

      {/* Unsaved Changes Warning */}
      {isDirty && (
        <div className="fixed bottom-4 right-4 z-50">
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-amber-800">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">You have unsaved changes</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}