"use client";

import { TemplateFormData, TemplateVariable } from "@/types/email-blast/email-template.types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Hash, 
  Plus, 
  Trash2, 
  Edit, 
  Save,
  X,
  AlertCircle,
  Eye,
  Copy
} from "lucide-react";
import { useState } from "react";
import { VariableChip, VariableList } from "../ui/variable-chip";
import { cn } from "@/lib/utils";
import { nanoid } from "nanoid";

interface TemplateVariablesSectionProps {
  data: TemplateFormData;
  onChange: (updates: Partial<TemplateFormData>) => void;
  className?: string;
}

const variableTypes = [
  { value: 'text', label: 'Text', description: 'Any text content' },
  { value: 'number', label: 'Number', description: 'Numeric values only' },
  { value: 'date', label: 'Date', description: 'Date values' },
  { value: 'boolean', label: 'Boolean', description: 'True/false values' },
  { value: 'url', label: 'URL', description: 'Web addresses' },
  { value: 'email', label: 'Email', description: 'Email addresses' }
];

const commonVariables = [
  { name: 'First Name', key: 'first_name', type: 'text' as const, required: true },
  { name: 'Last Name', key: 'last_name', type: 'text' as const, required: false },
  { name: 'Email', key: 'email', type: 'email' as const, required: true },
  { name: 'Company', key: 'company', type: 'text' as const, required: false },
  { name: 'Current Date', key: 'current_date', type: 'date' as const, required: false },
  { name: 'Website URL', key: 'website_url', type: 'url' as const, required: false }
];

export function TemplateVariablesSection({ data, onChange, className }: TemplateVariablesSectionProps) {
  const [editingVariable, setEditingVariable] = useState<string | null>(null);
  const [newVariable, setNewVariable] = useState<Partial<TemplateVariable>>({
    name: '',
    key: '',
    type: 'text',
    required: false,
    description: '',
    defaultValue: '',
    placeholder: ''
  });
  const [showNewVariableForm, setShowNewVariableForm] = useState(false);

  // Generate key from name
  const generateKey = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  };

  // Add variable
  const handleAddVariable = () => {
    if (!newVariable.name || !newVariable.key) return;

    const variable: TemplateVariable = {
      id: nanoid(),
      name: newVariable.name,
      key: newVariable.key,
      type: newVariable.type || 'text',
      required: newVariable.required || false,
      description: newVariable.description,
      defaultValue: newVariable.defaultValue,
      placeholder: newVariable.placeholder
    };

    onChange({
      variables: [...data.variables, variable]
    });

    // Reset form
    setNewVariable({
      name: '',
      key: '',
      type: 'text',
      required: false,
      description: '',
      defaultValue: '',
      placeholder: ''
    });
    setShowNewVariableForm(false);
  };

  // Update variable
  const handleUpdateVariable = (variableId: string, updates: Partial<TemplateVariable>) => {
    onChange({
      variables: data.variables.map(v => 
        v.id === variableId ? { ...v, ...updates } : v
      )
    });
  };

  // Remove variable
  const handleRemoveVariable = (variableId: string) => {
    onChange({
      variables: data.variables.filter(v => v.id !== variableId)
    });
  };

  // Add common variable
  const handleAddCommonVariable = (commonVar: typeof commonVariables[0]) => {
    const variable: TemplateVariable = {
      id: nanoid(),
      name: commonVar.name,
      key: commonVar.key,
      type: commonVar.type,
      required: commonVar.required,
      description: `Common variable for ${commonVar.name.toLowerCase()}`
    };

    onChange({
      variables: [...data.variables, variable]
    });
  };

  // Check if variable is used in content
  const isVariableUsed = (variable: TemplateVariable) => {
    const content = `${data.subject} ${data.body} ${data.bodyHtml || ''}`;
    return content.includes(`{{${variable.key}}}`);
  };

  // Get unused variables
  const unusedVariables = data.variables.filter(v => !isVariableUsed(v));

  return (
    <div className={cn('space-y-6', className)}>
      {/* Variables Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Hash className="h-5 w-5" />
              Template Variables ({data.variables.length})
            </CardTitle>
            <Button
              onClick={() => setShowNewVariableForm(true)}
              size="sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Variable
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {data.variables.length === 0 ? (
            <div className="text-center py-8">
              <Hash className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold">No Variables Defined</h3>
              <p className="text-muted-foreground mb-4">
                Variables allow you to personalize emails with dynamic content.
              </p>
              <Button onClick={() => setShowNewVariableForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Variable
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <VariableList variables={data.variables} />
              
              {/* Usage Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{data.variables.length}</div>
                  <div className="text-sm text-muted-foreground">Total Variables</div>
                </div>
                
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">
                    {data.variables.filter(v => v.required).length}
                  </div>
                  <div className="text-sm text-muted-foreground">Required</div>
                </div>
                
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">
                    {data.variables.filter(v => isVariableUsed(v)).length}
                  </div>
                  <div className="text-sm text-muted-foreground">Used in Content</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Unused Variables Warning */}
      {unusedVariables.length > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-amber-900">Unused Variables</h4>
                <p className="text-sm text-amber-700 mt-1">
                  The following variables are defined but not used in your template content:
                </p>
                <div className="mt-2 flex flex-wrap gap-1">
                  {unusedVariables.map((variable) => (
                    <code 
                      key={variable.id}
                      className="px-2 py-1 bg-amber-100 text-amber-800 rounded text-xs"
                    >
                      {`{{${variable.key}}}`}
                    </code>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add New Variable Form */}
      {showNewVariableForm && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Add New Variable</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowNewVariableForm(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Variable Name</Label>
                <Input
                  value={newVariable.name || ''}
                  onChange={(e) => {
                    const name = e.target.value;
                    setNewVariable(prev => ({
                      ...prev,
                      name,
                      key: generateKey(name)
                    }));
                  }}
                  placeholder="e.g., First Name"
                />
              </div>

              <div className="space-y-2">
                <Label>Variable Key</Label>
                <Input
                  value={newVariable.key || ''}
                  onChange={(e) => setNewVariable(prev => ({ ...prev, key: e.target.value }))}
                  placeholder="e.g., first_name"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Type</Label>
                <Select
                  value={newVariable.type || 'text'}
                  onValueChange={(value) => setNewVariable(prev => ({ ...prev, type: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {variableTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-xs text-muted-foreground">{type.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Default Value</Label>
                <Input
                  value={newVariable.defaultValue || ''}
                  onChange={(e) => setNewVariable(prev => ({ ...prev, defaultValue: e.target.value }))}
                  placeholder="Optional default value"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Description</Label>
              <Textarea
                value={newVariable.description || ''}
                onChange={(e) => setNewVariable(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this variable represents..."
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label>Placeholder Text</Label>
              <Input
                value={newVariable.placeholder || ''}
                onChange={(e) => setNewVariable(prev => ({ ...prev, placeholder: e.target.value }))}
                placeholder="Placeholder text for forms"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={newVariable.required || false}
                onCheckedChange={(checked) => setNewVariable(prev => ({ ...prev, required: !!checked }))}
              />
              <Label className="text-sm">This variable is required</Label>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleAddVariable}
                disabled={!newVariable.name || !newVariable.key}
              >
                <Save className="mr-2 h-4 w-4" />
                Add Variable
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowNewVariableForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Variable List */}
      {data.variables.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Manage Variables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.variables.map((variable, index) => (
                <div key={variable.id}>
                  {editingVariable === variable.id ? (
                    <VariableEditForm
                      variable={variable}
                      onSave={(updates) => {
                        handleUpdateVariable(variable.id, updates);
                        setEditingVariable(null);
                      }}
                      onCancel={() => setEditingVariable(null)}
                    />
                  ) : (
                    <VariableDisplayCard
                      variable={variable}
                      isUsed={isVariableUsed(variable)}
                      onEdit={() => setEditingVariable(variable.id)}
                      onDelete={() => handleRemoveVariable(variable.id)}
                    />
                  )}
                  {index < data.variables.length - 1 && <Separator className="mt-4" />}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Common Variables */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Add Common Variables</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {commonVariables
              .filter(common => !data.variables.some(v => v.key === common.key))
              .map((common) => (
                <Button
                  key={common.key}
                  variant="outline"
                  size="sm"
                  onClick={() => handleAddCommonVariable(common)}
                  className="justify-start"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {common.name}
                </Button>
              ))}
          </div>
          {commonVariables.every(common => data.variables.some(v => v.key === common.key)) && (
            <p className="text-sm text-muted-foreground">
              All common variables have been added.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Helper component for displaying variable
function VariableDisplayCard({ 
  variable, 
  isUsed, 
  onEdit, 
  onDelete 
}: {
  variable: TemplateVariable;
  isUsed: boolean;
  onEdit: () => void;
  onDelete: () => void;
}) {
  return (
    <div className="flex items-start justify-between p-4 border rounded-lg">
      <div className="flex-1 space-y-2">
        <div className="flex items-center gap-2">
          <VariableChip variable={variable} />
          {!isUsed && (
            <Badge variant="outline" className="text-amber-600 border-amber-200">
              Unused
            </Badge>
          )}
        </div>
        
        <div className="text-sm space-y-1">
          <p><strong>Key:</strong> <code className="px-1 py-0.5 bg-muted rounded text-xs">{`{{${variable.key}}}`}</code></p>
          {variable.description && <p className="text-muted-foreground">{variable.description}</p>}
          {variable.defaultValue && <p><strong>Default:</strong> {variable.defaultValue}</p>}
          {variable.placeholder && <p><strong>Placeholder:</strong> {variable.placeholder}</p>}
        </div>
      </div>

      <div className="flex gap-2">
        <Button variant="ghost" size="sm" onClick={onEdit}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={onDelete}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

// Helper component for editing variable
function VariableEditForm({ 
  variable, 
  onSave, 
  onCancel 
}: {
  variable: TemplateVariable;
  onSave: (updates: Partial<TemplateVariable>) => void;
  onCancel: () => void;
}) {
  const [editData, setEditData] = useState(variable);

  return (
    <div className="p-4 border rounded-lg bg-muted/50 space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Name</Label>
          <Input
            value={editData.name}
            onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
          />
        </div>
        
        <div className="space-y-2">
          <Label>Key</Label>
          <Input
            value={editData.key}
            onChange={(e) => setEditData(prev => ({ ...prev, key: e.target.value }))}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Type</Label>
          <Select
            value={editData.type}
            onValueChange={(value) => setEditData(prev => ({ ...prev, type: value as any }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {variableTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Default Value</Label>
          <Input
            value={editData.defaultValue || ''}
            onChange={(e) => setEditData(prev => ({ ...prev, defaultValue: e.target.value }))}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Description</Label>
        <Textarea
          value={editData.description || ''}
          onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
          rows={2}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          checked={editData.required}
          onCheckedChange={(checked) => setEditData(prev => ({ ...prev, required: !!checked }))}
        />
        <Label className="text-sm">Required variable</Label>
      </div>

      <div className="flex gap-2">
        <Button onClick={() => onSave(editData)} size="sm">
          <Save className="mr-2 h-4 w-4" />
          Save
        </Button>
        <Button variant="outline" onClick={onCancel} size="sm">
          Cancel
        </Button>
      </div>
    </div>
  );
}