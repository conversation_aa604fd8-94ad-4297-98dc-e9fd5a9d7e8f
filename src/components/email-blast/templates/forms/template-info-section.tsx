"use client";

import { TemplateFormData, TemplateCategory, TemplateStatus } from "@/types/email-blast/email-template.types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Tag, 
  Plus, 
  X,
  Info
} from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface TemplateInfoSectionProps {
  data: TemplateFormData;
  onChange: (updates: Partial<TemplateFormData>) => void;
  className?: string;
}

const categoryOptions = [
  { value: 'newsletter', label: 'Newsletter', description: 'Regular updates and content' },
  { value: 'promotional', label: 'Promotional', description: 'Sales and marketing campaigns' },
  { value: 'announcement', label: 'Announcement', description: 'Important news and updates' },
  { value: 'welcome', label: 'Welcome', description: 'New user onboarding' },
  { value: 'followup', label: 'Follow-up', description: 'Automated follow-up messages' },
  { value: 'event', label: 'Event', description: 'Event invitations and updates' },
  { value: 'survey', label: 'Survey', description: 'Feedback and questionnaires' },
  { value: 'notification', label: 'Notification', description: 'System notifications' },
  { value: 'custom', label: 'Custom', description: 'Custom template type' }
];

const statusOptions = [
  { value: 'draft', label: 'Draft', description: 'Work in progress, not ready for use' },
  { value: 'active', label: 'Active', description: 'Ready for use in campaigns' },
  { value: 'archived', label: 'Archived', description: 'Archived, not available for new campaigns' }
];

export function TemplateInfoSection({ data, onChange, className }: TemplateInfoSectionProps) {
  const [newTag, setNewTag] = useState('');

  const handleAddTag = () => {
    if (newTag.trim() && !data.tags?.includes(newTag.trim())) {
      onChange({
        tags: [...(data.tags || []), newTag.trim()]
      });
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    onChange({
      tags: data.tags?.filter(tag => tag !== tagToRemove) || []
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Name */}
          <div className="space-y-2">
            <Label htmlFor="name">
              Template Name <span className="text-destructive">*</span>
            </Label>
            <Input
              id="name"
              value={data.name}
              onChange={(e) => onChange({ name: e.target.value })}
              placeholder="Enter template name"
              required
            />
            <p className="text-xs text-muted-foreground">
              Choose a descriptive name that helps identify this template
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={data.description || ''}
              onChange={(e) => onChange({ description: e.target.value })}
              placeholder="Describe what this template is used for..."
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              Optional description to help others understand this template's purpose
            </p>
          </div>

          {/* Category and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>
                Category <span className="text-destructive">*</span>
              </Label>
              <Select 
                value={data.category} 
                onValueChange={(value) => onChange({ category: value as TemplateCategory })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Category helps organize and filter templates
              </p>
            </div>

            <div className="space-y-2">
              <Label>
                Status <span className="text-destructive">*</span>
              </Label>
              <Select 
                value={data.status} 
                onValueChange={(value) => onChange({ status: value as TemplateStatus })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Controls whether template is available for use
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Tags
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Template Tags</Label>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a tag..."
                className="flex-1"
              />
              <Button 
                type="button" 
                variant="outline" 
                size="sm"
                onClick={handleAddTag}
                disabled={!newTag.trim() || (data.tags?.includes(newTag.trim()) ?? false)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Tags help categorize and search for templates. Press Enter to add.
            </p>
          </div>

          {/* Current Tags */}
          {data.tags && data.tags.length > 0 && (
            <div className="space-y-2">
              <Label>Current Tags</Label>
              <div className="flex flex-wrap gap-2">
                {data.tags.map((tag, index) => (
                  <Badge 
                    key={index} 
                    variant="secondary" 
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Additional Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Template Guidelines</h4>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• Use descriptive names that clearly identify the template purpose</li>
              <li>• Choose appropriate categories to help with organization</li>
              <li>• Add relevant tags for better searchability</li>
              <li>• Keep descriptions concise but informative</li>
              <li>• Set status to "Draft" while working, "Active" when ready to use</li>
            </ul>
          </div>

          {/* Template Type Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">Current Category</Label>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {categoryOptions.find(c => c.value === data.category)?.label || data.category}
                </Badge>
              </div>
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">Current Status</Label>
              <div className="flex items-center gap-2">
                <Badge 
                  variant="outline"
                  className={cn(
                    data.status === 'active' && 'bg-green-100 text-green-800 border-green-200',
                    data.status === 'draft' && 'bg-gray-100 text-gray-800 border-gray-200',
                    data.status === 'archived' && 'bg-red-100 text-red-800 border-red-200'
                  )}
                >
                  {statusOptions.find(s => s.value === data.status)?.label || data.status}
                </Badge>
              </div>
            </div>
          </div>

          {/* Validation Info */}
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Validation Status</Label>
            <div className="space-y-1 text-xs">
              <div className={cn(
                'flex items-center gap-2',
                data.name.trim() ? 'text-green-600' : 'text-red-600'
              )}>
                <div className={cn(
                  'w-2 h-2 rounded-full',
                  data.name.trim() ? 'bg-green-600' : 'bg-red-600'
                )} />
                Template name {data.name.trim() ? 'provided' : 'required'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}