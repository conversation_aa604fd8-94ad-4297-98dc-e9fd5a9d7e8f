"use client";

import { TemplateVariable } from "@/types/email-blast/email-template.types";
import { Badge } from "@/components/ui/badge";
import { 
  Type, 
  Hash, 
  Calendar, 
  ToggleLeft, 
  Link, 
  Mail, 
  AlertCircle 
} from "lucide-react";
import { cn } from "@/lib/utils";

interface VariableChipProps {
  variable: TemplateVariable;
  size?: 'sm' | 'md' | 'lg';
  showType?: boolean;
  showRequired?: boolean;
  variant?: 'default' | 'secondary' | 'outline' | 'destructive';
  className?: string;
}

const typeIcons = {
  text: Type,
  number: Hash,
  date: Calendar,
  boolean: ToggleLeft,
  url: Link,
  email: Mail
};

const typeColors = {
  text: 'bg-blue-100 text-blue-800 border-blue-200',
  number: 'bg-green-100 text-green-800 border-green-200',
  date: 'bg-purple-100 text-purple-800 border-purple-200',
  boolean: 'bg-orange-100 text-orange-800 border-orange-200',
  url: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  email: 'bg-pink-100 text-pink-800 border-pink-200'
};

const sizeClasses = {
  sm: 'text-xs px-2 py-1',
  md: 'text-sm px-2.5 py-1.5',
  lg: 'text-base px-3 py-2'
};

const iconSizes = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-5 w-5'
};

export function VariableChip({
  variable,
  size = 'md',
  showType = true,
  showRequired = true,
  variant = 'default',
  className
}: VariableChipProps) {
  const IconComponent = typeIcons[variable.type];
  
  return (
    <div className={cn('inline-flex items-center gap-1', className)}>
      <Badge 
        variant={variant}
        className={cn(
          'inline-flex items-center gap-1.5 font-medium border',
          sizeClasses[size],
          showType && typeColors[variable.type]
        )}
      >
        {showType && <IconComponent className={iconSizes[size]} />}
        <span>{variable.name}</span>
        {showRequired && variable.required && (
          <AlertCircle className={cn(iconSizes[size], 'text-red-500')} />
        )}
      </Badge>
      
      {variable.description && size !== 'sm' && (
        <span className="text-xs text-muted-foreground ml-1">
          ({variable.description})
        </span>
      )}
    </div>
  );
}

export function VariableList({ 
  variables, 
  size = 'md',
  showType = true,
  showRequired = true,
  className 
}: {
  variables: TemplateVariable[];
  size?: 'sm' | 'md' | 'lg';
  showType?: boolean;
  showRequired?: boolean;
  className?: string;
}) {
  if (variables.length === 0) {
    return (
      <div className={cn('text-sm text-muted-foreground', className)}>
        No variables defined
      </div>
    );
  }

  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {variables.map((variable) => (
        <VariableChip
          key={variable.id}
          variable={variable}
          size={size}
          showType={showType}
          showRequired={showRequired}
        />
      ))}
    </div>
  );
}

export function VariableTypeIndicator({ 
  type, 
  size = 'md' 
}: { 
  type: TemplateVariable['type'];
  size?: 'sm' | 'md' | 'lg';
}) {
  const IconComponent = typeIcons[type];
  
  return (
    <div className={cn(
      'inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium',
      typeColors[type]
    )}>
      <IconComponent className={iconSizes[size]} />
      <span className="capitalize">{type}</span>
    </div>
  );
}