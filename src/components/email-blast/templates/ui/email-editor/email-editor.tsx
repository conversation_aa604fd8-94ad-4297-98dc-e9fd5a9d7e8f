'use client';

import React, { useCallback, useEffect, useState, useRef } from 'react';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Color from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { EditorToolbar } from './toolbar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Eye, Code } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TemplateVariable } from '@/types/email-blast/email-template.types';
import { useVariableInsertion } from '@/hooks/email-blast/use-email-editor';

export interface EmailEditorProps {
  initialContent?: string;
  placeholder?: string;
  variables?: TemplateVariable[];
  onChange?: (html: string) => void;
  onUpdate?: (state: { html: string; text: string; isEmpty: boolean }) => void;
  editable?: boolean;
  height?: string;
  showToolbar?: boolean;
  showFooter?: boolean;
  className?: string;
}

export function EmailEditor({
  initialContent = '',
  placeholder = 'Start typing your email content...',
  variables = [],
  onChange,
  onUpdate,
  editable = true,
  height = '400px',
  showToolbar = true,
  showFooter = true,
  className,
}: EmailEditorProps) {
  const [viewMode, setViewMode] = useState<'visual' | 'html'>('visual');
  const [htmlContent, setHtmlContent] = useState(initialContent);
  const [showVariableMenu, setShowVariableMenu] = useState(false);
  const htmlTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Variable insertion hook
  const { insertVariable } = useVariableInsertion();

  // Initialize TipTap editor
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Color,
      TextStyle,
      FontFamily,
      Placeholder.configure({
        placeholder,
      }),
      CharacterCount.configure({
        limit: null,
      }),
    ],
    content: initialContent,
    editable,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const text = editor.getText();
      const isEmpty = editor.isEmpty;
      
      setHtmlContent(html);
      onChange?.(html);
      onUpdate?.({ html, text, isEmpty });
    },
  });

  // Update editor content when initialContent changes
  useEffect(() => {
    if (editor && initialContent !== editor.getHTML()) {
      editor.commands.setContent(initialContent);
      setHtmlContent(initialContent);
    }
  }, [editor, initialContent]);

  // Handle HTML mode changes
  const handleHtmlChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newHtml = e.target.value;
    setHtmlContent(newHtml);
    
    if (editor && viewMode === 'html') {
      editor.commands.setContent(newHtml);
    }
    
    onChange?.(newHtml);
  }, [editor, onChange, viewMode]);

  // Handle view mode change
  const handleViewModeChange = useCallback((mode: string) => {
    if (mode === 'html' && editor) {
      setHtmlContent(editor.getHTML());
    } else if (mode === 'visual' && editor) {
      editor.commands.setContent(htmlContent);
    }
    setViewMode(mode as 'visual' | 'html');
  }, [editor, htmlContent]);

  // Handle variable insertion
  const handleVariableInsert = useCallback((variable: TemplateVariable) => {
    if (viewMode === 'visual' && editor) {
      editor.chain().focus().insertContent(`{{${variable.key}}}`).run();
    } else if (viewMode === 'html' && htmlTextareaRef.current) {
      insertVariable(
        htmlTextareaRef.current,
        variable.key,
        htmlContent,
        setHtmlContent
      );
    }
    setShowVariableMenu(false);
  }, [viewMode, editor, htmlContent, insertVariable]);

  if (!editor) {
    return (
      <Card className={cn('animate-pulse', className)}>
        <div className="h-[400px] bg-muted" />
      </Card>
    );
  }

  const characterCount = editor?.storage.characterCount?.characters() || 0;
  const wordCount = editor?.storage.characterCount?.words() || 0;

  return (
    <Card className={cn('flex flex-col', className)}>
      <Tabs value={viewMode} onValueChange={handleViewModeChange}>
        <div className="border-b">
          <div className="flex items-center justify-between px-4 py-2">
            <TabsList className="h-8">
              <TabsTrigger value="visual" className="text-xs">
                <Eye className="w-3 h-3 mr-1" />
                Visual
              </TabsTrigger>
              <TabsTrigger value="html" className="text-xs">
                <Code className="w-3 h-3 mr-1" />
                HTML
              </TabsTrigger>
            </TabsList>
            {variables.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowVariableMenu(!showVariableMenu)}
              >
                Insert Variable
              </Button>
            )}
          </div>
          {showToolbar && viewMode === 'visual' && (
            <EditorToolbar editor={editor} />
          )}
        </div>

        <TabsContent value="visual" className="m-0 flex-1">
          <div className="relative">
            <EditorContent
              editor={editor}
              className={cn(
                'prose prose-sm max-w-none p-4 focus:outline-none',
                'min-h-[300px] overflow-y-auto',
                !editable && 'cursor-not-allowed opacity-60'
              )}
              style={{ height }}
            />
            
            {/* Variable insertion menu */}
            {showVariableMenu && variables.length > 0 && (
              <div className="absolute top-0 right-0 m-4 bg-background border rounded-lg shadow-lg p-2 max-h-60 overflow-y-auto z-10">
                <div className="text-sm font-medium mb-2">Variables</div>
                {variables.map((variable) => (
                  <button
                    key={variable.id}
                    className="block w-full text-left px-2 py-1 text-sm hover:bg-muted rounded"
                    onClick={() => handleVariableInsert(variable)}
                  >
                    <div className="font-medium">{variable.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {`{{${variable.key}}}`}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="html" className="m-0 flex-1">
          <Textarea
            ref={htmlTextareaRef}
            value={htmlContent}
            onChange={handleHtmlChange}
            className="h-full min-h-[300px] font-mono text-sm resize-none border-0 focus:ring-0"
            style={{ height }}
            placeholder="Enter HTML content..."
            disabled={!editable}
          />
        </TabsContent>
      </Tabs>

      {showFooter && (
        <div className="border-t px-4 py-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-4">
              <span>{characterCount} characters</span>
              <span>{wordCount} words</span>
            </div>
            {variables.length > 0 && (
              <div className="flex items-center space-x-2">
                <span>Variables:</span>
                {variables.slice(0, 3).map((v) => (
                  <Badge key={v.id} variant="secondary" className="text-xs">
                    {v.name}
                  </Badge>
                ))}
                {variables.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{variables.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </Card>
  );
}