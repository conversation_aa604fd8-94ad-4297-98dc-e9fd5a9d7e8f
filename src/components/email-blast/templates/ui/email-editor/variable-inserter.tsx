'use client';

import React, { useState, useEffect } from 'react';
import { Editor } from '@tiptap/core';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Variable, Search, Info } from 'lucide-react';
import { TemplateVariable } from '@/types/email-blast/email-template.types';
import { useVariableInsertion } from '@/hooks/email-blast/use-email-editor';
import { cn } from '@/lib/utils';

interface VariableInserterProps {
  editor: Editor;
  variables: TemplateVariable[];
  className?: string;
  trigger?: React.ReactNode;
}

export function VariableInserter({
  editor,
  variables,
  className,
  trigger,
}: VariableInserterProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { extractVariables } = useVariableInsertion();

  // Filter variables based on search
  const filteredVariables = variables.filter(variable =>
    variable.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    variable.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
    variable.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get used variables from editor content
  const editorContent = editor.getHTML();
  const usedVariables = extractVariables(editorContent);
  const missingRequired = variables.filter(v => v.required && !usedVariables.includes(v.key));

  const handleSelect = (variable: TemplateVariable) => {
    // Insert variable into editor
    editor.chain().focus().insertContent(`{{${variable.key}}}`).run();
    setOpen(false);
    setSearchQuery('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Variable className="h-4 w-4 mr-2" />
            Variables
            {missingRequired.length > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 px-1">
                {missingRequired.length}
              </Badge>
            )}
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className={cn('p-0 w-80', className)} align="start">
        <Command>
          <CommandInput
            placeholder="Search variables..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>No variables found.</CommandEmpty>
            
            {missingRequired.length > 0 && (
              <CommandGroup heading="Missing Required Variables">
                {missingRequired.map((variable) => (
                  <VariableItem
                    key={variable.id}
                    variable={variable}
                    onSelect={handleSelect}
                    isUsed={false}
                    isMissing={true}
                  />
                ))}
              </CommandGroup>
            )}

            <CommandGroup heading="All Variables">
              {filteredVariables.map((variable) => (
                <VariableItem
                  key={variable.id}
                  variable={variable}
                  onSelect={handleSelect}
                  isUsed={usedVariables.includes(variable.key)}
                  isMissing={variable.required && !usedVariables.includes(variable.key)}
                />
              ))}
            </CommandGroup>
          </CommandList>
        </Command>

        {variables.length > 0 && (
          <div className="border-t p-2">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>
                {usedVariables.length} of {variables.length} variables used
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs"
                onClick={() => {
                  editor.commands.focus();
                  setOpen(false);
                }}
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}

interface VariableItemProps {
  variable: TemplateVariable;
  onSelect: (variable: TemplateVariable) => void;
  isUsed: boolean;
  isMissing: boolean;
  isSelected?: boolean;
}

function VariableItem({
  variable,
  onSelect,
  isUsed,
  isMissing,
  isSelected,
}: VariableItemProps) {
  return (
    <CommandItem
      value={variable.key}
      onSelect={() => onSelect(variable)}
      className={cn(
        'flex items-start gap-2 p-2',
        isSelected && 'bg-accent'
      )}
    >
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <span className="font-medium text-sm">{variable.name}</span>
          <code className="text-xs bg-muted px-1 py-0.5 rounded">
            {`{{${variable.key}}}`}
          </code>
        </div>
        {variable.description && (
          <p className="text-xs text-muted-foreground mt-0.5">
            {variable.description}
          </p>
        )}
        <div className="flex items-center gap-2 mt-1">
          {variable.required && (
            <Badge variant={isMissing ? 'destructive' : 'outline'} className="text-xs h-5">
              Required
            </Badge>
          )}
          {isUsed && (
            <Badge variant="secondary" className="text-xs h-5">
              Used
            </Badge>
          )}
          <Badge variant="outline" className="text-xs h-5">
            {variable.type}
          </Badge>
        </div>
      </div>
    </CommandItem>
  );
}

// Standalone variable list component for forms
export function VariableList({
  variables,
  onInsert,
  className,
}: {
  variables: TemplateVariable[];
  onInsert?: (variable: TemplateVariable) => void;
  className?: string;
}) {
  const [search, setSearch] = useState('');
  
  const filteredVars = variables.filter(
    (v) =>
      v.name.toLowerCase().includes(search.toLowerCase()) ||
      v.key.toLowerCase().includes(search.toLowerCase()) ||
      v.description?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className={cn('space-y-2', className)}>
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search variables..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-8"
        />
      </div>
      
      <ScrollArea className="h-60 rounded-md border">
        <div className="p-2 space-y-1">
          {filteredVars.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No variables found
            </p>
          ) : (
            filteredVars.map((variable) => (
              <div
                key={variable.id}
                className={cn(
                  'p-2 rounded-lg border bg-card text-card-foreground',
                  onInsert && 'cursor-pointer hover:bg-accent'
                )}
                onClick={() => onInsert?.(variable)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">
                        {variable.name}
                      </span>
                      <code className="text-xs bg-muted px-1 py-0.5 rounded">
                        {`{{${variable.key}}}`}
                      </code>
                    </div>
                    {variable.description && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {variable.description}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2 mt-2">
                  {variable.required && (
                    <Badge variant="outline" className="text-xs h-5">
                      Required
                    </Badge>
                  )}
                  <Badge variant="secondary" className="text-xs h-5">
                    {variable.type}
                  </Badge>
                  {variable.defaultValue && (
                    <span className="text-xs text-muted-foreground">
                      Default: {variable.defaultValue}
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>
      
      {variables.length > 0 && (
        <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
          <Info className="h-4 w-4 text-muted-foreground" />
          <p className="text-xs text-muted-foreground">
            Variables will be replaced with actual values when sending emails
          </p>
        </div>
      )}
    </div>
  );
}