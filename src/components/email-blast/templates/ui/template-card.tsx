"use client";

import { EmailTemplate, Template<PERSON>tatus, TemplateCategory } from "@/types/email-blast/email-template.types";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Copy, 
  Trash2, 
  Send,
  Calendar,
  User,
  Tag,
  AlertCircle,
  Crown
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { VariableList } from "./variable-chip";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { TemplateThumbnail } from "./template-thumbnail";

interface TemplateCardProps {
  template: EmailTemplate;
  onEdit?: (template: EmailTemplate) => void;
  onDelete?: (template: EmailTemplate) => void;
  onDuplicate?: (template: EmailTemplate) => void;
  onTest?: (template: EmailTemplate) => void;
  onPreview?: (template: EmailTemplate) => void;
  className?: string;
}

const statusStyles = {
  draft: 'bg-gray-100 text-gray-800 border-gray-200',
  active: 'bg-green-100 text-green-800 border-green-200',
  archived: 'bg-red-100 text-red-800 border-red-200'
};

const categoryStyles = {
  newsletter: 'bg-blue-100 text-blue-800',
  promotional: 'bg-purple-100 text-purple-800',
  announcement: 'bg-orange-100 text-orange-800',
  welcome: 'bg-green-100 text-green-800',
  followup: 'bg-indigo-100 text-indigo-800',
  event: 'bg-pink-100 text-pink-800',
  survey: 'bg-cyan-100 text-cyan-800',
  notification: 'bg-yellow-100 text-yellow-800',
  custom: 'bg-gray-100 text-gray-800'
};

export function TemplateCard({
  template,
  onEdit,
  onDelete,
  onDuplicate,
  onTest,
  onPreview,
  className
}: TemplateCardProps) {
  const canEdit = !template.isSystem;
  const canDelete = !template.isSystem;
  
  return (
    <Card className={cn('group hover:shadow-lg transition-all duration-200 flex flex-col h-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-lg truncate">
                {template.name}
              </h3>
              {template.isSystem && (
                <Crown className="h-4 w-4 text-amber-500 flex-shrink-0" />
              )}
            </div>
            
            <div className="flex items-center gap-2 flex-wrap">
              <Badge 
                variant="outline" 
                className={cn('text-xs', statusStyles[template.status])}
              >
                {template.status.charAt(0).toUpperCase() + template.status.slice(1)}
              </Badge>
              
              <Badge 
                variant="secondary" 
                className={cn('text-xs', categoryStyles[template.category])}
              >
                {template.category.charAt(0).toUpperCase() + template.category.slice(1)}
              </Badge>
              
              {template.variables.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  {template.variables.length} variables
                </Badge>
              )}
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onPreview?.(template)}>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </DropdownMenuItem>
              
              {canEdit && (
                <DropdownMenuItem onClick={() => onEdit?.(template)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
              
              <DropdownMenuItem onClick={() => onDuplicate?.(template)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onTest?.(template)}>
                <Send className="mr-2 h-4 w-4" />
                Send Test
              </DropdownMenuItem>
              
              {canDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete?.(template)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 flex-1 flex flex-col">
        {/* Thumbnail */}
        <div className="mb-3">
          <TemplateThumbnail template={template} className="w-full h-32" />
        </div>
        
        {/* Description */}
        {template.description && (
          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
            {template.description}
          </p>
        )}
        
        {/* Variables */}
        {template.variables.length > 0 && (
          <div className="mb-3">
            <h4 className="text-xs font-medium text-muted-foreground mb-1.5 uppercase tracking-wide">
              Variables
            </h4>
            <VariableList 
              variables={template.variables.slice(0, 3)} 
              size="sm" 
              showType={false}
              className="mb-1"
            />
            {template.variables.length > 3 && (
              <p className="text-xs text-muted-foreground">
                +{template.variables.length - 3} more variables
              </p>
            )}
          </div>
        )}
        
        {/* Tags */}
        {template.tags && template.tags.length > 0 && (
          <div className="mb-3">
            <div className="flex items-center gap-1 flex-wrap">
              <Tag className="h-3 w-3 text-muted-foreground" />
              {template.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs px-1.5 py-0.5">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <span className="text-xs text-muted-foreground">+{template.tags.length - 3}</span>
              )}
            </div>
          </div>
        )}
        
        {/* Usage Stats */}
        {template.usageStats && (
          <div className="text-xs text-muted-foreground space-y-1 mt-auto">
            <div className="flex justify-between">
              <span>Used in campaigns:</span>
              <span className="font-medium">{template.usageStats.campaignsUsed}</span>
            </div>
            <div className="flex justify-between">
              <span>Success rate:</span>
              <span className="font-medium">{template.usageStats.successRate.toFixed(1)}%</span>
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-3 pb-4 flex-col gap-3">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center text-xs text-muted-foreground">
            <User className="mr-1 h-3 w-3" />
            <span className="truncate max-w-[100px]">{template.createdBy}</span>
          </div>
          
          <div className="flex items-center text-xs text-muted-foreground">
            <Calendar className="mr-1 h-3 w-3" />
            <span>{formatDistanceToNow(template.updatedAt, { addSuffix: true })}</span>
          </div>
        </div>
        
        <div className="flex gap-2 w-full">
          <Button size="sm" variant="outline" className="flex-1" asChild>
            <Link href={`/email-blast/templates/${template.id}`}>
              <Eye className="mr-1.5 h-3.5 w-3.5" />
              View
            </Link>
          </Button>
          
          {canEdit && (
            <Button size="sm" className="flex-1" asChild>
              <Link href={`/email-blast/templates/${template.id}/edit`}>
                <Edit className="mr-1.5 h-3.5 w-3.5" />
                Edit
              </Link>
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}