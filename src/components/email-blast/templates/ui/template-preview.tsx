"use client";

import { EmailTemplate, TemplateVariable } from "@/types/email-blast/email-template.types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Eye, 
  Mail, 
  Code, 
  Variable, 
  Calendar,
  User,
  ExternalLink
} from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import { VariableList } from "./variable-chip";
import { CategoryChip } from "./template-category-filter";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface TemplatePreviewProps {
  template: EmailTemplate;
  variableValues?: Record<string, any>;
  className?: string;
  showHeader?: boolean;
  showActions?: boolean;
}

export function TemplatePreview({
  template,
  variableValues = {},
  className,
  showHeader = true,
  showActions = true
}: TemplatePreviewProps) {
  // Replace variables in content
  const processContent = (content: string, variables: Record<string, any>) => {
    let processedContent = content;
    
    template.variables.forEach((variable) => {
      const value = variables[variable.key] || variable.defaultValue || `{{${variable.key}}}`;
      const pattern = new RegExp(`\\{\\{${variable.key}\\}\\}`, 'g');
      processedContent = processedContent.replace(pattern, String(value));
    });
    
    return processedContent;
  };

  const processedSubject = processContent(template.content.subject, variableValues);
  const processedBody = processContent(template.content.body, variableValues);
  const processedBodyHtml = template.content.bodyHtml 
    ? processContent(template.content.bodyHtml, variableValues)
    : null;

  return (
    <div className={cn('space-y-6', className)}>
      {showHeader && (
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  {template.name}
                </CardTitle>
                
                <div className="flex items-center gap-2 flex-wrap">
                  <CategoryChip category={template.category} size="sm" />
                  
                  <Badge variant="outline" className="text-xs">
                    {template.status.charAt(0).toUpperCase() + template.status.slice(1)}
                  </Badge>
                  
                  {template.variables.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {template.variables.length} variables
                    </Badge>
                  )}
                </div>
                
                {template.description && (
                  <p className="text-sm text-muted-foreground">
                    {template.description}
                  </p>
                )}
              </div>
              
              {showActions && (
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/email-blast/templates/${template.id}`}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2 text-muted-foreground">
                <User className="h-4 w-4" />
                <span>Created by {template.createdBy}</span>
              </div>
              
              <div className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Updated {formatDistanceToNow(template.updatedAt, { addSuffix: true })}</span>
              </div>
            </div>
            
            {template.variables.length > 0 && (
              <div className="mt-4">
                <div className="flex items-center gap-2 mb-2">
                  <Variable className="h-4 w-4" />
                  <span className="text-sm font-medium">Template Variables</span>
                </div>
                <VariableList 
                  variables={template.variables} 
                  size="sm" 
                  showType={true}
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Email Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Email Preview
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Subject Line */}
          <div>
            <label className="text-sm font-medium text-muted-foreground">Subject Line</label>
            <div className="mt-1 p-3 bg-muted rounded-md">
              <p className="font-medium">{processedSubject}</p>
            </div>
          </div>
          
          {/* Preview Text */}
          {template.content.previewText && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Preview Text</label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                <p className="text-sm text-muted-foreground">
                  {processContent(template.content.previewText, variableValues)}
                </p>
              </div>
            </div>
          )}
          
          <Separator />
          
          {/* Email Body */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-muted-foreground">Email Content</label>
              {processedBodyHtml && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const tab = window.open('', '_blank');
                      if (tab) {
                        tab.document.write(processedBodyHtml);
                        tab.document.close();
                      }
                    }}
                  >
                    <Code className="mr-2 h-4 w-4" />
                    View HTML
                  </Button>
                </div>
              )}
            </div>
            
            <ScrollArea className="h-96 w-full rounded-md border">
              <div className="p-4">
                {processedBodyHtml ? (
                  <div 
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: processedBodyHtml }}
                  />
                ) : (
                  <div className="whitespace-pre-wrap font-mono text-sm">
                    {processedBody}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
          
          {/* Attachments */}
          {template.content.attachments && template.content.attachments.length > 0 && (
            <div>
              <label className="text-sm font-medium text-muted-foreground mb-2 block">
                Attachments ({template.content.attachments.length})
              </label>
              <div className="space-y-2">
                {template.content.attachments.map((attachment, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-2 border rounded-md"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
                        <Code className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{attachment.filename}</p>
                        <p className="text-xs text-muted-foreground">
                          {(attachment.size / 1024).toFixed(1)} KB • {attachment.mimeType}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Metadata */}
      {template.metadata && Object.keys(template.metadata).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Template Metadata</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(template.metadata).map(([key, value]) => (
                <div key={key} className="flex justify-between text-sm">
                  <span className="font-medium text-muted-foreground">{key}:</span>
                  <span className="text-right max-w-xs truncate">{String(value)}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export function QuickTemplatePreview({ 
  template, 
  className 
}: { 
  template: EmailTemplate;
  className?: string;
}) {
  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <h4 className="font-medium truncate">{template.name}</h4>
            <CategoryChip category={template.category} size="sm" />
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p className="font-medium">Subject: {template.content.subject}</p>
            <p className="mt-1 line-clamp-2">
              {template.content.body.substring(0, 100)}...
            </p>
          </div>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{template.variables.length} variables</span>
            <span>{formatDistanceToNow(template.updatedAt, { addSuffix: true })}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}