"use client";

import { EmailTemplate } from "@/types/email-blast/email-template.types";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Image as ImageIcon, 
  Mail, 
  RefreshCw, 
  Crown,
  Eye,
  Loader2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { useEmailTemplate } from "@/hooks/email-blast/use-email-templates";

interface TemplateThumbnailProps {
  template: EmailTemplate;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showActions?: boolean;
  showOverlay?: boolean;
}

const sizeClasses = {
  sm: 'h-20 w-32',
  md: 'h-32 w-48',
  lg: 'h-48 w-64'
};

export function TemplateThumbnail({
  template,
  className,
  size = 'md',
  showActions = false,
  showOverlay = true
}: TemplateThumbnailProps) {
  const [imageError, setImageError] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const { generateThumbnail } = useEmailTemplate(template.id);

  const handleGenerateThumbnail = async () => {
    setIsGenerating(true);
    try {
      await generateThumbnail();
      setImageError(false);
    } catch (error) {
      console.error('Failed to generate thumbnail:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card className={cn(
      'relative overflow-hidden group bg-gradient-to-br from-blue-50 to-indigo-50',
      sizeClasses[size],
      className
    )}>
      {template.thumbnail && !imageError ? (
        <div className="relative w-full h-full">
          <img
            src={template.thumbnail}
            alt={`${template.name} thumbnail`}
            className="w-full h-full object-cover object-top"
            onError={handleImageError}
          />
          
          {showOverlay && (
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="absolute inset-0 flex items-center justify-center">
                <Button size="sm" variant="secondary" className="gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </Button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="relative w-full h-full flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-200">
          <div className="text-center space-y-2">
            <div className="relative">
              <Mail className="h-8 w-8 text-muted-foreground mx-auto" />
              {template.isSystem && (
                <Crown className="h-4 w-4 text-amber-500 absolute -top-1 -right-1" />
              )}
            </div>
            
            <div className="space-y-1">
              <p className="text-xs font-medium text-muted-foreground line-clamp-2">
                {template.name}
              </p>
              
              <div className="flex items-center justify-center gap-1">
                <Badge variant="outline" className="text-xs px-1 py-0">
                  {template.category}
                </Badge>
              </div>
            </div>
            
            {showActions && !template.isSystem && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleGenerateThumbnail}
                disabled={isGenerating}
                className="text-xs gap-1"
              >
                {isGenerating ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <RefreshCw className="h-3 w-3" />
                )}
                Generate
              </Button>
            )}
          </div>
        </div>
      )}
      
      {/* Status Indicator */}
      <div className="absolute top-2 left-2 flex gap-1">
        {template.isSystem && (
          <Badge variant="secondary" className="text-xs">
            System
          </Badge>
        )}
        
        <Badge 
          variant={template.status === 'active' ? 'default' : 'secondary'}
          className="text-xs"
        >
          {template.status}
        </Badge>
      </div>
      
      {/* Variable Count */}
      {template.variables.length > 0 && (
        <div className="absolute top-2 right-2">
          <Badge variant="outline" className="text-xs bg-white/90">
            {template.variables.length} vars
          </Badge>
        </div>
      )}
    </Card>
  );
}

export function TemplateThumbnailGrid({ 
  templates, 
  onSelect,
  selectedId,
  className 
}: {
  templates: EmailTemplate[];
  onSelect?: (template: EmailTemplate) => void;
  selectedId?: string;
  className?: string;
}) {
  return (
    <div className={cn(
      'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4',
      className
    )}>
      {templates.map((template) => (
        <div
          key={template.id}
          className={cn(
            'cursor-pointer transition-all duration-200 hover:scale-105',
            selectedId === template.id && 'ring-2 ring-primary ring-offset-2'
          )}
          onClick={() => onSelect?.(template)}
        >
          <TemplateThumbnail
            template={template}
            size="sm"
            showActions={false}
            showOverlay={false}
          />
        </div>
      ))}
    </div>
  );
}

export function TemplateThumbnailCarousel({ 
  templates, 
  className 
}: {
  templates: EmailTemplate[];
  className?: string;
}) {
  return (
    <div className={cn('flex gap-4 overflow-x-auto pb-4', className)}>
      {templates.map((template) => (
        <div key={template.id} className="flex-shrink-0">
          <TemplateThumbnail
            template={template}
            size="md"
            showActions={true}
            className="cursor-pointer hover:shadow-lg transition-shadow"
          />
        </div>
      ))}
    </div>
  );
}