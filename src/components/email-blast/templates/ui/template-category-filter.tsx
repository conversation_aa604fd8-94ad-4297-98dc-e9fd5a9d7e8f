"use client";

import { TemplateCategory } from "@/types/email-blast/email-template.types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Mail, 
  Megaphone, 
  Bell, 
  UserPlus, 
  Reply, 
  Calendar, 
  ClipboardList, 
  AlertCircle,
  Settings,
  X
} from "lucide-react";
import { cn } from "@/lib/utils";

interface TemplateCategoryFilterProps {
  selectedCategory?: TemplateCategory;
  onCategoryChange: (category: TemplateCategory | undefined) => void;
  categoryCounts?: Record<string, number>;
  className?: string;
  variant?: 'buttons' | 'select';
}

const categoryConfig = {
  newsletter: {
    label: 'Newsletter',
    icon: Mail,
    color: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
  },
  promotional: {
    label: 'Promotional',
    icon: Megaphone,
    color: 'bg-purple-100 text-purple-800 hover:bg-purple-200'
  },
  announcement: {
    label: 'Announcement',
    icon: Bell,
    color: 'bg-orange-100 text-orange-800 hover:bg-orange-200'
  },
  welcome: {
    label: 'Welcome',
    icon: UserPlus,
    color: 'bg-green-100 text-green-800 hover:bg-green-200'
  },
  followup: {
    label: 'Follow-up',
    icon: Reply,
    color: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200'
  },
  event: {
    label: 'Event',
    icon: Calendar,
    color: 'bg-pink-100 text-pink-800 hover:bg-pink-200'
  },
  survey: {
    label: 'Survey',
    icon: ClipboardList,
    color: 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200'
  },
  notification: {
    label: 'Notification',
    icon: AlertCircle,
    color: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
  },
  custom: {
    label: 'Custom',
    icon: Settings,
    color: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
  }
};

export function TemplateCategoryFilter({
  selectedCategory,
  onCategoryChange,
  categoryCounts,
  className,
  variant = 'buttons'
}: TemplateCategoryFilterProps) {
  const categories = Object.entries(categoryConfig);

  if (variant === 'select') {
    return (
      <div className={cn('space-y-2', className)}>
        <Select
          value={selectedCategory || 'all'}
          onValueChange={(value) => 
            onCategoryChange(value === 'all' ? undefined : value as TemplateCategory)
          }
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(([key, config]) => {
              const Icon = config.icon;
              const count = categoryCounts?.[key] || 0;
              
              return (
                <SelectItem key={key} value={key}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span>{config.label}</span>
                    {count > 0 && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {count}
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Categories</h3>
        {selectedCategory && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCategoryChange(undefined)}
            className="h-6 px-2 text-xs"
          >
            <X className="h-3 w-3 mr-1" />
            Clear
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 lg:grid-cols-1">
        {categories.map(([key, config]) => {
          const Icon = config.icon;
          const count = categoryCounts?.[key] || 0;
          const isSelected = selectedCategory === key;
          
          return (
            <Button
              key={key}
              variant={isSelected ? "default" : "ghost"}
              size="sm"
              onClick={() => 
                onCategoryChange(isSelected ? undefined : key as TemplateCategory)
              }
              className={cn(
                'justify-start h-auto py-2 px-3',
                !isSelected && config.color
              )}
            >
              <Icon className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="flex-1 text-left">{config.label}</span>
              {count > 0 && (
                <Badge 
                  variant={isSelected ? "secondary" : "outline"} 
                  className="ml-2 text-xs"
                >
                  {count}
                </Badge>
              )}
            </Button>
          );
        })}
      </div>
    </div>
  );
}

export function CategoryChip({ 
  category, 
  size = 'md' 
}: { 
  category: TemplateCategory;
  size?: 'sm' | 'md' | 'lg';
}) {
  const config = categoryConfig[category];
  const Icon = config.icon;
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1.5',
    lg: 'text-base px-3 py-2'
  };
  
  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };
  
  return (
    <Badge 
      variant="outline" 
      className={cn(
        'inline-flex items-center gap-1.5 font-medium border',
        sizeClasses[size],
        config.color
      )}
    >
      <Icon className={iconSizes[size]} />
      <span>{config.label}</span>
    </Badge>
  );
}