"use client";

import { EmailTemplate } from "@/types/email-blast/email-template.types";
import { useEmailTemplate } from "@/hooks/email-blast/use-email-templates";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Edit, 
  Copy, 
  Send, 
  Trash2, 
  Download,
  ExternalLink,
  Crown,
  Calendar,
  User,
  Hash,
  Tag,
  Activity,
  Eye,
  Code,
  FileText,
  BarChart3
} from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import { CategoryChip } from "../ui/template-category-filter";
import { VariableList } from "../ui/variable-chip";
import { TemplatePreview } from "../ui/template-preview";
import { TemplateThumbnail } from "../ui/template-thumbnail";
import { TemplateUsageStats } from "./template-usage-stats";
import { DeleteTemplateDialog } from "../dialogs/delete-template-dialog";
import { DuplicateTemplateDialog } from "../dialogs/duplicate-template-dialog";
import { TestTemplateDialog } from "../dialogs/test-template-dialog";
import { useState } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface TemplateDetailProps {
  templateId: string;
  className?: string;
}

export function TemplateDetail({ templateId, className }: TemplateDetailProps) {
  const { template, loading, error, deleteTemplate } = useEmailTemplate(templateId);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Dialog states
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [duplicateDialog, setDuplicateDialog] = useState(false);
  const [testDialog, setTestDialog] = useState(false);

  if (loading) {
    return (
      <div className={cn('space-y-6', className)}>
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center space-y-2">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto" />
              <p className="text-muted-foreground">Loading template...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className={cn('space-y-6', className)}>
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center space-y-2">
              <p className="text-muted-foreground">Template not found</p>
              <Button asChild variant="outline">
                <Link href="/email-blast/templates">
                  Back to Templates
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const canEdit = !template.isSystem;
  const canDelete = !template.isSystem;

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold tracking-tight">{template.name}</h1>
            {template.isSystem && (
              <Crown className="h-6 w-6 text-amber-500" />
            )}
          </div>
          
          <div className="flex items-center gap-2 flex-wrap">
            <CategoryChip category={template.category} />
            
            <Badge 
              variant="outline" 
              className={cn(
                template.status === 'active' && 'bg-green-100 text-green-800 border-green-200',
                template.status === 'draft' && 'bg-gray-100 text-gray-800 border-gray-200',
                template.status === 'archived' && 'bg-red-100 text-red-800 border-red-200'
              )}
            >
              {template.status.charAt(0).toUpperCase() + template.status.slice(1)}
            </Badge>
            
            {template.variables.length > 0 && (
              <Badge variant="secondary">
                <Hash className="mr-1 h-3 w-3" />
                {template.variables.length} variables
              </Badge>
            )}
            
            <Badge variant="outline">
              v{template.version}
            </Badge>
          </div>
          
          {template.description && (
            <p className="text-muted-foreground max-w-2xl">{template.description}</p>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setTestDialog(true)}>
            <Send className="mr-2 h-4 w-4" />
            Send Test
          </Button>
          
          <Button variant="outline" onClick={() => setDuplicateDialog(true)}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </Button>
          
          {canEdit && (
            <Button asChild>
              <Link href={`/email-blast/templates/${template.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          )}
          
          {canDelete && (
            <Button 
              variant="destructive" 
              size="sm"
              onClick={() => setDeleteDialog(true)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Metadata Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Created by</p>
                <p className="font-medium">{template.createdBy}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Last updated</p>
                <p className="font-medium">{formatDistanceToNow(template.updatedAt, { addSuffix: true })}</p>
                <p className="text-xs text-muted-foreground">{format(template.updatedAt, 'PPP')}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Usage</p>
                <p className="font-medium">
                  {template.usageStats?.campaignsUsed || 0} campaigns
                </p>
                {template.usageStats && (
                  <p className="text-xs text-muted-foreground">
                    {template.usageStats.successRate.toFixed(1)}% success rate
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Variables</p>
                <p className="font-medium">{template.variables.length}</p>
                <p className="text-xs text-muted-foreground">
                  {template.variables.filter(v => v.required).length} required
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="code" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            Source
          </TabsTrigger>
          <TabsTrigger value="usage" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Usage Stats
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Template Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Variables */}
              {template.variables.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Hash className="h-5 w-5" />
                      Template Variables ({template.variables.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <VariableList variables={template.variables} />
                  </CardContent>
                </Card>
              )}

              {/* Tags */}
              {template.tags && template.tags.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Tag className="h-5 w-5" />
                      Tags
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {template.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Attachments */}
              {template.content.attachments && template.content.attachments.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Attachments ({template.content.attachments.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {template.content.attachments.map((attachment, index) => (
                        <div 
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-md"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-muted rounded flex items-center justify-center">
                              <FileText className="h-5 w-5" />
                            </div>
                            <div>
                              <p className="font-medium">{attachment.filename}</p>
                              <p className="text-sm text-muted-foreground">
                                {(attachment.size / 1024).toFixed(1)} KB • {attachment.mimeType}
                              </p>
                            </div>
                          </div>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Thumbnail */}
              <Card>
                <CardHeader>
                  <CardTitle>Template Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <TemplateThumbnail
                    template={template}
                    size="lg"
                    showActions={true}
                    className="w-full"
                  />
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button className="w-full justify-start" asChild>
                    <Link href={`/email-blast/templates/preview/${template.id}`}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Full Preview
                    </Link>
                  </Button>
                  
                  <Button variant="outline" className="w-full justify-start" onClick={() => setTestDialog(true)}>
                    <Send className="mr-2 h-4 w-4" />
                    Send Test Email
                  </Button>
                  
                  <Button variant="outline" className="w-full justify-start" onClick={() => setDuplicateDialog(true)}>
                    <Copy className="mr-2 h-4 w-4" />
                    Duplicate Template
                  </Button>
                  
                  {canEdit && (
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <Link href={`/email-blast/templates/${template.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Template
                      </Link>
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <TemplatePreview template={template} showActions={false} />
        </TabsContent>

        <TabsContent value="code" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Email Subject</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                  <code>{template.content.subject}</code>
                </pre>
              </CardContent>
            </Card>

            {template.content.previewText && (
              <Card>
                <CardHeader>
                  <CardTitle>Preview Text</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                    <code>{template.content.previewText}</code>
                  </pre>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Email Body (Text)</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm whitespace-pre-wrap">
                  <code>{template.content.body}</code>
                </pre>
              </CardContent>
            </Card>

            {template.content.bodyHtml && (
              <Card>
                <CardHeader>
                  <CardTitle>Email Body (HTML)</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                    <code>{template.content.bodyHtml}</code>
                  </pre>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6">
          {template.usageStats ? (
            <TemplateUsageStats stats={template.usageStats} />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center p-8">
                <div className="text-center space-y-2">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
                  <h3 className="text-lg font-semibold">No Usage Data</h3>
                  <p className="text-muted-foreground">
                    This template hasn't been used in any campaigns yet.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Template Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Name</label>
                    <p className="mt-1">{template.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Category</label>
                    <div className="mt-1">
                      <CategoryChip category={template.category} size="sm" />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                    <div className="mt-1">
                      <Badge variant="outline">
                        {template.status.charAt(0).toUpperCase() + template.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Version</label>
                    <p className="mt-1">v{template.version}</p>
                  </div>
                </div>
                
                {template.description && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="mt-1">{template.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {template.metadata && Object.keys(template.metadata).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Metadata</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(template.metadata).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-center">
                        <span className="text-sm font-medium text-muted-foreground">{key}:</span>
                        <span className="text-sm text-right max-w-xs truncate">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      {deleteDialog && (
        <DeleteTemplateDialog
          template={template}
          open={deleteDialog}
          onOpenChange={setDeleteDialog}
          onSuccess={() => {
            // Redirect to templates list after successful deletion
            window.location.href = '/email-blast/templates';
          }}
        />
      )}

      {duplicateDialog && (
        <DuplicateTemplateDialog
          template={template}
          open={duplicateDialog}
          onOpenChange={setDuplicateDialog}
          onSuccess={(newTemplate) => {
            // Redirect to new template
            window.location.href = `/email-blast/templates/${newTemplate.id}`;
          }}
        />
      )}

      {testDialog && (
        <TestTemplateDialog
          template={template}
          open={testDialog}
          onOpenChange={setTestDialog}
        />
      )}
    </div>
  );
}