"use client";

import { type TemplateUsageStats } from "@/types/email-blast/email-template.types";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart3, 
  TrendingUp, 
  Mail, 
  Target, 
  MousePointer,
  Calendar,
  Hash,
  Users
} from "lucide-react";
import { formatDistanceToNow, format, parseISO } from "date-fns";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";

interface TemplateUsageStatsProps {
  stats: TemplateUsageStats;
  className?: string;
}

export function TemplateUsageStats({ stats, className }: TemplateUsageStatsProps) {
  // Prepare chart data
  const monthlyData = stats.monthlyUsage.map(item => ({
    month: item.month,
    count: item.count,
    monthLabel: format(parseISO(item.month + '-01'), 'MMM yyyy')
  }));

  // Performance data for pie chart
  const performanceData = [
    {
      name: 'Successful',
      value: stats.successRate,
      color: '#10b981'
    },
    {
      name: 'Failed',
      value: 100 - stats.successRate,
      color: '#ef4444'
    }
  ];

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-md">
                  <Hash className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.totalUsage}</p>
                  <p className="text-sm text-muted-foreground">Total Uses</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-md">
                  <Users className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.campaignsUsed}</p>
                  <p className="text-sm text-muted-foreground">Campaigns</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-purple-100 rounded-md">
                  <Mail className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.avgOpenRate.toFixed(1)}%</p>
                  <p className="text-sm text-muted-foreground">Avg Open Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-orange-100 rounded-md">
                  <MousePointer className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.avgClickRate.toFixed(1)}%</p>
                  <p className="text-sm text-muted-foreground">Avg Click Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Success Rate */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Success Rate
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Overall Success Rate</span>
                  <span className="text-2xl font-bold">{stats.successRate.toFixed(1)}%</span>
                </div>
                <Progress value={stats.successRate} className="h-2" />
                
                <div className="pt-4">
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={performanceData}
                        cx="50%"
                        cy="50%"
                        outerRadius={60}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${(value || 0).toFixed(1)}%`}
                      >
                        {performanceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${Number(value).toFixed(1)}%`} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Last Used */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Usage Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Used</label>
                <p className="text-lg font-semibold">
                  {stats.lastUsedAt 
                    ? formatDistanceToNow(stats.lastUsedAt, { addSuffix: true })
                    : 'Never used'
                  }
                </p>
                {stats.lastUsedAt && (
                  <p className="text-sm text-muted-foreground">
                    {format(stats.lastUsedAt, 'PPP')}
                  </p>
                )}
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Performance Metrics</label>
                <div className="mt-2 space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Open Rate</span>
                    <div className="flex items-center gap-2">
                      <Progress value={stats.avgOpenRate} className="w-20 h-2" />
                      <span className="text-sm font-medium">{stats.avgOpenRate.toFixed(1)}%</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Click Rate</span>
                    <div className="flex items-center gap-2">
                      <Progress value={stats.avgClickRate} className="w-20 h-2" />
                      <span className="text-sm font-medium">{stats.avgClickRate.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Usage Chart */}
        {monthlyData.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Monthly Usage Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="monthLabel" 
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    labelStyle={{ color: '#000' }}
                    formatter={(value) => [value, 'Uses']}
                  />
                  <Bar dataKey="count" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        )}

        {/* Popular Variables */}
        {stats.popularVariables.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Popular Variables
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground mb-4">
                  Most frequently used template variables:
                </p>
                
                <div className="flex flex-wrap gap-2">
                  {stats.popularVariables.map((variable, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary"
                      className="text-sm"
                    >
                      #{index + 1} {variable}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Detailed Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Detailed Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Total Usage</label>
                <p className="text-2xl font-bold">{stats.totalUsage}</p>
                <p className="text-sm text-muted-foreground">
                  Across {stats.campaignsUsed} different campaigns
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Average Open Rate</label>
                <p className="text-2xl font-bold">{stats.avgOpenRate.toFixed(2)}%</p>
                <p className="text-sm text-muted-foreground">
                  Industry average: ~20-25%
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Average Click Rate</label>
                <p className="text-2xl font-bold">{stats.avgClickRate.toFixed(2)}%</p>
                <p className="text-sm text-muted-foreground">
                  Industry average: ~2-5%
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Success Rate</label>
                <p className="text-2xl font-bold">{stats.successRate.toFixed(2)}%</p>
                <p className="text-sm text-muted-foreground">
                  Delivery + engagement combined
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Variables Used</label>
                <p className="text-2xl font-bold">{stats.popularVariables.length}</p>
                <p className="text-sm text-muted-foreground">
                  Unique variables referenced
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Last Activity</label>
                <p className="text-lg font-semibold">
                  {stats.lastUsedAt 
                    ? formatDistanceToNow(stats.lastUsedAt, { addSuffix: true })
                    : 'No activity'
                  }
                </p>
                {stats.lastUsedAt && (
                  <p className="text-sm text-muted-foreground">
                    {format(stats.lastUsedAt, 'PPP')}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}