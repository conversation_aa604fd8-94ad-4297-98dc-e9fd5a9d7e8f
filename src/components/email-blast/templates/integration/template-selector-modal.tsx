"use client";

import { useState } from "react";
import { EmailTemplate, TemplateListFilters, TemplateStatus } from "@/types/email-blast/email-template.types";
import { useEmailTemplates } from "@/hooks/email-blast/use-email-templates";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  FileText, 
  Grid, 
  List,
  Eye,
  Check,
  Filter
} from "lucide-react";
import { TemplateCard } from "../ui/template-card";
import { TemplateThumbnailGrid, TemplateThumbnailCarousel } from "../ui/template-thumbnail";
import { TemplateCategoryFilter } from "../ui/template-category-filter";
import { useRecentlyUsedTemplates, usePopularTemplates } from "@/hooks/email-blast/use-email-templates";
import { cn } from "@/lib/utils";

interface TemplateSelectorModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectTemplate: (template: EmailTemplate) => void;
  selectedTemplateId?: string;
  title?: string;
  description?: string;
}

export function TemplateSelectorModal({
  open,
  onOpenChange,
  onSelectTemplate,
  selectedTemplateId,
  title = "Select Email Template",
  description = "Choose a template to start your email campaign"
}: TemplateSelectorModalProps) {
  const [activeTab, setActiveTab] = useState('browse');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>();
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);

  // Hooks
  const filters: TemplateListFilters = {
    search: searchQuery,
    category: selectedCategory as any,
    status: TemplateStatus.ACTIVE // Only show active templates
  };
  
  const { templates, loading } = useEmailTemplates({ initialFilters: filters });
  const { templates: recentTemplates } = useRecentlyUsedTemplates(5);
  const { templates: popularTemplates } = usePopularTemplates(6);

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        template.name.toLowerCase().includes(query) ||
        template.description?.toLowerCase().includes(query) ||
        template.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }
    return true;
  });

  const handleSelectTemplate = () => {
    if (selectedTemplate) {
      onSelectTemplate(selectedTemplate);
      onOpenChange(false);
    }
  };

  const handleTemplateClick = (template: EmailTemplate) => {
    setSelectedTemplate(template);
  };

  const handleClearSelection = () => {
    setSelectedTemplate(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="recent">Recent</TabsTrigger>
              <TabsTrigger value="popular">Popular</TabsTrigger>
              <TabsTrigger value="browse">Browse All</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden mt-4">
              <TabsContent value="recent" className="h-full m-0">
                <div className="space-y-4 h-full">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Recently Used Templates</h3>
                    <div className="text-sm text-muted-foreground">
                      {recentTemplates.length} templates
                    </div>
                  </div>

                  {recentTemplates.length === 0 ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-center space-y-2">
                        <FileText className="h-8 w-8 text-muted-foreground mx-auto" />
                        <p className="text-muted-foreground">No recently used templates</p>
                      </div>
                    </div>
                  ) : (
                    <ScrollArea className="h-96">
                      <TemplateThumbnailCarousel 
                        templates={recentTemplates}
                        className="pb-4"
                      />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        {recentTemplates.map((template) => (
                          <div
                            key={template.id}
                            className={cn(
                              'cursor-pointer transition-all duration-200 rounded-lg border-2',
                              selectedTemplate?.id === template.id
                                ? 'border-primary bg-primary/5'
                                : 'border-transparent hover:border-muted'
                            )}
                            onClick={() => handleTemplateClick(template)}
                          >
                            <TemplateCard
                              template={template}
                              className="border-0"
                            />
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="popular" className="h-full m-0">
                <div className="space-y-4 h-full">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Popular Templates</h3>
                    <div className="text-sm text-muted-foreground">
                      {popularTemplates.length} templates
                    </div>
                  </div>

                  <ScrollArea className="h-96">
                    <TemplateThumbnailGrid 
                      templates={popularTemplates}
                      selectedId={selectedTemplate?.id}
                      onSelect={handleTemplateClick}
                    />
                  </ScrollArea>
                </div>
              </TabsContent>

              <TabsContent value="browse" className="h-full m-0">
                <div className="space-y-4 h-full flex flex-col">
                  {/* Search and Filters */}
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                          placeholder="Search templates..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-9"
                        />
                      </div>
                    </div>

                    <TemplateCategoryFilter
                      selectedCategory={selectedCategory as any}
                      onCategoryChange={setSelectedCategory}
                      variant="select"
                      className="w-48"
                    />

                    <div className="flex border rounded-md">
                      <Button
                        variant={viewMode === 'grid' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('grid')}
                        className="rounded-r-none"
                      >
                        <Grid className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={viewMode === 'list' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('list')}
                        className="rounded-l-none"
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Results */}
                  <div className="flex-1 overflow-hidden">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-muted-foreground">
                        {loading ? 'Loading...' : `${filteredTemplates.length} templates found`}
                      </span>
                      
                      {(searchQuery || selectedCategory) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSearchQuery('');
                            setSelectedCategory(undefined);
                          }}
                        >
                          Clear filters
                        </Button>
                      )}
                    </div>

                    <ScrollArea className="h-80">
                      {loading ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                        </div>
                      ) : filteredTemplates.length === 0 ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="text-center space-y-2">
                            <FileText className="h-8 w-8 text-muted-foreground mx-auto" />
                            <p className="text-muted-foreground">No templates found</p>
                            <p className="text-sm text-muted-foreground">
                              Try adjusting your search or filters
                            </p>
                          </div>
                        </div>
                      ) : viewMode === 'grid' ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {filteredTemplates.map((template) => (
                            <div
                              key={template.id}
                              className={cn(
                                'cursor-pointer transition-all duration-200 rounded-lg border-2',
                                selectedTemplate?.id === template.id
                                  ? 'border-primary bg-primary/5'
                                  : 'border-transparent hover:border-muted'
                              )}
                              onClick={() => handleTemplateClick(template)}
                            >
                              <TemplateCard
                                template={template}
                                className="border-0"
                              />
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {filteredTemplates.map((template) => (
                            <div
                              key={template.id}
                              className={cn(
                                'flex items-center gap-4 p-4 rounded-lg border cursor-pointer transition-all duration-200',
                                selectedTemplate?.id === template.id
                                  ? 'border-primary bg-primary/5'
                                  : 'border-muted hover:border-muted-foreground'
                              )}
                              onClick={() => handleTemplateClick(template)}
                            >
                              <div className="w-20 h-12 bg-muted rounded flex items-center justify-center flex-shrink-0">
                                <FileText className="h-6 w-6 text-muted-foreground" />
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">{template.name}</h4>
                                <p className="text-sm text-muted-foreground truncate">
                                  {template.description}
                                </p>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {template.category}
                                  </Badge>
                                  <span className="text-xs text-muted-foreground">
                                    {template.variables.length} variables
                                  </span>
                                </div>
                              </div>

                              {selectedTemplate?.id === template.id && (
                                <Check className="h-5 w-5 text-primary flex-shrink-0" />
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </ScrollArea>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              {selectedTemplate && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{selectedTemplate.name}</Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearSelection}
                  >
                    Clear
                  </Button>
                </div>
              )}
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSelectTemplate}
                disabled={!selectedTemplate}
              >
                Use Template
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}