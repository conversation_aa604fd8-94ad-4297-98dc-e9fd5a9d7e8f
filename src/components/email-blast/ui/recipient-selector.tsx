"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Search, 
  Filter, 
  CalendarIcon,
  X,
  Ch<PERSON><PERSON>Down,
  <PERSON>,
  RefreshCw,
  Download,
  Upload 
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { 
  RecipientFilters, 
  RecipientPreview,
  MembershipStatus 
} from "@/types/email-blast/recipient-list.types";

interface RecipientSelectorProps {
  mode: "filter" | "manual";
  onApplyFilter?: (filters: RecipientFilters, count: number) => void;
  onManualSelection?: (selectedIds: string[], count: number) => void;
  initialFilters?: RecipientFilters;
  initialSelectedIds?: string[];
}

// Mock data for preview
const mockRecipients: RecipientPreview[] = [
  {
    id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    membershipType: "Premium",
    status: MembershipStatus.ACTIVE,
    expiryDate: new Date("2025-12-31"),
    location: "New York",
    phoneNumber: "+1234567890",
    tags: ["vip", "early-adopter"],
  },
  // Add more mock recipients...
];

const membershipTypes = ["Basic", "Premium", "VIP", "Corporate"];
const locations = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix"];
const tags = ["vip", "early-adopter", "newsletter", "events", "promotional"];

export function RecipientSelector({
  mode,
  onApplyFilter,
  onManualSelection,
  initialFilters = {},
  initialSelectedIds = [],
}: RecipientSelectorProps) {
  const [filters, setFilters] = useState<RecipientFilters>(initialFilters);
  const [selectedRecipients, setSelectedRecipients] = useState<string[]>(initialSelectedIds);
  const [searchQuery, setSearchQuery] = useState("");
  const [previewRecipients, setPreviewRecipients] = useState<RecipientPreview[]>(mockRecipients);
  const [isLoading, setIsLoading] = useState(false);
  const [recipientCount, setRecipientCount] = useState(0);

  useEffect(() => {
    if (mode === "filter") {
      // Simulate fetching count based on filters
      const count = Math.floor(Math.random() * 1000) + 100;
      setRecipientCount(count);
    } else {
      setRecipientCount(selectedRecipients.length);
    }
  }, [filters, selectedRecipients, mode]);

  const handleFilterChange = (key: keyof RecipientFilters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleApplyFilters = () => {
    if (onApplyFilter) {
      onApplyFilter(filters, recipientCount);
    }
  };

  const handleRecipientToggle = (recipientId: string) => {
    setSelectedRecipients((prev) =>
      prev.includes(recipientId)
        ? prev.filter((id) => id !== recipientId)
        : [...prev, recipientId]
    );
  };

  const handleSelectAll = () => {
    if (selectedRecipients.length === previewRecipients.length) {
      setSelectedRecipients([]);
    } else {
      setSelectedRecipients(previewRecipients.map((r) => r.id));
    }
  };

  const handleApplyManualSelection = () => {
    if (onManualSelection) {
      onManualSelection(selectedRecipients, selectedRecipients.length);
    }
  };

  const handleImportCSV = () => {
    // Handle CSV import
    console.log("Import CSV");
  };

  const handleExportSelection = () => {
    // Handle export
    console.log("Export selection");
  };

  if (mode === "filter") {
    return (
      <div className="space-y-6">
        <div className="grid gap-4">
          {/* Membership Status Filter */}
          <div className="space-y-2">
            <Label>Membership Status</Label>
            <div className="flex flex-wrap gap-2">
              {Object.values(MembershipStatus).map((status) => (
                <label key={status} className="flex items-center space-x-2">
                  <Checkbox
                    checked={filters.membershipStatus?.includes(status) || false}
                    onCheckedChange={(checked) => {
                      const current = filters.membershipStatus || [];
                      handleFilterChange(
                        "membershipStatus",
                        checked
                          ? [...current, status]
                          : current.filter((s) => s !== status)
                      );
                    }}
                  />
                  <span className="text-sm capitalize">{status}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Membership Types Filter */}
          <div className="space-y-2">
            <Label>Membership Types</Label>
            <Select
              value={filters.membershipTypes?.join(",") || ""}
              onValueChange={(value) =>
                handleFilterChange("membershipTypes", value ? value.split(",") : [])
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select membership types" />
              </SelectTrigger>
              <SelectContent>
                {membershipTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filters */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Expiry Date Range</Label>
              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.expiryDateRange?.from
                        ? format(filters.expiryDateRange.from, "MMM dd, yyyy")
                        : "From date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.expiryDateRange?.from}
                      onSelect={(date) =>
                        handleFilterChange("expiryDateRange", {
                          ...filters.expiryDateRange,
                          from: date,
                        })
                      }
                    />
                  </PopoverContent>
                </Popover>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.expiryDateRange?.to
                        ? format(filters.expiryDateRange.to, "MMM dd, yyyy")
                        : "To date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.expiryDateRange?.to}
                      onSelect={(date) =>
                        handleFilterChange("expiryDateRange", {
                          ...filters.expiryDateRange,
                          to: date,
                        })
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Registration Date Range</Label>
              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.registrationDateRange?.from
                        ? format(filters.registrationDateRange.from, "MMM dd, yyyy")
                        : "From date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.registrationDateRange?.from}
                      onSelect={(date) =>
                        handleFilterChange("registrationDateRange", {
                          ...filters.registrationDateRange,
                          from: date,
                        })
                      }
                    />
                  </PopoverContent>
                </Popover>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.registrationDateRange?.to
                        ? format(filters.registrationDateRange.to, "MMM dd, yyyy")
                        : "To date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.registrationDateRange?.to}
                      onSelect={(date) =>
                        handleFilterChange("registrationDateRange", {
                          ...filters.registrationDateRange,
                          to: date,
                        })
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* Location Filter */}
          <div className="space-y-2">
            <Label>Locations</Label>
            <Select
              value={filters.locations?.join(",") || ""}
              onValueChange={(value) =>
                handleFilterChange("locations", value ? value.split(",") : [])
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select locations" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tags Filter */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant={filters.tags?.includes(tag) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => {
                    const current = filters.tags || [];
                    handleFilterChange(
                      "tags",
                      current.includes(tag)
                        ? current.filter((t) => t !== tag)
                        : [...current, tag]
                    );
                  }}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Exclusions */}
          <div className="space-y-2">
            <Label>Exclusions</Label>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.excludeUnsubscribed || false}
                  onCheckedChange={(checked) =>
                    handleFilterChange("excludeUnsubscribed", checked)
                  }
                />
                <span className="text-sm">Exclude unsubscribed recipients</span>
              </label>
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.excludeBounced || false}
                  onCheckedChange={(checked) =>
                    handleFilterChange("excludeBounced", checked)
                  }
                />
                <span className="text-sm">Exclude bounced email addresses</span>
              </label>
            </div>
          </div>
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="text-sm font-medium">
              Estimated Recipients: {recipientCount.toLocaleString()}
            </span>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setFilters({})}
            >
              Clear Filters
            </Button>
            <Button onClick={handleApplyFilters}>
              <Filter className="mr-2 h-4 w-4" />
              Apply Filters
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Manual Selection Mode
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleImportCSV}>
            <Upload className="mr-2 h-4 w-4" />
            Import CSV
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportSelection}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <ScrollArea className="h-[400px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={selectedRecipients.length === previewRecipients.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Membership</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Location</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {previewRecipients
                .filter(
                  (r) =>
                    r.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    r.email.toLowerCase().includes(searchQuery.toLowerCase())
                )
                .map((recipient) => (
                  <TableRow key={recipient.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRecipients.includes(recipient.id)}
                        onCheckedChange={() => handleRecipientToggle(recipient.id)}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{recipient.name}</TableCell>
                    <TableCell>{recipient.email}</TableCell>
                    <TableCell>{recipient.membershipType}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          recipient.status === MembershipStatus.ACTIVE
                            ? "success"
                            : recipient.status === MembershipStatus.EXPIRED
                            ? "destructive"
                            : "secondary"
                        }
                      >
                        {recipient.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{recipient.location}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </Card>

      <div className="flex items-center justify-between">
        <span className="text-sm text-muted-foreground">
          {selectedRecipients.length} recipients selected
        </span>
        <Button 
          onClick={handleApplyManualSelection}
          disabled={selectedRecipients.length === 0}
        >
          Apply Selection
        </Button>
      </div>
    </div>
  );
}