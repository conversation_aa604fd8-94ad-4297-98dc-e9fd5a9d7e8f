"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Smartphone, Monitor, X, Maximize2, Minimize2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface EmailPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subject?: string;
  htmlContent?: string;
  textContent?: string;
  fromName?: string;
  fromEmail?: string;
  loading?: boolean;
}

export function EmailPreviewModal({
  open,
  onOpenChange,
  subject = "Email Subject",
  htmlContent = "",
  textContent = "",
  fromName = "Sender Name",
  fromEmail = "<EMAIL>",
  loading = false
}: EmailPreviewModalProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop');
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderEmailHeader = () => (
    <div className="border-b bg-gray-50 p-4 space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">From:</span>
        <span className="text-sm text-gray-600">{fromName} &lt;{fromEmail}&gt;</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">Subject:</span>
        <span className="text-sm text-gray-900 font-medium">{subject}</span>
      </div>
    </div>
  );

  const renderPreviewContent = () => {
    if (loading) {
      return (
        <div className="p-4 space-y-4">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      );
    }

    return (
      <Tabs defaultValue="html" className="h-full">
        <TabsList className="w-full justify-start rounded-none border-b">
          <TabsTrigger value="html">HTML Preview</TabsTrigger>
          <TabsTrigger value="text">Plain Text</TabsTrigger>
          <TabsTrigger value="source">HTML Source</TabsTrigger>
        </TabsList>
        
        <TabsContent value="html" className="m-0 h-[calc(100%-48px)]">
          <ScrollArea className="h-full">
            <div className="p-4">
              {htmlContent ? (
                <div 
                  dangerouslySetInnerHTML={{ __html: htmlContent }}
                  className="email-preview-content"
                />
              ) : (
                <p className="text-muted-foreground text-center py-8">
                  No HTML content to preview
                </p>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
        
        <TabsContent value="text" className="m-0 h-[calc(100%-48px)]">
          <ScrollArea className="h-full">
            <div className="p-4">
              {textContent ? (
                <pre className="whitespace-pre-wrap font-mono text-sm">
                  {textContent}
                </pre>
              ) : (
                <p className="text-muted-foreground text-center py-8">
                  No plain text content to preview
                </p>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
        
        <TabsContent value="source" className="m-0 h-[calc(100%-48px)]">
          <ScrollArea className="h-full">
            <div className="p-4">
              {htmlContent ? (
                <pre className="whitespace-pre-wrap font-mono text-xs bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                  {htmlContent}
                </pre>
              ) : (
                <p className="text-muted-foreground text-center py-8">
                  No HTML source to display
                </p>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={cn(
          "max-w-none h-[90vh] p-0 gap-0",
          isFullscreen ? "w-screen h-screen" : "w-[90vw] max-w-6xl"
        )}
      >
        <DialogHeader className="px-4 py-3 border-b flex-row items-center justify-between space-y-0">
          <DialogTitle>Email Preview</DialogTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center border rounded-md">
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('desktop')}
                className="rounded-r-none"
              >
                <Monitor className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Desktop</span>
              </Button>
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('mobile')}
                className="rounded-l-none"
              >
                <Smartphone className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Mobile</span>
              </Button>
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={handleFullscreenToggle}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="flex-1 bg-gray-100 p-4 overflow-hidden">
          <div className="h-full flex items-center justify-center">
            <Card 
              className={cn(
                "h-full shadow-lg overflow-hidden transition-all duration-300",
                viewMode === 'mobile' ? "w-[375px]" : "w-full max-w-3xl"
              )}
            >
              {renderEmailHeader()}
              <div className="h-[calc(100%-88px)]">
                {renderPreviewContent()}
              </div>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Add custom styles for email preview content
const emailPreviewStyles = `
  .email-preview-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
  }
  
  .email-preview-content h1,
  .email-preview-content h2,
  .email-preview-content h3,
  .email-preview-content h4,
  .email-preview-content h5,
  .email-preview-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }
  
  .email-preview-content p {
    margin-bottom: 1em;
  }
  
  .email-preview-content img {
    max-width: 100%;
    height: auto;
  }
  
  .email-preview-content a {
    color: #0066cc;
    text-decoration: underline;
  }
  
  .email-preview-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1em;
  }
  
  .email-preview-content td,
  .email-preview-content th {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
`;

// Inject styles on component mount
if (typeof window !== 'undefined') {
  const styleId = 'email-preview-styles';
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = emailPreviewStyles;
    document.head.appendChild(style);
  }
}