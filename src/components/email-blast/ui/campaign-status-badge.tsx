import { Circle } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';

// Temporary type until email-blast types are ready
type CampaignStatus = 'DRAFT' | 'SCHEDULED' | 'SENDING' | 'SENT' | 'FAILED' | 'PAUSED' | 'CANCELLED';

interface CampaignStatusBadgeProps {
  status: CampaignStatus | string | undefined;
  className?: string;
}

const statusConfig: Record<string, { color: string; label: string }> = {
  DRAFT: {
    color: "fill-gray-500 text-gray-500",
    label: "Draft"
  },
  SCHEDULED: {
    color: "fill-blue-500 text-blue-500",
    label: "Scheduled"
  },
  SENDING: {
    color: "fill-amber-500 text-amber-500",
    label: "Sending"
  },
  SENT: {
    color: "fill-green-500 text-green-500",
    label: "Sent"
  },
  FAILED: {
    color: "fill-red-500 text-red-500",
    label: "Failed"
  },
  PAUSED: {
    color: "fill-yellow-500 text-yellow-500",
    label: "Paused"
  },
  CANCELLED: {
    color: "fill-slate-500 text-slate-500",
    label: "Cancelled"
  },
  // Additional statuses for future use
  QUEUED: {
    color: "fill-purple-500 text-purple-500",
    label: "Queued"
  },
  PARTIALLY_SENT: {
    color: "fill-orange-500 text-orange-500",
    label: "Partially Sent"
  }
};

export function CampaignStatusBadge({ status, className }: CampaignStatusBadgeProps) {
  // Handle case where status is undefined
  if (!status) {
    return (
      <Button
        variant="outline"
        className={cn(
          "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
          className
        )}
      >
        <Circle className={cn("h-2 w-2", "fill-gray-400 text-gray-400")} />
        Unknown
      </Button>
    );
  }

  const config = statusConfig[status];

  // Handle case where status is not found in config
  if (!config) {
    return (
      <Button
        variant="outline"
        className={cn(
          "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
          className
        )}
      >
        <Circle className={cn("h-2 w-2", "fill-gray-400 text-gray-400")} />
        {status || "Unknown"}
      </Button>
    );
  }

  return (
    <Button
      variant="outline"
      className={cn(
        "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300 cursor-default hover:bg-transparent",
        className
      )}
    >
      <Circle className={cn("h-2 w-2", config.color)} />
      {config.label}
    </Button>
  );
}