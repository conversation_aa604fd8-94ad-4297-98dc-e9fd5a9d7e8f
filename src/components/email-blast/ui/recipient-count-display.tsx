import { Users, UserCheck, User<PERSON>, Clock, <PERSON><PERSON>he<PERSON>, MailOpen } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Card } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Skeleton } from '@/components/ui/skeleton';

interface RecipientCountDisplayProps {
  totalRecipients?: number;
  sentCount?: number;
  deliveredCount?: number;
  openedCount?: number;
  clickedCount?: number;
  bouncedCount?: number;
  failedCount?: number;
  className?: string;
  loading?: boolean;
  compact?: boolean;
}

interface CountItemProps {
  icon: React.ReactNode;
  label: string;
  count: number;
  total?: number;
  color: string;
  tooltip?: string;
}

function CountItem({ icon, label, count, total, color, tooltip }: CountItemProps) {
  const percentage = total && total > 0 ? Math.round((count / total) * 100) : 0;
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2">
            <div className={cn("flex items-center justify-center w-8 h-8 rounded-full", color)}>
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium">{count.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">{label}</p>
              {percentage > 0 && (
                <p className="text-xs text-muted-foreground">{percentage}%</p>
              )}
            </div>
          </div>
        </TooltipTrigger>
        {tooltip && (
          <TooltipContent>
            <p>{tooltip}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
}

function CompactDisplay({ totalRecipients = 0, sentCount = 0, deliveredCount = 0 }: RecipientCountDisplayProps) {
  return (
    <div className="flex items-center gap-4 text-sm">
      <div className="flex items-center gap-1">
        <Users className="h-4 w-4 text-muted-foreground" />
        <span>{totalRecipients.toLocaleString()}</span>
      </div>
      {sentCount > 0 && (
        <>
          <span className="text-muted-foreground">•</span>
          <div className="flex items-center gap-1">
            <MailCheck className="h-4 w-4 text-green-600" />
            <span className="text-green-600">{sentCount.toLocaleString()}</span>
          </div>
        </>
      )}
      {deliveredCount > 0 && (
        <>
          <span className="text-muted-foreground">•</span>
          <div className="flex items-center gap-1">
            <UserCheck className="h-4 w-4 text-blue-600" />
            <span className="text-blue-600">{deliveredCount.toLocaleString()}</span>
          </div>
        </>
      )}
    </div>
  );
}

export function RecipientCountDisplay({
  totalRecipients = 0,
  sentCount = 0,
  deliveredCount = 0,
  openedCount = 0,
  clickedCount = 0,
  bouncedCount = 0,
  failedCount = 0,
  className,
  loading = false,
  compact = false
}: RecipientCountDisplayProps) {
  if (loading) {
    return (
      <Card className={cn("p-4", className)}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center gap-2">
              <Skeleton className="w-8 h-8 rounded-full" />
              <div>
                <Skeleton className="h-4 w-16 mb-1" />
                <Skeleton className="h-3 w-12" />
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  if (compact) {
    return <CompactDisplay totalRecipients={totalRecipients} sentCount={sentCount} deliveredCount={deliveredCount} />;
  }

  return (
    <Card className={cn("p-4", className)}>
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <CountItem
          icon={<Users className="h-4 w-4 text-gray-600" />}
          label="Total Recipients"
          count={totalRecipients}
          color="bg-gray-100"
          tooltip="Total number of recipients selected for this campaign"
        />
        
        {sentCount > 0 && (
          <CountItem
            icon={<Clock className="h-4 w-4 text-blue-600" />}
            label="Sent"
            count={sentCount}
            total={totalRecipients}
            color="bg-blue-100"
            tooltip="Number of emails successfully sent"
          />
        )}
        
        {deliveredCount > 0 && (
          <CountItem
            icon={<MailCheck className="h-4 w-4 text-green-600" />}
            label="Delivered"
            count={deliveredCount}
            total={sentCount}
            color="bg-green-100"
            tooltip="Number of emails delivered to recipient inboxes"
          />
        )}
        
        {openedCount > 0 && (
          <CountItem
            icon={<MailOpen className="h-4 w-4 text-purple-600" />}
            label="Opened"
            count={openedCount}
            total={deliveredCount}
            color="bg-purple-100"
            tooltip="Number of unique email opens"
          />
        )}
        
        {clickedCount > 0 && (
          <CountItem
            icon={<UserCheck className="h-4 w-4 text-indigo-600" />}
            label="Clicked"
            count={clickedCount}
            total={openedCount}
            color="bg-indigo-100"
            tooltip="Number of recipients who clicked links"
          />
        )}
        
        {bouncedCount > 0 && (
          <CountItem
            icon={<UserX className="h-4 w-4 text-amber-600" />}
            label="Bounced"
            count={bouncedCount}
            total={sentCount}
            color="bg-amber-100"
            tooltip="Number of emails that bounced"
          />
        )}
        
        {failedCount > 0 && (
          <CountItem
            icon={<UserX className="h-4 w-4 text-red-600" />}
            label="Failed"
            count={failedCount}
            total={totalRecipients}
            color="bg-red-100"
            tooltip="Number of emails that failed to send"
          />
        )}
      </div>
    </Card>
  );
}