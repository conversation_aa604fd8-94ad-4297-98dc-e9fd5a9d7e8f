"use client";

import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  FileText, 
  Code, 
  Eye, 
  Upload, 
  Variable, 
  Image,
  Paperclip,
  X,
  Grid,
  Plus
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { TemplateSelectorModal } from "@/components/email-blast/templates/integration/template-selector-modal";
import { EmailTemplate } from "@/types/email-blast/email-template.types";

interface ContentEditorSectionProps {
  form: UseFormReturn<any>;
}

const variableOptions = [
  { value: "{{member_name}}", label: "Member Name" },
  { value: "{{member_email}}", label: "Member Email" },
  { value: "{{membership_type}}", label: "Membership Type" },
  { value: "{{expiry_date}}", label: "Expiry Date" },
  { value: "{{organization_name}}", label: "Organization Name" },
  { value: "{{current_date}}", label: "Current Date" },
];

export function ContentEditorSection({ form }: ContentEditorSectionProps) {
  const [editorMode, setEditorMode] = useState<"visual" | "html">("visual");
  const [attachments, setAttachments] = useState<any[]>([]);
  const [showVariables, setShowVariables] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);

  const handleTemplateSelect = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    form.setValue("content.templateId", template.id);
    form.setValue("content.subject", template.content.subject);
    form.setValue("content.body", template.content.body);
    if (template.content.bodyHtml) {
      form.setValue("content.bodyHtml", template.content.bodyHtml);
    }
    if (template.content.previewText) {
      form.setValue("content.previewText", template.content.previewText);
    }
  };

  const insertVariable = (variable: string) => {
    const currentContent = form.getValues("content.body");
    const textarea = document.getElementById("email-body") as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = 
        currentContent.substring(0, start) + 
        variable + 
        currentContent.substring(end);
      form.setValue("content.body", newContent);
      // Restore cursor position
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + variable.length;
        textarea.focus();
      }, 0);
    }
  };

  const handleAttachmentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newAttachments = Array.from(files).map((file) => ({
        filename: file.name,
        size: file.size,
        mimeType: file.type,
        url: URL.createObjectURL(file), // In real implementation, upload to server
      }));
      setAttachments([...attachments, ...newAttachments]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Email Content</h3>
        <p className="text-sm text-muted-foreground">
          Create your email content using templates or custom HTML
        </p>
      </div>

      {/* Template Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <FormLabel>Email Template</FormLabel>
            <FormDescription>
              Start with a pre-built template or create from scratch
            </FormDescription>
          </div>
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowTemplateSelector(true)}
          >
            <Grid className="mr-2 h-4 w-4" />
            Browse Templates
          </Button>
        </div>

        {selectedTemplate && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-8 bg-muted rounded flex items-center justify-center">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="font-medium">{selectedTemplate.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedTemplate.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{selectedTemplate.category}</Badge>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedTemplate(null);
                      form.setValue("content.templateId", "");
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <FormField
        control={form.control}
        name="content.subject"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Email Subject</FormLabel>
            <FormControl>
              <div className="relative">
                <Input 
                  placeholder="Enter email subject line" 
                  {...field} 
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-7 w-7 p-0"
                  onClick={() => setShowVariables(!showVariables)}
                >
                  <Variable className="h-4 w-4" />
                </Button>
              </div>
            </FormControl>
            <FormDescription>
              The subject line your recipients will see
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {showVariables && (
        <Alert>
          <Variable className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Available Variables:</p>
              <div className="flex flex-wrap gap-2">
                {variableOptions.map((variable) => (
                  <Badge
                    key={variable.value}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => insertVariable(variable.value)}
                  >
                    {variable.label}
                  </Badge>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <FormLabel>Email Body</FormLabel>
        <Tabs value={editorMode} onValueChange={(v) => setEditorMode(v as any)}>
          <TabsList>
            <TabsTrigger value="visual">
              <FileText className="mr-2 h-4 w-4" />
              Visual Editor
            </TabsTrigger>
            <TabsTrigger value="html">
              <Code className="mr-2 h-4 w-4" />
              HTML Editor
            </TabsTrigger>
          </TabsList>

          <TabsContent value="visual" className="mt-4">
            <FormField
              control={form.control}
              name="content.body"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      id="email-body"
                      placeholder="Write your email content here..."
                      className="min-h-[300px] font-sans"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          <TabsContent value="html" className="mt-4">
            <FormField
              control={form.control}
              name="content.bodyHtml"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="<html>...</html>"
                      className="min-h-[300px] font-mono text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Write or paste your HTML email content
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <FormLabel>Attachments</FormLabel>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => document.getElementById("attachment-upload")?.click()}
          >
            <Upload className="mr-2 h-4 w-4" />
            Add Attachment
          </Button>
          <input
            id="attachment-upload"
            type="file"
            multiple
            className="hidden"
            onChange={handleAttachmentUpload}
            accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
          />
        </div>

        {attachments.length > 0 && (
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-2">
                {attachments.map((attachment, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 rounded-md border"
                  >
                    <div className="flex items-center gap-2">
                      <Paperclip className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{attachment.filename}</span>
                      <Badge variant="secondary" className="text-xs">
                        {(attachment.size / 1024).toFixed(1)} KB
                      </Badge>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAttachment(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Alert>
          <AlertDescription>
            Maximum attachment size: 10MB per file, 25MB total
          </AlertDescription>
        </Alert>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline">
          <Eye className="mr-2 h-4 w-4" />
          Preview Email
        </Button>
        <Button type="button" variant="outline">
          Send Test Email
        </Button>
      </div>

      {/* Template Selector Modal */}
      <TemplateSelectorModal
        open={showTemplateSelector}
        onOpenChange={setShowTemplateSelector}
        onSelectTemplate={handleTemplateSelect}
        selectedTemplateId={selectedTemplate?.id}
        title="Select Email Template"
        description="Choose a template to start your email campaign content"
      />
    </div>
  );
}