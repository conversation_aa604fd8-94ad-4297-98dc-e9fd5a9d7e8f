"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { TagInput } from "@/components/ui/tag-input";

interface BasicInformationSectionProps {
  form: UseFormReturn<any>;
}

export function BasicInformationSection({ form }: BasicInformationSectionProps) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Basic Information</h3>
        <p className="text-sm text-muted-foreground">
          Provide basic details about your email campaign
        </p>
      </div>

      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Campaign Name</FormLabel>
            <FormControl>
              <Input 
                placeholder="e.g., Monthly Newsletter - December 2024" 
                {...field} 
              />
            </FormControl>
            <FormDescription>
              Choose a name that helps you identify this campaign
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Description (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Brief description of the campaign purpose and goals..."
                className="resize-none"
                rows={3}
                {...field}
              />
            </FormControl>
            <FormDescription>
              Add notes about the campaign for future reference
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="tags"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Tags (Optional)</FormLabel>
            <FormControl>
              <TagInput
                placeholder="Add tags..."
                tags={field.value || []}
                onTagsChange={field.onChange}
              />
            </FormControl>
            <FormDescription>
              Add tags to categorize and organize your campaigns
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}