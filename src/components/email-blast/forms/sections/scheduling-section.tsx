"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Clock, CalendarDays, Send, AlertCircle } from "lucide-react";
import { format, addDays, addHours, setHours, setMinutes } from "date-fns";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface SchedulingSectionProps {
  form: UseFormReturn<any>;
}

const timeSlots = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = i % 2 === 0 ? "00" : "30";
  return {
    value: `${hour.toString().padStart(2, "0")}:${minute}`,
    label: `${hour > 12 ? hour - 12 : hour || 12}:${minute} ${hour >= 12 ? "PM" : "AM"}`,
  };
});

const quickScheduleOptions = [
  { label: "In 1 hour", value: () => addHours(new Date(), 1) },
  { label: "In 4 hours", value: () => addHours(new Date(), 4) },
  { label: "Tomorrow morning (9 AM)", value: () => setMinutes(setHours(addDays(new Date(), 1), 9), 0) },
  { label: "Tomorrow afternoon (2 PM)", value: () => setMinutes(setHours(addDays(new Date(), 1), 14), 0) },
  { label: "Next Monday (9 AM)", value: () => {
    const today = new Date();
    const daysUntilMonday = (8 - today.getDay()) % 7 || 7;
    return setMinutes(setHours(addDays(today, daysUntilMonday), 9), 0);
  }},
];

export function SchedulingSection({ form }: SchedulingSectionProps) {
  const [scheduleType, setScheduleType] = useState<"now" | "later">("now");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(form.getValues("scheduledAt"));
  const [selectedTime, setSelectedTime] = useState<string>("09:00");

  const handleScheduleTypeChange = (value: string) => {
    setScheduleType(value as "now" | "later");
    if (value === "now") {
      form.setValue("scheduledAt", null);
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      updateScheduledDateTime(date, selectedTime);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    if (selectedDate) {
      updateScheduledDateTime(selectedDate, time);
    }
  };

  const updateScheduledDateTime = (date: Date, time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    const scheduledDate = new Date(date);
    scheduledDate.setHours(hours, minutes, 0, 0);
    form.setValue("scheduledAt", scheduledDate);
  };

  const handleQuickSchedule = (getDate: () => Date) => {
    const date = getDate();
    setSelectedDate(date);
    setSelectedTime(`${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`);
    form.setValue("scheduledAt", date);
    setScheduleType("later");
  };

  const scheduledAt = form.watch("scheduledAt");

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Schedule Delivery</h3>
        <p className="text-sm text-muted-foreground">
          Choose when to send your email campaign
        </p>
      </div>

      <RadioGroup value={scheduleType} onValueChange={handleScheduleTypeChange}>
        <Card className={cn(
          "cursor-pointer transition-colors",
          scheduleType === "now" && "border-primary"
        )}>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="now" id="send-now" />
              <label htmlFor="send-now" className="flex-1 cursor-pointer">
                <CardTitle className="text-base flex items-center gap-2">
                  <Send className="h-4 w-4" />
                  Send Immediately
                </CardTitle>
                <CardDescription>
                  Campaign will be sent as soon as you click send
                </CardDescription>
              </label>
            </div>
          </CardHeader>
        </Card>

        <Card className={cn(
          "cursor-pointer transition-colors",
          scheduleType === "later" && "border-primary"
        )}>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="later" id="send-later" />
              <label htmlFor="send-later" className="flex-1 cursor-pointer">
                <CardTitle className="text-base flex items-center gap-2">
                  <CalendarDays className="h-4 w-4" />
                  Schedule for Later
                </CardTitle>
                <CardDescription>
                  Choose a specific date and time to send
                </CardDescription>
              </label>
            </div>
          </CardHeader>
          {scheduleType === "later" && (
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div>
                  <FormLabel>Quick Schedule</FormLabel>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {quickScheduleOptions.map((option) => (
                      <Button
                        key={option.label}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickSchedule(option.value)}
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <FormLabel>Select Date</FormLabel>
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={handleDateSelect}
                      disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                      className="rounded-md border mt-2"
                    />
                  </div>

                  <div>
                    <FormLabel>Select Time</FormLabel>
                    <Select value={selectedTime} onValueChange={handleTimeSelect}>
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Select time" />
                      </SelectTrigger>
                      <SelectContent className="max-h-[300px]">
                        {timeSlots.map((slot) => (
                          <SelectItem key={slot.value} value={slot.value}>
                            <div className="flex items-center gap-2">
                              <Clock className="h-3 w-3" />
                              {slot.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    {scheduledAt && (
                      <Alert className="mt-4">
                        <CalendarDays className="h-4 w-4" />
                        <AlertTitle>Scheduled Send Time</AlertTitle>
                        <AlertDescription>
                          Your campaign will be sent on{" "}
                          <strong>
                            {format(new Date(scheduledAt), "MMMM d, yyyy")}
                          </strong>{" "}
                          at{" "}
                          <strong>
                            {format(new Date(scheduledAt), "h:mm a")}
                          </strong>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      </RadioGroup>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Scheduling Tips</AlertTitle>
        <AlertDescription>
          <ul className="list-disc list-inside space-y-1 mt-2">
            <li>Best open rates are typically Tuesday-Thursday, 10 AM - 2 PM</li>
            <li>Avoid sending on Mondays and Fridays when possible</li>
            <li>Consider your recipients' time zones</li>
            <li>All times are in your local timezone</li>
          </ul>
        </AlertDescription>
      </Alert>

      <FormField
        control={form.control}
        name="scheduledAt"
        render={({ field }) => (
          <FormItem className="hidden">
            <FormControl>
              <input type="hidden" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}