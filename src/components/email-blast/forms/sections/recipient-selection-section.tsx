"use client";

import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RecipientList } from "@/types/email-blast/recipient-list.types";
import { RecipientSelector } from "@/components/email-blast/ui/recipient-selector";
import { Users, Filter, UserPlus, Save, RefreshCw } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";

interface RecipientSelectionSectionProps {
  form: UseFormReturn<any>;
  recipientLists: RecipientList[];
}

export function RecipientSelectionSection({ form, recipientLists }: RecipientSelectionSectionProps) {
  const [activeTab, setActiveTab] = useState("saved-lists");
  const [selectedList, setSelectedList] = useState<RecipientList | null>(null);
  const [recipientCount, setRecipientCount] = useState(0);

  const handleListSelect = (listId: string) => {
    const list = recipientLists.find((l) => l.id === listId);
    if (list) {
      setSelectedList(list);
      setRecipientCount(list.recipientCount);
      form.setValue("recipientListId", listId);
    }
  };

  const handleCustomFilterApply = (filters: any, count: number) => {
    // Create a temporary list ID for the custom filter
    const customListId = `custom-${Date.now()}`;
    form.setValue("recipientListId", customListId);
    form.setValue("customFilters", filters);
    setRecipientCount(count);
  };

  const handleManualSelection = (selectedIds: string[], count: number) => {
    const manualListId = `manual-${Date.now()}`;
    form.setValue("recipientListId", manualListId);
    form.setValue("manualRecipientIds", selectedIds);
    setRecipientCount(count);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Select Recipients</h3>
        <p className="text-sm text-muted-foreground">
          Choose who will receive this email campaign
        </p>
      </div>

      <Alert>
        <Users className="h-4 w-4" />
        <AlertDescription>
          <strong>{recipientCount.toLocaleString()}</strong> recipients selected
        </AlertDescription>
      </Alert>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="saved-lists">
            <Users className="mr-2 h-4 w-4" />
            Saved Lists
          </TabsTrigger>
          <TabsTrigger value="custom-filter">
            <Filter className="mr-2 h-4 w-4" />
            Custom Filter
          </TabsTrigger>
          <TabsTrigger value="manual-selection">
            <UserPlus className="mr-2 h-4 w-4" />
            Manual Selection
          </TabsTrigger>
        </TabsList>

        <TabsContent value="saved-lists" className="space-y-4 mt-4">
          <FormField
            control={form.control}
            name="recipientListId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Select a Saved List</FormLabel>
                <Select onValueChange={handleListSelect} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a recipient list" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {recipientLists.map((list) => (
                      <SelectItem key={list.id} value={list.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{list.name}</span>
                          <Badge variant="secondary" className="ml-2">
                            {list.recipientCount.toLocaleString()}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Select from your pre-configured recipient lists
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {selectedList && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">{selectedList.name}</CardTitle>
                {selectedList.description && (
                  <CardDescription>{selectedList.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Recipients:</span>
                    <span className="font-medium">{selectedList.recipientCount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Updated:</span>
                    <span className="font-medium">
                      {new Date(selectedList.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                  {selectedList.tags && selectedList.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedList.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                <Separator className="my-4" />
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh Count
                  </Button>
                  <Button size="sm" variant="outline">
                    Preview Recipients
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="custom-filter" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Create Custom Filter</CardTitle>
              <CardDescription>
                Build a custom recipient list based on specific criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RecipientSelector
                mode="filter"
                onApplyFilter={handleCustomFilterApply}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manual-selection" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Manual Selection</CardTitle>
              <CardDescription>
                Search and select individual recipients for this campaign
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RecipientSelector
                mode="manual"
                onManualSelection={handleManualSelection}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex items-center gap-2 pt-4">
        <Button variant="outline" size="sm">
          <Save className="mr-2 h-4 w-4" />
          Save as New List
        </Button>
      </div>
    </div>
  );
}