"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Save, Send, Calendar, Eye } from "lucide-react";
import { EmailBlast, EmailCampaignStatus } from "@/types/email-blast/email-blast.types";
import { RecipientList } from "@/types/email-blast/recipient-list.types";
import { BasicInformationSection } from "./sections/basic-information-section";
import { RecipientSelectionSection } from "./sections/recipient-selection-section";
import { ContentEditorSection } from "./sections/content-editor-section";
import { SchedulingSection } from "./sections/scheduling-section";
import { useRouter } from "next/navigation";

const emailBlastSchema = z.object({
  name: z.string().min(1, "Campaign name is required"),
  description: z.string().optional(),
  content: z.object({
    subject: z.string().min(1, "Subject is required"),
    body: z.string().min(1, "Email content is required"),
    bodyHtml: z.string().optional(),
    templateId: z.string().optional(),
    variables: z.record(z.any()).optional(),
  }),
  recipientListId: z.string().min(1, "Please select recipients"),
  scheduledAt: z.date().optional().nullable(),
  tags: z.array(z.string()).optional(),
});

type EmailBlastFormData = z.infer<typeof emailBlastSchema>;

interface EmailBlastFormProps {
  campaign?: EmailBlast;
  recipientLists: RecipientList[];
  onSubmit: (data: EmailBlastFormData, action: "draft" | "schedule" | "send") => Promise<void>;
}

export function EmailBlastForm({ campaign, recipientLists, onSubmit }: EmailBlastFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<"draft" | "schedule" | "send" | null>(null);
  const [activeTab, setActiveTab] = useState("basic");
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<EmailBlastFormData>({
    resolver: zodResolver(emailBlastSchema),
    defaultValues: {
      name: campaign?.name || "",
      description: campaign?.description || "",
      content: {
        subject: campaign?.content.subject || "",
        body: campaign?.content.body || "",
        bodyHtml: campaign?.content.bodyHtml || "",
        templateId: campaign?.content.templateId || "",
        variables: campaign?.content.variables || {},
      },
      recipientListId: campaign?.recipientListId || "",
      scheduledAt: campaign?.scheduledAt ? new Date(campaign.scheduledAt) : null,
      tags: campaign?.tags || [],
    },
  });

  const handleSubmit = async (action: "draft" | "schedule" | "send") => {
    const isValid = await form.trigger();
    if (!isValid) {
      // Find the first tab with errors
      const errors = form.formState.errors;
      if (errors.name || errors.description) {
        setActiveTab("basic");
      } else if (errors.recipientListId) {
        setActiveTab("recipients");
      } else if (errors.content) {
        setActiveTab("content");
      } else if (errors.scheduledAt) {
        setActiveTab("schedule");
      }
      return;
    }

    if (action === "send") {
      setPendingAction(action);
      setShowConfirmDialog(true);
      return;
    }

    await executeSubmit(action);
  };

  const executeSubmit = async (action: "draft" | "schedule" | "send") => {
    setIsSubmitting(true);
    try {
      const data = form.getValues();
      await onSubmit(data, action);
      
      toast({
        title: "Success",
        description: action === "draft" 
          ? "Campaign saved as draft" 
          : action === "schedule"
          ? "Campaign scheduled successfully"
          : "Campaign sent successfully",
      });
      
      router.push("/email-blast");
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save campaign",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setShowConfirmDialog(false);
      setPendingAction(null);
    }
  };

  const handlePreview = () => {
    const data = form.getValues();
    // Open preview in new window or modal
    console.log("Preview:", data);
    toast({
      title: "Preview",
      description: "Preview functionality coming soon",
    });
  };

  return (
    <>
      <Form {...form}>
        <form className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="recipients">Recipients</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="schedule">Schedule</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="mt-6">
              <Card className="p-6">
                <BasicInformationSection form={form} />
              </Card>
            </TabsContent>

            <TabsContent value="recipients" className="mt-6">
              <Card className="p-6">
                <RecipientSelectionSection form={form} recipientLists={recipientLists} />
              </Card>
            </TabsContent>

            <TabsContent value="content" className="mt-6">
              <Card className="p-6">
                <ContentEditorSection form={form} />
              </Card>
            </TabsContent>

            <TabsContent value="schedule" className="mt-6">
              <Card className="p-6">
                <SchedulingSection form={form} />
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-between">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/email-blast")}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handlePreview}
                disabled={isSubmitting}
              >
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </Button>
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleSubmit("draft")}
                disabled={isSubmitting}
              >
                {isSubmitting && pendingAction === "draft" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                Save Draft
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => handleSubmit("schedule")}
                disabled={isSubmitting || !form.watch("scheduledAt")}
              >
                {isSubmitting && pendingAction === "schedule" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Calendar className="mr-2 h-4 w-4" />
                )}
                Schedule
              </Button>
              <Button
                type="button"
                onClick={() => handleSubmit("send")}
                disabled={isSubmitting}
              >
                {isSubmitting && pendingAction === "send" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Send Now
              </Button>
            </div>
          </div>
        </form>
      </Form>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Send</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to send this email campaign now? This action cannot be undone.
              <br /><br />
              Campaign: <strong>{form.watch("name")}</strong>
              <br />
              Recipients: <strong>{recipientLists.find(list => list.id === form.watch("recipientListId"))?.recipientCount || 0}</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => executeSubmit("send")}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Send className="mr-2 h-4 w-4" />
              )}
              Send Campaign
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}