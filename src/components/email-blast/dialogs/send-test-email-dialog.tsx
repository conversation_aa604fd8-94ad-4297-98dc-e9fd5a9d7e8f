"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Mail, Send, Plus, X, AlertCircle, CheckCircle } from "lucide-react";

interface SendTestEmailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  campaignName?: string;
  subject?: string;
  onSendTest?: (emails: string[]) => Promise<void>;
}

export function SendTestEmailDialog({
  open,
  onOpenChange,
  campaignName = "Email Campaign",
  subject = "Test Email",
  onSendTest
}: SendTestEmailDialogProps) {
  const [emails, setEmails] = useState<string[]>([]);
  const [currentEmail, setCurrentEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleAddEmail = () => {
    const trimmedEmail = currentEmail.trim().toLowerCase();
    
    if (!trimmedEmail) {
      setError("Please enter an email address");
      return;
    }
    
    if (!validateEmail(trimmedEmail)) {
      setError("Please enter a valid email address");
      return;
    }
    
    if (emails.includes(trimmedEmail)) {
      setError("This email is already in the list");
      return;
    }
    
    if (emails.length >= 10) {
      setError("Maximum 10 test emails allowed");
      return;
    }
    
    setEmails([...emails, trimmedEmail]);
    setCurrentEmail("");
    setError("");
  };

  const handleRemoveEmail = (email: string) => {
    setEmails(emails.filter(e => e !== email));
    setError("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddEmail();
    }
  };

  const handleSend = async () => {
    if (emails.length === 0) {
      setError("Please add at least one email address");
      return;
    }

    try {
      setIsLoading(true);
      setError("");
      
      if (onSendTest) {
        await onSendTest(emails);
      } else {
        // Simulate sending
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      toast({
        title: "Test emails sent",
        description: `Successfully sent test emails to ${emails.length} recipient${emails.length > 1 ? 's' : ''}.`,
        variant: "default",
      });
      
      // Reset and close
      setEmails([]);
      setCurrentEmail("");
      onOpenChange(false);
      
    } catch (error) {
      console.error("Error sending test emails:", error);
      toast({
        title: "Failed to send",
        description: error instanceof Error ? error.message : "Failed to send test emails. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setEmails([]);
    setCurrentEmail("");
    setError("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-blue-600" />
            Send Test Email
          </DialogTitle>
          <DialogDescription>
            Send a test version of your email campaign to review before sending to all recipients.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Campaign Info */}
          <div className="p-4 bg-muted/50 rounded-lg space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Campaign:</span>
              <span className="font-medium">{campaignName}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Subject:</span>
              <span className="font-medium truncate max-w-[300px]">{subject}</span>
            </div>
          </div>

          {/* Email Input */}
          <div className="space-y-2">
            <Label htmlFor="email">Test Email Recipients</Label>
            <div className="flex gap-2">
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                value={currentEmail}
                onChange={(e) => setCurrentEmail(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isLoading || emails.length >= 10}
              />
              <Button
                type="button"
                onClick={handleAddEmail}
                disabled={isLoading || emails.length >= 10}
                size="icon"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Add up to 10 email addresses to receive the test email
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Email List */}
          {emails.length > 0 && (
            <div className="space-y-2">
              <Label>Recipients ({emails.length}/10)</Label>
              <div className="flex flex-wrap gap-2 p-3 bg-muted/30 rounded-lg min-h-[60px]">
                {emails.map((email) => (
                  <Badge
                    key={email}
                    variant="secondary"
                    className="flex items-center gap-1 pr-1"
                  >
                    <span className="text-xs">{email}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveEmail(email)}
                      disabled={isLoading}
                      className="ml-1 hover:bg-muted rounded p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Info Message */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Note:</strong> Test emails will include all content and formatting but may show placeholder data for personalization tags.
            </AlertDescription>
          </Alert>

          {/* Success Indicators */}
          {emails.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Test Email Features:</Label>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Full email content and formatting</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Subject line and sender information</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Links and call-to-action buttons</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Images and attachments (if any)</span>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSend}
            disabled={isLoading || emails.length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Test Email{emails.length > 1 ? 's' : ''}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}