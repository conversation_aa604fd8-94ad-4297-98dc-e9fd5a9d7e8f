"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Send, AlertTriangle, Users, Mail, Clock, Calendar, CheckCircle, AlertCircle } from "lucide-react";
import { format } from "date-fns";

interface ConfirmSendDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  campaignName?: string;
  subject?: string;
  recipientCount?: number;
  scheduledDate?: Date | null;
  isScheduled?: boolean;
  onConfirm?: () => Promise<void>;
  testEmailsSent?: number;
}

export function ConfirmSendDialog({
  open,
  onOpenChange,
  campaignName = "Email Campaign",
  subject = "Email Subject",
  recipientCount = 0,
  scheduledDate,
  isScheduled = false,
  onConfirm,
  testEmailsSent = 0
}: ConfirmSendDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [confirmations, setConfirmations] = useState({
    content: false,
    recipients: false,
    schedule: false,
    testing: false
  });
  const { toast } = useToast();

  const allConfirmed = Object.values(confirmations).every(val => val);
  const hasTestEmails = testEmailsSent > 0;

  const handleConfirm = async () => {
    if (!allConfirmed) {
      toast({
        title: "Confirmation Required",
        description: "Please confirm all items in the checklist before sending.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      if (onConfirm) {
        await onConfirm();
      } else {
        // Simulate sending
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      const actionText = isScheduled ? "scheduled" : "sent";
      toast({
        title: `Campaign ${actionText} successfully`,
        description: isScheduled 
          ? `Your campaign will be sent on ${scheduledDate ? format(scheduledDate, "PPP 'at' p") : 'the scheduled date'}.`
          : `Your campaign is being sent to ${recipientCount.toLocaleString()} recipients.`,
        variant: "default",
      });
      
      // Close dialog
      onOpenChange(false);
      
    } catch (error) {
      console.error("Error confirming send:", error);
      toast({
        title: "Failed to send",
        description: error instanceof Error ? error.message : "Failed to send campaign. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setConfirmations({
      content: false,
      recipients: false,
      schedule: false,
      testing: false
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5 text-blue-600" />
            {isScheduled ? "Schedule Campaign" : "Send Campaign"}
          </DialogTitle>
          <DialogDescription>
            Please review and confirm your email campaign details before {isScheduled ? "scheduling" : "sending"}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Campaign Summary */}
          <div className="p-4 bg-muted/50 rounded-lg space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Campaign Summary
            </h4>
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex items-start gap-2">
                <span className="text-muted-foreground min-w-[100px]">Campaign:</span>
                <span className="font-medium">{campaignName}</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-muted-foreground min-w-[100px]">Subject:</span>
                <span className="font-medium">{subject}</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-muted-foreground min-w-[100px]">Recipients:</span>
                <span className="font-medium flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {recipientCount.toLocaleString()}
                </span>
              </div>
              {isScheduled && scheduledDate && (
                <div className="flex items-start gap-2">
                  <span className="text-muted-foreground min-w-[100px]">Send Date:</span>
                  <span className="font-medium flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {format(scheduledDate, "PPP 'at' p")}
                  </span>
                </div>
              )}
              {hasTestEmails && (
                <div className="flex items-start gap-2">
                  <span className="text-muted-foreground min-w-[100px]">Test Emails:</span>
                  <span className="font-medium text-green-600 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    {testEmailsSent} sent
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Warning for large recipient count */}
          {recipientCount > 1000 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Large Campaign:</strong> You're about to send to {recipientCount.toLocaleString()} recipients. 
                This may take several minutes to complete.
              </AlertDescription>
            </Alert>
          )}

          {/* Warning if no test emails sent */}
          {!hasTestEmails && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>No test emails sent:</strong> It's recommended to send test emails before sending to all recipients.
              </AlertDescription>
            </Alert>
          )}

          {/* Confirmation Checklist */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Please confirm the following:</Label>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Checkbox
                  id="content"
                  checked={confirmations.content}
                  onCheckedChange={(checked) => 
                    setConfirmations(prev => ({ ...prev, content: checked as boolean }))
                  }
                  disabled={isLoading}
                />
                <Label htmlFor="content" className="text-sm font-normal cursor-pointer">
                  I have reviewed the email content and it is ready to send
                </Label>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="recipients"
                  checked={confirmations.recipients}
                  onCheckedChange={(checked) => 
                    setConfirmations(prev => ({ ...prev, recipients: checked as boolean }))
                  }
                  disabled={isLoading}
                />
                <Label htmlFor="recipients" className="text-sm font-normal cursor-pointer">
                  I have verified the recipient list ({recipientCount.toLocaleString()} recipients)
                </Label>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="schedule"
                  checked={confirmations.schedule}
                  onCheckedChange={(checked) => 
                    setConfirmations(prev => ({ ...prev, schedule: checked as boolean }))
                  }
                  disabled={isLoading}
                />
                <Label htmlFor="schedule" className="text-sm font-normal cursor-pointer">
                  {isScheduled 
                    ? `I confirm the scheduled send time (${scheduledDate ? format(scheduledDate, "PPP 'at' p") : 'scheduled'})`
                    : "I want to send this campaign immediately"
                  }
                </Label>
              </div>
              
              <div className="flex items-start gap-3">
                <Checkbox
                  id="testing"
                  checked={confirmations.testing}
                  onCheckedChange={(checked) => 
                    setConfirmations(prev => ({ ...prev, testing: checked as boolean }))
                  }
                  disabled={isLoading}
                />
                <Label htmlFor="testing" className="text-sm font-normal cursor-pointer">
                  {hasTestEmails 
                    ? "I have reviewed the test emails and everything looks correct"
                    : "I understand that no test emails were sent and want to proceed anyway"
                  }
                </Label>
              </div>
            </div>
          </div>

          {/* Final Warning */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> {isScheduled 
                ? "Once scheduled, the campaign will be sent automatically at the specified time."
                : "Once sent, this action cannot be undone. Emails will be delivered to all recipients."
              }
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={isLoading || !allConfirmed}
            className={allConfirmed ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                {isScheduled ? "Scheduling..." : "Sending..."}
              </>
            ) : (
              <>
                {isScheduled ? (
                  <>
                    <Clock className="mr-2 h-4 w-4" />
                    Schedule Campaign
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Send Campaign Now
                  </>
                )}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}