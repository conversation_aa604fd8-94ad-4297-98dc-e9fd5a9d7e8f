"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ArrowUpDown, MoreHorizontal, Edit, Copy, Trash, Send, Eye, BarChart } from "lucide-react";
import { EmailBlast, EmailCampaignStatus } from "@/types/email-blast/email-blast.types";
import { format } from "date-fns";
import { Progress } from "@/components/ui/progress";

const statusColors: Record<EmailCampaignStatus, string> = {
  [EmailCampaignStatus.DRAFT]: "secondary",
  [EmailCampaignStatus.SCHEDULED]: "warning",
  [EmailCampaignStatus.SENDING]: "info",
  [EmailCampaignStatus.SENT]: "success",
  [EmailCampaignStatus.FAILED]: "destructive",
};

export const columns: ColumnDef<EmailBlast>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Campaign Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const campaign = row.original;
      return (
        <div>
          <div className="font-medium">{campaign.name}</div>
          {campaign.description && (
            <div className="text-sm text-muted-foreground">{campaign.description}</div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as EmailCampaignStatus;
      return (
        <Badge variant={statusColors[status] as any}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "content.subject",
    header: "Subject",
    cell: ({ row }) => {
      const subject = row.original.content.subject;
      return (
        <div className="max-w-[300px] truncate" title={subject}>
          {subject}
        </div>
      );
    },
  },
  {
    accessorKey: "recipientCount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Recipients
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const campaign = row.original;
      if (campaign.status === EmailCampaignStatus.DRAFT) {
        return <span className="text-muted-foreground">-</span>;
      }
      return (
        <div className="text-right">
          {campaign.recipientCount?.toLocaleString() || 0}
        </div>
      );
    },
  },
  {
    id: "performance",
    header: "Performance",
    cell: ({ row }) => {
      const campaign = row.original;
      
      if (campaign.status !== EmailCampaignStatus.SENT && campaign.status !== EmailCampaignStatus.SENDING) {
        return <span className="text-muted-foreground">-</span>;
      }

      const sentCount = campaign.sentCount || 0;
      const recipientCount = campaign.recipientCount || 0;
      const openCount = campaign.openCount || 0;
      const clickCount = campaign.clickCount || 0;
      
      const deliveryRate = recipientCount > 0 ? (sentCount / recipientCount) * 100 : 0;
      const openRate = sentCount > 0 ? (openCount / sentCount) * 100 : 0;
      const clickRate = openCount > 0 ? (clickCount / openCount) * 100 : 0;

      return (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground w-16">Delivery:</span>
            <Progress value={deliveryRate} className="h-2 w-20" />
            <span className="text-xs">{deliveryRate.toFixed(1)}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground w-16">Opens:</span>
            <Progress value={openRate} className="h-2 w-20" />
            <span className="text-xs">{openRate.toFixed(1)}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground w-16">Clicks:</span>
            <Progress value={clickRate} className="h-2 w-20" />
            <span className="text-xs">{clickRate.toFixed(1)}%</span>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "scheduledAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Scheduled
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const scheduledAt = row.getValue("scheduledAt") as Date | undefined;
      if (!scheduledAt) {
        return <span className="text-muted-foreground">-</span>;
      }
      return format(new Date(scheduledAt), "MMM d, yyyy h:mm a");
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as Date;
      return format(new Date(createdAt), "MMM d, yyyy");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const campaign = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            {campaign.status === EmailCampaignStatus.DRAFT && (
              <>
                <DropdownMenuItem>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Campaign
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Send className="mr-2 h-4 w-4" />
                  Send Now
                </DropdownMenuItem>
              </>
            )}
            {(campaign.status === EmailCampaignStatus.SENT || campaign.status === EmailCampaignStatus.SENDING) && (
              <DropdownMenuItem>
                <BarChart className="mr-2 h-4 w-4" />
                View Analytics
              </DropdownMenuItem>
            )}
            <DropdownMenuItem>
              <Copy className="mr-2 h-4 w-4" />
              Duplicate Campaign
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {campaign.status === EmailCampaignStatus.DRAFT && (
              <DropdownMenuItem className="text-destructive">
                <Trash className="mr-2 h-4 w-4" />
                Delete Campaign
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];