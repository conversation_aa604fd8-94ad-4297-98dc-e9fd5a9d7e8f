"use client";

import { UseFormReturn } from "react-hook-form";
import { FormField } from "@/components/ui/form";
import { FilterBuilder } from "../../ui/filter-builder";
import { RecipientFilters } from "@/types/email-blast/recipient-list.types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface FilterCriteriaSectionProps {
  form: UseFormReturn<any>;
  onPreview?: () => void;
  isLoadingPreview?: boolean;
}

export function FilterCriteriaSection({ 
  form, 
  onPreview,
  isLoadingPreview = false
}: FilterCriteriaSectionProps) {
  const isStatic = form.watch("isStatic");

  if (isStatic) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          This is a static list. Recipients must be manually added or imported.
          Switch to a dynamic list to use filters.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Define filters to automatically include recipients based on their attributes.
          Recipients matching these criteria will be dynamically included in the list.
        </AlertDescription>
      </Alert>

      <FormField
        control={form.control}
        name="filters"
        render={({ field }) => (
          <FilterBuilder
            filters={field.value as RecipientFilters}
            onChange={field.onChange}
            onPreview={onPreview}
            isLoadingPreview={isLoadingPreview}
          />
        )}
      />

      <div className="space-y-4 pt-4 border-t">
        <h4 className="text-sm font-medium">Additional Options</h4>
        
        <div className="space-y-3">
          <FormField
            control={form.control}
            name="filters.excludeUnsubscribed"
            render={({ field }) => (
              <div className="flex items-center justify-between">
                <Label htmlFor="exclude-unsubscribed" className="cursor-pointer">
                  <div>
                    <div className="font-normal">Exclude Unsubscribed</div>
                    <div className="text-sm text-muted-foreground">
                      Don't include recipients who have unsubscribed
                    </div>
                  </div>
                </Label>
                <Switch
                  id="exclude-unsubscribed"
                  checked={field.value || false}
                  onCheckedChange={field.onChange}
                />
              </div>
            )}
          />

          <FormField
            control={form.control}
            name="filters.excludeBounced"
            render={({ field }) => (
              <div className="flex items-center justify-between">
                <Label htmlFor="exclude-bounced" className="cursor-pointer">
                  <div>
                    <div className="font-normal">Exclude Bounced</div>
                    <div className="text-sm text-muted-foreground">
                      Don't include recipients with bounced email addresses
                    </div>
                  </div>
                </Label>
                <Switch
                  id="exclude-bounced"
                  checked={field.value || false}
                  onCheckedChange={field.onChange}
                />
              </div>
            )}
          />
        </div>
      </div>
    </div>
  );
}