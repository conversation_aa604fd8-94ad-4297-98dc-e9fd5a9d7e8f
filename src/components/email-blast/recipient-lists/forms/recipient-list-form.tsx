"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { RecipientList, RecipientFilters, MembershipStatus, CustomFieldFilter, FilterOperator } from "@/types/email-blast/recipient-list.types";
import { ListInfoSection } from "./sections/list-info-section";
import { FilterCriteriaSection } from "./sections/filter-criteria-section";
import { RecipientPreviewTable } from "../ui/recipient-preview-table";
import { Save, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";

const recipientListSchema = z.object({
  name: z.string().min(1, "List name is required"),
  description: z.string().optional(),
  isStatic: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  filters: z.object({
    membershipStatus: z.array(z.nativeEnum(MembershipStatus)).optional(),
    expiryDateRange: z.object({
      from: z.date().optional(),
      to: z.date().optional(),
    }).optional(),
    membershipTypes: z.array(z.string()).optional(),
    customFields: z.array(z.object({
      fieldName: z.string(),
      operator: z.union([z.nativeEnum(FilterOperator), z.string()]),
      value: z.any()
    })).optional(),
    tags: z.array(z.string()).optional(),
    locations: z.array(z.string()).optional(),
    ageRange: z.object({
      min: z.number().optional(),
      max: z.number().optional(),
    }).optional(),
    registrationDateRange: z.object({
      from: z.date().optional(),
      to: z.date().optional(),
    }).optional(),
    lastActivityDateRange: z.object({
      from: z.date().optional(),
      to: z.date().optional(),
    }).optional(),
    excludeUnsubscribed: z.boolean().optional(),
    excludeBounced: z.boolean().optional(),
  }).default({}),
});

type RecipientListFormData = z.infer<typeof recipientListSchema>;

interface RecipientListFormProps {
  list?: RecipientList;
  onSubmit?: (data: RecipientListFormData) => Promise<void>;
}

export function RecipientListForm({ list, onSubmit }: RecipientListFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("info");
  const [previewMembers, setPreviewMembers] = useState<any[]>([]);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  const form = useForm<RecipientListFormData>({
    resolver: zodResolver(recipientListSchema),
    defaultValues: {
      name: list?.name || "",
      description: list?.description || "",
      isStatic: list?.isStatic || false,
      tags: list?.tags || [],
      filters: list?.filters || {},
    },
  });

  const handleSubmit = async (data: RecipientListFormData) => {
    try {
      setIsSubmitting(true);
      
      if (onSubmit) {
        await onSubmit(data);
      } else {
        // Default behavior - create or update via API
        const response = await fetch(
          list ? `/api/email-blast/recipient-lists/${list.id}` : "/api/email-blast/recipient-lists",
          {
            method: list ? "PUT" : "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to save recipient list");
        }

        // toast.success(list ? "Recipient list updated" : "Recipient list created");
        router.push("/email-blast/recipient-lists");
      }
    } catch (error) {
      console.error("Error saving recipient list:", error);
      // toast.error("Failed to save recipient list");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/email-blast/recipient-lists");
  };

  const handlePreviewRecipients = async () => {
    const filters = form.getValues("filters");
    // Ensure customFields have required value property
    const processedFilters: RecipientFilters = {
      ...filters,
      customFields: filters.customFields?.filter(f => f.value !== undefined).map(f => ({
        fieldName: f.fieldName,
        operator: f.operator,
        value: f.value as any
      })) || undefined
    };
    
    setIsLoadingPreview(true);
    try {
      // Use the service method instead of API call for now
      const { recipientListsService } = await import('@/services/email-blast/recipient-lists.service');
      const members = await recipientListsService.previewMembers(processedFilters);
      
      setPreviewMembers(members);
      setShowPreview(true);
      
      // Auto-switch to preview tab
      setActiveTab("preview");
      
      toast({
        title: "Preview Generated",
        description: `Found ${members.length} recipients matching your criteria`,
      });
    } catch (error) {
      console.error("Error previewing recipients:", error);
      toast({
        title: "Error",
        description: "Failed to preview recipients",
        variant: "destructive",
      });
    } finally {
      setIsLoadingPreview(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Card>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="info">List Information</TabsTrigger>
              <TabsTrigger value="filters">Filter Criteria</TabsTrigger>
              <TabsTrigger value="preview" disabled={!showPreview}>Preview ({previewMembers.length})</TabsTrigger>
            </TabsList>
            
            <TabsContent value="info" className="p-6">
              <ListInfoSection form={form} />
            </TabsContent>
            
            <TabsContent value="filters" className="p-6">
              <FilterCriteriaSection 
                form={form} 
                onPreview={handlePreviewRecipients}
                isLoadingPreview={isLoadingPreview}
              />
            </TabsContent>
            
            <TabsContent value="preview" className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium">Recipient Preview</h3>
                    <p className="text-sm text-muted-foreground">
                      {previewMembers.length} recipients match your current filter criteria
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    onClick={handlePreviewRecipients}
                    disabled={isLoadingPreview}
                  >
                    {isLoadingPreview ? "Refreshing..." : "Refresh Preview"}
                  </Button>
                </div>
                <RecipientPreviewTable 
                  recipients={previewMembers}
                  isLoading={isLoadingPreview}
                  maxHeight="500px"
                />
              </div>
            </TabsContent>
          </Tabs>
        </Card>

        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "Saving..." : list ? "Update List" : "Create List"}
          </Button>
        </div>
      </form>
    </Form>
  );
}