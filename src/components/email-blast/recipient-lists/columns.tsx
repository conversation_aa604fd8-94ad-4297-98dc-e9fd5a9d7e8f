"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Users, Eye, Edit, Copy, Trash } from "lucide-react";
import { RecipientList } from "@/types/email-blast/recipient-list.types";
import { format } from "date-fns";

interface RecipientListColumnsProps {
  onView?: (list: RecipientList) => void;
  onDuplicate?: (list: RecipientList) => void;
  onDelete?: (list: RecipientList) => void;
}

export const recipientListColumns = (props: RecipientListColumnsProps): ColumnDef<RecipientList>[] => [
  {
    accessorKey: "name",
    header: "List Name",
    cell: ({ row }) => {
      const list = row.original;
      return (
        <div className="flex flex-col">
          <span className="font-medium">{list.name}</span>
          {list.description && (
            <span className="text-sm text-muted-foreground">
              {list.description}
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "recipientCount",
    header: "Recipients",
    cell: ({ row }) => {
      const count = row.getValue("recipientCount") as number;
      return (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{count.toLocaleString()}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "isStatic",
    header: "Type",
    cell: ({ row }) => {
      const isStatic = row.getValue("isStatic") as boolean;
      return (
        <Badge variant={isStatic ? "secondary" : "default"}>
          {isStatic ? "Static" : "Dynamic"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "tags",
    header: "Tags",
    cell: ({ row }) => {
      const tags = row.getValue("tags") as string[] | undefined;
      if (!tags || tags.length === 0) return null;
      
      return (
        <div className="flex flex-wrap gap-1">
          {tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{tags.length - 3}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Last Updated",
    cell: ({ row }) => {
      const date = row.getValue("updatedAt") as Date;
      return (
        <div className="text-sm text-muted-foreground">
          {format(new Date(date), "MMM d, yyyy")}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const list = row.original;
      
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => props.onView?.(list)}>
              <Eye className="mr-2 h-4 w-4" />
              View Recipients
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => props.onView?.(list)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit List
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => props.onDuplicate?.(list)}>
              <Copy className="mr-2 h-4 w-4" />
              Duplicate List
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-destructive"
              onClick={() => props.onDelete?.(list)}
            >
              <Trash className="mr-2 h-4 w-4" />
              Delete List
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];