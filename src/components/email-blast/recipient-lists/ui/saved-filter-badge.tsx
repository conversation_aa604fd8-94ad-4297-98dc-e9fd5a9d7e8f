"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { X, Filter, Calendar, Users, Tag } from "lucide-react";
import { cn } from "@/lib/utils";

interface FilterValue {
  field: string;
  operator: string;
  value: any;
  label?: string;
}

interface SavedFilterBadgeProps {
  filter: FilterValue | string; // Can be a filter object or a simple string
  onRemove?: () => void;
  variant?: "default" | "secondary" | "outline" | "destructive";
  className?: string;
  showIcon?: boolean;
  removable?: boolean;
}

export function SavedFilterBadge({
  filter,
  onRemove,
  variant = "secondary",
  className,
  showIcon = true,
  removable = true,
}: SavedFilterBadgeProps) {
  // Helper function to get icon based on filter type
  const getFilterIcon = (filterField: string) => {
    switch (filterField) {
      case "membershipStatus":
      case "status":
        return Users;
      case "joinDate":
      case "expiryDate":
      case "dateRange":
        return Calendar;
      case "membershipType":
      case "category":
        return Tag;
      default:
        return Filter;
    }
  };

  // Helper function to format filter display
  const formatFilterDisplay = () => {
    if (typeof filter === "string") {
      return filter;
    }

    const { field, operator, value, label } = filter;
    
    if (label) {
      return label;
    }

    // Format based on field type
    const formatValue = () => {
      if (Array.isArray(value)) {
        return value.join(", ");
      }
      if (typeof value === "object" && value !== null) {
        // Handle date ranges
        if (value.from && value.to) {
          return `${new Date(value.from).toLocaleDateString()} - ${new Date(
            value.to
          ).toLocaleDateString()}`;
        }
        return JSON.stringify(value);
      }
      return String(value);
    };

    const operatorMap: Record<string, string> = {
      equals: "is",
      not_equals: "is not",
      contains: "contains",
      not_contains: "doesn't contain",
      greater_than: ">",
      less_than: "<",
      between: "between",
      in: "in",
      not_in: "not in",
    };

    const formattedOperator = operatorMap[operator] || operator;
    const formattedField = field
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim();

    return `${formattedField} ${formattedOperator} ${formatValue()}`;
  };

  const displayText = formatFilterDisplay();
  const Icon =
    typeof filter === "object" && filter.field
      ? getFilterIcon(filter.field)
      : Filter;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant={variant}
            className={cn(
              "inline-flex items-center gap-1.5 px-2.5 py-1 text-xs",
              removable && "pr-1",
              className
            )}
          >
            {showIcon && <Icon className="h-3 w-3" />}
            <span className="max-w-[200px] truncate">{displayText}</span>
            {removable && onRemove && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent ml-1"
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove();
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{displayText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Compound component for displaying multiple filters
interface SavedFilterGroupProps {
  filters: (FilterValue | string)[];
  onRemove?: (index: number) => void;
  onClearAll?: () => void;
  className?: string;
  maxVisible?: number;
}

export function SavedFilterGroup({
  filters,
  onRemove,
  onClearAll,
  className,
  maxVisible = 5,
}: SavedFilterGroupProps) {
  const visibleFilters = filters.slice(0, maxVisible);
  const hiddenCount = filters.length - maxVisible;

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      {visibleFilters.map((filter, index) => (
        <SavedFilterBadge
          key={index}
          filter={filter}
          onRemove={onRemove ? () => onRemove(index) : undefined}
          removable={!!onRemove}
        />
      ))}
      
      {hiddenCount > 0 && (
        <Badge variant="outline" className="text-xs">
          +{hiddenCount} more
        </Badge>
      )}
      
      {onClearAll && filters.length > 0 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="h-6 text-xs text-muted-foreground hover:text-foreground"
        >
          Clear all
        </Button>
      )}
    </div>
  );
}