"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, X } from "lucide-react";
import { RecipientFilters } from "@/types/email-blast/recipient-list.types";
import { MembershipStatusFilter } from "./filters/membership-status-filter";
import { DateRangeFilter } from "./filters/date-range-filter";
import { MembershipTypeFilter } from "./filters/membership-type-filter";
import { CustomFieldFilter } from "./filters/custom-field-filter";

interface FilterBuilderProps {
  filters: RecipientFilters;
  onChange: (filters: RecipientFilters) => void;
  onPreview?: () => void;
  isLoadingPreview?: boolean;
}

type FilterType = 
  | "membershipStatus" 
  | "expiryDateRange" 
  | "membershipTypes" 
  | "registrationDateRange"
  | "lastActivityDateRange"
  | "ageRange"
  | "tags"
  | "locations"
  | "customFields";

const filterLabels: Record<FilterType, string> = {
  membershipStatus: "Membership Status",
  expiryDateRange: "Expiry Date",
  membershipTypes: "Membership Types",
  registrationDateRange: "Registration Date",
  lastActivityDateRange: "Last Activity",
  ageRange: "Age Range",
  tags: "Tags",
  locations: "Locations",
  customFields: "Custom Fields",
};

export function FilterBuilder({ filters, onChange, onPreview, isLoadingPreview = false }: FilterBuilderProps) {
  const [selectedFilterType, setSelectedFilterType] = useState<FilterType | "">("");
  
  const activeFilters = Object.entries(filters).filter(
    ([_, value]) => value !== undefined && value !== null
  );

  const addFilter = () => {
    if (!selectedFilterType) return;
    
    // Initialize the filter with default values
    const newFilters = { ...filters };
    
    switch (selectedFilterType) {
      case "membershipStatus":
        newFilters.membershipStatus = [];
        break;
      case "expiryDateRange":
      case "registrationDateRange":
      case "lastActivityDateRange":
        newFilters[selectedFilterType] = { from: undefined, to: undefined };
        break;
      case "membershipTypes":
      case "tags":
      case "locations":
        newFilters[selectedFilterType] = [];
        break;
      case "ageRange":
        newFilters.ageRange = { min: undefined, max: undefined };
        break;
      case "customFields":
        newFilters.customFields = [];
        break;
    }
    
    onChange(newFilters);
    setSelectedFilterType("");
  };

  const removeFilter = (filterKey: string) => {
    const newFilters = { ...filters };
    delete newFilters[filterKey as keyof RecipientFilters];
    onChange(newFilters);
  };

  const renderFilter = (filterKey: string) => {
    switch (filterKey) {
      case "membershipStatus":
        return (
          <MembershipStatusFilter
            value={filters.membershipStatus || []}
            onChange={(value) => onChange({ ...filters, membershipStatus: value })}
          />
        );
      case "expiryDateRange":
        return (
          <DateRangeFilter
            label="Expiry Date"
            value={filters.expiryDateRange}
            onChange={(value) => onChange({ ...filters, expiryDateRange: value })}
          />
        );
      case "registrationDateRange":
        return (
          <DateRangeFilter
            label="Registration Date"
            value={filters.registrationDateRange}
            onChange={(value) => onChange({ ...filters, registrationDateRange: value })}
          />
        );
      case "lastActivityDateRange":
        return (
          <DateRangeFilter
            label="Last Activity"
            value={filters.lastActivityDateRange}
            onChange={(value) => onChange({ ...filters, lastActivityDateRange: value })}
          />
        );
      case "membershipTypes":
        return (
          <MembershipTypeFilter
            value={filters.membershipTypes || []}
            onChange={(value) => onChange({ ...filters, membershipTypes: value })}
          />
        );
      case "customFields":
        return (
          <CustomFieldFilter
            value={filters.customFields || []}
            onChange={(value) => onChange({ ...filters, customFields: value })}
          />
        );
      default:
        return <div>Filter not implemented</div>;
    }
  };

  const availableFilters = Object.keys(filterLabels).filter(
    (key) => !filters[key as keyof RecipientFilters]
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Filter Criteria</CardTitle>
          <div className="flex items-center gap-2">
            {onPreview && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={onPreview}
                disabled={activeFilters.length === 0 || isLoadingPreview}
              >
                {isLoadingPreview ? "Loading..." : "Preview Recipients"}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Add Filter Control */}
          <div className="flex items-center gap-2">
            <Select
              value={selectedFilterType}
              onValueChange={(value) => setSelectedFilterType(value as FilterType)}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Add filter..." />
              </SelectTrigger>
              <SelectContent>
                {availableFilters.map((filterKey) => (
                  <SelectItem key={filterKey} value={filterKey}>
                    {filterLabels[filterKey as FilterType]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              size="sm" 
              onClick={addFilter}
              disabled={!selectedFilterType}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>

          {/* Active Filters */}
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {activeFilters.map(([filterKey]) => (
                <Card key={filterKey}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">
                        {filterLabels[filterKey as FilterType]}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFilter(filterKey)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {renderFilter(filterKey)}
                  </CardContent>
                </Card>
              ))}
              
              {activeFilters.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No filters applied. Add filters to define your recipient criteria.
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Filter Summary */}
          {activeFilters.length > 0 && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Active filters:</span>
                <div className="flex flex-wrap gap-1">
                  {activeFilters.map(([filterKey]) => (
                    <Badge key={filterKey} variant="secondary" className="text-xs">
                      {filterLabels[filterKey as FilterType]}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}