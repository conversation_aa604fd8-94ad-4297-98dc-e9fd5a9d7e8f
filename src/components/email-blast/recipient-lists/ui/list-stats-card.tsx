"use client";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  UserCheck,
  UserX,
  Mail,
  Calendar,
  Filter,
} from "lucide-react";

interface ListStatsCardProps {
  stats?: {
    totalRecipients?: number;
    activeRecipients?: number;
    inactiveRecipients?: number;
    lastUpdated?: string;
    filterCount?: number;
    estimatedEmailCount?: number;
  };
  isLoading?: boolean;
  className?: string;
}

export function ListStatsCard({
  stats = {},
  isLoading = false,
  className = "",
}: ListStatsCardProps) {
  const {
    totalRecipients = 0,
    activeRecipients = 0,
    inactiveRecipients = 0,
    lastUpdated = new Date().toISOString(),
    filterCount = 0,
    estimatedEmailCount = 0,
  } = stats;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>List Statistics</CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-32" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    } catch {
      return "N/A";
    }
  };

  const statsItems = [
    {
      label: "Total Recipients",
      value: totalRecipients.toLocaleString(),
      icon: Users,
      color: "text-blue-600",
    },
    {
      label: "Active",
      value: activeRecipients.toLocaleString(),
      icon: UserCheck,
      color: "text-green-600",
    },
    {
      label: "Inactive",
      value: inactiveRecipients.toLocaleString(),
      icon: UserX,
      color: "text-gray-500",
    },
    {
      label: "Est. Emails",
      value: estimatedEmailCount.toLocaleString(),
      icon: Mail,
      color: "text-purple-600",
    },
    {
      label: "Filters Applied",
      value: filterCount,
      icon: Filter,
      color: "text-orange-600",
    },
    {
      label: "Last Updated",
      value: formatDate(lastUpdated),
      icon: Calendar,
      color: "text-gray-600",
    },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>List Statistics</CardTitle>
        <CardDescription>
          Overview of recipients in this list
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {statsItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div
                key={index}
                className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50"
              >
                <Icon className={`h-5 w-5 mt-0.5 ${item.color}`} />
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    {item.label}
                  </p>
                  <p className="text-lg font-semibold">{item.value}</p>
                </div>
              </div>
            );
          })}
        </div>
        
        {activeRecipients > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                Active Rate
              </span>
              <Badge variant="success">
                {((activeRecipients / totalRecipients) * 100).toFixed(1)}%
              </Badge>
            </div>
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${(activeRecipients / totalRecipients) * 100}%`,
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}