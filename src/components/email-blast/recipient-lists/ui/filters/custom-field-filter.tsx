"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, X } from "lucide-react";
import { CustomFieldFilter as CustomFieldFilterType, FilterOperator } from "@/types/email-blast/recipient-list.types";

interface CustomFieldFilterProps {
  value: CustomFieldFilterType[];
  onChange: (value: CustomFieldFilterType[]) => void;
}

type FieldOperator = "equals" | "contains" | "starts_with" | "ends_with" | "greater_than" | "less_than";

interface CustomFieldCriteria {
  field: string;
  operator: FieldOperator;
  value: string;
}

// This would typically come from an API based on the organization's custom fields
const availableCustomFields = [
  { name: "department", label: "Department", type: "text" },
  { name: "employee_id", label: "Employee ID", type: "text" },
  { name: "join_year", label: "Join Year", type: "number" },
  { name: "interests", label: "Interests", type: "text" },
  { name: "referral_source", label: "Referral Source", type: "text" },
];

const operatorLabels: Record<FieldOperator, string> = {
  equals: "Equals",
  contains: "Contains",
  starts_with: "Starts with",
  ends_with: "Ends with",
  greater_than: "Greater than",
  less_than: "Less than",
};

export function CustomFieldFilter({ 
  value, 
  onChange 
}: CustomFieldFilterProps) {
  const [criteria, setCriteria] = useState<CustomFieldCriteria[]>(() => {
    // Convert existing value array to criteria format
    return value.map(filter => ({
      field: filter.fieldName,
      operator: (filter.operator as FieldOperator) || "equals",
      value: String(filter.value),
    }));
  });

  const addCriterion = () => {
    setCriteria([
      ...criteria,
      { field: availableCustomFields[0].name, operator: "equals", value: "" },
    ]);
  };

  const removeCriterion = (index: number) => {
    const newCriteria = criteria.filter((_, i) => i !== index);
    setCriteria(newCriteria);
    updateValue(newCriteria);
  };

  const updateCriterion = (
    index: number,
    updates: Partial<CustomFieldCriteria>
  ) => {
    const newCriteria = [...criteria];
    newCriteria[index] = { ...newCriteria[index], ...updates };
    setCriteria(newCriteria);
    updateValue(newCriteria);
  };

  const updateValue = (newCriteria: CustomFieldCriteria[]) => {
    const newValue: CustomFieldFilterType[] = newCriteria
      .filter(criterion => criterion.value) // Only include criteria with values
      .map(criterion => ({
        fieldName: criterion.field,
        operator: criterion.operator as FilterOperator,
        value: criterion.value,
      }));
    onChange(newValue);
  };

  return (
    <div className="space-y-3">
      {criteria.map((criterion, index) => (
        <div key={index} className="space-y-2 p-3 border rounded-lg">
          <div className="flex items-center justify-between">
            <Label className="text-sm">Custom Field {index + 1}</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeCriterion(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <Select
            value={criterion.field}
            onValueChange={(value) =>
              updateCriterion(index, { field: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select field" />
            </SelectTrigger>
            <SelectContent>
              {availableCustomFields.map((field) => (
                <SelectItem key={field.name} value={field.name}>
                  {field.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={criterion.operator}
            onValueChange={(value) =>
              updateCriterion(index, { operator: value as FieldOperator })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select operator" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(operatorLabels).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Input
            placeholder="Enter value"
            value={criterion.value}
            onChange={(e) =>
              updateCriterion(index, { value: e.target.value })
            }
          />
        </div>
      ))}

      <Button
        variant="outline"
        size="sm"
        onClick={addCriterion}
        className="w-full"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add Custom Field
      </Button>

      {criteria.length === 0 && (
        <p className="text-sm text-muted-foreground text-center py-4">
          No custom field filters applied
        </p>
      )}
    </div>
  );
}