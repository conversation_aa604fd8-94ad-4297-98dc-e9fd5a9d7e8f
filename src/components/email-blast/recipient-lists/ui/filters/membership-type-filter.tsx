"use client";

import { useState, useEffect } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search } from "lucide-react";

interface MembershipTypeFilterProps {
  value: string[];
  onChange: (value: string[]) => void;
}

// This would typically come from an API
const mockMembershipTypes = [
  "Gold Membership",
  "Silver Membership",
  "Bronze Membership",
  "Premium Membership",
  "Basic Membership",
  "Student Membership",
  "Senior Membership",
  "Corporate Membership",
  "Family Membership",
  "Lifetime Membership",
];

export function MembershipTypeFilter({ 
  value, 
  onChange 
}: MembershipTypeFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [membershipTypes, setMembershipTypes] = useState<string[]>([]);

  useEffect(() => {
    // In a real app, this would fetch from an API
    setMembershipTypes(mockMembershipTypes);
  }, []);

  const filteredTypes = membershipTypes.filter((type) =>
    type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggle = (type: string) => {
    const newValue = value.includes(type)
      ? value.filter((t) => t !== type)
      : [...value, type];
    onChange(newValue);
  };

  const handleSelectAll = () => {
    if (value.length === filteredTypes.length) {
      onChange([]);
    } else {
      onChange(filteredTypes);
    }
  };

  return (
    <div className="space-y-3">
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search membership types..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-8"
        />
      </div>

      <div className="flex items-center space-x-2 pb-2 border-b">
        <Checkbox
          id="select-all"
          checked={
            filteredTypes.length > 0 && 
            filteredTypes.every((type) => value.includes(type))
          }
          onCheckedChange={handleSelectAll}
        />
        <Label
          htmlFor="select-all"
          className="text-sm font-medium cursor-pointer"
        >
          Select All ({filteredTypes.length})
        </Label>
      </div>

      <ScrollArea className="h-[200px]">
        <div className="space-y-2">
          {filteredTypes.map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={`type-${type}`}
                checked={value.includes(type)}
                onCheckedChange={() => handleToggle(type)}
              />
              <Label
                htmlFor={`type-${type}`}
                className="text-sm cursor-pointer"
              >
                {type}
              </Label>
            </div>
          ))}
          
          {filteredTypes.length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-4">
              No membership types found
            </p>
          )}
        </div>
      </ScrollArea>

      {value.length > 0 && (
        <div className="text-sm text-muted-foreground">
          {value.length} membership type{value.length !== 1 ? 's' : ''} selected
        </div>
      )}
    </div>
  );
}