"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { MembershipStatus } from "@/types/email-blast/recipient-list.types";
import { Badge } from "@/components/ui/badge";

interface MembershipStatusFilterProps {
  value: MembershipStatus[];
  onChange: (value: MembershipStatus[]) => void;
}

const statusOptions = [
  { 
    value: MembershipStatus.ACTIVE, 
    label: "Active",
    color: "default" as const
  },
  { 
    value: MembershipStatus.EXPIRED, 
    label: "Expired",
    color: "destructive" as const
  },
  { 
    value: MembershipStatus.PENDING, 
    label: "Pending",
    color: "secondary" as const
  },
  { 
    value: MembershipStatus.SUSPENDED, 
    label: "Suspended",
    color: "outline" as const
  },
];

export function MembershipStatusFilter({ 
  value, 
  onChange 
}: MembershipStatusFilterProps) {
  const handleToggle = (status: MembershipStatus) => {
    const newValue = value.includes(status)
      ? value.filter((s) => s !== status)
      : [...value, status];
    onChange(newValue);
  };

  return (
    <div className="space-y-3">
      {statusOptions.map((option) => (
        <div key={option.value} className="flex items-center space-x-2">
          <Checkbox
            id={`status-${option.value}`}
            checked={value.includes(option.value)}
            onCheckedChange={() => handleToggle(option.value)}
          />
          <Label
            htmlFor={`status-${option.value}`}
            className="flex items-center gap-2 cursor-pointer"
          >
            <Badge variant={option.color}>
              {option.label}
            </Badge>
          </Label>
        </div>
      ))}
      
      {value.length === 0 && (
        <p className="text-sm text-muted-foreground">
          Select at least one membership status
        </p>
      )}
    </div>
  );
}