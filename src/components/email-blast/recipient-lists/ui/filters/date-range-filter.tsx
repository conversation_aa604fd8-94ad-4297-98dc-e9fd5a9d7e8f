"use client";

import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";

interface DateRangeFilterProps {
  label: string;
  value?: {
    from?: Date;
    to?: Date;
  };
  onChange: (value: { from?: Date; to?: Date }) => void;
}

export function DateRangeFilter({ 
  label, 
  value, 
  onChange 
}: DateRangeFilterProps) {
  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label>From Date</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full justify-start text-left font-normal",
                !value?.from && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {value?.from ? format(value.from, "PPP") : "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={value?.from}
              onSelect={(date) => 
                onChange({ ...value, from: date || undefined })
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="space-y-2">
        <Label>To Date</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full justify-start text-left font-normal",
                !value?.to && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {value?.to ? format(value.to, "PPP") : "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={value?.to}
              onSelect={(date) => 
                onChange({ ...value, to: date || undefined })
              }
              disabled={(date) => 
                value?.from ? date < value.from : false
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {value?.from && value?.to && value.from > value.to && (
        <p className="text-sm text-destructive">
          End date must be after start date
        </p>
      )}
    </div>
  );
}