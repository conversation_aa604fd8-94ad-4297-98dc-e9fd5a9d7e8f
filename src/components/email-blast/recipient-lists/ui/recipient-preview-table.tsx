"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { User } from "lucide-react";

interface RecipientPreviewTableProps {
  recipients?: any[]; // Will be typed properly when types are ready
  isLoading?: boolean;
  maxHeight?: string;
}

export function RecipientPreviewTable({
  recipients = [],
  isLoading = false,
  maxHeight = "400px",
}: RecipientPreviewTableProps) {
  if (isLoading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead className="w-[250px]">Email</TableHead>
              <TableHead>Membership Type</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(5)].map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-4 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-40" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-16" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (recipients.length === 0) {
    return (
      <div className="rounded-md border p-8">
        <div className="flex flex-col items-center justify-center text-center">
          <User className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-sm text-muted-foreground">
            No recipients match the selected criteria
          </p>
        </div>
      </div>
    );
  }

  return (
    <ScrollArea className="rounded-md border" style={{ maxHeight }}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Name</TableHead>
            <TableHead className="w-[250px]">Email</TableHead>
            <TableHead>Membership Type</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recipients.map((recipient, index) => (
            <TableRow key={recipient.id || index}>
              <TableCell className="font-medium">
                {recipient.name || "N/A"}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {recipient.email}
              </TableCell>
              <TableCell>
                {recipient.membershipType || "N/A"}
              </TableCell>
              <TableCell>
                <Badge
                  variant={
                    recipient.status === "active" ? "success" : "secondary"
                  }
                  className="capitalize"
                >
                  {recipient.status || "unknown"}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </ScrollArea>
  );
}