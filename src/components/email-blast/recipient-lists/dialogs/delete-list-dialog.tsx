"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON>riangle, Trash2, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface DeleteListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  listName: string;
  recipientCount?: number;
  usageCount?: number;
  isLoading?: boolean;
}

export function DeleteListDialog({
  open,
  onOpenChange,
  onConfirm,
  listName,
  recipientCount = 0,
  usageCount = 0,
  isLoading = false,
}: DeleteListDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to delete list:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    if (!isDeleting) {
      onOpenChange(false);
    }
  };

  const isInUse = usageCount > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Delete Recipient List
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the recipient list{" "}
            <strong>{listName}</strong>?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* List Statistics */}
          <div className="rounded-lg border p-4 space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground flex items-center gap-2">
                <Users className="h-4 w-4" />
                Recipients
              </span>
              <Badge variant="secondary">{recipientCount.toLocaleString()}</Badge>
            </div>
            
            {isInUse && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Used in campaigns</span>
                <Badge variant="destructive">{usageCount}</Badge>
              </div>
            )}
          </div>

          {/* Warning Alert */}
          <Alert className={isInUse ? "border-destructive" : "border-yellow-200 bg-yellow-50"}>
            <AlertTriangle className={`h-4 w-4 ${isInUse ? "text-destructive" : "text-yellow-600"}`} />
            <AlertDescription className={isInUse ? "text-destructive" : "text-yellow-800"}>
              {isInUse ? (
                <>
                  <strong>Warning:</strong> This list is currently being used in {usageCount} active 
                  campaign{usageCount > 1 ? "s" : ""}. Deleting it may affect ongoing email blasts.
                </>
              ) : (
                <>
                  This action cannot be undone. All filters and settings associated with this list 
                  will be permanently deleted.
                </>
              )}
            </AlertDescription>
          </Alert>

          {/* Additional Information */}
          <div className="text-sm text-muted-foreground space-y-1">
            <p>After deletion:</p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>The list will be removed from all saved templates</li>
              <li>Historical campaign data will be preserved</li>
              <li>You can recreate the list with the same filters if needed</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isDeleting || isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting || isLoading}
            className="gap-2"
          >
            {isDeleting ? (
              <>Deleting...</>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete List
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}