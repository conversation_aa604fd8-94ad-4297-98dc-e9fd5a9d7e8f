"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Copy, Info, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { SavedFilterGroup } from "../ui/saved-filter-badge";

interface DuplicateListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (name: string, description: string) => void | Promise<void>;
  originalList: {
    name: string;
    description?: string;
    recipientCount?: number;
    filters?: any[];
  };
  isLoading?: boolean;
}

export function DuplicateListDialog({
  open,
  onOpenChange,
  onConfirm,
  originalList,
  isLoading = false,
}: DuplicateListDialogProps) {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isDuplicating, setIsDuplicating] = useState(false);
  const [error, setError] = useState("");

  // Initialize form when dialog opens
  useEffect(() => {
    if (open) {
      setName(`${originalList.name} (Copy)`);
      setDescription(originalList.description || "");
      setError("");
    }
  }, [open, originalList]);

  const handleConfirm = async () => {
    // Validation
    if (!name.trim()) {
      setError("List name is required");
      return;
    }

    if (name.trim() === originalList.name) {
      setError("Duplicate list must have a different name");
      return;
    }

    setIsDuplicating(true);
    setError("");
    
    try {
      await onConfirm(name.trim(), description.trim());
      onOpenChange(false);
      // Reset form
      setName("");
      setDescription("");
    } catch (error) {
      console.error("Failed to duplicate list:", error);
      setError("Failed to duplicate list. Please try again.");
    } finally {
      setIsDuplicating(false);
    }
  };

  const handleCancel = () => {
    if (!isDuplicating) {
      onOpenChange(false);
      // Reset form
      setName("");
      setDescription("");
      setError("");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Copy className="h-5 w-5 text-primary" />
            Duplicate Recipient List
          </DialogTitle>
          <DialogDescription>
            Create a copy of <strong>{originalList.name}</strong> with all its filters and settings.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Original List Info */}
          <div className="rounded-lg border p-4 space-y-3 bg-muted/50">
            <div className="text-sm space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Original List</span>
                <span className="font-medium">{originalList.name}</span>
              </div>
              {originalList.recipientCount !== undefined && (
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Recipients
                  </span>
                  <Badge variant="secondary">
                    {originalList.recipientCount.toLocaleString()}
                  </Badge>
                </div>
              )}
            </div>
            
            {originalList.filters && originalList.filters.length > 0 && (
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground mb-2">Applied Filters:</p>
                <SavedFilterGroup
                  filters={originalList.filters}
                  maxVisible={3}
                  className="gap-1"
                />
              </div>
            )}
          </div>

          {/* New List Details */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="list-name">
                New List Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="list-name"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  setError("");
                }}
                placeholder="Enter a unique name for the duplicate list"
                disabled={isDuplicating || isLoading}
                className={error && !name.trim() ? "border-destructive" : ""}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="list-description">Description</Label>
              <Textarea
                id="list-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Add an optional description for this list"
                rows={3}
                disabled={isDuplicating || isLoading}
                className="resize-none"
              />
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Info Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription className="text-sm">
              The duplicate will include all filters and criteria from the original list. 
              Recipients will be recalculated based on current member data.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isDuplicating || isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isDuplicating || isLoading || !name.trim()}
            className="gap-2"
          >
            {isDuplicating ? (
              <>Creating Duplicate...</>
            ) : (
              <>
                <Copy className="h-4 w-4" />
                Create Duplicate
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}