"use client";

import { User } from "lucide-react";
import { cn } from "@/lib/utils";

interface UserDisplayInfo {
  id: string;
  name: string;
  email: string;
}

interface UserDisplayProps {
  user: UserDisplayInfo | null | undefined;
  fallbackText?: string;
  showEmail?: boolean;
  showIcon?: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function UserDisplay({ 
  user, 
  fallbackText = "User",
  showEmail = false,
  showIcon = false,
  className,
  size = "md"
}: UserDisplayProps) {
  const sizeClasses = {
    sm: "text-sm",
    md: "text-base", 
    lg: "text-lg"
  };

  const iconSizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  };

  // Determine display text with priority: name > email > fallback
  const displayText = user?.name || user?.email || fallbackText;
  
  // Create tooltip text with full user info
  const tooltipText = user ? 
    `${user.name || 'User'}${user.email ? ` (${user.email})` : ''}` : 
    undefined;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {showIcon && (
        <User className={cn("text-muted-foreground", iconSizeClasses[size])} />
      )}
      <div className="flex flex-col">
        <span 
          className={cn("font-medium", sizeClasses[size])}
          title={tooltipText}
        >
          {displayText}
        </span>
        {showEmail && user?.email && user.name && (
          <span className="text-xs text-muted-foreground">
            {user.email}
          </span>
        )}
      </div>
    </div>
  );
}