import { Circle } from 'lucide-react'
import { cn } from "@/lib/utils"
import { Button } from './button'

type StatusVariant = 'active' | 'inactive' | 'pending' | 'suspended' | 'expired' | 'terminated' | 'pending_renewal' | 'pending_re_application'

interface StatusBadgeProps {
  variant: StatusVariant
  className?: string
}

const statusConfig: Record<StatusVariant, { color: string; label: string }> = {
  active: {
    color: "fill-green-500 text-green-500",
    label: "Active"
  },
  inactive: {
    color: "fill-yellow-500 text-yellow-500",
    label: "Inactive"
  },
  pending: {
    color: "fill-blue-500 text-blue-500",
    label: "Pending"
  },
  suspended: {
    color: "fill-red-500 text-red-500",
    label: "Suspended"
  },
  expired: {
    color: "fill-gray-500 text-gray-500",
    label: "Expired"
  },
  terminated: {
    color: "fill-red-700 text-red-700",
    label: "Terminated"
  },
  pending_renewal: {
    color: "fill-amber-500 text-amber-500",
    label: "Pending Renewal"
  },
  pending_re_application: {
    color: "fill-purple-500 text-purple-500",
    label: "Pending Re-Application"
  }
}

export function StatusBadge({ variant, className }: StatusBadgeProps) {
  const config = statusConfig[variant]

  return (
    <Button
      variant="outline"
      className={cn(
        "flex items-center gap-2 px-3 py-1 text-sm font-medium border-gray-300",
        className
      )}
    >
      <Circle className={cn("h-2 w-2", config.color)} />
      {config.label}
    </Button>
  )
} 