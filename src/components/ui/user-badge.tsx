"use client";

import { Badge } from "@/components/ui/badge";
import { User } from "lucide-react";
import { cn } from "@/lib/utils";

interface UserDisplayInfo {
  id: string;
  name: string;
  email: string;
}

interface UserBadgeProps {
  user: UserDisplayInfo | null | undefined;
  fallbackText?: string;
  variant?: "default" | "secondary" | "outline" | "destructive";
  showIcon?: boolean;
  className?: string;
  maxLength?: number;
}

export function UserBadge({ 
  user, 
  fallbackText = "User",
  variant = "secondary",
  showIcon = false,
  className,
  maxLength = 20
}: UserBadgeProps) {
  // Determine display text with priority: name > email > fallback
  let displayText = user?.name || user?.email || fallbackText;
  
  // Truncate if too long
  if (displayText.length > maxLength) {
    displayText = displayText.substring(0, maxLength - 3) + "...";
  }
  
  // Create tooltip text with full user info
  const tooltipText = user ? 
    `${user.name || 'User'}${user.email ? ` (${user.email})` : ''}` : 
    undefined;

  return (
    <Badge 
      variant={variant} 
      className={cn("flex items-center gap-1", className)}
      title={tooltipText}
    >
      {showIcon && <User className="h-3 w-3" />}
      <span className="truncate">{displayText}</span>
    </Badge>
  );
}