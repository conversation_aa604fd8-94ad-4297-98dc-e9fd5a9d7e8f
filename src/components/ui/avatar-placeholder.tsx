import { User } from "lucide-react"

interface AvatarPlaceholderProps {
  size?: 'sm' | 'md' | 'lg'
}

export function AvatarPlaceholder({ size = 'md' }: AvatarPlaceholderProps) {
  const sizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-20 w-20'
  }

  return (
    <div className={`${sizeClasses[size]} flex-shrink-0 rounded-full bg-muted flex items-center justify-center`}>
      <User className="h-1/2 w-1/2 text-muted-foreground" />
    </div>
  )
} 