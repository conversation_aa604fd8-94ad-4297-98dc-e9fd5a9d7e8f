"use client"

import { forwardRef, useImperativeHandle } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/events/tabs";
import { useCourseEventBudgetTable } from "@/hooks/budgets/edit-event-budget";
import type { BudgetChildRef } from "@/types/budgets/budgets.types";

type CourseEventBudgetTableProps = {
  budgetRevisionId: string;
  submitForReview: () => void;
};

const CourseEventBudgetTable = forwardRef<BudgetChildRef, CourseEventBudgetTableProps>(({ budgetRevisionId, submitForReview }, ref) => {
  const {
    incomeEntries,
    expenditureEntries,
    totalIncome,
    totalExpenditure,
    netSurplusDeficit,
    percentageSurplusDeficit,
    budgetData,
    isSubmitting,
    setTabValue,
    setIncomeEntries,
    setExpenditureEntries,
    addIncomeEntry,
    removeIncomeEntry,
    addExpenditureEntry,
    removeExpenditureEntry,
    calculateIncomeTotal,
    calculateExpenditureTotal,
    handleSubmit,
  } = useCourseEventBudgetTable(budgetRevisionId, submitForReview)

  // Expose handleSubmit to the parent via ref
  useImperativeHandle(ref, () => ({
    itemsubmit: handleSubmit
  }))

  return (
    <>
      <Tabs
        defaultValue="income"
        className="w-full"
        onValueChange={(value) => setTabValue(value)}
      >
        <TabsList>
          <TabsTrigger value="income">Income</TabsTrigger>
          <TabsTrigger value="expenditure">Expenditure</TabsTrigger>
        </TabsList>

        <TabsContent value="income">
          <Table className="mt-6">
            <TableCaption>Proposed income details.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Proposed Rate</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Sub Total</TableHead>
                <TableHead>Remarks</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {incomeEntries.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Select
                      value={entry.type}
                      onValueChange={(value) => {
                        const newEntries = [...incomeEntries];
                        newEntries[index].type = value;

                        // Retain existing values for proposedRate and quantity, no database dependency
                        const rate = Number(newEntries[index].proposedRate) || 0;
                        const quantity = Number(newEntries[index].quantity) || 0;

                        // Calculate subtotal dynamically
                        newEntries[index].subTotal = (rate * quantity).toFixed(2);

                        setIncomeEntries(newEntries);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Item" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {budgetData.budgetItems
                            .filter((item) => item.name !== undefined)
                            .map((item) => (
                              <SelectItem
                                key={item.id}
                                value={item.id}
                              >
                                {item.name}
                              </SelectItem>
                            ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      placeholder="Enter Rate"
                      value={entry.proposedRate}
                      onChange={(e) => {
                        const newEntries = [...incomeEntries];
                        newEntries[index].proposedRate = e.target.value;

                        const rate = Number(e.target.value) || 0;
                        const quantity = Number(newEntries[index].quantity) || 0;

                        newEntries[index].subTotal = (rate * quantity).toFixed(2); // Update subtotal
                        setIncomeEntries(newEntries);
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      placeholder="Enter Quantity"
                      value={entry.quantity}
                      onChange={(e) => {
                        const newEntries = [...incomeEntries];
                        newEntries[index].quantity = e.target.value;

                        const rate = Number(newEntries[index].proposedRate) || 0;
                        const quantity = Number(e.target.value) || 0;

                        newEntries[index].subTotal = (rate * quantity).toFixed(2); // Update subtotal
                        setIncomeEntries(newEntries);
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="text"
                      placeholder="Sub Total"
                      value={entry.subTotal}
                      readOnly
                    />
                  </TableCell>
                  <TableCell>
                    <Textarea
                      placeholder="Enter remarks here..."
                      value={entry.remarks}
                      onChange={(e) => {
                        const newEntries = [...incomeEntries];
                        newEntries[index].remarks = e.target.value;
                        setIncomeEntries(newEntries);
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="destructive"
                      type="button"
                      onClick={() => removeIncomeEntry(index, entry.id)}
                    >
                      Remove Item
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              <TableRow>
                <TableCell colSpan={3}>Total Budget</TableCell>
                <TableCell>
                  <Input
                    type="text"
                    value={calculateIncomeTotal().toFixed(2)}
                    readOnly
                  />
                </TableCell>
                <TableCell colSpan={2}></TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <Button
            type="button"
            onClick={addIncomeEntry}
          // disabled={allIncomeItemsSelected()}
          >
            Add Item
          </Button>
        </TabsContent>

        <TabsContent value="expenditure">
          <Table className="mt-6">
            <TableCaption>Proposed expenditure details.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Proposed Rate</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Sub Total</TableHead>
                <TableHead>Remarks</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {expenditureEntries.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Select
                      value={entry.type}
                      onValueChange={(value) => {
                        const newEntries = [...expenditureEntries];
                        newEntries[index].type = value;

                        // Retain existing values for proposedRate and quantity, no database dependency
                        const rate = Number(newEntries[index].proposedRate) || 0;
                        const quantity = Number(newEntries[index].quantity) || 0;

                        // Calculate subtotal dynamically
                        newEntries[index].subTotal = (rate * quantity).toFixed(2);

                        setExpenditureEntries(newEntries);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Item" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {budgetData.budgetItems
                            .filter((item) => item.name !== undefined)
                            .map((item) => (
                              <SelectItem
                                key={item.id}
                                value={item.id}
                              >
                                {item.name}
                              </SelectItem>
                            ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      placeholder="Enter Rate"
                      value={entry.proposedRate}
                      onChange={(e) => {
                        const newEntries = [...expenditureEntries];
                        newEntries[index].proposedRate = e.target.value;

                        const rate = Number(e.target.value) || 0;
                        const quantity = Number(newEntries[index].quantity) || 0;

                        newEntries[index].subTotal = (rate * quantity).toFixed(2); // Update subtotal
                        setExpenditureEntries(newEntries);
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      placeholder="Enter Quantity"
                      value={entry.quantity}
                      onChange={(e) => {
                        const newEntries = [...expenditureEntries];
                        newEntries[index].quantity = e.target.value;

                        const rate = Number(newEntries[index].proposedRate) || 0;
                        const quantity = Number(e.target.value) || 0;

                        newEntries[index].subTotal = (rate * quantity).toFixed(2); // Update subtotal
                        setExpenditureEntries(newEntries);
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="text"
                      placeholder="Sub Total"
                      value={entry.subTotal}
                      readOnly
                    />
                  </TableCell>
                  <TableCell>
                    <Textarea
                      placeholder="Enter remarks here..."
                      value={entry.remarks}
                      onChange={(e) => {
                        const newEntries = [...expenditureEntries];
                        newEntries[index].remarks = e.target.value;
                        setExpenditureEntries(newEntries);
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="destructive"
                      type="button"
                      onClick={() => removeExpenditureEntry(index, entry.id)}
                    >
                      Remove Item
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              <TableRow>
                <TableCell colSpan={3}>Total Budget</TableCell>
                <TableCell>
                  <Input
                    type="text"
                    value={calculateExpenditureTotal().toFixed(2)}
                    readOnly
                  />
                </TableCell>
                <TableCell colSpan={2}></TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <Button
            type="button"
            onClick={addExpenditureEntry}
          // disabled={allExpenditureItemsSelected()}
          >
            Add Item
          </Button>
        </TabsContent>
      </Tabs >

      <Table className="mt-6">
        <TableBody>
          <TableRow>
            <TableCell colSpan={3}>Total Income (Excl GST)</TableCell>
            <TableCell>
              <Input
                type="text"
                value={totalIncome}
                readOnly
              />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Total Expenditure (Excl GST)</TableCell>
            <TableCell>
              <Input
                type="text"
                value={totalExpenditure}
                readOnly
              />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
          {/* Net Surplus/Deficit Row */}
          <TableRow>
            <TableCell colSpan={3}>Net Surplus / Deficit (A - B)</TableCell>
            <TableCell>
              <Input
                type="text"
                value={netSurplusDeficit}
                readOnly
              />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
          {/* Percentage of Net Surplus/Deficit Row */}
          <TableRow>
            <TableCell colSpan={3}>
              % Of Net Surplus / Deficit Over Total Income
            </TableCell>
            <TableCell>
              <Input
                type="text"
                value={`${percentageSurplusDeficit.toFixed(2)}%`}
                readOnly
              />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <div className="mt-4 flex justify-end">
        <Button
          onClick={() => handleSubmit()}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Save"}
        </Button>
      </div>
    </>
  );
});

export default CourseEventBudgetTable;
