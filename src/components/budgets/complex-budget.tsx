"use client"

import { forwardRef, useImperativeHandle } from "react";
import {
  Table,
  TableCaption,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useComplexBudgetTable } from "@/hooks/budgets/edit-conference-budget";
import type { BudgetChildRef } from "@/types/budgets/budgets.types";

type ComplexBudgetTableProps = {
  budgetRevisionId: string;
  submitForReview: () => void;
};

const ComplexBudgetTable = forwardRef<BudgetChildRef, ComplexBudgetTableProps>(({ budgetRevisionId, submitForReview }, ref) => {
  const {
    incomeSections,
    expenditureSections,
    budgetData,
    totalIncome,
    totalExpenditure,
    netSurplusDeficit,
    percentageSurplusDeficit,
    isSubmitting,
    setTabValue,
    addSection,
    isSectionSelected,
    removeSection,
    addExpenditureSection,
    isExpenditureSectionSelected,
    addTypeToSection,
    removeExpenditureSection,
    addExpenditureTypeToSection,
    handleInputChange,
    handleExpenditureInputChange,
    removeIncomeTypeFromSection,
    removeExpenditureTypeFromSection,
    calculateSectionTotal,
    calculateExpenditureSectionTotal,
    handleSubmit,
  } = useComplexBudgetTable(budgetRevisionId, submitForReview);

  // Expose handleSubmit to the parent via ref
  useImperativeHandle(ref, () => ({
    itemsubmit: handleSubmit
  }));

  return (
    <>
      <Tabs
        defaultValue="income"
        className="w-full"
        onValueChange={(value) => setTabValue(value)}
      >
        <TabsList>
          <TabsTrigger value="income">Income</TabsTrigger>
          <TabsTrigger value="expenditure">Expenditure</TabsTrigger>
        </TabsList>

        <TabsContent value="income">
          <div className="flex items-center gap-2 mb-4 mt-4" style={{ width: '500px' }}>
            <Select onValueChange={addSection} value={""}>
              <SelectTrigger>
                <SelectValue placeholder="Select Income Section" />
              </SelectTrigger>
              <SelectContent>
                {budgetData.budgetSections
                  .filter((section) => section.name !== undefined)
                  .map((section) => (
                    <SelectItem key={section.id} value={section.id} disabled={isSectionSelected(section.id)}>
                      {section.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {incomeSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mt-6">
              <div className="rounded-md bg-gray-100 p-2 flex justify-between items-center mb-2">
                <h2 className="text-md text-gray-500 font-medium">
                  {section.sectionName}
                </h2>
                <Button
                  variant="destructive"
                  type="button"
                  onClick={() => removeSection(sectionIndex)}
                >
                  Remove Section
                </Button>
              </div>

              <Table className="mt-6">
                <TableCaption>
                  Proposed details for {section.sectionName}.
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Proposed Rate</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Sub Total</TableHead>
                    <TableHead>Remarks</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {section.types.map((type, typeIndex) => (
                    <TableRow key={typeIndex}>
                      <TableCell>
                        <Select
                          value={type.type}
                          onValueChange={(value) =>
                            handleInputChange(
                              sectionIndex,
                              typeIndex,
                              "type",
                              value
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select Item" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetData.budgetItems
                              .filter((item) => item.name !== undefined && item.section_id === section.sectionId)
                              .map((item) => (
                                <SelectItem
                                  key={item.id}
                                  value={item.id}
                                >
                                  {item.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          placeholder="Enter Rate"
                          value={type.proposedRate}
                          onChange={(e) =>
                            handleInputChange(
                              sectionIndex,
                              typeIndex,
                              "proposedRate",
                              e.target.value
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          placeholder="Enter Quantity"
                          value={type.quantity}
                          onChange={(e) =>
                            handleInputChange(
                              sectionIndex,
                              typeIndex,
                              "quantity",
                              e.target.value
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Input type="text" value={type.subTotal} readOnly />
                      </TableCell>
                      <TableCell>
                        <Textarea
                          placeholder="Enter remarks here..."
                          value={type.remarks}
                          onChange={(e) =>
                            handleInputChange(
                              sectionIndex,
                              typeIndex,
                              "remarks",
                              e.target.value
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="destructive"
                          type="button"
                          onClick={() =>
                            removeIncomeTypeFromSection(sectionIndex, typeIndex, type.id)
                          }
                        >
                          Remove Item
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}

                  <TableRow>
                    <TableCell colSpan={3}>Total Budget</TableCell>
                    <TableCell>
                      <Input
                        type="text"
                        value={calculateSectionTotal(section).toFixed(2)}
                        readOnly
                      />
                    </TableCell>
                    <TableCell colSpan={2}></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              <Button
                type="button"
                onClick={() => addTypeToSection(sectionIndex)}
              >
                Add Item
              </Button>
            </div>
          ))}
        </TabsContent>

        <TabsContent value="expenditure">
          <div className="flex items-center gap-2 mb-4 mt-4" style={{ width: '500px' }}>
            <Select onValueChange={addExpenditureSection} value={""}>
              <SelectTrigger>
                <SelectValue placeholder="Select Expenditure Section" />
              </SelectTrigger>
              <SelectContent>
                {budgetData.budgetSections
                  .filter((section) => section.name !== undefined)
                  .map((section) => (
                    <SelectItem key={section.id} value={section.id} disabled={isExpenditureSectionSelected(section.id)}>
                      {section.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {expenditureSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mt-6">
              <div className="rounded-md bg-gray-100 p-2 flex justify-between items-center mb-2">
                <h2 className="text-md text-gray-500 font-medium">
                  {section.sectionName}
                </h2>
                <Button
                  variant="destructive"
                  type="button"
                  onClick={() => removeExpenditureSection(sectionIndex)}
                >
                  Remove Section
                </Button>
              </div>

              <Table className="mt-6">
                <TableCaption>
                  Proposed details for {section.sectionName}.
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Proposed Rate</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Sub Total</TableHead>
                    <TableHead>Remarks</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {section.types.map((type, typeIndex) => (
                    <TableRow key={typeIndex}>
                      <TableCell>
                        <Select
                          value={type.type}
                          onValueChange={(value) =>
                            handleExpenditureInputChange(
                              sectionIndex,
                              typeIndex,
                              "type",
                              value
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select Item" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetData.budgetItems
                              .filter((item) => item.name !== undefined && item.section_id === section.sectionId)
                              .map((item) => (
                                <SelectItem
                                  key={item.id}
                                  value={item.id}
                                >
                                  {item.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          placeholder="Enter Rate"
                          value={type.proposedRate}
                          onChange={(e) =>
                            handleExpenditureInputChange(
                              sectionIndex,
                              typeIndex,
                              "proposedRate",
                              e.target.value
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          placeholder="Enter Quantity"
                          value={type.quantity}
                          onChange={(e) =>
                            handleExpenditureInputChange(
                              sectionIndex,
                              typeIndex,
                              "quantity",
                              e.target.value
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="text"
                          placeholder="Sub Total"
                          value={type.subTotal}
                          readOnly
                        />
                      </TableCell>
                      <TableCell>
                        <Textarea
                          placeholder="Enter remarks here..."
                          value={type.remarks}
                          onChange={(e) =>
                            handleExpenditureInputChange(
                              sectionIndex,
                              typeIndex,
                              "remarks",
                              e.target.value
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="destructive"
                          type="button"
                          onClick={() =>
                            removeExpenditureTypeFromSection(sectionIndex, typeIndex, type.id)
                          }
                        >
                          Remove Item
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}

                  {/* Total Budget Row */}
                  <TableRow>
                    <TableCell colSpan={3}>Total Budget</TableCell>
                    <TableCell>
                      <Input
                        type="text"
                        value={calculateExpenditureSectionTotal(
                          section
                        ).toFixed(2)}
                        readOnly
                      />
                    </TableCell>
                    <TableCell colSpan={2}></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              {/* Add Item Button Row */}
              <Button
                type="button"
                onClick={() => addExpenditureTypeToSection(sectionIndex)}
              >
                Add Item
              </Button>
            </div>
          ))}
        </TabsContent>
      </Tabs>

      <Table className="mt-6">
        <TableBody>
          <TableRow>
            <TableCell colSpan={3}>Total Income (Excl GST)</TableCell>
            <TableCell>
              <Input type="text" value={totalIncome} readOnly />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Total Expenditure (Excl GST)</TableCell>
            <TableCell>
              <Input type="text" value={totalExpenditure} readOnly />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Net Surplus / Deficit (A - B)</TableCell>
            <TableCell>
              <Input type="text" value={netSurplusDeficit} readOnly />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>
              % Of Net Surplus / Deficit Over Total Income
            </TableCell>
            <TableCell>
              <Input
                type="text"
                value={`${percentageSurplusDeficit.toFixed(2)}%`}
                readOnly
              />
            </TableCell>
            <TableCell colSpan={2}></TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <div className="mt-4 flex justify-end">
        <Button
          onClick={() => handleSubmit()}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Save"}
        </Button>
      </div>
    </>
  );
});

export default ComplexBudgetTable;
