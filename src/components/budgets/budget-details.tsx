"use client"

import { forwardRef, useImperativeHandle } from "react";
import { FormProvider } from "react-hook-form";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { BudgetChildRef, BudgetRevisions } from "@/types/budgets/budgets.types";
import { useBudgetDetailForm } from "@/hooks/budgets/use-budget-form";

type BudgetDetailFormProps = {
    createState: boolean;
    submitForReview?: () => void;
    budgetRevisionId?: string;
    budgetDetails?: BudgetRevisions;
};

const BudgetDetailForm = forwardRef<BudgetChildRef, BudgetDetailFormProps>(({ createState, submitForReview, budgetRevisionId, budgetDetails }, ref) => {
    const {
        budgetType,
        budgetFormat,
        form,
        handleParentCall,
    } = useBudgetDetailForm(createState, submitForReview, budgetRevisionId, budgetDetails)

    // Expose onSubmit to the parent via ref
    useImperativeHandle(ref, () => ({
        detailsubmit: handleParentCall
    }));

    return (
        <>
            <FormProvider {...form}>
                <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                            control={form.control}
                            name="budgetType"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Budget Type</FormLabel>
                                    <FormControl>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value}
                                            disabled={!createState}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select Type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {budgetType.map((type) => (
                                                    <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="budgetFormat"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Budget Format</FormLabel>
                                    <FormControl>
                                        <Select
                                            onValueChange={field.onChange}
                                            value={field.value}
                                            disabled={!createState}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select Format" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {budgetFormat.map((format) => (
                                                    <SelectItem key={format.id} value={format.value}>{format.label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="col-span-full">
                            <FormField
                                control={form.control}
                                name="title"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Title</FormLabel>
                                        <FormControl>
                                            <Input
                                                type="text"
                                                placeholder="Enter Title"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="col-span-full">
                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="Enter description"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>

                    {createState === false && (
                        <div className="flex justify-end">
                            <Button type="button" onClick={() => handleParentCall()}>Save</Button>
                        </div>
                    )}

                </form>
            </FormProvider>
        </>
    );
});

export default BudgetDetailForm;