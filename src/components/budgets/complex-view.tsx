"use client"

import {
  Table,
  TableCaption,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { BudgetComplexDataEntries, BudgetReviewComplexProps } from "@/types/budgets/budgets.types";

export const ComplexBudgetView: React.FC<BudgetReviewComplexProps> = ({
  incomeEntries,
  expenditureEntries,
  summary
}) => {
  const calculateSectionTotal = (section: BudgetComplexDataEntries): number =>
    section.types.reduce((total, type) => total + (type.subTotal || 0), 0);

  const toTitleCase = (str: string): string => {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <>
      <Tabs defaultValue="income" className="w-full">
        <TabsList>
          <TabsTrigger value="income">Income</TabsTrigger>
          <TabsTrigger value="expenditure">Expenditure</TabsTrigger>
        </TabsList>

        <TabsContent value="income">
          {incomeEntries.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mt-6">
              <div className="rounded-md bg-gray-100 p-2 flex justify-between items-center mb-2">
                <h2 className="text-md text-gray-500 font-medium">
                  {toTitleCase(section.sectionName)}
                </h2>
              </div>

              <Table className="mt-6">
                <TableCaption>
                  <br /><br />
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Proposed Rate</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Sub Total</TableHead>
                    <TableHead>Remarks</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {section.types.map((type, typeIndex) => (
                    <TableRow key={typeIndex}>
                      <TableCell>{type.type}</TableCell>
                      <TableCell>{type.proposedRate.toFixed(2)}</TableCell>
                      <TableCell>{type.quantity.toString()}</TableCell>
                      <TableCell>{type.subTotal.toFixed(2)}</TableCell>
                      <TableCell>{type.remarks}</TableCell>
                    </TableRow>
                  ))}

                  {/* Section Total Row */}
                  <TableRow className="font-medium bg-gray-50">
                    <TableCell colSpan={3}>Total Budget</TableCell>
                    <TableCell>
                      {calculateSectionTotal(section).toFixed(2)}
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          ))}

          {/* Overall Income Total */}
          <Table className="mt-6">
            <TableBody>
              <TableRow className="font-medium bg-gray-50">
                <TableCell colSpan={3}>Total Income</TableCell>
                <TableCell>{summary?.total_income?.toFixed(2)}</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>

        <TabsContent value="expenditure">
          {expenditureEntries.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mt-6">
              <div className="rounded-md bg-gray-100 p-2 flex justify-between items-center mb-2">
                <h2 className="text-md text-gray-500 font-medium">
                  {toTitleCase(section.sectionName)}
                </h2>
              </div>

              <Table className="mt-6">
                <TableCaption>
                  <br /><br />
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Proposed Rate</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Sub Total</TableHead>
                    <TableHead>Remarks</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {section.types.map((type, typeIndex) => (
                    <TableRow key={typeIndex}>
                      <TableCell>{type.type}</TableCell>
                      <TableCell>{type.proposedRate.toFixed(2)}</TableCell>
                      <TableCell>{type.quantity.toString()}</TableCell>
                      <TableCell>{type.subTotal.toFixed(2)}</TableCell>
                      <TableCell>{type.remarks}</TableCell>
                    </TableRow>
                  ))}

                  {/* Section Total Row */}
                  <TableRow className="font-medium bg-gray-50">
                    <TableCell colSpan={3}>Total Budget</TableCell>
                    <TableCell>
                      {calculateSectionTotal(section).toFixed(2)}
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          ))}

          {/* Overall Expenditure Total */}
          <Table className="mt-6">
            <TableBody>
              <TableRow className="font-medium bg-gray-50">
                <TableCell colSpan={3}>Total Expenditure</TableCell>
                <TableCell>{summary?.total_expenditure?.toFixed(2)}</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>
      {/* Summary Table */}
      <Table className="mt-6">
        <TableBody>
          <TableRow>
            <TableCell colSpan={3}>Total Income (Excl GST)</TableCell>
            <TableCell>{summary?.total_income?.toFixed(2)}</TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Total Expenditure (Excl GST)</TableCell>
            <TableCell>{summary?.total_expenditure?.toFixed(2)}</TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Net Surplus / Deficit (A - B)</TableCell>
            <TableCell>{summary?.net_surplus_deficit?.toFixed(2)}</TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>
              % Of Net Surplus / Deficit Over Total Income
            </TableCell>
            <TableCell>{`${summary?.surplus_deficit_percentage?.toFixed(2)}%`}</TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </>
  );
};