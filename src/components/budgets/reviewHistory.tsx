"use client"

import {
    Table,
    TableCaption,
    TableHeader,
    TableRow,
    TableHead,
    TableBody,
    TableCell
} from "@/components/ui/table";
import type { approvalActionHistory } from "@/types/approvals/approval.types";

interface BudgetReviewHistoryProps {
    reviewHistory: approvalActionHistory[] | null;
}

export const BudgetReviewHistoryComponent: React.FC<BudgetReviewHistoryProps> = ({
    reviewHistory
}) => {
    if (reviewHistory?.length === 0) {
        return (
            <Table className="mt-6">
                <TableCaption>No review history found.</TableCaption>
            </Table>
        );
    }

    return (
        <Table className="mt-6">
            <TableCaption>
                Event Review History
            </TableCaption>
            <TableHeader>
                <TableRow>
                    <TableHead>Version</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Comments</TableHead>
                    <TableHead>Created At</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {reviewHistory?.map((review, index) => (
                    <TableRow key={index}>
                        <TableCell>{review.version || '-'}</TableCell>
                        <TableCell>{review.action || '-'}</TableCell>
                        <TableCell>{review.comments || '-'}</TableCell>
                        <TableCell>
                            {review.action_at ? new Date(review.action_at).toLocaleString() : '-'}
                        </TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    );
};