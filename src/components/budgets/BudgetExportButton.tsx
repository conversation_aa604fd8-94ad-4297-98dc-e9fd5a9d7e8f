"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { BudgetRevisions, BudgetReviewComplexProps, BudgetReviewSimpleEventProps } from "@/types/budgets/budgets.types";

interface BudgetExportButtonProps {
  budgetData: BudgetRevisions;
  complexItemData?: BudgetReviewComplexProps | null;
  simpleItemData?: BudgetReviewSimpleEventProps | null;
  className?: string;
}

export function BudgetExportButton({
  budgetData,
  complexItemData,
  simpleItemData,
  className
}: BudgetExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const handleExport = async () => {
    try {
      setIsExporting(true);
      
      // Import the PDF generator utility dynamically
      const { generateBudgetPDF } = await import("@/utils/pdf-generator");
      
      // Prepare the data based on budget format
      const dataToExport = budgetData.format === "COMPLEX" 
        ? complexItemData 
        : simpleItemData;

      if (!dataToExport) {
        throw new Error("No data available for export");
      }

      const budgetDetails = {
        budget_code: budgetData.budget_code || undefined,
        title: budgetData.title || undefined,
        description: budgetData.description || undefined,
        entity_type: budgetData.entity_type || undefined,
        format: budgetData.format as 'SIMPLE' | 'COMPLEX' | undefined,
        status: budgetData.status || undefined,
        programme_name: budgetData.entity_metadata?.programme_name || undefined,
        programme_code: budgetData.entity_metadata?.programme_code || undefined,
      };
      
      // Generate and download the PDF
      await generateBudgetPDF(
        dataToExport,
        budgetDetails,
        budgetData.format as 'SIMPLE' | 'COMPLEX'
      );
      
      toast({
        title: "Success",
        description: "Budget PDF has been downloaded successfully",
      });
    } catch (error) {
      console.error("Failed to export budget PDF:", error);
      toast({
        title: "Error",
        description: "Failed to export budget PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      onClick={handleExport}
      disabled={isExporting}
      variant="outline"
      className={className}
    >
      <Download className="h-4 w-4" />
      {isExporting ? "Exporting..." : "Export PDF"}
    </Button>
  );
}