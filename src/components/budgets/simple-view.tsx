"use client"

import React from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/events/tabs";
import { BudgetReviewSimpleEventProps } from "@/types/budgets/budgets.types";

export const EventBudgetView: React.FC<BudgetReviewSimpleEventProps> = ({
  incomeEntries,
  expenditureEntries,
  summary
}) => {
  return (
    <>
      <Tabs defaultValue="income" className="w-full">
        <TabsList>
          <TabsTrigger value="income">Income</TabsTrigger>
          <TabsTrigger value="expenditure">Expenditure</TabsTrigger>
        </TabsList>

        <TabsContent value="income">
          <Table className="mt-6">
            <TableCaption>Proposed income details.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Proposed Rate</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Sub Total</TableHead>
                <TableHead>Remarks</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {incomeEntries.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>{entry.type}</TableCell>
                  <TableCell>{entry.proposedRate.toFixed(2)}</TableCell>
                  <TableCell>{entry.quantity}</TableCell>
                  <TableCell>{entry.subTotal.toFixed(2)}</TableCell>
                  <TableCell>{entry.remarks}</TableCell>
                </TableRow>
              ))}
              <TableRow className="font-medium bg-gray-50">
                <TableCell colSpan={3}>Total Income</TableCell>
                <TableCell>
                  {summary?.total_income?.toFixed(2)}
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>

        <TabsContent value="expenditure">
          <Table className="mt-6">
            <TableCaption>Proposed expenditure details.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Proposed Rate</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Sub Total</TableHead>
                <TableHead>Remarks</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {expenditureEntries.map((entry, index) => (
                <TableRow key={index}>
                  <TableCell>{entry.type}</TableCell>
                  <TableCell>{entry.proposedRate.toFixed(2)}</TableCell>
                  <TableCell>{entry.quantity}</TableCell>
                  <TableCell>{entry.subTotal.toFixed(2)}</TableCell>
                  <TableCell>{entry.remarks}</TableCell>
                </TableRow>
              ))}
              <TableRow className="font-medium bg-gray-50">
                <TableCell colSpan={3}>Total Expenditure</TableCell>
                <TableCell>
                  {summary?.total_expenditure?.toFixed(2)}
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>

      {/* Summary Table */}
      <Table className="mt-6">
        <TableCaption>Budget Summary</TableCaption>
        <TableBody>
          <TableRow>
            <TableCell colSpan={3}>Total Income (Excl GST)</TableCell>
            <TableCell>{summary?.total_income?.toFixed(2)}</TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Total Expenditure (Excl GST)</TableCell>
            <TableCell>{summary?.total_expenditure?.toFixed(2)}</TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Net Surplus / Deficit (A - B)</TableCell>
            <TableCell>{summary?.net_surplus_deficit?.toFixed(2)}</TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>
              % Of Net Surplus / Deficit Over Total Income
            </TableCell>
            <TableCell>{`${summary?.surplus_deficit_percentage?.toFixed(2)}%`}</TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </>
  );
};

