"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Membership } from "@/types/members/table.types"
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header"
import { DataTableRowActions } from "@/components/pages/members/data-table-row-actions"
import { getFormattedStatus } from "@/components/member-details/summary"

export const columns: ColumnDef<Membership>[] = [
  {
    accessorKey: "membership_id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="No." />
    ),
    cell: ({ row, table }) => {
      const { pageIndex, pageSize } = table.getState().pagination;
      const rowIndex = row.index;
      const number = pageIndex * pageSize + rowIndex + 1;

      return (
        <div className="flex items-center">
          <span data-user-id={row.original.membership_id}>{number}</span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "membership_type_name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Membership Type" />
    ),
    cell: ({ row }) => {
      const membershipType = row.getValue("membership_type_name") as string;
      
      const getMembershipTypeStyle = (type: string) => {
        const upperType = type?.toUpperCase();
        if (upperType?.includes('LIFE')) {
          return 'bg-purple-100 text-purple-800 border-purple-200';
        } else if (upperType?.includes('FELLOW')) {
          return 'bg-indigo-100 text-indigo-800 border-indigo-200';
        } else if (upperType?.includes('MEMBER')) {
          return 'bg-blue-100 text-blue-800 border-blue-200';
        } else if (upperType?.includes('GRADUATE')) {
          return 'bg-green-100 text-green-800 border-green-200';
        } else if (upperType?.includes('STUDENT')) {
          return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        } else if (upperType?.includes('ORGANISATIONAL')) {
          return 'bg-orange-100 text-orange-800 border-orange-200';
        } else {
          return 'bg-gray-100 text-gray-800 border-gray-200';
        }
      };
      
      return (
        <div className="flex items-center">
          <span className={`px-2 py-1 rounded text-xs font-medium border ${getMembershipTypeStyle(membershipType)}`}>
            {membershipType}
          </span>
        </div>
      );
    },
    sortingFn: 'text',
    meta: { label: "Membership Type" },
  },
  {
    accessorKey: "membership_number",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Membership Number" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">{row.getValue("membership_number")}</div>
    ),
    sortingFn: 'alphanumeric',
    meta: { label: "Membership Number" },
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">{row.getValue("name")}</div>
    ),
    sortingFn: 'alphanumeric',
    meta: { label: "Name" },
  },
  {
    accessorKey: "start_date",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Start Date" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        {row.getValue("start_date")}
      </div>
    ),
    sortingFn: 'alphanumeric',
    meta: { label: "Start Date" },
  },
  {
    accessorKey: "end_date",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="End Date" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        {row.getValue("end_date")}
      </div>
    ),
    sortingFn: 'alphanumeric',
    meta: { label: "End Date" },
  },
  {
    accessorKey: "member_since",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Member Since" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        {row.getValue("member_since")}
      </div>
    ),
    sortingFn: 'alphanumeric',
    meta: { label: "Member Since" },
  },
  {
    accessorKey: "membership_status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const rawStatus = row.getValue("membership_status") as string;
      const formattedStatus = getFormattedStatus(rawStatus);
      
      const getMembersStatusStyle = (status: string) => {
        switch (status?.toUpperCase()) {
          case 'ACTIVE':
            return 'bg-green-200 text-green-800 border-green-300';
          case 'INACTIVE':
            return 'bg-gray-200 text-gray-800 border-gray-300';
          case 'SUSPENDED':
            return 'bg-red-200 text-red-800 border-red-300';
          case 'EXPIRED':
            return 'bg-orange-200 text-orange-800 border-orange-300';
          case 'TERMINATED':
            return 'bg-red-200 text-red-800 border-red-300';
          case 'PENDING_RENEWAL':
            return 'bg-yellow-200 text-yellow-800 border-yellow-300';
          case 'PENDING_RE_APPLICATION':
            return 'bg-blue-200 text-blue-800 border-blue-300';
          default:
            return 'bg-gray-200 text-gray-800 border-gray-300';
        }
      };
      
      const statusStyle = getMembersStatusStyle(rawStatus);

      return (
        <div className="flex items-center">
          <span className={`px-2 py-0.5 rounded-full text-xs font-medium border ${statusStyle}`}>
            {formattedStatus}
          </span>
        </div>
      );
    },
    sortingFn: 'text',
    meta: { label: "Status" },
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];
