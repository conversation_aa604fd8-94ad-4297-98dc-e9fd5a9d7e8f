"use client"

import React, { useState, useContext } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Pencil } from "lucide-react";
import { ApplicationService } from "@/services/applications/application.service";
import { ApplicationInterface, statuses } from "@/types/applications/applications.types";
import { updateBatchPContext } from "@/components/table/batch-processing-table";

interface DataTableRowActionProps {
    selectedRows: ApplicationInterface[];
    application?: ApplicationInterface;
}

export const DataTableRowActions: React.FC<DataTableRowActionProps> = ({ selectedRows, application }) => {
    const [open, setOpen] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState(application?.application_status || 'Pending');
    const [comments, setComments] = useState(application?.comment || '');
    const { setBatchApplicationsUpdate } = useContext(updateBatchPContext);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    const handleUpdate = async () => {
        try {
            setErrorMessage(null);

            const validStatuses = statuses.map(status => status.value);
            if (!selectedStatus || !validStatuses.includes(selectedStatus)) {
                throw new Error('Invalid status selected.');
            }

            const rowsToUpdate = selectedRows.length > 0 ? selectedRows : [application!];

            let hasError = false;

            for (const app of rowsToUpdate) {
                if (!app.application_id) {
                    throw new Error(`Invalid application ID for one of the selected rows.`);
                }

                const result = await ApplicationService.updateApplications(app.application_id, selectedStatus, comments);

                if (!result) {
                    hasError = true;
                }
            }

            if (hasError) {
                setErrorMessage('Failed to update some applications, please try again later.');
            }
            setBatchApplicationsUpdate();
            setOpen(false);
            setComments('');
        } catch {
            setErrorMessage('An unexpected error occurred. Please try again later.');
        }
    };

    return (
        <div className="flex justify-center space-x-2">
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogTrigger asChild>
                    <button
                        className="h-8 w-8 p-0 text-gray-600 hover:text-blue-500 bg-transparent focus:outline-none"
                        onClick={() => setOpen(true)}
                    >
                        <Pencil className="h-5 w-5" />
                    </button>
                </DialogTrigger>

                <DialogContent className="rounded-xl p-8">
                    <DialogHeader>
                        <DialogTitle>{selectedRows.length > 0 ? 'Bulk Update Status and Comments' : 'Update Status and Add Comments'}</DialogTitle>
                        <DialogDescription>
                            {selectedRows.length > 0
                                ? `Update status and add comments for ${selectedRows.length} selected applications`
                                : 'Please update the status and add your comments.'}
                        </DialogDescription>
                    </DialogHeader>

                    <div className="mt-4">
                        <label className="block mb-2 text-sm font-semibold">Status</label>
                        <select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            className="w-full border rounded-md p-2 text-sm"
                        >
                            {statuses.map((status) => (
                                <option key={status.value} value={status.value}>
                                    {status.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="mt-4">
                        <label className="block mb-2 text-sm font-semibold">Comments</label>
                        <textarea
                            value={comments}
                            onChange={(e) => setComments(e.target.value)}
                            className="w-full border rounded-md p-2"
                            rows={4}
                            placeholder="Add comments for all selected applications"
                        />
                    </div>

                    {errorMessage && (
                        <div className="mt-4 text-red-500 text-sm">
                            <strong>Error:</strong> {errorMessage}
                        </div>
                    )}

                    <div className="flex justify-end mt-4">
                        <Button variant="outline" onClick={() => setOpen(false)} className="rounded-md">
                            Cancel
                        </Button>
                        <Button
                            variant="default"
                            onClick={handleUpdate}
                            className="ml-2 rounded-md"
                        >
                            {selectedRows.length > 0 ? 'Update All Selected' : 'Save'}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};
