"use client"

import {
    Ho<PERSON><PERSON><PERSON>,
    Hover<PERSON><PERSON><PERSON>ontent,
    HoverCardTrigger,
} from "@/components/ui/hover-card";
import { ColumnDef } from "@tanstack/react-table"
import { ApplicationInterface } from "@/types/applications/applications.types";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import React, { useState } from "react";
import { DataTableRowActions } from "@/components/pages/batch-processing/data-table-row-actions";
import { Checkbox } from "@/components/ui/checkbox";

const CommentsCell = ({ row }: {
    row: {
        getValue: (key: string) => string | undefined;
        index: number;
    }
}) => {
    // Fetch the actual comment from row
    const comments = row.getValue("comment");

    // Truncate comment if it's too long
    const truncatedComments = typeof comments === 'string' && comments.length > 20
        ? `${comments.substring(0, 20)}...`
        : comments;

    return (
        <HoverCard>
            <HoverCardTrigger>
                <div className="italic text-gray-500 cursor-pointer">
                    {truncatedComments}
                </div>
            </HoverCardTrigger>
            <HoverCardContent className="p-2 border border-gray-300 rounded-md shadow-md" style={{ width: '200px' }}>
                <div>{comments}</div>
            </HoverCardContent>
        </HoverCard>
    );
};

export const useBatchProcessingColumns = () => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

    const toggleRowSelection = (invoiceId: string) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.has(invoiceId)) {
                newSelectedRows.delete(invoiceId);
            } else {
                newSelectedRows.add(invoiceId);
            }
            return newSelectedRows;
        });
    };

    const toggleAllRowsSelection = (rows: any[]) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.size === rows.length) {
                newSelectedRows.clear();
            } else {
                rows.forEach((row) => newSelectedRows.add(row.original.application_id));
            }
            return newSelectedRows;
        });
    };

    const columns: ColumnDef<ApplicationInterface>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <div className="flex justify-center items-center">
                    <Checkbox
                        checked={
                            selectedRows.size > 0
                                ? selectedRows.size === table.getRowModel().rows.length
                                    ? true
                                    : "indeterminate"
                                : false
                        }
                        onCheckedChange={() => {
                            toggleAllRowsSelection(table.getRowModel().rows);
                        }}
                        aria-label="Select all"
                        className="translate-y-[2px]"
                    />
                </div>
            ),
            cell: ({ row }) => (
                <div className="flex justify-center items-center">
                    <Checkbox
                        checked={selectedRows.has(row.original.application_id)}
                        onCheckedChange={() => toggleRowSelection(row.original.application_id)}
                        aria-label={`Select ${row.original.application_id}`}
                        className="translate-y-[2px]"
                    />
                </div>
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "application_id",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="No." />
            ),
            cell: ({ row, table }) => {
                const { pageIndex, pageSize } = table.getState().pagination;
                const rowIndex = row.index;
                const number = pageIndex * pageSize + rowIndex + 1;

                return (
                    <div className=" flex text-center">
                        <span data-user-id={row.original.application_id}>{number}</span>
                    </div>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "reference_no",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Reference No" />
            ),
            cell: ({ row }) => <div className="flex items-center">{row.getValue("reference_no")}</div>,
            sortingFn: 'alphanumeric',
            meta: { label: "Reference No" },
        },
        {
            accessorKey: "full_name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Applicant Name" />
            ),
            cell: ({ row }) => <div className="flex items-center truncate">{row.getValue("full_name")}</div>,
            sortingFn: 'alphanumeric',
            meta: { label: "Applicant Name" },
        },
        {
            accessorKey: "membership_type_name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Membership Type" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("membership_type_name")}</div>
            ),
            sortingFn: 'text',
            meta: { label: "Membership Type" },
        },
        {
            accessorKey: "application_type",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Type" />
            ),
            cell: ({ row }) => {
                // Utility function to capitalize the text
                const capitalize = (text: string) =>
                    text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();

                return (
                    <div className="flex items-center">
                        {capitalize(row.getValue("application_type"))}
                    </div>
                );
            },
            sortingFn: 'text',
            meta: { label: "Type" },
        },
        {
            accessorKey: "submitted_at",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Submission Date" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    {row.getValue("submitted_at")}
                </div>
            ),
            sortingFn: 'alphanumeric',
        },
        {
            accessorKey: "application_status",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Status" />
            ),
            cell: ({ row }) => {
                const status = row.getValue("application_status") as string;

                const statusDisplayText: Record<string, string> = {
                    SUBMITTED_PENDING_PAYMENT: "Pending Payment",
                    PAID_PENDING_REVIEW: "Pending Review",
                    TO_INTERVIEW: "Pending Interview",
                    DOCUMENT_REQUIRED: "Documents Needed",
                    APPROVED: "Approved",
                    REJECTED: "Rejected",
                    CANCELLED: "Cancelled",
                };

                const displayText = statusDisplayText[status] || status;

                const getApplicationStatusStyle = (status: string) => {
                    switch (status) {
                        case 'SUBMITTED_PENDING_PAYMENT':
                        case 'TO_INTERVIEW':
                        case 'PAID_PENDING_REVIEW':
                            return 'bg-orange-200 text-black';
                        case 'APPROVED':
                            return 'bg-green-200 text-black';
                        case 'REJECTED':
                        case 'CANCELLED':
                            return 'bg-red-200 text-black';
                        case 'DOCUMENT_REQUIRED':
                        default:
                            return 'bg-gray-200 text-black';
                    }
                };

                return (
                    <div className="flex items-center">
                        <button className={`px-4 py-1 rounded-3xl ${getApplicationStatusStyle(status)}`}
                            style={{ textTransform: "capitalize" }}>
                            {displayText}
                        </button>
                    </div>
                );
            },
            sortingFn: 'text',
            meta: { label: "Status" },
        },
        {
            accessorKey: "approved_or_rejected_at",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Approved At" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    {row.getValue("approved_or_rejected_at")}
                </div>
            ),
            sortingFn: 'alphanumeric',
            meta: { label: "Approved At" },
        },
        {
            accessorKey: "comment",
            header: "Comments",
            cell: ({ row }) => (
                <div className="flex items-center">
                    <CommentsCell row={row} />
                </div>
            ),
            meta: { label: "Comments" },
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row, table }) => (
                <DataTableRowActions selectedRows={table.getSelectedRowModel().rows.map(r => r.original)} application={row.original} />
            ),
        },
    ];

    return { columns, selectedRows, setSelectedRows };
};
