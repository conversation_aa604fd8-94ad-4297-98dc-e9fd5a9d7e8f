"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { Checkbox } from "@/components/ui/checkbox";
import { invoiceDetailsView } from "@/types/finance/invoices.types";
import { DataTableRowActions } from "@/components/pages/invoices/data-table-row-actions";

export const useInvoiceColumns = () => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

    const toggleRowSelection = (invoiceId: string) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.has(invoiceId)) {
                newSelectedRows.delete(invoiceId);
            } else {
                newSelectedRows.add(invoiceId);
            }
            return newSelectedRows;
        });
    };

    const toggleAllRowsSelection = (rows: any[]) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.size === rows.length) {
                newSelectedRows.clear();
            } else {
                rows.forEach((row) => newSelectedRows.add(row.original.invoice_id));
            }
            return newSelectedRows;
        });
    };

    const columns: ColumnDef<invoiceDetailsView>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <div className="flex justify-center items-center">
                    <Checkbox
                        checked={
                            selectedRows.size > 0
                                ? selectedRows.size === table.getRowModel().rows.length
                                    ? true
                                    : "indeterminate"
                                : false
                        }
                        onCheckedChange={() => {
                            toggleAllRowsSelection(table.getRowModel().rows);
                        }}
                        aria-label="Select all"
                        className="translate-y-[2px]"
                    />
                </div>
            ),
            cell: ({ row }) => (
                <div className="flex justify-center items-center">
                    <Checkbox
                        checked={selectedRows.has(row.original.invoice_id ?? "")}
                        onCheckedChange={() => toggleRowSelection(row.original.invoice_id ?? "")}
                        aria-label={`Select ${row.original.invoice_id}`}
                        className="translate-y-[2px]"
                    />
                </div>
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "invoice_id",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="No." />
            ),
            cell: ({ row, table }) => {
                const { pageIndex, pageSize } = table.getState().pagination;
                const rowIndex = row.index;
                const number = pageIndex * pageSize + rowIndex + 1;

                return (
                    <div className=" flex text-center">
                        <span data-user-id={row.original.invoice_id}>{number}</span>
                    </div>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "invoice_number",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Invoice Number" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("invoice_number")}</div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Invoice Number" },
        },
        {
            accessorKey: "issue_date",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Issue Date" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    {row.getValue("issue_date")}
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Issue Date" },
        },
        {
            accessorKey: "due_date",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Due Date" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    {row.getValue("due_date")}
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Due Date" },
        },
        {
            accessorKey: "total_amount",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Total Amount" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    ${parseFloat(row.getValue("total_amount")).toFixed(2)}
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Total Amount" },
        },
        {
            accessorKey: "balance_due",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Balance Due" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    ${parseFloat(row.getValue("balance_due")).toFixed(2)}
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Balance Due" },
        },
        {
            accessorKey: "full_name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Invoice To" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("full_name")}</div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Invoice To" },
        },
        {
            accessorKey: "status",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Status" />
            ),
            cell: ({ row }) => {
                const status = row.getValue("status") as string;

                const getInvoicesStatusStyle = (status: string) => {
                    switch (status) {
                        case 'DRAFT':
                        case 'VOID':
                        case 'UNPAID':
                        case 'SENT':
                        case 'PARTIALLY_PAID':
                            return 'bg-orange-200 text-black';
                        case 'PAID':
                            return 'bg-green-200 text-black';
                        case 'OVERDUE':
                            return 'bg-red-200 text-black';
                        default:
                            return 'bg-gray-200 text-black';
                    }
                };

                return (
                    <div className="flex items-center">
                        <button
                            className={`px-4 py-1 rounded-3xl ${getInvoicesStatusStyle(status)}`}
                            style={{ textTransform: "capitalize" }}
                        >
                            {status.toLowerCase()}
                        </button>
                    </div>
                );
            },
            sortingFn: "text",
            meta: { label: "Status" },
        },
        {
            accessorKey: "source_type",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Source Type" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("source_type")}</div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Source Type" },
        },
        {
            accessorKey: "membership_type_name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Source" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("membership_type_name") || "--"}</div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Source" },
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} />,
        },
    ];

    return { columns, selectedRows, setSelectedRows };
};