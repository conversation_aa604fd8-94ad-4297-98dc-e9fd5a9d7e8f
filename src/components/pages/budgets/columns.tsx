"use client"

import { ColumnDef } from "@tanstack/react-table";
import { DataTableRowActions } from "@/components/pages/budgets/data-table-row-actions";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import type { BudgetRevisionView } from "@/types/budgets/budgets.types";

export const columns: ColumnDef<BudgetRevisionView>[] = [
    {
        accessorKey: "id",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="No." />
        ),
        cell: ({ row, table }) => {
            const { pageIndex, pageSize } = table.getState().pagination;
            const rowIndex = row.index;
            const number = pageIndex * pageSize + rowIndex + 1;

            return (
                <div className="flex text-center">
                    <span data-user-id={row.original.id}>{number}</span>
                </div>
            );
        },
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "budget_code",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Budget Code" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("budget_code") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Budget Code" },
    },
    {
        accessorKey: "latest_version",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Latest Version" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("latest_version") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Latest Version" },
    },
    {
        accessorKey: "type",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Type" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("type") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Type" },
    },
    {
        accessorKey: "format",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Format" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("format") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Format" },
    },
    {
        accessorKey: "latest_title",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Latest Title" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("latest_title") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Latest Title" },
    },
    {
        accessorKey: "latest_status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Latest Status" />
        ),
        cell: ({ row }) => {
            const status = row.getValue("latest_status") as string;

            const getCoursesEventsStatusStyle = (status: string) => {
                switch (status) {
                    case 'DRAFT':
                        return 'bg-gray-200 text-black';
                    case 'PENDING_APPROVAL':
                        return 'bg-orange-200 text-black';
                    case 'APPROVED':
                        return 'bg-green-200 text-black';
                    case 'REJECTED':
                        return 'bg-red-200 text-black';
                    default:
                        return 'bg-gray-200 text-black';
                }
            };

            return (
                <div className="flex items-center">
                    <button className={`px-4 py-1 rounded-3xl ${getCoursesEventsStatusStyle(status)}`}
                        style={{ textTransform: "capitalize" }}>
                        {status.toLowerCase()}
                    </button>
                </div>
            );
        },
        sortingFn: 'text',
        meta: { label: "Latest Status" },
    },
    {
        accessorKey: "latest_submitted_by",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Latest Requestor" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("latest_submitted_by") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Latest Requestor" },
    },
    {
        accessorKey: "latest_reviewed_by",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Latest Approver(s)" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("latest_reviewed_by") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Latest Approver(s)" },
    },
    {
        accessorKey: "created_by",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Created By" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("created_by") || "--"}</div>,
        sortingFn: 'alphanumeric',
        meta: { label: "Created By" },
    },
    {
        id: "actions",
        cell: ({ row }) => <DataTableRowActions row={row} />,
    },
];
