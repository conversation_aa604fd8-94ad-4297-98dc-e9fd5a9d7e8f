"use client";

import { Row } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { BudgetRevisionView } from "@/types/budgets/budgets.types";
import { useVerifyPageDestinationOnBudget } from "@/services/budgets/budget.service";

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
    row,
}: DataTableRowActionsProps<TData>) {
    const budget = row.original as BudgetRevisionView;
    const verifyPageDestination = useVerifyPageDestinationOnBudget();

    const handleView = async () => {
        if (budget.latest_status !== 'DRAFT' && budget.latest_id) {
            return verifyPageDestination(budget.latest_id)
        }

        if (budget.latest_approval_id) {
            return verifyPageDestination(budget.latest_approval_id)
        }

        if (budget.id) {
            return verifyPageDestination(budget.id)
        }
    }

    const handleEditDraft = async () => {
        if (budget.latest_id) {
            verifyPageDestination(budget.latest_id)
        }
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                >
                    <MoreHorizontal />
                    <span className="sr-only">Open menu</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
                <DropdownMenuItem onClick={handleView}>View</DropdownMenuItem>
                {(budget.latest_status === 'DRAFT' && budget.latest_id) && (
                    <DropdownMenuItem onClick={handleEditDraft}>Edit Draft</DropdownMenuItem>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
