"use client"

import { ColumnDef } from "@tanstack/react-table";
import { ProgrammeList } from "@/types/programmes/programme.types";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Eye, Copy, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const columns: ColumnDef<ProgrammeList>[] = [
    {
        accessorKey: "id",
        header: ({ column }) => (

            <DataTableColumnHeader column={column} title="No." />
        ),
        cell: ({ row, table }) => {
            const { pageIndex, pageSize } = table.getState().pagination;
            const rowIndex = row.index;
            const number = pageIndex * pageSize + rowIndex + 1;

            return (
                <div className="flex text-center">
                    <span data-user-id={row.original.id}>{number}</span>
                </div>
            );
        },
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "programme_code",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Programme Code" />
        ),
        cell: ({ row }) => <div className="flex items-center">{row.getValue("programme_code") || "--"}</div>,
        enableSorting: true,
        meta: { label: "Programme Code" },
    },
    {
        accessorKey: "name",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Programme Name" />
        ),
        cell: ({ row }) => {
            const name = row.getValue("name") as string;
            return (
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className="flex items-center max-w-[300px]">
                            <span className="truncate">{name || "--"}</span>
                        </div>
                    </TooltipTrigger>
                    {name && name.length > 40 && (
                        <TooltipContent className="max-w-[400px]">
                            <p className="whitespace-normal">{name}</p>
                        </TooltipContent>
                    )}
                </Tooltip>
            );
        },
        enableSorting: true,
        meta: { label: "Programme Name" },
    },
    {
        accessorKey: "type",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Type" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center">{row.getValue("type") || "--"}</div>
        ),
        enableSorting: true,
        meta: { label: "Type" },
    },
    {
        accessorKey: "status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
            const status = row.getValue("status") as string;

            const getCoursesEventsStatusStyle = (status: string) => {
                switch (status) {
                    case 'DRAFT':
                    case 'UNPUBLISHED':
                    case 'PENDING_REVIEW':
                        return 'bg-orange-200 text-black';
                    case 'APPROVED':
                        return 'bg-orange-200 text-black';
                    case 'PUBLISHED':
                        return 'bg-green-200 text-black';
                    case 'COMPLETED':
                        return 'bg-green-200 text-black';
                    case 'REJECTED':
                    case 'TERMINATED':
                        return 'bg-red-200 text-black';
                    default:
                        return 'bg-gray-200 text-black';
                }
            };

            return (
                <div className="flex items-center">
                    <button className={`px-4 py-1 rounded-3xl ${getCoursesEventsStatusStyle(status)}`}
                        style={{ textTransform: "capitalize" }}>
                        {status.toLowerCase()}
                    </button>
                </div>
            );
        },
        enableSorting: true,
        meta: { label: "Status" },
    },
    {
        accessorKey: "staff_in_charge_name",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Staff in Charge" />
        ),
        cell: ({ row }) => (<div className="flex items-center">{row.getValue("staff_in_charge_name") || "--"}</div>),
        enableSorting: true,
        meta: { label: "Staff in Charge" },
    },
    {
        accessorKey: "coordinator_name",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Coordinator" />
        ),
        cell: ({ row }) => (<div className="flex items-center">{row.getValue("coordinator_name") || "--"}</div>),
        enableSorting: true,
        meta: { label: "Coordinator" },
    },
    {
        accessorFn: (row) => {
            const schedules = row.programmeSchedules;
            return schedules?.[0]?.date || null; // return first date or null
        },
        id: "programmeDate",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Programme Date" />
        ),
        cell: ({ row }) => {
            const schedules = row.original.programmeSchedules;

            if (!schedules || schedules.length === 0) {
                return <div className="flex items-center">--</div>;
            }

            const firstDate = schedules[0].date;
            const additionalDates = schedules.slice(1).map((schedule: any) => schedule.date).join(", ");

            return (
                <div className="flex items-center">
                    {schedules.length > 1 ? (
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span className="underline cursor-pointer">{firstDate}</span>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Additional Dates: {additionalDates}</p>
                            </TooltipContent>
                        </Tooltip>
                    ) : (
                        <span>{firstDate}</span>
                    )}
                </div>
            );
        },
        enableSorting: true,
        meta: { label: "Programme Date" },
    },
    {
        id: "actions",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Actions" />
        ),
        cell: ({ row }) => {
            const programme = row.original;
            // eslint-disable-next-line react-hooks/rules-of-hooks
            const router = useRouter();
            const isPublished = programme.status === 'PUBLISHED';

            const handleCopyLink = async () => {
                const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://portal.ies.org.sg';
                const publicLink = `${siteUrl}/programmes/${programme.id}`;
                try {
                    await navigator.clipboard.writeText(publicLink);
                    toast({
                        title: "Success",
                        description: "Public link copied to clipboard",
                    });
                } catch (err) {
                    toast({
                        title: "Error",
                        description: "Failed to copy link to clipboard",
                        variant: "destructive",
                    });
                }
            };

            return (
                <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/programmes/${programme.id}`)}
                        className="flex items-center gap-1"
                    >
                        <Eye className="h-4 w-4" />
                        Details
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                            >
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem
                                onClick={handleCopyLink}
                                disabled={!isPublished}
                                className="cursor-pointer"
                            >
                                <Copy className="mr-2 h-4 w-4" />
                                Copy Public Link
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            );
        },
        enableSorting: false,
        meta: { label: "Actions" },
    },
];
