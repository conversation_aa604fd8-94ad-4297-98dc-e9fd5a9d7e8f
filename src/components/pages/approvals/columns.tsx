"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox"
import { ApprovalTableRow } from "@/types/approvals/approval.types"
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header"
import { DataTableRowActions } from "@/components/pages/approvals/data-table-row-actions"
import { formatDistanceToNow } from "date-fns"
import Link from "next/link"

export const columns: ColumnDef<ApprovalTableRow>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex justify-center items-center">
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => {
            table.getRowModel().rows.forEach((row) => {
              row.toggleSelected(!!value);
            });
          }}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={`Select approval ${row.getValue("request_number")}`}
          className="translate-y-[2px]"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="No." />
    ),
    cell: ({ row, table }) => {
      const { pageIndex, pageSize } = table.getState().pagination;
      const rowIndex = row.index;
      const number = pageIndex * pageSize + rowIndex + 1;

      return (
        <div className="flex items-center">
          <span data-approval-id={row.original.id}>{number}</span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "request_number",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Request Number" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        <Link
          href={`/approvals/${row.original.id}`}
          className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
        >
          {row.getValue("request_number")}
        </Link>
      </div>
    ),
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: "module_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Module Type" />
    ),
    cell: ({ row }) => {
      const moduleType = row.getValue("module_type") as string;

      const getModuleTypeStyle = (type: string) => {
        switch (type.toLowerCase()) {
          case 'budget':
            return 'bg-blue-100 text-blue-800 border-blue-200';
          case 'expense':
            return 'bg-purple-100 text-purple-800 border-purple-200';
          case 'leave':
            return 'bg-yellow-100 text-yellow-800 border-yellow-200';
          case 'programme':
            return 'bg-green-100 text-green-800 border-green-200';
          case 'application':
            return 'bg-indigo-100 text-indigo-800 border-indigo-200';
          case 'purchase_order':
            return 'bg-teal-100 text-teal-800 border-teal-200';
          default:
            return 'bg-gray-100 text-gray-800 border-gray-200';
        }
      };

      return (
        <div className="flex items-center">
          <span className={`px-2 py-1 rounded text-xs font-medium border ${getModuleTypeStyle(moduleType)}`}>
            {moduleType.charAt(0).toUpperCase() + moduleType.slice(1).toLowerCase()}
          </span>
        </div>
      );
    },
    sortingFn: 'text',
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "submitted_by_name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Submitted By" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        <span className="font-medium">{row.getValue("submitted_by_name")}</span>
      </div>
    ),
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status") as string;

      const getApprovalStatusStyle = (status: string) => {
        switch (status.toLowerCase()) {
          case 'pending':
            return 'bg-yellow-200 text-yellow-800 border-yellow-300';
          case 'approved':
            return 'bg-green-200 text-green-800 border-green-300';
          case 'rejected':
            return 'bg-red-200 text-red-800 border-red-300';
          case 'cancelled':
            return 'bg-gray-200 text-gray-800 border-gray-300';
          default:
            return 'bg-blue-200 text-blue-800 border-blue-300';
        }
      };

      return (
        <div className="flex items-center">
          <span className={`px-2 py-0.5 rounded-full text-xs font-medium border ${getApprovalStatusStyle(status)}`}>
            {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
          </span>
        </div>
      );
    },
    sortingFn: 'text',
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "data_summary",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Summary" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        <span className="text-sm text-gray-600 max-w-xs truncate" title={row.getValue("data_summary")}>
          {row.getValue("data_summary")}
        </span>
      </div>
    ),
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: "submitted_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Submitted" />
    ),
    cell: ({ row }) => {
      const submittedAt = row.getValue("submitted_at") as string;
      const date = new Date(submittedAt);

      return (
        <div className="flex items-center">
          <div className="flex flex-col">
            <span className="text-sm font-medium">
              {date.toLocaleDateString()}
            </span>
            <span className="text-xs text-gray-500">
              {formatDistanceToNow(date, { addSuffix: true })}
            </span>
          </div>
        </div>
      );
    },
    sortingFn: 'datetime',
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
    enableSorting: false,
    enableHiding: false,
  },
];