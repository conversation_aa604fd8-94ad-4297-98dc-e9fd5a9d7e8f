"use client"

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { FileText, Al<PERSON><PERSON>riangle, Settings } from "lucide-react";
import { format } from "date-fns";
import { ApprovalRequestWithDetails } from '@/types/approvals/approval.types';
import { ApprovalTimeline } from '@/components/pages/approvals/approval-timeline';
import { ApprovalActionsDialog } from '@/components/pages/approvals/approval-actions-dialog';
import { ApprovalService } from '@/services/approvals/approval.service';
import { createClient } from '@/utils/supabase/Client';
import { useVerifyPageDestinationOnBudget } from '@/services/budgets/budget.service';

interface ApprovalDetailProps {
  approval: ApprovalRequestWithDetails;
  onRefresh?: () => void;
}

export function ApprovalDetail({ approval, onRefresh }: ApprovalDetailProps) {
  const verifyBudgetDestination = useVerifyPageDestinationOnBudget();
  const [isActionsDialogOpen, setIsActionsDialogOpen] = useState(false);
  const [userCanApprove, setUserCanApprove] = useState<boolean | null>(null);
  const [approvalPermissionMessage, setApprovalPermissionMessage] = useState<string>('');

  // Check user approval permissions when component loads
  React.useEffect(() => {
    checkUserApprovalPermissions();
  }, [approval.id]);

  const checkUserApprovalPermissions = async () => {
    try {
      // Only check permissions for pending requests
      if (approval.status?.toLowerCase() !== 'pending') {
        setUserCanApprove(false);
        setApprovalPermissionMessage('This request is no longer pending approval.');
        return;
      }

      const supabase = await createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        setUserCanApprove(false);
        setApprovalPermissionMessage('You must be logged in to approve requests.');
        return;
      }

      // Get user's approval options for this request
      const approvalOptions = await ApprovalService.getUserApprovalOptions(approval.id, user.id);

      if (approvalOptions.length > 0) {
        setUserCanApprove(true);
        setApprovalPermissionMessage('');
      } else {
        // Check if user has already acted on this request
        const userActions = approval.actions?.filter(action => action.actioned_by === user.id);

        if (userActions && userActions.length > 0) {
          setUserCanApprove(false);
          setApprovalPermissionMessage('You have already taken action on this request.');
        } else {
          setUserCanApprove(false);
          setApprovalPermissionMessage('You do not have permission to approve this request at the current stage.');
        }
      }
    } catch (error) {
      console.error('Error checking approval permissions:', error);
      setUserCanApprove(false);
      setApprovalPermissionMessage('Unable to verify approval permissions. Please try refreshing the page.');
    }
  };

  const handleActionComplete = (action: string, result: any) => {
    // Show success message based on the action and result
    const actionMessages = {
      approve: 'Request has been approved successfully',
      reject: 'Request has been rejected',
      request_info: 'Information request has been sent to the submitter',
      delegate: 'Request has been delegated successfully'
    };

    const message = actionMessages[action as keyof typeof actionMessages] || 'Action completed successfully';

    // In a real app, you might show a toast notification here
    alert(message);

    // Refresh the data if callback provided
    if (onRefresh) {
      onRefresh();
    }

    // Re-check permissions after action
    checkUserApprovalPermissions();
  };
  const getStatusBadge = (status: string) => {
    const normalizedStatus = status?.toLowerCase();
    switch (normalizedStatus) {
      case 'pending':
        return <Badge className="bg-yellow-200 text-yellow-800 border-yellow-300">Pending</Badge>;
      case 'approved':
        return <Badge className="bg-green-200 text-green-800 border-green-300">Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-200 text-red-800 border-red-300">Rejected</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-200 text-gray-800 border-gray-300">Cancelled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getModuleTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case 'budget':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Budget</Badge>;
      case 'expense':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Expense</Badge>;
      case 'leave':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Leave</Badge>;
      case 'programme':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Programme</Badge>;
      case 'application':
        return <Badge className="bg-indigo-100 text-indigo-800 border-indigo-200">Application</Badge>;
      case 'purchase_order':
        return <Badge className="bg-teal-100 text-teal-800 border-teal-200">Purchase Order</Badge>
      default:
        return <Badge>{type}</Badge>;
    }
  };

  // Transform and group stages for timeline (handling parallel approvals)
  const timelineStages = React.useMemo(() => {
    if (!approval.workflow_stages?.length) return [];

    const currentStageOrder = approval.current_stage?.stage_order || 0;

    // Group stages by stage_order to handle parallel approvals
    const stageGroups = approval.workflow_stages.reduce((groups, stage) => {
      const order = stage.stage_order;
      if (!groups[order]) groups[order] = [];
      groups[order].push(stage);
      return groups;
    }, {} as Record<number, typeof approval.workflow_stages>);

    // Transform each group
    return Object.entries(stageGroups)
      .sort(([a], [b]) => parseInt(a) - parseInt(b))
      .map(([stageOrder, stagesInGroup]) => {
        const orderNum = parseInt(stageOrder);
        const isParallel = stagesInGroup.length > 1;

        // Get actions for all stages in this group
        const stagesWithActions = stagesInGroup.map(stage => {
          const action = approval.actions?.find(a => a.stage_id === stage.id);


          let status: 'completed' | 'current' | 'pending' | 'partial';

          // Priority order for status determination:
          // 1. Check if this specific stage has an approved action
          if (action && action.action?.toLowerCase() === 'approve') {
            status = 'completed';
          }
          // 2. Check if this specific stage has been rejected
          else if (action && action.action?.toLowerCase() === 'reject') {
            status = 'completed'; // Still completed, but rejected
          }
          // 3. Check if overall request is approved and this stage should be completed
          else if (approval.status?.toLowerCase() === 'approved' && orderNum <= currentStageOrder) {
            status = 'completed';
          }
          // 4. Check if this is the current stage order (could be current or pending within parallel group)
          else if (orderNum === currentStageOrder) {
            status = 'current';
          }
          // 5. Check if this is a past stage that should be completed
          else if (orderNum < currentStageOrder) {
            status = 'completed';
          }
          // 6. Future stage
          else {
            status = 'pending';
          }

          return {
            ...stage,
            status,
            actioned_by: action?.actioned_by_name,
            actioned_at: action?.actioned_at,
            comments: action?.comments,
            action: action?.action,
          };
        });

        // For parallel stages, determine overall group status
        let groupStatus: 'completed' | 'current' | 'pending' | 'partial';
        if (isParallel) {
          const completedCount = stagesWithActions.filter(s => s.status === 'completed').length;
          const totalCount = stagesWithActions.length;

          if (completedCount === totalCount) {
            groupStatus = 'completed';
          } else if (completedCount > 0 && orderNum === currentStageOrder) {
            groupStatus = 'partial';
          } else if (orderNum === currentStageOrder) {
            groupStatus = 'current';
          } else if (orderNum < currentStageOrder) {
            groupStatus = 'completed';
          } else {
            groupStatus = 'pending';
          }
        } else {
          groupStatus = stagesWithActions[0].status;
        }

        return {
          stageOrder: orderNum,
          isParallel,
          groupStatus,
          stages: stagesWithActions,
          completedCount: stagesWithActions.filter(s => s.status === 'completed').length,
          totalCount: stagesWithActions.length,
        };
      });
  }, [approval.workflow_stages, approval.actions, approval.current_stage, approval.status]);

  // Format data for display
  const formatDataValue = (value: any): string => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') return value.toLocaleString();
    if (value instanceof Date) return format(value, 'MMM d, yyyy');
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  };

  const formatFieldName = (field: string): string => {
    return field
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const handleViewDocument = () => {
    if (approval.module_type === 'budget') {
      verifyBudgetDestination(approval.module_record_id, true)
    } else if (approval.module_type === 'purchase_order') {
      window.open(`/purchase-orders/${approval.module_record_id}`, '_blank');
    }
  }

  return (
    <div className="space-y-6">
      {/* Header with title and action buttons */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Approval Details</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2" onClick={handleViewDocument}>
            <FileText className="h-4 w-4" />
            View Document
          </Button>
        </div>
      </div>

      {/* Action Notice */}
      {approval.status?.toLowerCase() === 'pending' && (
        <>
          {userCanApprove === null && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <Settings className="h-4 w-4 text-gray-600 animate-spin" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Checking Permissions</h3>
                  <p className="text-sm text-gray-700">Verifying your approval permissions...</p>
                </div>
              </div>
            </div>
          )}

          {userCanApprove === true && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                      <AlertTriangle className="h-4 w-4 text-amber-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-amber-900">Action Required</h3>
                    <p className="text-sm text-amber-700">This request needs your approval decision.</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setIsActionsDialogOpen(true)}
                  className="px-4 py-2 text-sm"
                >
                  Review & Decide
                </Button>
              </div>
            </div>
          )}

          {userCanApprove === false && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-blue-900">No Action Available</h3>
                  <p className="text-sm text-blue-700">{approvalPermissionMessage}</p>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main details card - spans 2 columns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card className="rounded-xl">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Request Number</p>
                  <p className="mt-1 text-sm font-semibold text-gray-900">{approval.request_number}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</p>
                  <div className="mt-1">{getStatusBadge(approval.status)}</div>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Module Type</p>
                  <div className="mt-1">{getModuleTypeBadge(approval.module_type)}</div>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Workflow</p>
                  <p className="mt-1 text-sm font-semibold text-gray-900">{approval.workflow?.name || 'N/A'}</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Submitted By</p>
                  <p className="mt-1 text-sm font-semibold text-gray-900">{approval.submitted_by_user?.name || 'Unknown'}</p>
                  {approval.submitted_by_user?.email && (
                    <p className="text-xs text-gray-500">{approval.submitted_by_user.email}</p>
                  )}
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Submitted On</p>
                  <p className="mt-1 text-sm font-semibold text-gray-900">
                    {approval.submitted_at ? format(new Date(approval.submitted_at), 'MMM d, yyyy') : 'N/A'}
                  </p>
                  {approval.submitted_at && (
                    <p className="text-xs text-gray-500">
                      {format(new Date(approval.submitted_at), 'h:mm a')}
                    </p>
                  )}
                </div>
              </div>

              {approval.current_stage && (
                <>
                  <Separator />
                  <div>
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Current Stage</p>
                    <p className="mt-1 text-sm font-semibold text-gray-900">
                      Stage {approval.current_stage.stage_order}: {approval.current_stage.stage_name}
                    </p>
                    {approval.current_stage.description && (
                      <p className="text-xs text-gray-500 mt-1">{approval.current_stage.description}</p>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Request Details */}
          <Card className="rounded-xl">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Request Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {approval.metadata && typeof approval.metadata === 'object' ?
                  Object.entries(approval.metadata).map(([key, value]) => (
                    <div key={key} className={key === 'description' || key === 'notes' ? 'col-span-2' : ''}>
                      <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                        {formatFieldName(key)}
                      </p>
                      <p className="mt-1 text-sm font-semibold text-gray-900">
                        {formatDataValue(value)}
                      </p>
                    </div>
                  )) : (
                    <div className="col-span-2 text-center py-8">
                      <p className="text-sm text-gray-500">No additional request details available.</p>
                    </div>
                  )
                }
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Timeline - spans 1 column */}
        <div className="lg:col-span-1">
          <Card className="rounded-xl h-fit">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Approval Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <ApprovalTimeline
                stages={timelineStages}
                submittedBy={approval.submitted_by_user?.name || 'Unknown'}
                submittedAt={approval.submitted_at || new Date().toISOString()}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Approval Actions Dialog */}
      <ApprovalActionsDialog
        isOpen={isActionsDialogOpen}
        onClose={() => setIsActionsDialogOpen(false)}
        requestId={approval.id}
        requestNumber={approval.request_number}
        onActionComplete={handleActionComplete}
        currentApprovers={approval.current_approvers}
        isParallelStage={approval.current_stage_is_parallel}
        parallelProgress={
          approval.parallel_approvals_received !== undefined && approval.parallel_approvals_required !== undefined
            ? {
              received: approval.parallel_approvals_received,
              required: approval.parallel_approvals_required,
            }
            : undefined
        }
      />
    </div>
  );
}

