import React from 'react';
import { CheckCircle2, Circle, Clock, Users, XCircle } from 'lucide-react';
import { format } from 'date-fns';

interface TimelineStage {
  id: string;
  stage_name: string;
  stage_order: number;
  status: 'completed' | 'current' | 'pending' | 'partial';
  actioned_by?: string;
  actioned_at?: string;
  comments?: string;
  action?: string;
}

interface StageGroup {
  stageOrder: number;
  isParallel: boolean;
  groupStatus: 'completed' | 'current' | 'pending' | 'partial';
  stages: TimelineStage[];
  completedCount: number;
  totalCount: number;
}

interface ApprovalTimelineProps {
  stages: StageGroup[];
  submittedBy: string;
  submittedAt: string;
}

export function ApprovalTimeline({ stages, submittedBy, submittedAt }: ApprovalTimelineProps) {
  const getGroupIcon = (status: StageGroup['groupStatus'], isParallel: boolean) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-5 h-5 text-emerald-600 flex-shrink-0" />;
      case 'partial':
        return <Clock className="w-5 h-5 text-amber-600 flex-shrink-0" />;
      case 'current':
        return isParallel ? <Users className="w-5 h-5 text-blue-600 flex-shrink-0" /> : <Clock className="w-5 h-5 text-blue-600 flex-shrink-0" />;
      case 'pending':
        return <Circle className="w-5 h-5 text-gray-400 flex-shrink-0" />;
    }
  };

  const getGroupBackground = (status: StageGroup['groupStatus']) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-50';
      case 'partial':
        return 'bg-amber-50';
      case 'current':
        return 'bg-blue-50';
      case 'pending':
        return 'bg-gray-50';
    }
  };

  const getStageTextColor = (status: StageGroup['groupStatus']) => {
    switch (status) {
      case 'completed':
        return 'text-gray-900';
      case 'partial':
      case 'current':
        return 'text-gray-900';
      case 'pending':
        return 'text-gray-500';
    }
  };

  const getStageIcon = (status: TimelineStage['status'], action?: string) => {
    if (status === 'completed') {
      if (action?.toLowerCase() === 'reject') {
        return <XCircle className="w-4 h-4 text-red-600" />;
      }
      return <CheckCircle2 className="w-4 h-4 text-emerald-600" />;
    }
    
    switch (status) {
      case 'current':
        return <Clock className="w-4 h-4 text-blue-600" />;
      case 'pending':
        return <Circle className="w-4 h-4 text-gray-400" />;
      default:
        return <Circle className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="relative">
      <ul className="space-y-6">
        {/* Submitted stage */}
        <li className="relative flex gap-x-3">
          {stages.length > 0 && (
            <div className="absolute left-5 top-5 -bottom-6 w-0.5 bg-gray-200" aria-hidden="true" />
          )}
          <div className="relative flex h-10 w-10 flex-none items-center justify-center rounded-full bg-emerald-50">
            <CheckCircle2 className="w-5 h-5 text-emerald-600" />
          </div>
          <div className="flex-auto py-0.5">
            <h4 className="text-sm font-semibold text-gray-900">Submitted</h4>
            <p className="mt-0.5 text-sm text-gray-500">
              By {submittedBy} on {format(new Date(submittedAt), 'MMM d, yyyy')}
            </p>
          </div>
        </li>

        {/* Workflow stage groups */}
        {stages.length > 0 ? stages.map((group, groupIdx) => {
          const isLast = groupIdx === stages.length - 1;
          
          return (
            <li key={`group-${group.stageOrder}`} className="relative flex gap-x-3">
              {!isLast && (
                <div className="absolute left-5 top-5 -bottom-6 w-0.5 bg-gray-200" aria-hidden="true" />
              )}
              <div className={`relative flex h-10 w-10 flex-none items-center justify-center rounded-full ${getGroupBackground(group.groupStatus)}`}>
                {getGroupIcon(group.groupStatus, group.isParallel)}
              </div>
              <div className="flex-auto py-0.5">
                <div className="flex items-center gap-2">
                  <h4 className={`text-sm font-semibold ${getStageTextColor(group.groupStatus)}`}>
                    Stage {group.stageOrder}
                    {group.isParallel && ` (${group.completedCount}/${group.totalCount} completed)`}
                  </h4>
                  {group.isParallel && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Parallel
                    </span>
                  )}
                </div>

                {/* Status description */}
                {group.groupStatus === 'completed' && (
                  <p className="mt-0.5 text-sm text-gray-500">All approvals completed</p>
                )}
                {group.groupStatus === 'partial' && (
                  <p className="mt-0.5 text-sm text-gray-500">
                    Partial approval ({group.completedCount} of {group.totalCount} completed)
                  </p>
                )}
                {group.groupStatus === 'current' && !group.isParallel && (
                  <p className="mt-0.5 text-sm text-gray-500">Awaiting approval</p>
                )}
                {group.groupStatus === 'current' && group.isParallel && (
                  <p className="mt-0.5 text-sm text-gray-500">Awaiting parallel approvals</p>
                )}
                {group.groupStatus === 'pending' && (
                  <p className="mt-0.5 text-sm text-gray-500">Pending</p>
                )}

                {/* Individual stages in parallel */}
                {group.isParallel ? (
                  <div className="mt-3 space-y-2">
                    {group.stages.map((stage) => (
                      <div key={stage.id} className="flex items-start gap-3 p-3 bg-white rounded-lg border border-gray-100">
                        <div className="flex-shrink-0 mt-0.5">
                          {getStageIcon(stage.status, stage.action)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h5 className="text-sm font-medium text-gray-900">{stage.stage_name}</h5>
                          {stage.status === 'completed' && stage.actioned_by && stage.actioned_at && (
                            <p className="mt-1 text-xs text-gray-500">
                              {stage.action?.toLowerCase() === 'reject' ? 'Rejected' : 'Approved'} by {stage.actioned_by} on {format(new Date(stage.actioned_at), 'MMM d, yyyy')}
                            </p>
                          )}
                          {stage.status === 'current' && (
                            <p className="mt-1 text-xs text-gray-500">Awaiting approval</p>
                          )}
                          {stage.status === 'pending' && (
                            <p className="mt-1 text-xs text-gray-500">Pending</p>
                          )}
                          {stage.comments && (
                            <p className="mt-2 text-xs text-gray-600 italic break-words">"{stage.comments}"</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  /* Single stage display */
                  <div className="mt-2">
                    <h5 className="text-sm font-medium text-gray-700">{group.stages[0]?.stage_name}</h5>
                    {group.stages[0]?.status === 'completed' && group.stages[0]?.actioned_by && group.stages[0]?.actioned_at && (
                      <p className="mt-1 text-sm text-gray-500">
                        {group.stages[0].action?.toLowerCase() === 'reject' ? 'Rejected' : 'Approved'} by {group.stages[0].actioned_by} on {format(new Date(group.stages[0].actioned_at), 'MMM d, yyyy')}
                      </p>
                    )}
                    {group.stages[0]?.comments && (
                      <p className="mt-2 text-sm text-gray-600 italic break-words">"{group.stages[0].comments}"</p>
                    )}
                  </div>
                )}
              </div>
            </li>
          );
        }) : (
          <li className="relative flex gap-x-3">
            <div className="relative flex h-10 w-10 flex-none items-center justify-center rounded-full bg-gray-50">
              <Circle className="w-5 h-5 text-gray-400" />
            </div>
            <div className="flex-auto py-0.5">
              <h4 className="text-sm font-semibold text-gray-500">
                No workflow stages configured
              </h4>
              <p className="mt-0.5 text-sm text-gray-400">
                This approval request has no defined workflow stages.
              </p>
            </div>
          </li>
        )}
      </ul>
    </div>
  );
}