"use client"

import { Row } from "@tanstack/react-table"
import { MoreHorizontal, Eye, CheckCircle, XCircle, MessageSquare } from "lucide-react"
import { useState } from "react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"

import { ApprovalTableRow } from "@/types/approvals/approval.types"
import { ApprovalService } from "@/services/approvals/approval.service"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const approval = row.original as ApprovalTableRow
  const router = useRouter()
  const { toast } = useToast()
  
  const [showApprovalDialog, setShowApprovalDialog] = useState(false)
  const [showRejectionDialog, setShowRejectionDialog] = useState(false)
  const [comments, setComments] = useState("")
  const [loading, setLoading] = useState(false)

  const handleView = () => {
    // Navigate to approval details page
    router.push(`/approvals/${approval.id}`)
  }

  const handleApprove = async () => {
    setLoading(true)
    try {
      const result = await ApprovalService.submitApprovalDecision({
        requestId: approval.id,
        action: 'approve',
        comments: comments || undefined
      })

      if (result.success) {
        toast({
          title: "Success",
          description: "Approval request approved successfully"
        })
        setShowApprovalDialog(false)
        setComments("")
        // Refresh the page to update the data
        router.refresh()
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error || 'Failed to approve request'
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleReject = async () => {
    if (!comments.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Comments are required when rejecting a request"
      })
      return
    }

    setLoading(true)
    try {
      const result = await ApprovalService.submitApprovalDecision({
        requestId: approval.id,
        action: 'reject',
        comments: comments
      })

      if (result.success) {
        toast({
          title: "Success",
          description: "Approval request rejected successfully"
        })
        setShowRejectionDialog(false)
        setComments("")
        // Refresh the page to update the data
        router.refresh()
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error || 'Failed to reject request'
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    } finally {
      setLoading(false)
    }
  }

  const canApprove = approval.status === 'pending'

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          <DropdownMenuItem onClick={handleView}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
          
          {canApprove && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => setShowApprovalDialog(true)}
                className="text-green-600 focus:text-green-600"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => setShowRejectionDialog(true)}
                className="text-red-600 focus:text-red-600"
              >
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Approval Dialog */}
      <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Approve Request</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve request {approval.request_number}?
              You can add optional comments below.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="approval-comments">Comments (Optional)</Label>
              <Textarea
                id="approval-comments"
                placeholder="Add any comments for this approval..."
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowApprovalDialog(false)
                setComments("")
              }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleApprove}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700"
            >
              {loading ? (
                <>
                  <span className="animate-spin mr-2">⭐</span>
                  Approving...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={showRejectionDialog} onOpenChange={setShowRejectionDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reject Request</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject request {approval.request_number}?
              Please provide a reason for rejection.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="rejection-comments">Reason for Rejection *</Label>
              <Textarea
                id="rejection-comments"
                placeholder="Please provide a reason for rejecting this request..."
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                rows={4}
                required
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRejectionDialog(false)
                setComments("")
              }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleReject}
              disabled={loading}
              variant="destructive"
            >
              {loading ? (
                <>
                  <span className="animate-spin mr-2">⭐</span>
                  Rejecting...
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}