"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, XCircle, MessageSquare, UserCheck, Loader2, Users } from "lucide-react";
import { ApprovalService } from "@/services/approvals/approval.service";
import { createClient } from '@/utils/supabase/Client';
import type { CurrentApprover } from "@/types/approvals/approval.types";

interface ApprovalActionsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  requestId: string;
  requestNumber: string;
  onActionComplete: (action: string, result: any) => void;
  currentApprovers?: CurrentApprover[]; // For multi-role support
  isParallelStage?: boolean;
  parallelProgress?: {
    received: number;
    required: number;
  };
}

type ApprovalAction = 'approve' | 'reject' | 'request_info' | 'delegate';

interface ActionConfig {
  id: ApprovalAction;
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  requiresComments: boolean;
  requiresDelegate?: boolean;
}

const actionConfigs: ActionConfig[] = [
  {
    id: 'approve',
    label: 'Approve',
    description: 'Approve this request and move it forward',
    icon: <CheckCircle className="h-5 w-5" />,
    color: 'text-emerald-600',
    bgColor: 'hover:border-emerald-200',
    requiresComments: false,
  },
  {
    id: 'reject',
    label: 'Reject',
    description: 'Reject this request with explanation',
    icon: <XCircle className="h-5 w-5" />,
    color: 'text-red-600',
    bgColor: 'hover:border-red-200',
    requiresComments: true,
  },
  // {
  //   id: 'request_info',
  //   label: 'Request Info',
  //   description: 'Ask for additional information',
  //   icon: <MessageSquare className="h-5 w-5" />,
  //   color: 'text-amber-600',
  //   bgColor: 'hover:border-amber-200',
  //   requiresComments: true,
  // },
  // {
  //   id: 'delegate',
  //   label: 'Delegate',
  //   description: 'Transfer to another approver',
  //   icon: <UserCheck className="h-5 w-5" />,
  //   color: 'text-blue-600',
  //   bgColor: 'hover:border-blue-200',
  //   requiresComments: false,
  //   requiresDelegate: true,
  // },
];

export function ApprovalActionsDialog({
  isOpen,
  onClose,
  requestId,
  requestNumber,
  onActionComplete,
  currentApprovers = [],
  isParallelStage = false,
  parallelProgress
}: ApprovalActionsDialogProps) {
  const [selectedAction, setSelectedAction] = useState<ApprovalAction | null>(null);
  const [selectedStageId, setSelectedStageId] = useState<string>('');
  const [comments, setComments] = useState('');
  const [delegateUserId, setDelegateUserId] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<Array<{id: string, name: string, email: string}>>([]);
  const [userApprovalOptions, setUserApprovalOptions] = useState<CurrentApprover[]>([]);

  // Load user approval options and delegation users
  React.useEffect(() => {
    if (isOpen) {
      loadUserApprovalOptions();
    }
    
    if (selectedAction === 'delegate') {
      setAvailableUsers([
        { id: 'user-1', name: 'Sarah Johnson', email: '<EMAIL>' },
        { id: 'user-2', name: 'Mike Chen', email: '<EMAIL>' },
        { id: 'user-3', name: 'Lisa Rodriguez', email: '<EMAIL>' },
      ]);
    }
  }, [selectedAction, isOpen, requestId]);

  const loadUserApprovalOptions = async () => {
    try {
      const supabase = await createClient();
      
      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get approval options for this user
      const options = await ApprovalService.getUserApprovalOptions(requestId, user.id);
      setUserApprovalOptions(options);

      // Auto-select stage if user has only one option
      if (options.length === 1) {
        setSelectedStageId(options[0].stage_id);
      }
    } catch (error) {
      console.error('Error loading user approval options:', error);
    }
  };

  const handleAction = async () => {
    if (!selectedAction) return;

    const config = actionConfigs.find(a => a.id === selectedAction);
    if (!config) return;

    // Validation
    if (userApprovalOptions.length > 1 && !selectedStageId) {
      alert('Please select which role you are acting as');
      return;
    }

    if (config.requiresComments && !comments.trim()) {
      alert('Comments are required for this action');
      return;
    }

    if (config.requiresDelegate && !delegateUserId) {
      alert('Please select a user to delegate to');
      return;
    }

    setIsProcessing(true);

    try {
      const result = await ApprovalService.processApprovalAction({
        requestId,
        userId: '',
        action: selectedAction,
        stageId: selectedStageId, // Now explicitly provided
        comments: selectedAction === 'delegate' 
          ? `${comments.trim() || ''} [Delegate to: ${availableUsers.find(u => u.id === delegateUserId)?.name || delegateUserId}]`
          : comments.trim() || undefined,
      });

      if (result.success) {
        onActionComplete(selectedAction, result.data);
        onClose();
        resetForm();
      } else {
        alert(result.error || 'Failed to process approval action');
      }
    } catch (error) {
      console.error('Error processing approval action:', error);
      alert('An error occurred while processing the action');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setSelectedAction(null);
    setComments('');
    setDelegateUserId('');
    setSelectedStageId('');
    setUserApprovalOptions([]);
  };

  const handleClose = () => {
    if (!isProcessing) {
      onClose();
      resetForm();
    }
  };

  const selectedConfig = selectedAction ? actionConfigs.find(a => a.id === selectedAction) : null;

  const getButtonColor = (actionId: ApprovalAction) => {
    switch (actionId) {
      case 'approve':
        return 'bg-emerald-600 hover:bg-emerald-700 text-white';
      case 'reject':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'request_info':
        return 'bg-amber-600 hover:bg-amber-700 text-white';
      case 'delegate':
        return 'bg-blue-600 hover:bg-blue-700 text-white';
      default:
        return 'bg-gray-600 hover:bg-gray-700 text-white';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold flex items-center gap-2">
            Take Action
            {isParallelStage && <Users className="h-4 w-4 text-blue-600" />}
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Choose an action for request {requestNumber}
            {isParallelStage && parallelProgress && (
              <div className="mt-1 text-blue-600 font-medium">
                Parallel Stage: {parallelProgress.received} of {parallelProgress.required} approvals received
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {!selectedAction ? (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Select Action</Label>
              <div className="grid grid-cols-2 gap-3">
                {actionConfigs.map((config) => (
                  <Card 
                    key={config.id}
                    className={`cursor-pointer transition-all hover:shadow-sm bg-background border ${config.bgColor}`}
                    onClick={() => setSelectedAction(config.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex flex-col items-center text-center space-y-2">
                        <div className={`${config.color}`}>
                          {config.icon}
                        </div>
                        <div>
                          <div className={`text-sm font-medium ${config.color}`}>
                            {config.label}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            {config.description}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Selected Action Header */}
              <div className="p-3 rounded-lg border bg-muted/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`${selectedConfig?.color}`}>
                      {selectedConfig?.icon}
                    </div>
                    <div>
                      <div className={`text-sm font-medium ${selectedConfig?.color}`}>
                        {selectedConfig?.label}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {selectedConfig?.description}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedAction(null)}
                    disabled={isProcessing}
                  >
                    Change
                  </Button>
                </div>
              </div>

              {/* Multi-role selection for users with multiple approval roles */}
              {userApprovalOptions.length > 1 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Approving As <span className="text-red-500">*</span>
                  </Label>
                  <Select value={selectedStageId} onValueChange={setSelectedStageId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose which role you are acting as">
                        {selectedStageId && (() => {
                          const selectedOption = userApprovalOptions.find(opt => opt.stage_id === selectedStageId);
                          return selectedOption ? (
                            <div className="flex items-center space-x-2">
                              <div className="w-4 h-4 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-xs font-medium text-blue-600">
                                  {selectedOption.approver_type.charAt(0)}
                                </span>
                              </div>
                              <span className="text-sm">{selectedOption.stage_name}</span>
                            </div>
                          ) : null;
                        })()}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {userApprovalOptions.map((option) => (
                        <SelectItem key={option.stage_id} value={option.stage_id}>
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-xs font-medium text-blue-600">
                                {option.approver_type.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <div className="text-sm font-medium">{option.stage_name}</div>
                              <div className="text-xs text-muted-foreground">{option.approver_type}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    You have multiple roles that can approve this stage. Select which capacity you are acting in.
                  </p>
                </div>
              )}

              {/* Delegate User Selection */}
              {selectedAction === 'delegate' && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Delegate To <span className="text-red-500">*</span>
                  </Label>
                  <Select value={delegateUserId} onValueChange={setDelegateUserId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a user" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableUsers.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-xs font-medium text-blue-600">
                                {user.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                            <div>
                              <div className="text-sm font-medium">{user.name}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Comments */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Comments
                  {selectedConfig?.requiresComments && <span className="text-red-500 ml-1">*</span>}
                  {!selectedConfig?.requiresComments && <span className="text-muted-foreground font-normal ml-1">(Optional)</span>}
                </Label>
                <Textarea
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  placeholder={
                    selectedAction === 'approve' ? 'Add any notes about your approval...' :
                    selectedAction === 'reject' ? 'Please explain why you are rejecting this request...' :
                    selectedAction === 'request_info' ? 'What additional information do you need?' :
                    selectedAction === 'delegate' ? 'Optional: Explain why you are delegating...' :
                    'Add your comments...'
                  }
                  rows={3}
                  disabled={isProcessing}
                  className="resize-none"
                />
                {selectedConfig?.requiresComments && (
                  <p className="text-xs text-muted-foreground">
                    <span className="font-medium">Required:</span> Please provide comments for this action.
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <div className="flex space-x-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isProcessing}
              className="flex-1 sm:flex-none"
            >
              Cancel
            </Button>
            {selectedAction && (
              <Button
                className={`flex-1 sm:flex-none ${getButtonColor(selectedAction)}`}
                onClick={handleAction}
                disabled={isProcessing}
              >
                {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isProcessing ? 'Processing...' : selectedConfig?.label}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}