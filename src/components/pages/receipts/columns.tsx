"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { Checkbox } from "@/components/ui/checkbox";
import { receiptsInterface } from "@/types/finance/receipts.types";

export const useReceiptColumns = () => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

    const toggleRowSelection = (receiptId: string) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.has(receiptId)) {
                newSelectedRows.delete(receiptId);
            } else {
                newSelectedRows.add(receiptId);
            }
            return newSelectedRows;
        });
    };

    const toggleAllRowsSelection = (rows: any[]) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.size === rows.length) {
                newSelectedRows.clear();
            } else {
                rows.forEach((row) => newSelectedRows.add(row.original.receipt_id));
            }
            return newSelectedRows;
        });
    };

    const columns: ColumnDef<receiptsInterface>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <div className="flex justify-center items-center">
                    <Checkbox
                        checked={
                            selectedRows.size > 0
                                ? selectedRows.size === table.getRowModel().rows.length
                                    ? true
                                    : "indeterminate"
                                : false
                        }
                        onCheckedChange={() => {
                            toggleAllRowsSelection(table.getRowModel().rows);
                        }}
                        aria-label="Select all"
                        className="translate-y-[2px]"
                    />
                </div>
            ),
            cell: ({ row }) => (
                <div className="flex justify-center items-center">
                    <Checkbox
                        checked={selectedRows.has(row.original.receipt_id)}
                        onCheckedChange={() => toggleRowSelection(row.original.receipt_id)}
                        aria-label={`Select ${row.original.receipt_id}`}
                        className="translate-y-[2px]"
                    />
                </div>
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "receipt_id",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="No." />
            ),
            cell: ({ row, table }) => {
                const { pageIndex, pageSize } = table.getState().pagination;
                const rowIndex = row.index;
                const number = pageIndex * pageSize + rowIndex + 1;

                return (
                    <div className="flex text-center">
                        <span data-receipt-id={row.original.receipt_id}>{number}</span>
                    </div>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "receipt_number",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Receipt Number" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("receipt_number")}</div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Receipt Number" },
        },
        {
            accessorKey: "invoice_number",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Invoice Number" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("invoice_number")}</div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Invoice Number" },
        },
        {
            accessorKey: "payment_reference",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Payment Reference" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    <div className="flex items-center">{row.getValue("payment_reference")}</div>
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Payment Reference" },
        },
        {
            accessorKey: "payment_method",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Payment Method" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    <div className="flex items-center">{row.getValue("payment_method")}</div>
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Payment Method" },
        },
        {
            accessorKey: "payment_date",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Payment Date" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    {row.getValue("payment_date")}
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Payment Date" },
        },
        {
            accessorKey: "payment_by",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Payment By" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    <div className="flex items-center">{row.getValue("payment_by")}</div>
                </div>
            ),
            sortingFn: "alphanumeric",
            meta: { label: "Payment By" },
        },
        {
            accessorKey: "payment_status",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Payment Status" />
            ),
            cell: ({ row }) => {
                const status = row.getValue("payment_status") as string;

                const getReceiptsStatusStyle = (status: string) => {
                    switch (status) {
                        case 'SUCCESS':
                            return 'bg-green-200 text-black';
                        case 'PENDING':
                            return 'bg-orange-200 text-black';
                        case 'FAILED':
                        case 'VOID':
                            return 'bg-red-200 text-black';
                        default:
                            return 'bg-gray-200 text-black';
                    }
                };

                return (
                    <div className="flex items-center">
                        <button
                            className={`px-4 py-1 rounded-3xl ${getReceiptsStatusStyle(status)}`}
                            style={{ textTransform: "capitalize" }}
                        >
                            {status.toLowerCase()}
                        </button>
                    </div>
                );
            },
            sortingFn: "text",
            meta: { label: "Payment Status" },
        }
    ];

    return { columns, selectedRows, setSelectedRows };
};