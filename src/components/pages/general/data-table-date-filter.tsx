"use client"

import * as React from "react"
import { format } from "date-fns"
import { CalendarIcon, X } from 'lucide-react'
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface TableDateRangePickerProps {
    className?: string
    onChange: (date: DateRange | undefined) => void
    value?: DateRange
}

export function TableDateRangePicker({
    className,
    onChange,
    value,
}: TableDateRangePickerProps) {
    const [date, setDate] = React.useState<DateRange | undefined>(value)

    React.useEffect(() => {
        setDate(value)
    }, [value])

    const handleDateChange = (newDate: DateRange | undefined) => {
        setDate(newDate)
        onChange(newDate)
    }

    const handleClear = () => {
        setDate(undefined)
        onChange(undefined)
    }

    return (
        <div className={cn("grid gap-2 relative", className)}>

            <Popover>
                <PopoverTrigger asChild>
                    <Button
                        id="date"
                        variant="outline"
                        size="sm"
                        className="h-8 border-dashed pr-10 flex items-center justify-between w-full relative"
                    >
                        <div className="flex items-center">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date?.from ? (
                                date.to ? (
                                    <>
                                        {format(date.from, "LLL dd, y")} -{" "}
                                        {format(date.to, "LLL dd, y")}
                                    </>
                                ) : (
                                    format(date.from, "LLL dd, y")
                                )
                            ) : (
                                <span>Pick a date range</span>
                            )}
                        </div>

                        {date && (
                            <button
                                className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-white hover:bg-gray-200 focus:ring-2 focus:ring-gray-400"
                                onClick={handleClear}
                            >
                                <X className="h-4 w-4 text-gray-500" />
                                <span className="sr-only">Clear date range</span>
                            </button>
                        )}
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                        mode="range"
                        defaultMonth={date?.from}
                        selected={date}
                        onSelect={handleDateChange}
                        numberOfMonths={2}
                    />
                </PopoverContent>
            </Popover>
        </div>
    )
}