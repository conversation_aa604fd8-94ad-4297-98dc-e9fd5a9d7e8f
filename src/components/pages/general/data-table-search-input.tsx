import { useEffect, useRef, useState } from "react";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";

interface SearchInputProps {
    onSearch: (query: string) => void;
    value?: string;
    placeholder?: string;
    debounceDelay?: number;
}

const SearchInput: React.FC<SearchInputProps> = ({
    onSearch,
    value = "",
    placeholder = "Search here",
    debounceDelay = 1000,
}) => {
    const [inputValue, setInputValue] = useState(value);
    const [isTyping, setIsTyping] = useState(false);
    const debounceTimer = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        setInputValue(value);
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setInputValue(newValue);
        setIsTyping(true);

        if (debounceTimer.current) {
            clearTimeout(debounceTimer.current);
        }

        debounceTimer.current = setTimeout(() => {
            setIsTyping(false);
            const trimmed = newValue.trim();
            if (trimmed !== "" || newValue === "") {
                onSearch(trimmed);
            }
        }, debounceDelay);
    };

    return (
        <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                {isTyping ? (
                    <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                ) : (
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-500" />
                )}
            </div>
            <Input
                placeholder={placeholder}
                value={inputValue}
                onChange={handleChange}
                className="h-8 w-[150px] lg:w-[250px] pl-10"
            />
        </div>
    );
};

export default SearchInput;