"use client"

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Supplier, SupplierStatus } from "@/types/purchase-orders/purchase-orders.types";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { SupplierStatusBadge } from "@/components/purchase-orders/ui/supplier-status-badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableRowActions } from "@/components/pages/suppliers/data-table-row-actions";

interface useSuppliersColumnsProps {
    loadSuppliers: () => void;
}

export const useSuppliersColumns = ({
    loadSuppliers,
}: useSuppliersColumnsProps) => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

    const toggleRowSelection = (supplierId: string) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.has(supplierId)) {
                newSelectedRows.delete(supplierId);
            } else {
                newSelectedRows.add(supplierId);
            }
            return newSelectedRows;
        });
    };

    const toggleAllRowsSelection = (rows: any[]) => {
        setSelectedRows((prevSelectedRows) => {
            const newSelectedRows = new Set(prevSelectedRows);
            if (newSelectedRows.size === rows.length) {
                newSelectedRows.clear();
            } else {
                rows.forEach((row) => newSelectedRows.add(row.original.id));
            }
            return newSelectedRows;
        });
    };

    const columns: ColumnDef<Supplier>[] = [
        // {
        //     id: "select",
        //     header: ({ table }) => (
        //         <div className="flex justify-center items-center">
        //             <Checkbox
        //                 checked={
        //                     selectedRows.size > 0
        //                         ? selectedRows.size === table.getRowModel().rows.length
        //                             ? true
        //                             : "indeterminate"
        //                         : false
        //                 }
        //                 onCheckedChange={() => {
        //                     toggleAllRowsSelection(table.getRowModel().rows);
        //                 }}
        //                 aria-label="Select all"
        //                 className="translate-y-[2px]"
        //             />
        //         </div>
        //     ),
        //     cell: ({ row }) => (
        //         <div className="flex justify-center items-center">
        //             <Checkbox
        //                 checked={selectedRows.has(row.original.id)}
        //                 onCheckedChange={() => toggleRowSelection(row.original.id)}
        //                 aria-label={`Select ${row.original.id}`}
        //                 className="translate-y-[2px]"
        //             />
        //         </div>
        //     ),
        //     enableSorting: false,
        //     enableHiding: false,
        // },
        {
            accessorKey: "supplier_code",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Supplier Code" />
            ),
            cell: ({ row }) => <div className="flex items-center">{row.getValue("supplier_code")}</div>,
            sortingFn: 'alphanumeric',
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Name" />
            ),
            cell: ({ row }) => <div className="flex items-center">{row.getValue("name")}</div>,
            sortingFn: 'alphanumeric',
            meta: { label: "Name" },
        },
        {
            accessorKey: "supplier_type",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Type" />
            ),
            cell: ({ row }) => <div className="flex items-center truncate">{row.getValue("supplier_type")}</div>,
            sortingFn: 'alphanumeric',
            meta: { label: "Type" },
        },
        {
            accessorKey: "status",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Status" />
            ),
            cell: ({ row }) => {
                const status = row.getValue("status") as SupplierStatus;
                return <SupplierStatusBadge status={status} />;
            },
            sortingFn: 'text',
            meta: { label: "Status" },
        },
        {
            accessorKey: "email",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Email" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("email")}</div>
            ),
            sortingFn: 'alphanumeric',
            meta: { label: "Email" },
        },
        {
            accessorKey: "currency_code",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Currency code" />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">{row.getValue("currency_code")}</div>
            ),
            sortingFn: 'alphanumeric',
            meta: { label: "Currency code" },
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} onSuccess={loadSuppliers} />,
        },
    ];

    return { columns, selectedRows, setSelectedRows };
};