"use client";

import { useState } from "react";
import { Row } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AlertCircle, Ban, CircleCheck, CircleX, Edit, Eye, MoreHorizontal } from "lucide-react";

import { Supplier, SupplierStatus } from "@/types/purchase-orders/purchase-orders.types";
import { SupplierFormDialog } from "@/components/purchase-orders/forms/supplier-form-dialog";
import { SupplierDetailsDialog } from "@/components/purchase-orders/dialogs/supplier-details-dialog";
import { SupplierStatusChangeDialog } from "@/components/purchase-orders/dialogs/supplier-status-change-dialog";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
  onSuccess: () => void;
}

export function DataTableRowActions<TData>({
  row,
  onSuccess
}: DataTableRowActionsProps<TData>) {
  const supplier = row.original as Supplier;

  const [menuOpen, setMenuOpen] = useState(false);
  const [showSupplierDialog, setShowSupplierDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showStatusChangeDialog, setShowStatusChangeDialog] = useState(false);
  const [newStatus, setNewStatus] = useState<SupplierStatus>(null);

  const handleStatus = (status: SupplierStatus) => {
    setMenuOpen(false);
    setNewStatus(status);
    setShowStatusChangeDialog(true);
  }

  return (
    <>
      <DropdownMenu open={menuOpen} onOpenChange={setMenuOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost">
            <MoreHorizontal />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => {
            setMenuOpen(false);
            setShowSupplierDialog(true)
          }}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Supplier
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => {
            setMenuOpen(false);
            setShowDetailsDialog(true)
          }}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>Change Status</DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              {supplier.status !== "ACTIVE" && (
                <DropdownMenuItem onClick={() => handleStatus("ACTIVE")}>
                  <CircleCheck className="mr-2 h-4 w-4 text-green-500" />
                  Activate
                </DropdownMenuItem>
              )}
              {supplier.status !== "INACTIVE" && (
                <DropdownMenuItem onClick={() => handleStatus("INACTIVE")}>
                  <CircleX className="mr-2 h-4 w-4 text-gray-500" />
                  Deactivate
                </DropdownMenuItem>
              )}
              {supplier.status !== "SUSPENDED" && (
                <DropdownMenuItem onClick={() => handleStatus("SUSPENDED")}>
                  <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                  Suspend
                </DropdownMenuItem>
              )}
              {supplier.status !== "BLACKLISTED" && (
                <DropdownMenuItem onClick={() => handleStatus("BLACKLISTED")}>
                  <Ban className="mr-2 h-4 w-4 text-red-500" />
                  Blacklist
                </DropdownMenuItem>
              )}
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        </DropdownMenuContent>
      </DropdownMenu>

      {showSupplierDialog && (
        <SupplierFormDialog
          open={showSupplierDialog}
          onOpenChange={setShowSupplierDialog}
          supplierId={supplier.id}
          onSuccess={onSuccess}
        />
      )}

      {showDetailsDialog && (
        <SupplierDetailsDialog
          open={showDetailsDialog}
          onOpenChange={setShowDetailsDialog}
          supplierId={supplier.id}
        />
      )}

      {showStatusChangeDialog && (
        <SupplierStatusChangeDialog
          open={showStatusChangeDialog}
          onOpenChange={setShowStatusChangeDialog}
          supplier={supplier}
          newStatus={newStatus}
          onSuccess={onSuccess}
        />
      )}
    </>
  );
}
