"use client";

import { Row } from "@tanstack/react-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import { ProgrammeParticipantInterface } from "@/types/programmes/programme.types";
import { Eye } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const Participant = row.original as ProgrammeParticipantInterface;

  return (
    <Sheet>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <SheetTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon"
                className="h-8 w-8 p-0 hover:bg-muted"
              >
                <Eye className="h-4 w-4" />
                <span className="sr-only">View participant details</span>
              </Button>
            </SheetTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>View details</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <SheetContent className="min-w-[40%]">
        <SheetHeader>
          <SheetTitle>Participant Details</SheetTitle>
          <SheetDescription>
            Below are the details of the participant. This information is read-only.
          </SheetDescription>
        </SheetHeader>
        <div className="overflow-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
            {Participant.salutation && (
              <div>
                <p className="text-sm font-medium leading-none">Salutation</p>
                <p className="text-sm text-muted-foreground">{Participant.salutation}</p>
              </div>
            )}

            {Participant.name && (
              <div>
                <p className="text-sm font-medium leading-none">Name</p>
                <p className="text-sm text-muted-foreground">{Participant.name}</p>
              </div>
            )}

            {Participant.email && (
              <div>
                <p className="text-sm font-medium leading-none">Email</p>
                <p className="text-sm text-muted-foreground">{Participant.email}</p>
              </div>
            )}

            {Participant.designation && (
              <div>
                <p className="text-sm font-medium leading-none">Designation</p>
                <p className="text-sm text-muted-foreground">{Participant.designation}</p>
              </div>
            )}

            {Participant.contact_no && (
              <div>
                <p className="text-sm font-medium leading-none">Contact No</p>
                <p className="text-sm text-muted-foreground">{Participant.contact_no}</p>
              </div>
            )}

            {Participant.nationality && (
              <div>
                <p className="text-sm font-medium leading-none">Nationality</p>
                <p className="text-sm text-muted-foreground">{Participant.nationality}</p>
              </div>
            )}

            {Participant.identification_type && (
              <div>
                <p className="text-sm font-medium leading-none">Identification Type</p>
                <p className="text-sm text-muted-foreground">{Participant.identification_type}</p>
              </div>
            )}

            {Participant.identification_no && (
              <div>
                <p className="text-sm font-medium leading-none">Identification No</p>
                <p className="text-sm text-muted-foreground">{Participant.identification_no}</p>
              </div>
            )}

            {Participant.professional_id_type && (
              <div>
                <p className="text-sm font-medium leading-none">Professional ID Type</p>
                <p className="text-sm text-muted-foreground">{Participant.professional_id_type}</p>
              </div>
            )}

            {Participant.professional_id && (
              <div>
                <p className="text-sm font-medium leading-none">Professional ID</p>
                <p className="text-sm text-muted-foreground">{Participant.professional_id}</p>
              </div>
            )}

            {Participant.registration_type && (
              <div>
                <p className="text-sm font-medium leading-none">Registration Type</p>
                <p className="text-sm text-muted-foreground">{Participant.registration_type}</p>
              </div>
            )}

            {Participant.registration_status && (
              <div>
                <p className="text-sm font-medium leading-none">Registration Status</p>
                <p className="text-sm text-muted-foreground">{Participant.registration_status}</p>
              </div>
            )}

            {Participant.payment_status && (
              <div>
                <p className="text-sm font-medium leading-none">Payment Status</p>
                <p className="text-sm text-muted-foreground">{Participant.payment_status}</p>
              </div>
            )}

            {Participant.unit_price && (
              <div>
                <p className="text-sm font-medium leading-none">Unit Price</p>
                <p className="text-sm text-muted-foreground">{Participant.unit_price}</p>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}