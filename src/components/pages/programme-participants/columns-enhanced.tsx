"use client"

import { ColumnDef } from "@tanstack/react-table";
import { ProgrammeParticipantInterface } from "@/types/programmes/programme.types";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { DataTableRowActions } from "@/components/pages/programme-participants/data-table-row-actions-enhanced";
import { Badge } from "@/components/ui/badge";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

// Helper component for truncated text with tooltip
const TruncatedText = ({ text, maxLength = 25 }: { text: string; maxLength?: number }) => {
  const needsTruncation = text && text.length > maxLength;
  const truncatedText = needsTruncation ? `${text.substring(0, maxLength)}...` : text;

  if (!needsTruncation) {
    return <span>{text}</span>;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-default">{truncatedText}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Helper component for registration status badge
const RegistrationStatusBadge = ({ status }: { status: string }) => {
  const getStatusVariant = (status: string) => {
    switch (status?.toUpperCase()) {
      case "PENDING":
        return "warning";
      case "CONFIRMED":
        return "success";
      case "CANCELLED":
        return "secondary";
      case "ATTENDED":
        return "default";
      default:
        return "outline";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case "PENDING":
        return "bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-100";
      case "CONFIRMED":
        return "bg-green-100 text-green-800 border-green-200 hover:bg-green-100";
      case "CANCELLED":
        return "bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-100";
      case "ATTENDED":
        return "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-100";
      default:
        return "";
    }
  };

  return (
    <Badge 
      variant="outline" 
      className={cn("font-medium", getStatusColor(status))}
    >
      {status}
    </Badge>
  );
};

export const columnsEnhanced: ColumnDef<ProgrammeParticipantInterface>[] = [
  {
    accessorKey: "participant_id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row, table }) => {
      const { pageIndex, pageSize } = table.getState().pagination;
      const rowIndex = row.index;
      const number = pageIndex * pageSize + rowIndex + 1;

      return (
        <div className="flex text-center">
          <span data-user-id={row.original.participant_id} className="text-sm text-muted-foreground">{number}</span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: true,
    meta: { 
      label: "ID"
    },
  },
  // Group: Personal Information
  {
    accessorKey: "salutation",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Salutation" className="font-medium hidden sm:table-cell" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center font-medium hidden sm:table-cell">{row.getValue("salutation")}</div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Salutation"
    },
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" className="font-semibold" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        <span className="font-semibold text-foreground">
          <TruncatedText text={row.getValue("name")} maxLength={30} />
        </span>
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Name"
    },
  },
  // Group: Contact Information
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" className="font-semibold" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        <span className="text-sm">
          <TruncatedText text={row.getValue("email")} maxLength={25} />
        </span>
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Email"
    },
  },
  {
    accessorKey: "contact_no",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Contact No." className="hidden md:table-cell" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center hidden md:table-cell">
        <span className="text-sm">{row.getValue("contact_no")}</span>
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Contact No"
    },
  },
  // Hidden by default
  {
    accessorKey: "identification_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID Type" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center text-sm text-muted-foreground">
        {row.getValue("identification_type")}
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "ID Type"
    },
  },
  {
    accessorKey: "identification_no",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID No" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center text-sm text-muted-foreground">
        {row.getValue("identification_no")}
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "ID No"
    },
  },
  {
    accessorKey: "nationality",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nationality" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center text-sm text-muted-foreground">
        {row.getValue("nationality")}
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Nationality"
    },
  },
  // Group: Registration Information
  {
    accessorKey: "registration_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Type" className="hidden lg:table-cell" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center hidden lg:table-cell">
        <Badge variant="outline" className="font-medium">
          {row.getValue("registration_type")}
        </Badge>
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Registration Type"
    },
  },
  {
    accessorKey: "registration_status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" className="font-semibold" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center">
        <RegistrationStatusBadge status={row.getValue("registration_status")} />
      </div>
    ),
    sortingFn: "text",
    meta: { 
      label: "Status"
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
    meta: {
      label: "Actions"
    }
  },
];