"use client"

import { ColumnDef } from "@tanstack/react-table";
import { ProgrammeParticipantInterface } from "@/types/programmes/programme.types";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { DataTableRowActions } from "@/components/pages/programme-participants/data-table-row-actions";

export const columns: ColumnDef<ProgrammeParticipantInterface>[] = [
    {
        accessorKey: "participant_id",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="ID" />
        ),
        cell: ({ row, table }) => {
            const { pageIndex, pageSize } = table.getState().pagination;
            const rowIndex = row.index;
            const number = pageIndex * pageSize + rowIndex + 1;

            return (
                <div className="flex text-center">
                    <span data-user-id={row.original.participant_id}>{number}</span>
                </div>
            );
        },
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "salutation",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Salutation" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center">{row.getValue("salutation")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Salutation" },
    },
    {
        accessorKey: "name",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Name" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center truncate">{row.getValue("name")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Name" },
    },
    {
        accessorKey: "email",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Email" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center truncate">{row.getValue("email")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Email" },
    },
    {
        accessorKey: "contact_no",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Contact No." />
        ),
        cell: ({ row }) => (
            <div className="flex items-center truncate">{row.getValue("contact_no")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Contact No" },
    },
    {
        accessorKey: "identification_type",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Identification Type" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center truncate">{row.getValue("identification_type")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Identification Type" },
    },
    {
        accessorKey: "identification_no",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Identification No" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center truncate">{row.getValue("identification_no")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Identification No" },
    },
    {
        accessorKey: "nationality",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Nationality" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center truncate">{row.getValue("nationality")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Nationality" },
    },
    {
        accessorKey: "registration_type",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Registration Type" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center">{row.getValue("registration_type")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Registration Type" },
    },
    {
        accessorKey: "registration_status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Registration Status" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center">{row.getValue("registration_status")}</div>
        ),
        sortingFn: "text",
        meta: { label: "Registration Status" },
    },
    {
        id: "actions",
        cell: ({ row }) => <DataTableRowActions row={row} />,
    },
];