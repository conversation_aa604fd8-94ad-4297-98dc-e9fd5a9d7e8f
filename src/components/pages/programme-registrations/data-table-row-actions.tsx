"use client";

import { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ProgrammeRegistrationsInterface } from "@/types/programmes/programme.types";
import { ProgrammeRegistrationParticipants } from "@/components/programmes/edit-programme/registration-partificipants";
import { TransferRegistrationDialog } from "@/components/programmes/transfer-registration-dialog";
import { useEffect, useState } from "react";
import { getOptionLabel } from "@/services/system-options.service";
import { formatTimeStamp } from "@/lib/utils";
import { Eye, MoreHorizontal, ArrowRightLeft } from "lucide-react";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
  table?: any;
}

export function DataTableRowActions<TData>({
  row,
  table,
}: DataTableRowActionsProps<TData>) {
  const registration = row.original as ProgrammeRegistrationsInterface;
  const [addressCountry, setAddressCountry] = useState('--');
  const [isTransferDialogOpen, setIsTransferDialogOpen] = useState(false);
  const [isDetailsSheetOpen, setIsDetailsSheetOpen] = useState(false);

  const address = registration.contact_persons?.address;
  
  // Get programme info from table meta
  const programmeInfo = table?.options?.meta?.programmeInfo || {
    id: '',
    name: 'Current Programme',
    programme_code: 'N/A'
  };
  const onRefresh = table?.options?.meta?.onRefresh;

  useEffect(() => {
    const fetchCountryLabel = async () => {
      if (address?.country) {
        const label = await getOptionLabel(address.country, 'country')
        setAddressCountry(label)
      }
    }

    fetchCountryLabel()
  }, [address?.country])

  const handleTransferSuccess = () => {
    setIsTransferDialogOpen(false);
    if (onRefresh) {
      onRefresh();
    }
  };

  return (
    <>
      <div className="flex items-center gap-1">
        <Sheet open={isDetailsSheetOpen} onOpenChange={setIsDetailsSheetOpen}>
          <SheetTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon"
              className="h-8 w-8 hover:bg-gray-100"
              title="View registration details"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </SheetTrigger>
      <SheetContent className="flex flex-col sm:max-w-[700px]">
        <SheetHeader>
          <SheetTitle>Registration Details</SheetTitle>
          <SheetDescription>
            Below are the details of the registration. This information is read-only.
          </SheetDescription>
        </SheetHeader>
        <div className="overflow-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
            {registration.registration_type && (
              <div>
                <p className="text-sm font-medium leading-none">Registration Type</p>
                <p className="text-sm text-muted-foreground">{registration.registration_type}</p>
              </div>
            )}

            {registration.status && (
              <div>
                <p className="text-sm font-medium leading-none">Status</p>
                <p className="text-sm text-muted-foreground">{registration.status}</p>
              </div>
            )}

            {registration.total_amount != null && (
              <div>
                <p className="text-sm font-medium leading-none">Total Amount</p>
                <p className="text-sm text-muted-foreground">{registration.total_amount}</p>
              </div>
            )}

            {registration.currency && (
              <div>
                <p className="text-sm font-medium leading-none">Currency</p>
                <p className="text-sm text-muted-foreground">{registration.currency}</p>
              </div>
            )}

            {registration.payment_status && (
              <div>
                <p className="text-sm font-medium leading-none">Payment Status</p>
                <p className="text-sm text-muted-foreground">{registration.payment_status}</p>
              </div>
            )}

            {registration.payment_date && (
              <div>
                <p className="text-sm font-medium leading-none">Payment Date</p>
                <p className="text-sm text-muted-foreground">{formatTimeStamp(registration.payment_date)}</p>
              </div>
            )}

            {registration.remarks && (
              <div>
                <p className="text-sm font-medium leading-none">Remarks</p>
                <p className="text-sm text-muted-foreground">{registration.remarks}</p>
              </div>
            )}

            {registration.created_at && (
              <div>
                <p className="text-sm font-medium leading-none">Created At</p>
                <p className="text-sm text-muted-foreground">{formatTimeStamp(registration.created_at)}</p>
              </div>
            )}
          </div>

          <h4 className="font-medium">Contact Person</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
            {registration.contact_persons?.name && (
              <div>
                <p className="text-sm font-medium leading-none">Name</p>
                <p className="text-sm text-muted-foreground">{registration.contact_persons.name}</p>
              </div>
            )}

            {registration.contact_persons?.email && (
              <div>
                <p className="text-sm font-medium leading-none">Email</p>
                <p className="text-sm text-muted-foreground">{registration.contact_persons.email}</p>
              </div>
            )}

            {registration.contact_persons?.contact_no && (
              <div>
                <p className="text-sm font-medium leading-none">Contact No</p>
                <p className="text-sm text-muted-foreground">{registration.contact_persons.contact_no}</p>
              </div>
            )}

            {registration.contact_persons?.fax_no && (
              <div>
                <p className="text-sm font-medium leading-none">Fax No</p>
                <p className="text-sm text-muted-foreground">{registration.contact_persons.fax_no}</p>
              </div>
            )}

            {registration.contact_persons?.business_unit_code && (
              <div>
                <p className="text-sm font-medium leading-none">Business Unit Code</p>
                <p className="text-sm text-muted-foreground">{registration.contact_persons.business_unit_code}</p>
              </div>
            )}

            {address && (
              <div>
                <p className="text-sm font-medium leading-none">Address</p>
                <p className="text-sm text-muted-foreground">
                  {address.address_line_1 || "--"}
                  {address.address_line_2 && (
                    <>
                      , {address.address_line_2}
                    </>
                  )}
                  <br />
                  {address.city || "--"},{" "}
                  {address.state || "--"}{" "}
                  {address.postal_code || "--"}
                  <br />
                  {addressCountry}
                </p>
              </div>
            )}
          </div>

          <ProgrammeRegistrationParticipants programmeId={registration.id} />
        </div>
      </SheetContent>
    </Sheet>

    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 hover:bg-gray-100"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => setIsTransferDialogOpen(true)}
          className="cursor-pointer"
        >
          <ArrowRightLeft className="mr-2 h-4 w-4" />
          Transfer Registration
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>

  <TransferRegistrationDialog
    open={isTransferDialogOpen}
    onOpenChange={setIsTransferDialogOpen}
    registration={registration}
    currentProgramme={programmeInfo}
    onSuccess={handleTransferSuccess}
  />
  </>
  );
}
