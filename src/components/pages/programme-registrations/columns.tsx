"use client"

import { ColumnDef } from "@tanstack/react-table"
import { ProgrammeRegistrationsInterface } from "@/types/programmes/programme.types";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { DataTableRowActions } from "@/components/pages/programme-registrations/data-table-row-actions";
import { formatTimeStamp } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

export const columns: ColumnDef<ProgrammeRegistrationsInterface>[] = [
    {
        accessorKey: "id",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="No." />
        ),
        cell: ({ row, table }) => {
            const { pageIndex, pageSize } = table.getState().pagination;
            const rowIndex = row.index;
            const number = pageIndex * pageSize + rowIndex + 1;

            return (
                <div className="flex items-center justify-center">
                    <span 
                        data-user-id={row.original.id}
                        className="text-gray-600 font-medium"
                    >
                        {number}
                    </span>
                </div>
            );
        },
        enableSorting: false,
        enableHiding: false,
    },
    // Registration ID column - hidden for now as not needed
    // {
    //     id: "registration_code",
    //     header: ({ column }) => (
    //         <DataTableColumnHeader column={column} title="Registration ID" />
    //     ),
    //     cell: ({ row }) => {
    //         const id = row.original.id;
    //         // Create a short registration code from the UUID
    //         const shortCode = id ? `REG-${id.slice(0, 8).toUpperCase()}` : '--';
    //         return (
    //             <div className="text-center">
    //                 <span className="font-mono text-sm">{shortCode}</span>
    //             </div>
    //         );
    //     },
    //     enableSorting: false,
    //     meta: { label: "Registration ID" },
    // },
    {
        id: "contact_person",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Contact Person / Company" />
        ),
        cell: ({ row }) => {
            const contactPerson = row.original.contact_persons;
            if (!contactPerson) {
                return (
                    <div className="text-center">
                        <span className="text-gray-400">No contact info</span>
                    </div>
                );
            }
            
            return (
                <div className="text-left space-y-1">
                    <div className="font-semibold">
                        {contactPerson.is_company ? contactPerson.company_name : contactPerson.name}
                    </div>
                    {contactPerson.email && (
                        <div className="text-sm text-gray-600">{contactPerson.email}</div>
                    )}
                </div>
            );
        },
        enableSorting: false,
        meta: { label: "Contact Person" },
    },
    {
        accessorKey: "registration_type",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Registration Type" />
        ),
        cell: ({ row }) => {
            const type = row.getValue("registration_type") as string;
            const getTypeColor = () => {
                switch (type) {
                    case "INDIVIDUAL":
                        return "bg-purple-100 text-purple-800 border-purple-300 hover:bg-purple-100";
                    case "GROUP":
                        return "bg-indigo-100 text-indigo-800 border-indigo-300 hover:bg-indigo-100";
                    default:
                        return "bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-100";
                }
            };
            return (
                <div className="flex items-center justify-center">
                    <Badge 
                        variant="outline"
                        className={`${getTypeColor()} font-medium`}
                    >
                        {type}
                    </Badge>
                </div>
            );
        },
        sortingFn: "text",
        meta: { label: "Registration Type" },
    },
    {
        id: "participant_count",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Participants" />
        ),
        cell: ({ row }) => {
            const participantCount = row.original.participants?.[0]?.count || 0;
            const tableReg = row.original.table_registrations;
            const type = row.original.registration_type;
            
            if (type === "GROUP" && tableReg) {
                return (
                    <div className="flex flex-col items-center">
                        <span className="font-medium">{participantCount}</span>
                        <span className="text-xs text-gray-500">
                            of {tableReg.seats_allocated} seats
                        </span>
                    </div>
                );
            }
            
            return (
                <div className="flex items-center justify-center">
                    <span className="font-medium">{participantCount}</span>
                </div>
            );
        },
        enableSorting: false,
        meta: { label: "Participants" },
    },
    {
        accessorKey: "status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            const getStatusColor = () => {
                switch (status) {
                    case "PENDING":
                        return "bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-100";
                    case "CONFIRMED":
                        return "bg-green-100 text-green-800 border-green-200 hover:bg-green-100";
                    case "CANCELLED":
                        return "bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-100";
                    default:
                        return "";
                }
            };
            return (
                <div className="flex items-center justify-center">
                    <Badge 
                        variant="outline"
                        className={`${getStatusColor()} font-medium`}
                    >
                        {status}
                    </Badge>
                </div>
            );
        },
        sortingFn: "text",
        meta: { label: "Status" },
    },
    {
        accessorKey: "total_amount",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Total Amount" />
        ),
        cell: ({ row }) => {
            const amount = row.getValue("total_amount") as number;
            return (
                <div className="flex items-center justify-center font-medium">
                    {amount ? `$${amount.toFixed(2)}` : "--"}
                </div>
            );
        },
        sortingFn: "text",
        meta: { label: "Total Amount" },
    },
    {
        accessorKey: "payment_status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Payment Status" />
        ),
        cell: ({ row }) => {
            const paymentStatus = row.getValue("payment_status") as string;
            const getPaymentStatusColor = () => {
                switch (paymentStatus) {
                    case "PAID":
                        return "bg-green-100 text-green-800 border-green-300 hover:bg-green-100";
                    case "UNPAID":
                        return "bg-red-100 text-red-800 border-red-300 hover:bg-red-100";
                    case "PENDING":
                        return "bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-100";
                    default:
                        return "bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-100";
                }
            };
            return (
                <div className="flex items-center justify-center">
                    <Badge 
                        variant="outline"
                        className={`${getPaymentStatusColor()} font-medium`}
                    >
                        {paymentStatus || "N/A"}
                    </Badge>
                </div>
            );
        },
        sortingFn: "text",
        meta: { label: "Payment Status" },
    },
    {
        accessorKey: "payment_date",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Payment Date" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center justify-center text-gray-600">
                {formatTimeStamp(row.getValue("payment_date"))}
            </div>
        ),
        sortingFn: "text",
        meta: { label: "Payment Date" },
    },
    {
        accessorKey: "created_at",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Created At" />
        ),
        cell: ({ row }) => (
            <div className="flex items-center justify-center text-gray-600">
                {formatTimeStamp(row.getValue("created_at"))}
            </div>
        ),
        sortingFn: "text",
        meta: { label: "Created At" },
    },
    {
        id: "actions",
        cell: ({ row, table }) => <DataTableRowActions row={row} table={table} />,
    },
];