"use client";

import { Row } from "@tanstack/react-table";
import { MoreHorizontal, Eye, Edit, Copy, Mail, Package, X, Printer } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";
import { clonePurchaseOrder } from "@/services/purchase-orders/purchase-orders-server.service";
import { AuthService } from "@/services/auth.service";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const router = useRouter();
  const { toast } = useToast();
  const purchaseOrder = row.original as PurchaseOrder;

  const handleView = () => {
    router.push(`/purchase-orders/${purchaseOrder.id}`);
  };

  const handleEdit = () => {
    router.push(`/purchase-orders/edit?id=${purchaseOrder.id}`);
  };

  const handleClone = async () => {
    try {
      const user = await AuthService.getCurrentUser();

      if (!user?.id) {
        toast({
          title: "Authentication Error",
          description: "You must be logged in to cancel a purchase order.",
          variant: "destructive",
        });
        return;
      }

      const result = await clonePurchaseOrder(purchaseOrder.id, user.id);

      if (result.success && result.newId) {
        toast({
          title: "Success",
          description: "Purchase order cloned successfully",
        });
        router.push(`/purchase-orders/edit?id=${result.newId}`);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to clone purchase order",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const handlePrint = () => {
    // TODO: Implement print functionality
    toast({
      title: "Info",
      description: "Print functionality will be implemented",
    });
  };

  const handleEmail = () => {
    // TODO: Implement email functionality
    toast({
      title: "Info",
      description: "Email functionality will be implemented",
    });
  };

  const handleCreateGoodsReceipt = () => {
    router.push(`/purchase-orders/goods-receipt?po_id=${purchaseOrder.id}`);
  };

  const canEdit = purchaseOrder.status === "DRAFT";
  const canCreateGoodsReceipt = ["APPROVED", "SENT", "PARTIALLY_RECEIVED"].includes(purchaseOrder.status);
  const canCancel = !["CANCELLED", "CLOSED", "RECEIVED"].includes(purchaseOrder.status);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[180px]">
        <DropdownMenuItem onClick={handleView}>
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </DropdownMenuItem>

        {canEdit && (
          <DropdownMenuItem onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        )}

        <DropdownMenuItem onClick={handleClone}>
          <Copy className="mr-2 h-4 w-4" />
          Clone
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={handlePrint}>
          <Printer className="mr-2 h-4 w-4" />
          Print
        </DropdownMenuItem>

        <DropdownMenuItem onClick={handleEmail}>
          <Mail className="mr-2 h-4 w-4" />
          Email to Supplier
        </DropdownMenuItem>

        {canCreateGoodsReceipt && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleCreateGoodsReceipt}>
              <Package className="mr-2 h-4 w-4" />
              Create Goods Receipt
            </DropdownMenuItem>
          </>
        )}

        {canCancel && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                toast({
                  title: "Info",
                  description: "Cancel functionality will be implemented",
                });
              }}
              className="text-red-600 focus:text-red-600"
            >
              <X className="mr-2 h-4 w-4" />
              Cancel PO
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}