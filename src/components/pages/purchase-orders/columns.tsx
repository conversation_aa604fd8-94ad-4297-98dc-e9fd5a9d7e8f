"use client";

import { useState } from "react";
import Link from "next/link";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableRowActions } from "@/components/pages/purchase-orders/data-table-row-actions";
import { DataTableColumnHeader } from "@/components/pages/general/data-table-column-header";
import { Checkbox } from "@/components/ui/checkbox";
import { POStatusBadge, CurrencyDisplay } from "@/components/purchase-orders/ui";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";

export const usePurchaseOrderColumns = () => {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

  const toggleRowSelection = (id: string) => {
    setSelectedRows((prevSelectedRows) => {
      const newSelectedRows = new Set(prevSelectedRows);
      if (newSelectedRows.has(id)) {
        newSelectedRows.delete(id);
      } else {
        newSelectedRows.add(id);
      }
      return newSelectedRows;
    });
  };

  const toggleAllRowsSelection = (rows: any[]) => {
    setSelectedRows((prevSelectedRows) => {
      const newSelectedRows = new Set(prevSelectedRows);
      if (newSelectedRows.size === rows.length) {
        newSelectedRows.clear();
      } else {
        rows.forEach((row) => newSelectedRows.add(row.original.id));
      }
      return newSelectedRows;
    });
  };

  const columns: ColumnDef<PurchaseOrder>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <div className="flex justify-center items-center">
          <Checkbox
            checked={
              selectedRows.size > 0
                ? selectedRows.size === table.getRowModel().rows.length
                  ? true
                  : "indeterminate"
                : false
            }
            onCheckedChange={() => {
              toggleAllRowsSelection(table.getRowModel().rows);
            }}
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex justify-center items-center">
          <Checkbox
            checked={selectedRows.has(row.original.id)}
            onCheckedChange={() => toggleRowSelection(row.original.id)}
            aria-label={`Select ${row.original.id}`}
            className="translate-y-[2px]"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "id",
      header: ({ column }) => (

        <DataTableColumnHeader column={column} title="No." />
      ),
      cell: ({ row, table }) => {
        const { pageIndex, pageSize } = table.getState().pagination;
        const rowIndex = row.index;
        const number = pageIndex * pageSize + rowIndex + 1;

        return (
          <div className="flex text-center">
            <span data-user-id={row.original.id}>{number}</span>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "po_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="PO Number" />
      ),
      cell: ({ row }) => {
        const poNumber = row.original.po_number;
        const poId = row.original.id;

        return (
          <div className="flex items-center">
            <Link
              href={`/purchase-orders/${poId}`}
              className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
            >
              {poNumber}
            </Link>
          </div>
        );
      },
      sortingFn: 'alphanumeric',
      meta: { label: "PO Number" },
    },
    {
      accessorKey: "supplier_name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Supplier" />
      ),
      cell: ({ row }) => {
        const supplierName = row.original.suppliers?.name || row.original.suppliers?.display_name || "--";

        return (
          <div className="flex items-center max-w-[200px]">
            <span className="truncate" title={supplierName}>
              {supplierName}
            </span>
          </div>
        );
      },
      sortingFn: 'alphanumeric',
      meta: { label: "Supplier" },
    },
    {
      accessorKey: "entity_id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Entity" />
      ),
      cell: ({ row }) => {
        const entityName = row.original.entity?.name || "--";

        return (
          <div className="flex items-center max-w-[150px]">
            <span className="truncate" title={entityName}>
              {entityName}
            </span>
          </div>
        );
      },
      sortingFn: 'alphanumeric',
      meta: { label: "Entity" },
    },
    {
      accessorKey: "po_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="PO Date" />
      ),
      cell: ({ row }) => {
        const date = row.original.po_date;
        const formattedDate = new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: '2-digit'
        });

        return (
          <div className="flex items-center">
            {formattedDate}
          </div>
        );
      },
      sortingFn: 'datetime',
      meta: { label: "PO Date" },
    },
    {
      accessorKey: "expected_delivery_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Expected Delivery" />
      ),
      cell: ({ row }) => {
        const date = row.original.expected_delivery_date;
        if (!date) return <div className="flex items-center">-</div>;

        const formattedDate = new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: '2-digit'
        });

        // Check if overdue
        const isOverdue = new Date(date) < new Date() &&
          !['RECEIVED', 'CANCELLED', 'CLOSED'].includes(row.original.status);

        return (
          <div className="flex items-center">
            <span className={isOverdue ? 'text-red-600 font-medium' : ''}>
              {formattedDate}
            </span>
          </div>
        );
      },
      sortingFn: 'datetime',
      meta: { label: "Expected Delivery" },
    },
    {
      accessorKey: "total_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total Amount" />
      ),
      cell: ({ row }) => {
        const amount = row.original.total_amount;
        const currency = row.original.currency_code;

        return (
          <div className="flex items-center">
            <CurrencyDisplay
              amount={amount}
              currency={currency}
              showCurrency={false}
            />
          </div>
        );
      },
      sortingFn: 'alphanumeric',
      meta: { label: "Total Amount" },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.original.status;

        return (
          <div className="flex items-center">
            <POStatusBadge status={status} />
          </div>
        );
      },
      sortingFn: 'text',
      meta: { label: "Status" },
    },
    {
      accessorKey: "department_code",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Department" />
      ),
      cell: ({ row }) => {
        const department = row.original.department_code;

        return (
          <div className="flex items-center">
            {department || "--"}
          </div>
        );
      },
      sortingFn: 'alphanumeric',
      meta: { label: "Department" },
    },
    {
      accessorKey: "created_by",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created By" />
      ),
      cell: ({ row }) => {
        const createdBy = row.original.created_by_name?.full_name || row.original.created_by_name?.email || "--";

        return (
          <div className="flex items-center">
            {createdBy}
          </div>
        );
      },
      sortingFn: 'alphanumeric',
      meta: { label: "Created By" },
    },
    {
      id: "actions",
      cell: ({ row }) => <DataTableRowActions row={row} />,
    },
  ];

  return { columns, selectedRows, setSelectedRows };
}