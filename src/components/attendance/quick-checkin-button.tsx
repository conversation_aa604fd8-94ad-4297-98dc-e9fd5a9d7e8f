"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AttendanceStatus, CheckInRequest, CheckOutRequest } from "@/types/attendance/attendance.types";
import { Loader2, UserCheck, UserX } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { checkInParticipant, checkOutParticipant } from "@/services/client/attendance-client.service";
import { useUser } from "@/hooks/use-user";

interface QuickCheckInButtonProps {
  participantId: string;
  programmeId: string;
  currentStatus?: AttendanceStatus;
  onSuccess: () => void;
  size?: "small" | "medium";
  disabled?: boolean;
  className?: string;
  scheduleId?: string;
  requireCheckout?: boolean;
  canCheckOut?: boolean;
}

export function QuickCheckInButton({
  participantId,
  programmeId,
  currentStatus = "absent",
  onSuccess,
  size = "medium",
  disabled = false,
  className,
  scheduleId,
  requireCheckout = false,
  canCheckOut = false
}: QuickCheckInButtonProps) {
  const [isLoading, setIsLoading] = React.useState(false);
  const { toast } = useToast();
  const { user } = useUser();

  // Determine the action based on current status and configuration
  const isPresent = currentStatus === "present" || currentStatus === "late" || currentStatus === "partial";
  
  // If require_checkout is false, never show checkout button
  // If require_checkout is true, show checkout button when checked in and can check out
  const showCheckOut = requireCheckout && isPresent && canCheckOut;
  const action = showCheckOut ? "checkout" : "checkin";
  
  // If already checked in and checkout is not required, hide the button
  if (isPresent && !requireCheckout) {
    return null;
  }
  
  const handleClick = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);
    
    try {
      if (action === "checkin") {
        // Create check-in request
        const checkInRequest: CheckInRequest = {
          participant_id: participantId,
          programme_id: programmeId,
          schedule_id: scheduleId,
          check_in_method: 'manual',
          check_in_location: 'Admin Panel',
          notes: `Manual check-in by ${user?.email || 'staff'}`
        };

        // Call the real check-in service
        const result = await checkInParticipant(checkInRequest);
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to check in participant');
        }

        toast({
          title: "Success",
          description: "Participant checked in successfully",
        });
      } else {
        // Create check-out request
        const checkOutRequest: CheckOutRequest = {
          participant_id: participantId,
          programme_id: programmeId,
          notes: `Manual check-out by ${user?.email || 'staff'}`
        };

        // Call the check-out service
        const result = await checkOutParticipant(checkOutRequest);
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to check out participant');
        }

        toast({
          title: "Success",
          description: "Participant checked out successfully",
        });
      }

      onSuccess();
    } catch (error) {
      console.error(`Error during ${action}:`, error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${action} participant. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const buttonSize = size === "small" ? "sm" : "default";
  const iconSize = size === "small" ? "h-3 w-3" : "h-4 w-4";

  return (
    <Button
      onClick={handleClick}
      disabled={disabled || isLoading}
      size={buttonSize}
      variant={showCheckOut ? "destructive" : "default"}
      className={cn(
        "gap-1.5",
        showCheckOut && "bg-orange-600 hover:bg-orange-700 text-white border-orange-700",
        className
      )}
    >
      {isLoading ? (
        <Loader2 className={cn(iconSize, "animate-spin")} />
      ) : showCheckOut ? (
        <UserX className={iconSize} />
      ) : (
        <UserCheck className={iconSize} />
      )}
      
      <span className={size === "small" ? "text-xs" : "text-sm"}>
        {isLoading 
          ? (action === "checkin" ? "Checking in..." : "Checking out...")
          : (action === "checkin" ? "Check In" : "Check Out")
        }
      </span>
    </Button>
  );
}

export default QuickCheckInButton;