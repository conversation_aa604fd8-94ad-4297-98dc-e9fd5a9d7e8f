"use client"

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, TrendingUp, Award } from "lucide-react";

interface AttendanceSummaryStatsProps {
  programmeId: string;
  stats?: {
    totalParticipants: number;
    checkedInCount: number;
    attendanceRate: number;
    averageAttendancePercentage: number;
    certificateEligibleCount: number;
  };
  loading?: boolean;
}

export function AttendanceSummaryStats({ 
  programmeId, 
  stats, 
  loading = false 
}: AttendanceSummaryStatsProps) {
  // Default stats when no data is available
  const defaultStats = {
    totalParticipants: 0,
    checkedInCount: 0,
    attendanceRate: 0,
    averageAttendancePercentage: 0,
    certificateEligibleCount: 0,
  };

  const displayStats = stats || defaultStats;

  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Attendance Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Attendance Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Total Participants */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-gray-600">Total Registered</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {displayStats.totalParticipants}
            </div>
            <p className="text-xs text-gray-500">Participants</p>
          </div>

          {/* Current Check-ins */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <UserCheck className="w-4 h-4 text-green-500" />
              <span className="text-sm text-gray-600">Current Session</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {displayStats.checkedInCount}
            </div>
            <Badge variant="outline" className="text-xs">
              {formatPercentage(displayStats.attendanceRate)} present
            </Badge>
          </div>

          {/* Average Attendance */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-orange-500" />
              <span className="text-sm text-gray-600">Average Attendance</span>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {formatPercentage(displayStats.averageAttendancePercentage)}
            </div>
            <p className="text-xs text-gray-500">Across all sessions</p>
          </div>

          {/* Certificate Eligible */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4 text-purple-500" />
              <span className="text-sm text-gray-600">Certificate Eligible</span>
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {displayStats.certificateEligibleCount}
            </div>
            <Badge 
              variant={displayStats.certificateEligibleCount > 0 ? "default" : "secondary"}
              className="text-xs"
            >
              {displayStats.totalParticipants > 0 
                ? formatPercentage((displayStats.certificateEligibleCount / displayStats.totalParticipants) * 100)
                : "0%"
              } eligible
            </Badge>
          </div>
        </div>

        {/* Progress Bar for Overall Attendance */}
        {displayStats.totalParticipants > 0 && (
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Overall Programme Attendance</span>
              <span className="font-medium">
                {formatPercentage(displayStats.averageAttendancePercentage)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${Math.min(displayStats.averageAttendancePercentage, 100)}%` 
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>
        )}

        {/* No data message */}
        {displayStats.totalParticipants === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-sm">No participants registered yet</p>
            <p className="text-xs text-gray-400 mt-1">
              Attendance stats will appear once participants are registered
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}