"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useBulkCheckIn } from "@/hooks/attendance/use-bulk-checkin-simple";
import { ParticipantWithAttendance } from "@/types/attendance/attendance.types";
import { bulkCheckInRequestSchema } from "@/schemas/attendance/attendance-record.schema";
import { 
  Users, 
  UserCheck, 
  UserX, 
  AlertTriangle,
  CheckCircle,
  MapPin,
  MessageSquare
} from "lucide-react";
import { z } from "zod";

interface BulkCheckInModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  programmeId: string;
  selectedParticipantIds: string[];
  participants: ParticipantWithAttendance[];
  onSuccess?: () => void;
}

const bulkCheckInFormSchema = bulkCheckInRequestSchema.extend({
  confirm_action: z.boolean().optional()
});

type BulkCheckInFormData = z.infer<typeof bulkCheckInFormSchema>;

export function BulkCheckInModal({
  open,
  onOpenChange,
  programmeId,
  selectedParticipantIds,
  participants,
  onSuccess
}: BulkCheckInModalProps) {
  const [step, setStep] = useState<'form' | 'confirm' | 'result'>('form');
  const [lastResult, setLastResult] = useState<any>(null);

  const {
    performBulkCheckIn,
    getSelectionSummary,
    validateSelection,
    isLoading
  } = useBulkCheckIn({ 
    programmeId,
    onSuccess: (result) => {
      setLastResult(result);
      setStep('result');
      onSuccess?.();
    }
  });

  const form = useForm<BulkCheckInFormData>({
    resolver: zodResolver(bulkCheckInFormSchema),
    defaultValues: {
      participant_ids: selectedParticipantIds,
      programme_id: programmeId,
      check_in_method: "manual",
      check_in_location: "",
      notes: ""
    }
  });

  // Get selection details
  const selectionSummary = getSelectionSummary(participants);
  const validation = validateSelection(participants);
  const selectedParticipants = participants.filter(p => selectedParticipantIds.includes(p.id));

  // Handle form submission
  const onSubmit = async (data: BulkCheckInFormData) => {
    if (step === 'form') {
      setStep('confirm');
      return;
    }

    if (step === 'confirm') {
      await performBulkCheckIn(
        selectedParticipantIds,
        undefined, // scheduleId
        data.check_in_location,
        data.notes
      );
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isLoading) {
      setStep('form');
      setLastResult(null);
      form.reset();
      onOpenChange(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case 'form':
        return (
          <div className="space-y-6">
            {/* Selection Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Selected Participants
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center mb-4">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{selectionSummary.total}</div>
                    <div className="text-sm text-muted-foreground">Selected</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{selectionSummary.canCheckIn}</div>
                    <div className="text-sm text-muted-foreground">Can Check In</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-yellow-600">{selectionSummary.alreadyCheckedIn}</div>
                    <div className="text-sm text-muted-foreground">Already Checked In</div>
                  </div>
                </div>

                {!validation.isValid && (
                  <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <span className="text-sm text-red-700">{validation.message}</span>
                  </div>
                )}

                {validation.isValid && validation.warning && (
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm text-yellow-700">{validation.message}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Check-in Details Form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Check-in Details</CardTitle>
                <CardDescription>
                  Optional information for the bulk check-in operation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="check_in_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        Location
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Main Auditorium, Room 101"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Where are participants checking in? (Optional)
                      </FormDescription>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4" />
                        Notes
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Additional notes for this bulk check-in..."
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Any additional information about this check-in session
                      </FormDescription>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Selected Participants Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Participants to Check In</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {selectedParticipants.map((participant) => (
                      <div 
                        key={participant.id} 
                        className="flex items-center justify-between p-2 border rounded-md"
                      >
                        <div>
                          <div className="font-medium">{participant.name}</div>
                          <div className="text-sm text-muted-foreground">{participant.email}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          {participant.can_check_in ? (
                            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Ready
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                              <UserX className="h-3 w-3 mr-1" />
                              Skip
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        );

      case 'confirm':
        const formValues = form.getValues();
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  Confirm Bulk Check-in
                </CardTitle>
                <CardDescription>
                  Please review the details before proceeding
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Participants:</span>
                    <span className="ml-2">{selectionSummary.canCheckIn} will be checked in</span>
                  </div>
                  <div>
                    <span className="font-medium">Method:</span>
                    <span className="ml-2 capitalize">{formValues.check_in_method.replace('_', ' ')}</span>
                  </div>
                  {formValues.check_in_location && (
                    <div>
                      <span className="font-medium">Location:</span>
                      <span className="ml-2">{formValues.check_in_location}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Time:</span>
                    <span className="ml-2">{new Date().toLocaleString()}</span>
                  </div>
                </div>

                {formValues.notes && (
                  <div>
                    <span className="font-medium">Notes:</span>
                    <p className="mt-1 text-sm text-muted-foreground">{formValues.notes}</p>
                  </div>
                )}

                {selectionSummary.alreadyCheckedIn > 0 && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-700">Warning</span>
                    </div>
                    <p className="text-sm text-yellow-700 mt-1">
                      {selectionSummary.alreadyCheckedIn} participants are already checked in and will be skipped.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      case 'result':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Bulk Check-in Complete
                </CardTitle>
              </CardHeader>
              <CardContent>
                {lastResult && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-green-600">{lastResult.successCount}</div>
                        <div className="text-sm text-muted-foreground">Successful</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-red-600">{lastResult.failCount}</div>
                        <div className="text-sm text-muted-foreground">Failed</div>
                      </div>
                    </div>

                    {lastResult.failed.length > 0 && (
                      <div>
                        <h4 className="font-medium text-red-700 mb-2">Failed Check-ins:</h4>
                        <ScrollArea className="h-32">
                          <div className="space-y-1">
                            {lastResult.failed.map((failure: any, index: number) => (
                              <div key={index} className="text-sm p-2 bg-red-50 border border-red-200 rounded">
                                <div className="font-medium">
                                  {participants.find(p => p.id === failure.participant_id)?.name || 'Unknown'}
                                </div>
                                <div className="text-red-600">{failure.error}</div>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            {step === 'form' && 'Bulk Check-in'}
            {step === 'confirm' && 'Confirm Check-in'}
            {step === 'result' && 'Check-in Results'}
          </DialogTitle>
          <DialogDescription>
            {step === 'form' && 'Check in multiple participants at once'}
            {step === 'confirm' && 'Review and confirm the bulk check-in operation'}
            {step === 'result' && 'Bulk check-in operation completed'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {renderStepContent()}

            <DialogFooter className="mt-6">
              {step === 'form' && (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={!validation.isValid || selectionSummary.canCheckIn === 0}
                  >
                    Next: Review
                  </Button>
                </>
              )}

              {step === 'confirm' && (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setStep('form')}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    {isLoading ? (
                      <>
                        <LoadingSpinner />
                        Checking In...
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-4 w-4" />
                        Confirm Check-in
                      </>
                    )}
                  </Button>
                </>
              )}

              {step === 'result' && (
                <Button onClick={handleClose} className="w-full">
                  Close
                </Button>
              )}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}