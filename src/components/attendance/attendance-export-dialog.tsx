"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Badge } from "@/components/ui/badge";
import { exportAttendanceData } from "@/services/client/attendance-client.service";
import { ParticipantWithAttendance } from "@/types/attendance/attendance.types";
import { attendanceExportOptionsSchema } from "@/schemas/attendance/attendance-record.schema";
import { 
  Download, 
  FileText, 
  Users, 
  Calendar,
  Award,
  CheckCircle
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { z } from "zod";

interface AttendanceExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  programmeId: string;
  participants: ParticipantWithAttendance[];
  preSelectedParticipants?: string[];
}

const exportFormSchema = attendanceExportOptionsSchema.extend({
  export_all_participants: z.boolean().default(true),
  selected_participant_ids: z.array(z.string()).optional()
});

type ExportFormData = z.infer<typeof exportFormSchema>;

export function AttendanceExportDialog({
  open,
  onOpenChange,
  programmeId,
  participants,
  preSelectedParticipants = []
}: AttendanceExportDialogProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [selectedParticipantIds, setSelectedParticipantIds] = useState<string[]>(preSelectedParticipants);

  const form = useForm<ExportFormData>({
    resolver: zodResolver(exportFormSchema),
    defaultValues: {
      programme_id: programmeId,
      format: "csv",
      include_summary: true,
      include_sessions: false,
      include_certificate_status: true,
      export_all_participants: preSelectedParticipants.length === 0,
      selected_participant_ids: preSelectedParticipants
    }
  });

  const watchExportAll = form.watch("export_all_participants");
  const watchFormat = form.watch("format");

  // Handle participant selection
  const handleParticipantToggle = (participantId: string, checked: boolean) => {
    if (checked) {
      setSelectedParticipantIds(prev => [...prev, participantId]);
    } else {
      setSelectedParticipantIds(prev => prev.filter(id => id !== participantId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedParticipantIds(participants.map(p => p.id));
    } else {
      setSelectedParticipantIds([]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: ExportFormData) => {
    setIsExporting(true);
    
    try {
      const exportOptions = {
        programme_id: data.programme_id,
        participant_ids: data.export_all_participants ? undefined : selectedParticipantIds,
        include_summary: data.include_summary,
        include_sessions: data.include_sessions,
        include_certificate_status: data.include_certificate_status,
        format: data.format
      };

      const result = await exportAttendanceData(exportOptions);
      
      if (result.success && result.data) {
        // Create download link
        const url = URL.createObjectURL(result.data);
        const link = document.createElement('a');
        link.href = url;
        
        const timestamp = new Date().toISOString().split('T')[0];
        const participantCount = data.export_all_participants ? participants.length : selectedParticipantIds.length;
        link.download = `attendance-report-${participantCount}-participants-${timestamp}.${data.format}`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast({
          title: "Export Successful",
          description: `Attendance report exported for ${participantCount} participants`,
        });

        onOpenChange(false);
      } else {
        throw new Error(result.error || 'Export failed');
      }
    } catch (error) {
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const getExportSummary = () => {
    const participantCount = watchExportAll ? participants.length : selectedParticipantIds.length;
    const certificateEligible = participants.filter(p => 
      watchExportAll ? true : selectedParticipantIds.includes(p.id)
    ).filter(p => p.certificate_eligible).length;

    return {
      participantCount,
      certificateEligible,
      attendanceRate: participants.length > 0 
        ? Math.round((participants.filter(p => p.current_attendance_status === 'present').length / participants.length) * 100)
        : 0
    };
  };

  const summary = getExportSummary();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Attendance Report
          </DialogTitle>
          <DialogDescription>
            Generate and download attendance data in your preferred format
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Export Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Export Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{summary.participantCount}</div>
                    <div className="text-sm text-muted-foreground">Participants</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{summary.attendanceRate}%</div>
                    <div className="text-sm text-muted-foreground">Attendance Rate</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">{summary.certificateEligible}</div>
                    <div className="text-sm text-muted-foreground">Certificate Eligible</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Format Selection */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-semibold">Export Format</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="grid grid-cols-3 gap-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="csv" id="csv" />
                          <Label htmlFor="csv" className="flex items-center gap-2 cursor-pointer">
                            <FileText className="h-4 w-4" />
                            CSV
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="excel" id="excel" />
                          <Label htmlFor="excel" className="flex items-center gap-2 cursor-pointer">
                            <FileText className="h-4 w-4" />
                            Excel
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="pdf" id="pdf" />
                          <Label htmlFor="pdf" className="flex items-center gap-2 cursor-pointer">
                            <FileText className="h-4 w-4" />
                            PDF
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Data Options */}
            <div className="space-y-4">
              <h3 className="text-base font-semibold">Include in Export</h3>
              
              <div className="space-y-3">
                <FormField
                  control={form.control}
                  name="include_summary"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="cursor-pointer">
                          Attendance Summary
                        </FormLabel>
                        <FormDescription>
                          Include total sessions, attended sessions, and attendance percentage
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="include_sessions"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="cursor-pointer">
                          Session Details
                        </FormLabel>
                        <FormDescription>
                          Include individual session attendance records and timestamps
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="include_certificate_status"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="cursor-pointer">
                          Certificate Status
                        </FormLabel>
                        <FormDescription>
                          Include certificate eligibility and issuance information
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Participant Selection */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="export_all_participants"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="cursor-pointer text-base font-semibold">
                        Export All Participants
                      </FormLabel>
                      <FormDescription>
                        Include all {participants.length} participants in the export
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {!watchExportAll && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center justify-between">
                      <span>Select Participants</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {selectedParticipantIds.length} selected
                        </Badge>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAll(selectedParticipantIds.length !== participants.length)}
                        >
                          {selectedParticipantIds.length === participants.length ? "Deselect All" : "Select All"}
                        </Button>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-48 overflow-y-auto space-y-2">
                      {participants.map((participant) => (
                        <div key={participant.id} className="flex items-center space-x-3">
                          <Checkbox
                            checked={selectedParticipantIds.includes(participant.id)}
                            onCheckedChange={(checked) => 
                              handleParticipantToggle(participant.id, checked as boolean)
                            }
                          />
                          <div className="flex-1 flex items-center justify-between">
                            <div>
                              <div className="font-medium">{participant.name}</div>
                              <div className="text-sm text-muted-foreground">{participant.email}</div>
                            </div>
                            <div className="flex items-center gap-2">
                              {participant.certificate_eligible && (
                                <Award className="h-4 w-4 text-green-600" />
                              )}
                              <Badge 
                                variant="outline" 
                                className={
                                  participant.current_attendance_status === 'present' 
                                    ? "bg-green-100 text-green-800 border-green-200"
                                    : "bg-red-100 text-red-800 border-red-200"
                                }
                              >
                                {participant.current_attendance_status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isExporting}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isExporting || (!watchExportAll && selectedParticipantIds.length === 0)}
                className="flex items-center gap-2"
              >
                {isExporting ? (
                  <>
                    <LoadingSpinner />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Export Report
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}