import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { AttendanceStatus } from "@/types/attendance/attendance.types";
import { format } from "date-fns";

interface AttendanceStatusBadgeProps {
  status: AttendanceStatus;
  showTime?: boolean;
  checkInTime?: string | null;
  className?: string;
}

const statusConfig: Record<AttendanceStatus, {
  label: string;
  variant: "default" | "secondary" | "success" | "warning" | "destructive";
  className?: string;
}> = {
  present: {
    label: "Present",
    variant: "success",
    className: "bg-green-100 text-green-800 border-green-200 hover:bg-green-100/80"
  },
  absent: {
    label: "Absent",
    variant: "destructive",
    className: "bg-red-100 text-red-800 border-red-200 hover:bg-red-100/80"
  },
  late: {
    label: "Late",
    variant: "warning",
    className: "bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-100/80"
  },
  excused: {
    label: "Excused",
    variant: "secondary",
    className: "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-100/80"
  },
  partial: {
    label: "Partial",
    variant: "warning",
    className: "bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-100/80"
  }
};

export function AttendanceStatusBadge({
  status,
  showTime = false,
  checkInTime,
  className
}: AttendanceStatusBadgeProps) {
  const config = statusConfig[status];
  
  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return format(date, "HH:mm");
    } catch {
      return "Invalid time";
    }
  };

  const displayText = React.useMemo(() => {
    if (!showTime || !checkInTime || status === 'absent') {
      return config.label;
    }
    
    return `${config.label} (${formatTime(checkInTime)})`;
  }, [config.label, showTime, checkInTime, status]);

  return (
    <Badge
      variant={config.variant}
      className={cn(config.className, className)}
      title={showTime && checkInTime ? `Checked in at ${formatTime(checkInTime)}` : undefined}
    >
      {displayText}
    </Badge>
  );
}

export default AttendanceStatusBadge;