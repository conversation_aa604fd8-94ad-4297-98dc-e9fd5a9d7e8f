"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { AttendanceHistoryItem, AttendanceStatus, CheckInMethod } from "@/types/attendance/attendance.types";
import { Calendar, Clock, MapPin, User, FileText, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { format, parseISO, differenceInMinutes } from "date-fns";
import AttendanceStatusBadge from "./attendance-status-badge";

interface AttendanceHistoryTimelineProps {
  participantId: string;
  programmeId: string;
  showSessionDetails?: boolean;
  className?: string;
}

// Mock data - replace with actual data fetching
const mockAttendanceHistory: AttendanceHistoryItem[] = [
  {
    id: "1",
    date: "2024-07-01",
    session_name: "Opening Ceremony",
    check_in_time: "2024-07-01T09:00:00Z",
    check_out_time: "2024-07-01T10:30:00Z",
    duration_minutes: 90,
    attendance_status: "present",
    check_in_method: "qr_code",
    notes: null
  },
  {
    id: "2",
    date: "2024-07-01",
    session_name: "Workshop Session 1",
    check_in_time: "2024-07-01T11:15:00Z",
    check_out_time: "2024-07-01T12:45:00Z",
    duration_minutes: 90,
    attendance_status: "late",
    check_in_method: "manual",
    notes: "Arrived 15 minutes late"
  },
  {
    id: "3",
    date: "2024-07-02",
    session_name: "Panel Discussion",
    check_in_time: null,
    check_out_time: null,
    duration_minutes: null,
    attendance_status: "absent",
    check_in_method: "manual",
    notes: "Medical leave"
  }
];

const methodIcons: Record<CheckInMethod, React.ReactNode> = {
  qr_code: <CheckCircle className="h-3 w-3" />,
  manual: <User className="h-3 w-3" />,
  auto: <CheckCircle className="h-3 w-3" />,
  facial_recognition: <User className="h-3 w-3" />
};

const statusIcons: Record<AttendanceStatus, React.ReactNode> = {
  present: <CheckCircle className="h-4 w-4 text-green-600" />,
  late: <AlertCircle className="h-4 w-4 text-amber-600" />,
  absent: <XCircle className="h-4 w-4 text-red-600" />,
  excused: <AlertCircle className="h-4 w-4 text-blue-600" />,
  partial: <AlertCircle className="h-4 w-4 text-orange-600" />
};

function TimelineItem({ item, showSessionDetails }: {
  item: AttendanceHistoryItem;
  showSessionDetails?: boolean;
}) {
  const formatTime = (timeString: string) => {
    try {
      return format(parseISO(timeString), "HH:mm");
    } catch {
      return "Invalid time";
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  return (
    <div className="flex gap-3 pb-4 last:pb-0">
      {/* Timeline indicator */}
      <div className="flex flex-col items-center">
        <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 bg-background">
          {statusIcons[item.attendance_status as AttendanceStatus]}
        </div>
        <div className="w-px h-full bg-border mt-2 last:hidden" />
      </div>

      {/* Content */}
      <div className="flex-1 space-y-2">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-sm">
                {item.session_name || "General Attendance"}
              </h4>
              <AttendanceStatusBadge status={item.attendance_status} />
            </div>
            
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {format(parseISO(item.date), "MMM dd, yyyy")}
              </div>
              
              <div className="flex items-center gap-1">
                {methodIcons[item.check_in_method as CheckInMethod]}
                {item.check_in_method.replace('_', ' ')}
              </div>
            </div>
          </div>
        </div>

        {/* Time details */}
        {item.check_in_time && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
            <div className="flex items-center gap-1 text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>In: {formatTime(item.check_in_time)}</span>
            </div>
            
            {item.check_out_time && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>Out: {formatTime(item.check_out_time)}</span>
              </div>
            )}
            
            {item.duration_minutes && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>Duration: {formatDuration(item.duration_minutes)}</span>
              </div>
            )}
          </div>
        )}

        {/* Notes */}
        {item.notes && (
          <div className="flex items-start gap-1 text-xs">
            <FileText className="h-3 w-3 mt-0.5 text-muted-foreground" />
            <span className="text-muted-foreground italic">{item.notes}</span>
          </div>
        )}

        {/* Session details */}
        {showSessionDetails && item.session_name && (
          <div className="text-xs text-muted-foreground p-2 bg-muted rounded-md">
            Session: {item.session_name}
          </div>
        )}
      </div>
    </div>
  );
}

export function AttendanceHistoryTimeline({
  participantId,
  programmeId,
  showSessionDetails = false,
  className
}: AttendanceHistoryTimelineProps) {
  const [isLoading, setIsLoading] = React.useState(true);
  const [attendanceHistory, setAttendanceHistory] = React.useState<AttendanceHistoryItem[]>([]);

  React.useEffect(() => {
    const fetchAttendanceHistory = async () => {
      setIsLoading(true);
      try {
        // TODO: Replace with actual API call
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setAttendanceHistory(mockAttendanceHistory);
      } catch (error) {
        console.error("Failed to fetch attendance history:", error);
        setAttendanceHistory([]);
      } finally {
        setIsLoading(false);
      }
    };

    if (participantId && programmeId) {
      fetchAttendanceHistory();
    }
  }, [participantId, programmeId]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-base">Attendance History</CardTitle>
          <CardDescription>Loading attendance records...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex gap-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-base">Attendance History</CardTitle>
        <CardDescription>
          {attendanceHistory.length > 0 
            ? `${attendanceHistory.length} attendance records`
            : "No attendance records found"
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {attendanceHistory.length > 0 ? (
          <div className="space-y-0">
            {attendanceHistory.map((item) => (
              <TimelineItem
                key={item.id}
                item={item}
                showSessionDetails={showSessionDetails}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No attendance records found</p>
            <p className="text-sm">Attendance records will appear here once the participant checks in</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default AttendanceHistoryTimeline;