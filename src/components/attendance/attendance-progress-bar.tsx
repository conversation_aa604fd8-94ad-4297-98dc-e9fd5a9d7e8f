import * as React from "react";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Award, Clock } from "lucide-react";

interface AttendanceProgressBarProps {
  current: number; // Current attendance percentage
  required: number; // Required attendance for completion
  showCertificateEligibility?: boolean;
  compact?: boolean;
  className?: string;
  attendedSessions?: number;
  totalSessions?: number;
}

export function AttendanceProgressBar({
  current,
  required,
  showCertificateEligibility = false,
  compact = false,
  className,
  attendedSessions,
  totalSessions
}: AttendanceProgressBarProps) {
  // Calculate certificate eligibility
  const isCertificateEligible = current >= required;
  
  // Determine progress color based on attendance level
  const getProgressColor = () => {
    if (current >= required) return "bg-green-500";
    if (current >= required * 0.75) return "bg-amber-500";
    return "bg-red-500";
  };

  // Format percentage display
  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  return (
    <div className={cn("space-y-2", compact && "space-y-1", className)}>
      {/* Progress Bar */}
      <div className="space-y-1">
        <div className="flex items-center justify-between text-sm">
          <span className={cn(
            "font-medium",
            compact && "text-xs"
          )}>
            Attendance Progress
          </span>
          
          <div className="flex items-center gap-2">
            {attendedSessions !== undefined && totalSessions !== undefined && (
              <span className={cn(
                "text-muted-foreground flex items-center gap-1",
                compact && "text-xs"
              )}>
                <Clock className="h-3 w-3" />
                {attendedSessions}/{totalSessions} sessions
              </span>
            )}
            
            <span className={cn(
              "font-semibold",
              isCertificateEligible ? "text-green-600" : "text-muted-foreground",
              compact && "text-xs"
            )}>
              {formatPercentage(current)}
            </span>
          </div>
        </div>
        
        <div className="relative">
          <Progress 
            value={current} 
            className={cn(
              "h-2",
              compact && "h-1.5"
            )}
          />
          
          {/* Custom progress bar styling */}
          <div 
            className={cn(
              "absolute top-0 left-0 h-full rounded-full transition-all duration-300",
              getProgressColor()
            )}
            style={{ 
              width: `${Math.min(current, 100)}%`,
              height: compact ? '6px' : '8px',
              borderRadius: '9999px'
            }}
          />
          
          {/* Required threshold indicator */}
          {required > 0 && required < 100 && (
            <div 
              className="absolute top-0 w-0.5 bg-gray-400 rounded-full"
              style={{ 
                left: `${required}%`,
                height: compact ? '6px' : '8px',
                transform: 'translateX(-50%)'
              }}
              title={`Required: ${formatPercentage(required)}`}
            />
          )}
        </div>
        
        {/* Required threshold label */}
        {!compact && required > 0 && (
          <div className="text-xs text-muted-foreground">
            Required: {formatPercentage(required)}
          </div>
        )}
      </div>

      {/* Certificate Eligibility Badge */}
      {showCertificateEligibility && (
        <div className="flex items-center gap-2">
          <Badge
            variant={isCertificateEligible ? "success" : "secondary"}
            className={cn(
              "gap-1",
              isCertificateEligible 
                ? "bg-green-100 text-green-800 border-green-200" 
                : "bg-gray-100 text-gray-600 border-gray-200",
              compact && "text-xs px-2 py-0.5"
            )}
          >
            <Award className={cn("h-3 w-3", compact && "h-2.5 w-2.5")} />
            {isCertificateEligible ? "Certificate Eligible" : "Not Eligible"}
          </Badge>
          
          {!isCertificateEligible && (
            <span className={cn(
              "text-xs text-muted-foreground",
              compact && "text-[10px]"
            )}>
              Need {formatPercentage(required - current)} more
            </span>
          )}
        </div>
      )}
    </div>
  );
}

export default AttendanceProgressBar;