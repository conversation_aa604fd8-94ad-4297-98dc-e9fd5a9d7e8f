"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { getAttendanceRecords } from "@/services/client/attendance-client.service";
import { AttendanceRecord } from "@/types/attendance/attendance.types";
import {
  Calendar,
  Clock,
  MapPin,
  User,
  CheckCircle,
  XCircle,
  Timer,
  Shield,
  AlertCircle,
  Download,
  Smartphone
} from "lucide-react";

interface ParticipantAttendanceHistoryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  participantId: string;
  programmeId: string;
}

// Attendance record card component
function AttendanceRecordCard({ record }: { record: AttendanceRecord }) {
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "present":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "absent":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "late":
        return <Timer className="h-4 w-4 text-yellow-600" />;
      case "excused":
        return <Shield className="h-4 w-4 text-blue-600" />;
      case "partial":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "present":
        return "bg-green-100 text-green-800 border-green-200";
      case "absent":
        return "bg-red-100 text-red-800 border-red-200";
      case "late":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "excused":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "partial":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case "qr_code":
        return <Smartphone className="h-3 w-3" />;
      case "manual":
        return <User className="h-3 w-3" />;
      case "auto":
        return <Clock className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const formatDuration = (checkIn: string, checkOut?: string) => {
    if (!checkOut) return "Still checked in";
    
    const start = new Date(checkIn);
    const end = new Date(checkOut);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatTime = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch {
      return dateString;
    }
  };

  return (
    <Card className="mb-4">
      <CardContent className="pt-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            {getStatusIcon(record.attendance_status)}
            <Badge 
              variant="outline" 
              className={`font-medium ${getStatusColor(record.attendance_status)}`}
            >
              {record.attendance_status.charAt(0).toUpperCase() + record.attendance_status.slice(1)}
            </Badge>
          </div>
          
          <div className="text-sm text-muted-foreground">
            {record.check_in_time ? formatDate(record.check_in_time) : 'N/A'}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Check-in:</span>
              <span>{record.check_in_time ? formatTime(record.check_in_time) : 'N/A'}</span>
            </div>
            
            {record.check_out_time && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Check-out:</span>
                <span>{formatTime(record.check_out_time)}</span>
              </div>
            )}
            
            <div className="flex items-center gap-2">
              <Timer className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Duration:</span>
              <span>{record.check_in_time ? formatDuration(record.check_in_time, record.check_out_time) : 'N/A'}</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getMethodIcon(record.check_in_method)}
              <span className="font-medium">Method:</span>
              <span className="capitalize">{record.check_in_method.replace("_", " ")}</span>
            </div>
            
            {record.check_in_location && (
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Location:</span>
                <span>{record.check_in_location}</span>
              </div>
            )}
          </div>
        </div>

        {record.notes && (
          <div className="mt-3 pt-3 border-t">
            <div className="text-sm">
              <span className="font-medium text-muted-foreground">Notes:</span>
              <p className="mt-1">{record.notes}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Summary statistics component
function AttendanceSummaryStats({ records }: { records: AttendanceRecord[] }) {
  const stats = {
    total: records.length,
    present: records.filter(r => r.attendance_status === "present").length,
    late: records.filter(r => r.attendance_status === "late").length,
    absent: records.filter(r => r.attendance_status === "absent").length,
    excused: records.filter(r => r.attendance_status === "excused").length,
    totalDuration: 0
  };

  // Calculate total duration
  records.forEach(record => {
    if (record.check_out_time && record.check_in_time) {
      const start = new Date(record.check_in_time);
      const end = new Date(record.check_out_time);
      stats.totalDuration += end.getTime() - start.getTime();
    }
  });

  const totalHours = Math.floor(stats.totalDuration / (1000 * 60 * 60));
  const totalMinutes = Math.floor((stats.totalDuration % (1000 * 60 * 60)) / (1000 * 60));

  const attendanceRate = stats.total > 0 
    ? Math.round(((stats.present + stats.late) / stats.total) * 100) 
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Attendance Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-muted-foreground">Total Sessions</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-green-600">{attendanceRate}%</div>
            <div className="text-sm text-muted-foreground">Attendance Rate</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {totalHours}h {totalMinutes}m
            </div>
            <div className="text-sm text-muted-foreground">Total Duration</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-orange-600">{stats.late}</div>
            <div className="text-sm text-muted-foreground">Late Arrivals</div>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            Present: {stats.present}
          </Badge>
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Late: {stats.late}
          </Badge>
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            Absent: {stats.absent}
          </Badge>
          {stats.excused > 0 && (
            <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
              Excused: {stats.excused}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function ParticipantAttendanceHistory({
  open,
  onOpenChange,
  participantId,
  programmeId
}: ParticipantAttendanceHistoryProps) {
  const [records, setRecords] = useState<AttendanceRecord[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch attendance records for the participant
  const fetchRecords = async () => {
    if (!open || !participantId || !programmeId) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await getAttendanceRecords(
        programmeId,
        { participantIds: [participantId] },
        1,
        100 // Get all records
      );
      
      if (result.success) {
        setRecords(result.data || []);
      } else {
        setError(result.error || 'Failed to fetch attendance history');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, [open, participantId, programmeId]);

  // Export functionality
  const handleExport = () => {
    if (records.length === 0) return;

    const formatDate = (dateString: string) => {
      try {
        return new Date(dateString).toISOString().split('T')[0];
      } catch {
        return dateString;
      }
    };

    const formatTime = (dateString: string) => {
      try {
        return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } catch {
        return dateString;
      }
    };

    const csvContent = [
      ['Date', 'Status', 'Check-in Time', 'Check-out Time', 'Duration', 'Method', 'Location', 'Notes'],
      ...records.map(record => [
        record.check_in_time ? formatDate(record.check_in_time) : 'N/A',
        record.attendance_status,
        record.check_in_time ? formatTime(record.check_in_time) : 'N/A',
        record.check_out_time ? formatTime(record.check_out_time) : "",
        record.check_out_time && record.check_in_time ? 
          `${Math.floor((new Date(record.check_out_time).getTime() - new Date(record.check_in_time).getTime()) / (1000 * 60))} minutes` : 
          "Still checked in",
        record.check_in_method,
        record.check_in_location || "",
        record.notes || ""
      ])
    ]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `attendance-history-${participantId}-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Participant Attendance History</DialogTitle>
          <DialogDescription>
            Complete attendance record for this participant
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {/* Export Controls */}
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-muted-foreground">
              {records.length} attendance record{records.length !== 1 ? 's' : ''} found
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                disabled={records.length === 0}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export CSV
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={fetchRecords}
                disabled={isLoading}
              >
                Refresh
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <LoadingSpinner />
              </div>
            ) : error ? (
              <div className="text-center text-red-600 py-8">
                Error loading attendance history: {error}
              </div>
            ) : records.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No attendance records found for this participant</p>
              </div>
            ) : (
              <div className="space-y-6 h-full overflow-hidden">
                {/* Summary Stats */}
                <AttendanceSummaryStats records={records} />

                {/* Records List */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Attendance Records</h3>
                  <ScrollArea className="h-[400px] pr-4">
                    {records
                      .sort((a, b) => {
                        if (!a.check_in_time || !b.check_in_time) return 0;
                        return new Date(b.check_in_time).getTime() - new Date(a.check_in_time).getTime();
                      })
                      .map((record) => (
                        <AttendanceRecordCard 
                          key={record.id} 
                          record={record} 
                        />
                      ))
                    }
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}