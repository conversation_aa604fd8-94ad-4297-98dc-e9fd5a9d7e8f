'use client'

import React, { createContext, useContext, ReactNode } from 'react'
import { useRB<PERSON> } from '@/hooks/rbac/use-rbac'

type RBACContextType = ReturnType<typeof useRBAC>

const RBACContext = createContext<RBACContextType | null>(null)

interface RBACProviderProps {
  children: ReactNode
}

export const RBACProvider: React.FC<RBACProviderProps> = ({ children }) => {
  const rbac = useRBAC()
  
  return (
    <RBACContext.Provider value={rbac}>
      {children}
    </RBACContext.Provider>
  )
}

export const useRBACContext = () => {
  const context = useContext(RBACContext)
  
  if (!context) {
    throw new Error('useRBACContext must be used within RBACProvider')
  }
  
  return context
}

// Export individual hooks for convenience
export const usePermissions = () => {
  const { permissions, loading } = useRBACContext()
  return { permissions, loading }
}

export const usePermissionCheck = () => {
  const { hasPermission, getCachedPermission } = useRBACContext()
  return { hasPermission, getCachedPermission }
}

export const useAccessibleData = () => {
  const { 
    getAccessibleMemberships, 
    getAccessibleApplications, 
    getUserAccessibleMembershipTypes 
  } = useRBACContext()
  
  return { 
    getAccessibleMemberships, 
    getAccessibleApplications, 
    getUserAccessibleMembershipTypes 
  }
}