'use client'

import { createClient } from '@/utils/supabase/Client'
import { AttendanceStatsResponse } from '@/types/attendance/attendance.types'

/**
 * Get attendance statistics for a programme or all programmes
 */
export async function getAttendanceStatistics(programmeId?: string): Promise<{
  data: AttendanceStatsResponse | null;
  error: string | null;
}> {
  const supabase = createClient();

  try {
    // Build base query for total participants
    let participantsQuery = supabase
      .schema('programme')
      .from('participants')
      .select('id, registrations!inner(status, programme_id)', { count: 'exact' })
      .eq('registrations.status', 'CONFIRMED');

    if (programmeId && programmeId !== 'all') {
      participantsQuery = participantsQuery.eq('registrations.programme_id', programmeId);
    }

    const { count: totalParticipants, error: participantsError } = await participantsQuery;

    if (participantsError) {
      return { data: null, error: participantsError.message };
    }

    // Get attendance summary data
    let summaryQuery = supabase
      .schema('programme')
      .from('attendance_summary')
      .select('attendance_percentage, certificate_eligible, attended_sessions, total_sessions');

    if (programmeId && programmeId !== 'all') {
      summaryQuery = summaryQuery.eq('programme_id', programmeId);
    }

    const { data: summaryData, error: summaryError } = await summaryQuery;

    if (summaryError) {
      return { data: null, error: summaryError.message };
    }

    // Calculate statistics
    const total = totalParticipants || 0;
    let checkedInCount = 0;
    let totalAttendancePercentage = 0;
    let certificateEligibleCount = 0;

    if (summaryData && summaryData.length > 0) {
      summaryData.forEach(summary => {
        if (summary.attended_sessions > 0) {
          checkedInCount++;
        }
        totalAttendancePercentage += summary.attendance_percentage || 0;
        if (summary.certificate_eligible) {
          certificateEligibleCount++;
        }
      });
    }

    const attendanceRate = total > 0 ? (checkedInCount / total) * 100 : 0;
    const averageAttendancePercentage = summaryData && summaryData.length > 0 
      ? totalAttendancePercentage / summaryData.length 
      : 0;

    const stats: AttendanceStatsResponse = {
      total_participants: total,
      checked_in_count: checkedInCount,
      attendance_rate: Math.round(attendanceRate * 10) / 10,
      average_attendance_percentage: Math.round(averageAttendancePercentage * 10) / 10,
      certificate_eligible_count: certificateEligibleCount
    };

    return { data: stats, error: null };
  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Get list of programmes with attendance tracking
 */
export async function getProgrammesWithAttendance(): Promise<{
  data: Array<{ id: string; name: string }> | null;
  error: string | null;
}> {
  const supabase = createClient();

  try {
    const { data: programmes, error } = await supabase
      .from('programmes')
      .select(`
        id,
        name,
        registrations!inner(
          id,
          status
        )
      `)
      .eq('registrations.status', 'CONFIRMED')
      .order('name');

    if (error) {
      return { data: null, error: error.message };
    }

    // Filter unique programmes
    const uniqueProgrammes = programmes?.reduce((acc, prog) => {
      if (!acc.find(p => p.id === prog.id)) {
        acc.push({ id: prog.id, name: prog.name });
      }
      return acc;
    }, [] as Array<{ id: string; name: string }>) || [];

    return { data: uniqueProgrammes, error: null };
  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}