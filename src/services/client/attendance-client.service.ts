'use client'

import { createClient } from '@/utils/supabase/Client'
import {
  AttendanceRecord,
  SessionAttendance,
  AttendanceSummary,
  AttendanceConfiguration,
  ParticipantWithAttendance,
  AttendanceReportRecord,
  CheckInRequest,
  CheckOutRequest,
  BulkCheckInRequest,
  AttendanceStatusSummary,
  CertificateEligibilityResult,
  AttendanceExportOptions,
  PaginatedAttendanceResponse,
  AttendanceApiResponse
} from '@/types/attendance/attendance.types';

/**
 * Client-side attendance service for browser operations
 * Follows the existing pattern from programmes/events-client.service.ts
 */

// ==================== CORE CRUD OPERATIONS ====================

/**
 * Check in a participant manually
 */
export async function checkInParticipant(request: CheckInRequest): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = createClient();

  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    
    // First, check if participant already has an attendance record for today
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999).toISOString();
    
    const { data: existingRecord } = await supabase
      .schema('programme')
      .from('attendance_records')
      .select('*')
      .eq('participant_id', request.participant_id)
      .eq('programme_id', request.programme_id)
      .gte('check_in_time', startOfDay)
      .lte('check_in_time', endOfDay)
      .single();

    // If already checked in for today
    if (existingRecord) {
      // Check if checkout is required and if they haven't checked out yet
      const { data: config } = await supabase
        .schema('programme')
        .from('attendance_configuration')
        .select('require_checkout')
        .eq('programme_id', request.programme_id)
        .single();
      
      if (config?.require_checkout && !existingRecord.check_out_time) {
        return { 
          data: null as any, 
          error: 'Participant is currently checked in. Please check out first before checking in again.', 
          success: false 
        };
      } else if (!config?.require_checkout) {
        return { 
          data: null as any, 
          error: 'Participant has already checked in today', 
          success: false 
        };
      }
    }
    
    // Create new attendance record
    const { data: attendanceRecord, error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .insert({
        participant_id: request.participant_id,
        programme_id: request.programme_id,
        schedule_id: request.schedule_id,
        qr_code_id: request.qr_code_id,
        check_in_time: new Date().toISOString(),
        attendance_status: 'present',
        check_in_method: request.check_in_method,
        check_in_location: request.check_in_location,
        check_in_device_info: request.check_in_device_info,
        checked_in_by: user?.id || 'system',
        notes: request.notes
      })
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    // Also create session attendance record if schedule_id is provided
    if (request.schedule_id && attendanceRecord) {
      await supabase
        .schema('programme')
        .from('session_attendance')
        .upsert({
          attendance_record_id: attendanceRecord.id,
          participant_id: request.participant_id,
          programme_id: request.programme_id,
          schedule_id: request.schedule_id,
          session_date: new Date().toISOString().split('T')[0],
          is_present: true,
          arrival_time: new Date().toISOString(),
          session_type: 'regular'
        });
    }

    return { data: attendanceRecord, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Check out a participant
 */
export async function checkOutParticipant(request: CheckOutRequest): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = createClient();

  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    
    // If attendance_record_id is provided, use it directly
    if (request.attendance_record_id) {
      const { data, error } = await supabase
        .schema('programme')
        .from('attendance_records')
        .update({
          check_out_time: request.check_out_time || new Date().toISOString(),
          checked_out_by: user?.id || 'system'
        })
        .eq('id', request.attendance_record_id)
        .is('check_out_time', null) // Only update if not already checked out
        .select()
        .single();

      if (error) {
        return { data: null as any, error: error.message, success: false };
      }

      if (!data) {
        return { data: null as any, error: 'No active check-in found or already checked out', success: false };
      }

      return { data, success: true };
    }
    
    // If no attendance_record_id, find today's check-in record
    if (request.participant_id && request.programme_id) {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999).toISOString();
      
      // Find today's attendance records where check_out_time is null
      const { data: attendanceRecords } = await supabase
        .schema('programme')
        .from('attendance_records')
        .select('*')
        .eq('participant_id', request.participant_id)
        .eq('programme_id', request.programme_id)
        .is('check_out_time', null)
        .order('check_in_time', { ascending: false });

      // Filter for today's records and get the most recent one
      const todayRecords = attendanceRecords?.filter(record => {
        if (!record.check_in_time) return false;
        return record.check_in_time >= startOfDay && record.check_in_time <= endOfDay;
      }) || [];
      
      const attendanceRecord = todayRecords.length > 0 ? todayRecords[0] : null;

      if (!attendanceRecord) {
        return { 
          data: null as any, 
          error: 'No active check-in found for today', 
          success: false 
        };
      }

      // Update the record with check-out time
      const { data, error } = await supabase
        .schema('programme')
        .from('attendance_records')
        .update({
          check_out_time: request.check_out_time || new Date().toISOString(),
          checked_out_by: user?.id || 'system'
        })
        .eq('id', attendanceRecord.id)
        .select()
        .single();

      if (error) {
        return { data: null as any, error: error.message, success: false };
      }

      return { data, success: true };
    }
    
    return { 
      data: null as any, 
      error: 'Either attendance_record_id or participant_id and programme_id must be provided', 
      success: false 
    };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Bulk check-in multiple participants
 */
export async function bulkCheckInParticipants(request: BulkCheckInRequest): Promise<AttendanceApiResponse<AttendanceRecord[]>> {
  const supabase = createClient();

  try {
    const records = request.participant_ids.map(id => ({
      participant_id: id,
      programme_id: request.programme_id,
      schedule_id: request.schedule_id,
      check_in_time: new Date().toISOString(),
      attendance_status: 'present' as const,
      check_in_method: request.check_in_method,
      check_in_location: request.check_in_location,
      notes: request.notes
    }));

    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .insert(records)
      .select();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== ATTENDANCE DATA RETRIEVAL ====================

/**
 * Get attendance records for a programme
 */
export async function getAttendanceRecords(
  programmeId: string,
  filters?: {
    participantIds?: string[];
    scheduleId?: string;
    attendanceStatus?: string[];
    dateFrom?: string;
    dateTo?: string;
  },
  page: number = 1,
  limit: number = 50
): Promise<PaginatedAttendanceResponse<AttendanceRecord>> {
  const supabase = createClient();

  try {
    let query = supabase
      .schema('programme')
      .from('attendance_records')
      .select('*', { count: 'exact' })
      .eq('programme_id', programmeId);

    // Apply filters
    if (filters?.participantIds?.length) {
      query = query.in('participant_id', filters.participantIds);
    }

    if (filters?.scheduleId) {
      query = query.eq('schedule_id', filters.scheduleId);
    }

    if (filters?.attendanceStatus?.length) {
      query = query.in('attendance_status', filters.attendanceStatus);
    }

    if (filters?.dateFrom) {
      query = query.gte('check_in_time', `${filters.dateFrom}T00:00:00.000Z`);
    }

    if (filters?.dateTo) {
      query = query.lte('check_in_time', `${filters.dateTo}T23:59:59.999Z`);
    }

    const { data, count, error } = await query
      .order('check_in_time', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      return { data: [], total: 0, page, limit, success: false, error: error.message };
    }

    return { data: data || [], total: count || 0, page, limit, success: true };
  } catch (error) {
    return { 
      data: [], 
      total: 0, 
      page, 
      limit, 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Get participants with attendance data for a programme
 */
export async function getParticipantsWithAttendance(
  programmeId: string,
  filters: { subFilters: { [key: string]: string[] } },
  page: number,
  limit: number,
  searchTerm?: string
): Promise<PaginatedAttendanceResponse<ParticipantWithAttendance>> {
  const supabase = createClient();

  try {
    // Get today's date range
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();
    const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();
    
    let mainQuery = supabase
      .schema('programme')
      .from('participants')
      .select(`
        *,
        registrations!inner(
          programme_id,
          status
        ),
        attendance_records(
          id,
          attendance_status,
          check_in_time,
          check_out_time
        ),
        attendance_summary(
          attendance_percentage,
          certificate_eligible,
          attended_sessions,
          total_sessions
        )
      `, { count: "exact" })
      .eq('registrations.programme_id', programmeId)
      .eq('registrations.status', 'CONFIRMED');

    // Apply filters
    Object.keys(filters.subFilters).forEach((filterKey) => {
      const filterValues = filters.subFilters[filterKey];
      if (Array.isArray(filterValues) && filterValues.length > 0) {
        mainQuery = mainQuery.in(filterKey, filterValues);
      }
    });

    // Apply search term
    if (searchTerm) {
      const normalizedSearchTerm = searchTerm.trim();
      mainQuery = mainQuery.or(
        `name.ilike.%${normalizedSearchTerm}%,email.ilike.%${normalizedSearchTerm}%,contact_no.ilike.%${normalizedSearchTerm}%`
      );
    }

    const { data: participants, count: total, error } = await mainQuery
      .order("name")
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      return { data: [], total: 0, page, limit, success: false, error: error.message };
    }

    if (!participants) {
      return { data: [], total: total || 0, page, limit, success: true };
    }

    // Transform data to include attendance information
    const participantsWithAttendance: ParticipantWithAttendance[] = participants.map(participant => {
      // Sort attendance records by check_in_time descending to get most recent first
      const sortedRecords = participant.attendance_records?.sort((a: any, b: any) => {
        if (!a.check_in_time) return 1;
        if (!b.check_in_time) return -1;
        return new Date(b.check_in_time).getTime() - new Date(a.check_in_time).getTime();
      }) || [];
      
      // Find today's attendance record (most recent record for today)
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
      const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999).toISOString();
      
      const todayAttendance = sortedRecords.find((record: any) => {
        if (!record.check_in_time) return false;
        return record.check_in_time >= todayStart && record.check_in_time <= todayEnd;
      });
      
      // Get the most recent attendance record (for display purposes)
      const latestAttendance = sortedRecords[0];
      const summary = participant.attendance_summary?.[0];

      return {
        ...participant,
        latest_attendance: todayAttendance || latestAttendance,
        current_attendance_status: todayAttendance?.attendance_status || 'absent',
        attendance_percentage: summary?.attendance_percentage || 0,
        certificate_eligible: summary?.certificate_eligible || false,
        last_check_in_time: latestAttendance?.check_in_time,
        can_check_in: !todayAttendance || todayAttendance.check_out_time !== null,
        can_check_out: todayAttendance && !todayAttendance.check_out_time
      };
    });

    return { 
      data: participantsWithAttendance, 
      total: total || 0, 
      page, 
      limit, 
      success: true 
    };
  } catch (error) {
    return { 
      data: [], 
      total: 0, 
      page, 
      limit, 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Get attendance summary for a programme
 */
export async function getAttendanceSummary(programmeId: string): Promise<AttendanceApiResponse<AttendanceStatusSummary>> {
  const supabase = createClient();

  try {
    // Get participants with their attendance records to determine current status
    const { data: participants } = await supabase
      .schema('programme')
      .from('participants')
      .select(`
        id,
        registrations!inner(
          programme_id,
          status
        ),
        attendance_records(
          id,
          attendance_status,
          check_in_time,
          check_out_time
        )
      `)
      .eq('registrations.programme_id', programmeId)
      .eq('registrations.status', 'CONFIRMED');

    const statusCounts = {
      total: participants?.length || 0,
      present: 0,
      absent: 0,
      late: 0,
      excused: 0,
      partial: 0,
      percentage: 0
    };

    if (participants) {
      // Get today's date range
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
      const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999).toISOString();

      participants.forEach(participant => {
        // Sort attendance records by check_in_time descending to get most recent first
        const sortedRecords = participant.attendance_records?.sort((a: any, b: any) => {
          if (!a.check_in_time) return 1;
          if (!b.check_in_time) return -1;
          return new Date(b.check_in_time).getTime() - new Date(a.check_in_time).getTime();
        }) || [];
        
        // Find today's attendance record (most recent record for today)
        const todayAttendance = sortedRecords.find((record: any) => {
          if (!record.check_in_time) return false;
          return record.check_in_time >= todayStart && record.check_in_time <= todayEnd;
        });
        
        // Determine current status based on today's attendance
        const currentStatus = todayAttendance?.attendance_status || 'absent';
        
        // Count by attendance status for the day (not by current check-in/out state)
        if (currentStatus in statusCounts) {
          statusCounts[currentStatus as keyof typeof statusCounts]++;
        }
      });
    }

    // Calculate attendance percentage
    const attendedCount = statusCounts.present + statusCounts.late + statusCounts.partial;
    statusCounts.percentage = statusCounts.total > 0 
      ? Math.round((attendedCount / statusCounts.total) * 100) 
      : 0;

    return { data: statusCounts, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== CONFIGURATION MANAGEMENT ====================

/**
 * Get attendance configuration for a programme
 */
export async function getAttendanceConfig(programmeId: string): Promise<AttendanceApiResponse<AttendanceConfiguration>> {
  const supabase = createClient();

  try {
    let { data: config, error } = await supabase
      .schema('programme')
      .from('attendance_configuration')
      .select('*')
      .eq('programme_id', programmeId)
      .single();

    // If no config exists, create one with defaults
    if (!config && error?.code === 'PGRST116') {
      const { data: newConfig, error: createError } = await supabase
        .schema('programme')
        .from('attendance_configuration')
        .insert({
          programme_id: programmeId,
          minimum_attendance_percentage: 80,
          late_arrival_threshold_minutes: 15,
          early_departure_threshold_minutes: 15,
          certificate_minimum_attendance: 80,
          allow_self_checkin: true,
          require_checkout: false,
          checkin_starts_minutes_before: 30,
          checkin_ends_minutes_after: 30,
          qr_code_enabled: true,
          qr_code_refresh_interval_minutes: 60
        })
        .select()
        .single();

      if (createError) {
        return { data: null as any, error: createError.message, success: false };
      }

      config = newConfig;
    } else if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data: config, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Update attendance configuration for a programme
 */
export async function updateAttendanceConfig(
  programmeId: string, 
  updates: Partial<AttendanceConfiguration>
): Promise<AttendanceApiResponse<AttendanceConfiguration>> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_configuration')
      .update(updates)
      .eq('programme_id', programmeId)
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== CERTIFICATE ELIGIBILITY ====================

/**
 * Check certificate eligibility for a participant
 */
export async function checkCertificateEligibility(
  participantId: string,
  programmeId: string
): Promise<AttendanceApiResponse<CertificateEligibilityResult>> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .schema('programme')
      .rpc('check_certificate_eligibility', {
        p_participant_id: participantId,
        p_programme_id: programmeId
      });

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data: { eligible: data } as any, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== REPORTING AND EXPORT ====================

/**
 * Get attendance report data
 */
export async function getAttendanceReport(programmeId: string): Promise<AttendanceApiResponse<AttendanceReportRecord[]>> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_report')
      .select('*')
      .eq('programme_id', programmeId)
      .order('participant_name');

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data: data || [], success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Export attendance data
 */
export async function exportAttendanceData(options: AttendanceExportOptions): Promise<AttendanceApiResponse<Blob>> {
  const supabase = createClient();

  try {
    // Get the attendance report data
    let query = supabase
      .schema('programme')
      .from('attendance_report')
      .select('*')
      .eq('programme_id', options.programme_id);

    if (options.participant_ids?.length) {
      query = query.in('participant_id', options.participant_ids);
    }

    const { data, error } = await query.order('participant_name');

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    // For now, return CSV format (PDF and Excel would require additional libraries)
    const csvContent = convertToCSV(data || [], options);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    return { data: blob, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== HELPER FUNCTIONS ====================

/**
 * Convert attendance data to CSV format
 */
function convertToCSV(data: AttendanceReportRecord[], options: AttendanceExportOptions): string {
  if (!data.length) return '';

  const headers = [
    'Participant Name',
    'Email',
    'Contact',
    'Registration Type',
    'Registration Status'
  ];

  if (options.include_summary) {
    headers.push(
      'Total Sessions',
      'Attended Sessions',
      'Attendance Percentage',
      'Total Duration (minutes)'
    );
  }

  if (options.include_certificate_status) {
    headers.push(
      'Certificate Eligible',
      'Certificate Issued',
      'Certificate Number'
    );
  }

  const rows = data.map(record => {
    const row = [
      record.participant_name,
      record.participant_email,
      record.participant_contact,
      record.registration_type,
      record.registration_status
    ];

    if (options.include_summary) {
      row.push(
        record.total_sessions.toString(),
        record.attended_sessions.toString(),
        `${record.attendance_percentage}%`,
        record.total_duration_minutes.toString()
      );
    }

    if (options.include_certificate_status) {
      row.push(
        record.certificate_eligible ? 'Yes' : 'No',
        record.certificate_issued ? 'Yes' : 'No',
        record.certificate_number || ''
      );
    }

    return row;
  });

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');

  return csvContent;
}

/**
 * Get current user device info for security logging
 */
export function getUserDeviceInfo(): Record<string, any> {
  if (typeof window === 'undefined') return {};

  return {
    userAgent: window.navigator.userAgent,
    platform: window.navigator.platform,
    language: window.navigator.language,
    screen: {
      width: window.screen.width,
      height: window.screen.height
    },
    timestamp: new Date().toISOString()
  };
}