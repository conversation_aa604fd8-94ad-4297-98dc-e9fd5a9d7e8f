'use server'

import { createClient } from '@/utils/supabase/server'
import {
  AttendanceRecord,
  AttendanceConfiguration,
  CheckInRequest,
  CheckOutRequest,
  BulkCheckInRequest,
  AttendanceApiResponse
} from '@/types/attendance/attendance.types';

/**
 * Server-side attendance service for secure operations
 * Follows the existing pattern from programmes/events-server.service.ts
 */

// ==================== SECURE CHECK-IN OPERATIONS ====================

/**
 * Server-side check-in with user authentication
 */
export async function serverCheckInParticipant(
  request: CheckInRequest,
  checkedInBy: string
): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = await createClient();

  try {
    // First, verify the participant exists and is registered for this programme
    const { data: participant, error: participantError } = await supabase
      .schema('programme')
      .from('participants')
      .select(`
        *,
        registrations!inner(programme_id, status)
      `)
      .eq('id', request.participant_id)
      .eq('registrations.programme_id', request.programme_id)
      .eq('registrations.status', 'CONFIRMED')
      .single();

    if (participantError || !participant) {
      return { 
        data: null as any, 
        error: 'Participant not found or not registered for this programme', 
        success: false 
      };
    }

    // Check if participant is already checked in (and not checked out)
    const { data: existingRecord } = await supabase
      .schema('programme')
      .from('attendance_records')
      .select('*')
      .eq('participant_id', request.participant_id)
      .eq('programme_id', request.programme_id)
      .is('check_out_time', null)
      .order('check_in_time', { ascending: false })
      .limit(1)
      .single();

    if (existingRecord) {
      return { 
        data: null as any, 
        error: 'Participant is already checked in', 
        success: false 
      };
    }

    // Create attendance record
    const { data: attendanceRecord, error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .insert({
        participant_id: request.participant_id,
        programme_id: request.programme_id,
        schedule_id: request.schedule_id,
        qr_code_id: request.qr_code_id,
        check_in_time: new Date().toISOString(),
        attendance_status: 'present',
        check_in_method: request.check_in_method,
        check_in_location: request.check_in_location,
        check_in_device_info: request.check_in_device_info,
        checked_in_by: checkedInBy,
        notes: request.notes
      })
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    // Create session attendance record if schedule_id is provided
    if (request.schedule_id && attendanceRecord) {
      await supabase
        .schema('programme')
        .from('session_attendance')
        .upsert({
          attendance_record_id: attendanceRecord.id,
          participant_id: request.participant_id,
          programme_id: request.programme_id,
          schedule_id: request.schedule_id,
          session_date: new Date().toISOString().split('T')[0],
          is_present: true,
          arrival_time: new Date().toISOString(),
          session_type: 'regular'
        });
    }

    return { data: attendanceRecord, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Server-side check-out with user authentication
 */
export async function serverCheckOutParticipant(
  request: CheckOutRequest,
  checkedOutBy: string
): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = await createClient();

  try {
    // Verify the attendance record exists and belongs to the user
    const { data: existingRecord, error: verifyError } = await supabase
      .schema('programme')
      .from('attendance_records')
      .select('*')
      .eq('id', request.attendance_record_id)
      .single();

    if (verifyError || !existingRecord) {
      return { 
        data: null as any, 
        error: 'Attendance record not found', 
        success: false 
      };
    }

    if (existingRecord.check_out_time) {
      return { 
        data: null as any, 
        error: 'Participant is already checked out', 
        success: false 
      };
    }

    // Update the attendance record
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .update({
        check_out_time: request.check_out_time || new Date().toISOString(),
        checked_out_by: checkedOutBy,
        attendance_status: 'present'
      })
      .eq('id', request.attendance_record_id)
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    // Update session attendance if exists
    if (existingRecord.schedule_id) {
      await supabase
        .schema('programme')
        .from('session_attendance')
        .update({
          departure_time: request.check_out_time || new Date().toISOString()
        })
        .eq('attendance_record_id', request.attendance_record_id);
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Server-side bulk check-in with validation
 */
export async function serverBulkCheckInParticipants(
  request: BulkCheckInRequest,
  checkedInBy: string
): Promise<AttendanceApiResponse<{ successful: AttendanceRecord[], failed: { participant_id: string, error: string }[] }>> {
  const supabase = await createClient();

  try {
    const successful: AttendanceRecord[] = [];
    const failed: { participant_id: string, error: string }[] = [];

    // Process each participant individually to handle errors gracefully
    for (const participantId of request.participant_ids) {
      try {
        // Verify participant exists and is registered
        const { data: participant, error: participantError } = await supabase
          .schema('programme')
          .from('participants')
          .select(`
            *,
            registrations!inner(programme_id, status)
          `)
          .eq('id', participantId)
          .eq('registrations.programme_id', request.programme_id)
          .eq('registrations.status', 'CONFIRMED')
          .single();

        if (participantError || !participant) {
          failed.push({ participant_id: participantId, error: 'Not registered or not confirmed' });
          continue;
        }

        // Check if already checked in
        const { data: existingRecord } = await supabase
          .schema('programme')
          .from('attendance_records')
          .select('*')
          .eq('participant_id', participantId)
          .eq('programme_id', request.programme_id)
          .is('check_out_time', null)
          .order('check_in_time', { ascending: false })
          .limit(1)
          .single();

        if (existingRecord) {
          failed.push({ participant_id: participantId, error: 'Already checked in' });
          continue;
        }

        // Create attendance record
        const { data: attendanceRecord, error: insertError } = await supabase
          .schema('programme')
          .from('attendance_records')
          .insert({
            participant_id: participantId,
            programme_id: request.programme_id,
            schedule_id: request.schedule_id,
            check_in_time: new Date().toISOString(),
            attendance_status: 'present',
            check_in_method: request.check_in_method,
            check_in_location: request.check_in_location,
            checked_in_by: checkedInBy,
            notes: request.notes
          })
          .select()
          .single();

        if (insertError) {
          failed.push({ participant_id: participantId, error: insertError.message });
          continue;
        }

        successful.push(attendanceRecord);

        // Create session attendance if schedule provided
        if (request.schedule_id) {
          await supabase
            .schema('programme')
            .from('session_attendance')
            .upsert({
              attendance_record_id: attendanceRecord.id,
              participant_id: participantId,
              programme_id: request.programme_id,
              schedule_id: request.schedule_id,
              session_date: new Date().toISOString().split('T')[0],
              is_present: true,
              arrival_time: new Date().toISOString(),
              session_type: 'regular'
            });
        }
      } catch (error) {
        failed.push({ 
          participant_id: participantId, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return { 
      data: { successful, failed }, 
      success: true 
    };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== CONFIGURATION MANAGEMENT ====================

/**
 * Create attendance configuration for a programme
 */
export async function createAttendanceConfiguration(
  programmeId: string,
  config: Partial<AttendanceConfiguration>
): Promise<AttendanceApiResponse<AttendanceConfiguration>> {
  const supabase = await createClient();

  try {
    // Check if configuration already exists
    const { data: existingConfig } = await supabase
      .schema('programme')
      .from('attendance_configuration')
      .select('*')
      .eq('programme_id', programmeId)
      .single();

    if (existingConfig) {
      return { 
        data: null as any, 
        error: 'Configuration already exists for this programme', 
        success: false 
      };
    }

    // Create new configuration with defaults
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_configuration')
      .insert({
        programme_id: programmeId,
        minimum_attendance_percentage: config.minimum_attendance_percentage || 80,
        late_arrival_threshold_minutes: config.late_arrival_threshold_minutes || 15,
        early_departure_threshold_minutes: config.early_departure_threshold_minutes || 15,
        certificate_minimum_attendance: config.certificate_minimum_attendance || 80,
        certificate_minimum_sessions: config.certificate_minimum_sessions,
        allow_self_checkin: config.allow_self_checkin !== undefined ? config.allow_self_checkin : true,
        require_checkout: config.require_checkout !== undefined ? config.require_checkout : false,
        checkin_starts_minutes_before: config.checkin_starts_minutes_before || 30,
        checkin_ends_minutes_after: config.checkin_ends_minutes_after || 30,
        qr_code_enabled: config.qr_code_enabled !== undefined ? config.qr_code_enabled : true,
        qr_code_refresh_interval_minutes: config.qr_code_refresh_interval_minutes || 60
      })
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Update attendance configuration (server-side validation)
 */
export async function serverUpdateAttendanceConfiguration(
  programmeId: string,
  updates: Partial<AttendanceConfiguration>
): Promise<AttendanceApiResponse<AttendanceConfiguration>> {
  const supabase = await createClient();

  try {
    // Verify programme exists and user has permission
    const { data: programme, error: programmeError } = await supabase
      .schema('programme')
      .from('programmes')
      .select('id')
      .eq('id', programmeId)
      .single();

    if (programmeError || !programme) {
      return { 
        data: null as any, 
        error: 'Programme not found', 
        success: false 
      };
    }

    // Filter only allowed updates (exclude hidden fields from UI updates)
    const allowedUpdates: Partial<AttendanceConfiguration> = {};
    
    if (updates.allow_self_checkin !== undefined) {
      allowedUpdates.allow_self_checkin = updates.allow_self_checkin;
    }
    
    if (updates.qr_code_enabled !== undefined) {
      allowedUpdates.qr_code_enabled = updates.qr_code_enabled;
    }

    // Update configuration
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_configuration')
      .update(allowedUpdates)
      .eq('programme_id', programmeId)
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== ADMIN OPERATIONS ====================

/**
 * Mark participant as absent (admin function)
 */
export async function markParticipantAbsent(
  participantId: string,
  programmeId: string,
  scheduleId?: string,
  reason?: string
): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .insert({
        participant_id: participantId,
        programme_id: programmeId,
        schedule_id: scheduleId,
        check_in_time: new Date().toISOString(),
        attendance_status: 'absent',
        check_in_method: 'manual',
        notes: reason
      })
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Update attendance status (admin function)
 */
export async function updateAttendanceStatus(
  attendanceRecordId: string,
  status: 'present' | 'absent' | 'late' | 'excused' | 'partial',
  notes?: string
): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .update({
        attendance_status: status,
        notes: notes
      })
      .eq('id', attendanceRecordId)
      .select()
      .single();

    if (error) {
      return { data: null as any, error: error.message, success: false };
    }

    return { data, success: true };
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

/**
 * Delete attendance record (admin function)
 */
export async function deleteAttendanceRecord(attendanceRecordId: string): Promise<AttendanceApiResponse<boolean>> {
  const supabase = await createClient();

  try {
    // First delete related session attendance
    await supabase
      .schema('programme')
      .from('session_attendance')
      .delete()
      .eq('attendance_record_id', attendanceRecordId);

    // Then delete the attendance record
    const { error } = await supabase
      .schema('programme')
      .from('attendance_records')
      .delete()
      .eq('id', attendanceRecordId);

    if (error) {
      return { data: false, error: error.message, success: false };
    }

    return { data: true, success: true };
  } catch (error) {
    return { 
      data: false, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}

// ==================== QR CODE INTEGRATION ====================

/**
 * Process QR code check-in
 */
export async function processQRCodeCheckIn(
  qrCodeData: string,
  participantId: string,
  deviceInfo?: Record<string, any>
): Promise<AttendanceApiResponse<AttendanceRecord>> {
  const supabase = await createClient();

  try {
    // Parse QR code to get programme/schedule info
    let qrInfo;
    try {
      qrInfo = JSON.parse(qrCodeData);
    } catch {
      return { 
        data: null as any, 
        error: 'Invalid QR code format', 
        success: false 
      };
    }

    // Verify QR code is valid and active
    const { data: qrCode, error: qrError } = await supabase
      .schema('programme')
      .from('qr_codes')
      .select('*')
      .eq('id', qrInfo.qrCodeId)
      .eq('action', 'CHECKIN')
      .single();

    if (qrError || !qrCode) {
      return { 
        data: null as any, 
        error: 'Invalid or expired QR code', 
        success: false 
      };
    }

    // Process check-in
    const checkInRequest: CheckInRequest = {
      participant_id: participantId,
      programme_id: qrCode.programme_id,
      schedule_id: qrCode.schedule_id,
      qr_code_id: qrCode.id,
      check_in_method: 'qr_code',
      check_in_device_info: deviceInfo
    };

    return await serverCheckInParticipant(checkInRequest, participantId);
  } catch (error) {
    return { 
      data: null as any, 
      error: error instanceof Error ? error.message : 'Unknown error', 
      success: false 
    };
  }
}