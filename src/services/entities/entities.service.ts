"use client";

import { createClient } from "@/utils/supabase/Client";

export interface Entity {
  id: string;
  name: string;
  merchant_abbreviation?: string;
  entity_type: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface DropdownOption {
  value: string;
  label: string;
}

// Get active entities for dropdown
export async function getActiveEntities(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("finance")
      .from("entities")
      .select("id, name, merchant_abbreviation")
      .order("name");

    if (error) {
      console.error("Error fetching active entities:", error);
      
      if (error.message?.includes("schema") || error.message?.includes("table")) {
        console.warn("Entities table not available, returning empty list");
        return [];
      }
      
      throw new Error(`Failed to load entities: ${error.message}`);
    }

    if (!data || data.length === 0) {
      console.warn("No active entities found");
      return [];
    }

    return data.map(entity => ({
      value: entity.id,
      label: entity.merchant_abbreviation ? `${entity.merchant_abbreviation} - ${entity.name}` : entity.name
    }));
    
  } catch (error) {
    console.error("Error in getActiveEntities:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    return [];
  }
}

// Get entity by ID
export async function getEntityById(id: string): Promise<Entity | null> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("finance")
      .from("entities")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching entity:", error);
      
      if (error.code === "PGRST116") {
        return null; // Entity not found
      }
      
      throw new Error(`Failed to load entity: ${error.message}`);
    }

    return data as Entity;
    
  } catch (error) {
    console.error("Error in getEntityById:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    return null;
  }
}

// Search entities
export async function searchEntities(searchTerm: string): Promise<Entity[]> {
  const supabase = await createClient();

  try {
    if (!searchTerm?.trim()) {
      return [];
    }

    const normalizedSearchTerm = searchTerm.trim();

    const { data, error } = await supabase
      .schema("finance")
      .from("entities")
      .select("id, name, merchant_abbreviation, description, entity_type")
      .or(
        `name.ilike.%${normalizedSearchTerm}%,` +
        `merchant_abbreviation.ilike.%${normalizedSearchTerm}%,` +
        `description.ilike.%${normalizedSearchTerm}%`
      )
      .order("name")
      .limit(20);

    if (error) {
      console.error("Error searching entities:", error);
      throw new Error(`Failed to search entities: ${error.message}`);
    }

    return data as Entity[] || [];
    
  } catch (error) {
    console.error("Error in searchEntities:", error);
    return [];
  }
}