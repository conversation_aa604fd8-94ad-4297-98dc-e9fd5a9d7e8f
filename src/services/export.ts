import { createClient } from '@/utils/supabase/Client'

type ExportJobStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'

export interface ExportJob {
  status: ExportJobStatus
  file_url?: string
  error?: string
}

export const startExport = async (selectedIds: (string | null)[]) => {
  const supabase = await createClient()

  const { data, error } = await supabase.functions.invoke('export-applications', {
    body: {
      selectedIds: selectedIds,
    }
  })

  if (error) throw error
  return data
}

export const checkExportStatus = async (jobId: string) => {
  const supabase = await createClient()

  const { data, error } = await supabase.functions.invoke('check-export-status', {
    body: { jobId }
  })

  if (error) throw error
  return data as ExportJob
} 