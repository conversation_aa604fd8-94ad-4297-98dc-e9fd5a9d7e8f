import { createClient } from "@/utils/supabase/Client";
import type { Database } from "@/types/database.types";
import J<PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';

type ExportJob = Database['public']['Tables']['export_jobs']['Row'];
type ExportType = Database['public']['Enums']['export_type'];

export class ReportService {
  private static async getSignedUrl(filename: string): Promise<string | null> {
    const supabase = await createClient();
    
    try {
      const { data, error } = await supabase
        .storage
        .from('data-export')
        .createSignedUrl(filename, 60);

      if (error) {
        console.error("Error getting signed URL:", error);
        return null;
      }

      return data.signedUrl;
    } catch (error) {
      console.error("Error in getSignedUrl:", error);
      return null;
    }
  }

  static async fetchReports(exportType: ExportType): Promise<ExportJob[]> {
    try {
      const supabase = await createClient();
      const { data, error } = await supabase
        .from('export_jobs')
        .select('*')
        .eq('export_type', exportType)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching reports:', error);
      throw error;
    }
  }

  static async downloadReport(report: ExportJob): Promise<void> {
    if (!report.filename) {
      throw new Error('No filename provided');
    }

    try {
      const signedUrl = await this.getSignedUrl(report.filename);
      
      if (!signedUrl) {
        throw new Error('Could not generate download URL');
      }

      const response = await fetch(signedUrl);
      const blob = await response.blob();
      saveAs(blob, report.filename);

    } catch (error) {
      console.error('Error downloading report:', error);
      throw error;
    }
  }

  static async downloadMultipleReports(reports: ExportJob[]): Promise<void> {
    try {
      const zip = new JSZip();
      
      // Fetch all reports and add them to the zip
      const downloadPromises = reports.map(async (report) => {
        if (!report.filename) return;
        
        const signedUrl = await this.getSignedUrl(report.filename);
        if (!signedUrl) {
          throw new Error(`Could not generate download URL for ${report.filename}`);
        }
        
        const response = await fetch(signedUrl);
        const blob = await response.blob();
        zip.file(report.filename, blob);
      });

      // Wait for all downloads to complete
      await Promise.all(downloadPromises);

      // Generate and download zip file
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      saveAs(zipBlob, 'reports.zip');

    } catch (error) {
      console.error("Error downloading reports:", error);
      throw error;
    }
  }
} 