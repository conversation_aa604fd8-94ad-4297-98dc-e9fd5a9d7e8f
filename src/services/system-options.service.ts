import { SystemOptions, option_type } from "@/types/members/options"
import { createClient } from "@/utils/supabase/Client"

export async function getPersonalInfoOptions(): Promise<SystemOptions> {
  const supabase = await createClient()

  const [
    { data: countries, error: countriesError },
    { data: salutations, error: salutationsError },
    { data: idTypes, error: idTypesError },
    { data: languages, error: languagesError },
    { data: institutes, error: institutesError },
    { data: instituteCourse, error: instituteCourseError },
    { data: industries, error: industriesError }
  ] = await Promise.all([
    supabase.schema('system').from('options').select('code, label')
      .match({ type: "country", is_active: "TRUE" })
      .order('label'),
    supabase.schema('system').from('options').select('code, label')
      .match({ type: "salutation", is_active: "TRUE" })
      .order('label'),
    supabase.schema('system').from('options').select('code, label')
      .match({ type: "id_type", is_active: "TRUE" })
      .order('label'),
    supabase.schema('system').from('options').select('code, label')
      .match({ type: "language", is_active: "TRUE" })
      .order('label'),
    supabase.from('institutes').select('id, name, country')
      .order('name'),
    supabase.from('institute_courses').select('id, name, category')
      .order('category'),
    supabase.from('sys_industries').select('id, name, group')
      .order('group')
  ])

  if (countriesError || salutationsError || idTypesError || languagesError || institutesError || instituteCourseError || industriesError) {
    throw new Error('Failed to fetch options')
  }

  return {
    countries: countries,
    salutations: salutations,
    idTypes: idTypes,
    languages: languages,
    institute: institutes,
    instituteCourse: instituteCourse,
    industries: industries,
  }
}

// Helper functions for View Component
export async function getOptionLabel(code: string, type: option_type): Promise<string> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema('system')
    .from('options')
    .select('label')
    .match({ type, code })
    .order('label')
    .limit(1);

  if (error || !data || data.length === 0) {
    return code; // fallback to code if not found or error occurs
  }

  return data[0].label;
}

export async function getLanguageLabels(languageArray: string[]): Promise<string> {
  const supabase = await createClient();

  const cleaned = languageArray
    .map(lang => lang.trim())
    .filter(Boolean);

  if (cleaned.length === 0) {
    return '--';
  }

  const { data, error } = await supabase
    .schema('system')
    .from('options')
    .select('code, label')
    .match({ type: 'language' })
    .in('code', cleaned);

  if (error || !data) {
    return cleaned.join(', ') || '--';
  }

  const labels = cleaned.map(code => {
    const match = data.find(l => l.code.toLowerCase() === code.toLowerCase());
    return match?.label || code;
  });

  return labels.join(', ') || '--';
}