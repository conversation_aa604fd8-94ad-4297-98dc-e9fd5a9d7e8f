import { createClient } from "@/utils/supabase/Client"
import { DocumentDisplay } from "@/types/members/member-details"
import JSZip from 'jszip'
import { saveAs } from 'file-saver'

export class DocumentService {
    
  private static async getSignedUrl(path: string): Promise<string | null> {
    const supabase = await createClient()
    
    try {
      const { data, error } = await supabase
        .storage
        .from('application_documents')
        .createSignedUrl(path, 60) // URL valid for 60 seconds

      if (error) {
        console.error("Error getting signed URL:", error)
        return null
      }

      return data.signedUrl
    } catch (error) {
      console.error("Error in getSignedUrl:", error)
      return null
    }
  }

  static async downloadDocument(documentId: string, path: string): Promise<void> {
    try {
      console.log("Downloading document:", documentId, path)
      const signedUrl = await this.getSignedUrl(path)
      
      if (!signedUrl) {
        throw new Error("Could not generate download URL")
      }

      const response = await fetch(signedUrl)
      const blob = await response.blob()
      const filename = path.split('/').pop() || 'document'
      
      saveAs(blob, filename)

    } catch (error) {
      console.error("Error downloading document:", error)
      throw error
    }
  }

  static async downloadAllDocuments(documents: DocumentDisplay[]): Promise<void> {
    try {
      const zip = new JSZip()
      
      // Fetch all documents and add them to the zip
      const downloadPromises = documents.map(async (doc) => {
        const signedUrl = await this.getSignedUrl(doc.path)
        if (!signedUrl) {
          throw new Error(`Could not generate download URL for ${doc.name}`)
        }
        
        const response = await fetch(signedUrl)
        const blob = await response.blob()
        const filename = doc.path.split('/').pop() || doc.name
        zip.file(filename, blob)
      })

      // Wait for all downloads to complete
      await Promise.all(downloadPromises)

      // Generate and download zip file
      const zipBlob = await zip.generateAsync({ type: 'blob' })
      saveAs(zipBlob, 'documents.zip')

    } catch (error) {
      console.error("Error downloading all documents:", error)
      throw error
    }
  }
} 