import { createClient } from "@/utils/supabase/Client"
import type { User } from "@supabase/supabase-js"

interface UserProfile {
  name: string
  email: string
  avatar: string
}

interface UserDisplayInfo {
  id: string
  name: string
  email: string
}

export class UserService {
  static formatUserProfile(user: User | null): UserProfile | null {
    if (!user) return null

    return {
      name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
      email: user.email || '',
      avatar: user.user_metadata?.avatar_url || '/images/placeholder-avatar.png',
    }
  }

  static async getUserProfile(userId: string) {
    const supabase = await createClient()
    // Get user profile data from your database
  }

  static async updateUserProfile(userId: string, data: any) {
    const supabase = await createClient()
    // Update user profile in your database
  }

  /**
   * Get user display information by user IDs
   * Attempts to fetch from profiles table first, falls back to auth.users, then memberprofiles
   */
  static async getUsersDisplayInfo(userIds: string[]): Promise<UserDisplayInfo[]> {
    if (!userIds || userIds.length === 0) return []

    const supabase = await createClient()
    const uniqueUserIds = Array.from(new Set(userIds.filter(Boolean)))
    const userDisplayInfo: UserDisplayInfo[] = []

    try {
      // Strategy 1: Try to get user info from profiles table (common pattern in Supabase apps)
      try {
        const { data: profilesData, error: profilesError } = await supabase
          .from("profiles")
          .select("id, full_name, email")
          .in("id", uniqueUserIds)

        if (!profilesError && profilesData) {
          profilesData.forEach(profile => {
            userDisplayInfo.push({
              id: profile.id,
              name: profile.full_name || profile.email?.split('@')[0] || 'User',
              email: profile.email || ''
            })
          })
        }
      } catch (profilesErr) {
        console.log("Profiles table not available, trying next approach")
      }

      // Get remaining user IDs that weren't found in profiles
      let foundUserIds = userDisplayInfo.map(u => u.id)
      let remainingUserIds = uniqueUserIds.filter(id => !foundUserIds.includes(id))

      // Strategy 2: Try user_meta table (primary user data table in this system)
      if (remainingUserIds.length > 0) {
        try {
          const { data: userMetaData, error: userMetaError } = await supabase
            .from("user_meta")
            .select("id, full_name, email")
            .in("id", remainingUserIds)

          if (!userMetaError && userMetaData) {
            userMetaData.forEach(userMeta => {
              userDisplayInfo.push({
                id: userMeta.id,
                name: userMeta.full_name || userMeta.email?.split('@')[0] || 'User',
                email: userMeta.email || ''
              })
            })
          }
        } catch (userMetaErr) {
          console.log("User meta table not available, trying member profiles")
        }
      }

      // Update remaining user IDs after user_meta lookup
      foundUserIds = userDisplayInfo.map(u => u.id)
      remainingUserIds = uniqueUserIds.filter(id => !foundUserIds.includes(id))

      // Strategy 3: Try member profiles (based on the existing member service structure)
      if (remainingUserIds.length > 0) {
        try {
          const { data: memberProfilesData, error: memberError } = await supabase
            .from("membership_profiles")
            .select("user_id, full_name, email")
            .in("user_id", remainingUserIds)

          if (!memberError && memberProfilesData) {
            memberProfilesData.forEach(member => {
              if (member.user_id) {
                userDisplayInfo.push({
                  id: member.user_id,
                  name: member.full_name || member.email?.split('@')[0] || 'Member',
                  email: member.email || ''
                })
              }
            })
          }
        } catch (memberErr) {
          console.log("Member profiles not available, trying auth")
        }
      }

      // Update remaining user IDs
      foundUserIds = userDisplayInfo.map(u => u.id)
      remainingUserIds = uniqueUserIds.filter(id => !foundUserIds.includes(id))

      // Strategy 4: Try auth.users (requires service role key)
      if (remainingUserIds.length > 0) {
        try {
          // Note: This requires service role access to auth.users
          const { data: authUsersData, error: authError } = await supabase.auth.admin.listUsers()

          if (!authError && authUsersData?.users) {
            authUsersData.users
              .filter(user => remainingUserIds.includes(user.id))
              .forEach(user => {
                userDisplayInfo.push({
                  id: user.id,
                  name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
                  email: user.email || ''
                })
              })
          }
        } catch (authErr) {
          console.log("Auth admin access not available")
        }
      }

      // Final fallback: Create placeholder entries for any remaining user IDs
      foundUserIds = userDisplayInfo.map(u => u.id)
      remainingUserIds = uniqueUserIds.filter(id => !foundUserIds.includes(id))

      if (remainingUserIds.length > 0) {
        remainingUserIds.forEach(userId => {
          userDisplayInfo.push({
            id: userId,
            name: 'User',
            email: ''
          })
        })
      }

      return userDisplayInfo
    } catch (error) {
      console.error("Error fetching user display info:", error)
      // Return placeholder data for all requested users
      return uniqueUserIds.map(userId => ({
        id: userId,
        name: 'User',
        email: ''
      }))
    }
  }

  /**
   * Get single user display information
   */
  static async getUserDisplayInfo(userId: string): Promise<UserDisplayInfo | null> {
    if (!userId) return null

    const users = await this.getUsersDisplayInfo([userId])
    return users[0] || null
  }
} 