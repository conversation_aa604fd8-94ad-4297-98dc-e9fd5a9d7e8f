import { createClient } from "@/utils/supabase/Client"

export class AuthService {
  static async signOut() {
    const supabase = await createClient()
    return await supabase.auth.signOut()
  }

  static async signIn(email: string, password: string) {
    const supabase = await createClient();

    const { data, error } = await supabase.auth.signInWithPassword({ email, password });

    if (error || !data.user) {
      const errorMessage = error?.message.toLowerCase().includes("invalid login")
        ? "Invalid login credentials. Please check your email and password."
        : "No account found with the provided email and password. Please check your details or create a new account.";
      return { error: errorMessage };
    }

    const userId = data.user.id;

    // Fetch user profile
    const { data: userMeta, error: userError } = await supabase
      .from("user_meta")
      .select("first_login, status, user_type")
      .eq("id", userId)
      .single();

    if (userError) {
      await supabase.auth.signOut();
      return { error: "Error retrieving profile information. Please try again." };
    }

    // Check status and user type
    if (userMeta.status !== true || userMeta.user_type !== "ADMIN") {
      await supabase.auth.signOut();
      return { error: "You do not have the necessary permissions to access this application." };
    }

    const firstLogin = userMeta.first_login;

    return { success: true, firstLogin };
  }

  static async getCurrentUser() {
    const supabase = await createClient()
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      throw error
    }

    return user
  }

  static async onAuthStateChange(callback: (user: any) => void) {
    const supabase = await createClient()
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      callback(session?.user ?? null)
    })

    return subscription
  }

  // Add other auth-related methods here
} 