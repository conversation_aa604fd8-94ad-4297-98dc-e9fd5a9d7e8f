"use client";

import { createClient } from "@/utils/supabase/Client";
import type {
  PurchaseOrder,
  PurchaseOrdersTableFilters,
  PurchaseOrdersSummary
} from "@/types/purchase-orders/purchase-orders.types";
import { CurrencyOption, DropdownOption, FileWithPreview, GlAccountOption, TaxCodeOption } from "@/types/general.types";
import { boolean } from "zod";
import { UserService } from "@/services/user.service";

// Purchase order attachments upload/delete/get
export async function submitPoDocuments(files: FileWithPreview[], poId: string) {
  const supabase = await createClient();
  const failedUploads: { fileName: string; message: string }[] = [];

  for (const file of files) {
    try {
      const currentDateTime = new Date().toISOString().replace(/[:.]/g, "-");
      const fileExtension = file.name.split('.').pop();
      const fileName = `${poId}/${file.name}-${currentDateTime}.${fileExtension}`;

      const { error: uploadError } = await supabase
        .storage
        .from('purchase-orders')
        .upload(fileName, file);

      if (uploadError) {
        console.error(`Failed to upload ${file.name}:`, uploadError.message);
        failedUploads.push({
          fileName: file.name,
          message: uploadError.message || 'Unknown error',
        });
      }
    } catch (err: any) {
      console.error(`Unexpected error while uploading ${file.name}:`, err);
      failedUploads.push({
        fileName: file.name,
        message: err?.message || 'Unexpected error',
      });
    }
  }

  return {
    success: failedUploads.length === 0,
    errors: failedUploads,
  };
}

export async function deletePODocument(path: string) {
  const supabase = await createClient();

  try {
    // Delete the document from storage
    const { error: storageError } = await supabase.storage
      .from('purchase-orders')
      .remove([path]);

    if (storageError) {
      return { success: false, error: `Failed deleting document from storage}` };
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: "An unexpected error occurred, please try again later." };
  }
}

export async function getPurchaseOrderAttachment(id: string): Promise<FileWithPreview[]> {
  const supabase = await createClient();

  const { data: files, error } = await supabase.storage
    .from("purchase-orders")
    .list(`${id}`);

  if (error || !files) {
    console.error("Failed to list attachments:", error);
    return [];
  }

  const results: FileWithPreview[] = files.map((file) => {
    const fullPath = `${id}/${file.name}`;
    return {
      name: file.name,
      size: file.metadata?.size ?? 0,
      type: file.metadata?.mimetype ?? '',
      isExisting: true,
      path: fullPath,
    } as FileWithPreview;
  });

  return results;
}

// Purchase Orders CRUD operations
export async function getAllPurchaseOrders(
  filters: { subFilters: { [key: string]: string[] } },
  page: number,
  limit: number,
  searchTerm?: string
): Promise<{
  purchaseOrders: PurchaseOrder[] | null;
  success: boolean;
  total: number;
  currentPage: number;
  allPurchaseOrderIds: string[];
}> {
  const supabase = await createClient();

  try {
    // Base query with filters and search term (no cross-schema joins)
    let mainQuery = supabase
      .schema("procurement")
      .from("purchase_orders")
      .select(`
        id,
        po_number,
        title,
        supplier_id,
        entity_id,
        po_date,
        expected_delivery_date,
        total_amount,
        currency_code,
        status,
        department_code,
        created_by,
        created_at
      `, { count: "exact" });

    // Apply filters
    Object.keys(filters.subFilters).forEach((filterKey) => {
      const filterValues = filters.subFilters[filterKey];
      if (Array.isArray(filterValues) && filterValues.length > 0) {
        mainQuery = mainQuery.in(filterKey, filterValues);
      }
    });

    // Apply search term
    if (searchTerm) {
      const normalizedSearchTerm = searchTerm.trim();
      mainQuery = mainQuery.or(
        `po_number.ilike.%${normalizedSearchTerm}%,` +
        `title.ilike.%${normalizedSearchTerm}%,` +
        `department_code.ilike.%${normalizedSearchTerm}%`
      );
    }

    // Execute query to get total count and paginated data
    const { data: purchaseOrderData, count: total, error } = await mainQuery
      .order("created_at", { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw new Error();

    if (!purchaseOrderData || purchaseOrderData.length === 0) {
      return {
        purchaseOrders: [],
        success: true,
        total: total ?? 0,
        currentPage: page,
        allPurchaseOrderIds: []
      };
    }

    // Get unique supplier and entity IDs
    const supplierIds = Array.from(new Set(purchaseOrderData.map(po => po.supplier_id).filter(Boolean)));
    const entityIds = Array.from(new Set(purchaseOrderData.map(po => po.entity_id).filter(Boolean)));
    const createByIds = Array.from(new Set(purchaseOrderData.map(po => po.created_by).filter(boolean)));

    // Fetch suppliers separately
    let suppliersData: any[] = [];
    if (supplierIds.length > 0) {
      const { data: suppliers } = await supabase
        .schema("procurement")
        .from("suppliers")
        .select("id, name, display_name")
        .in("id", supplierIds);
      suppliersData = suppliers || [];
    }

    // Fetch entities separately
    let entitiesData: any[] = [];
    if (entityIds.length > 0) {
      const { data: entities } = await supabase
        .schema("finance")
        .from("entities")
        .select("id, name")
        .in("id", entityIds);
      entitiesData = entities || [];
    }

    let createdByData: any[] = [];
    if (createByIds.length > 0) {
      const { data: createdBy } = await supabase
        .from("user_meta")
        .select("id, full_name, email")
        .in("id", createByIds);

      createdByData = createdBy || [];
    }

    // Create lookup maps
    const suppliersMap = new Map(suppliersData.map(s => [s.id, s]));
    const entitiesMap = new Map(entitiesData.map(e => [e.id, e]));
    const createdByMap = new Map(createdByData.map(c => [c.id, c]));

    // Join the data
    const enrichedPurchaseOrders = purchaseOrderData.map(po => ({
      ...po,
      suppliers: po.supplier_id ? suppliersMap.get(po.supplier_id) : null,
      entity: po.entity_id ? entitiesMap.get(po.entity_id) : null,
      created_by_name: po.created_by ? createdByMap.get(po.created_by) : null
    }));

    // Get all IDs for bulk operations
    const allIdsQuery = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("id");

    const allIds = allIdsQuery.data?.map(po => po.id) || [];

    return {
      purchaseOrders: enrichedPurchaseOrders as unknown as PurchaseOrder[],
      success: true,
      total: total ?? 0,
      currentPage: page,
      allPurchaseOrderIds: allIds
    };
  } catch {
    return {
      purchaseOrders: null,
      success: false,
      total: 0,
      currentPage: page,
      allPurchaseOrderIds: []
    };
  }
}

export async function getPurchaseOrderById(id: string): Promise<PurchaseOrder | null> {
  const supabase = await createClient();

  try {
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      console.error("Invalid UUID format:", id);
      return null;
    }

    // First, get the main purchase order data
    const { data: poData, error: poError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("*")
      .eq("id", id)
      .single();

    if (poError || !poData) {
      console.error("Purchase order not found:", { id, error: poError });
      return null;
    }

    // Get supplier data separately
    let supplierData = null;
    if (poData.supplier_id) {
      const { data: supplier, error: supplierError } = await supabase
        .schema("procurement")
        .from("suppliers")
        .select('*')
        .eq("id", poData.supplier_id)
        .single();

      if (supplierError) {
        console.error("Error fetching supplier:", supplierError);
      } else {
        supplierData = supplier;
      }
    }

    // Get entity data separately
    let entityData = null;
    if (poData.entity_id) {
      try {
        const { data: entity, error: entityError } = await supabase
          .schema("finance")
          .from("entities")
          .select('*')
          .eq("id", poData.entity_id)
          .single();

        if (entityError) {
          console.error("Error fetching entity:", entityError);
        } else {
          entityData = entity;
        }
      } catch (err) {
        console.error("Failed to fetch entity from finance schema:", err);
      }
    }

    // Get department data separately
    let departmentData = null;
    if (poData.department_code) {
      try {
        const { data: department, error: departmentError } = await supabase
          .schema("organisation")
          .from("departments")
          .select('id, code, name')
          .eq("id", poData.department_code)
          .single();

        if (departmentError) {
          console.error("Error fetching department:", departmentError);
        } else {
          departmentData = department;
        }
      } catch (err) {
        console.error("Failed to fetch department from organisation schema:", err);
      }
    }

    // Get purchase order items
    const { data: rawItemsData, error: itemsError } = await supabase
      .schema("procurement")
      .from("purchase_order_items")
      .select(`
        id,
        line_number,
        item_code,
        description,
        quantity,
        unit_of_measure,
        unit_price,
        discount_percentage,
        tax_code,
        tax_rate,
        line_total,
        gl_account_code,
        budget_item_id
      `)
      .eq("purchase_order_id", id)
      .order("line_number");

    if (itemsError) {
      console.error("Error fetching purchase order items:", itemsError);
    }

    // Map unit_of_measure to uom to match the frontend type
    const itemsData = rawItemsData?.map(item => ({
      ...item,
      uom: item.unit_of_measure
    })) || [];

    // Get goods receipts
    const { data: goodsReceiptsData, error: grError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select(`
        id,
        gr_number,
        receipt_date,
        status,
        received_by
      `)
      .eq("purchase_order_id", id)
      .order("receipt_date", { ascending: false });

    if (grError) {
      console.error("Error fetching goods receipts:", grError);
    }

    // Get user data for created_by field
    let createdByUser = null;
    if (poData.created_by) {
      try {
        const userData = await UserService.getUserDisplayInfo(poData.created_by);
        createdByUser = userData;
      } catch (err) {
        console.error("Failed to fetch created_by user data:", err);
      }
    }

    // Get user data for goods receipts received_by fields
    let enrichedGoodsReceipts = goodsReceiptsData || [];
    if (goodsReceiptsData && goodsReceiptsData.length > 0) {
      try {
        const receivedByIds = Array.from(new Set(
          goodsReceiptsData.map(gr => gr.received_by).filter(Boolean)
        ));
        
        if (receivedByIds.length > 0) {
          const usersData = await UserService.getUsersDisplayInfo(receivedByIds);
          const usersMap = new Map(usersData.map(u => [u.id, u]));
          
          enrichedGoodsReceipts = goodsReceiptsData.map(gr => ({
            ...gr,
            received_by_user: gr.received_by ? usersMap.get(gr.received_by) : null
          }));
        }
      } catch (err) {
        console.error("Failed to fetch goods receipt user data:", err);
      }
    }

    // Combine all data into the final purchase order object
    const purchaseOrder = {
      ...poData,
      supplier: supplierData, // Use singular form to match type
      entity: entityData, // Use singular form to match type  
      department: departmentData, // Add department information
      total_tax: poData.tax_amount, // Map tax_amount to total_tax for compatibility
      items: itemsData,
      goods_receipts: enrichedGoodsReceipts, // Use enriched goods receipts with user data
      created_by_user: createdByUser // Add user display information
    };

    return purchaseOrder as unknown as PurchaseOrder;

  } catch (error) {
    console.error("Unexpected error in getPurchaseOrderById:", error);
    return null;
  }
}

export async function getPurchaseOrdersSummary(): Promise<PurchaseOrdersSummary> {
  const supabase = await createClient();

  try {
    // Get total count and amount
    const { data: totalData, error: totalError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("total_amount");

    if (totalError) throw totalError;

    // Get pending approval count
    const { data: pendingData, error: pendingError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("id", { count: "exact" })
      .eq("status", "SUBMITTED");

    if (pendingError) throw pendingError;

    // Get overdue deliveries count
    const today = new Date().toISOString().split('T')[0];
    const { data: overdueData, error: overdueError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("id", { count: "exact" })
      .lt("expected_delivery_date", today)
      .in("status", ["APPROVED", "SENT", "PARTIALLY_RECEIVED"]);

    if (overdueError) throw overdueError;

    const totalCount = totalData?.length || 0;
    const totalAmount = totalData?.reduce((sum, po) => sum + (po.total_amount || 0), 0) || 0;
    const pendingApprovalCount = pendingData?.length || 0;
    const overdueDeliveriesCount = overdueData?.length || 0;

    return {
      total_count: totalCount,
      total_amount: totalAmount,
      pending_approval_count: pendingApprovalCount,
      overdue_deliveries_count: overdueDeliveriesCount
    };
  } catch {
    return {
      total_count: 0,
      total_amount: 0,
      pending_approval_count: 0,
      overdue_deliveries_count: 0
    };
  }
}

// validate access to edit purchase order
export async function checkPOEditPermissions(id: string): Promise<boolean> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("procurement")
    .from("purchase_orders")
    .select("status")
    .eq('id', id)
    .single();

  if (error || !data || !['DRAFT'].includes(data.status)) {
    return false
  }

  return true
}

// Dropdown options
export async function getEntities(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("finance")
    .from("entities")
    .select("id, name, merchant_abbreviation")
    .eq("is_active", true)
    .order("name");

  if (error || !data) {
    return [];
  }

  return data.map(entity => ({
    value: entity.id,
    label: `${entity.name}${entity.merchant_abbreviation ? ` (${entity.merchant_abbreviation})` : ''}`
  }));
}

export async function getCurrencies(): Promise<CurrencyOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("finance")
    .from("currencies")
    .select("value:code, label:name, symbol")
    .is("is_active", true)
    .order("name");

  if (error || !data) {
    return [];
  }

  return data;
}

export async function getUnitsOfMeasure(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("system")
    .from("options")
    .select("value:code, label")
    .eq("type", "uom")
    .is("is_active", true)
    .order("label");

  if (error || !data) {
    return [];
  }

  return data;
}

export async function getTaxCodes(): Promise<TaxCodeOption[]> {
  return [
    { value: "GST_9", label: "GST 9%", rate: 9 },
    { value: "GST_0", label: "GST 0%", rate: 0 },
    { value: "EXEMPT", label: "Tax Exempt", rate: 0 }
  ];
}

export async function getGLAccounts(): Promise<GlAccountOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("finance")
    .from("gl_account_codes")
    .select("value:code, label:account_name, group:account_type")
    .is("is_active", true)
    .order("account_name");

  if (error || !data) {
    return [];
  }

  const grouped: Record<string, DropdownOption[]> = {};

  for (const item of data) {
    if (!grouped[item.group]) {
      grouped[item.group] = [];
    }

    grouped[item.group].push({
      value: item.value,
      label: item.label,
    });
  }

  return Object.entries(grouped).map(([group, list]) => ({
    group,
    list,
  }));
}

export async function getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
  return 1;
  // if (fromCurrency === toCurrency) return 1;

  // const supabase = await createClient();

  // const { data, error } = await supabase
  //   .schema("finance")
  //   .from("exchange_rates")
  //   .select("rate")
  //   .eq("from_currency", fromCurrency)
  //   .eq("to_currency", toCurrency)
  //   .order("effective_date", { ascending: false })
  //   .limit(1)
  //   .single();

  // if (error || !data) {
  //   return 1; // Default exchange rate
  // }

  // return data.rate;
}

export async function getDepartments(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("organisation")
      .from("departments")
      .select("id, code, name, is_active")
      .eq("is_active", true)
      .order("name");

    if (error) {
      console.error("Error fetching departments:", error);
      // Return hardcoded departments as fallback
      const departments = [
        { value: "FIN", label: "Finance" },
        { value: "HR", label: "Human Resources" },
        { value: "IT", label: "Information Technology" },
        { value: "SALES", label: "Sales & Marketing" },
        { value: "OPS", label: "Operations" },
        { value: "ADMIN", label: "Administration" },
        { value: "PROC", label: "Procurement" },
        { value: "R&D", label: "Research & Development" },
        { value: "QA", label: "Quality Assurance" },
        { value: "CS", label: "Customer Service" },
        { value: "LEGAL", label: "Legal & Compliance" },
        { value: "MAINT", label: "Maintenance" },
        { value: "SEC", label: "Security" },
        { value: "TRAIN", label: "Training & Development" },
        { value: "PROJ", label: "Project Management" }
      ];
      return departments.sort((a, b) => a.label.localeCompare(b.label));
    }

    if (!data || data.length === 0) {
      // Return hardcoded departments as fallback when no data
      const departments = [
        { value: "FIN", label: "Finance" },
        { value: "HR", label: "Human Resources" },
        { value: "IT", label: "Information Technology" },
        { value: "SALES", label: "Sales & Marketing" },
        { value: "OPS", label: "Operations" },
        { value: "ADMIN", label: "Administration" },
        { value: "PROC", label: "Procurement" },
        { value: "R&D", label: "Research & Development" },
        { value: "QA", label: "Quality Assurance" },
        { value: "CS", label: "Customer Service" },
        { value: "LEGAL", label: "Legal & Compliance" },
        { value: "MAINT", label: "Maintenance" },
        { value: "SEC", label: "Security" },
        { value: "TRAIN", label: "Training & Development" },
        { value: "PROJ", label: "Project Management" }
      ];
      return departments.sort((a, b) => a.label.localeCompare(b.label));
    }

    // Map database results to DropdownOption format
    return data.map(dept => ({
      value: dept.id,
      label: dept.name
    }));
  } catch (err) {
    console.error("Failed to fetch departments from organisation schema:", err);
    // Return hardcoded departments as fallback
    const departments = [
      { value: "FIN", label: "FIN - Finance" },
      { value: "HR", label: "HR - Human Resources" },
      { value: "IT", label: "IT - Information Technology" },
      { value: "SALES", label: "SALES - Sales & Marketing" },
      { value: "OPS", label: "OPS - Operations" },
      { value: "ADMIN", label: "ADMIN - Administration" },
      { value: "PROC", label: "PROC - Procurement" },
      { value: "R&D", label: "R&D - Research & Development" },
      { value: "QA", label: "QA - Quality Assurance" },
      { value: "CS", label: "CS - Customer Service" },
      { value: "LEGAL", label: "LEGAL - Legal & Compliance" },
      { value: "MAINT", label: "MAINT - Maintenance" },
      { value: "SEC", label: "SEC - Security" },
      { value: "TRAIN", label: "TRAIN - Training & Development" },
      { value: "PROJ", label: "PROJ - Project Management" }
    ];
    return departments.sort((a, b) => a.label.localeCompare(b.label));
  }
}
