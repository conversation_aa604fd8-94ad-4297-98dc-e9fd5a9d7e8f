"use server";

import { createClient } from "@/utils/supabase/server";
import type { PurchaseOrderFormData, PurchaseOrderItemFormData } from "@/schemas/purchase-orders";

// Helper function to convert "none" values to null
function transformNoneValues<T extends Record<string, any>>(data: T): T {
  const transformed = { ...data };
  Object.keys(transformed).forEach((key) => {
    const value = transformed[key];
    if (value === "none" || value === "" || value === undefined) {
      (transformed as any)[key] = null;
    }
  });
  return transformed;
}

export async function createPurchaseOrder(
  formData: PurchaseOrderFormData,
  userId: string
): Promise<{ success: boolean; newId?: string; error?: string }> {
  const supabase = await createClient();

  try {
    // Generate PO number
    const poNumber = await generatePONumber();

    // Calculate totals from items
    const { subtotal, totalTax, totalAmount } = calculateOrderTotals(formData.items);

    // Prepare purchase order data with "none" value transformation
    const purchaseOrderData = transformNoneValues({
      po_number: poNumber,
      supplier_id: formData.supplier_id,
      title: formData.title,
      po_date: formData.po_date.toISOString(),
      entity_id: formData.entity_id,
      subtotal,
      tax_amount: totalTax,
      total_amount: totalAmount,
      created_by: userId,
      description: formData.description,
      expected_delivery_date: formData.expected_delivery_date?.toISOString(),
      currency_code: formData.currency_code,
      exchange_rate: formData.exchange_rate,
      delivery_address: formData.delivery_address,
      delivery_instructions: formData.delivery_instructions,
      budget_revision_id: formData.budget_revision_id,
      project_reference: formData.project_reference,
      department_code: formData.department_code,
    });

    // Insert purchase order
    const { data: poData, error: poError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .insert(purchaseOrderData)
      .select("id")
      .single();

    if (poError || !poData) {
      console.log(poError)
      throw new Error('Error while inserting purchase order');
    }

    // Insert line items
    if (formData.items.length > 0) {
      const lineItems = formData.items.map((item, index) => {
        const { lineTotal, discountAmount } = calculateLineTotal(item);

        return transformNoneValues({
          purchase_order_id: poData.id,
          line_number: index + 1,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          line_total: lineTotal,
          item_code: item.item_code,
          unit_of_measure: item.uom,
          discount_percentage: item.discount_percentage,
          discount_amount: discountAmount,
          tax_code: item.tax_code,
          tax_rate: item.tax_rate,
          gl_account_code: item.gl_account_code,
          budget_item_id: item.budget_item_id
        });
      });

      const { error: itemsError } = await supabase
        .schema("procurement")
        .from("purchase_order_items")
        .insert(lineItems);

      if (itemsError) {
        // Rollback: delete the created purchase order
        await supabase
          .schema("procurement")
          .from("purchase_orders")
          .delete()
          .eq("id", poData.id);

        throw new Error('failed to insert purchase order items');
      }
    }

    return { success: true, newId: poData.id };
  } catch (error) {
    console.error('createPurchaseOrder: ', error)
    return {
      success: false,
      error: "Failed to create a purchase order, please try again"
    };
  }
}

export async function updatePurchaseOrder(
  id: string,
  formData: PurchaseOrderFormData
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if PO can be edited (must be DRAFT or REJECTED)
    const { data: currentPO, error: checkError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("status")
      .eq("id", id)
      .single();

    if (checkError || !currentPO) {
      return { success: false, error: "Purchase order not found" };
    }

    if (!["DRAFT", "REJECTED"].includes(currentPO.status)) {
      return { success: false, error: "Purchase order cannot be edited in current status" };
    }

    // Calculate totals from items
    const { subtotal, totalTax, totalAmount } = calculateOrderTotals(formData.items);

    // Prepare update data with "none" value transformation
    const updateData = transformNoneValues({
      entity_id: formData.entity_id,
      title: formData.title,
      description: formData.description,
      supplier_id: formData.supplier_id,
      po_date: formData.po_date.toISOString(),
      expected_delivery_date: formData.expected_delivery_date?.toISOString(),
      currency_code: formData.currency_code,
      exchange_rate: formData.exchange_rate,
      budget_revision_id: formData.budget_revision_id,
      department_code: formData.department_code,
      project_reference: formData.project_reference,
      delivery_address: formData.delivery_address,
      delivery_instructions: formData.delivery_instructions,
      subtotal,
      tax_amount: totalTax,
      total_amount: totalAmount,
      updated_at: new Date().toISOString()
    });

    // Update purchase order
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .update(updateData)
      .eq("id", id);

    if (updateError) {
      throw new Error('Error while updating purchase order');
    }

    // Delete existing line items
    const { error: deleteError } = await supabase
      .schema("procurement")
      .from("purchase_order_items")
      .delete()
      .eq("purchase_order_id", id);

    if (deleteError) {
      throw new Error('failed to delete purchase order items');
    }

    // Insert new line items
    if (formData.items.length > 0) {
      const lineItems = formData.items.map((item, index) => {
        const { lineTotal, discountAmount } = calculateLineTotal(item);

        return transformNoneValues({
          purchase_order_id: id,
          line_number: index + 1,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          line_total: lineTotal,
          item_code: item.item_code,
          unit_of_measure: item.uom,
          discount_percentage: item.discount_percentage,
          discount_amount: discountAmount,
          tax_code: item.tax_code,
          tax_rate: item.tax_rate,
          gl_account_code: item.gl_account_code,
          budget_item_id: item.budget_item_id
        });
      });

      const { error: itemsError } = await supabase
        .schema("procurement")
        .from("purchase_order_items")
        .insert(lineItems);

      if (itemsError) {
        throw new Error('failed to update purchase order items');
      }
    }

    return { success: true };
  } catch (error) {
    console.error('updatePurchaseOrder: ', error)
    return {
      success: false,
      error: "Failed to update a purchase order, please try again"
    };
  }
}

export async function deletePurchaseOrder(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if PO can be deleted (must be DRAFT)
    const { data: currentPO, error: checkError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("status")
      .eq("id", id)
      .single();

    if (checkError || !currentPO) {
      return { success: false, error: "Purchase order not found" };
    }

    if (currentPO.status !== "DRAFT") {
      return { success: false, error: "Only draft purchase orders can be deleted" };
    }

    // Delete line items first
    const { error: itemsError } = await supabase
      .schema("procurement")
      .from("purchase_order_items")
      .delete()
      .eq("purchase_order_id", id);

    if (itemsError) {
      return { success: false, error: itemsError.message };
    }

    // Delete purchase order
    const { error: deleteError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .delete()
      .eq("id", id);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

export async function submitPurchaseOrderForApproval(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Validate PO is in DRAFT status
    const { data: currentPO, error: checkError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("status, total_amount")
      .eq("id", id)
      .single();

    if (checkError || !currentPO) {
      return { success: false, error: "Purchase order not found" };
    }

    if (currentPO.status !== "DRAFT") {
      return { success: false, error: "Only draft purchase orders can be submitted for approval" };
    }

    // Update status to SUBMITTED
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .update({
        status: "SUBMITTED",
        updated_at: new Date().toISOString()
      })
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Create approval request (if approval system is integrated)
    // This would integrate with the existing approval system

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

export async function cancelPurchaseOrder(
  id: string,
  reason: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if PO can be cancelled
    const { data: currentPO, error: checkError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("status")
      .eq("id", id)
      .single();

    if (checkError || !currentPO) {
      return { success: false, error: "Purchase order not found" };
    }

    if (["CANCELLED", "CLOSED", "RECEIVED"].includes(currentPO.status)) {
      return { success: false, error: "Purchase order cannot be cancelled in current status" };
    }

    // Update status to CANCELLED
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .update({
        status: "CANCELLED",
        // cancellation_reason: reason, // column is not available
        // cancelled_at: new Date().toISOString(), // column is not available
        updated_at: new Date().toISOString(),
        updated_by: userId
      })
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

export async function clonePurchaseOrder(id: string, userId: string): Promise<{ success: boolean; newId?: string; error?: string }> {
  const supabase = await createClient();

  try {
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      console.error("Invalid UUID format for clone:", id);
      return { success: false, error: "Invalid purchase order ID format" };
    }

    // Get original purchase order with items
    const { data: originalPO, error: fetchError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select('*, items:purchase_order_items (*)')
      .eq("id", id)
      .single();

    if (fetchError || !originalPO) {
      console.error("Failed to fetch original PO for cloning:", { id, error: fetchError });
      return { success: false, error: "Purchase order not found" };
    }

    console.log("Original PO fetched for cloning:", originalPO.po_number);

    // Prepare cloned data
    const formData: PurchaseOrderFormData = {
      entity_id: originalPO.entity_id,
      title: `Copy of ${originalPO.title}`,
      description: originalPO.description,
      supplier_id: originalPO.supplier_id,
      po_date: new Date(),
      expected_delivery_date: originalPO.expected_delivery_date ? new Date(originalPO.expected_delivery_date) : undefined,
      currency_code: originalPO.currency_code,
      exchange_rate: originalPO.exchange_rate,
      budget_revision_id: originalPO.budget_revision_id,
      department_code: originalPO.department_code,
      project_reference: originalPO.project_reference,
      delivery_address: originalPO.delivery_address,
      delivery_instructions: originalPO.delivery_instructions,
      items: originalPO.items?.map((item: any) => ({
        item_code: item.item_code,
        description: item.description,
        quantity: item.quantity,
        uom: item.unit_of_measure, // Map unit_of_measure back to uom for the form
        unit_price: item.unit_price,
        discount_percentage: item.discount_percentage,
        tax_code: item.tax_code,
        tax_rate: item.tax_rate,
        gl_account_code: item.gl_account_code,
        budget_item_id: item.budget_item_id
      })) || []
    };

    // Create the cloned purchase order
    console.log("Creating cloned purchase order with formData:", {
      title: formData.title,
      itemCount: formData.items.length
    });

    const result = await createPurchaseOrder(formData, userId);

    if (result.success) {
      console.log("Clone completed successfully, new PO ID:", result.newId);
    } else {
      console.error("Clone creation failed:", result.error);
    }

    return result;
  } catch (error) {
    console.error("Unexpected error during cloning:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

// Helper functions
async function generatePONumber(): Promise<string> {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');

  // This would typically query a sequence table or use a more sophisticated numbering system
  const timestamp = Date.now().toString().slice(-6);

  return `PO${year}${month}${timestamp}`;
}

function calculateLineTotal(item: PurchaseOrderItemFormData): {
  lineTotal: number;
  discountAmount: number;
} {
  const subtotal = item.quantity * item.unit_price;
  const discountAmount = subtotal * ((item.discount_percentage ?? 0) / 100);
  const afterDiscount = subtotal - discountAmount;
  const taxAmount = afterDiscount * (item.tax_rate / 100);

  return {
    lineTotal: afterDiscount + taxAmount,
    discountAmount,
  };
}

function calculateOrderTotals(items: PurchaseOrderItemFormData[]): {
  subtotal: number;
  totalTax: number;
  totalAmount: number;
} {
  let subtotal = 0;
  let totalTax = 0;

  items.forEach(item => {
    const itemSubtotal = item.quantity * item.unit_price;
    const discountAmount = itemSubtotal * ((item.discount_percentage ?? 0) / 100);
    const afterDiscount = itemSubtotal - discountAmount;
    const taxAmount = afterDiscount * (item.tax_rate / 100);

    subtotal += afterDiscount;
    totalTax += taxAmount;
  });

  return {
    subtotal,
    totalTax,
    totalAmount: subtotal + totalTax,
  };
}