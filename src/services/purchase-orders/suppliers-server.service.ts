"use server";

import { createClient } from "@/utils/supabase/server";
import type { SupplierFormData } from "@/schemas/purchase-orders";

export async function createSupplier(
  formData: SupplierFormData
): Promise<{ success: boolean; newId?: string; error?: string; supplierCode?: string }> {
  const supabase = await createClient();

  try {
    // Prepare contact persons JSONB data
    const contactPersonsData = formData.contact_persons?.map(contact => ({
      id: crypto.randomUUID(),
      name: contact.name,
      title: contact.title,
      email: contact.email,
      phone_number: contact.phone_number,
      is_primary: contact.is_primary
    })) || [];

    // Prepare bank details JSONB data
    const bankDetailsData = formData.bank_details?.map(bank => ({
      id: crypto.randomUUID(),
      bank_name: bank.bank_name,
      account_number: bank.account_number,
      swift_code: bank.swift_code,
      account_type: bank.account_type
    })) || [];

    // Prepare supplier data - map to database field names with JSONB fields
    // Note: supplier_code is now optional and will be generated by the database
    const supplierData = {
      supplier_code: formData.basic_info.supplier_code ?? null,
      name: formData.basic_info.name,
      display_name: formData.basic_info.display_name,
      registration_number: formData.basic_info.registration_number,
      tax_registration_number: formData.basic_info.tax_registration_number,
      supplier_type: formData.basic_info.supplier_type,
      category: formData.basic_info.category,
      status: formData.basic_info.status,
      email: formData.basic_info.email,
      phone: formData.basic_info.phone_number,
      website: formData.basic_info.website,
      billing_address: formData.address.billing_address,
      shipping_address: formData.address.same_as_billing ? formData.address.billing_address : formData.address.shipping_address,
      payment_terms: formData.financial.payment_terms,
      currency_code: formData.financial.currency_code,
      credit_limit: formData.financial.credit_limit,
      is_gst_registered: formData.financial.is_gst_registered,
      contact_persons: contactPersonsData,
      bank_details: bankDetailsData
    };

    // Insert supplier with JSONB data
    const { data: supplierResult, error: supplierError } = await supabase
      .schema("procurement")
      .from("suppliers")
      .insert(supplierData)
      .select("id, supplier_code")
      .single();

    if (supplierError || !supplierResult) {
      // Handle unique constraint violations with user-friendly messages
      if (supplierError?.code === '23505') {
        if (supplierError.message.includes('supplier_code')) {
          return { success: false, error: "A supplier with this code already exists. Please try again." };
        } else if (supplierError.message.includes('email')) {
          return { success: false, error: "A supplier with this email already exists." };
        } else if (supplierError.message.includes('registration_number')) {
          return { success: false, error: "A supplier with this registration number already exists." };
        }
      }
      return { success: false, error: supplierError?.message || "Failed to create supplier" };
    }

    return {
      success: true,
      newId: supplierResult.id,
      supplierCode: supplierResult.supplier_code
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

export async function updateSupplier(
  id: string,
  formData: SupplierFormData
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Prepare contact persons JSONB data
    const contactPersonsData = formData.contact_persons?.map(contact => ({
      id: contact.id || crypto.randomUUID(), // Use existing ID or generate new one
      name: contact.name,
      title: contact.title,
      email: contact.email,
      phone_number: contact.phone_number,
      is_primary: contact.is_primary
    })) || [];

    // Prepare bank details JSONB data
    const bankDetailsData = formData.bank_details?.map(bank => ({
      id: bank.id || crypto.randomUUID(), // Use existing ID or generate new one
      bank_name: bank.bank_name,
      account_number: bank.account_number,
      swift_code: bank.swift_code,
      account_type: bank.account_type
    })) || [];

    // Prepare update data - map to database field names with JSONB fields
    const updateData = {
      name: formData.basic_info.name,
      display_name: formData.basic_info.display_name,
      registration_number: formData.basic_info.registration_number,
      tax_registration_number: formData.basic_info.tax_registration_number,
      supplier_type: formData.basic_info.supplier_type,
      category: formData.basic_info.category,
      status: formData.basic_info.status,
      email: formData.basic_info.email,
      phone: formData.basic_info.phone_number,
      website: formData.basic_info.website,
      billing_address: formData.address.billing_address,
      shipping_address: formData.address.same_as_billing ? formData.address.billing_address : formData.address.shipping_address,
      payment_terms: formData.financial.payment_terms,
      currency_code: formData.financial.currency_code,
      credit_limit: formData.financial.credit_limit,
      is_gst_registered: formData.financial.is_gst_registered,
      contact_persons: contactPersonsData,
      bank_details: bankDetailsData,
      updated_at: new Date().toISOString()
    };

    // Update supplier with JSONB data
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("suppliers")
      .update(updateData)
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

export async function deleteSupplier(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if supplier has any purchase orders
    const { data: poData, error: poCheckError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("id")
      .eq("supplier_id", id)
      .limit(1);

    if (poCheckError) {
      return { success: false, error: poCheckError.message };
    }

    if (poData && poData.length > 0) {
      return { success: false, error: "Cannot delete supplier with existing purchase orders" };
    }

    // Delete supplier (JSONB data will be deleted automatically)
    const { error: deleteError } = await supabase
      .schema("procurement")
      .from("suppliers")
      .delete()
      .eq("id", id);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

export async function updateSupplierStatus(
  id: string,
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "BLACKLISTED",
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (reason) {
      updateData.status_change_reason = reason;
    }

    const { error: updateError } = await supabase
      .schema("procurement")
      .from("suppliers")
      .update(updateData)
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

// Bulk update supplier status
export async function bulkUpdateSupplierStatus(
  supplierIds: string[],
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "BLACKLISTED",
  reason?: string
): Promise<{ success: boolean; error?: string; updatedCount?: number }> {
  const supabase = await createClient();

  try {
    if (!supplierIds || supplierIds.length === 0) {
      return { success: false, error: "No suppliers selected" };
    }

    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (reason) {
      updateData.status_change_reason = reason;
    }

    const { data, error: updateError } = await supabase
      .schema("procurement")
      .from("suppliers")
      .update(updateData)
      .in("id", supplierIds)
      .select("id");

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    return {
      success: true,
      updatedCount: data?.length || 0
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

// Get supplier status history (if tracking is implemented)
export async function getSupplierStatusHistory(
  supplierId: string
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  const supabase = await createClient();

  try {
    // For now, return the current status and last update
    // This can be expanded when status history tracking is implemented
    const { data, error } = await supabase
      .schema("procurement")
      .from("suppliers")
      .select("status, updated_at, status_change_reason")
      .eq("id", supplierId)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return {
      success: true,
      data: data ? [{
        status: data.status,
        changed_at: data.updated_at,
        reason: data.status_change_reason
      }] : []
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}