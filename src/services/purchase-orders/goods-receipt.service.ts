"use client";

import { createClient } from "@/utils/supabase/Client";
import { UserService } from "@/services/user.service";
import type {
  GoodsReceipt,
  GoodsReceiptItem,
  PurchaseOrder
} from "@/types/purchase-orders/purchase-orders.types";
import { FileWithPreview } from "@/types/general.types";

// Goods Receipt attachments upload/delete/get
export async function submitGRDocuments(files: FileWithPreview[], id: string) {
  const supabase = await createClient();
  const failedUploads: { fileName: string; message: string }[] = [];

  for (const file of files) {
    try {
      const currentDateTime = new Date().toISOString().replace(/[:.]/g, "-");
      const fileExtension = file.name.split('.').pop();
      const fileName = `${id}/${file.name}-${currentDateTime}.${fileExtension}`;

      const { error: uploadError } = await supabase
        .storage
        .from('sales-orders')
        .upload(fileName, file);

      if (uploadError) {
        console.error(`Failed to upload ${file.name}:`, uploadError.message);
        failedUploads.push({
          fileName: file.name,
          message: uploadError.message || 'Unknown error',
        });
      }
    } catch (err: any) {
      console.error(`Unexpected error while uploading ${file.name}:`, err);
      failedUploads.push({
        fileName: file.name,
        message: err?.message || 'Unexpected error',
      });
    }
  }

  return {
    success: failedUploads.length === 0,
    errors: failedUploads,
  };
}

export async function deleteGRDocument(path: string) {
  const supabase = await createClient();

  try {
    // Delete the document from storage
    const { error: storageError } = await supabase.storage
      .from('sales-orders')
      .remove([path]);

    if (storageError) {
      return { success: false, error: `Failed deleting document from storage}` };
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: "An unexpected error occurred, please try again later." };
  }
}

export async function getGRAttachment(id: string): Promise<FileWithPreview[]> {
  const supabase = await createClient();

  const { data: files, error } = await supabase.storage
    .from("purchase-orders")
    .list(`${id}`);

  if (error || !files) {
    console.error("Failed to list attachments:", error);
    return [];
  }

  const results: FileWithPreview[] = files.map((file) => {
    const fullPath = `${id}/${file.name}`;
    return {
      name: file.name,
      size: file.metadata?.size ?? 0,
      type: file.metadata?.mimetype ?? '',
      isExisting: true,
      path: fullPath,
    } as FileWithPreview;
  });

  return results;
}

// Get purchase orders available for goods receipt
export async function getPurchaseOrdersForReceipt(
  searchTerm: string | null,
  page: number
): Promise<{
  success: boolean;
  purchaseOrder: PurchaseOrder[];
  currentPage: number;
  totalPages: number;
}> {
  const normalizedSearchTerm = searchTerm?.trim();
  if (!normalizedSearchTerm) return { success: true, purchaseOrder: [], currentPage: 0, totalPages: 0 };

  const itemsPerPage = 10;
  const currentPage = page < 1 ? 1 : page;

  const supabase = await createClient();

  // Query
  const { data: poData, error: poError, count: total } = await supabase
    .schema("procurement")
    .from("purchase_orders")
    .select("*, suppliers:supplier_id(*), items:purchase_order_items(*)", { count: "exact" })
    .in("status", ["APPROVED", "SENT", "PARTIALLY_RECEIVED"])
    .or(
      `po_number.ilike.%${normalizedSearchTerm}%,` +
      `title.ilike.%${normalizedSearchTerm}%`
    )
    .order("po_date", { ascending: false })
    .range((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage - 1);

  if (poError) {
    console.error("Error fetching purchase order data:", poError.message);
    return { success: false, purchaseOrder: [], currentPage: 0, totalPages: 0 };
  }

  if (!poData || poData.length === 0) {
    return { success: false, purchaseOrder: [], currentPage: 0, totalPages: 0 };
  }

  const totalPages = Math.ceil((total ?? 0) / itemsPerPage);

  return {
    success: true,
    purchaseOrder: poData,
    currentPage,
    totalPages
  };
}

export async function searchSpecificPurchaseOrder(id: string): Promise<PurchaseOrder | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("procurement")
    .from("purchase_orders")
    .select("*, suppliers:supplier_id(*), items:purchase_order_items(*)", { count: "exact" })
    .in("status", ["APPROVED", "SENT", "PARTIALLY_RECEIVED"])
    .single();

  if (error || !data) {
    return null;
  }

  return data
}

// Get goods receipts with filtering and pagination
export async function getAllGoodsReceipts(
  filters: { subFilters: { [key: string]: string[] } },
  page: number,
  limit: number,
  searchTerm?: string
): Promise<{
  goodsReceipts: GoodsReceipt[] | null;
  success: boolean;
  total: number;
  currentPage: number;
}> {
  const supabase = await createClient();

  try {
    let mainQuery = supabase
      .schema("procurement")
      .from("goods_receipts")
      .select(`
        id,
        gr_number,
        receipt_date,
        received_by,
        notes,
        status,
        created_at,
        purchase_orders:purchase_order_id (
          id,
          po_number,
          title,
          suppliers:supplier_id (
            name,
            display_name
          )
        )
      `, { count: "exact" });

    // Apply filters
    Object.keys(filters.subFilters).forEach((filterKey) => {
      const filterValues = filters.subFilters[filterKey];
      if (Array.isArray(filterValues) && filterValues.length > 0) {
        mainQuery = mainQuery.in(filterKey, filterValues);
      }
    });

    // Apply search term
    if (searchTerm) {
      const normalizedSearchTerm = searchTerm.trim();
      mainQuery = mainQuery.or(
        `gr_number.ilike.%${normalizedSearchTerm}%,` +
        `purchase_orders.po_number.ilike.%${normalizedSearchTerm}%,` +
        `received_by.ilike.%${normalizedSearchTerm}%`
      );
    }

    const { data: goodsReceiptData, count: total, error } = await mainQuery
      .order("created_at", { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;

    if (!goodsReceiptData || goodsReceiptData.length === 0) {
      return {
        goodsReceipts: [],
        success: true,
        total: total ?? 0,
        currentPage: page
      };
    }

    // Get user data for received_by fields
    const receivedByIds = Array.from(new Set(
      goodsReceiptData.map(gr => gr.received_by).filter(Boolean)
    ));

    let usersData: any[] = [];
    if (receivedByIds.length > 0) {
      try {
        usersData = await UserService.getUsersDisplayInfo(receivedByIds);
      } catch (err) {
        console.error("Failed to fetch user data for goods receipts:", err);
      }
    }

    // Create user lookup map
    const usersMap = new Map(usersData.map(u => [u.id, u]));

    // Enrich goods receipts with user data
    const enrichedGoodsReceipts = goodsReceiptData.map(gr => ({
      ...gr,
      received_by_user: gr.received_by ? usersMap.get(gr.received_by) : null
    }));

    return {
      goodsReceipts: (enrichedGoodsReceipts as unknown as GoodsReceipt[]) || [],
      success: true,
      total: total ?? 0,
      currentPage: page
    };
  } catch {
    return {
      goodsReceipts: null,
      success: false,
      total: 0,
      currentPage: page
    };
  }
}

// Get goods receipt by ID
export async function getGoodsReceiptById(id: string): Promise<GoodsReceipt | null> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select(`
        *,
        purchase_orders:purchase_order_id (
          id,
          po_number,
          title,
          po_date,
          expected_delivery_date,
          suppliers:supplier_id (
            name,
            display_name,
            email
          )
        ),
        items:goods_receipt_items (
          id,
          quantity_received,
          quantity_accepted,
          quantity_rejected,
          rejection_reason,
          purchase_order_items:po_item_id (
            id,
            line_number,
            item_code,
            description,
            quantity,
            unit_of_measure,
            unit_price
          )
        )
      `)
      .eq("id", id)
      .single();

    if (error) throw error;

    return data as GoodsReceipt;
  } catch {
    return null;
  }
}

// Get receipt status for purchase order items
export async function getPurchaseOrderReceiptStatus(poId: string): Promise<{
  items: Array<{
    item_id: string;
    line_number: number;
    description: string;
    ordered_quantity: number;
    received_quantity: number;
    pending_quantity: number;
    uom: string;
  }>;
}> {
  const supabase = await createClient();

  try {
    // Get PO items with receipt quantities
    const { data: itemsData, error: itemsError } = await supabase
      .schema("procurement")
      .from("purchase_order_items")
      .select(`
        id,
        line_number,
        description,
        quantity,
        unit_of_measure,
        goods_receipt_items (
          quantity_received
        )
      `)
      .eq("purchase_order_id", poId)
      .order("line_number");

    if (itemsError) throw itemsError;

    const items = itemsData?.map(item => {
      const receivedQuantity = item.goods_receipt_items?.reduce(
        (total: number, receipt: any) => total + receipt.quantity_received, 0
      ) || 0;

      return {
        item_id: item.id,
        line_number: item.line_number,
        description: item.description,
        ordered_quantity: item.quantity,
        received_quantity: receivedQuantity,
        pending_quantity: item.quantity - receivedQuantity,
        uom: item.unit_of_measure
      };
    }) || [];

    return { items };
  } catch {
    return { items: [] };
  }
}

// Search goods receipts
export async function searchGoodsReceipts(searchTerm: string): Promise<GoodsReceipt[]> {
  const normalizedSearchTerm = searchTerm?.trim();

  if (!normalizedSearchTerm) return [];

  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select(`
        id,
        gr_number,
        receipt_date,
        status,
        purchase_orders:purchase_order_id (
          po_number,
          title
        )
      `)
      .or(
        `gr_number.ilike.%${normalizedSearchTerm}%,` +
        `purchase_orders.po_number.ilike.%${normalizedSearchTerm}%`
      )
      .order("receipt_date", { ascending: false })
      .limit(10);

    if (error) throw error;

    return (data as unknown as GoodsReceipt[]) || [];
  } catch {
    return [];
  }
}

// Get pending deliveries
export async function getPendingDeliveries(): Promise<Array<{
  po_id: string;
  po_number: string;
  supplier_name: string;
  expected_delivery_date: string;
  days_overdue: number;
  total_amount: number;
  currency: string;
}>> {
  const supabase = await createClient();

  try {
    const today = new Date().toISOString().split('T')[0];

    const { data, error } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select(`
        id,
        po_number,
        expected_delivery_date,
        total_amount,
        currency_code,
        supplier:suppliers!purchase_orders_supplier_id_fkey (
          name,
          display_name
        )
      `)
      .in("status", ["APPROVED", "SENT", "PARTIALLY_RECEIVED"])
      .not("expected_delivery_date", "is", null)
      .order("expected_delivery_date");

    if (error) throw error;

    const pendingDeliveries = data?.map(po => {
      const expectedDate = new Date(po.expected_delivery_date);
      const todayDate = new Date(today);
      const diffTime = todayDate.getTime() - expectedDate.getTime();
      const daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return {
        po_id: po.id,
        po_number: po.po_number,
        supplier_name: (po.supplier as any)?.display_name || (po.supplier as any)?.name || 'Unknown',
        expected_delivery_date: po.expected_delivery_date,
        days_overdue: daysOverdue,
        total_amount: po.total_amount,
        currency: po.currency_code
      };
    }) || [];

    return pendingDeliveries;
  } catch {
    return [];
  }
}