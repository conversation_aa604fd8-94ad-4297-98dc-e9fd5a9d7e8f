"use server";

import { createClient } from "@/utils/supabase/server";
import type { GoodsReceiptFormData } from "@/schemas/purchase-orders/goods-receipt.schema";

export async function createGoodsReceipt(
  formData: GoodsReceiptFormData
): Promise<{ success: boolean; newId?: string; error?: string }> {
  console.log('Creating goods receipt with data:', formData);
  const supabase = await createClient();

  try {
    // Generate GR number
    const grNumber = await generateGRNumber();
    console.log('Generated GR number:', grNumber);

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('Failed to get current user:', userError);
      return { success: false, error: "User authentication failed" };
    }

    // Prepare goods receipt data
    // Note: received_by in DB expects a UUID, so we use the current user's ID
    // The name provided in the form is added to the notes for reference
    const goodsReceiptData = {
      gr_number: grNumber,
      purchase_order_id: formData.purchase_order_id,
      receipt_date: formData.receipt_date.toISOString().split('T')[0],
      received_by: user.id, // Use the current user's ID for received_by
      notes: formData.notes ? `${formData.notes}\n\nReceived by: ${formData.received_by}` : `Received by: ${formData.received_by}`,
      status: "PENDING",
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Inserting goods receipt:', goodsReceiptData);

    // Insert goods receipt
    const { data: grData, error: grError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .insert(goodsReceiptData)
      .select("id")
      .single();

    if (grError || !grData) {
      console.error('Failed to create goods receipt:', grError);
      return { success: false, error: grError?.message || "Failed to create goods receipt" };
    }

    console.log('Created goods receipt with ID:', grData.id);

    // Insert goods receipt items
    if (formData.items.length > 0) {
      const receiptItems = formData.items.map(item => ({
        goods_receipt_id: grData.id,
        po_item_id: item.po_item_id, // Use po_item_id to match database schema
        quantity_received: parseFloat((item.quantity_received || 0).toFixed(4)),
        quantity_accepted: parseFloat((item.quantity_accepted || 0).toFixed(4)),
        quantity_rejected: parseFloat((item.quantity_rejected || 0).toFixed(4)),
        rejection_reason: item.rejection_reason || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      console.log('Inserting goods receipt items:', receiptItems);

      const { error: itemsError } = await supabase
        .schema("procurement")
        .from("goods_receipt_items")
        .insert(receiptItems);

      if (itemsError) {
        console.error('Failed to insert receipt items:', itemsError);
        // Rollback: delete the created goods receipt
        await supabase
          .schema("procurement")
          .from("goods_receipts")
          .delete()
          .eq("id", grData.id);
        
        return { success: false, error: itemsError.message };
      }
      
      console.log('Successfully inserted receipt items');
    }

    // Update purchase order status based on receipt completion
    await updatePurchaseOrderStatus(formData.purchase_order_id);

    return { success: true, newId: grData.id };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function updateGoodsReceipt(
  id: string,
  formData: GoodsReceiptFormData
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if goods receipt can be edited (must be PENDING)
    const { data: currentGR, error: checkError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select("status, purchase_order_id")
      .eq("id", id)
      .single();

    if (checkError || !currentGR) {
      return { success: false, error: "Goods receipt not found" };
    }

    if (currentGR.status !== "PENDING") {
      return { success: false, error: "Goods receipt cannot be edited in current status" };
    }

    // Prepare update data
    const updateData = {
      receipt_date: formData.receipt_date.toISOString(),
      received_by: formData.received_by,
      notes: formData.notes,
      updated_at: new Date().toISOString()
    };

    // Update goods receipt
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .update(updateData)
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Delete existing items
    const { error: deleteError } = await supabase
      .schema("procurement")
      .from("goods_receipt_items")
      .delete()
      .eq("goods_receipt_id", id);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    // Insert updated items
    if (formData.items.length > 0) {
      const receiptItems = formData.items.map(item => ({
        goods_receipt_id: id,
        po_item_id: item.po_item_id,
        quantity_received: item.quantity_received,
        quantity_accepted: item.quantity_accepted,
        quantity_rejected: item.quantity_rejected,
        rejection_reason: item.rejection_reason
      }));

      const { error: itemsError } = await supabase
        .schema("procurement")
        .from("goods_receipt_items")
        .insert(receiptItems);

      if (itemsError) {
        return { success: false, error: itemsError.message };
      }
    }

    // Update purchase order status based on receipt completion
    await updatePurchaseOrderStatus(currentGR.purchase_order_id);

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function completeGoodsReceipt(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Validate that all quantities are properly recorded
    const { data: grData, error: grError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select(`
        status,
        purchase_order_id,
        items:goods_receipt_items (
          quantity_received,
          quantity_accepted,
          quantity_rejected
        )
      `)
      .eq("id", id)
      .single();

    if (grError || !grData) {
      return { success: false, error: "Goods receipt not found" };
    }

    if (grData.status !== "PENDING") {
      return { success: false, error: "Goods receipt is not in pending status" };
    }

    // Validate that all received quantities are properly allocated
    const hasInvalidQuantities = grData.items?.some((item: any) => 
      item.quantity_received !== (item.quantity_accepted + item.quantity_rejected)
    );

    if (hasInvalidQuantities) {
      return { success: false, error: "All received quantities must be allocated between accepted and rejected" };
    }

    // Update status to COMPLETE
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .update({ 
        status: "COMPLETE",
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Update purchase order status
    await updatePurchaseOrderStatus(grData.purchase_order_id);

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function cancelGoodsReceipt(
  id: string, 
  reason: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    
    // Check if goods receipt can be cancelled
    const { data: currentGR, error: checkError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select("status, purchase_order_id")
      .eq("id", id)
      .single();

    if (checkError || !currentGR) {
      return { success: false, error: "Goods receipt not found" };
    }

    if (currentGR.status === "CANCELLED") {
      return { success: false, error: "Goods receipt is already cancelled" };
    }

    // Update status to CANCELLED
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .update({ 
        status: "CANCELLED",
        cancellation_reason: reason,
        cancelled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        updated_by: user?.id
      })
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Update purchase order status
    await updatePurchaseOrderStatus(currentGR.purchase_order_id);

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

// Save goods receipt as draft (same as create but with validation relaxed)
export async function saveGoodsReceiptAsDraft(
  formData: GoodsReceiptFormData
): Promise<{ success: boolean; newId?: string; error?: string }> {
  const supabase = await createClient();

  try {
    // Generate GR number using database function
    const grNumber = await generateGRNumber();
    console.log('Generated GR number for draft:', grNumber);

    // Get current user for created_by field
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return { success: false, error: "User authentication failed" };
    }

    // Prepare goods receipt data
    const goodsReceiptData = {
      gr_number: grNumber,
      purchase_order_id: formData.purchase_order_id,
      receipt_date: formData.receipt_date.toISOString().split('T')[0],
      received_by: formData.received_by,
      notes: formData.notes,
      status: "PENDING" as const,
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Insert goods receipt
    const { data: grData, error: grError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .insert(goodsReceiptData)
      .select("id")
      .single();

    if (grError || !grData) {
      console.error('Error creating draft goods receipt:', grError);
      return { success: false, error: grError?.message || "Failed to create goods receipt draft" };
    }

    console.log('Created draft goods receipt with ID:', grData.id);

    // Insert goods receipt items (even if quantities are 0 for draft)
    if (formData.items.length > 0) {
      const receiptItems = formData.items.map(item => ({
        goods_receipt_id: grData.id,
        po_item_id: item.po_item_id,
        quantity_received: parseFloat((item.quantity_received || 0).toFixed(4)),
        quantity_accepted: parseFloat((item.quantity_accepted || 0).toFixed(4)),
        quantity_rejected: parseFloat((item.quantity_rejected || 0).toFixed(4)),
        rejection_reason: item.rejection_reason || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { error: itemsError } = await supabase
        .schema("procurement")
        .from("goods_receipt_items")
        .insert(receiptItems);

      if (itemsError) {
        console.error('Error inserting draft receipt items:', itemsError);
        // Rollback: delete the created goods receipt
        await supabase
          .schema("procurement")
          .from("goods_receipts")
          .delete()
          .eq("id", grData.id);
        
        return { success: false, error: itemsError.message };
      }
    }

    return { success: true, newId: grData.id };
  } catch (error) {
    console.error('Unexpected error in saveGoodsReceiptAsDraft:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function deleteGoodsReceipt(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if goods receipt can be deleted (must be PENDING)
    const { data: currentGR, error: checkError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select("status, purchase_order_id")
      .eq("id", id)
      .single();

    if (checkError || !currentGR) {
      return { success: false, error: "Goods receipt not found" };
    }

    if (currentGR.status !== "PENDING") {
      return { success: false, error: "Only pending goods receipts can be deleted" };
    }

    // Delete receipt items first
    const { error: itemsError } = await supabase
      .schema("procurement")
      .from("goods_receipt_items")
      .delete()
      .eq("goods_receipt_id", id);

    if (itemsError) {
      return { success: false, error: itemsError.message };
    }

    // Delete goods receipt
    const { error: deleteError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .delete()
      .eq("id", id);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    // Update purchase order status
    await updatePurchaseOrderStatus(currentGR.purchase_order_id);

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

// Helper functions
async function generateGRNumber(): Promise<string> {
  const supabase = await createClient();
  
  const { data: grNumberData, error: grNumberError } = await supabase
    .schema('procurement')
    .rpc('generate_gr_number');
  
  if (grNumberError || !grNumberData) {
    console.error('Failed to generate GR number:', grNumberError);
    throw new Error('Failed to generate GR number: ' + (grNumberError?.message || 'Unknown error'));
  }
  
  console.log('Generated GR number:', grNumberData);
  return grNumberData;
}

async function updatePurchaseOrderStatus(purchaseOrderId: string): Promise<void> {
  const supabase = await createClient();

  try {
    // Get PO items and their receipt status
    const { data: poItems, error: poItemsError } = await supabase
      .schema("procurement")
      .from("purchase_order_items")
      .select(`
        id,
        quantity,
        goods_receipt_items (
          quantity_received
        )
      `)
      .eq("purchase_order_id", purchaseOrderId);

    if (poItemsError || !poItems) return;

    // Calculate total ordered vs received quantities
    let totalOrdered = 0;
    let totalReceived = 0;

    poItems.forEach(item => {
      totalOrdered += item.quantity;
      const itemReceived = item.goods_receipt_items?.reduce(
        (sum: number, receipt: any) => sum + receipt.quantity_received, 0
      ) || 0;
      totalReceived += itemReceived;
    });

    // Determine new status
    let newStatus: string;
    if (totalReceived === 0) {
      newStatus = "APPROVED"; // or keep current status if already SENT
    } else if (totalReceived >= totalOrdered) {
      newStatus = "RECEIVED";
    } else {
      newStatus = "PARTIALLY_RECEIVED";
    }

    // Update purchase order status
    await supabase
      .schema("procurement")
      .from("purchase_orders")
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq("id", purchaseOrderId);
  } catch (error) {
    // Log error but don't throw - this is a side effect
    console.error("Error updating purchase order status:", error);
  }
}