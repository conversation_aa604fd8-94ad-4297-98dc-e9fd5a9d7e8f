"use client";

import { createClient } from "@/utils/supabase/Client";
import type {
  Supplier,
  SuppliersResponse,
  SuppliersTableFilters,
} from "@/types/purchase-orders/purchase-orders.types";
import { DropdownOption } from "@/types/general.types";

// Suppliers CRUD operations
export async function getAllSuppliers(
  filters: { subFilters: { [key: string]: string[] } },
  page: number,
  limit: number,
  searchTerm?: string
): Promise<SuppliersResponse> {
  const supabase = await createClient();

  try {
    // Base query with filters and search term
    let mainQuery = supabase
      .schema("procurement")
      .from("suppliers")
      .select('*', { count: "exact" });

    // Apply filters
    Object.keys(filters.subFilters).forEach((filterKey) => {
      const filterValues = filters.subFilters[filterKey];
      if (Array.isArray(filterValues) && filterValues.length > 0) {
        mainQuery = mainQuery.in(filterKey, filterValues);
      }
    });

    // Apply search term
    if (searchTerm) {
      const normalizedSearchTerm = searchTerm.trim();
      mainQuery = mainQuery.or(
        `supplier_code.ilike.%${normalizedSearchTerm}%,` +
        `name.ilike.%${normalizedSearchTerm}%,` +
        `display_name.ilike.%${normalizedSearchTerm}%,` +
        `email.ilike.%${normalizedSearchTerm}%`
      );
    }

    // Execute query to get total count and paginated data
    const { data: supplierData, count: total, error } = await mainQuery
      .order("created_at", { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw new Error();

    if (!supplierData || supplierData.length === 0) {
      return {
        suppliers: [],
        success: true,
        total: total ?? 0,
        currentPage: page,
        allSupplierIds: []
      };
    }

    // Get all IDs for bulk operations
    const allIdsQuery = await supabase
      .schema("procurement")
      .from("suppliers")
      .select("id");

    const allIds = allIdsQuery.data?.map(supplier => supplier.id) || [];

    return {
      suppliers: supplierData as Supplier[],
      success: true,
      total: total ?? 0,
      currentPage: page,
      allSupplierIds: allIds
    };
  } catch {
    return {
      suppliers: null,
      success: false,
      total: 0,
      currentPage: page,
      allSupplierIds: []
    };
  }
}

export async function getSupplierById(id: string): Promise<Supplier | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("procurement")
    .from("suppliers")
    .select('*')
    .eq("id", id)
    .single();

  if (error || !data) {
    return null;
  }

  return data as Supplier;
}

export async function searchSuppliers(searchTerm: string): Promise<Supplier[]> {
  const normalizedSearchTerm = searchTerm?.trim();

  if (!normalizedSearchTerm) return [];

  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("procurement")
    .from("suppliers")
    .select(`
      id,
      supplier_code,
      name,
      display_name,
      status,
      email,
      currency_code,
      payment_terms
    `)
    .eq("is_active", true)
    .or(
      `supplier_code.ilike.%${normalizedSearchTerm}%,` +
      `name.ilike.%${normalizedSearchTerm}%,` +
      `display_name.ilike.%${normalizedSearchTerm}%`
    )
    .order("name")
    .limit(20);

  if (error || !data || data.length === 0) {
    return [];
  }

  return data as Supplier[];
}

// Search suppliers for dropdown with direct database query
export async function searchSuppliersForDropdown(searchTerm: string): Promise<DropdownOption[]> {
  const normalizedSearchTerm = searchTerm?.trim();

  // Return empty array for empty search
  if (!normalizedSearchTerm || normalizedSearchTerm.length < 2) return [];

  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("procurement")
      .from("suppliers")
      .select("id, supplier_code, name, display_name, status")
      .in("status", ["ACTIVE", "INACTIVE"])
      .or(
        `supplier_code.ilike.%${normalizedSearchTerm}%,` +
        `name.ilike.%${normalizedSearchTerm}%,` +
        `display_name.ilike.%${normalizedSearchTerm}%`
      )
      .order("name")
      .limit(50); // Increased limit for better search results

    if (error) {
      console.error("Error searching suppliers:", error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    return data.map(supplier => ({
      value: supplier.id,
      label: `${supplier.supplier_code ? supplier.supplier_code + ' - ' : ''}${supplier.display_name || supplier.name}${supplier.status === 'INACTIVE' ? ' (Inactive)' : ''}`
    }));
  } catch (error) {
    console.error("Error in searchSuppliersForDropdown:", error);
    return [];
  }
}

// Get active suppliers for dropdown
export async function getActiveSuppliers(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("procurement")
      .from("suppliers")
      .select("id, supplier_code, name, display_name, status")
      .in("status", ["ACTIVE", "INACTIVE"])  // Include both ACTIVE and INACTIVE suppliers
      .order("name");

    if (error) {
      console.error("Error fetching active suppliers:", error);
      
      if (error.message?.includes("schema") || error.message?.includes("table")) {
        console.warn("Suppliers table not available, returning empty list");
        return [];
      }
      
      throw new Error(`Failed to load suppliers: ${error.message}`);
    }

    if (!data || data.length === 0) {
      console.warn("No suppliers found");
      return [];
    }

    return data.map(supplier => ({
      value: supplier.id,
      label: `${supplier.supplier_code ? supplier.supplier_code + ' - ' : ''}${supplier.display_name || supplier.name}${supplier.status === 'INACTIVE' ? ' (Inactive)' : ''}`
    }));
    
  } catch (error) {
    console.error("Error in getActiveSuppliers:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    return [];
  }
}

// Get supplier types for dropdown
export async function getSupplierTypes(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("system")
    .from("options")
    .select("id, code, label")
    .eq("type", "supplier_type")
    .eq("is_active", true)
    .order("label");

  if (error || !data) {
    // Return default supplier types
    return [
      { value: "VENDOR", label: "Vendor" },
      { value: "CONTRACTOR", label: "Contractor" },
      { value: "SERVICE_PROVIDER", label: "Service Provider" },
      { value: "CONSULTANT", label: "Consultant" }
    ];
  }

  return data.map(type => ({
    value: type.code,
    label: type.label
  }));
}

// Get supplier categories for dropdown
export async function getSupplierCategories(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("system")
    .from("options")
    .select("id, code, label")
    .eq("type", "supplier_category")
    .eq("is_active", true)
    .order("label");

  if (error || !data) {
    // Return default categories
    return [
      { value: "OFFICE_SUPPLIES", label: "Office Supplies" },
      { value: "IT_EQUIPMENT", label: "IT Equipment" },
      { value: "FURNITURE", label: "Furniture" },
      { value: "CATERING", label: "Catering" },
      { value: "PROFESSIONAL_SERVICES", label: "Professional Services" },
      { value: "MAINTENANCE", label: "Maintenance" },
      { value: "UTILITIES", label: "Utilities" }
    ];
  }

  return data.map(category => ({
    value: category.code,
    label: category.label
  }));
}

// Get supplier performance metrics
export async function getSupplierPerformance(supplierId: string): Promise<{
  totalOrders: number;
  totalAmount: number;
  onTimeDeliveryRate: number;
  qualityScore: number;
  lastOrderDate?: string;
}> {
  const supabase = await createClient();

  try {
    // Get total orders and amount
    const { data: ordersData, error: ordersError } = await supabase
      .schema("procurement")
      .from("purchase_orders")
      .select("total_amount, po_date, expected_delivery_date")
      .eq("supplier_id", supplierId)
      .neq("status", "CANCELLED");

    if (ordersError) throw ordersError;

    const totalOrders = ordersData?.length || 0;
    const totalAmount = ordersData?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0;

    // Get delivery performance (simplified calculation)
    const { data: receiptsData, error: receiptsError } = await supabase
      .schema("procurement")
      .from("goods_receipts")
      .select(`
        receipt_date,
        purchase_orders!inner(expected_delivery_date, supplier_id)
      `)
      .eq("purchase_orders.supplier_id", supplierId)
      .eq("status", "COMPLETE");

    if (receiptsError) throw receiptsError;

    let onTimeDeliveries = 0;
    if (receiptsData) {
      onTimeDeliveries = receiptsData.filter(receipt => {
        const receiptDate = new Date(receipt.receipt_date);
        const expectedDate = new Date((receipt.purchase_orders as any)?.[0]?.expected_delivery_date);
        return receiptDate <= expectedDate;
      }).length;
    }

    const onTimeDeliveryRate = receiptsData?.length ? (onTimeDeliveries / receiptsData.length) * 100 : 0;

    // Quality score (simplified - based on rejection rates)
    const { data: qualityData, error: qualityError } = await supabase
      .schema("procurement")
      .from("goods_receipt_items")
      .select(`
        quantity_received,
        quantity_rejected,
        goods_receipts!inner(
          purchase_orders!inner(supplier_id)
        )
      `)
      .eq("goods_receipts.purchase_orders.supplier_id", supplierId);

    if (qualityError) throw qualityError;

    let qualityScore = 100;
    if (qualityData && qualityData.length > 0) {
      const totalReceived = qualityData.reduce((sum, item) => sum + item.quantity_received, 0);
      const totalRejected = qualityData.reduce((sum, item) => sum + item.quantity_rejected, 0);
      qualityScore = totalReceived > 0 ? ((totalReceived - totalRejected) / totalReceived) * 100 : 100;
    }

    const lastOrderDate = ordersData && ordersData.length > 0
      ? ordersData.reduce((latest, order) =>
        order.po_date > latest ? order.po_date : latest, ordersData[0].po_date)
      : undefined;

    return {
      totalOrders,
      totalAmount,
      onTimeDeliveryRate,
      qualityScore,
      lastOrderDate
    };
  } catch {
    return {
      totalOrders: 0,
      totalAmount: 0,
      onTimeDeliveryRate: 0,
      qualityScore: 0
    };
  }
}