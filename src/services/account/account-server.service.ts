'use server'

import { createClient } from '@/utils/supabase/server';

interface PasswordChangeData {
    currentPassword: string;
    newPassword: string;
}

interface ServiceResponse<T = void> {
    data?: T;
    error?: string;
    success: boolean;
}

export async function updateProfile(userData: {
    fullName: string;
    partial_nric: string;
    date_of_birth: Date;
    email: string;
    mobileNumber: string;
}): Promise<{ success: boolean; error?: string }> {
    const supabase = await createClient();

    try {
        // Get the current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError) {
            console.error('Error fetching user:', userError);
            return { success: false, error: userError.message };
        }

        if (!user) {
            console.log('No user found');
            return { success: false, error: 'User not found' };
        }

        const formattedDateOfBirth = userData.date_of_birth.toISOString();

        // Update the user_meta table
        const { error: updateError } = await supabase
            .from('user_meta')
            .update({
                partial_nric: userData.partial_nric,
                date_of_birth: formattedDateOfBirth,
                full_name: userData.fullName,
            })
            .eq('id', user.id);

        if (updateError) {
            console.error('Error updating profile in user_meta:', updateError);
            return { success: false, error: updateError.message };
        }

        // Update email and phone number in Supabase Auth
        const { error: authError } = await supabase.auth.updateUser({
            email: userData.email,
            phone: userData.mobileNumber,
        });

        if (authError) {
            console.error('Error updating email or phone number in auth:', authError);
            return { success: false, error: authError.message };
        }

        return { success: true };
    } catch (err) {
        console.error('Unexpected error while updating profile:', err);
        return { success: false, error: 'Unexpected error occurred' };
    }
}

export async function changeNewPassword(data: PasswordChangeData): Promise<ServiceResponse> {
    const supabase = await createClient();

    try {
        // Step 1: Get the current user to retrieve their email
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user || !user.email) {
            return { success: false, error: 'Failed to get user information or no authenticated user found' };
        }

        // Step 2: Sign in with old password to verify identity
        const { error: signInError } = await supabase.auth.signInWithPassword({
            email: user.email,
            password: data.currentPassword,
        });
        if (signInError) {
            return { success: false, error: 'Old password is incorrect, please try again' };
        }

        // Step 3: Update the user's password
        const { error: updateError } = await supabase.auth.updateUser({
            password: data.newPassword,
        });

        if (updateError) {
            if (updateError.code === 'same_password') {
                return { success: false, error: 'Your new password cannot be the same as your current password. Please choose a different password.' };
            }
            return { success: false, error: "Failed to update your new password, please try again." };
        }

        return { success: true };

    } catch (error) {
        return { success: false, error: 'An unexpected error occurred. Please try again later' };
    }
}