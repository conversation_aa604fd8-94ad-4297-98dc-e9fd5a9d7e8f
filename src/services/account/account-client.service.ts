'use client'

import { createClient } from '@/utils/supabase/Client';

export async function getProfileData(): Promise<{
  profile: {
    fullName: string;
    partial_nric: string;
    date_of_birth?: Date;
    email: string;
    mobileNumber: string;
  } | null;
  success: boolean;
}> {
  const supabase = await createClient();

  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error fetching user:', userError);
      throw userError;
    }

    if (!user) {
      console.log('No user found');
      return { profile: null, success: false };
    }

    // Fetch user meta
    const { data: profile, error: profileError } = await supabase
      .from('user_meta')
      .select('partial_nric, date_of_birth, id, full_name')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Profile fetch error:', profileError);
      throw profileError;
    }

    if (!profile) {
      console.log('No profile found');
      return { profile: null, success: true };
    }

    const profileData = {
      fullName: profile.full_name ?? '',
      partial_nric: profile.partial_nric ?? '',
      date_of_birth: profile?.date_of_birth ? new Date(profile.date_of_birth) : undefined,
      email: user.email ?? '',
      mobileNumber: user.phone ? `+${user.phone}` : ''
    };

    return { profile: profileData, success: true };
  } catch (error) {
    console.error('Error fetching profile:', error);
    return { profile: null, success: false };
  }
}