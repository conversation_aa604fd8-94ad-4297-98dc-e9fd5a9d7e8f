// Enterprise User Management Client Service
// Client-side functions for user and role management with optimistic updates

import { createClient } from '@/utils/supabase/Client'
import type {
  User,
  UserWithRole,
  Role,
  Module,
  UserPermissions,
  CreateUserRequest,
  UpdateUserRequest,
  AssignRoleRequest,
  CreateRoleRequest,
  UserManagementResponse,
  UserFilters,
  RoleFilters
} from '@/types/user-management.types'

export class UserManagementClientService {
  private static getClient() {
    return createClient()
  }

  // ============================================================================
  // USER MANAGEMENT FUNCTIONS
  // ============================================================================

  /**
   * Get all users with their assigned roles (client-side)
   */
  static async getUsersWithRoles(): Promise<UserManagementResponse<UserWithRole[]>> {
    try {
      const supabase = this.getClient()
      
      const { data, error } = await supabase.rpc('get_users_with_roles')
      
      if (error) {
        console.error('Error fetching users with roles:', error)
        return { data: [], success: false, error: error.message }
      }

      return { data: data || [], success: true }
    } catch (err) {
      console.error('Unexpected error in getUsersWithRoles:', err)
      return { data: [], success: false, error: 'An unexpected error occurred' }
    }
  }

  /**
   * Filter users based on criteria
   */
  static filterUsers(users: UserWithRole[], filters: UserFilters): UserWithRole[] {
    return users.filter(user => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const searchableFields = [
          user.full_name,
          user.email,
          user.role_name,
          user.partial_nric
        ].filter(Boolean).join(' ').toLowerCase()
        
        if (!searchableFields.includes(searchTerm)) {
          return false
        }
      }

      // Note: user_type filter removed since all admin panel users are ADMIN type

      // Role filter
      if (filters.role && filters.role !== 'ALL') {
        if (user.role_name !== filters.role) {
          return false
        }
      }

      // Status filter
      if (filters.status !== undefined && filters.status !== 'ALL') {
        if (user.status !== filters.status) {
          return false
        }
      }

      // Date range filter
      if (filters.created_date_from) {
        const userDate = new Date(user.created_at)
        const fromDate = new Date(filters.created_date_from)
        if (userDate < fromDate) {
          return false
        }
      }

      if (filters.created_date_to) {
        const userDate = new Date(user.created_at)
        const toDate = new Date(filters.created_date_to)
        if (userDate > toDate) {
          return false
        }
      }

      return true
    })
  }

  /**
   * Sort users by field
   */
  static sortUsers(
    users: UserWithRole[], 
    field: keyof UserWithRole, 
    direction: 'asc' | 'desc'
  ): UserWithRole[] {
    return [...users].sort((a, b) => {
      const aValue = a[field]
      const bValue = b[field]

      if (aValue === null || aValue === undefined) return 1
      if (bValue === null || bValue === undefined) return -1

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue)
        return direction === 'asc' ? comparison : -comparison
      }

      if (aValue < bValue) return direction === 'asc' ? -1 : 1
      if (aValue > bValue) return direction === 'asc' ? 1 : -1
      return 0
    })
  }

  /**
   * Get user statistics
   */
  static getUserStatistics(users: UserWithRole[]) {
    const total = users.length
    const active = users.filter(u => u.status).length
    const inactive = total - active
    const usersWithRoles = users.filter(u => u.role_name && u.role_name !== 'NO_ROLE').length
    const usersWithoutRoles = total - usersWithRoles

    return {
      total,
      active,
      inactive,
      usersWithRoles,
      usersWithoutRoles,
      activePercentage: total > 0 ? Math.round((active / total) * 100) : 0
    }
  }

  // ============================================================================
  // ROLE MANAGEMENT FUNCTIONS
  // ============================================================================

  /**
   * Get all roles (client-side)
   */
  static async getRoles(): Promise<UserManagementResponse<Role[]>> {
    try {
      const supabase = this.getClient()
      
      const { data, error } = await supabase
        .schema('access_control')
        .from('roles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        return { data: [], success: false, error: error.message }
      }

      return { data: data || [], success: true }
    } catch (err) {
      console.error('Unexpected error in getRoles:', err)
      return { data: [], success: false, error: 'An unexpected error occurred' }
    }
  }

  /**
   * Get all roles with user counts (client-side)
   */
  static async getRolesWithUserCounts(): Promise<UserManagementResponse<Role[]>> {
    try {
      const supabase = this.getClient()
      
      const { data, error } = await supabase.rpc('get_roles_with_user_counts')
      
      if (error) {
        console.error('Error fetching roles with user counts:', error)
        return { data: [], success: false, error: error.message }
      }

      return { data: data || [], success: true }
    } catch (err) {
      console.error('Unexpected error in getRolesWithUserCounts:', err)
      return { data: [], success: false, error: 'An unexpected error occurred' }
    }
  }

  /**
   * Filter roles based on criteria
   */
  static filterRoles(roles: Role[], filters: RoleFilters): Role[] {
    return roles.filter(role => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const searchableFields = [
          role.name,
          role.role_description
        ].filter(Boolean).join(' ').toLowerCase()
        
        if (!searchableFields.includes(searchTerm)) {
          return false
        }
      }

      // Active status filter
      if (filters.is_active !== undefined && filters.is_active !== 'ALL') {
        if (role.is_active !== filters.is_active) {
          return false
        }
      }

      return true
    })
  }

  /**
   * Get role statistics
   */
  static getRoleStatistics(roles: Role[]) {
    const total = roles.length
    const active = roles.filter(r => r.is_active).length
    const inactive = total - active

    return {
      total,
      active,
      inactive,
      activePercentage: total > 0 ? Math.round((active / total) * 100) : 0
    }
  }

  // ============================================================================
  // PERMISSION MANAGEMENT FUNCTIONS
  // ============================================================================

  /**
   * Get current user permissions (client-side with caching)
   */
  static async getCurrentUserPermissions(): Promise<UserManagementResponse<UserPermissions[]>> {
    try {
      const supabase = this.getClient()
      
      const { data, error } = await supabase.rpc('get_user_permissions')

      if (error) {
        return { data: [], success: false, error: error.message }
      }

      return { data: data || [], success: true }
    } catch (err) {
      console.error('Unexpected error in getCurrentUserPermissions:', err)
      return { data: [], success: false, error: 'An unexpected error occurred' }
    }
  }

  /**
   * Check specific permission (client-side)
   */
  static async checkPermission(
    module: string, 
    action: string, 
    membershipTypeId?: string
  ): Promise<boolean> {
    try {
      const supabase = this.getClient()
      
      const { data, error } = await supabase.rpc('check_user_permission', {
        module_name: module,
        action: action,
        membership_type_id: membershipTypeId || null
      })

      if (error) {
        console.error('Permission check error:', error)
        return false
      }

      return Boolean(data)
    } catch (err) {
      console.error('Unexpected error in checkPermission:', err)
      return false
    }
  }

  /**
   * Get system modules (client-side)
   */
  static async getModules(): Promise<UserManagementResponse<Module[]>> {
    try {
      const supabase = this.getClient()
      
      // First try to use RPC function if available
      const { data: rpcData, error: rpcError } = await supabase
        .schema('access_control').rpc('get_system_modules')
      
      if (!rpcError && rpcData) {
        return { data: rpcData || [], success: true }
      }
      
      // If RPC fails, try direct table access (for backward compatibility)
      const { data, error } = await supabase
        .schema('access_control')
        .from('modules')
        .select('*')
        .eq('is_active', true)
        .order('module_category', { ascending: true })

      if (error) {
        // Always return fallback modules when there's any error
        console.warn('Error accessing modules table:', error.message, '. Using fallback module list.')
        const fallbackModules: Module[] = [
          {
            module_id: 'membership',
            module_name: 'MEMBERSHIP',
            module_description: 'Manage member registrations and profiles',
            module_category: 'CORE',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            module_id: 'application',
            module_name: 'APPLICATION',
            module_description: 'Handle member applications and approvals',
            module_category: 'CORE',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            module_id: 'programme',
            module_name: 'PROGRAMME',
            module_description: 'Manage educational programmes and courses',
            module_category: 'OPERATIONS',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            module_id: 'finance',
            module_name: 'FINANCE',
            module_description: 'Financial management and reporting',
            module_category: 'OPERATIONS',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            module_id: 'system',
            module_name: 'SYSTEM',
            module_description: 'System administration and configuration',
            module_category: 'ADMIN',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            module_id: 'reports',
            module_name: 'REPORTS',
            module_description: 'Generate and view system reports',
            module_category: 'OPERATIONS',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ]
        return { data: fallbackModules, success: true }
      }

      return { data: data || [], success: true }
    } catch (err) {
      console.error('Unexpected error in getModules:', err)
      // Return fallback modules even for unexpected errors
      const fallbackModules: Module[] = [
        {
          module_id: 'membership',
          module_name: 'MEMBERSHIP',
          module_description: 'Manage member registrations and profiles',
          module_category: 'CORE',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          module_id: 'application',
          module_name: 'APPLICATION',
          module_description: 'Handle member applications and approvals',
          module_category: 'CORE',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          module_id: 'programme',
          module_name: 'PROGRAMME',
          module_description: 'Manage educational programmes and courses',
          module_category: 'OPERATIONS',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          module_id: 'finance',
          module_name: 'FINANCE',
          module_description: 'Financial management and reporting',
          module_category: 'OPERATIONS',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          module_id: 'system',
          module_name: 'SYSTEM',
          module_description: 'System administration and configuration',
          module_category: 'ADMIN',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          module_id: 'reports',
          module_name: 'REPORTS',
          module_description: 'Generate and view system reports',
          module_category: 'OPERATIONS',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]
      return { data: fallbackModules, success: true }
    }
  }

  // ============================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ============================================================================

  /**
   * Subscribe to user changes
   */
  static subscribeToUserChanges(callback: (payload: any) => void) {
    const supabase = createClient()
    return supabase
      .channel('user_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_meta'
        },
        callback
      )
      .subscribe()
  }

  /**
   * Subscribe to role changes
   */
  static subscribeToRoleChanges(callback: (payload: any) => void) {
    const supabase = createClient()
    return supabase
      .channel('role_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'access_control',
          table: 'roles'
        },
        callback
      )
      .subscribe()
  }

  /**
   * Subscribe to user role assignments
   */
  static subscribeToUserRoleChanges(callback: (payload: any) => void) {
    const supabase = createClient()
    return supabase
      .channel('user_role_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'access_control',
          table: 'user_roles'
        },
        callback
      )
      .subscribe()
  }

  // ============================================================================
  // BULK OPERATIONS
  // ============================================================================

  /**
   * Bulk update user status
   */
  static async bulkUpdateUserStatus(
    userIds: string[], 
    status: boolean
  ): Promise<UserManagementResponse<boolean>> {
    try {
      const supabase = this.getClient()
      
      const { error } = await supabase
        .from('user_meta')
        .update({ status, updated_at: new Date().toISOString() })
        .in('id', userIds)

      if (error) {
        return { data: false, success: false, error: error.message }
      }

      return { data: true, success: true }
    } catch (err) {
      console.error('Unexpected error in bulkUpdateUserStatus:', err)
      return { data: false, success: false, error: 'An unexpected error occurred' }
    }
  }

  /**
   * Bulk assign role to users
   */
  static async bulkAssignRole(
    userIds: string[], 
    roleName: string, 
    reason?: string
  ): Promise<UserManagementResponse<number>> {
    try {
      let successCount = 0
      const errors: string[] = []

      for (const userId of userIds) {
        try {
          const supabase = this.getClient()
          const { error } = await supabase.rpc('assign_role_to_user', {
            user_id: userId,
            role_name: roleName,
            reason: reason || 'Bulk role assignment'
          })

          if (error) {
            errors.push(`User ${userId}: ${error.message}`)
          } else {
            successCount++
          }
        } catch (err) {
          errors.push(`User ${userId}: Unexpected error`)
        }
      }

      if (errors.length > 0) {
        return { 
          data: successCount, 
          success: false, 
          error: `Partial success. ${successCount}/${userIds.length} users updated. Errors: ${errors.join(', ')}` 
        }
      }

      return { data: successCount, success: true }
    } catch (err) {
      console.error('Unexpected error in bulkAssignRole:', err)
      return { data: 0, success: false, error: 'An unexpected error occurred' }
    }
  }

  // ============================================================================
  // VALIDATION HELPERS
  // ============================================================================

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Validate user data
   */
  static validateUserData(userData: Partial<CreateUserRequest>): string[] {
    const errors: string[] = []

    if (!userData.email) {
      errors.push('Email is required')
    } else if (!this.validateEmail(userData.email)) {
      errors.push('Invalid email format')
    }

    if (!userData.full_name || userData.full_name.trim().length < 2) {
      errors.push('Full name must be at least 2 characters')
    }

    if (userData.password && userData.password.length < 8) {
      errors.push('Password must be at least 8 characters')
    }

    return errors
  }

  /**
   * Validate role data
   */
  static validateRoleData(roleData: Partial<CreateRoleRequest>): string[] {
    const errors: string[] = []

    if (!roleData.name || roleData.name.trim().length < 2) {
      errors.push('Role name must be at least 2 characters')
    }

    // Check for valid role name format (uppercase, underscore)
    if (roleData.name && !/^[A-Z][A-Z0-9_]*$/.test(roleData.name)) {
      errors.push('Role name must start with uppercase letter and contain only uppercase letters, numbers, and underscores')
    }

    return errors
  }
}