import { createClient } from "@/utils/supabase/server";
import { Database } from "@/types/database.types";
import type {
  PurchaseInvoice,
  PurchaseInvoiceFilters,
  PurchaseInvoiceSummary,
  CreatePurchaseInvoiceInput,
  ThreeWayMatchResult,
  SupplierStatement,
  VendorPerformanceMetrics,
  PurchaseInvoiceItem,
  PaymentInfo,
  SupplierStatementLine,
} from "@/types/purchase-invoices/purchase-invoices.types";

export async function getPurchaseInvoicesServer(filters: PurchaseInvoiceFilters) {
  const supabase = await createClient();
  
  let query = supabase
    .from("purchase_invoices")
    .select(`
      *,
      supplier:suppliers!inner(
        id,
        supplier_code,
        name,
        display_name,
        email,
        phone,
        payment_terms,
        status
      ),
      entity:entities!inner(
        id,
        name,
        code
      ),
      purchase_order:purchase_orders(
        id,
        po_number,
        po_date,
        total_amount,
        status,
        delivery_date
      ),
      items:purchase_invoice_items(count),
      payments:supplier_payments!invoice_id(
        amount
      )
    `, { count: "exact" });

  // Apply filters
  if (filters.search) {
    query = query.or(
      `invoice_number.ilike.%${filters.search}%,supplier.name.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%,supplier_reference.ilike.%${filters.search}%`
    );
  }

  if (filters.status && filters.status.length > 0) {
    query = query.in("status", filters.status);
  }

  if (filters.payment_status && filters.payment_status.length > 0) {
    query = query.in("payment_status", filters.payment_status);
  }

  if (filters.supplier_id) {
    query = query.eq("supplier_id", filters.supplier_id);
  }

  if (filters.entity_id) {
    query = query.eq("entity_id", filters.entity_id);
  }

  if (filters.purchase_order_id) {
    query = query.eq("purchase_order_id", filters.purchase_order_id);
  }

  if (filters.date_from) {
    query = query.gte("invoice_date", filters.date_from);
  }

  if (filters.date_to) {
    query = query.lte("invoice_date", filters.date_to);
  }

  if (filters.due_date_from) {
    query = query.gte("due_date", filters.due_date_from);
  }

  if (filters.due_date_to) {
    query = query.lte("due_date", filters.due_date_to);
  }

  if (filters.amount_min) {
    query = query.gte("total_amount", filters.amount_min);
  }

  if (filters.amount_max) {
    query = query.lte("total_amount", filters.amount_max);
  }

  if (filters.is_overdue) {
    const today = new Date().toISOString().split("T")[0];
    query = query.lt("due_date", today).neq("payment_status", "COMPLETED");
  }

  // Sorting
  const sortColumn = filters.sort_by || "invoice_date";
  const sortOrder = filters.sort_order || "desc";
  query = query.order(sortColumn, { ascending: sortOrder === "asc" });

  // Pagination
  const page = filters.page || 1;
  const limit = filters.limit || 10;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching purchase invoices:", error);
    throw error;
  }

  // Calculate computed fields
  const invoices = (data || []).map((invoice) => {
    const totalPaid = invoice.payments?.reduce(
      (sum: number, payment: PaymentInfo) => sum + payment.amount,
      0
    ) || 0;
    
    const outstanding = invoice.total_amount - totalPaid;
    
    let daysOverdue = 0;
    if (invoice.due_date && outstanding > 0) {
      const dueDate = new Date(invoice.due_date);
      const today = new Date();
      const diffTime = today.getTime() - dueDate.getTime();
      daysOverdue = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
    }

    return {
      ...invoice,
      outstanding_amount: outstanding,
      days_overdue: daysOverdue,
      items_count: invoice.items?.[0]?.count || 0,
    };
  });

  return {
    data: invoices,
    total: count || 0,
    page,
    limit,
    totalPages: Math.ceil((count || 0) / limit),
  };
}

export async function getPurchaseInvoiceByIdServer(id: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from("purchase_invoices")
    .select(`
      *,
      supplier:suppliers!inner(
        id,
        supplier_code,
        name,
        display_name,
        email,
        phone,
        payment_terms,
        status
      ),
      entity:entities!inner(
        id,
        name,
        code
      ),
      purchase_order:purchase_orders(
        id,
        po_number,
        po_date,
        total_amount,
        status,
        delivery_date
      ),
      items:purchase_invoice_items(
        id,
        po_item_id,
        line_number,
        description,
        quantity,
        unit_price,
        discount_amount,
        discount_percentage,
        tax_amount,
        tax_percentage,
        line_total,
        item_code,
        gl_account_code,
        cost_center,
        created_at,
        updated_at
      ),
      payments:supplier_payments!invoice_id(
        id,
        payment_number,
        payment_date,
        amount,
        payment_method,
        reference_number,
        status,
        bank_details
      ),
      approval_history:approval_requests!request_id(
        id,
        action,
        approver_id,
        approval_date,
        comments,
        approval_level
      )
    `)
    .eq("id", id)
    .single();

  if (error) {
    console.error("Error fetching purchase invoice:", error);
    throw error;
  }

  // Calculate outstanding amount
  const totalPaid = data.payments?.reduce((sum: number, payment: PaymentInfo) => sum + payment.amount, 0) || 0;
  const outstanding = data.total_amount - totalPaid;
  
  let daysOverdue = 0;
  if (data.due_date && outstanding > 0) {
    const dueDate = new Date(data.due_date);
    const today = new Date();
    const diffTime = today.getTime() - dueDate.getTime();
    daysOverdue = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }

  return {
    ...data,
    outstanding_amount: outstanding,
    days_overdue: daysOverdue,
  } as PurchaseInvoice;
}

export async function getPurchaseInvoicesSummaryServer(): Promise<PurchaseInvoiceSummary> {
  const supabase = await createClient();
  
  // Get all invoices for calculations
  const { data: invoicesData, error: invoicesError } = await supabase
    .from("purchase_invoices")
    .select(`
      id,
      total_amount,
      payment_status,
      due_date,
      status,
      created_at,
      payments:supplier_payments!invoice_id(amount)
    `)
    .not("status", "eq", "CANCELLED");

  if (invoicesError) {
    console.error("Error fetching invoices summary:", invoicesError);
    throw invoicesError;
  }

  const total_count = invoicesData?.length || 0;
  
  // Calculate outstanding amount
  const outstanding_amount = invoicesData?.reduce((total: number, invoice: { total_amount: number; payments?: Array<{ amount: number }> }) => {
    const totalPaid = invoice.payments?.reduce((sum: number, payment: { amount: number }) => sum + payment.amount, 0) || 0;
    const outstanding = invoice.total_amount - totalPaid;
    return total + Math.max(0, outstanding);
  }, 0) || 0;

  // Count overdue invoices
  const today = new Date().toISOString().split("T")[0];
  const overdue_count = invoicesData?.filter((invoice: { due_date: string | null; payment_status: string; total_amount: number; payments?: Array<{ amount: number }> }) => {
    if (!invoice.due_date || invoice.payment_status === "COMPLETED") return false;
    
    const totalPaid = invoice.payments?.reduce((sum: number, payment: { amount: number }) => sum + payment.amount, 0) || 0;
    const outstanding = invoice.total_amount - totalPaid;
    
    return invoice.due_date < today && outstanding > 0;
  }).length || 0;

  // Count pending approval
  const pending_approval_count = invoicesData?.filter((inv: { status: string }) => 
    inv.status === "PENDING_APPROVAL"
  ).length || 0;

  // This month amount
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  const this_month_amount = invoicesData?.filter((inv: { created_at: string }) => 
    new Date(inv.created_at) >= startOfMonth
  ).reduce((sum: number, inv: { total_amount: number }) => sum + inv.total_amount, 0) || 0;

  return {
    total_count,
    outstanding_amount,
    overdue_count,
    pending_approval_count,
    this_month_amount,
    average_processing_days: 5.2, // This would need to be calculated based on actual data
  };
}

export async function createPurchaseInvoiceServer(input: CreatePurchaseInvoiceInput) {
  const supabase = await createClient();
  
  // Generate invoice number if not provided
  let invoiceNumber = input.invoice_number;
  if (!invoiceNumber) {
    const { data: numberData, error: numberError } = await supabase
      .rpc("generate_pi_number");
    
    if (numberError) {
      console.error("Error generating invoice number:", numberError);
      throw numberError;
    }
    
    invoiceNumber = numberData;
  }

  // Start a transaction by creating the invoice and items in sequence
  const { data: invoice, error: invoiceError } = await supabase
    .from("purchase_invoices")
    .insert({
      supplier_id: input.supplier_id,
      entity_id: input.entity_id,
      purchase_order_id: input.purchase_order_id,
      invoice_number: invoiceNumber,
      supplier_reference: input.supplier_reference,
      invoice_date: input.invoice_date,
      due_date: input.due_date,
      currency_code: input.currency_code,
      reference_number: input.reference_number,
      notes: input.notes,
      status: "SUBMITTED",
      payment_status: "PENDING",
      total_amount: 0, // Will be calculated
      tax_amount: 0,
      created_at: new Date().toISOString(),
      created_by: "current_user", // Should be replaced with actual user
    })
    .select()
    .single();

  if (invoiceError) {
    console.error("Error creating purchase invoice:", invoiceError);
    throw invoiceError;
  }

  // Insert items
  const itemsToInsert = input.items.map((item) => ({
    invoice_id: invoice.id,
    po_item_id: item.po_item_id,
    line_number: item.line_number,
    description: item.description,
    quantity: item.quantity,
    unit_price: item.unit_price,
    discount_amount: item.discount_amount || 0,
    discount_percentage: item.discount_percentage || 0,
    tax_amount: (item.quantity * item.unit_price * item.tax_percentage) / 100,
    tax_percentage: item.tax_percentage,
    line_total: item.quantity * item.unit_price,
    item_code: item.item_code,
    gl_account_code: item.gl_account_code,
    cost_center: item.cost_center,
    created_at: new Date().toISOString(),
  }));

  const { data: items, error: itemsError } = await supabase
    .from("purchase_invoice_items")
    .insert(itemsToInsert)
    .select();

  if (itemsError) {
    console.error("Error creating purchase invoice items:", itemsError);
    // Rollback by deleting the invoice
    await supabase.from("purchase_invoices").delete().eq("id", invoice.id);
    throw itemsError;
  }

  // Calculate totals
  const subtotal = items.reduce((sum: number, item: PurchaseInvoiceItem) => sum + item.line_total, 0);
  const totalTax = items.reduce((sum: number, item: PurchaseInvoiceItem) => sum + item.tax_amount, 0);
  const totalAmount = subtotal + totalTax;

  // Update invoice with totals
  const { data: updatedInvoice, error: updateError } = await supabase
    .from("purchase_invoices")
    .update({ 
      total_amount: totalAmount,
      tax_amount: totalTax 
    })
    .eq("id", invoice.id)
    .select()
    .single();

  if (updateError) {
    console.error("Error updating invoice totals:", updateError);
    throw updateError;
  }

  return updatedInvoice;
}

export async function getThreeWayMatchResultServer(invoiceId: string): Promise<ThreeWayMatchResult | null> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .rpc("perform_three_way_match", {
      invoice_id: invoiceId,
    });

  if (error) {
    console.error("Error getting three-way match result:", error);
    return null;
  }

  return data;
}

export async function getSupplierStatementServer(
  supplierId: string,
  fromDate: string,
  toDate: string
): Promise<SupplierStatement> {
  const supabase = await createClient();
  
  // Get supplier information
  const { data: supplier, error: supplierError } = await supabase
    .from("suppliers")
    .select("id, name, supplier_code")
    .eq("id", supplierId)
    .single();

  if (supplierError) {
    console.error("Error fetching supplier:", supplierError);
    throw supplierError;
  }

  // Get invoices in date range
  const { data: invoices, error: invoicesError } = await supabase
    .from("purchase_invoices")
    .select(`
      id,
      invoice_number,
      invoice_date,
      due_date,
      total_amount,
      status,
      payments:supplier_payments!invoice_id(
        payment_date,
        amount,
        reference_number
      )
    `)
    .eq("supplier_id", supplierId)
    .gte("invoice_date", fromDate)
    .lte("invoice_date", toDate)
    .order("invoice_date", { ascending: true });

  if (invoicesError) {
    console.error("Error fetching invoices:", invoicesError);
    throw invoicesError;
  }

  // Build statement lines
  const lines: SupplierStatementLine[] = [];
  let runningBalance = 0;

  invoices?.forEach((invoice: { invoice_date: string; invoice_number: string; total_amount: number; payments?: Array<{ payment_date: string; amount: number; reference_number: string | null }> }) => {
    // Add invoice line
    runningBalance += invoice.total_amount;
    lines.push({
      date: invoice.invoice_date,
      type: "INVOICE",
      reference: invoice.invoice_number,
      description: `Invoice ${invoice.invoice_number}`,
      debit_amount: invoice.total_amount,
      credit_amount: 0,
      balance: runningBalance,
    });

    // Add payment lines
    invoice.payments?.forEach((payment: { payment_date: string; amount: number; reference_number: string | null }) => {
      runningBalance -= payment.amount;
      lines.push({
        date: payment.payment_date,
        type: "PAYMENT",
        reference: payment.reference_number || "",
        description: `Payment for ${invoice.invoice_number}`,
        debit_amount: 0,
        credit_amount: payment.amount,
        balance: runningBalance,
      });
    });
  });

  const totalInvoices = invoices?.reduce((sum: number, inv: { total_amount: number }) => sum + inv.total_amount, 0) || 0;
  const totalPayments = invoices?.reduce((sum: number, inv: { payments?: Array<{ amount: number }> }) => {
    return sum + (inv.payments?.reduce((pSum: number, p: { amount: number }) => pSum + p.amount, 0) || 0);
  }, 0) || 0;

  return {
    supplier_id: supplierId,
    supplier_name: supplier.name,
    statement_date: new Date().toISOString().split("T")[0],
    from_date: fromDate,
    to_date: toDate,
    opening_balance: 0, // Would need to calculate from previous period
    closing_balance: totalInvoices - totalPayments,
    total_invoices: totalInvoices,
    total_payments: totalPayments,
    total_adjustments: 0,
    lines,
  };
}

export async function getVendorPerformanceMetricsServer(supplierId: string): Promise<VendorPerformanceMetrics> {
  const supabase = await createClient();
  
  // Get supplier information
  const { data: supplier, error: supplierError } = await supabase
    .from("suppliers")
    .select("id, name, supplier_code")
    .eq("id", supplierId)
    .single();

  if (supplierError) {
    console.error("Error fetching supplier:", supplierError);
    throw supplierError;
  }

  // Get performance data
  const { data: invoices, error: invoicesError } = await supabase
    .from("purchase_invoices")
    .select(`
      id,
      total_amount,
      invoice_date,
      created_at,
      approved_at,
      purchase_order:purchase_orders(
        delivery_date,
        po_date
      )
    `)
    .eq("supplier_id", supplierId)
    .gte("invoice_date", new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()) // Last year
    .not("status", "eq", "CANCELLED");

  if (invoicesError) {
    console.error("Error fetching invoice performance data:", invoicesError);
    throw invoicesError;
  }

  const typedInvoices = invoices as unknown as InvoiceWithApproval[] | null;
  const totalInvoices = typedInvoices?.length || 0;
  const totalAmount = typedInvoices?.reduce((sum: number, inv: InvoiceWithApproval) => sum + inv.total_amount, 0) || 0;

  // Calculate average processing days
  type InvoiceWithApproval = {
    id: string;
    total_amount: number;
    invoice_date: string;
    created_at: string;
    approved_at?: string;
    purchase_order?: {
      delivery_date: string | null;
      po_date: string;
    };
  };

  const processingDays = typedInvoices?.filter((inv: InvoiceWithApproval) => inv.approved_at).map((inv: InvoiceWithApproval) => {
    const created = new Date(inv.created_at);
    const approved = new Date(inv.approved_at!);
    return Math.ceil((approved.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  }) || [];

  const averageProcessingDays = processingDays.length > 0 
    ? processingDays.reduce((sum: number, days: number) => sum + days, 0) / processingDays.length
    : 0;

  // Calculate on-time delivery rate (simplified)
  const onTimeDeliveries = typedInvoices?.filter((inv: InvoiceWithApproval) => {
    if (!inv.purchase_order?.delivery_date) return true;
    return new Date(inv.invoice_date) <= new Date(inv.purchase_order.delivery_date);
  }).length || 0;

  const onTimeDeliveryRate = totalInvoices > 0 ? (onTimeDeliveries / totalInvoices) * 100 : 100;

  return {
    supplier_id: supplierId,
    supplier_name: supplier.name,
    total_invoices: totalInvoices,
    total_amount: totalAmount,
    average_processing_days: Math.round(averageProcessingDays * 10) / 10,
    on_time_delivery_rate: Math.round(onTimeDeliveryRate * 10) / 10,
    price_variance_percentage: 2.1, // Would need more complex calculation
    quality_score: 8.5, // Would come from quality management system
    payment_terms_compliance: 95.2, // Would need payment analysis
  };
}

export async function getOverdueInvoicesServer(supplierId?: string) {
  const supabase = await createClient();
  
  let query = supabase
    .from("purchase_invoices")
    .select(`
      *,
      supplier:suppliers!inner(
        name,
        supplier_code,
        payment_terms
      ),
      payments:supplier_payments!invoice_id(amount)
    `)
    .neq("payment_status", "COMPLETED")
    .not("status", "eq", "CANCELLED")
    .lt("due_date", new Date().toISOString().split("T")[0]);

  if (supplierId) {
    query = query.eq("supplier_id", supplierId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching overdue invoices:", error);
    throw error;
  }

  return (data || []).map((invoice: { total_amount: number; due_date: string; payments?: Array<{ amount: number }> }) => {
    const totalPaid = invoice.payments?.reduce((sum: number, payment: { amount: number }) => sum + payment.amount, 0) || 0;
    const outstanding = invoice.total_amount - totalPaid;
    
    const dueDate = new Date(invoice.due_date);
    const today = new Date();
    const daysOverdue = Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      ...invoice,
      outstanding_amount: outstanding,
      days_overdue: Math.max(0, daysOverdue),
    };
  }).filter((invoice: { outstanding_amount: number }) => invoice.outstanding_amount > 0);
}