"use client";

import { createClient } from "@/utils/supabase/Client";

// Simple cache implementation for performance optimization
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

const getCachedData = (key: string) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data;
  }
  return null;
};

const setCachedData = (key: string, data: any, ttlMinutes = 15) => {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl: ttlMinutes * 60 * 1000
  });
};
import type {
  OutstandingPayablesFilters,
  OutstandingPayablesData,
  PaymentDueFilters,
  PaymentDueData,
  ThreeWayMatchExceptionFilters,
  ThreeWayMatchExceptionData,
  SupplierPaymentHistoryFilters,
  SupplierPaymentHistoryData,
  VendorPerformanceFilters,
  VendorPerformanceData,
  AnalyticsDashboardData,
  ExportOptions,
  ExportResult
} from "@/types/purchase-invoices/reports.types";

/**
 * Get outstanding payables report with aging analysis
 */
export async function getOutstandingPayables(
  filters: OutstandingPayablesFilters = {}
): Promise<OutstandingPayablesData> {
  // Generate cache key based on filters
  const cacheKey = `outstanding_payables_${JSON.stringify(filters)}`;
  
  // Check cache first
  const cachedResult = getCachedData(cacheKey);
  if (cachedResult) {
    return cachedResult;
  }

  const supabase = await createClient();
  
  try {
    // Build the query based on filters
    let query = supabase
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          payment_terms,
          status
        ),
        entity:entities(
          id,
          name,
          code
        )
      `)
      .in("status", ["APPROVED", "MATCHED"])
      .in("payment_status", ["PENDING", "PARTIAL"]);

    // Apply filters
    if (filters.entity_id) {
      query = query.eq("entity_id", filters.entity_id);
    }
    
    if (filters.supplier_id) {
      query = query.eq("supplier_id", filters.supplier_id);
    }
    
    if (filters.search) {
      query = query.or(`invoice_number.ilike.%${filters.search}%,supplier.name.ilike.%${filters.search}%`);
    }

    // Apply sorting
    switch (filters.sort_by) {
      case "amount_desc":
        query = query.order("total_amount", { ascending: false });
        break;
      case "amount_asc":
        query = query.order("total_amount", { ascending: true });
        break;
      case "supplier_name":
        query = query.order("supplier.name", { ascending: true });
        break;
      default:
        query = query.order("due_date", { ascending: true });
    }

    const { data: invoices, error } = await query;

    if (error) {
      console.error("Error fetching outstanding payables:", error);
      throw error;
    }

    // Process the data to calculate aging and summary
    const processedInvoices = (invoices || []).map(invoice => {
      const dueDate = invoice.due_date ? new Date(invoice.due_date) : new Date();
      const today = new Date();
      const diffTime = today.getTime() - dueDate.getTime();
      const daysOverdue = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
      
      // Calculate outstanding amount (for now, using total_amount - in real app, subtract payments)
      const outstandingAmount = invoice.total_amount || 0;
      
      return {
        ...invoice,
        days_overdue: daysOverdue,
        outstanding_amount: outstandingAmount
      };
    });

    // Filter by aging bucket if specified
    let filteredInvoices = processedInvoices;
    if (filters.aging_bucket && filters.aging_bucket !== "all") {
      filteredInvoices = processedInvoices.filter(invoice => {
        const days = invoice.days_overdue;
        switch (filters.aging_bucket) {
          case "Current (0-30 days)":
            return days >= 0 && days <= 30;
          case "31-60 days":
            return days >= 31 && days <= 60;
          case "61-90 days":
            return days >= 61 && days <= 90;
          case "Over 90 days":
            return days > 90;
          default:
            return true;
        }
      });
    }

    // Calculate summary
    const totalOutstanding = filteredInvoices.reduce((sum, inv) => sum + inv.outstanding_amount, 0);
    const overdueAmount = filteredInvoices
      .filter(inv => inv.days_overdue > 0)
      .reduce((sum, inv) => sum + inv.outstanding_amount, 0);
    const averageDaysOutstanding = filteredInvoices.length > 0
      ? filteredInvoices.reduce((sum, inv) => sum + inv.days_overdue, 0) / filteredInvoices.length
      : 0;

    // Calculate aging analysis
    const agingBuckets = [
      { bucket: "Current (0-30 days)", min: 0, max: 30 },
      { bucket: "31-60 days", min: 31, max: 60 },
      { bucket: "61-90 days", min: 61, max: 90 },
      { bucket: "Over 90 days", min: 91, max: null }
    ];

    const agingAnalysis = agingBuckets.map(bucket => {
      const invoicesInBucket = processedInvoices.filter(inv => {
        const days = inv.days_overdue;
        return days >= bucket.min && (bucket.max === null || days <= bucket.max);
      });
      
      const amount = invoicesInBucket.reduce((sum, inv) => sum + inv.outstanding_amount, 0);
      const count = invoicesInBucket.length;
      const percentage = totalOutstanding > 0 ? (amount / totalOutstanding) * 100 : 0;
      
      return {
        bucket: bucket.bucket,
        amount,
        count,
        percentage
      };
    });

    const result = {
      summary: {
        total_outstanding: totalOutstanding,
        overdue_amount: overdueAmount,
        average_days_outstanding: averageDaysOutstanding,
        total_invoices: filteredInvoices.length,
        currency_breakdown: { USD: totalOutstanding } // Simplified for now
      },
      aging_analysis: agingAnalysis,
      invoices: filteredInvoices,
      generated_at: new Date().toISOString()
    };

    // Cache the result for 15 minutes
    setCachedData(cacheKey, result, 15);
    
    return result;

  } catch (error) {
    console.error("Error in getOutstandingPayables:", error);
    throw new Error("Failed to fetch outstanding payables report");
  }
}

/**
 * Get payment due report with date groupings
 */
export async function getPaymentDueReport(
  filters: PaymentDueFilters = {}
): Promise<PaymentDueData> {
  const supabase = await createClient();
  
  try {
    let query = supabase
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          payment_terms
        ),
        entity:entities(
          id,
          name,
          code
        )
      `)
      .in("status", ["APPROVED", "MATCHED"])
      .in("payment_status", ["PENDING", "PARTIAL"])
      .not("due_date", "is", null);

    // Apply filters
    if (filters.entity_id) {
      query = query.eq("entity_id", filters.entity_id);
    }
    
    if (filters.supplier_id) {
      query = query.eq("supplier_id", filters.supplier_id);
    }
    
    if (filters.due_date_from) {
      query = query.gte("due_date", filters.due_date_from);
    }
    
    if (filters.due_date_to) {
      query = query.lte("due_date", filters.due_date_to);
    }

    query = query.order("due_date", { ascending: true });

    const { data: invoices, error } = await query;

    if (error) {
      console.error("Error fetching payment due report:", error);
      throw error;
    }

    // Process and group by due date
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    const weekAfterNext = new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000);

    const processedInvoices = (invoices || []).map(invoice => ({
      ...invoice,
      outstanding_amount: invoice.total_amount || 0 // Simplified
    }));

    // Group by date
    const groupings = processedInvoices.reduce((groups: any[], invoice) => {
      const dueDate = invoice.due_date;
      const existingGroup = groups.find(g => g.date === dueDate);
      
      if (existingGroup) {
        existingGroup.total_amount += invoice.outstanding_amount;
        existingGroup.invoice_count += 1;
        existingGroup.invoices.push(invoice);
      } else {
        groups.push({
          date: dueDate,
          total_amount: invoice.outstanding_amount,
          invoice_count: 1,
          invoices: [invoice]
        });
      }
      
      return groups;
    }, []);

    // Calculate summary
    const totalDueAmount = processedInvoices.reduce((sum, inv) => sum + inv.outstanding_amount, 0);
    const dueThisWeek = processedInvoices
      .filter(inv => new Date(inv.due_date) <= nextWeek)
      .reduce((sum, inv) => sum + inv.outstanding_amount, 0);
    const dueNextWeek = processedInvoices
      .filter(inv => new Date(inv.due_date) > nextWeek && new Date(inv.due_date) <= weekAfterNext)
      .reduce((sum, inv) => sum + inv.outstanding_amount, 0);
    const overdueAmount = processedInvoices
      .filter(inv => new Date(inv.due_date) < today)
      .reduce((sum, inv) => sum + inv.outstanding_amount, 0);

    return {
      summary: {
        total_due_amount: totalDueAmount,
        due_this_week: dueThisWeek,
        due_next_week: dueNextWeek,
        overdue_amount: overdueAmount,
        total_invoices: processedInvoices.length
      },
      groupings: groupings.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()),
      priority_breakdown: {
        high: { amount: overdueAmount, count: processedInvoices.filter(inv => new Date(inv.due_date) < today).length },
        medium: { amount: dueThisWeek, count: processedInvoices.filter(inv => new Date(inv.due_date) <= nextWeek && new Date(inv.due_date) >= today).length },
        low: { amount: dueNextWeek, count: processedInvoices.filter(inv => new Date(inv.due_date) > nextWeek).length }
      },
      generated_at: new Date().toISOString()
    };

  } catch (error) {
    console.error("Error in getPaymentDueReport:", error);
    throw new Error("Failed to fetch payment due report");
  }
}

/**
 * Get three-way match exception report
 */
export async function getThreeWayMatchExceptions(
  filters: ThreeWayMatchExceptionFilters = {}
): Promise<ThreeWayMatchExceptionData> {
  const supabase = await createClient();
  
  try {
    // For now, return mock data as three-way match exceptions require complex joins
    // In a real implementation, this would query the three_way_match_results table
    
    return {
      summary: {
        total_exceptions: 23,
        unresolved_exceptions: 15,
        total_variance_amount: 12500.75,
        average_variance_percentage: 3.2
      },
      variance_analysis: {
        total_variances: 23,
        quantity_variances: 8,
        price_variances: 12,
        amount_variance: 12500.75,
        percentage_variance: 3.2
      },
      exceptions: [], // Would be populated from actual data
      trends: {
        daily_exceptions: [],
        exception_types: [
          { type: "Price Variance", count: 12, percentage: 52.2 },
          { type: "Quantity Variance", count: 8, percentage: 34.8 },
          { type: "Missing PO", count: 3, percentage: 13.0 }
        ]
      },
      generated_at: new Date().toISOString()
    };

  } catch (error) {
    console.error("Error in getThreeWayMatchExceptions:", error);
    throw new Error("Failed to fetch three-way match exceptions report");
  }
}

/**
 * Get supplier payment history
 */
export async function getSupplierPaymentHistory(
  supplierId: string,
  filters: SupplierPaymentHistoryFilters = {}
): Promise<SupplierPaymentHistoryData> {
  const supabase = await createClient();
  
  try {
    // Get supplier info
    const { data: supplier, error: supplierError } = await supabase
      .from("suppliers")
      .select("*")
      .eq("id", supplierId)
      .single();

    if (supplierError) {
      throw supplierError;
    }

    // For now, return mock data as payment history requires payment records
    return {
      supplier,
      stats: {
        total_payments: 45,
        total_amount: 125000.00,
        average_payment_amount: 2777.78,
        average_payment_days: 28.5,
        early_payment_discount_total: 1250.00,
        on_time_payment_percentage: 87.5,
        preferred_payment_method: "BANK_TRANSFER"
      },
      payments: [], // Would be populated from actual payment data
      trends: {
        monthly_payments: [],
        payment_timing: { on_time: 85, early: 10, late: 5 }
      },
      generated_at: new Date().toISOString()
    };

  } catch (error) {
    console.error("Error in getSupplierPaymentHistory:", error);
    throw new Error("Failed to fetch supplier payment history");
  }
}

/**
 * Get vendor performance metrics
 */
export async function getVendorPerformanceMetrics(
  filters: VendorPerformanceFilters = {}
): Promise<VendorPerformanceData> {
  const supabase = await createClient();
  
  try {
    // For now, return mock data as vendor performance requires complex calculations
    return {
      summary: {
        total_suppliers: 142,
        average_performance_score: 78.5,
        top_performers_count: 23,
        at_risk_suppliers_count: 8
      },
      metrics: [], // Would be populated with actual vendor metrics
      benchmarks: {
        payment_days_benchmark: 30,
        accuracy_benchmark: 95,
        dispute_rate_benchmark: 2
      },
      trends: {
        performance_over_time: [],
        top_risk_factors: [
          { factor: "Late Payments", count: 15 },
          { factor: "Invoice Accuracy", count: 12 },
          { factor: "Delivery Issues", count: 8 }
        ]
      },
      generated_at: new Date().toISOString()
    };

  } catch (error) {
    console.error("Error in getVendorPerformanceMetrics:", error);
    throw new Error("Failed to fetch vendor performance metrics");
  }
}

/**
 * Get analytics dashboard data
 */
export async function getAnalyticsDashboard(): Promise<AnalyticsDashboardData> {
  const supabase = await createClient();
  
  try {
    // For now, return mock data as analytics dashboard requires complex aggregations
    return {
      overview: {
        total_invoice_value: 2400000,
        total_invoice_count: 1234,
        average_processing_time: 3.2,
        approval_rate: 94.5,
        payment_completion_rate: 89.2
      },
      trends: {
        monthly_invoice_volume: [],
        payment_trends: [],
        approval_trends: []
      },
      top_suppliers: {
        by_volume: [],
        by_count: [],
        by_performance: []
      },
      exceptions_summary: {
        three_way_match_exceptions: 23,
        approval_bottlenecks: 8,
        payment_delays: 15,
        duplicate_invoices: 3
      },
      financial_insights: {
        potential_early_payment_savings: 25000,
        cash_flow_optimization_opportunities: 150000,
        supplier_concentration_risk: 15.2,
        compliance_score: 92.5
      },
      generated_at: new Date().toISOString()
    };

  } catch (error) {
    console.error("Error in getAnalyticsDashboard:", error);
    throw new Error("Failed to fetch analytics dashboard data");
  }
}

/**
 * Export report data in specified format
 */
export async function exportReport(
  reportType: string,
  options: ExportOptions
): Promise<ExportResult> {
  try {
    // Mock export functionality - in real implementation, this would generate files
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing time
    
    const fileName = `${reportType}_${new Date().toISOString().split('T')[0]}.${options.format}`;
    
    return {
      success: true,
      file_url: `/downloads/${fileName}`,
      file_name: fileName
    };

  } catch (error) {
    console.error("Error exporting report:", error);
    return {
      success: false,
      error_message: "Failed to export report"
    };
  }
}