"use client";

import { createClient } from "@/utils/supabase/Client";
import type {
  PaymentBatch,
  PaymentBatchFilters,
  PaymentSummary,
  CreatePaymentBatchInput,
  ProcessPaymentBatchInput,
  AuthorizePaymentBatchInput,
  GeneratePaymentFileInput,
  PaymentBatchResponse,
  PaymentFileResponse,
  PaymentValidationResult,
  BankAccount,
  PaymentApprovalLimit,
  CurrencyRate,
  PaymentFileTemplate,
  BankTransactionStatus,
  PaymentInvoiceSelection,
} from "@/types/purchase-invoices/payment-processing.types";
import type { PurchaseInvoice } from "@/types/purchase-invoices/purchase-invoices.types";

/**
 * Get payment-ready invoices (approved invoices pending payment)
 */
export async function getPaymentReadyInvoices(filters: {
  supplier_id?: string;
  entity_id?: string;
  currency_code?: string;
  due_date_from?: string;
  due_date_to?: string;
  min_amount?: number;
  max_amount?: number;
  priority?: string;
  search?: string;
  page?: number;
  limit?: number;
}) {
  const supabase = await createClient();
  
  try {
    let query = supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          email,
          phone,
          payment_terms,
          preferred_payment_method,
          bank_details,
          status
        ),
        entity:finance.entities(
          id,
          name,
          code,
          currency_code,
          country_code
        )
      `)
      .eq("status", "APPROVED")
      .gt("amount_due", 0); // Use amount_due instead of payment_status

    // Apply filters
    if (filters.supplier_id) {
      query = query.eq("supplier_id", filters.supplier_id);
    }
    
    if (filters.entity_id) {
      query = query.eq("entity_id", filters.entity_id);
    }
    
    if (filters.currency_code) {
      query = query.eq("currency_code", filters.currency_code);
    }
    
    if (filters.due_date_from) {
      query = query.gte("due_date", filters.due_date_from);
    }
    
    if (filters.due_date_to) {
      query = query.lte("due_date", filters.due_date_to);
    }
    
    if (filters.min_amount) {
      query = query.gte("total_amount", filters.min_amount);
    }
    
    if (filters.max_amount) {
      query = query.lte("total_amount", filters.max_amount);
    }
    
    if (filters.search) {
      query = query.or(`invoice_number.ilike.%${filters.search}%,supplier_reference.ilike.%${filters.search}%`);
    }

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 50;
    const offset = (page - 1) * limit;
    
    query = query.range(offset, offset + limit - 1);
    
    const { data, error, count } = await query;
    
    if (error) {
      console.error("Error fetching payment-ready invoices:", error);
      throw new Error(`Failed to fetch payment-ready invoices: ${error.message}`);
    }
    
    // Calculate computed fields
    const invoicesWithComputedFields = (data || []).map((invoice: any) => ({
      ...invoice,
      outstanding_amount: invoice.amount_due || 0,
      days_overdue: invoice.due_date ? Math.max(0, Math.floor((Date.now() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24))) : 0,
      early_payment_discount: calculateEarlyPaymentDiscount(invoice),
    }));

    return {
      data: invoicesWithComputedFields,
      count: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
  } catch (error) {
    console.error("Error in getPaymentReadyInvoices:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to load payment-ready invoices. Please try again or contact support.");
  }
}

/**
 * Get payment batches with filters
 */
export async function getPaymentBatches(filters: PaymentBatchFilters) {
  const supabase = await createClient();
  
  try {
    let query = supabase
      .schema("procurement")
      .from("payment_batches")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name
        ),
        entity:finance.entities(
          id,
          name,
          code
        ),
        bank_account:bank_accounts(
          id,
          account_name,
          account_number,
          bank_name,
          currency_code
        ),
        payment_invoice_selections(
          id,
          payment_amount,
          early_discount_amount,
          currency_rate,
          selected,
          priority,
          invoice:purchase_invoices(
            id,
            invoice_number,
            total_amount,
            currency_code,
            supplier_reference
          )
        )
      `);

  // Apply filters
  if (filters.status?.length) {
    query = query.in("status", filters.status);
  }
  
  if (filters.payment_method?.length) {
    query = query.in("payment_method", filters.payment_method);
  }
  
  if (filters.supplier_id) {
    query = query.eq("supplier_id", filters.supplier_id);
  }
  
  if (filters.entity_id) {
    query = query.eq("entity_id", filters.entity_id);
  }
  
  if (filters.currency_code) {
    query = query.eq("currency_code", filters.currency_code);
  }
  
  if (filters.bank_account_id) {
    query = query.eq("bank_account_id", filters.bank_account_id);
  }
  
  if (filters.date_from) {
    query = query.gte("payment_date", filters.date_from);
  }
  
  if (filters.date_to) {
    query = query.lte("payment_date", filters.date_to);
  }
  
  if (filters.search) {
    query = query.or(`batch_name.ilike.%${filters.search}%,batch_number.ilike.%${filters.search}%`);
  }

  // Sorting
  const sortBy = filters.sort_by || "created_at";
  const sortOrder = filters.sort_order || "desc";
  query = query.order(sortBy, { ascending: sortOrder === "asc" });

  // Pagination
  if (filters.page && filters.limit) {
    const offset = (filters.page - 1) * filters.limit;
    query = query.range(offset, offset + filters.limit - 1);
  }
  
    const { data, error, count } = await query;
    
    if (error) {
      console.error("Error fetching payment batches:", error);
      throw new Error(`Failed to fetch payment batches: ${error.message}`);
    }
    
    return {
      data: data || [],
      count: count || 0,
      page: filters.page || 1,
      limit: filters.limit || 50,
      total_pages: Math.ceil((count || 0) / (filters.limit || 50)),
    };
  } catch (error) {
    console.error("Error in getPaymentBatches:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to load payment batches. Please try again or contact support.");
  }
}

/**
 * Create a new payment batch
 */
export async function createPaymentBatch(input: CreatePaymentBatchInput): Promise<PaymentBatchResponse> {
  const supabase = await createClient();
  
  try {
    // Get current authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required to create payment batch");
    }

    // Start transaction
    const { data: batch, error: batchError } = await supabase
      .schema("procurement")
      .from("payment_batches")
      .insert({
        batch_name: input.batch_name,
        batch_number: await generateBatchNumber(input.currency_code),
        supplier_id: input.supplier_id,
        entity_id: input.entity_id,
        currency_code: input.currency_code,
        payment_method: input.payment_method,
        payment_date: input.payment_date,
        value_date: input.value_date,
        bank_account_id: input.bank_account_id,
        priority: input.priority,
        reference_number: input.reference_number,
        notes: input.notes,
        total_amount: input.invoice_selections.reduce((sum, sel) => sum + sel.payment_amount, 0),
        invoice_count: input.invoice_selections.length,
        status: "DRAFT",
        requires_authorization: await checkAuthorizationRequired(input),
        authorization_level: await getRequiredAuthorizationLevel(input),
        created_by: user.id,
      })
      .select()
      .single();

    if (batchError) {
      throw batchError;
    }

    // Create invoice selections
    const invoiceSelections = input.invoice_selections.map(selection => ({
      batch_id: batch.id,
      invoice_id: selection.invoice_id,
      payment_amount: selection.payment_amount,
      original_amount: selection.payment_amount, // Would be fetched from invoice
      early_discount_amount: selection.apply_early_discount ? (selection.payment_amount * 0.02) : 0, // Example 2% early discount
      withholding_tax_amount: selection.withholding_tax_amount || 0,
      currency_rate: selection.currency_rate || 1.0,
      currency_rate_date: new Date().toISOString().split('T')[0],
      currency_rate_source: "MANUAL",
      selected: true,
      priority: "MEDIUM",
      reference_number: selection.reference_number,
      notes: selection.notes,
    }));

    const { error: selectionsError } = await supabase
      .schema("procurement")
      .from("payment_invoice_selections")
      .insert(invoiceSelections);

    if (selectionsError) {
      throw selectionsError;
    }

    // Validate the batch
    const validationResult = await validatePaymentBatch(batch.id);

    return {
      batch,
      validation_errors: validationResult.errors.filter(e => e.severity === "ERROR").map(e => e.error_message),
      warnings: validationResult.warnings.map(w => w.warning_message),
    };

  } catch (error) {
    console.error("Error creating payment batch:", error);
    throw error;
  }
}

/**
 * Authorize payment batch
 */
export async function authorizePaymentBatch(input: AuthorizePaymentBatchInput): Promise<PaymentBatch> {
  const supabase = await createClient();
  
  const { data: batch, error } = await supabase
    .from("payment_batches")
    .update({
      status: input.action === "APPROVE" ? "APPROVED" : input.action === "REJECT" ? "CANCELLED" : "DRAFT",
      authorized_by: input.action === "APPROVE" ? "current-user" : null,
      authorized_at: input.action === "APPROVE" ? new Date().toISOString() : null,
      updated_at: new Date().toISOString(),
      updated_by: "current-user",
    })
    .eq("id", input.batch_id)
    .select()
    .single();

  if (error) {
    console.error("Error authorizing payment batch:", error);
    throw error;
  }

  // Log authorization history
  await supabase
    .from("payment_authorization_history")
    .insert({
      batch_id: input.batch_id,
      user_id: "current-user",
      user_name: "Current User", // Would come from auth context
      action: input.action === "APPROVE" ? "APPROVED" : input.action === "REJECT" ? "REJECTED" : "RETURNED",
      authorization_level: input.authorization_level,
      comments: input.comments,
      authorization_date: new Date().toISOString(),
    });

  return batch;
}

/**
 * Process payment batch
 */
export async function processPaymentBatch(input: ProcessPaymentBatchInput): Promise<PaymentBatch> {
  const supabase = await createClient();
  
  // Update batch status to processing
  const { data: batch, error } = await supabase
    .from("payment_batches")
    .update({
      status: "PROCESSING",
      processed_by: "current-user",
      processed_at: new Date().toISOString(),
      payment_date: input.payment_date,
      value_date: input.value_date,
      bank_account_id: input.bank_account_id,
    })
    .eq("id", input.batch_id)
    .select()
    .single();

  if (error) {
    console.error("Error processing payment batch:", error);
    throw error;
  }

  // Log processing step
  await supabase
    .from("payment_processing_log")
    .insert({
      batch_id: input.batch_id,
      step: "BANK_SUBMISSION",
      status: "IN_PROGRESS",
      message: "Payment batch submitted to bank for processing",
      created_at: new Date().toISOString(),
    });

  // Here you would integrate with actual bank APIs
  // For now, we'll simulate processing
  setTimeout(async () => {
    await supabase
      .from("payment_batches")
      .update({
        status: "COMPLETED",
      })
      .eq("id", input.batch_id);

    await supabase
      .from("payment_processing_log")
      .insert({
        batch_id: input.batch_id,
        step: "COMPLETION",
        status: "COMPLETED",
        message: "Payment batch processed successfully",
        created_at: new Date().toISOString(),
      });
  }, 5000); // Simulate 5 second processing time

  return batch;
}

/**
 * Generate payment file
 */
export async function generatePaymentFile(input: GeneratePaymentFileInput): Promise<PaymentFileResponse> {
  const supabase = await createClient();
  
  // Get payment batches data
  const { data: batches, error: batchesError } = await supabase
    .from("payment_batches")
    .select(`
      *,
      payment_invoice_selections(
        *,
        invoice:purchase_invoices(
          *,
          supplier:suppliers(*)
        )
      )
    `)
    .in("id", input.batch_ids);

  if (batchesError) {
    throw batchesError;
  }

  // Get file template
  const { data: template, error: templateError } = await supabase
    .from("payment_file_templates")
    .select("*")
    .eq("id", input.template_id)
    .single();

  if (templateError) {
    throw templateError;
  }

  // Generate file content based on template and format
  const fileContent = generateFileContent(batches || [], template, input);
  const fileName = input.file_name || `payment_file_${Date.now()}.${template.file_extension}`;
  
  // In a real implementation, you would upload this to storage
  const filePath = `/payment-files/${fileName}`;
  const fileSize = Buffer.byteLength(fileContent, 'utf8');
  const fileHash = await generateFileHash(fileContent);

  // Save file record
  const { data: paymentFile, error: fileError } = await supabase
    .from("payment_files")
    .insert({
      batch_id: input.batch_ids[0], // Primary batch
      file_name: fileName,
      file_format: input.file_format,
      file_size: fileSize,
      file_path: filePath,
      file_hash: fileHash,
      record_count: batches?.reduce((sum, batch) => sum + (batch.payment_invoice_selections?.length || 0), 0) || 0,
      total_amount: batches?.reduce((sum, batch) => sum + batch.total_amount, 0) || 0,
      currency_code: batches?.[0]?.currency_code || "USD",
      status: "GENERATED",
      generated_by: "current-user",
      generated_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (fileError) {
    throw fileError;
  }

  return {
    file: paymentFile,
    download_url: `/api/payment-files/${paymentFile.id}/download`,
    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
  };
}

/**
 * Get payment summary statistics
 */
export async function getPaymentSummary(): Promise<PaymentSummary> {
  const supabase = await createClient();
  
  const { data: batches, error } = await supabase
    .from("payment_batches")
    .select("*");

  if (error) {
    throw error;
  }

  const now = new Date();
  const today = now.toISOString().split('T')[0];
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];

  const summary: PaymentSummary = {
    total_batches: batches?.length || 0,
    total_amount: batches?.reduce((sum, batch) => sum + batch.total_amount, 0) || 0,
    total_invoices: batches?.reduce((sum, batch) => sum + batch.invoice_count, 0) || 0,
    pending_authorization: batches?.filter(b => b.status === "PENDING_APPROVAL").length || 0,
    processing_count: batches?.filter(b => b.status === "PROCESSING").length || 0,
    completed_count: batches?.filter(b => b.status === "COMPLETED").length || 0,
    failed_count: batches?.filter(b => b.status === "FAILED").length || 0,
    currency_breakdown: {},
    method_breakdown: {},
    daily_processed_amount: batches?.filter(b => b.processed_at?.startsWith(today)).reduce((sum, batch) => sum + batch.total_amount, 0) || 0,
    monthly_processed_amount: batches?.filter(b => b.processed_at && b.processed_at >= firstDayOfMonth).reduce((sum, batch) => sum + batch.total_amount, 0) || 0,
  };

  // Calculate breakdowns
  batches?.forEach(batch => {
    // Currency breakdown
    summary.currency_breakdown[batch.currency_code] = (summary.currency_breakdown[batch.currency_code] || 0) + batch.total_amount;
    
    // Method breakdown
    summary.method_breakdown[batch.payment_method] = (summary.method_breakdown[batch.payment_method] || 0) + batch.total_amount;
  });

  return summary;
}

/**
 * Get bank accounts
 */
export async function getBankAccounts(entityId?: string): Promise<BankAccount[]> {
  const supabase = await createClient();
  
  let query = supabase
    .from("bank_accounts")
    .select("*")
    .eq("status", "ACTIVE");
    
  if (entityId) {
    query = query.eq("entity_id", entityId);
  }
  
  const { data, error } = await query.order("is_default", { ascending: false });
  
  if (error) {
    throw error;
  }
  
  return data || [];
}

/**
 * Get payment approval limits for user
 */
export async function getPaymentApprovalLimits(userId: string): Promise<PaymentApprovalLimit[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from("payment_approval_limits")
    .select("*")
    .eq("user_id", userId)
    .eq("is_active", true)
    .gte("effective_to", new Date().toISOString());
  
  if (error) {
    throw error;
  }
  
  return data || [];
}

/**
 * Get current currency rates
 */
export async function getCurrencyRates(baseCurrency: string = "USD"): Promise<CurrencyRate[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from("currency_rates")
    .select("*")
    .eq("from_currency", baseCurrency)
    .eq("is_active", true)
    .lte("valid_from", new Date().toISOString())
    .gte("valid_to", new Date().toISOString());
  
  if (error) {
    throw error;
  }
  
  return data || [];
}

// Helper functions
async function generateBatchNumber(currencyCode: string): Promise<string> {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  const timestamp = Date.now().toString().slice(-6);
  
  return `PAY-${currencyCode}-${year}${month}${day}-${timestamp}`;
}

async function checkAuthorizationRequired(input: CreatePaymentBatchInput): Promise<boolean> {
  const totalAmount = input.invoice_selections.reduce((sum, sel) => sum + sel.payment_amount, 0);
  
  // Simple rule: amounts over $10,000 require authorization
  return totalAmount > 10000;
}

async function getRequiredAuthorizationLevel(input: CreatePaymentBatchInput): Promise<"SINGLE" | "DUAL" | "MULTI_LEVEL"> {
  const totalAmount = input.invoice_selections.reduce((sum, sel) => sum + sel.payment_amount, 0);
  
  // Simple rules based on amount
  if (totalAmount > 100000) return "MULTI_LEVEL";
  if (totalAmount > 50000) return "DUAL";
  return "SINGLE";
}

async function validatePaymentBatch(batchId: string): Promise<PaymentValidationResult> {
  // Mock validation - in real implementation, this would check various business rules
  return {
    batch_id: batchId,
    is_valid: true,
    errors: [],
    warnings: [],
    validation_date: new Date().toISOString(),
  };
}

function calculateEarlyPaymentDiscount(invoice: any): number {
  // Mock early payment discount calculation
  // In real implementation, this would check supplier terms and current date vs due date
  const daysUntilDue = invoice.due_date ? Math.floor((new Date(invoice.due_date).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0;
  
  if (daysUntilDue > 10) {
    return invoice.total_amount * 0.02; // 2% early payment discount
  }
  
  return 0;
}

function generateFileContent(batches: any[], template: any, input: GeneratePaymentFileInput): string {
  // Mock file generation - in real implementation, this would use the template
  // to generate properly formatted payment files (CSV, XML, MT940, etc.)
  
  let content = "";
  
  if (input.include_header) {
    content += "Header,Batch,Invoice,Amount,Currency,Supplier\n";
  }
  
  batches.forEach(batch => {
    batch.payment_invoice_selections?.forEach((selection: any) => {
      content += `DATA,${batch.batch_number},${selection.invoice.invoice_number},${selection.payment_amount},${batch.currency_code},${selection.invoice.supplier.name}\n`;
    });
  });
  
  if (input.include_footer) {
    const totalAmount = batches.reduce((sum, batch) => sum + batch.total_amount, 0);
    const totalRecords = batches.reduce((sum, batch) => sum + (batch.payment_invoice_selections?.length || 0), 0);
    content += `FOOTER,${totalRecords},${totalAmount}\n`;
  }
  
  return content;
}

async function generateFileHash(content: string): Promise<string> {
  // Mock hash generation - in real implementation, use crypto
  return Buffer.from(content).toString('base64').substring(0, 32);
}