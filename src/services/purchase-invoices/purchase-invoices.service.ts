"use client";

import { createClient } from "@/utils/supabase/Client";
import type {
  PurchaseInvoice,
  PurchaseInvoiceFilters,
  PurchaseInvoiceSummary,
  CreatePurchaseInvoiceInput,
  ProcessPaymentInput,
  ThreeWayMatchResult,
  SupplierStatement,
  PurchaseInvoiceExportData,
  ToleranceSettings,
  VendorPerformanceMetrics,
} from "@/types/purchase-invoices/purchase-invoices.types";

export async function getPurchaseInvoices(filters: PurchaseInvoiceFilters) {
  const supabase = await createClient();
  
  try {
    // Start with a simpler query to handle table schema issues
    let query = supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          email,
          phone,
          payment_terms,
          status
        )
      `);

  // Apply filters
  if (filters.search) {
    query = query.or(
      `invoice_number.ilike.%${filters.search}%,supplier.name.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%,supplier_reference.ilike.%${filters.search}%`
    );
  }

  if (filters.status && filters.status.length > 0) {
    query = query.in("status", filters.status);
  }

  // Note: payment_status filter not supported - using status instead
  // if (filters.payment_status && filters.payment_status.length > 0) {
  //   query = query.in("payment_status", filters.payment_status);
  // }

  if (filters.supplier_id) {
    query = query.eq("supplier_id", filters.supplier_id);
  }

  if (filters.entity_id) {
    query = query.eq("entity_id", filters.entity_id);
  }

  if (filters.purchase_order_id) {
    query = query.eq("purchase_order_id", filters.purchase_order_id);
  }

  if (filters.date_from) {
    query = query.gte("invoice_date", filters.date_from);
  }

  if (filters.date_to) {
    query = query.lte("invoice_date", filters.date_to);
  }

  if (filters.due_date_from) {
    query = query.gte("due_date", filters.due_date_from);
  }

  if (filters.due_date_to) {
    query = query.lte("due_date", filters.due_date_to);
  }

  if (filters.amount_min) {
    query = query.gte("total_amount", filters.amount_min);
  }

  if (filters.amount_max) {
    query = query.lte("total_amount", filters.amount_max);
  }

  if (filters.is_overdue) {
    const today = new Date().toISOString().split("T")[0];
    query = query.lt("due_date", today).neq("status", "PAID");
  }

  // Sorting
  const sortColumn = filters.sort_by || "invoice_date";
  const sortOrder = filters.sort_order || "desc";
  query = query.order(sortColumn, { ascending: sortOrder === "asc" });

  // Pagination
  const page = filters.page || 1;
  const limit = filters.limit || 10;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error("Error fetching purchase invoices:", error);
      
      // Handle specific errors that might occur
      if (error.message?.includes("schema") || error.message?.includes("table")) {
        throw new Error("Database schema issue detected. Some tables may not exist or need configuration.");
      }
      
      if (error.message?.includes("permission") || error.message?.includes("access")) {
        throw new Error("Access denied. Please check your permissions for purchase invoices.");
      }
      
      // Generic database error
      throw new Error(`Database error: ${error.message || "Unknown error occurred"}`);
    }

    // Calculate computed fields
    const invoices = (data || []).map((invoice) => {
      // Use the database computed amount_due field
      const outstanding = invoice.amount_due || 0;
      
      let daysOverdue = 0;
      if (invoice.due_date && outstanding > 0) {
        const dueDate = new Date(invoice.due_date);
        const today = new Date();
        const diffTime = today.getTime() - dueDate.getTime();
        daysOverdue = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
      }

      return {
        ...invoice,
        outstanding_amount: outstanding,
        days_overdue: daysOverdue,
      } as PurchaseInvoice;
    });

    return {
      data: invoices,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    };
    
  } catch (error) {
    console.error("Error in getPurchaseInvoices:", error);
    
    // If it's already an Error object, re-throw it
    if (error instanceof Error) {
      throw error;
    }
    
    // Otherwise create a new error
    throw new Error("Failed to load purchase invoices. Please try again or contact support.");
  }
}

export async function getPurchaseInvoiceById(id: string) {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          email,
          phone,
          payment_terms,
          status
        ),
        items:purchase_invoice_items(
          id,
          purchase_order_item_id,
          line_number,
          description,
          quantity,
          unit_price,
          tax_amount,
          tax_rate,
          line_total,
          created_at
        )
      `)
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching purchase invoice:", error);
      
      if (error.code === "PGRST116") {
        throw new Error("Purchase invoice not found");
      }
      
      if (error.message?.includes("schema") || error.message?.includes("table")) {
        throw new Error("Database schema issue detected. Some tables may not exist or need configuration.");
      }
      
      throw new Error(`Database error: ${error.message || "Unknown error occurred"}`);
    }

    if (!data) {
      throw new Error("Purchase invoice not found");
    }

    // Use the database computed amount_due field
    const outstanding = data.amount_due || 0;

    // Transform the data to match frontend expectations
    const transformedData = {
      ...data,
      outstanding_amount: outstanding,
      // Transform line items to match frontend interface
      items: data.items?.map((item: any) => ({
        ...item,
        po_item_id: item.purchase_order_item_id, // Map database column to frontend field
        tax_percentage: item.tax_rate, // Map database tax_rate to frontend tax_percentage
        discount_amount: null, // Default value for missing column
        discount_percentage: null, // Default value for missing column
        item_code: null, // Default value for missing column
        cost_center: null, // Default value for missing column
        gl_account_code: null, // Default value for missing column
        updated_at: null, // Default value for missing column
      })) || [],
    };

    return transformedData as PurchaseInvoice;
    
  } catch (error) {
    console.error("Error in getPurchaseInvoiceById:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to load purchase invoice. Please try again or contact support.");
  }
}

export async function getPurchaseInvoicesSummary(): Promise<PurchaseInvoiceSummary> {
  const supabase = await createClient();
  
  try {
    // Get total count and outstanding amount
    const { data: invoicesData, error: invoicesError } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select("id, total_amount, amount_paid, amount_due, due_date, status, created_at")
      .not("status", "eq", "CANCELLED");

    if (invoicesError) {
      console.error("Error fetching invoices summary:", invoicesError);
      
      if (invoicesError.message?.includes("schema") || invoicesError.message?.includes("table")) {
        throw new Error("Database schema issue detected. Purchase invoices table may not exist or need configuration.");
      }
      
      throw new Error(`Database error: ${invoicesError.message || "Unknown error occurred"}`);
    }

  const total_count = invoicesData?.length || 0;
  
  // Calculate outstanding amount (unpaid invoices)
  const unpaidInvoices = invoicesData?.filter((inv: any) => 
    inv.status !== "PAID" && inv.status !== "CANCELLED"
  ) || [];
  
  const outstanding_amount = unpaidInvoices.reduce((sum: number, inv: any) => sum + inv.amount_due, 0);

  // Count overdue invoices
  const today = new Date().toISOString().split("T")[0];
  const overdue_count = unpaidInvoices.filter((inv: any) => 
    inv.due_date && inv.due_date < today
  ).length;

  // Count pending approval
  const pending_approval_count = invoicesData?.filter((inv: any) => 
    inv.status === "PENDING_APPROVAL"
  ).length || 0;

  // This month amount
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  const this_month_amount = invoicesData?.filter((inv: any) => 
    new Date(inv.created_at) >= startOfMonth
  ).reduce((sum: number, inv: any) => sum + inv.total_amount, 0) || 0;

    return {
      total_count,
      outstanding_amount,
      overdue_count,
      pending_approval_count,
      this_month_amount,
      average_processing_days: 5.2, // This would need to be calculated based on actual data
    };
    
  } catch (error) {
    console.error("Error in getPurchaseInvoicesSummary:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    // Return default summary if all else fails
    return {
      total_count: 0,
      outstanding_amount: 0,
      overdue_count: 0,
      pending_approval_count: 0,
      this_month_amount: 0,
      average_processing_days: 0,
    };
  }
}

export async function createPurchaseInvoice(input: CreatePurchaseInvoiceInput) {
  const supabase = await createClient();
  
  // Get current authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required to create purchase invoice");
  }

  // Calculate totals from items first
  let subtotal = 0;
  let totalTax = 0;
  
  input.items.forEach((item) => {
    const lineSubtotal = item.quantity * item.unit_price;
    
    // Apply discount if any
    let discountedAmount = lineSubtotal;
    if (item.discount_percentage && item.discount_percentage > 0) {
      discountedAmount = lineSubtotal * (1 - item.discount_percentage / 100);
    } else if (item.discount_amount && item.discount_amount > 0) {
      discountedAmount = Math.max(0, lineSubtotal - item.discount_amount);
    }
    
    const taxAmount = discountedAmount * (item.tax_percentage / 100);
    
    subtotal += discountedAmount;
    totalTax += taxAmount;
  });
  
  const totalAmount = subtotal + totalTax;

  const { data: invoice, error: invoiceError } = await supabase
    .schema("procurement")
    .from("purchase_invoices")
    .insert({
      supplier_id: input.supplier_id,
      entity_id: input.entity_id,
      purchase_order_id: input.purchase_order_id || null,
      invoice_number: input.invoice_number,
      our_reference: input.supplier_reference || null, // Map to correct column name
      invoice_date: input.invoice_date,
      due_date: input.due_date || null,
      currency_code: input.currency_code,
      // reference_number: input.reference_number || null, // Removed - column doesn't exist in database
      // notes: input.notes || null, // Removed - column doesn't exist in database
      status: "RECEIVED",
      subtotal: subtotal, // Include calculated subtotal
      total_amount: totalAmount, // Include calculated total
      tax_amount: totalTax, // Include calculated tax
      created_at: new Date().toISOString(),
      created_by: user.id, // Use actual authenticated user ID
    })
    .select()
    .single();

  if (invoiceError) {
    console.error("Error creating purchase invoice:", invoiceError);
    throw invoiceError;
  }

  // Insert items
  const itemsToInsert = input.items.map((item) => {
    const lineSubtotal = item.quantity * item.unit_price;
    
    // Apply discount if any
    let discountedAmount = lineSubtotal;
    if (item.discount_percentage && item.discount_percentage > 0) {
      discountedAmount = lineSubtotal * (1 - item.discount_percentage / 100);
    } else if (item.discount_amount && item.discount_amount > 0) {
      discountedAmount = Math.max(0, lineSubtotal - item.discount_amount);
    }
    
    const taxAmount = discountedAmount * (item.tax_percentage / 100);
    const lineTotal = discountedAmount + taxAmount;
    
    return {
      purchase_invoice_id: invoice.id,
      purchase_order_item_id: item.po_item_id || null, // Correct column name in database
      line_number: item.line_number,
      description: item.description,
      quantity: item.quantity,
      unit_price: item.unit_price,
      tax_amount: taxAmount,
      tax_rate: item.tax_percentage, // Database uses tax_rate, not tax_percentage
      line_total: lineTotal,
      gl_account_code: item.gl_account_code || null,
      budget_item_id: null, // Database has this column
      created_at: new Date().toISOString(),
    };
  });

  const { data: items, error: itemsError } = await supabase
    .schema("procurement")
    .from("purchase_invoice_items")
    .insert(itemsToInsert)
    .select();

  if (itemsError) {
    console.error("Error creating purchase invoice items:", itemsError);
    // Rollback by deleting the invoice
    await supabase.schema("procurement").from("purchase_invoices").delete().eq("id", invoice.id);
    throw itemsError;
  }

  // Perform three-way match if requested
  if (input.perform_three_way_match && input.purchase_order_id) {
    try {
      await performThreeWayMatch(invoice.id);
    } catch (matchError) {
      console.error("Error performing three-way match:", matchError);
      // Don't fail the invoice creation if three-way match fails
    }
  }

  // Return the created invoice with all fields
  const { data: createdInvoice, error: fetchError } = await supabase
    .schema("procurement")
    .from("purchase_invoices")
    .select(`
      *,
      supplier:suppliers(
        id,
        supplier_code,
        name,
        display_name,
        email,
        phone,
        payment_terms,
        status
      )
    `)
    .eq("id", invoice.id)
    .single();

  if (fetchError) {
    console.error("Error fetching created invoice:", fetchError);
    return invoice; // Return the basic invoice if fetch fails
  }

  return createdInvoice;
}

export async function updatePurchaseInvoice(id: string, updates: Partial<CreatePurchaseInvoiceInput>) {
  const supabase = await createClient();
  
  // Get current authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required to update purchase invoice");
  }

  // Build update object with only provided fields
  const updateData: any = {
    updated_at: new Date().toISOString(),
    updated_by: user.id, // Track who updated the invoice
  };

  // Map fields to correct column names
  if (updates.invoice_date !== undefined) updateData.invoice_date = updates.invoice_date;
  if (updates.due_date !== undefined) updateData.due_date = updates.due_date || null;
  // if (updates.reference_number !== undefined) updateData.reference_number = updates.reference_number || null; // Removed - column doesn't exist in database
  if (updates.supplier_reference !== undefined) updateData.our_reference = updates.supplier_reference || null; // Map to correct column
  // if (updates.notes !== undefined) updateData.notes = updates.notes || null; // Removed - column doesn't exist in database
  if (updates.currency_code !== undefined) updateData.currency_code = updates.currency_code;

  // If items are provided, recalculate totals
  if (updates.items && updates.items.length > 0) {
    let subtotal = 0;
    let totalTax = 0;
    
    updates.items.forEach((item) => {
      const lineSubtotal = item.quantity * item.unit_price;
      
      // Apply discount if any
      let discountedAmount = lineSubtotal;
      if (item.discount_percentage && item.discount_percentage > 0) {
        discountedAmount = lineSubtotal * (1 - item.discount_percentage / 100);
      } else if (item.discount_amount && item.discount_amount > 0) {
        discountedAmount = Math.max(0, lineSubtotal - item.discount_amount);
      }
      
      const taxAmount = discountedAmount * (item.tax_percentage / 100);
      
      subtotal += discountedAmount;
      totalTax += taxAmount;
    });
    
    updateData.subtotal = subtotal;
    updateData.tax_amount = totalTax;
    updateData.total_amount = subtotal + totalTax;
  }

  const { data, error } = await supabase
    .schema("procurement")
    .from("purchase_invoices")
    .update(updateData)
    .eq("id", id)
    .select(`
      *,
      supplier:suppliers(
        id,
        supplier_code,
        name,
        display_name,
        email,
        phone,
        payment_terms,
        status
      )
    `)
    .single();

  if (error) {
    console.error("Error updating purchase invoice:", error);
    throw error;
  }

  // If items were provided, update them too
  if (updates.items && updates.items.length > 0) {
    // Delete existing items
    await supabase
      .schema("procurement")
      .from("purchase_invoice_items")
      .delete()
      .eq("purchase_invoice_id", id);

    // Insert new items
    const itemsToInsert = updates.items.map((item) => {
      const lineSubtotal = item.quantity * item.unit_price;
      
      // Apply discount if any
      let discountedAmount = lineSubtotal;
      if (item.discount_percentage && item.discount_percentage > 0) {
        discountedAmount = lineSubtotal * (1 - item.discount_percentage / 100);
      } else if (item.discount_amount && item.discount_amount > 0) {
        discountedAmount = Math.max(0, lineSubtotal - item.discount_amount);
      }
      
      const taxAmount = discountedAmount * (item.tax_percentage / 100);
      const lineTotal = discountedAmount + taxAmount;
      
      return {
        purchase_invoice_id: id,
        purchase_order_item_id: item.po_item_id || null,
        line_number: item.line_number,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        discount_amount: item.discount_amount || 0,
        discount_percentage: item.discount_percentage || 0,
        tax_amount: taxAmount,
        tax_rate: item.tax_percentage, // Database uses tax_rate, not tax_percentage
        line_total: lineTotal,
        item_code: item.item_code || null,
        gl_account_code: item.gl_account_code || null,
        cost_center: item.cost_center || null,
        created_at: new Date().toISOString(),
      };
    });

    const { error: itemsError } = await supabase
      .schema("procurement")
      .from("purchase_invoice_items")
      .insert(itemsToInsert);

    if (itemsError) {
      console.error("Error updating purchase invoice items:", itemsError);
      // Don't throw here, the main invoice update was successful
    }
  }

  return data;
}

export async function performThreeWayMatch(invoiceId: string): Promise<ThreeWayMatchResult> {
  const supabase = await createClient();
  
  try {
    // First, get the invoice to understand which entity it belongs to
    const { data: invoice, error: invoiceError } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select("id, entity_id")
      .eq("id", invoiceId)
      .single();

    if (invoiceError || !invoice) {
      throw new Error(`Invoice not found: ${invoiceError?.message || 'Unknown error'}`);
    }

    // Get tolerance settings for the specific entity
    const toleranceSettings = await getToleranceSettings(invoice.entity_id);
    
    // Perform the three-way match with tolerance settings
    const { data, error } = await supabase
      .rpc("perform_three_way_match", {
        invoice_id: invoiceId,
        tolerance_settings: toleranceSettings,
      });

    if (error) {
      console.error("Error performing three-way match:", error);
      throw new Error(`Three-way match failed: ${error.message}`);
    }

    // Validate the result
    if (!data) {
      throw new Error("Three-way match returned no data");
    }

    return data;
  } catch (error) {
    console.error("Error in performThreeWayMatch:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to perform three-way match. Please try again or contact support.");
  }
}

export async function performAdvancedThreeWayMatch(
  invoiceId: string, 
  options: {
    autoApproveWithinTolerance?: boolean;
    requireApprovalAboveTolerance?: boolean;
    customToleranceSettings?: Partial<ToleranceSettings>;
  } = {}
): Promise<ThreeWayMatchResult> {
  const supabase = await createClient();
  
  try {
    // Get base tolerance settings and merge with custom ones
    const baseToleranceSettings = await getToleranceSettings();
    const toleranceSettings = {
      ...baseToleranceSettings,
      ...options.customToleranceSettings,
    };

    // Perform the three-way match
    const { data, error } = await supabase
      .rpc("perform_three_way_match", {
        invoice_id: invoiceId,
        tolerance_settings: toleranceSettings,
      });

    if (error) {
      console.error("Error performing advanced three-way match:", error);
      throw new Error(`Advanced three-way match failed: ${error.message}`);
    }

    if (!data) {
      throw new Error("Three-way match returned no data");
    }

    // Apply business rules based on match result
    await applyThreeWayMatchBusinessRules(invoiceId, data, options);

    return data;
  } catch (error) {
    console.error("Error in performAdvancedThreeWayMatch:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to perform advanced three-way match. Please try again or contact support.");
  }
}

async function applyThreeWayMatchBusinessRules(
  invoiceId: string,
  matchResult: ThreeWayMatchResult,
  options: {
    autoApproveWithinTolerance?: boolean;
    requireApprovalAboveTolerance?: boolean;
  }
) {
  const supabase = await createClient();
  
  try {
    // If variance is within tolerance and auto-approval is enabled
    if (!matchResult.tolerance_exceeded && options.autoApproveWithinTolerance) {
      // Auto-approve the invoice
      await supabase
        .schema("procurement")
        .from("purchase_invoices")
        .update({
          status: "APPROVED",
          approved_at: new Date().toISOString(),
          approved_by: null, // System approval
          updated_at: new Date().toISOString(),
        })
        .eq("id", invoiceId);

      // Log auto-approval
      await supabase
        .from("approval_history")
        .insert({
          entity_type: "purchase_invoice",
          entity_id: invoiceId,
          action: "APPROVED",
          approver_id: null, // System approval
          approval_date: new Date().toISOString(),
          comments: "Auto-approved: Three-way match within tolerance",
          approval_level: 0,
        });
    }
    
    // If variance exceeds tolerance and manual approval is required
    if (matchResult.tolerance_exceeded && options.requireApprovalAboveTolerance) {
      // Mark as requiring approval
      await supabase
        .schema("procurement")
        .from("purchase_invoices")
        .update({
          status: "PENDING_APPROVAL",
          updated_at: new Date().toISOString(),
        })
        .eq("id", invoiceId);

      // Create approval request if approval schema exists
      if (await schemaExists('approval')) {
        await supabase
          .from("approval_requests")
          .insert({
            request_type: "purchase_invoice_approval",
            entity_type: "purchase_invoice",
            entity_id: invoiceId,
            status: "pending",
            metadata: {
              reason: "Three-way match variance exceeds tolerance",
              total_variance: matchResult.total_variance,
              tolerance_exceeded: true,
            },
          });
      }
    }
  } catch (error) {
    console.error("Error applying three-way match business rules:", error);
    // Don't throw here as the match itself was successful
  }
}

async function schemaExists(schemaName: string): Promise<boolean> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .rpc('check_schema_exists', { schema_name: schemaName });
    
    return !error && data === true;
  } catch {
    return false;
  }
}

export async function processPayment(input: ProcessPaymentInput) {
  const supabase = await createClient();
  
  try {
    // Pre-payment validation
    await validatePaymentInput(input);

    // Calculate early payment discounts if applicable
    let enhancedInput = input;
    if (input.apply_early_payment_discount) {
      enhancedInput = await calculateEarlyPaymentDiscounts(input);
    }

    // Process the payment
    const { data, error } = await supabase
      .rpc("process_supplier_payment", {
        p_invoice_ids: enhancedInput.invoice_ids,
        p_payment_date: enhancedInput.payment_date,
        p_payment_method: enhancedInput.payment_method,
        p_reference_number: enhancedInput.reference_number,
        p_bank_details: enhancedInput.bank_details,
        p_notes: enhancedInput.notes,
        p_apply_early_payment_discount: enhancedInput.apply_early_payment_discount,
        p_batch_name: enhancedInput.batch_name,
        p_bank_account_id: enhancedInput.bank_account_id,
        p_value_date: enhancedInput.value_date,
        p_priority: enhancedInput.priority,
      });

    if (error) {
      console.error("Error processing payment:", error);
      throw new Error(`Payment processing failed: ${error.message}`);
    }

    if (!data || !data.success) {
      throw new Error(data?.error || "Payment processing failed without specific error");
    }

    // Post-payment processing
    await postPaymentProcessing(data);

    return data;
  } catch (error) {
    console.error("Error in processPayment:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to process payment. Please try again or contact support.");
  }
}

async function validatePaymentInput(input: ProcessPaymentInput): Promise<void> {
  const supabase = await createClient();

  // Validate invoice IDs exist and are eligible for payment
  const { data: invoices, error } = await supabase
    .schema("procurement")
    .from("purchase_invoices")
    .select("id, status, total_amount, amount_due, supplier_id")
    .in("id", input.invoice_ids);

  if (error) {
    throw new Error(`Failed to validate invoices: ${error.message}`);
  }

  if (!invoices || invoices.length !== input.invoice_ids.length) {
    throw new Error("One or more invoices not found");
  }

  // Check if invoices can be paid
  const invalidInvoices = invoices.filter(
    invoice => !["APPROVED", "PARTIALLY_PAID"].includes(invoice.status)
  );

  if (invalidInvoices.length > 0) {
    throw new Error(`Cannot pay invoices with status: ${invalidInvoices.map(i => i.status).join(", ")}`);
  }

  // Check for outstanding amounts
  const unpaidInvoices = invoices.filter(invoice => (invoice.amount_due || 0) <= 0);
  if (unpaidInvoices.length > 0) {
    throw new Error("One or more invoices have no outstanding amount to pay");
  }

  // Validate payment date
  const paymentDate = new Date(input.payment_date);
  const today = new Date();
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(today.getFullYear() - 1);

  if (paymentDate < oneYearAgo || paymentDate > today) {
    throw new Error("Payment date must be within the last year and not in the future");
  }

  // Validate payment method
  const validMethods = ["BANK_TRANSFER", "CHEQUE", "CASH", "ONLINE", "CREDIT_CARD"];
  if (!validMethods.includes(input.payment_method)) {
    throw new Error(`Invalid payment method: ${input.payment_method}`);
  }
}

async function calculateEarlyPaymentDiscounts(input: ProcessPaymentInput): Promise<ProcessPaymentInput> {
  const supabase = await createClient();

  try {
    const { data: invoices, error } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        id,
        total_amount,
        amount_due,
        due_date,
        invoice_date,
        supplier:suppliers(payment_terms, early_payment_discount_percentage, early_payment_discount_days)
      `)
      .in("id", input.invoice_ids);

    if (error || !invoices) {
      console.warn("Could not calculate early payment discounts:", error);
      return input;
    }

    const paymentDate = new Date(input.payment_date);
    let totalDiscountAmount = 0;

    for (const invoice of invoices) {
      const supplier = Array.isArray(invoice.supplier) ? invoice.supplier[0] : invoice.supplier;
      if (supplier?.early_payment_discount_percentage && supplier?.early_payment_discount_days) {
        const invoiceDate = new Date(invoice.invoice_date);
        const discountDeadline = new Date(invoiceDate);
        discountDeadline.setDate(invoiceDate.getDate() + supplier.early_payment_discount_days);

        // Check if payment is within early payment discount period
        if (paymentDate <= discountDeadline) {
          const discountAmount = (invoice.amount_due || invoice.total_amount) * 
            (supplier.early_payment_discount_percentage / 100);
          totalDiscountAmount += discountAmount;
        }
      }
    }

    // Add discount information to notes
    const discountNote = totalDiscountAmount > 0 
      ? `Early payment discount applied: $${totalDiscountAmount.toFixed(2)}. ` 
      : "";

    return {
      ...input,
      notes: `${discountNote}${input.notes || ""}`.trim(),
    };
  } catch (error) {
    console.warn("Error calculating early payment discounts:", error);
    return input;
  }
}

async function postPaymentProcessing(paymentResult: any): Promise<void> {
  // This function handles post-payment activities like notifications, reporting, etc.
  try {
    // Log payment completion
    console.log("Payment processed successfully:", paymentResult);

    // Here you could add:
    // - Email notifications to suppliers
    // - Accounting system integration
    // - Payment confirmations
    // - Audit trail creation
  } catch (error) {
    console.error("Error in post-payment processing:", error);
    // Don't throw here as the payment itself was successful
  }
}

export async function createPaymentBatch(
  invoiceIds: string[],
  batchDetails: {
    batch_name: string;
    payment_date: string;
    payment_method: "BANK_TRANSFER" | "CHEQUE" | "CASH" | "ONLINE" | "CREDIT_CARD";
    bank_account_id?: string;
    notes?: string;
  }
): Promise<{
  batch_id: string;
  total_amount: number;
  invoice_count: number;
  early_discount_amount: number;
}> {
  const supabase = await createClient();

  try {
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required to create payment batch");
    }

    // Validate invoices
    const { data: invoices, error: invoicesError } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        id,
        total_amount,
        amount_due,
        status,
        supplier_id,
        invoice_date,
        supplier:suppliers(name, early_payment_discount_percentage, early_payment_discount_days)
      `)
      .in("id", invoiceIds)
      .in("status", ["APPROVED", "PARTIALLY_PAID"]);

    if (invoicesError) {
      throw new Error(`Failed to validate invoices: ${invoicesError.message}`);
    }

    if (!invoices || invoices.length === 0) {
      throw new Error("No eligible invoices found for payment batch");
    }

    // Calculate totals and early payment discounts
    let totalAmount = 0;
    let earlyDiscountAmount = 0;
    const paymentDate = new Date(batchDetails.payment_date);

    for (const invoice of invoices) {
      const outstandingAmount = invoice.amount_due || invoice.total_amount;
      totalAmount += outstandingAmount;

      // Calculate early payment discount if applicable
      const supplier = Array.isArray(invoice.supplier) ? invoice.supplier[0] : invoice.supplier;
      if (supplier?.early_payment_discount_percentage && supplier?.early_payment_discount_days) {
        const invoiceDate = new Date(invoice.invoice_date);
        const discountDeadline = new Date(invoiceDate);
        discountDeadline.setDate(invoiceDate.getDate() + supplier.early_payment_discount_days);

        if (paymentDate <= discountDeadline) {
          earlyDiscountAmount += outstandingAmount * (supplier.early_payment_discount_percentage / 100);
        }
      }
    }

    // Create payment batch
    const { data: batch, error: batchError } = await supabase
      .schema("procurement")
      .from("payment_batches")
      .insert({
        batch_name: batchDetails.batch_name,
        payment_date: batchDetails.payment_date,
        payment_method: batchDetails.payment_method,
        bank_account_id: batchDetails.bank_account_id,
        total_amount: totalAmount,
        early_discount_amount: earlyDiscountAmount,
        invoice_count: invoices.length,
        status: "CREATED",
        created_by: user.id,
        notes: batchDetails.notes,
      })
      .select()
      .single();

    if (batchError) {
      throw new Error(`Failed to create payment batch: ${batchError.message}`);
    }

    // Create payment batch items
    const batchItems = invoices.map(invoice => ({
      batch_id: batch.id,
      invoice_id: invoice.id,
      amount: invoice.amount_due || invoice.total_amount,
      supplier_id: invoice.supplier_id,
    }));

    const { error: itemsError } = await supabase
      .schema("procurement")
      .from("payment_batch_items")
      .insert(batchItems);

    if (itemsError) {
      // Rollback batch creation
      await supabase.schema("procurement").from("payment_batches").delete().eq("id", batch.id);
      throw new Error(`Failed to create payment batch items: ${itemsError.message}`);
    }

    return {
      batch_id: batch.id,
      total_amount: totalAmount,
      invoice_count: invoices.length,
      early_discount_amount: earlyDiscountAmount,
    };
  } catch (error) {
    console.error("Error creating payment batch:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to create payment batch. Please try again or contact support.");
  }
}

export async function authorizePaymentBatch(batchId: string, authorizationDetails: {
  authorized_by: string;
  authorization_date: string;
  authorization_comments?: string;
}): Promise<void> {
  const supabase = await createClient();

  try {
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required to authorize payment batch");
    }

    // Check if batch exists and is in correct status
    const { data: batch, error: batchError } = await supabase
      .schema("procurement")
      .from("payment_batches")
      .select("id, status, total_amount")
      .eq("id", batchId)
      .single();

    if (batchError || !batch) {
      throw new Error("Payment batch not found");
    }

    if (batch.status !== "CREATED") {
      throw new Error(`Cannot authorize batch with status: ${batch.status}`);
    }

    // Update batch status to authorized
    const { error: updateError } = await supabase
      .schema("procurement")
      .from("payment_batches")
      .update({
        status: "AUTHORIZED",
        authorized_by: user.id,
        authorized_at: authorizationDetails.authorization_date,
        authorization_comments: authorizationDetails.authorization_comments,
        updated_at: new Date().toISOString(),
      })
      .eq("id", batchId);

    if (updateError) {
      throw new Error(`Failed to authorize payment batch: ${updateError.message}`);
    }

    // Log authorization (if approval_history table exists)
    try {
      await supabase
        .schema("procurement")
        .from("approval_history")
        .insert({
          entity_type: "payment_batch",
          entity_id: batchId,
          action: "AUTHORIZED",
          approver_id: user.id,
          approval_date: authorizationDetails.authorization_date,
          comments: authorizationDetails.authorization_comments,
          approval_level: 2,
        });
    } catch (auditError) {
      console.warn("Failed to log payment batch authorization:", auditError);
    }

  } catch (error) {
    console.error("Error authorizing payment batch:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to authorize payment batch. Please try again or contact support.");
  }
}

export async function allocatePaymentToInvoices(
  paymentId: string,
  allocations: Array<{
    invoice_id: string;
    amount: number;
    discount_amount?: number;
  }>
): Promise<void> {
  const supabase = await createClient();

  try {
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required to allocate payments");
    }

    // Validate payment exists and is unallocated
    const { data: payment, error: paymentError } = await supabase
      .schema("procurement")
      .from("supplier_payments")
      .select("id, amount, status, allocated_amount")
      .eq("id", paymentId)
      .single();

    if (paymentError || !payment) {
      throw new Error("Payment not found");
    }

    if (payment.status !== "COMPLETED") {
      throw new Error("Can only allocate completed payments");
    }

    // Calculate total allocation amount
    const totalAllocation = allocations.reduce((sum, alloc) => sum + alloc.amount + (alloc.discount_amount || 0), 0);
    const availableAmount = payment.amount - (payment.allocated_amount || 0);

    if (totalAllocation > availableAmount) {
      throw new Error(`Total allocation ($${totalAllocation}) exceeds available amount ($${availableAmount})`);
    }

    // Validate invoices exist and have outstanding amounts
    const invoiceIds = allocations.map(alloc => alloc.invoice_id);
    const { data: invoices, error: invoicesError } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select("id, amount_due, status")
      .in("id", invoiceIds);

    if (invoicesError || !invoices) {
      throw new Error("Failed to validate invoices for allocation");
    }

    for (const allocation of allocations) {
      const invoice = invoices.find(inv => inv.id === allocation.invoice_id);
      if (!invoice) {
        throw new Error(`Invoice ${allocation.invoice_id} not found`);
      }

      if (allocation.amount > ((invoice as any).amount_due || 0)) {
        throw new Error(`Allocation amount exceeds outstanding balance for invoice ${allocation.invoice_id}`);
      }
    }

    // Create payment allocations
    const allocationRecords = allocations.map(alloc => ({
      payment_id: paymentId,
      invoice_id: alloc.invoice_id,
      allocated_amount: alloc.amount,
      discount_amount: alloc.discount_amount || 0,
      allocated_by: user.id,
      allocated_at: new Date().toISOString(),
    }));

    const { error: insertError } = await supabase
      .schema("procurement")
      .from("payment_allocations")
      .insert(allocationRecords);

    if (insertError) {
      throw new Error(`Failed to create payment allocations: ${insertError.message}`);
    }

    // Update payment allocated amount
    const newAllocatedAmount = (payment.allocated_amount || 0) + totalAllocation;
    const { error: updatePaymentError } = await supabase
      .schema("procurement")
      .from("supplier_payments")
      .update({
        allocated_amount: newAllocatedAmount,
        updated_at: new Date().toISOString(),
      })
      .eq("id", paymentId);

    if (updatePaymentError) {
      throw new Error(`Failed to update payment allocation: ${updatePaymentError.message}`);
    }

    // Update invoice outstanding amounts and payment status
    for (const allocation of allocations) {
      const invoice = invoices.find(inv => inv.id === allocation.invoice_id);
      if (invoice) {
        const newOutstanding = ((invoice as any).amount_due || 0) - allocation.amount - (allocation.discount_amount || 0);
        const newStatus = newOutstanding <= 0.01 ? "PAID" : "APPROVED"; // Allow for small rounding differences

        await supabase
          .schema("procurement")
          .from("purchase_invoices")
          .update({
            amount_paid: ((invoice as any).total_amount || 0) - Math.max(0, newOutstanding),
            status: newStatus,
            updated_at: new Date().toISOString(),
            updated_by: user.id,
          })
          .eq("id", allocation.invoice_id);
      }
    }

  } catch (error) {
    console.error("Error allocating payment to invoices:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to allocate payment. Please try again or contact support.");
  }
}

export async function getPaymentStatus(invoiceId: string): Promise<{
  total_amount: number;
  paid_amount: number;
  outstanding_amount: number;
  payment_status: "PENDING" | "PARTIAL" | "PAID" | "FAILED";
  payments: Array<{
    id: string;
    payment_date: string;
    amount: number;
    method: "BANK_TRANSFER" | "CHEQUE" | "CASH" | "ONLINE" | "CREDIT_CARD";
    status: "PENDING" | "PARTIAL" | "COMPLETED" | "FAILED";
    reference: string | null;
  }>;
  early_payment_discount_available: boolean;
  early_payment_discount_amount?: number;
  early_payment_discount_deadline?: string;
}> {
  const supabase = await createClient();

  try {
    // Get invoice details with payments
    const { data: invoice, error } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        id,
        total_amount,
        amount_due,
        payment_status,
        invoice_date,
        supplier:suppliers(
          early_payment_discount_percentage,
          early_payment_discount_days
        ),
        payments:supplier_payments(
          id,
          payment_date,
          amount,
          payment_method,
          status,
          reference_number
        )
      `)
      .eq("id", invoiceId)
      .single();

    if (error || !invoice) {
      throw new Error("Invoice not found");
    }

    const paidAmount = invoice.total_amount - (invoice.amount_due || 0);
    
    // Calculate early payment discount if applicable
    let earlyPaymentDiscountAvailable = false;
    let earlyPaymentDiscountAmount = 0;
    let earlyPaymentDiscountDeadline: string | undefined;

    const supplier = Array.isArray(invoice.supplier) ? invoice.supplier[0] : invoice.supplier;
    if (supplier?.early_payment_discount_percentage && supplier?.early_payment_discount_days) {
      const invoiceDate = new Date(invoice.invoice_date);
      const discountDeadline = new Date(invoiceDate);
      discountDeadline.setDate(invoiceDate.getDate() + supplier.early_payment_discount_days);
      
      const today = new Date();
      if (today <= discountDeadline && (invoice.amount_due || 0) > 0) {
        earlyPaymentDiscountAvailable = true;
        earlyPaymentDiscountAmount = (invoice.amount_due || 0) * 
          (supplier.early_payment_discount_percentage / 100);
        earlyPaymentDiscountDeadline = discountDeadline.toISOString().split('T')[0];
      }
    }

    return {
      total_amount: invoice.total_amount,
      paid_amount: paidAmount,
      outstanding_amount: invoice.amount_due || 0,
      payment_status: invoice.amount_due > 0 ? "PENDING" : "PAID",
      payments: (invoice.payments || []).map(payment => ({
        id: payment.id,
        payment_date: payment.payment_date,
        amount: payment.amount,
        method: payment.payment_method,
        status: payment.status,
        reference: payment.reference_number,
      })),
      early_payment_discount_available: earlyPaymentDiscountAvailable,
      early_payment_discount_amount: earlyPaymentDiscountAmount > 0 ? earlyPaymentDiscountAmount : undefined,
      early_payment_discount_deadline: earlyPaymentDiscountDeadline,
    };

  } catch (error) {
    console.error("Error getting payment status:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to get payment status. Please try again or contact support.");
  }
}

export async function calculateEarlyPaymentDiscountEligibility(invoiceIds: string[]): Promise<Array<{
  invoice_id: string;
  invoice_number: string;
  outstanding_amount: number;
  discount_percentage: number;
  discount_amount: number;
  discount_deadline: string;
  days_remaining: number;
  eligible: boolean;
}>> {
  const supabase = await createClient();

  try {
    const { data: invoices, error } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        id,
        invoice_number,
        amount_due,
        invoice_date,
        supplier:suppliers(
          early_payment_discount_percentage,
          early_payment_discount_days
        )
      `)
      .in("id", invoiceIds)
      .gt("outstanding_amount", 0);

    if (error) {
      throw new Error(`Failed to fetch invoice data: ${error.message}`);
    }

    const today = new Date();
    
    return (invoices || []).map(invoice => {
      const supplier = Array.isArray(invoice.supplier) ? invoice.supplier[0] : invoice.supplier;
      const discountPercentage = supplier?.early_payment_discount_percentage || 0;
      const discountDays = supplier?.early_payment_discount_days || 0;
      
      const invoiceDate = new Date(invoice.invoice_date);
      const discountDeadline = new Date(invoiceDate);
      discountDeadline.setDate(invoiceDate.getDate() + discountDays);
      
      const daysRemaining = Math.ceil((discountDeadline.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      const eligible = daysRemaining > 0 && discountPercentage > 0;
      
      const discountAmount = eligible 
        ? ((invoice as any).amount_due || 0) * (discountPercentage / 100)
        : 0;

      return {
        invoice_id: invoice.id,
        invoice_number: invoice.invoice_number,
        outstanding_amount: (invoice as any).amount_due || 0,
        discount_percentage: discountPercentage,
        discount_amount: discountAmount,
        discount_deadline: discountDeadline.toISOString().split('T')[0],
        days_remaining: Math.max(0, daysRemaining),
        eligible,
      };
    });

  } catch (error) {
    console.error("Error calculating early payment discount eligibility:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to calculate early payment discount eligibility. Please try again or contact support.");
  }
}

export async function submitInvoiceForApproval(invoiceId: string, comments?: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("submit_purchase_invoice_for_approval", {
      p_invoice_id: invoiceId,
      p_comments: comments,
    });

  if (error) {
    console.error("Error submitting invoice for approval:", error);
    throw new Error(`Failed to submit invoice for approval: ${error.message}`);
  }

  if (!data.success) {
    throw new Error(data.error || "Failed to submit invoice for approval");
  }

  return data;
}

export async function approveInvoice(invoiceId: string, comments?: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("approve_purchase_invoice", {
      p_invoice_id: invoiceId,
      p_comments: comments,
    });

  if (error) {
    console.error("Error approving invoice:", error);
    throw new Error(`Failed to approve invoice: ${error.message}`);
  }

  if (!data.success) {
    throw new Error(data.error || "Failed to approve invoice");
  }

  return data;
}

export async function rejectInvoice(invoiceId: string, reason: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("reject_purchase_invoice", {
      p_invoice_id: invoiceId,
      p_reason: reason,
    });

  if (error) {
    console.error("Error rejecting invoice:", error);
    throw new Error(`Failed to reject invoice: ${error.message}`);
  }

  if (!data.success) {
    throw new Error(data.error || "Failed to reject invoice");
  }

  return data;
}

export async function getApprovalHistory(invoiceId: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("get_purchase_invoice_approval_history", {
      p_invoice_id: invoiceId,
    });

  if (error) {
    console.error("Error fetching approval history:", error);
    throw new Error(`Failed to fetch approval history: ${error.message}`);
  }

  return data || [];
}

export async function getSupplierStatement(
  supplierId: string,
  fromDate: string,
  toDate: string
): Promise<SupplierStatement> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("get_supplier_statement", {
      p_supplier_id: supplierId,
      p_from_date: fromDate,
      p_to_date: toDate,
    });

  if (error) {
    console.error("Error fetching supplier statement:", error);
    throw error;
  }

  return data;
}

export async function exportPurchaseInvoices(filters: PurchaseInvoiceFilters): Promise<PurchaseInvoiceExportData> {
  const supabase = await createClient();
  
  // Get current authenticated user for export tracking
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  const exportedBy = user?.id || "anonymous";

  const allData = await getPurchaseInvoices({ ...filters, limit: 1000 });
  const summary = await getPurchaseInvoicesSummary();

  return {
    invoices: allData.data,
    summary,
    export_date: new Date().toISOString(),
    exported_by: exportedBy, // Use actual authenticated user ID or anonymous
  };
}

export async function getVendorPerformanceMetrics(supplierId: string): Promise<VendorPerformanceMetrics> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("get_vendor_performance_metrics", {
      p_supplier_id: supplierId,
    });

  if (error) {
    console.error("Error fetching vendor performance metrics:", error);
    throw error;
  }

  return data;
}

export async function getToleranceSettings(entityId?: string): Promise<ToleranceSettings> {
  const supabase = await createClient();
  
  try {
    // Get entity-specific tolerance settings from new procurement.tolerance_settings table
    const { data: toleranceData, error } = await supabase
      .schema("procurement")
      .from("tolerance_settings")
      .select("*")
      .eq("is_active", true)
      .eq("entity_id", entityId || "00000000-0000-0000-0000-000000000000") // Default entity
      .order("priority", { ascending: true });

    if (error) {
      console.error("Error fetching tolerance settings:", error);
      // Return default values
      return {
        quantity_tolerance_percentage: 5,
        price_tolerance_percentage: 2,
        total_tolerance_amount: 100,
        auto_approve_within_tolerance: true,
        require_approval_above_tolerance: true,
      };
    }

    if (!toleranceData || toleranceData.length === 0) {
      // Return default values if no settings found
      return {
        quantity_tolerance_percentage: 5,
        price_tolerance_percentage: 2,
        total_tolerance_amount: 100,
        auto_approve_within_tolerance: true,
        require_approval_above_tolerance: true,
      };
    }

    // Convert array of tolerance settings to a single object
    const settings: ToleranceSettings = {
      quantity_tolerance_percentage: 5,
      price_tolerance_percentage: 2,
      total_tolerance_amount: 100,
      auto_approve_within_tolerance: true,
      require_approval_above_tolerance: true,
    };

    toleranceData.forEach(setting => {
      switch (setting.tolerance_type) {
        case 'QUANTITY':
          settings.quantity_tolerance_percentage = setting.tolerance_percentage || 5;
          break;
        case 'PRICE':
          settings.price_tolerance_percentage = setting.tolerance_percentage || 2;
          break;
        case 'AMOUNT':
        case 'TAX':
          if (setting.tolerance_amount) {
            settings.total_tolerance_amount = setting.tolerance_amount;
          }
          break;
      }
    });

    return settings;
  } catch (error) {
    console.error("Error in getToleranceSettings:", error);
    // Return default values on any error
    return {
      quantity_tolerance_percentage: 5,
      price_tolerance_percentage: 2,
      total_tolerance_amount: 100,
      auto_approve_within_tolerance: true,
      require_approval_above_tolerance: true,
    };
  }
}

export async function getPaymentMethods() {
  return [
    { value: "BANK_TRANSFER", label: "Bank Transfer" },
    { value: "CHEQUE", label: "Cheque" },
    { value: "CASH", label: "Cash" },
    { value: "ONLINE", label: "Online Payment" },
    { value: "CREDIT_CARD", label: "Credit Card" },
  ];
}

export async function getInvoiceStatuses() {
  return [
    { value: "RECEIVED", label: "Received" },
    { value: "PENDING_APPROVAL", label: "Pending Approval" },
    { value: "APPROVED", label: "Approved" },
    { value: "PAID", label: "Paid" },
    { value: "DISPUTED", label: "Disputed" },
    { value: "CANCELLED", label: "Cancelled" },
  ];
}

export async function getOverdueInvoices(supplierId?: string) {
  const supabase = await createClient();
  let query = supabase
    .from("purchase_invoices")
    .select(`
      *,
      supplier:suppliers!inner(name, supplier_code)
    `)
    .neq("payment_status", "COMPLETED")
    .not("status", "eq", "CANCELLED")
    .lt("due_date", new Date().toISOString().split("T")[0]);

  if (supplierId) {
    query = query.eq("supplier_id", supplierId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching overdue invoices:", error);
    throw error;
  }

  return data || [];
}

// Three-Way Matching specific functions
export async function getInvoicesForMatching(filters: PurchaseInvoiceFilters) {
  const supabase = await createClient();
  
  try {
    let query = supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          email,
          phone,
          payment_terms,
          status
        ),
        three_way_matches!purchase_invoice_id(
          purchase_invoice_id,
          purchase_order_id,
          goods_receipt_id,
          match_status,
          total_variance,
          quantity_variance,
          price_variance,
          amount_variance,
          tax_variance,
          tolerance_exceeded,
          requires_approval,
          matched_at,
          matched_by,
          notes
        )
      `)
      .eq("requires_three_way_match", true)
      .in("status", ["RECEIVED", "PENDING_APPROVAL", "MATCHED"]);

  // Apply filters
  if (filters.search) {
    query = query.or(
      `invoice_number.ilike.%${filters.search}%,supplier.name.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%`
    );
  }

  if (filters.supplier_id) {
    query = query.eq("supplier_id", filters.supplier_id);
  }

  if (filters.entity_id) {
    query = query.eq("entity_id", filters.entity_id);
  }

  if (filters.date_from) {
    query = query.gte("invoice_date", filters.date_from);
  }

  if (filters.date_to) {
    query = query.lte("invoice_date", filters.date_to);
  }

  if (filters.amount_min) {
    query = query.gte("total_amount", filters.amount_min);
  }

  if (filters.amount_max) {
    query = query.lte("total_amount", filters.amount_max);
  }

  // Sorting
  const sortColumn = filters.sort_by || "invoice_date";
  const sortOrder = filters.sort_order || "desc";
  query = query.order(sortColumn, { ascending: sortOrder === "asc" });

  // Pagination
  const page = filters.page || 1;
  const limit = filters.limit || 50;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error("Error fetching invoices for matching:", error);
      throw new Error(`Failed to fetch invoices for matching: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    };
  } catch (error) {
    console.error("Error in getInvoicesForMatching:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to load invoices for matching. Please try again or contact support.");
  }
}

export async function getMatchingSummary() {
  const supabase = await createClient();
  
  try {
    // Get matching statistics using proper schema and relationship
    const { data: matchingData, error } = await supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        id,
        total_amount,
        status,
        three_way_matches!purchase_invoice_id(
          match_status,
          total_variance,
          tolerance_exceeded,
          requires_approval
        )
      `)
      .eq("requires_three_way_match", true)
      .not("status", "eq", "CANCELLED");

    if (error) {
      console.error("Error fetching matching summary:", error);
      // Return default summary instead of throwing
      return {
        total_pending: 0,
        high_priority: 0,
        within_tolerance: 0,
        over_tolerance: 0,
        average_variance: 0,
        total_variance_amount: 0,
      };
    }

    const total_pending = matchingData?.filter(inv => 
      !inv.three_way_matches || inv.three_way_matches.length === 0 || 
      inv.three_way_matches[0]?.match_status === "NOT_MATCHED"
    ).length || 0;

    const high_priority = matchingData?.filter(inv => 
      inv.total_amount > 10000 || 
      (inv.three_way_matches?.[0]?.tolerance_exceeded && inv.three_way_matches?.[0]?.requires_approval)
    ).length || 0;

    const within_tolerance = matchingData?.filter(inv => 
      inv.three_way_matches?.[0] && !inv.three_way_matches[0].tolerance_exceeded
    ).length || 0;

    const over_tolerance = matchingData?.filter(inv => 
      inv.three_way_matches?.[0]?.tolerance_exceeded
    ).length || 0;

    const variances = matchingData?.map(inv => 
      inv.three_way_matches?.[0]?.total_variance || 0
    ).filter(v => v > 0) || [];

    const average_variance = variances.length > 0 
      ? variances.reduce((sum, v) => sum + v, 0) / variances.length 
      : 0;

    const total_variance_amount = variances.reduce((sum, v) => sum + v, 0);

    return {
      total_pending,
      high_priority,
      within_tolerance,
      over_tolerance,
      average_variance,
      total_variance_amount,
    };
  } catch (error) {
    console.error("Error in getMatchingSummary:", error);
    
    // Return default summary on any error
    return {
      total_pending: 0,
      high_priority: 0,
      within_tolerance: 0,
      over_tolerance: 0,
      average_variance: 0,
      total_variance_amount: 0,
    };
  }
}

export async function bulkPerformThreeWayMatch(
  invoiceIds: string[],
  options: {
    autoApproveWithinTolerance?: boolean;
    requireApprovalAboveTolerance?: boolean;
    customToleranceSettings?: Partial<ToleranceSettings>;
  } = {}
) {
  const results = [];
  const batchSize = 5; // Process in batches to avoid overwhelming the database

  for (let i = 0; i < invoiceIds.length; i += batchSize) {
    const batch = invoiceIds.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (invoiceId) => {
      try {
        const result = await performAdvancedThreeWayMatch(invoiceId, options);
        return { invoiceId, success: true, result };
      } catch (error) {
        return { 
          invoiceId, 
          success: false, 
          error: (error as Error).message 
        };
      }
    });

    const batchResults = await Promise.allSettled(batchPromises);
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          invoiceId: 'unknown',
          success: false,
          error: result.reason?.message || 'Unknown error occurred'
        });
      }
    }

    // Add a small delay between batches to prevent database overload
    if (i + batchSize < invoiceIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  // Generate summary statistics
  const summary = {
    total: results.length,
    successful: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    matched: results.filter(r => r.success && 'result' in r && r.result?.match_status === 'MATCHED').length,
    partiallyMatched: results.filter(r => r.success && 'result' in r && r.result?.match_status === 'PARTIALLY_MATCHED').length,
    notMatched: results.filter(r => r.success && 'result' in r && r.result?.match_status === 'NOT_MATCHED').length,
    toleranceExceeded: results.filter(r => r.success && 'result' in r && r.result?.tolerance_exceeded).length,
  };

  return {
    results,
    summary
  };
}

export async function analyzeVariances(invoiceIds: string[]) {
  const supabase = await createClient();
  
  try {
    const { data: invoiceData, error } = await supabase
      .from("purchase_invoices")
      .select(`
        id,
        invoice_number,
        total_amount,
        supplier:suppliers(name),
        three_way_match:three_way_matches(
          match_status,
          total_variance,
          quantity_variance,
          price_variance,
          tax_variance,
          tolerance_exceeded,
          requires_approval
        )
      `)
      .in("id", invoiceIds);

    if (error) {
      throw new Error(`Failed to analyze variances: ${error.message}`);
    }

    if (!invoiceData) {
      return {
        invoices: [],
        summary: {
          totalInvoices: 0,
          totalVariance: 0,
          averageVariance: 0,
          highestVariance: 0,
          lowestVariance: 0,
          variancesByType: {
            quantity: 0,
            price: 0,
            tax: 0
          }
        }
      };
    }

    // Calculate variance statistics
    const variances = invoiceData
      .filter(inv => inv.three_way_match?.[0])
      .map(inv => inv.three_way_match[0]);

    const totalVariance = variances.reduce((sum, match) => sum + Math.abs(match.total_variance), 0);
    const quantityVariance = variances.reduce((sum, match) => sum + Math.abs(match.quantity_variance), 0);
    const priceVariance = variances.reduce((sum, match) => sum + Math.abs(match.price_variance), 0);
    const taxVariance = variances.reduce((sum, match) => sum + Math.abs(match.tax_variance), 0);

    const varianceAmounts = variances.map(match => Math.abs(match.total_variance));
    const averageVariance = varianceAmounts.length > 0 
      ? varianceAmounts.reduce((sum, v) => sum + v, 0) / varianceAmounts.length 
      : 0;

    const summary = {
      totalInvoices: invoiceData.length,
      totalVariance,
      averageVariance,
      highestVariance: varianceAmounts.length > 0 ? Math.max(...varianceAmounts) : 0,
      lowestVariance: varianceAmounts.length > 0 ? Math.min(...varianceAmounts) : 0,
      variancesByType: {
        quantity: quantityVariance,
        price: priceVariance,
        tax: taxVariance
      }
    };

    return {
      invoices: invoiceData,
      summary
    };

  } catch (error) {
    console.error("Error analyzing variances:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to analyze variances. Please try again or contact support.");
  }
}

export async function resolveMatchingException(
  invoiceId: string,
  resolution: {
    action: 'APPROVE' | 'REJECT' | 'ADJUST';
    comments: string;
    adjustments?: {
      quantity?: number;
      price?: number;
      tax?: number;
    };
  }
) {
  const supabase = await createClient();
  
  try {
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required to resolve matching exception");
    }

    switch (resolution.action) {
      case 'APPROVE':
        // Approve despite variance
        await supabase
          .from("purchase_invoices")
          .update({
            status: "APPROVED",
            approved_at: new Date().toISOString(),
            approved_by: user.id,
            updated_at: new Date().toISOString(),
          })
          .eq("id", invoiceId);

        // Log exception resolution
        await supabase
          .from("approval_history")
          .insert({
            entity_type: "purchase_invoice",
            entity_id: invoiceId,
            action: "APPROVED",
            approver_id: user.id,
            approval_date: new Date().toISOString(),
            comments: `Exception resolved: ${resolution.comments}`,
            approval_level: 99, // Exception approval level
          });
        break;

      case 'REJECT':
        // Reject the invoice
        await supabase
          .from("purchase_invoices")
          .update({
            status: "RETURNED",
            updated_at: new Date().toISOString(),
            updated_by: user.id,
          })
          .eq("id", invoiceId);

        // Log exception rejection
        await supabase
          .from("approval_history")
          .insert({
            entity_type: "purchase_invoice",
            entity_id: invoiceId,
            action: "REJECTED",
            approver_id: user.id,
            approval_date: new Date().toISOString(),
            comments: `Exception rejection: ${resolution.comments}`,
            approval_level: 99,
          });
        break;

      case 'ADJUST':
        // Apply adjustments and re-run matching
        if (resolution.adjustments) {
          // This would typically involve creating adjustment entries
          // For now, we'll just log the adjustment intention
          await supabase
            .from("approval_history")
            .insert({
              entity_type: "purchase_invoice",
              entity_id: invoiceId,
              action: "ADJUSTMENT",
              approver_id: user.id,
              approval_date: new Date().toISOString(),
              comments: `Adjustments applied: ${resolution.comments}`,
              approval_level: 50,
            });

          // Re-perform three-way match after adjustments
          await performThreeWayMatch(invoiceId);
        }
        break;
    }

    return {
      success: true,
      action: resolution.action,
      message: `Exception resolved successfully with action: ${resolution.action}`
    };

  } catch (error) {
    console.error("Error resolving matching exception:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to resolve matching exception. Please try again or contact support.");
  }
}

export async function bulkApproveInvoices(invoiceIds: string[], comments?: string) {
  const supabase = await createClient();
  const results = [];

  for (const invoiceId of invoiceIds) {
    try {
      await approveInvoice(invoiceId, comments);
      results.push({ invoiceId, success: true });
    } catch (error) {
      results.push({ invoiceId, success: false, error: (error as Error).message });
    }
  }

  return results;
}

export async function bulkRejectInvoices(invoiceIds: string[], reason: string) {
  const supabase = await createClient();
  const results = [];

  for (const invoiceId of invoiceIds) {
    try {
      await rejectInvoice(invoiceId, reason);
      results.push({ invoiceId, success: true });
    } catch (error) {
      results.push({ invoiceId, success: false, error: (error as Error).message });
    }
  }

  return results;
}

export async function getMatchingPerformanceMetrics() {
  const supabase = await createClient();
  
  try {
    // This would typically be a more complex query or stored procedure
    const { data, error } = await supabase
      .schema("procurement")
      .from("three_way_matches")
      .select(`
        match_status,
        total_variance,
        tolerance_exceeded,
        matched_at,
        created_at
      `)
      .gte("created_at", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    if (error) {
      console.error("Error fetching matching performance metrics:", error);
      // Return default metrics instead of throwing
      return {
        totalMatches: 0,
        successRate: 0,
        partialMatchRate: 0,
        failureRate: 0,
        exceptionRate: 0,
        averageProcessingTime: 0,
        autoMatchRate: 0,
      };
    }

    const totalMatches = data?.length || 0;
    const successfulMatches = data?.filter(m => m.match_status === "MATCHED").length || 0;
    const partialMatches = data?.filter(m => m.match_status === "VARIANCE").length || 0;
    const failedMatches = data?.filter(m => m.match_status === "NOT_MATCHED").length || 0;
    const overTolerance = data?.filter(m => m.tolerance_exceeded).length || 0;

    // Calculate average processing time
    const processingTimes = data?.filter(m => m.matched_at).map(m => {
      const matchDate = new Date(m.matched_at);
      const createdDate = new Date(m.created_at);
      return (matchDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60); // hours
    }) || [];

    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
      : 0;

    return {
      totalMatches,
      successRate: totalMatches > 0 ? (successfulMatches / totalMatches) * 100 : 0,
      partialMatchRate: totalMatches > 0 ? (partialMatches / totalMatches) * 100 : 0,
      failureRate: totalMatches > 0 ? (failedMatches / totalMatches) * 100 : 0,
      exceptionRate: totalMatches > 0 ? (overTolerance / totalMatches) * 100 : 0,
      averageProcessingTime,
      autoMatchRate: 85, // This would be calculated based on actual automation data
    };
  } catch (error) {
    console.error("Error in getMatchingPerformanceMetrics:", error);
    
    // Return default metrics on any error
    return {
      totalMatches: 0,
      successRate: 0,
      partialMatchRate: 0,
      failureRate: 0,
      exceptionRate: 0,
      averageProcessingTime: 0,
      autoMatchRate: 0,
    };
  }
}

export async function updateToleranceSettings(settings: ToleranceSettings) {
  const supabase = await createClient();
  
  try {
    // Get current user for audit trail
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required to update tolerance settings");
    }

    // Validate settings
    if (settings.quantity_tolerance_percentage < 0 || settings.quantity_tolerance_percentage > 100) {
      throw new Error("Quantity tolerance percentage must be between 0 and 100");
    }
    
    if (settings.price_tolerance_percentage < 0 || settings.price_tolerance_percentage > 100) {
      throw new Error("Price tolerance percentage must be between 0 and 100");
    }
    
    if (settings.total_tolerance_amount < 0) {
      throw new Error("Total tolerance amount must be non-negative");
    }

    const { data, error } = await supabase
      .schema("public")
      .from("system_settings")
      .upsert({
        category: "three_way_match_tolerance",
        key: "tolerance_settings",
        value: settings,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      })
      .select()
      .single();

    if (error) {
      console.error("Error updating tolerance settings:", error);
      throw new Error(`Failed to update tolerance settings: ${error.message}`);
    }

    // Log the settings change (if audit_log table exists)
    try {
      await supabase
        .schema("public")
        .from("audit_log")
        .insert({
          action: "UPDATE_TOLERANCE_SETTINGS",
          entity_type: "system_settings",
          entity_id: data.id,
          user_id: user.id,
          metadata: {
            old_settings: await getToleranceSettings(), // Get previous settings
            new_settings: settings,
          },
          timestamp: new Date().toISOString(),
        });
    } catch (auditError) {
      // Ignore audit log errors as they shouldn't block the main operation
      console.warn("Failed to create audit log entry for tolerance settings update:", auditError);
    }

    return data;
  } catch (error) {
    console.error("Error in updateToleranceSettings:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to update tolerance settings. Please try again or contact support.");
  }
}

export async function validateToleranceSettings(settings: Partial<ToleranceSettings>): Promise<{
  valid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];

  if (settings.quantity_tolerance_percentage !== undefined) {
    if (settings.quantity_tolerance_percentage < 0 || settings.quantity_tolerance_percentage > 100) {
      errors.push("Quantity tolerance percentage must be between 0 and 100");
    }
  }

  if (settings.price_tolerance_percentage !== undefined) {
    if (settings.price_tolerance_percentage < 0 || settings.price_tolerance_percentage > 100) {
      errors.push("Price tolerance percentage must be between 0 and 100");
    }
  }

  if (settings.total_tolerance_amount !== undefined) {
    if (settings.total_tolerance_amount < 0) {
      errors.push("Total tolerance amount must be non-negative");
    }
  }

  // Business rule validation
  if (settings.auto_approve_within_tolerance && settings.require_approval_above_tolerance === false) {
    errors.push("Cannot auto-approve within tolerance if approval is not required above tolerance");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export async function getToleranceSettingsHistory(): Promise<Array<{
  timestamp: string;
  user_id: string;
  settings: ToleranceSettings;
  changes: string[];
}>> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .schema("public")
      .from("audit_log")
      .select(`
        timestamp,
        user_id,
        metadata,
        users:user_id(email)
      `)
      .eq("action", "UPDATE_TOLERANCE_SETTINGS")
      .order("timestamp", { ascending: false })
      .limit(50);

    if (error) {
      console.error("Error fetching tolerance settings history:", error);
      return [];
    }

    return data?.map(entry => ({
      timestamp: entry.timestamp,
      user_id: entry.user_id,
      settings: entry.metadata?.new_settings || {},
      changes: compareToleranceSettings(
        entry.metadata?.old_settings || {},
        entry.metadata?.new_settings || {}
      ),
    })) || [];
  } catch (error) {
    console.error("Error in getToleranceSettingsHistory:", error);
    return [];
  }
}

function compareToleranceSettings(oldSettings: ToleranceSettings, newSettings: ToleranceSettings): string[] {
  const changes: string[] = [];

  if (oldSettings.quantity_tolerance_percentage !== newSettings.quantity_tolerance_percentage) {
    changes.push(`Quantity tolerance: ${oldSettings.quantity_tolerance_percentage}% → ${newSettings.quantity_tolerance_percentage}%`);
  }

  if (oldSettings.price_tolerance_percentage !== newSettings.price_tolerance_percentage) {
    changes.push(`Price tolerance: ${oldSettings.price_tolerance_percentage}% → ${newSettings.price_tolerance_percentage}%`);
  }

  if (oldSettings.total_tolerance_amount !== newSettings.total_tolerance_amount) {
    changes.push(`Total tolerance: $${oldSettings.total_tolerance_amount} → $${newSettings.total_tolerance_amount}`);
  }

  if (oldSettings.auto_approve_within_tolerance !== newSettings.auto_approve_within_tolerance) {
    changes.push(`Auto-approve within tolerance: ${oldSettings.auto_approve_within_tolerance} → ${newSettings.auto_approve_within_tolerance}`);
  }

  if (oldSettings.require_approval_above_tolerance !== newSettings.require_approval_above_tolerance) {
    changes.push(`Require approval above tolerance: ${oldSettings.require_approval_above_tolerance} → ${newSettings.require_approval_above_tolerance}`);
  }

  return changes;
}

// Additional functions for payment processing integration
export async function getApprovedInvoicesForPayment(filters: {
  supplier_id?: string;
  entity_id?: string;
  currency_code?: string;
  due_date_from?: string;
  due_date_to?: string;
  min_amount?: number;
  max_amount?: number;
  search?: string;
  page?: number;
  limit?: number;
} = {}) {
  const supabase = await createClient();
  
  try {
    let query = supabase
      .schema("procurement")
      .from("purchase_invoices")
      .select(`
        *,
        supplier:suppliers(
          id,
          supplier_code,
          name,
          display_name,
          email,
          phone,
          payment_terms,
          preferred_payment_method,
          bank_details,
          status,
          early_payment_discount_percentage,
          early_payment_discount_days
        ),
        entity:finance.entities(
          id,
          name,
          code,
          currency_code,
          country_code
        )
      `)
      .eq("status", "APPROVED")
      .gt("amount_due", 0); // Only invoices with outstanding amounts

    // Apply filters
    if (filters.supplier_id) {
      query = query.eq("supplier_id", filters.supplier_id);
    }
    
    if (filters.entity_id) {
      query = query.eq("entity_id", filters.entity_id);
    }
    
    if (filters.currency_code) {
      query = query.eq("currency_code", filters.currency_code);
    }
    
    if (filters.due_date_from) {
      query = query.gte("due_date", filters.due_date_from);
    }
    
    if (filters.due_date_to) {
      query = query.lte("due_date", filters.due_date_to);
    }
    
    if (filters.min_amount) {
      query = query.gte("total_amount", filters.min_amount);
    }
    
    if (filters.max_amount) {
      query = query.lte("total_amount", filters.max_amount);
    }
    
    if (filters.search) {
      query = query.or(`invoice_number.ilike.%${filters.search}%,supplier_reference.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%`);
    }

    // Sort by due date (overdue first, then by due date)
    query = query.order("due_date", { ascending: true });

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 50;
    const offset = (page - 1) * limit;
    
    query = query.range(offset, offset + limit - 1);
    
    const { data, error, count } = await query;
    
    if (error) {
      console.error("Error fetching approved invoices for payment:", error);
      throw new Error(`Failed to fetch approved invoices: ${error.message}`);
    }
    
    // Calculate computed fields
    const invoicesWithComputedFields = (data || []).map((invoice: any) => {
      const outstanding = invoice.amount_due || 0;
      
      let daysOverdue = 0;
      if (invoice.due_date && outstanding > 0) {
        const dueDate = new Date(invoice.due_date);
        const today = new Date();
        const diffTime = today.getTime() - dueDate.getTime();
        daysOverdue = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
      }

      // Calculate early payment discount if applicable
      let earlyPaymentDiscount = 0;
      const supplier = Array.isArray(invoice.supplier) ? invoice.supplier[0] : invoice.supplier;
      if (supplier?.early_payment_discount_percentage && supplier?.early_payment_discount_days && outstanding > 0) {
        const invoiceDate = new Date(invoice.invoice_date);
        const discountDeadline = new Date(invoiceDate);
        discountDeadline.setDate(invoiceDate.getDate() + supplier.early_payment_discount_days);
        
        const today = new Date();
        if (today <= discountDeadline) {
          earlyPaymentDiscount = outstanding * (supplier.early_payment_discount_percentage / 100);
        }
      }

      return {
        ...invoice,
        outstanding_amount: outstanding,
        days_overdue: daysOverdue,
        early_payment_discount: earlyPaymentDiscount,
      } as PurchaseInvoice;
    });

    return {
      data: invoicesWithComputedFields,
      count: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit),
    };
    
  } catch (error) {
    console.error("Error in getApprovedInvoicesForPayment:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    throw new Error("Failed to load approved invoices for payment. Please try again or contact support.");
  }
}