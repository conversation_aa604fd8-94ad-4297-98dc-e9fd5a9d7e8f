import { FormSection, FormSectionType } from "@/types/application-form.types"
import { ApplicationDetail, ApplicationInterface, AppointmentDetail } from "@/types/applications/applications.types"
import { createClient } from '@/utils/supabase/Client'
import { ValidationRule } from "react-hook-form"
import { MemberService } from "@/services/members/member.service"

export class ApplicationService {
    // ==========================================
    // table part
    // ==========================================
    static async getAllApplications(
        filters: {
            subFilters: { [key: string]: string[] };
        },
        page: number,
        limit: number,
        searchTerm?: string
    ): Promise<{
        applications: ApplicationInterface[] | null;
        success: boolean;
        total: number;
        currentPage: number;
        allApplicationIds: string[];
    }> {
        const supabase = await createClient();

        try {
            let baseQuery = supabase
                .from("application_profiles")
                .select("application_id", { count: "exact" })
                .neq("application_status", "DRAFT");

            // Add subFilters
            Object.keys(filters.subFilters).forEach((filterKey) => {
                const filterValues = filters.subFilters[filterKey];
                if (Array.isArray(filterValues) && filterValues.length > 0) {
                    baseQuery = baseQuery.in(filterKey, filterValues);
                }
            });

            // Apply search term filter if present
            if (searchTerm) {
                const normalizedSearchTerm = searchTerm.trim();
                baseQuery = baseQuery.or(
                    `reference_no.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
                );
            }

            const { data: allApplicationIdsData, count: total, error: totalError } = await baseQuery;
            if (totalError) {
                throw new Error();
            }

            const allApplicationIds = allApplicationIdsData?.map((app) => app.application_id) || [];

            // Handle case where no applications match
            if (!total) {
                return { applications: [], success: true, total: 0, currentPage: 1, allApplicationIds: [] };
            }

            // Pagination
            const maxPage = Math.ceil(total / limit);
            const adjustedPage = Math.max(1, Math.min(page, maxPage));
            const start = (adjustedPage - 1) * limit;
            const end = start + limit - 1;

            // Main query
            let mainQuery = supabase
                .from("application_profiles")
                .select(
                    `
                application_id,
                reference_no,
                membership_type_name,
                submitted_at,
                application_status,
                application_type,
                approved_or_rejected_at,
                comment,
                full_name
            `,
                    { count: "exact" }
                )
                .neq("application_status", "DRAFT")
                .range(start, end);

            // Reapply filters on the main query
            Object.keys(filters.subFilters).forEach((filterKey) => {
                const filterValues = filters.subFilters[filterKey];
                if (Array.isArray(filterValues) && filterValues.length > 0) {
                    mainQuery = mainQuery.in(filterKey, filterValues);
                }
            });

            // Apply search term filter if present
            if (searchTerm) {
                const normalizedSearchTerm = searchTerm.trim();
                mainQuery = mainQuery.or(
                    `reference_no.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
                );
            }

            const { data: applicationsData, error: applicationsError } = await mainQuery;
            if (applicationsError) {
                throw new Error();
            }

            if (!applicationsData || applicationsData.length === 0) {
                return { applications: [], success: true, total, currentPage: adjustedPage, allApplicationIds };
            }

            const enrichedApplications = applicationsData.map((app) => ({
                application_id: app.application_id ?? "-",
                reference_no: app.reference_no ?? "-",
                membership_type_name: app.membership_type_name ?? "-",
                submitted_at: app.submitted_at ? new Date(app.submitted_at).toLocaleDateString() : "-",
                application_status: app.application_status ?? "-",
                application_type: app.application_type ?? "-",
                approved_or_rejected_at: app.approved_or_rejected_at ? new Date(app.approved_or_rejected_at).toLocaleDateString() : "-",
                comment: app.comment ?? "Add comments...",
                full_name: app.full_name ?? "-"
            }));

            return {
                applications: enrichedApplications,
                success: true,
                total,
                currentPage: adjustedPage,
                allApplicationIds
            };
        } catch {
            return { applications: null, success: false, total: 0, currentPage: page, allApplicationIds: [] };
        }
    }

    static async getMembershipTypes(): Promise<{ id: string, name: string }[]> {
        const supabase = await createClient();

        const { data: membershipTypes, error: queryError } = await supabase
            .from('membership_types')
            .select('id, name');

        if (queryError) {
            return [];
        }

        return membershipTypes || [];
    }

    // ==========================================
    // details part
    // ==========================================

    static async updateApplications(id: string, status: string, comment: string) {
        const supabase = await createClient();

        // Get the current date and time
        const approved_or_rejected_at = new Date().toISOString();

        const { error } = await supabase
            .from('applications')
            .update({ status, comment, approved_or_rejected_at })
            .eq('id', id);

        if (error) {
            return false
        }

        return true;
    }

    static async getFormSections(id: string): Promise<{ sections: FormSection[], required_physical_submission: boolean }> {
        const supabase = await createClient();

        try {
            // Fetch application data to get membership type
            const { data: applicationData, error: applicationError } = await supabase
                .from('applications')
                .select('membership_type')
                .eq('id', id)
                .single();

            if (applicationError || !applicationData) {
                throw new Error();
            }

            const membershipTypeId = applicationData.membership_type;
            if (!membershipTypeId) {
                throw new Error();
            }

            // Fetch membership type data to get the required_physical_submission
            const { data: membershipData, error: membershipError } = await supabase
                .from('membership_types')
                .select('required_physical_submission')
                .eq('id', membershipTypeId)
                .single();

            if (membershipError || !membershipData) {
                throw new Error();
            }

            const requiredPhysicalSubmission = membershipData.required_physical_submission ?? false;

            // Fetch form sections based on membership type
            const { data: formSectionsData, error: formSectionsError } = await supabase
                .from('membership_section_mapping')
                .select(`
                *,
                form_sections (
                    id,
                    name,
                    order_number,
                    type
                )
            `)
                .eq('membership_type_id', membershipTypeId);

            if (formSectionsError) {
                throw new Error();
            }

            // Process and sort form sections
            const sections = (formSectionsData || [])
                .sort((a, b) => (a.form_sections?.order_number ?? 0) - (b.form_sections?.order_number ?? 0))
                .map(item => ({
                    name: item.form_sections?.name ?? '',
                    order_number: item.form_sections?.order_number ?? 0,
                    is_required: item.is_required ?? false,
                    is_visible: item.is_visible ?? false,
                    validation_rules: (item.validation_rules as Record<string, ValidationRule[]>) ?? {},
                    type: item.form_sections?.type as FormSectionType,
                }));

            // Return both sections and required_physical_submission flag
            return { sections, required_physical_submission: requiredPhysicalSubmission };
        } catch {
            return { sections: [], required_physical_submission: false };
        }
    }

    static async getApplicationDetailsById(id: string): Promise<ApplicationDetail | null> {
        const supabase = await createClient()

        try {
            const { data, error } = await supabase
                .from("applications")
                .select('*, membership_types(*)')
                .eq('id', id)
                .single()

            if (error) throw error

            const profileDetails = await MemberService.getProfileDetails(data.profile_id)

            if (!profileDetails) {
                return null
            }

            // Transform the database data into the Member type
            const applicationDetails: ApplicationDetail = {
                ...profileDetails,
                applications: {
                    id: data.id,
                    type: data.type,
                    membershipNames: data.membership_types.name,
                    status: data.status,
                    referenceNo: data.reference_no,
                    createdAt: data.created_at,
                    submittedAt: data.submitted_at,
                    approvedOrRejectedAt: data.approved_or_rejected_at,
                    comment: data.comment,
                }
            }

            return applicationDetails

        } catch {
            return null
        }
    }

    static async getAppointmentDetails(id: string): Promise<{ AppointmentDetail: AppointmentDetail[] }> {
        const supabase = await createClient();

        // Fetch appointment details
        const { data: appointmentData, error: appointmentError } = await supabase
            .schema('calendar')
            .from('application_appointments')
            .select(`
                scheduled_at,
                status,
                location,
                appointment_type,
                duration_minutes
            `)
            .eq('reference_id', id)
            .single();

        if (appointmentError || !appointmentData) {
            return { AppointmentDetail: [] };
        }

        const appointmentDetail: AppointmentDetail = {
            scheduled_at: appointmentData.scheduled_at || '',
            status: appointmentData.status || '',
            location: appointmentData.location || '',
            appointment_type: appointmentData.appointment_type || '',
            duration_minutes: appointmentData.duration_minutes || 0,
        };

        return { AppointmentDetail: [appointmentDetail] };
    }
}