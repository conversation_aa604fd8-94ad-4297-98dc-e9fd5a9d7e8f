'use server'

import { createClient } from '@/utils/supabase/server'

export async function updateParticipantCertificateNumber(
    participantId: string,
    certificateNumber: string
): Promise<{ success: boolean; error?: string }> {
    const supabase = await createClient();

    try {
        const { error } = await supabase
            .schema('programme')
            .from('participants')
            .update({
                certificate_no: certificateNumber
            })
            .eq('id', participantId);

        if (error) {
            console.log(error)
            // Check for unique constraint violation (Postgres error code 23505)
            if (error.code === '23505' || error.message.toLowerCase().includes('duplicate key')) {
                throw new Error('This certificate number already exists. Please use another one.');
            }

            throw new Error('Failed to update the certificate number.');
        }

        return { success: true };
    } catch (err: unknown) {
        return { success: false, error: String(err) || 'An unexpected error occurred.' };
    }
}

export async function sendParticipantCertificate(
    participantId: string,
    programmeId: string
): Promise<{ success: boolean; error?: string }> {
    const supabase = await createClient();

    try {
        // Get participant details
        const { data: participant, error: participantError } = await supabase
            .schema('programme')
            .from('participants')
            .select('name, email, certificate_filename')
            .eq('id', participantId)
            .single();

        if (participantError || !participant) {
            console.error('Failed to fetch participant');
            throw new Error('Failed to fetch participant details.');
        }

        let certificateFilePath = participant.certificate_filename;

        // Generate certificate if not exists
        if (!certificateFilePath) {
            const { data: certData, error: certError } = await supabase.functions.invoke(
                'create-certificate',
                {
                    body: { id: participantId, type: 'PROGRAMME' }
                }
            );

            if (certError || !certData?.result || !certData?.fileName) {
                console.error('Certificate generation failed');
                throw new Error('Failed to generate participant certificate.');
            }

            certificateFilePath = certData.fileName; // Full path
        }

        // Get programme details
        const { data: programme, error: programmeError } = await supabase
            .schema('programme')
            .from('programmes')
            .select('status, name, programme_code')
            .eq('id', programmeId)
            .single();

        if (programmeError || !programme) {
            console.error('Failed to fetch programme:', programmeError);
            throw new Error('Failed to fetch programme details.');
        }

        // Send email
        const { error: emailError } = await supabase.functions.invoke(
            'process-email-event',
            {
                body: {
                    event_type: 'send_certificate_on_programme_completion',
                    event_data: {
                        participant: {
                            email: participant.email,
                            name: participant.name,
                        },
                        programme: {
                            status: programme.status,
                            name: programme.name,
                            code: programme.programme_code,
                        },
                        attachments: {
                            certificate_file_path: certificateFilePath.replace(/\.[^/.]+$/, ''),
                            certificate_file_name: certificateFilePath.split('/').pop() || '',
                        }
                    }
                }
            }
        );

        if (emailError) {
            console.error('Failed to send email:', emailError);
            throw new Error('Failed to send certificate email.');
        }

        return { success: true };
    } catch (err: unknown) {
        return { success: false, error: String(err) || 'An unexpected error occurred.' };
    }
}