'use server'

import { createClient } from '@/utils/supabase/server'
import { AddProgrammeFormData } from '@/hooks/programmes/use-add-programme';
import { EditProgrammeFormData } from '@/hooks/programmes/use-edit-programme';

export async function submitProgramme(data: AddProgrammeFormData): Promise<{ success: boolean, programmeId?: string }> {
    const supabase = await createClient();
    let programmeId: string | undefined;

    try {
        const { schedules, ...programmeFields } = data;

        const fieldMapping: Record<string, string> = {
            name: "name",
            description: "description",
            programmeType: "type",
            programmeFormat: "format",
            venueId: "venue_id",
            departmentId: "organizing_dept_id",
            staffInChargeId: "staff_in_charge_id",
            committeeId: "committee_id",
            secondary_committeeId: "secondary_committee_id",
            remark: "remarks",
            thirdPartyCheck: "has_profit_sharing",
            thirdPartyEntity: "profit_sharing_entity",
            budget_revision_id: "approved_budget_revision_id"
        };

        const dbFields: Record<string, unknown> = {};
        for (const [key, value] of Object.entries(programmeFields)) {
            const mappedKey = fieldMapping[key];
            if (!mappedKey) continue;

            if (value === 'none') {
                dbFields[mappedKey] = null;
            } else if (value !== undefined && value !== null && value !== "") {
                dbFields[mappedKey] = value;
            }
        }

        // Insert new programme
        const { data: insertedProgramme, error: insertProgrammeError } = await supabase
            .schema("programme")
            .from("programmes")
            .insert(dbFields)
            .select("id, programme_code")  // Get the new programme ID for schedule insertion
            .single();

        if (insertProgrammeError || !insertedProgramme) {
            console.log(insertProgrammeError)
            throw new Error("Failed to insert new programme.");
        }

        programmeId = insertedProgramme.id;

        if (!programmeId) throw new Error("Failed to get new programme.")

        // Insert schedules
        if (schedules && schedules.length > 0) {
            const scheduleInserts = schedules.map(schedule => ({
                programme_id: programmeId,
                day_number: schedule.dayNumber,
                date: schedule.date,
                start_time: schedule.startTime,
                end_time: schedule.endTime,
            }));

            const { error: scheduleInsertError } = await supabase
                .schema("programme")
                .from("schedules")
                .insert(scheduleInserts);

            if (scheduleInsertError) {
                throw new Error("Failed to insert schedule data.");
            }
        }

        return { success: true, programmeId: programmeId };
    } catch {
        if (programmeId) {
            // Rollback by deleting the newly created programme
            await supabase
                .schema("programme")
                .from("programmes")
                .delete()
                .eq("id", programmeId);
        }
        return { success: false };
    }
}

export const terminateProgramme = async (programme_id: string): Promise<void> => {
    const supabase = await createClient();
    if (!programme_id) {
        throw new Error("Programme ID is required for terminate Event.");
    }

    const { error } = await supabase
        .schema("programme")
        .from("programmes")
        .update({
            status: "TERMINATED"
        })
        .eq("id", programme_id);

    if (error) {
        throw new Error(`Failed to terminate events: ${error.message}`);
    }
}

export async function updateApprovedEventData(data: Partial<EditProgrammeFormData>): Promise<void> {
    if (!data.id) {
        throw new Error("Programme ID is required to update the programme.");
    }

    const supabase = await createClient();

    const dbFields: Record<string, unknown> = {
        ...(data.name && { name: data.name }),
        ...(data.description && { description: data.description }),
        ...(data.maxParticipants && { max_participants: Number(data.maxParticipants) }),
        ...(data.registrationStartDate && { registration_start_date: data.registrationStartDate.toISOString() }),
        ...(data.registrationEndDate && { registration_end_date: data.registrationEndDate.toISOString() }),
        ...(data.venueId && { venue_id: data.venueId }),
        ...(data.departmentId && { organizing_dept_id: data.departmentId }),
        ...(data.staffInChargeId && { staff_in_charge_id: data.staffInChargeId }),
        ...(data.committeeId && { committee_id: data.committeeId === 'none' ? null : data.committeeId }),
        ...(data.secondary_committeeId && { secondary_committee_id: data.secondary_committeeId === 'none' ? null : data.secondary_committeeId }),
    }

    if (Object.keys(dbFields).length > 0) {
        // Update programme data only if there are fields to update
        const { error: programmeError } = await supabase
            .schema('programme')
            .from("programmes")
            .update(dbFields)
            .eq("id", data.id);

        if (programmeError) {
            throw new Error("Failed to update programme data.");
        }
    }

    // Handle schedules
    if (data.schedules && data.schedules.length > 0) {
        for (const schedule of data.schedules) {
            if (schedule.id) {
                // Update existing schedule
                const scheduleData = {
                    day_number: schedule.dayNumber,
                    date: schedule.date ? schedule.date.toISOString() : null,
                    start_time: schedule.startTime,
                    end_time: schedule.endTime
                };

                const { error: scheduleUpdateError } = await supabase
                    .schema('programme')
                    .from('schedules')
                    .update(scheduleData)
                    .eq('id', schedule.id);

                if (scheduleUpdateError) {
                    throw new Error("Failed to update schedule: " + scheduleUpdateError.message);
                }
            } else {
                // Insert new schedule
                const scheduleData = {
                    programme_id: data.id,
                    day_number: schedule.dayNumber,
                    date: schedule.date ? schedule.date.toISOString() : null,
                    start_time: schedule.startTime,
                    end_time: schedule.endTime
                };

                const { error: scheduleInsertError } = await supabase
                    .schema('programme')
                    .from('schedules')
                    .insert(scheduleData);

                if (scheduleInsertError) {
                    throw new Error("Failed to insert schedule: " + scheduleInsertError.message);
                }
            }
        }
    }

    if (data.programmePoint && data.programmePoint.length > 0) {
        for (const point of data.programmePoint) {
            if (!point.point_type_id || !point.points) {
                continue;
            }
            
            if (point.id) {
                // Update existing programme point
                const { error: pointUpdateError } = await supabase
                    .schema('points')
                    .from('programme_points')
                    .update({
                        point_type_id: point.point_type_id,
                        points: point.points,
                    })
                    .eq('id', point.id);

                if (pointUpdateError) {
                    throw new Error("Failed to update programme point.");
                }
            } else {
                // Insert new programme point
                const { error: pointInsertError } = await supabase
                    .schema('points')
                    .from('programme_points')
                    .insert({
                        programme_id: data.id,
                        point_type_id: point.point_type_id,
                        points: point.points,
                    });

                if (pointInsertError) {
                    throw new Error("Failed to insert programme point.");
                }
            }
        }
    }

    // Handle pricing updates
    if (data.programmePricing && data.programmePricing.length > 0) {
        for (const pricing of data.programmePricing) {
            if (pricing.id) {
                const { error: updatePricingError } = await supabase
                    .schema('programme')
                    .from('pricing')
                    .update({
                        name: pricing.name,
                        description: pricing.description || null,
                        requires_membership: pricing.requires_membership
                    })
                    .eq('id', pricing.id);

                if (updatePricingError) {
                    throw new Error("Failed to update pricing: " + updatePricingError.message);
                }

                if (pricing.requires_membership) {
                    const { data: existingMemberships, error: fetchError } = await supabase
                        .schema('programme')
                        .from('pricing_memberships')
                        .select('membership_type_id')
                        .eq('pricing_id', pricing.id);

                    if (fetchError) {
                        throw new Error("Failed to fetch existing pricing membership types");
                    }

                    const existingIds = existingMemberships?.map(m => m.membership_type_id) || [];
                    const targetIds = pricing.target_membership || [];

                    // Determine what to insert and delete
                    const toInsert = targetIds.filter(id => !existingIds.includes(id));
                    const toDelete = existingIds.filter(id => !targetIds.includes(id));

                    // Insert new ones
                    for (const id of toInsert) {
                        const { error: insertError } = await supabase
                            .schema('programme')
                            .from('pricing_memberships')
                            .insert({
                                pricing_id: pricing.id,
                                membership_type_id: id
                            });
                        if (insertError) {
                            throw new Error(`Failed to insert membership_type_id`);
                        }
                    }

                    // Delete missing ones
                    for (const id of toDelete) {
                        const { error: deleteError } = await supabase
                            .schema('programme')
                            .from('pricing_memberships')
                            .delete()
                            .match({
                                pricing_id: pricing.id,
                                membership_type_id: id
                            });
                        if (deleteError) {
                            throw new Error(`Failed to delete membership_type_id`);
                        }
                    }
                } else {
                    // delete all pricing
                    const { error: deleteError } = await supabase
                        .schema('programme')
                        .from('pricing_memberships')
                        .delete()
                        .match({ pricing_id: pricing.id });

                    if (deleteError) {
                        throw new Error(`Failed to delete pricing membership type`);
                    }
                }
            }
        }
    }
}

export async function updateProgrammeStatus(
    id: string,
    selectedStatus: string,
    startDate?: Date,
    endDate?: Date
): Promise<void> {
    const supabase = await createClient();

    if (!id) {
        throw new Error("Programme ID is required to update the programme.");
    }

    const updateData: Record<string, unknown> = {
        status: selectedStatus,
    };

    // Only add publish dates if both are provided
    if (startDate && endDate) {
        updateData.publish_start_date = startDate;
        updateData.publish_end_date = endDate;
    }

    const { error } = await supabase
        .schema("programme")
        .from("programmes")
        .update(updateData)
        .eq("id", id);

    if (error) {
        throw new Error("Failed to update programme data.");
    }
}

export const deleteEventDetailPoint = async (pointToDelete: { id: string }[]): Promise<void> => {
    const supabase = await createClient();

    if (!pointToDelete || pointToDelete.length === 0) {
        throw new Error("id is required for deleting event point.");
    }

    const ids = pointToDelete.map(item => item.id);

    const { error } = await supabase
        .schema("points")
        .from("programme_points")
        .delete()
        .in("id", ids);

    if (error) {
        throw new Error(`Failed to delete event point: ${error.message}`);
    }
}

export const deleteEventSchedules = async (schedulesToDelete: { id: string }[]): Promise<void> => {
    const supabase = await createClient();

    if (!schedulesToDelete || schedulesToDelete.length === 0) {
        throw new Error("IDs are required for deleting event schedules.");
    }

    const ids = schedulesToDelete.map(item => item.id);

    const { error } = await supabase
        .schema("programme")
        .from("schedules")
        .delete()
        .in("id", ids);

    if (error) {
        throw new Error(`Failed to delete event schedules: ${error.message}`);
    }
}

export async function deleteProgramme(id: string): Promise<boolean> {
    const supabase = await createClient();

    const { error: deleteError } = await supabase
        .schema("programme")
        .from("programmes")
        .delete()
        .eq("id", id);

    if (deleteError) {
        console.error("Failed to delete programme:", deleteError);
        return false;
    }

    return true;
}

export async function setProgrammeArchived(id: string): Promise<boolean> {
    const supabase = await createClient();

    try {
        const { error } = await supabase
            .schema('programme')
            .from('programmes')
            .update({ is_archived: true })
            .eq('id', id)

        if (error) return false

        return true
    } catch {
        return false
    }
}

export async function getProgrammeDetailsWithAttendance(programmeId: string) {
    const supabase = await createClient();

    try {
        // Get programme details
        const { data: programme, error: programmeError } = await supabase
            .schema('programme')
            .from('programmes')
            .select(`
                *,
                venues (*),
                schedules (*)
            `)
            .eq('id', programmeId)
            .single();

        if (programmeError || !programme) {
            throw new Error('Programme not found');
        }

        // Get attendance statistics from attendance_summary
        const { data: attendanceStats, error: attendanceError } = await supabase
            .schema('programme')
            .from('attendance_summary')
            .select('attendance_percentage, attended_sessions, total_sessions, certificate_eligible')
            .eq('programme_id', programmeId);

        if (attendanceError) {
            console.error('Error fetching attendance stats:', attendanceError);
        }

        // Calculate aggregate attendance statistics
        let avgAttendance = 0;
        let totalCertificateEligible = 0;
        let totalAttendedSessions = 0;
        let totalSessionsCount = 0;

        if (attendanceStats && attendanceStats.length > 0) {
            const validStats = attendanceStats.filter(stat => stat.attendance_percentage !== null);
            if (validStats.length > 0) {
                avgAttendance = validStats.reduce((sum, stat) => sum + (stat.attendance_percentage || 0), 0) / validStats.length;
            }
            totalCertificateEligible = attendanceStats.filter(stat => stat.certificate_eligible).length;
            
            // Get the max values for sessions (programme-level stats)
            const sessionStats = attendanceStats.find(stat => stat.total_sessions > 0);
            if (sessionStats) {
                totalAttendedSessions = sessionStats.attended_sessions || 0;
                totalSessionsCount = sessionStats.total_sessions || 0;
            }
        }

        // Get participant counts from registrations
        const { data: registrations, error: registrationsError } = await supabase
            .schema('programme')
            .from('registrations')
            .select('id, status')
            .eq('programme_id', programmeId)
            .eq('status', 'CONFIRMED');

        if (registrationsError) {
            console.error('Error fetching registrations:', registrationsError);
        }

        const enrolledCount = registrations?.length || 0;

        // Get staff, department, and committee details
        const personInCharge: {
            organizingDept: { id: string; name: string } | null;
            staffInCharge: { id: string; name: string } | null;
            committee: { id: string; name: string } | null;
            secondaryCommittee: { id: string; name: string } | null;
        } = {
            organizingDept: null,
            staffInCharge: null,
            committee: null,
            secondaryCommittee: null
        };

        if (programme.organizing_dept_id) {
            const { data: dept } = await supabase
                .schema('organisation')
                .from('departments')
                .select('id, name')
                .eq('id', programme.organizing_dept_id)
                .single();
            if (dept) personInCharge.organizingDept = dept;
        }

        if (programme.staff_in_charge_id) {
            const { data: staff } = await supabase
                .schema('organisation')
                .from('staff')
                .select('id, name')
                .eq('id', programme.staff_in_charge_id)
                .single();
            if (staff) personInCharge.staffInCharge = staff;
        }

        if (programme.committee_id) {
            const { data: committee } = await supabase
                .schema('organisation')
                .from('committees')
                .select('id, name')
                .eq('id', programme.committee_id)
                .single();
            if (committee) personInCharge.committee = committee;
        }

        if (programme.secondary_committee_id) {
            const { data: committee } = await supabase
                .schema('organisation')
                .from('committees')
                .select('id, name')
                .eq('id', programme.secondary_committee_id)
                .single();
            if (committee) personInCharge.secondaryCommittee = committee;
        }

        // Get last session attendance if available
        let lastSessionAttendance = null;
        if (programme.schedules && programme.schedules.length > 0) {
            // Sort schedules by date to get the most recent completed session
            const sortedSchedules = programme.schedules
                .filter((s: any) => s.date && new Date(s.date) < new Date())
                .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime());
            
            if (sortedSchedules.length > 0) {
                const lastSchedule = sortedSchedules[0];
                const { data: lastSessionStats } = await supabase
                    .schema('programme')
                    .from('session_attendance')
                    .select('attendance_rate')
                    .eq('programme_id', programmeId)
                    .eq('schedule_id', lastSchedule.id)
                    .single();
                
                if (lastSessionStats) {
                    lastSessionAttendance = lastSessionStats.attendance_rate;
                }
            }
        }

        return {
            programme: {
                ...programme,
                enrolled: enrolledCount,
                capacity: programme.max_participants || 50,
                venue: programme.venues?.name || programme.venue || 'TBD',
                status: programme.status?.toLowerCase() || 'active'
            },
            attendanceStats: {
                average: avgAttendance,
                lastSession: lastSessionAttendance || avgAttendance,
                certificateEligible: totalCertificateEligible,
                totalSessions: totalSessionsCount || programme.schedules?.length || 0,
                completedSessions: totalAttendedSessions
            },
            personInCharge,
            schedules: programme.schedules || []
        };
    } catch (error) {
        console.error('Error in getProgrammeDetailsWithAttendance:', error);
        throw error;
    }
}