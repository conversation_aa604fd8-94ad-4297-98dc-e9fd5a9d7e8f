'use client'

import { createClient } from '@/utils/supabase/Client'
import {
    ProfitSharingDetail,
    ProgrammeList,
    ProgrammeDetails,
    ProgrammeParticipantInterface,
    ProgrammeRegistrationsInterface,
    ProgrammePricingWithType,
    FileWithPreview,
} from '@/types/programmes/programme.types';

// Form Options
export async function getVenues(): Promise<{ id: string; name: string }[]> {
    const supabase = createClient();


    const { data } = await supabase
        .schema('programme')
        .from('venues')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

    return data || [];
}

export async function getOrganizingDepartment(): Promise<{ id: string; name: string }[]> {
    const supabase = createClient();

    const { data } = await supabase
        .schema("organisation")
        .from("departments")
        .select('id, name')
        .order('name', { ascending: true });

    return data || [];
}

export async function getStaffData(): Promise<{ id: string; name: string; department_id: string; }[]> {
    const supabase = createClient();

    const { data } = await supabase
        .schema("organisation")
        .from("staff")
        .select(`id, name, department_id`)
        .order('name', { ascending: true });

    return data || [];
}

export async function getCommitteesData(): Promise<{ id: string; name: string }[]> {
    const supabase = createClient();

    const { data } = await supabase
        .schema("organisation")
        .from("committees")
        .select('id, name')
        .order('name', { ascending: true });

    return data || [];
}

export async function getPointTypes(): Promise<{ id: string; name: string }[]> {
    const supabase = createClient();

    const { data, error } = await supabase
        .schema('points')
        .from('point_types')
        .select('id, name');

    if (error || !data) {
        return [];
    }

    return data;
}

// Data
export async function getAllProgramme(
    filters: {
        subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    onlyParentProgrammes: boolean,
    onlyArchivedProgrammes: boolean,
    searchTerm?: string,
    sortBy?: string,
    sortDirection?: 'asc' | 'desc'
): Promise<{
    programme: ProgrammeList[] | null;
    success: boolean;
    total: number;
    currentPage: number;
}> {
    const supabase = createClient();

    try {
        // Base query with filters and search term
        let mainQuery = supabase
            .schema("programme")
            .from("programmes_with_details")
            .select('*, schedules(*)', { count: "exact" });

        if (onlyParentProgrammes) {
            mainQuery = mainQuery.is("parent_programme", null);
        }

        mainQuery = onlyArchivedProgrammes
            ? mainQuery.eq("is_archived", true)
            : mainQuery.eq("is_archived", false);

        // Apply filters
        Object.keys(filters.subFilters).forEach((filterKey) => {
            const filterValues = filters.subFilters[filterKey];
            if (Array.isArray(filterValues) && filterValues.length > 0) {
                mainQuery = mainQuery.in(filterKey, filterValues);
            }
        });

        // Apply search term
        if (searchTerm) {
            const normalizedSearchTerm = searchTerm.trim();
            mainQuery = mainQuery.or(
                `programme_code.ilike.%${normalizedSearchTerm}%,` +
                `name.ilike.%${normalizedSearchTerm}%,` +
                `venue_name.ilike.%${normalizedSearchTerm}%,` +
                `staff_in_charge_name.ilike.%${normalizedSearchTerm}%,` +
                `coordinator_name.ilike.%${normalizedSearchTerm}%`
            );
        }

        // Apply sorting based on provided parameters
        if (sortBy) {
            const ascending = sortDirection !== 'desc';
            mainQuery = mainQuery.order(sortBy, { ascending });
        } else {
            // Default sorting by created_at if no sort specified
            mainQuery = mainQuery.order("created_at", { ascending: false });
        }

        // Execute query to get total count and paginated data
        const { data: programmeData, count: total, error } = await mainQuery
            .range((page - 1) * limit, page * limit - 1);

        if (error) throw new Error(error.message);

        if (!programmeData || programmeData.length === 0) {
            return { programme: [], success: true, total: total ?? 0, currentPage: page };
        }

        const enrichedProgrammeData: ProgrammeList[] = programmeData.map(programme => ({
            ...programme,
            programmeSchedules: programme.schedules ?? [],
        }));

        return {
            programme: enrichedProgrammeData,
            success: true,
            total: total ?? 0,
            currentPage: page,
        };
    } catch {
        return { programme: null, success: false, total: 0, currentPage: page };
    }
}

export async function getSpecificProgramme(programme_id: string): Promise<ProgrammeDetails | null> {
    const supabase = createClient();

    // Fetch events data
    const { data: programmeData, error: eventsError } = await supabase
        .schema("programme")
        .from("programmes")
        .select('*, venues(*), pricing(*, pricing_memberships(*)), schedules(*)')
        .eq("id", programme_id)
        .single();

    if (eventsError) {
        return null
    }

    const programmePersonInCharge = {
        organizingDept: { name: '-' },
        staffInCharge: { name: '-' },
        committee: { name: '-' },
    };

    const queries = []

    if (programmeData.organizing_dept_id) {
        queries.push(
            supabase
                .schema('organisation')
                .from('departments')
                .select('id, name')
                .eq('id', programmeData.organizing_dept_id)
                .single()
                .then(({ data, error }) => {
                    if (!error && data) programmePersonInCharge.organizingDept = data;
                })
        );
    }

    if (programmeData.staff_in_charge_id) {
        queries.push(
            supabase
                .schema('organisation')
                .from('staff')
                .select('id, name')
                .eq('id', programmeData.staff_in_charge_id)
                .single()
                .then(({ data, error }) => {
                    if (!error && data) programmePersonInCharge.staffInCharge = data;
                })
        );
    }

    if (programmeData.committee_id) {
        queries.push(
            supabase
                .schema('organisation')
                .from('committees')
                .select('id, name')
                .eq('id', programmeData.committee_id)
                .single()
                .then(({ data, error }) => {
                    if (!error && data) programmePersonInCharge.committee = data;
                })
        );
    }

    await Promise.all(queries);

    // Filter pricing to only include those with is_active === true
    const filteredPricing = Array.isArray(programmeData?.pricing)
        ? programmeData.pricing.filter((p: ProgrammePricingWithType) => p.is_active)
        : programmeData?.pricing;

    return {
        programme: programmeData ? {
            ...programmeData,
            venue: programmeData.venues || null
        } : null,
        programmePricing: filteredPricing || null,
        programmeSchedules: programmeData?.schedules || null,
        programmePersonInCharge
    };
}

export async function getProgrammeCertificateAndImage(
    programmeId: string
): Promise<{
    certificate: FileWithPreview | null;
    image: FileWithPreview | null;
}> {
    const supabase = createClient();

    // Fetch both file paths in one query
    const { data, error } = await supabase
        .schema("programme")
        .from("programmes")
        .select("certificate_template, feature_image_path")
        .eq("id", programmeId)
        .single();

    if (error || !data) {
        return { certificate: null, image: null };
    }

    const result: {
        certificate: FileWithPreview | null;
        image: FileWithPreview | null;
    } = {
        certificate: null,
        image: null,
    };

    // Load certificate (private signed URL) 5 minutes
    if (data.certificate_template) {
        const { data: signedUrlData } = await supabase.storage
            .from("form_templates")
            .createSignedUrl(data.certificate_template, 300);

        const signedUrl = signedUrlData?.signedUrl;
        if (signedUrl) {
            const res = await fetch(signedUrl);
            const blob = await res.blob();

            const file = new File(
                [blob],
                data.certificate_template.split("/").pop() || "certificate",
                { type: blob.type }
            );

            result.certificate = Object.assign(file, {
                isExisting: true,
                path: data.certificate_template,
                preview: signedUrl
            });
        }
    }

    // Load image (public URL)
    if (data.feature_image_path) {
        const { data: publicUrlData } = supabase.storage
            .from("programme_images")
            .getPublicUrl(data.feature_image_path);

        const publicUrl = publicUrlData?.publicUrl;
        if (publicUrl) {
            const res = await fetch(publicUrl);
            const blob = await res.blob();

            const file = new File(
                [blob],
                data.feature_image_path.split("/").pop() || "image",
                { type: blob.type }
            );

            result.image = Object.assign(file, {
                isExisting: true,
                path: data.feature_image_path,
                preview: publicUrl
            });
        }
    }

    return result;
}

export async function getProfitSharingDetails(programme_id: string): Promise<ProfitSharingDetail | null> {
    const supabase = createClient();

    const { data: programmeData, error: programmeError } = await supabase
        .schema('programme')
        .from('programmes')
        .select('has_profit_sharing, profit_sharing_entity, prepared_by, approved_by')
        .eq('id', programme_id)
        .single();

    if (programmeError || !programmeData) {
        return null;
    }

    let preparedByUser = null;
    let approvedByUser = null;

    if (programmeData.prepared_by) {
        const { data: preparedData } = await supabase
            .from('user_meta')
            .select('email')
            .eq('id', programmeData.prepared_by)
            .single();
        preparedByUser = preparedData;
    }

    if (programmeData.approved_by) {
        const { data: approvedData } = await supabase
            .from('user_meta')
            .select('email')
            .eq('id', programmeData.approved_by)
            .single();
        approvedByUser = approvedData;
    }

    return {
        has_profit_sharing: programmeData.has_profit_sharing,
        profit_sharing_entity: programmeData.profit_sharing_entity,
        prepared_by: programmeData.prepared_by,
        approved_by: programmeData.approved_by,
        prepared_by_name: preparedByUser?.email || null,
        approved_by_name: approvedByUser?.email || null
    };
}

export async function getProgrammePoints(id: string): Promise<{ id?: string; point_type_id: string; points: number; }[]> {
    const supabase = createClient();

    const { data: programmeData, error: programmeError } = await supabase
        .schema('points')
        .from('programme_points')
        .select(`
          id,
          point_type_id,
          points
        `)
        .eq('programme_id', id);

    if (programmeError || !programmeData) {
        return [];
    }

    // Transform the data to match programmePointInterface
    const transformedData = programmeData.map(item => ({
        id: item.id,
        point_type_id: item.point_type_id || '',
        points: item.points
    }));

    return transformedData;
}

export async function getProgrammeParticipants(
    id: string,
    filters: {
        subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    isProgrammeLevel: boolean,
    searchTerm?: string
): Promise<{
    participants: ProgrammeParticipantInterface[] | null;
    success: boolean;
    total: number;
    currentPage: number;
}> {
    const supabase = createClient();

    if (!id) {
        console.error("Invalid ID provided");
        return { participants: null, success: false, total: 0, currentPage: 0 };
    }

    const currentPage = page < 1 ? 1 : page;

    let mainQuery = supabase
        .schema('programme')
        .from('programme_participants')
        .select('*', { count: "exact" });

    mainQuery = mainQuery.eq(isProgrammeLevel ? "programme_id" : "registration_id", id);

    // Apply sub-filters
    Object.keys(filters.subFilters).forEach((filterKey) => {
        const filterValues = filters.subFilters[filterKey];
        if (Array.isArray(filterValues) && filterValues.length > 0) {
            mainQuery = mainQuery.in(filterKey, filterValues);
        }
    });

    // Apply search term
    if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim();
        mainQuery = mainQuery.or(
            `name.ilike.%${normalizedSearchTerm}%,email.ilike.%${normalizedSearchTerm}%,contact_no.ilike.%${normalizedSearchTerm}%,identification_no.ilike.%${normalizedSearchTerm}%`
        );
    }

    const { data: participants, count: total, error } = await mainQuery
        .order("name")
        .range((currentPage - 1) * limit, currentPage * limit - 1);

    if (error) {
        console.error("Error fetching programme participants:", error);
        return { participants: null, success: false, total: 0, currentPage: 0 };
    }

    if (!participants) {
        return { participants: [], success: true, total: total ?? 0, currentPage: 0 };
    }

    // Extract unique codes to reduce repeated DB calls
    const salutations = Array.from(new Set(participants.map(p => p.salutation).filter(Boolean)));
    const nationalities = Array.from(new Set(participants.map(p => p.nationality).filter(Boolean)));
    const idTypes = Array.from(new Set(participants.map(p => p.identification_type).filter(Boolean)));

    // Fetch mappings from system.options
    const [salutationRes, nationalityRes, idTypeRes] = await Promise.all([
        supabase.schema('system').from('options').select('code, label').eq('type', 'salutation').in('code', salutations),
        supabase.schema('system').from('options').select('code, label').eq('type', 'country').in('code', nationalities),
        supabase.schema('system').from('options').select('code, label').eq('type', 'id_type').in('code', idTypes),
    ]);

    const salutationMap = Object.fromEntries((salutationRes.data || []).map(opt => [opt.code, opt.label]));
    const nationalityMap = Object.fromEntries((nationalityRes.data || []).map(opt => [opt.code, opt.label]));
    const idTypeMap = Object.fromEntries((idTypeRes.data || []).map(opt => [opt.code, opt.label]));

    // Map participant codes to labels
    const enrichedParticipants = participants.map(p => ({
        ...p,
        salutation: salutationMap[p.salutation] || p.salutation,
        nationality: nationalityMap[p.nationality] || p.nationality,
        identification_type: idTypeMap[p.identification_type] || p.identification_type,
    }));

    return {
        participants: enrichedParticipants,
        success: true,
        total: total ?? 0,
        currentPage,
    };
}

export async function getProgrammeRegistrations(
    id: string,
    filters: {
        subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    searchTerm?: string
): Promise<{
    registrations: ProgrammeRegistrationsInterface[] | null;
    success: boolean;
    total: number;
    currentPage: number;
}> {
    const supabase = createClient();

    if (!id) {
        console.error("Invalid ID provided");
        return { registrations: null, success: false, total: 0, currentPage: 0 };
    }

    const currentPage = page < 1 ? 1 : page;

    let mainQuery = supabase
        .schema('programme')
        .from('registrations')
        .select(`
            *,
            contact_persons(*),
            table_registrations(*)
        `, { count: "exact" })
        .eq('programme_id', id);

    // Reapply filters on the main query
    Object.keys(filters.subFilters).forEach((filterKey) => {
        const filterValues = filters.subFilters[filterKey];
        if (Array.isArray(filterValues) && filterValues.length > 0) {
            mainQuery = mainQuery.in(filterKey, filterValues);
        }
    });

    // Apply search term filter
    if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim();

        // For searching within nested contact_persons, we need to use a different approach
        // We'll search in the main table and filter results after fetching
        mainQuery = mainQuery.or(
            `remarks.ilike.%${normalizedSearchTerm}%`
        );
    }

    // Execute main query
    const { data: registrations, count: total, error } = await mainQuery
        .order("created_at", { ascending: false })
        .range((currentPage - 1) * limit, currentPage * limit - 1);

    if (error) {
        console.error("Error fetching programme registrations:", error);
        return { registrations: null, success: false, total: 0, currentPage: 0 };
    }

    if (!registrations) {
        return { registrations: [], success: true, total: 0, currentPage: 0 };
    }

    // Fetch participant counts for each registration
    const registrationIds = registrations.map(r => r.id);
    const { data: participantCounts } = await supabase
        .schema('programme')
        .from('participants')
        .select('registration_id')
        .in('registration_id', registrationIds);

    // Create a map of registration_id to participant count
    const participantCountMap: { [key: string]: number } = {};
    if (participantCounts) {
        participantCounts.forEach(p => {
            participantCountMap[p.registration_id] = (participantCountMap[p.registration_id] || 0) + 1;
        });
    }

    // Add participant counts to registrations
    const registrationsWithCounts = registrations.map(reg => ({
        ...reg,
        participants: [{ count: participantCountMap[reg.id] || 0 }]
    }));

    // Apply client-side filtering for contact person search
    let filteredRegistrations = registrationsWithCounts;
    if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim().toLowerCase();
        filteredRegistrations = registrationsWithCounts.filter(reg => {
            // Check remarks
            if (reg.remarks?.toLowerCase().includes(normalizedSearchTerm)) return true;

            // Check contact person details
            if (reg.contact_persons) {
                const cp = reg.contact_persons;
                if (cp.name?.toLowerCase().includes(normalizedSearchTerm)) return true;
                if (cp.email?.toLowerCase().includes(normalizedSearchTerm)) return true;
                if (cp.company_name?.toLowerCase().includes(normalizedSearchTerm)) return true;
                if (cp.contact_no?.includes(normalizedSearchTerm)) return true;
            }

            // Check registration ID
            if (reg.id?.toLowerCase().includes(normalizedSearchTerm)) return true;

            return false;
        });
    }

    return {
        registrations: filteredRegistrations,
        success: true,
        total: searchTerm ? filteredRegistrations.length : (total ?? 0),
        currentPage,
    };
}

// file upload
export async function uploadProgrammeImage(image: FileWithPreview, id: string): Promise<void> {
    const supabase = createClient();

    // Step 1: Get current programme image
    const { data: programmeData, error: programmeError } = await supabase
        .schema("programme")
        .from("programmes")
        .select("feature_image_path")
        .eq("id", id)
        .single();

    if (programmeError || !programmeData) {
        console.error("Error fetching programme data:", programmeError);
        throw new Error("Failed to fetch programme data.");
    }

    const currentFeatureImagePath = programmeData.feature_image_path;

    // Step 2: Delete old image if exists
    if (currentFeatureImagePath) {
        const { error: deleteError } = await supabase.storage
            .from("programme_images")
            .remove([currentFeatureImagePath]);

        if (deleteError) {
            console.error("Error deleting old image:", deleteError);
            throw new Error("Failed to delete old image.");
        }
    }

    // Step 3: Upload new image
    const file = image as File;

    const timestamp = new Date().toISOString();
    const fileExtension = file.name.split(".").pop();
    const timestampedFileName = `event-image-${timestamp}.${fileExtension}`;
    const filePath = `${id}/${timestampedFileName}`;

    const { error: uploadError } = await supabase.storage
        .from("programme_images")
        .upload(filePath, file, {
            upsert: true,
        });

    if (uploadError) {
        console.error("Error uploading image:", uploadError);
        throw new Error("Failed to upload image.");
    }

    // Step 4: Update programme DB
    const { error: updateError } = await supabase
        .schema("programme")
        .from("programmes")
        .update({ "feature_image_path": filePath })
        .eq("id", id);

    if (updateError) {
        console.error("Error updating programme record:", updateError);
        throw new Error("Failed to update programme with new image.");
    }
}

export async function uploadProgrammeCertificate(certificate: FileWithPreview, id: string): Promise<void> {
    const supabase = createClient();

    const file = certificate as File;

    // Create timestamped file name (safe for S3 paths)
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileExtension = file.name.split(".").pop();
    const timestampedFileName = `certificate-${timestamp}.${fileExtension}`;
    const filePath = `programme-certificate/${id}/${timestampedFileName}`;

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
        .from("form_templates")
        .upload(filePath, file, {
            upsert: true, // overwrite if exists
        });

    if (uploadError) {
        console.error("Error uploading certificate:", uploadError);
        throw new Error("Failed to upload certificate.");
    }

    // Update the programme record with the new certificate path
    const { error: updateError } = await supabase
        .schema("programme")
        .from("programmes")
        .update({ certificate_template: filePath })
        .eq("id", id);

    if (updateError) {
        console.error("Error updating programme record:", updateError);
        throw new Error("Failed to update programme with new certificate.");
    }
}

export async function deleteProgrammeCertificate(id: string, path: string) {
    const supabase = await createClient();

    try {
        // 1. Delete the certificate file from storage
        const { error: storageError } = await supabase.storage
            .from('form_templates')
            .remove([path]);

        if (storageError) {
            return { success: false, error: `Failed deleting document from storage` };
        }

        // 2. Update the programme row to nullify the certificate_template
        const { error: updateError } = await supabase
            .schema('programme')
            .from('programmes')
            .update({ certificate_template: null })
            .eq('id', id);

        if (updateError) {
            return { success: false, error: `Failed updating programme record` };
        }

        return { success: true };
    } catch (error) {
        return { success: false, error: "An unexpected error occurred, please try again later." };
    }
}

export async function transferRegistration(
    registrationId: string,
    sourceProgrammeId: string,
    targetProgrammeId: string
): Promise<{ success: boolean; error?: string }> {
    const supabase = createClient();

    try {
        // Validate inputs
        if (!registrationId || !sourceProgrammeId || !targetProgrammeId) {
            return { success: false, error: "Missing required parameters" };
        }

        if (sourceProgrammeId === targetProgrammeId) {
            return { success: false, error: "Cannot transfer to the same programme" };
        }

        // Check if target programme exists
        const { data: targetProgramme, error: targetError } = await supabase
            .schema('programme')
            .from('programmes')
            .select('id, status, max_participants')
            .eq('id', targetProgrammeId)
            .single();

        if (targetError || !targetProgramme) {
            return { success: false, error: "Target programme not found" };
        }

        // Allow transfers to programmes that are not terminated or cancelled
        if (targetProgramme.status === 'TERMINATED' || targetProgramme.status === 'CANCELLED') {
            return { success: false, error: "Cannot transfer to a terminated or cancelled programme" };
        }

        // Check if registration exists
        const { data: registration, error: regError } = await supabase
            .schema('programme')
            .from('registrations')
            .select('id, status')
            .eq('id', registrationId)
            .eq('programme_id', sourceProgrammeId)
            .single();

        if (regError || !registration) {
            return { success: false, error: "Registration not found" };
        }

        // Update the registration's programme_id
        const { error: updateError } = await supabase
            .schema('programme')
            .from('registrations')
            .update({ programme_id: targetProgrammeId })
            .eq('id', registrationId);

        if (updateError) {
            console.error("Error transferring registration:", updateError);
            return { success: false, error: "Failed to transfer registration" };
        }

        return { success: true };
    } catch (error) {
        console.error("Unexpected error in transferRegistration:", error);
        return { success: false, error: "An unexpected error occurred" };
    }
}

export async function getProgrammeRegistrationsCount(
    programmeId: string
): Promise<number> {
    const supabase = createClient();

    try {
        const { count, error } = await supabase
            .schema('programme')
            .from('registrations')
            .select('*', { count: 'exact', head: true })
            .eq('programme_id', programmeId)
            .neq('status', 'CANCELLED');

        if (error) {
            console.error("Error fetching registration count:", error);
            return 0;
        }

        return count || 0;
    } catch (error) {
        console.error("Unexpected error in getProgrammeRegistrationsCount:", error);
        return 0;
    }
}