import { createClient } from '@/utils/supabase/Client'
import type {
  ApprovalTableRow,
  ApprovalListResponse,
  ApprovalRequestWithDetails,
  ApprovalDetailsResponse,
  ApprovalDecision,
  ApprovalActionResult,
  ApprovalFilters,
  CurrentApprover
} from '@/types/approvals/approval.types'
import { approvalActionHistory } from '@/types/approvals/approval.types';

export class ApprovalService {
  // ==========================================
  // Fetch all approval requests with pagination and filters
  // ==========================================
  static async getAllApprovalRequests(
    filters: ApprovalFilters,
    page: number,
    limit: number,
    searchTerm?: string
  ): Promise<ApprovalListResponse> {
    const supabase = await createClient();

    try {
      // Build base query for counting total records
      let countQuery = supabase
        .schema('approval')
        .from('approval_requests')
        .select('id', { count: 'exact' });

      // Apply filters to count query
      if (filters.status && filters.status.length > 0) {
        countQuery = countQuery.in('status', filters.status);
      }

      if (filters.module_type && filters.module_type.length > 0) {
        countQuery = countQuery.in('module_type', filters.module_type);
      }

      if (filters.date_range?.from && filters.date_range?.to) {
        countQuery = countQuery
          .gte('submitted_at', filters.date_range.from)
          .lte('submitted_at', filters.date_range.to);
      }

      // Apply search term filter if present
      if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim();
        countQuery = countQuery.or(
          `request_number.ilike.%${normalizedSearchTerm}%,module_type.ilike.%${normalizedSearchTerm}%`
        );
      }

      // Get total count
      const { count: total, error: totalError } = await countQuery;
      if (totalError) {
        throw new Error(`Failed to count approval requests: ${totalError.message}`);
      }

      // Handle case where no requests match
      if (!total) {
        return {
          data: [],
          total: 0,
          page: 1,
          pageSize: limit,
          totalPages: 0
        };
      }

      // Handle pagination
      const totalPages = Math.ceil(total / limit);
      const adjustedPage = Math.min(totalPages, Math.max(1, page));
      const start = (adjustedPage - 1) * limit;
      const end = start + limit - 1;

      // Main query to fetch approval requests with corrected foreign key references
      let mainQuery = supabase
        .schema('approval')
        .from('approval_requests')
        .select(`
          id,
          request_number,
          module_type,
          status,
          submitted_by,
          submitted_at
        `)
        .range(start, end)
        .order('submitted_at', { ascending: false });

      // Reapply filters on the main query
      if (filters.status && filters.status.length > 0) {
        mainQuery = mainQuery.in('status', filters.status);
      }

      if (filters.module_type && filters.module_type.length > 0) {
        mainQuery = mainQuery.in('module_type', filters.module_type);
      }

      if (filters.date_range?.from && filters.date_range?.to) {
        mainQuery = mainQuery
          .gte('submitted_at', filters.date_range.from)
          .lte('submitted_at', filters.date_range.to);
      }

      // Apply search term filter if present
      if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim();
        mainQuery = mainQuery.or(
          `request_number.ilike.%${normalizedSearchTerm}%,module_type.ilike.%${normalizedSearchTerm}%`
        );
      }

      const { data: requestsData, error: requestsError } = await mainQuery;
      if (requestsError) {
        throw new Error(`Failed to fetch approval requests: ${requestsError.message}`);
      }

      if (!requestsData || requestsData.length === 0) {
        return {
          data: [],
          total,
          page: adjustedPage,
          pageSize: limit,
          totalPages
        };
      }

      // Fetch user data only
      const userIds = Array.from(new Set(requestsData.map(r => r.submitted_by).filter(Boolean)));

      // Fetch user emails from user_meta table
      const { data: usersData, error: usersError } = await supabase
        .from('user_meta')
        .select('id, email')
        .in('id', userIds);

      // Create user lookup map
      const userMap = new Map();

      // Populate user map with email data
      if (usersData && usersData.length > 0) {
        usersData.forEach(user => {
          userMap.set(user.id, user.email || user.id || 'Unknown User');
        });
      }

      // Transform data for table display
      const enrichedRequests: ApprovalTableRow[] = requestsData.map((request: any) => {
        const userEmail = userMap.get(request.submitted_by);

        return {
          id: request.id,
          request_number: request.request_number ?? '-',
          module_type: request.module_type ?? '-',
          status: request.status,
          submitted_by: request.submitted_by ?? '-',
          submitted_by_name: userEmail || request.submitted_by || 'Unknown User',
          submitted_at: request.submitted_at,
          priority: 0,
          data_summary: this.generateDataSummary(request.module_type, null)
        };
      });

      return {
        data: enrichedRequests,
        total,
        page: adjustedPage,
        pageSize: limit,
        totalPages
      };
    } catch (error) {
      console.error('Error fetching approval requests:', error);
      return {
        data: [],
        total: 0,
        page: page,
        pageSize: limit,
        totalPages: 0
      };
    }
  }

  // ==========================================
  // Get approval request by ID with full details using optimized view
  // ==========================================
  static async getApprovalRequestById(id: string): Promise<ApprovalDetailsResponse | null> {
    const supabase = await createClient();

    try {
      // Use the optimized view for comprehensive data
      const { data: viewData, error: viewError } = await supabase
        .schema('approval')
        .from('approval_requests_view')
        .select('*')
        .eq('request_id', id)
        .single();

      if (viewError || !viewData) {
        console.error('Failed to fetch approval request from view:', viewError?.message);
        return null;
      }

      // Fetch the basic request data for backwards compatibility
      const { data: requestData, error: requestError } = await supabase
        .schema('approval')
        .from('approval_requests')
        .select('*')
        .eq('id', id)
        .single();

      if (requestError || !requestData) {
        console.error('Failed to fetch approval request:', requestError?.message);
        return null;
      }

      // Fetch related data separately
      const [workflowData, stageData, actionsData, allStagesData] = await Promise.all([
        // Fetch workflow
        requestData.workflow_id ? supabase
          .schema('approval')
          .from('approval_workflows')
          .select('*')
          .eq('id', requestData.workflow_id)
          .single() : { data: null },

        // Fetch current stage
        requestData.current_stage_id ? supabase
          .schema('approval')
          .from('approval_stages')
          .select('*')
          .eq('id', requestData.current_stage_id)
          .single() : { data: null },

        // Fetch actions
        supabase
          .schema('approval')
          .from('approval_actions')
          .select('*')
          .eq('request_id', id)
          .order('action_at', { ascending: true }),

        // Fetch all workflow stages for timeline
        requestData.workflow_id ? supabase
          .schema('approval')
          .from('approval_stages')
          .select('*')
          .eq('workflow_id', requestData.workflow_id)
          .order('stage_order', { ascending: true }) : { data: [] }
      ]);

      // Get user information for submitted_by and approver_id users
      const userIds = [
        requestData.submitted_by,
        ...(actionsData.data?.map((action: any) => action.approver_id) || [])
      ].filter(Boolean);

      const uniqueUserIds = Array.from(new Set(userIds));

      interface UserMeta {
        id: string;
        email?: string;
        full_name?: string;
      }

      let usersData: UserMeta[] = [];
      if (uniqueUserIds.length > 0) {
        const { data: userData, error: userError } = await supabase
          .from('user_meta')
          .select('id, email, full_name')
          .in('id', uniqueUserIds);

        if (!userError && userData) {
          usersData = userData;
        }
      }

      // Create user lookup map
      const userMap = new Map();
      usersData.forEach((user: any) => {
        userMap.set(user.id, {
          id: user.id,
          email: user.email,
          name: user.full_name || user.email || 'Unknown User'
        });
      });

      // Get submitted by user info
      const submittedByUser = userMap.get(requestData.submitted_by) || {
        id: requestData.submitted_by,
        email: requestData.submitted_by,
        name: 'Unknown User'
      };

      // Transform actions with user info
      const enrichedActions = actionsData.data?.map((action: any) => {
        const approverUser = userMap.get(action.approver_id);
        return {
          ...action,
          actioned_by: action.approver_id, // For backward compatibility
          actioned_by_name: approverUser?.name || 'Unknown User',
          actioned_at: action.action_at // Use the correct database field name
        };
      }) || [];

      // Transform the data with enhanced parallel approval information
      const transformedData: ApprovalRequestWithDetails = {
        ...requestData,
        workflow: workflowData.data || undefined,
        current_stage: stageData.data || undefined,
        submitted_by_user: submittedByUser,
        actions: enrichedActions,
        workflow_stages: allStagesData.data || [],
        // Add parallel approval data from view
        current_approvers: viewData.current_approvers || [],
        parallel_approvals_received: viewData.parallel_approvals_received,
        parallel_approvals_required: viewData.parallel_approvals_required,
        current_stage_is_parallel: viewData.current_stage_is_parallel,
        active_delegations: viewData.active_delegations || [],
        recent_actions: viewData.recent_actions || [],
        urgency_status: viewData.urgency_status
      };

      return { data: transformedData };
    } catch (error) {
      console.error('Error fetching approval request details:', error);
      return null;
    }
  }

  // ==========================================
  // Submit approval decision (approve/reject) - Legacy method
  // ==========================================
  static async submitApprovalDecision({
    requestId,
    action,
    comments
  }: ApprovalDecision): Promise<ApprovalActionResult> {
    return this.processApprovalAction({
      requestId,
      userId: '',
      action,
      comments
    });
  }

  // ==========================================
  // Process approval action using RPC method (NEW)
  // ==========================================
  static async processApprovalAction({
    requestId,
    userId,
    action,
    comments,
    stageId
  }: {
    requestId: string;
    userId: string;
    action: 'approve' | 'reject' | 'request_info' | 'delegate';
    comments?: string;
    stageId?: string; // If not provided, we'll determine it automatically
  }): Promise<ApprovalActionResult> {
    const supabase = await createClient();

    try {
      // Get current user if userId not provided
      let currentUserId = userId;
      if (!currentUserId) {
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
          return {
            success: false,
            error: 'Authentication required'
          };
        }
        currentUserId = user.id;
      }

      // Get stage ID if not provided
      let targetStageId = stageId;
      if (!targetStageId) {
        const stageIdResult = await this.getCurrentStageForApproval(requestId, currentUserId);
        targetStageId = stageIdResult === null ? undefined : stageIdResult;
        if (!targetStageId) {
          return {
            success: false,
            error: 'Cannot determine which stage you can approve. You may not have permission or have already acted.'
          };
        }
      }

      // Use the RPC method with required stage_id parameter
      const { data: result, error } = await supabase
        .schema('approval')
        .rpc('process_approval_action', {
          p_request_id: requestId,
          p_user_id: currentUserId,
          p_action: action.toUpperCase(),
          p_stage_id: targetStageId, // Now required!
          p_comments: comments || null
        });

      if (error) {
        console.error('RPC error:', error);

        // Handle specific error types
        if (error.message.includes('already acted')) {
          return {
            success: false,
            error: 'You have already acted on this request'
          };
        } else if (error.message.includes('permission')) {
          return {
            success: false,
            error: 'You do not have permission to approve this request'
          };
        } else if (error.message.includes('not found')) {
          return {
            success: false,
            error: 'Approval request not found'
          };
        } else {
          return {
            success: false,
            error: `Action failed: ${error.message}`
          };
        }
      }

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('Error processing approval action:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // ==========================================
  // Get current stage ID for user approval (for multi-role support)
  // ==========================================
  static async getCurrentStageForApproval(requestId: string, userId: string): Promise<string | null> {
    const supabase = await createClient();

    try {
      // Get the current request details from the optimized view
      const { data: request, error } = await supabase
        .schema('approval')
        .from('approval_requests_view')
        .select('current_stage_id, current_approvers')
        .eq('request_id', requestId)
        .single();

      if (error || !request) {
        console.error('Failed to fetch request for stage determination:', error?.message);
        return null;
      }

      // Find the stage this user can approve
      const userApproverStage = request.current_approvers?.find(
        (approver: any) => approver.user_id === userId && !approver.has_acted
      );

      if (!userApproverStage) {
        console.warn('User cannot approve this request or has already acted');
        return null;
      }

      return userApproverStage.stage_id;
    } catch (error) {
      console.error('Error determining current stage for approval:', error);
      return null;
    }
  }

  // ==========================================
  // Get user's available approval options (for multi-role users)
  // ==========================================
  static async getUserApprovalOptions(requestId: string, userId: string): Promise<CurrentApprover[]> {
    const supabase = await createClient();

    try {
      const { data: request, error } = await supabase
        .schema('approval')
        .from('approval_requests_view')
        .select('current_approvers')
        .eq('request_id', requestId)
        .single();

      if (error || !request) {
        console.error('Failed to fetch approval options:', error?.message);
        return [];
      }

      // Filter to stages this user can approve
      const userOptions = request.current_approvers?.filter(
        (approver: any) => approver.user_id === userId && !approver.has_acted
      ) || [];

      return userOptions;
    } catch (error) {
      console.error('Error getting user approval options:', error);
      return [];
    }
  }

  // ==========================================
  // Check if user can approve a specific request
  // ==========================================
  static async canUserApprove(requestId: string, userId: string): Promise<boolean> {
    const supabase = await createClient();

    try {
      // Try to call the RPC function first
      const { data, error } = await supabase
        .rpc('can_user_approve', {
          p_request_id: requestId,
          p_user_id: userId
        });

      if (error) {
        // If the RPC function doesn't exist, implement basic permission check
        console.warn('can_user_approve RPC function not found, implementing fallback logic');

        // Get the approval request details
        const { data: requestData } = await supabase
          .schema('approval')
          .from('approval_requests')
          .select('status, current_stage_id')
          .eq('id', requestId)
          .single();

        // Basic check: user can approve if request is pending
        return requestData?.status === 'pending';
      }

      return data || false;
    } catch (error) {
      console.error('Error checking approval permissions:', error);
      // For development, default to true to allow testing
      return true;
    }
  }

  // ==========================================
  // Get pending approvals for current user
  // ==========================================
  static async getPendingApprovalsForUser(userId: string): Promise<ApprovalTableRow[]> {
    const supabase = await createClient();

    try {
      // This would need to be implemented based on your business logic
      // For now, we'll get all pending approvals
      const response = await this.getAllApprovalRequests(
        { status: ['pending'] },
        1,
        50
      );

      return response.data;
    } catch (error) {
      console.error('Error fetching pending approvals for user:', error);
      return [];
    }
  }

  // ==========================================
  // Generate summary text for different module types
  // ==========================================
  private static generateDataSummary(moduleType: string, data: any): string {
    if (!data) {
      // Return a generic summary based on module type when data is not available
      switch (moduleType.toLowerCase()) {
        case 'budget':
          return 'Budget request';
        case 'expense':
          return 'Expense request';
        case 'leave':
          return 'Leave request';
        case 'programme':
          return 'Programme request';
        case 'application':
          return 'Application request';
        default:
          return 'Approval request';
      }
    }

    try {
      switch (moduleType.toLowerCase()) {
        case 'budget':
          return `Amount: ${data.total_amount || data.amount || 'N/A'}`;
        case 'expense':
          return `Expense: ${data.amount || 'N/A'} - ${data.description || 'N/A'}`;
        case 'leave':
          return `${data.leave_type || 'Leave'}: ${data.start_date || 'N/A'} to ${data.end_date || 'N/A'}`;
        case 'programme':
          return `Programme: ${data.name || data.title || 'N/A'}`;
        case 'application':
          return `Application: ${data.application_type || data.type || 'N/A'}`;
        default:
          // Try to extract meaningful information from the data object
          const keys = Object.keys(data);
          const meaningfulKeys = ['name', 'title', 'amount', 'description', 'type'];
          const relevantKey = keys.find(key => meaningfulKeys.includes(key.toLowerCase()));
          return relevantKey ? `${relevantKey}: ${data[relevantKey]}` : 'Data available';
      }
    } catch (error) {
      return 'Data available';
    }
  }

  // ==========================================
  // Get approval statistics for dashboard
  // ==========================================
  static async getApprovalStatistics(): Promise<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  }> {
    const supabase = await createClient();

    try {
      const { data, error } = await supabase
        .schema('approval')
        .from('approval_requests')
        .select('status');

      if (error) {
        throw new Error(`Failed to fetch approval statistics: ${error.message}`);
      }

      const stats = {
        total: data?.length || 0,
        pending: 0,
        approved: 0,
        rejected: 0
      };

      data?.forEach((request: any) => {
        switch (request.status) {
          case 'pending':
            stats.pending++;
            break;
          case 'approved':
            stats.approved++;
            break;
          case 'rejected':
            stats.rejected++;
            break;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error fetching approval statistics:', error);
      return { total: 0, pending: 0, approved: 0, rejected: 0 };
    }
  }

  static async getApprovalHistory(request_ids: string[]): Promise<approvalActionHistory[] | null> {
    const supabase = await createClient()

    const { data, error } = await supabase
      .schema("approval")
      .from("approval_actions")
      .select("*")
      .in("request_id", request_ids)

    if (error || !data) {
      return null
    }

    return data
  }
}