import { createClient } from "@/utils/supabase/Client";

export interface BudgetItem {
  id: string;
  budget_id: string;
  item_code: string;
  item_name: string;
  description?: string;
  category: string;
  gl_account_code?: string;
  budgeted_amount: number;
  spent_amount: number;
  committed_amount: number;
  available_amount: number;
  is_active: boolean;
  entity_id?: string;
  cost_center?: string;
  created_at: string;
  updated_at: string;
  
  // Related data
  budget?: {
    id: string;
    budget_name: string;
    fiscal_year: number;
    status: string;
  };
  entity?: {
    id: string;
    name: string;
  };
}

export interface BudgetItemsResponse {
  data: BudgetItem[];
  total: number;
  page: number;
  limit: number;
}

export interface BudgetItemFilters {
  search?: string;
  budget_id?: string;
  entity_id?: string;
  category?: string;
  gl_account_code?: string;
  cost_center?: string;
  is_active?: boolean;
  has_available_budget?: boolean;
  page?: number;
  limit?: number;
}

/**
 * Fetch budget items with optional filtering and pagination
 */
export async function getBudgetItems(filters: BudgetItemFilters = {}): Promise<BudgetItemsResponse> {
  const supabase = await createClient();
  
  try {
    const {
      search,
      budget_id,
      entity_id,
      category,
      gl_account_code,
      cost_center,
      is_active = true,
      has_available_budget,
      page = 1,
      limit = 100
    } = filters;

    let query = supabase
      .from('budget_items')
      .select(`
        *,
        budget:budget_id (
          id,
          budget_name,
          fiscal_year,
          status
        ),
        entity:entity_id (
          id,
          name
        )
      `, { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`item_code.ilike.%${search}%,item_name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (budget_id) {
      query = query.eq('budget_id', budget_id);
    }

    if (entity_id) {
      query = query.eq('entity_id', entity_id);
    }

    if (category) {
      query = query.eq('category', category);
    }

    if (gl_account_code) {
      query = query.eq('gl_account_code', gl_account_code);
    }

    if (cost_center) {
      query = query.eq('cost_center', cost_center);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active);
    }

    if (has_available_budget) {
      query = query.gt('available_amount', 0);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by item code
    query = query.order('item_code', { ascending: true });

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching budget items:', error);
      throw new Error(`Failed to fetch budget items: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
    };
  } catch (error) {
    console.error('Error in getBudgetItems:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching budget items');
  }
}

/**
 * Get budget item by ID
 */
export async function getBudgetItemById(id: string): Promise<BudgetItem | null> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .from('budget_items')
      .select(`
        *,
        budget:budget_id (
          id,
          budget_name,
          fiscal_year,
          status
        ),
        entity:entity_id (
          id,
          name
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      console.error('Error fetching budget item:', error);
      throw new Error(`Failed to fetch budget item: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in getBudgetItemById:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching budget item');
  }
}

/**
 * Search budget items for dropdown/autocomplete
 */
export async function searchBudgetItems(searchTerm: string, limit: number = 50): Promise<BudgetItem[]> {
  const supabase = await createClient();
  
  try {
    let query = supabase
      .from('budget_items')
      .select(`
        *,
        budget:budget_id (
          id,
          budget_name,
          fiscal_year,
          status
        ),
        entity:entity_id (
          id,
          name
        )
      `)
      .eq('is_active', true)
      .gt('available_amount', 0); // Only show items with available budget

    if (searchTerm.trim()) {
      query = query.or(`item_code.ilike.%${searchTerm}%,item_name.ilike.%${searchTerm}%`);
    }

    query = query
      .order('item_code')
      .limit(limit);

    const { data, error } = await query;

    if (error) {
      console.error('Error searching budget items:', error);
      throw new Error(`Failed to search budget items: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchBudgetItems:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while searching budget items');
  }
}

/**
 * Get budget categories for filtering
 */
export async function getBudgetCategories(): Promise<string[]> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .from('budget_items')
      .select('category')
      .eq('is_active', true)
      .order('category');

    if (error) {
      console.error('Error fetching budget categories:', error);
      throw new Error(`Failed to fetch budget categories: ${error.message}`);
    }

    // Extract unique categories
    const categories = data?.map(item => item.category) || [];
    const uniqueCategories = Array.from(new Set(categories));
    return uniqueCategories.filter(Boolean);
  } catch (error) {
    console.error('Error in getBudgetCategories:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching budget categories');
  }
}

/**
 * Check budget availability for a specific item and amount
 */
export async function checkBudgetAvailability(budgetItemId: string, requestedAmount: number): Promise<{
  available: boolean;
  availableAmount: number;
  budgetItem: BudgetItem | null;
}> {
  const supabase = await createClient();
  
  try {
    const budgetItem = await getBudgetItemById(budgetItemId);
    
    if (!budgetItem) {
      return {
        available: false,
        availableAmount: 0,
        budgetItem: null,
      };
    }

    const available = budgetItem.available_amount >= requestedAmount;
    
    return {
      available,
      availableAmount: budgetItem.available_amount,
      budgetItem,
    };
  } catch (error) {
    console.error('Error in checkBudgetAvailability:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while checking budget availability');
  }
}

/**
 * Reserve budget amount (for draft invoices)
 */
export async function reserveBudgetAmount(budgetItemId: string, amount: number): Promise<boolean> {
  const supabase = await createClient();
  
  try {
    // This would typically use a stored procedure to ensure atomicity
    const { data, error } = await supabase.rpc('reserve_budget_amount', {
      budget_item_id: budgetItemId,
      reserve_amount: amount
    });

    if (error) {
      console.error('Error reserving budget amount:', error);
      throw new Error(`Failed to reserve budget amount: ${error.message}`);
    }

    return data?.success || false;
  } catch (error) {
    console.error('Error in reserveBudgetAmount:', error);
    
    // Fallback manual update if RPC doesn't exist
    try {
      const budgetItem = await getBudgetItemById(budgetItemId);
      if (!budgetItem || budgetItem.available_amount < amount) {
        return false;
      }

      const { error: updateError } = await supabase
        .from('budget_items')
        .update({
          committed_amount: budgetItem.committed_amount + amount,
          available_amount: budgetItem.available_amount - amount,
          updated_at: new Date().toISOString(),
        })
        .eq('id', budgetItemId);

      if (updateError) {
        console.error('Error updating budget item:', updateError);
        return false;
      }

      return true;
    } catch (fallbackError) {
      console.error('Error in fallback budget reservation:', fallbackError);
      return false;
    }
  }
}

/**
 * Release reserved budget amount
 */
export async function releaseBudgetAmount(budgetItemId: string, amount: number): Promise<boolean> {
  const supabase = await createClient();
  
  try {
    // This would typically use a stored procedure to ensure atomicity
    const { data, error } = await supabase.rpc('release_budget_amount', {
      budget_item_id: budgetItemId,
      release_amount: amount
    });

    if (error) {
      console.error('Error releasing budget amount:', error);
      throw new Error(`Failed to release budget amount: ${error.message}`);
    }

    return data?.success || false;
  } catch (error) {
    console.error('Error in releaseBudgetAmount:', error);
    
    // Fallback manual update if RPC doesn't exist
    try {
      const budgetItem = await getBudgetItemById(budgetItemId);
      if (!budgetItem) {
        return false;
      }

      const { error: updateError } = await supabase
        .from('budget_items')
        .update({
          committed_amount: Math.max(0, budgetItem.committed_amount - amount),
          available_amount: budgetItem.available_amount + amount,
          updated_at: new Date().toISOString(),
        })
        .eq('id', budgetItemId);

      if (updateError) {
        console.error('Error updating budget item:', updateError);
        return false;
      }

      return true;
    } catch (fallbackError) {
      console.error('Error in fallback budget release:', fallbackError);
      return false;
    }
  }
}