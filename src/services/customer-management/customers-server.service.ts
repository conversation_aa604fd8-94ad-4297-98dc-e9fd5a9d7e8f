"use server";

import { createClient } from "@/utils/supabase/server";
import type {
  CustomerFormData,
  ContactPersonFormData,
  CustomerStatus
} from "@/types/customer-management";

export async function createCustomer(
  formData: CustomerFormData
): Promise<{ success: boolean; newId?: string; error?: string; customerCode?: string }> {
  const supabase = await createClient();

  try {
    // Prepare contact persons JSONB data
    const contactPersonsData = formData.contact_persons?.map(contact => ({
      id: crypto.randomUUID(),
      name: contact.name,
      title: contact.title,
      email: contact.email,
      phone: contact.phone,
      is_primary: contact.is_primary
    })) || [];

    // Prepare customer data - map to database field names with JSONB fields
    // Note: customer_code is now optional and will be generated by the database
    const customerData: any = {
      name: formData.name,
      display_name: formData.display_name || null,
      registration_number: formData.registration_number || null,
      tax_registration_number: formData.tax_registration_number || null,
      customer_type: formData.customer_type,
      category: formData.category || null,
      status: formData.status,
      email: formData.email,
      phone: formData.phone || null,
      website: formData.website || null,
      billing_address: formData.billing_address || null,
      shipping_addresses: formData.shipping_addresses || [],
      payment_terms: formData.payment_terms ? parseInt(formData.payment_terms) : null,
      credit_limit: formData.credit_limit ?? null,
      is_gst_registered: formData.is_gst_registered,
      contact_persons: contactPersonsData
    };

    // Only include customer_code if explicitly provided (for special cases)
    if (formData.customer_code) {
      customerData.customer_code = formData.customer_code;
    }

    // Insert customer with JSONB data
    // First attempt: try to insert and return all fields including trigger-generated ones
    const { data: customerResult, error: customerError } = await supabase
      .schema("finance")
      .from("customers")
      .insert(customerData)
      .select("*")
      .single();

    if (customerError || !customerResult) {
      // Handle unique constraint violations with user-friendly messages
      if (customerError?.code === '23505') {
        if (customerError.message.includes('customer_code')) {
          return { success: false, error: "A customer with this code already exists. Please try again." };
        } else if (customerError.message.includes('email')) {
          return { success: false, error: "A customer with this email already exists." };
        } else if (customerError.message.includes('registration_number')) {
          return { success: false, error: "A customer with this registration number already exists." };
        }
      }
      return { success: false, error: customerError?.message || "Failed to create customer" };
    }

    // Always fetch the customer after creation to ensure we get trigger-generated values
    // This is necessary because Supabase client may not properly return BEFORE INSERT trigger modifications
    const { data: createdCustomer, error: fetchError } = await supabase
      .schema("finance")
      .from("customers")
      .select("id, customer_code")
      .eq("id", customerResult.id)
      .single();

    if (fetchError || !createdCustomer) {
      return { 
        success: true, 
        newId: customerResult.id,
        error: "Customer created but could not retrieve customer code" 
      };
    }

    return { 
      success: true, 
      newId: createdCustomer.id,
      customerCode: createdCustomer.customer_code 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function updateCustomer(
  id: string,
  formData: CustomerFormData
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Prepare contact persons JSONB data
    const contactPersonsData = formData.contact_persons?.map(contact => ({
      id: contact.id || crypto.randomUUID(), // Use existing ID or generate new one
      name: contact.name,
      title: contact.title,
      email: contact.email,
      phone: contact.phone,
      is_primary: contact.is_primary
    })) || [];

    // Prepare update data - map to database field names with JSONB fields
    const updateData = {
      name: formData.name,
      display_name: formData.display_name || null,
      registration_number: formData.registration_number || null,
      tax_registration_number: formData.tax_registration_number || null,
      customer_type: formData.customer_type,
      category: formData.category || null,
      status: formData.status,
      email: formData.email,
      phone: formData.phone || null,
      website: formData.website || null,
      billing_address: formData.billing_address || null,
      shipping_addresses: formData.shipping_addresses || [],
      payment_terms: formData.payment_terms ? parseInt(formData.payment_terms) : null,
      credit_limit: formData.credit_limit ?? null,
      is_gst_registered: formData.is_gst_registered,
      contact_persons: contactPersonsData,
      updated_at: new Date().toISOString()
    };

    // Update customer with JSONB data
    const { error: updateError } = await supabase
      .schema("finance")
      .from("customers")
      .update(updateData)
      .eq("id", id);

    if (updateError) {
      // Handle unique constraint violations with user-friendly messages
      if (updateError?.code === '23505') {
        if (updateError.message.includes('email')) {
          return { success: false, error: "A customer with this email already exists." };
        } else if (updateError.message.includes('registration_number')) {
          return { success: false, error: "A customer with this registration number already exists." };
        }
      }
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function deleteCustomer(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    // Check if customer has any sales orders
    // const { data: soData, error: soCheckError } = await supabase
    //   .schema("finance")
    //   .from("sales_orders")
    //   .select("id")
    //   .eq("customer_id", id)
    //   .limit(1);

    // if (soCheckError) {
    //   return { success: false, error: soCheckError.message };
    // }

    // if (soData && soData.length > 0) {
    //   return { success: false, error: "Cannot delete customer with existing sales orders" };
    // }

    // Delete customer (JSONB data will be deleted automatically)
    const { error: deleteError } = await supabase
      .schema("finance")
      .from("customers")
      .delete()
      .eq("id", id);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

export async function updateCustomerStatus(
  id: string, 
  status: CustomerStatus,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (reason) {
      updateData.status_change_reason = reason;
    }

    const { error: updateError } = await supabase
      .schema("finance")
      .from("customers")
      .update(updateData)
      .eq("id", id);

    if (updateError) {
      return { success: false, error: updateError.message };
    }
    
    // Check if customer_status_history table exists and insert record
    const { data: historyData, error: historyError } = await supabase
      .schema("finance")
      .from("customer_status_history")
      .insert({
        customer_id: id,
        status: status,
        reason: reason || null,
        changed_at: new Date().toISOString()
      })
      .select();
      
    if (historyError) {
      // Silently ignore if table doesn't exist
    }

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

// Bulk update customer status
export async function bulkUpdateCustomerStatus(
  customerIds: string[],
  status: CustomerStatus,
  reason?: string
): Promise<{ success: boolean; error?: string; updatedCount?: number }> {
  const supabase = await createClient();

  try {
    if (!customerIds || customerIds.length === 0) {
      return { success: false, error: "No customers selected" };
    }

    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (reason) {
      updateData.status_change_reason = reason;
    }

    const { data, error: updateError } = await supabase
      .schema("finance")
      .from("customers")
      .update(updateData)
      .in("id", customerIds)
      .select("id");

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    return { 
      success: true, 
      updatedCount: data?.length || 0 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

// Get customer status history (if tracking is implemented)
export async function getCustomerStatusHistory(
  customerId: string
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  const supabase = await createClient();

  try {
    // For now, return the current status and last update
    // This can be expanded when status history tracking is implemented
    const { data, error } = await supabase
      .schema("finance")
      .from("customers")
      .select("status, updated_at, status_change_reason")
      .eq("id", customerId)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { 
      success: true, 
      data: data ? [{
        status: data.status,
        changed_at: data.updated_at,
        reason: data.status_change_reason
      }] : []
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

// Get customer by ID
export async function getCustomerById(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("finance")
      .from("customers")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { 
      success: true, 
      data 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}

// Get customers for dropdown/selection
export async function getCustomersForSelection(): Promise<{ 
  success: boolean; 
  data?: Array<{ id: string; customer_code: string; name: string; email: string }>; 
  error?: string 
}> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("finance")
      .from("customers")
      .select("id, customer_code, name, email")
      .eq("status", "ACTIVE")
      .order("name");

    if (error) {
      return { success: false, error: error.message };
    }

    return { 
      success: true, 
      data: data || [] 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
}