"use client";

import { createClient } from "@/utils/supabase/Client";
import type {
  Customer,
  CustomersResponse,
  CustomersTableFilters,
  CustomerType,
  CustomerCategory,
  DropdownOption
} from "@/types/customer-management";
import { 
  updateCustomerStatus, 
  bulkUpdateCustomerStatus,
  getCustomerStatusHistory as getStatusHistory,
  createCustomer,
  updateCustomer,
  deleteCustomer
} from './customers-server.service';

// Customers CRUD operations
export async function getAllCustomers(
  filters: { subFilters: { [key: string]: string[] } },
  page: number,
  limit: number,
  searchTerm?: string
): Promise<CustomersResponse> {
  const supabase = await createClient();

  try {
    // Base query with filters and search term
    let mainQuery = supabase
      .schema("finance")
      .from("customers")
      .select(`
        id,
        customer_code,
        name,
        display_name,
        customer_type,
        category,
        status,
        email,
        phone,
        payment_terms,
        credit_limit,
        is_gst_registered,
        created_at,
        updated_at
      `, { count: "exact" });

    // Apply filters
    Object.keys(filters.subFilters).forEach((filterKey) => {
      const filterValues = filters.subFilters[filterKey];
      if (Array.isArray(filterValues) && filterValues.length > 0) {
        mainQuery = mainQuery.in(filterKey, filterValues);
      }
    });

    // Apply search term
    if (searchTerm) {
      const normalizedSearchTerm = searchTerm.trim();
      mainQuery = mainQuery.or(
        `customer_code.ilike.%${normalizedSearchTerm}%,` +
        `name.ilike.%${normalizedSearchTerm}%,` +
        `display_name.ilike.%${normalizedSearchTerm}%,` +
        `email.ilike.%${normalizedSearchTerm}%`
      );
    }

    // Execute query to get total count and paginated data
    const { data: customerData, count: total, error } = await mainQuery
      .order("created_at", { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw new Error();

    if (!customerData || customerData.length === 0) {
      return { 
        customers: [], 
        success: true, 
        total: total ?? 0, 
        currentPage: page,
        allCustomerIds: []
      };
    }

    // Get all IDs for bulk operations
    const allIdsQuery = await supabase
      .schema("finance")
      .from("customers")
      .select("id");

    const allIds = allIdsQuery.data?.map(customer => customer.id) || [];

    return {
      customers: customerData as Customer[],
      success: true,
      total: total ?? 0,
      currentPage: page,
      allCustomerIds: allIds
    };
  } catch {
    return { 
      customers: null, 
      success: false, 
      total: 0, 
      currentPage: page,
      allCustomerIds: []
    };
  }
}

export async function getCustomerById(id: string): Promise<Customer | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("finance")
    .from("customers")
    .select(`
      *
    `)
    .eq("id", id)
    .single();

  if (error || !data) {
    return null;
  }

  return data as Customer;
}

export async function searchCustomers(searchTerm: string): Promise<Customer[]> {
  const normalizedSearchTerm = searchTerm?.trim();

  if (!normalizedSearchTerm) return [];

  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("finance")
    .from("customers")
    .select(`
      id,
      customer_code,
      name,
      display_name,
      status,
      email,
      customer_type,
      category,
      payment_terms
    `)
    .eq("status", "ACTIVE")
    .or(
      `customer_code.ilike.%${normalizedSearchTerm}%,` +
      `name.ilike.%${normalizedSearchTerm}%,` +
      `display_name.ilike.%${normalizedSearchTerm}%`
    )
    .order("name")
    .limit(20);

  if (error || !data || data.length === 0) {
    return [];
  }

  return data as Customer[];
}

// Get active customers for dropdown
export async function getActiveCustomers(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .schema("finance")
      .from("customers")
      .select("id, customer_code, name, display_name")
      .eq("status", "ACTIVE")
      .order("name");

    if (error) {
      console.error("Error fetching active customers:", error);
      
      if (error.message?.includes("schema") || error.message?.includes("table")) {
        console.warn("Customers table not available, returning empty list");
        return [];
      }
      
      throw new Error(`Failed to load customers: ${error.message}`);
    }

    if (!data || data.length === 0) {
      console.warn("No active customers found");
      return [];
    }

    return data.map(customer => ({
      value: customer.id,
      label: `${customer.customer_code} - ${customer.display_name || customer.name}`
    }));
    
  } catch (error) {
    console.error("Error in getActiveCustomers:", error);
    
    if (error instanceof Error) {
      throw error;
    }
    
    return [];
  }
}

// Get customer types for dropdown
export async function getCustomerTypes(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("system")
    .from("options")
    .select("id, code, label")
    .eq("type", "customer_type")
    .eq("is_active", true)
    .order("label");

  if (error || !data) {
    // Return default customer types
    return [
      { value: "INDIVIDUAL", label: "Individual" },
      { value: "COMPANY", label: "Company" },
      { value: "GOVERNMENT", label: "Government" },
      { value: "NON_PROFIT", label: "Non-Profit" },
      { value: "EDUCATIONAL", label: "Educational" }
    ];
  }

  return data.map(type => ({
    value: type.code,
    label: type.label
  }));
}

// Get customer categories for dropdown
export async function getCustomerCategories(): Promise<DropdownOption[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("system")
    .from("options")
    .select("id, code, label")
    .eq("type", "customer_category")
    .eq("is_active", true)
    .order("label");

  if (error || !data) {
    // Return default categories
    return [
      { value: "REGULAR", label: "Regular" },
      { value: "VIP", label: "VIP" },
      { value: "CORPORATE", label: "Corporate" },
      { value: "RESELLER", label: "Reseller" },
      { value: "DISTRIBUTOR", label: "Distributor" }
    ];
  }

  return data.map(category => ({
    value: category.code,
    label: category.label
  }));
}

// Status Management Functions - Re-export server actions for client-side use
export const changeCustomerStatus = updateCustomerStatus;
export const bulkChangeCustomerStatus = bulkUpdateCustomerStatus;
export const getCustomerStatusHistory = getStatusHistory;

// Re-export server actions for CRUD operations
export { createCustomer, updateCustomer, deleteCustomer, updateCustomerStatus, bulkUpdateCustomerStatus };

// Get customer transaction metrics
export async function getCustomerMetrics(customerId: string): Promise<{
  totalOrders: number;
  totalAmount: number;
  lastOrderDate?: string;
  averageOrderValue: number;
  paymentStatus: 'GOOD' | 'WARNING' | 'OVERDUE';
}> {
  const supabase = await createClient();

  try {
    // Get total orders and amount from sales_orders (when implemented)
    // For now, return mock data
    return {
      totalOrders: 0,
      totalAmount: 0,
      averageOrderValue: 0,
      paymentStatus: 'GOOD'
    };
  } catch {
    return {
      totalOrders: 0,
      totalAmount: 0,
      averageOrderValue: 0,
      paymentStatus: 'GOOD'
    };
  }
}

// Get customer by code (for validation)
export async function getCustomerByCode(code: string): Promise<Customer | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .schema("finance")
    .from("customers")
    .select("*")
    .eq("customer_code", code)
    .single();

  if (error || !data) {
    return null;
  }

  return data as Customer;
}

// Check if customer email exists
export async function checkCustomerEmailExists(email: string, excludeId?: string): Promise<boolean> {
  const supabase = await createClient();

  let query = supabase
    .schema("finance")
    .from("customers")
    .select("id")
    .eq("email", email);

  if (excludeId) {
    query = query.neq("id", excludeId);
  }

  const { data, error } = await query;

  if (error || !data) {
    return false;
  }

  return data.length > 0;
}

// Check if customer registration number exists
export async function checkCustomerRegistrationExists(registrationNumber: string, excludeId?: string): Promise<boolean> {
  const supabase = await createClient();

  let query = supabase
    .schema("finance")
    .from("customers")
    .select("id")
    .eq("registration_number", registrationNumber);

  if (excludeId) {
    query = query.neq("id", excludeId);
  }

  const { data, error } = await query;

  if (error || !data) {
    return false;
  }

  return data.length > 0;
}