'use server'

import { createClient } from '@/utils/supabase/server'
import { supabaseClient } from '@/utils/supabase/service'

export async function forgetPassword(email: string) {
    const supabase = await createClient();

    const { data: userMeta, error: userError } = await supabaseClient
        .from("user_meta")
        .select("user_type")
        .eq("email", email)
        .single();

    if (userError || userMeta?.user_type !== "ADMIN") {
        // do nothing
    } else {
        await supabase.auth.resetPasswordForEmail(email,
            { redirectTo: `${process.env.NEXT_ADMIN_SITE_URL}/new-credentials` });
    }
}

export async function resetPassword(newPassword: string) {
    const supabase = await createClient();

    try {
        const { error: updateError } = await supabase.auth.updateUser({
            password: newPassword,
        });

        if (updateError) {
            if (updateError.code === 'same_password') {
                return { success: false, error: 'Your new password cannot be the same as your current password. Please choose a different password.' };
            }
            return { success: false, error: "Failed to update your new password, please try again." };
        }

        return { success: true };

    } catch {
        return { success: false, error: "An unexpected error occurred, please try again later" };
    }
}

export async function setPassword(newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClient();

        // Step 1: Get the current user
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError || !userData?.user || !userData.user.email) {
            return { success: false, error: 'No authenticated user found' };
        }

        const user = userData.user;

        // Step 2: Check if this is the user's first login
        const { data: userMeta, error: profileError } = await supabase
            .from("user_meta")
            .select("first_login")
            .eq("id", user.id)
            .single();

        if (profileError || !userMeta || userMeta.first_login !== true) {
            return { success: false, error: 'Password can only be set during first login' };
        }

        // Step 3: Update the user's password
        const { error: updateError } = await supabase.auth.updateUser({
            password: newPassword,
        });

        if (updateError) {
            if (updateError.code === 'same_password') {
                return { success: false, error: 'Your new password cannot be the same as your current password. Please choose a different password.' };
            }
            return { success: false, error: "Failed to update your new password, please try again." };
        }

        // Step 4: Update the first_login flag to false
        const { error: updateProfileError } = await supabase
            .from("user_meta")
            .update({ first_login: false })
            .eq("id", user.id);

        if (updateProfileError) {
            return { success: false, error: 'Password updated but failed to update login status, please try again' };
        }

        return { success: true };
    } catch {
        return { success: false, error: "An unexpected error occurred, please try again later" };
    }
}