import { createClient } from "@/utils/supabase/Client";
import type { BudgetRevisions, BudgetRevisionView } from "@/types/budgets/budgets.types";
import type { approvalActionHistory } from "@/types/approvals/approval.types";
import type {
    budgetsSimpleItem,
    Budgets,
    budgetComplexItem,
    budgetsItemGrouped,
    budgetSectionsData,
    BudgetItems,
    BudgetComplexDataEntries,
    BudgetDataEntries,
    BudgetDataQueryResponse,
    BudgetReviewComplexProps,
    BudgetReviewSimpleEventProps,
    BudgetSectionResponse,
} from "@/types/budgets/budgets.types";
import type { DropdownOption } from "@/types/general.types";
import { useRouter } from "next/navigation";
import { ApprovalService } from "@/services/approvals/approval.service";

// Redirect
export function useVerifyPageDestinationOnBudget() {
    const router = useRouter();

    const verifyPageDestinationOnBudget = async (
        id: string,
        openInNewTab?: boolean
    ): Promise<void> => {
        const supabase = await createClient();
        const { data, error } = await supabase
            .schema("budget")
            .from("budget_revisions")
            .select("status")
            .eq('id', id)
            .single();

        if (error) {
            return;
        }

        const params = new URLSearchParams();
        params.set("id", id);

        const url =
            data.status === 'DRAFT'
                ? `/budgets/edit-budget?${params.toString()}`
                : `/budgets/budget-review?${params.toString()}`;

        if (openInNewTab) {
            window.open(url, "_blank");
        } else {
            router.push(url);
        }
    };

    return verifyPageDestinationOnBudget;
}

export function useVerifyAccessToEditBudget() {
    const router = useRouter();

    const verifyAccessToEditBudget = async (id: string): Promise<void> => {
        const budgetDetails = await getBudgetDetails(id);

        if (!budgetDetails || !(budgetDetails.status === 'DRAFT' || budgetDetails.status === 'REJECTED')) {
            router.push(`/budgets`);
        }
    };

    return verifyAccessToEditBudget;
}

export function useVerifyAccessToBudgetReview() {
    const router = useRouter();

    const verifyAccessToBudgetReview = async (id: string): Promise<void> => {
        const budgetDetails = await getBudgetDetails(id);

        if (!budgetDetails || budgetDetails.status === 'DRAFT') {
            router.push(`/budgets`);
        }
    };

    return verifyAccessToBudgetReview;
}

// Form Options
export async function getBudgetTypeNFormat() {
    const supabase = await createClient();

    const { data: typeData, error: typeError } = await supabase
        .schema('budget')
        .from('entity_types')
        .select('entity_type, display_name');

    const { data: formatData, error: formatError } = await supabase
        .schema('system')
        .from('options')
        .select('id, code, label')
        .eq('type', 'budget_format');

    if (typeError || formatError) {
        throw new Error(
            `Error fetching options: ${typeError?.message || ''} ${formatError?.message || ''}`
        );
    }

    const filteredTypeData = typeData.map(item => ({
        code: item.entity_type,
        label: item.display_name,
    }));

    return {
        budgetType: filteredTypeData,
        budgetFormat: formatData,
    };
}

export async function getBudgetItemNoSection(type?: "INCOME" | "EXPENDITURE"): Promise<{
    budgetItems: BudgetItems[];
}> {
    const supabase = await createClient();

    const { data: budgetItems, error: itemsError } = await supabase
        .schema("budget")
        .from("budget_items")
        .select("*")
        .eq("type", type)
        .is("section_id", null);

    if (itemsError || budgetItems.length === 0) {
        return {
            budgetItems: []
        }
    }

    return {
        budgetItems: budgetItems,
    };
}

export async function getBudgetItemWithSection(type?: "INCOME" | "EXPENDITURE"): Promise<{
    budgetSections: budgetSectionsData[];
    budgetItems: BudgetItems[];
}> {
    const supabase = await createClient();

    // Fetch budget sections
    let sectionsQuery = supabase
        .schema("budget")
        .from("budget_sections")
        .select("*");
    if (type) {
        sectionsQuery = sectionsQuery.eq("type", type);
    }

    const { data: budgetSections, error: sectionsError } = await sectionsQuery;

    if (sectionsError) {
        throw new Error("Failed to fetch budget sections.");
    }

    // Fetch budget items
    let itemsQuery = supabase
        .schema("budget")
        .from("budget_items")
        .select("*")
        .not("section_id", "is", null);

    if (type) {
        itemsQuery = itemsQuery.eq("type", type);
    }

    const { data: budgetItems, error: itemsError } = await itemsQuery;

    if (itemsError) {
        throw new Error("Failed to fetch budget items.");
    }

    return {
        budgetSections: budgetSections || [],
        budgetItems: budgetItems || [],
    };
}

// Data
export async function getAllBudgets(
    filters: {
        subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    onlyArchivedBudgets: boolean,
    searchTerm?: string,
): Promise<{
    BudgetRevisionView: BudgetRevisionView[] | null;
    success: boolean;
    total: number;
    currentPage: number;
}> {
    const supabase = await createClient();

    try {
        // Base query with filters and search term
        let mainQuery = supabase
            .schema("budget")
            .from("parent_budget_revisions")
            .select("*", { count: "exact" });

        mainQuery = onlyArchivedBudgets
            ? mainQuery.eq("is_archived", true)
            : mainQuery.eq("is_archived", false);

        // Apply filters
        Object.keys(filters.subFilters).forEach((filterKey) => {
            const filterValues = filters.subFilters[filterKey];
            if (Array.isArray(filterValues) && filterValues.length > 0) {
                mainQuery = mainQuery.in(filterKey, filterValues);
            }
        });

        // Apply search term
        if (searchTerm) {
            const normalizedSearchTerm = searchTerm.trim();
            mainQuery = mainQuery.or(
                `budget_code.ilike.%${normalizedSearchTerm}%,` +
                `latest_title.ilike.%${normalizedSearchTerm}%,` +
                `latest_reviewed_by.ilike.%${normalizedSearchTerm}%,` +
                `latest_submitted_by.ilike.%${normalizedSearchTerm}%,` +
                `created_by.ilike.%${normalizedSearchTerm}%`
            );
        }

        // Execute query to get total count and paginated data
        const { data: budgetData, count: total, error } = await mainQuery
            .order("created_at", { ascending: false })
            .range((page - 1) * limit, page * limit - 1);

        if (error) throw new Error();

        if (!budgetData || budgetData.length === 0) {
            return { BudgetRevisionView: [], success: true, total: total ?? 0, currentPage: page };
        }

        return {
            BudgetRevisionView: budgetData,
            success: true,
            total: total ?? 0,
            currentPage: page,
        };
    } catch {
        return { BudgetRevisionView: null, success: false, total: 0, currentPage: page };
    }
}

export async function getBudgetDetails(id: string): Promise<BudgetRevisions | null> {
    const supabase = await createClient();

    const { data, error } = await supabase
        .schema('budget')
        .from('budget_revisions')
        .select('*')
        .eq('id', id)
        .single()

    if (error || !data) {
        return null
    }

    return data
}

export async function getSimpleBudgetForm(budget_revision_id: string): Promise<budgetsSimpleItem> {
    const supabase = await createClient();

    // Fetch the budget data
    const { data: budgetsData, error: budgetsError } = await supabase
        .schema("budget")
        .from("budgets")
        .select("*")
        .eq('budget_revision_id', budget_revision_id);

    if (budgetsError) {
        throw new Error("Failed to fetch budgets item data.");
    }

    // Split the data into income and expenditure entries
    const incomeEntries: Budgets[] = [];
    const expenditureEntries: Budgets[] = [];

    if (budgetsData) {
        budgetsData.forEach((item) => {
            if (item.type === 'INCOME') {
                incomeEntries.push(item);
            } else if (item.type === 'EXPENDITURE') {
                expenditureEntries.push(item);
            }
        });
    }

    // Return the data categorized into income and expenditure
    return {
        incomeEntries: incomeEntries.length > 0 ? incomeEntries : null,
        expenditureEntries: expenditureEntries.length > 0 ? expenditureEntries : null,
    };
}

export async function getComplexBudgetForm(budget_revision_id: string): Promise<budgetComplexItem> {
    const supabase = await createClient();

    const { data: budgetsData, error: budgetsError } = await supabase
        .schema("budget")
        .from("budgets")
        .select("*")
        .eq('budget_revision_id', budget_revision_id);

    if (budgetsError) {
        throw new Error("Failed to fetch budgets item data.");
    }

    // Prepare to group items by section
    const sections: { [key: string]: budgetsItemGrouped } = {};

    for (const budget of budgetsData) {
        const { data: budgetItemData, error: budgetItemError } = await supabase
            .schema("budget")
            .from("budget_items")
            .select("section_id")
            .eq("id", budget.budget_item_id)
            .single();

        if (budgetItemError) {
            throw new Error("Failed to fetch budget item details.");
        }

        const { data: budgetSectionData, error: budgetSectionError } = await supabase
            .schema("budget")
            .from("budget_sections")
            .select("id, name")
            .eq("id", budgetItemData.section_id)
            .single();

        if (budgetSectionError) {
            throw new Error("Failed to fetch budget section details.");
        }

        // Ensure the section exists in the map, otherwise create it
        if (!sections[budgetSectionData.id]) {
            sections[budgetSectionData.id] = {
                id: budgetSectionData.id,
                name: budgetSectionData.name,
                types: []
            };
        }

        // Add the budget item to the correct section
        sections[budgetSectionData.id].types?.push(budget);
    }

    // Prepare final result object with the appropriate grouped data
    const result: budgetComplexItem = {
        incomeEntries: null,
        expenditureEntries: null
    };

    // Separate income and expenditure entries based on their section and type
    Object.values(sections).forEach((section) => {
        const sectionEntries = section.types || [];
        if (sectionEntries.length > 0) {
            if (sectionEntries[0].type === 'INCOME') {
                result.incomeEntries = result.incomeEntries || [];
                result.incomeEntries.push(section);
            } else if (sectionEntries[0].type === 'EXPENDITURE') {
                result.expenditureEntries = result.expenditureEntries || [];
                result.expenditureEntries.push(section);
            }
        }
    });

    // Return the grouped data
    return result;
}

export async function getSimpleBudgetData(budget_revision_id: string): Promise<BudgetReviewSimpleEventProps> {
    try {
        const supabase = await createClient();

        // Fetch budget summaries
        const { data: budgetSummaries, error: summaryError } = await supabase
            .schema('budget')
            .from('budget_summaries')
            .select('*')
            .eq('budget_revision_id', budget_revision_id)
            .order('updated_at', { ascending: false })
            .single();

        if (summaryError) {
            console.warn("Budget summary not found, returning default empty summary.");
        }

        // Fetch budget entries with their items
        const { data: budgetEntries, error: budgetError } = await supabase
            .schema('budget')
            .from('budgets')
            .select(`*, budget_items!inner(name)`)
            .eq('budget_revision_id', budget_revision_id)
            .is('budget_items.section_id', null)
            .order('created_at');

        if (budgetError) {
            console.warn("Budget entries not found, returning empty entries.");
        }

        const typedBudgetEntries = budgetEntries as unknown as BudgetDataQueryResponse[];

        // Group entries by type
        const incomeEntries: BudgetDataEntries[] = (typedBudgetEntries || [])
            .filter(entry => entry.type === 'INCOME')
            .map(entry => ({
                type: entry.budget_items?.name || "Unknown",
                proposedRate: entry.proposed_rate || 0,
                quantity: entry.quantity || 0,
                subTotal: entry.sub_total || 0,
                remarks: entry.remarks || ''
            }));

        const expenditureEntries: BudgetDataEntries[] = (typedBudgetEntries || [])
            .filter(entry => entry.type === 'EXPENDITURE')
            .map(entry => ({
                type: entry.budget_items?.name || "Unknown",
                proposedRate: entry.proposed_rate || 0,
                quantity: entry.quantity || 0,
                subTotal: entry.sub_total || 0,
                remarks: entry.remarks || ''
            }));

        return {
            incomeEntries,
            expenditureEntries,
            summary: budgetSummaries || null
        };
    } catch {
        return {
            incomeEntries: [],
            expenditureEntries: [],
            summary: null
        };
    }
}

export async function getComplexBudgetData(budget_revision_id: string): Promise<BudgetReviewComplexProps> {
    try {
        const supabase = await createClient();

        // Fetch budget summaries
        const { data: budgetSummaries, error: summaryError } = await supabase
            .schema('budget')
            .from('budget_summaries')
            .select('*')
            .eq('budget_revision_id', budget_revision_id)
            .order('updated_at', { ascending: false })
            .single();

        if (summaryError) {
            console.warn("Summary not found, returning empty data.");
        }

        // Fetch budget entries with their items
        const { data: rawBudgetEntries, error: budgetError } = await supabase
            .schema('budget')
            .from('budgets')
            .select(`*, budget_items!inner(name, section_id)`)
            .eq('budget_revision_id', budget_revision_id)
            .order('created_at');

        if (budgetError) {
            console.warn("Budget entries not found, returning empty data.");
        }

        const budgetEntries = (rawBudgetEntries as unknown) as BudgetDataQueryResponse[];

        const sectionIds = budgetEntries
            .map(entry => entry.budget_items?.section_id)
            .filter((id): id is string => !!id)
            .filter((id, index, self) => self.indexOf(id) === index);

        // Fetch sections
        const { data: rawSections, error: sectionsError } = await supabase
            .schema('budget')
            .from('budget_sections')
            .select('*')
            .in('id', sectionIds);

        if (sectionsError) {
            console.warn("Sections not found, returning empty data.");
        }

        const sections = (rawSections || []) as BudgetSectionResponse[];

        // Create a map of sections for easy lookup
        const sectionsMap = sections.reduce<Record<string, BudgetSectionResponse>>(
            (acc, section) => {
                acc[section.id] = section;
                return acc;
            },
            {}
        );

        // Process the entries
        const groupedEntries = budgetEntries.reduce((acc: { [key: string]: BudgetDataEntries[] }, entry) => {
            const section = entry.budget_items?.section_id ? sectionsMap[entry.budget_items.section_id] : null;
            if (section) {
                const sectionName = section.name;
                if (!acc[sectionName]) {
                    acc[sectionName] = [];
                }

                acc[sectionName].push({
                    type: entry.budget_items?.name || 'Unknown',
                    proposedRate: entry.proposed_rate || 0,
                    quantity: entry.quantity || 0,
                    subTotal: entry.sub_total || 0,
                    remarks: entry.remarks || ''
                });
            }
            return acc;
        }, {});

        // Format sections
        const formatSections = (entries: { [key: string]: BudgetDataEntries[] }): BudgetComplexDataEntries[] => {
            return Object.entries(entries).map(([sectionName, types]) => ({
                sectionName,
                types: types.sort((a, b) => a.type.localeCompare(b.type))
            }));
        };

        // Split into income and expenditure sections
        const incomeSections = formatSections(
            Object.fromEntries(
                Object.entries(groupedEntries).filter(([key]) =>
                    sections.some(section =>
                        section.type === 'INCOME' &&
                        section.name === key
                    )
                )
            )
        );

        const expenditureSections = formatSections(
            Object.fromEntries(
                Object.entries(groupedEntries).filter(([key]) =>
                    sections.some(section =>
                        section.type === 'EXPENDITURE' &&
                        section.name === key
                    )
                )
            )
        );

        // Sort sections by sequence
        const sortSections = (sectionsToSort: BudgetComplexDataEntries[], allBudgetSections: BudgetSectionResponse[]): BudgetComplexDataEntries[] => {
            return sectionsToSort.sort((a, b) => {
                const sectionA = allBudgetSections.find(s => s.name === a.sectionName);
                const sectionB = allBudgetSections.find(s => s.name === b.sectionName);
                return (sectionA?.sequence || 0) - (sectionB?.sequence || 0);
            });
        };

        return {
            incomeEntries: sortSections(incomeSections, sections),
            expenditureEntries: sortSections(expenditureSections, sections),
            summary: budgetSummaries || null
        };

    } catch {
        return {
            incomeEntries: [],
            expenditureEntries: [],
            summary: null
        };
    }
}

// also used by another component
export async function getAllBudgetVersion(
    budget_revision_id?: string | null,
    programme_id?: string
) {
    const supabase = await createClient();

    if (programme_id) {
        // Mode 1: programme-based lookup
        const { data, error } = await supabase
            .schema('budget')
            .from('budget_revisions')
            .select('id, version, status, is_archived')
            .eq('entity_id', programme_id)
            .order('version', { ascending: true });

        if (error || !data) return [];
        return data;
    }

    if (budget_revision_id) {
        // Mode 2: parent-child revision lookup
        const childResult = await supabase
            .schema('budget')
            .from('budget_revisions')
            .select('id, version, status, is_archived')
            .eq('parent_id', budget_revision_id)
            .order('version', { ascending: true });

        const parentResult = await supabase
            .schema('budget')
            .from('budget_revisions')
            .select('id, version, status, is_archived')
            .eq('id', budget_revision_id)
            .single();

        if (childResult.error || !childResult.data || parentResult.error || !parentResult.data) {
            return [];
        }

        return [
            {
                id: parentResult.data.id,
                version: parentResult.data.version,
                status: parentResult.data.status,
                is_archived: parentResult.data.is_archived,
            },
            ...childResult.data
        ];
    }

    return [];
}

export async function searchBudgets(
    searchTerm: string | null,
    page: number,
    entity_type: "PROGRAMME" | "DEPARTMENT"
): Promise<{
    success: boolean;
    budget: BudgetRevisionView[];
    currentPage: number;
    totalPages: number;
}> {
    const normalizedSearchTerm = searchTerm?.trim();
    if (!normalizedSearchTerm) return { success: true, budget: [], currentPage: 0, totalPages: 0 };

    const itemsPerPage = 10;
    const currentPage = page < 1 ? 1 : page;

    const supabase = await createClient();

    // Query budgets
    const { data: budgetData, error: budgetError, count: total } = await supabase
        .schema("budget")
        .from("parent_budget_revisions")
        .select("*", { count: "exact" })
        .match({ latest_status: "APPROVED", is_archived: false, entity_type })
        .or(
            `entity_id.is.null,` +
            `budget_code.ilike.%${normalizedSearchTerm}%,` +
            `latest_title.ilike.%${normalizedSearchTerm}%`
        )
        .order("created_at", { ascending: false })
        .range((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage - 1);

    if (budgetError) {
        console.error("Error fetching budget data:", budgetError.message);
        return { success: false, budget: [], currentPage: 0, totalPages: 0 };
    }

    if (!budgetData || budgetData.length === 0) {
        return { success: false, budget: [], currentPage: 0, totalPages: 0 };
    }

    // Extract programme IDs
    const programmeIds = budgetData
        .map(b => b.programme_id)
        .filter((id): id is string => Boolean(id));

    let programmeData: { id: string; programme_code: string; name: string }[] = [];

    if (programmeIds.length > 0) {
        const { data, error: programmeError } = await supabase
            .schema("programme")
            .from("programmes")
            .select("id, programme_code, name")
            .in("id", programmeIds);

        if (programmeError) {
            console.warn("Error fetching programme data:", programmeError.message);
        } else if (data) {
            programmeData = data;
        }
    }

    // Augment budget data
    const augmentedBudgets: BudgetRevisionView[] = budgetData.map(b => {
        const programme = programmeData.find(p => p.id === b.programme_id);
        return {
            ...b,
            programme_name: programme?.name ?? null,
            programme_code: programme?.programme_code ?? null,
        };
    });

    const totalPages = Math.ceil((total ?? 0) / itemsPerPage);

    return {
        success: true,
        budget: augmentedBudgets,
        currentPage,
        totalPages
    };
}

export async function searchSpecificBudgets(id: string): Promise<BudgetRevisionView | null> {
    const supabase = await createClient();

    const { data, error } = await supabase
        .schema('budget')
        .from('parent_budget_revisions')
        .select('*')
        .match({ latest_status: "APPROVED", is_archived: false, id })
        .single()

    if (error || !data) {
        return null
    }

    return data
}

type BudgetDataForProgramme =
    | { format: 'SIMPLE'; budgetData: BudgetReviewSimpleEventProps }
    | { format: 'COMPLEX'; budgetData: BudgetReviewComplexProps };

export async function getBudgetDataForProgramme(approval_budget_id: string): Promise<BudgetDataForProgramme | null> {
    const supabase = await createClient();

    // Get budget format
    const { data, error } = await supabase
        .schema("budget")
        .from("budget_revisions")
        .select("format")
        .eq('id', approval_budget_id)
        .single();

    if (error || !data) {
        return null;
    }

    if (data.format === 'SIMPLE') {
        const simpleResponse = await getSimpleBudgetData(approval_budget_id)
        return { format: "SIMPLE", budgetData: simpleResponse };
    } else if (data.format === 'COMPLEX') {
        const complexResponse = await getComplexBudgetData(approval_budget_id)
        return { format: "COMPLEX", budgetData: complexResponse };
    } else {
        return null;
    }
}

export async function getBudgetReviewHistory(
    parent_budget_id?: string,
    programme_id?: string
): Promise<approvalActionHistory[] | null> {
    const supabase = await createClient();

    let budget_revision_id = parent_budget_id;
    let revisions: { id: string; approval_request_id: string; version: number }[] = [];
    let revisionIds: string[] = [];

    if (programme_id) {
        const { data, error } = await supabase
            .schema("programme")
            .from("programmes")
            .select("approved_budget_revision_id")
            .eq("id", budget_revision_id)
            .single();

        if (error || !data) {
            return null;
        }

        budget_revision_id = data.approved_budget_revision_id;
    }

    const { data, error } = await supabase
        .schema("budget")
        .from("budget_revisions")
        .select("id, approval_request_id, version")
        .eq("parent_id", budget_revision_id);

    if (error || !data) {
        return null;
    }

    revisions = data;

    // Get parent revision itself
    const { data: parent, error: parentError } = await supabase
        .schema("budget")
        .from("budget_revisions")
        .select("id, approval_request_id, version")
        .eq("id", budget_revision_id)
        .single();

    if (parentError || !parent) {
        return null;
    }

    // Add parent revision to list
    revisions.push(parent);

    // Extract all approval request IDs
    revisionIds = revisions.map(r => r.approval_request_id);

    const history = await ApprovalService.getApprovalHistory(revisionIds);

    if (!history) {
        return [];
    }

    // Map version by approval_request_id
    const versionMap = Object.fromEntries(
        revisions.map(r => [r.approval_request_id, r.version])
    );

    const formattedHistory = history.map(h => ({
        ...h,
        version: versionMap[h.request_id ?? ''],
    }));

    return formattedHistory;
}

export async function checkReviseBudgetPermission(parent_id: string): Promise<boolean> {
    const supabase = await createClient();

    const { data, error } = await supabase
        .schema("budget")
        .from("parent_budget_revisions")
        .select("latest_status")
        .eq("id", parent_id)
        .single();

    if (error || !['APPROVED', 'REJECTED'].includes(data.latest_status)) {
        return false;
    }

    return true;
}

export async function getLatestBudgetId(
    programme_id?: string,
    budget_revision_id?: string
): Promise<string | null> {
    const supabase = await createClient();

    let query;

    if (programme_id) {
        // Mode 1: programme
        query = supabase
            .schema("budget")
            .from("parent_budget_revisions")
            .select("latest_id, latest_approval_id")
            .eq("entity_id", programme_id)
            .single()
    } else if (budget_revision_id) {
        // Mode 2: budget
        query = supabase
            .schema("budget")
            .from("parent_budget_revisions")
            .select("latest_id, latest_approval_id")
            .eq("id", budget_revision_id)
            .single()
    } else {
        return null;
    }

    const { data, error } = await query

    if (error || !data) {
        return null;
    }

    return data.latest_id ?? data.latest_approval_id ?? null;
}

export async function getBudgetItems(budgetRevisionId: string): Promise<DropdownOption[]> {
    const supabase = await createClient();

    const { data, error } = await supabase
        .schema("budget")
        .from("budgets")
        .select("budget_items!inner(id, name)")
        .match({ type: "EXPENDITURE", budget_revision_id: budgetRevisionId });

    if (error || !data) {
        return [];
    }

    const filteredData = data.map(item => {
        const budgetItemArray = item.budget_items as { id: string; name: string }[];
        const budgetItem = Array.isArray(budgetItemArray) ? budgetItemArray[0] : budgetItemArray;
        return {
            value: budgetItem.id,
            label: budgetItem.name,
        };
    });

    const uniqueItems = new Map();
    filteredData.forEach(item => {
        if (item.value) {
            uniqueItems.set(item.value, item);
        }
    });

    return Array.from(uniqueItems.values());
}