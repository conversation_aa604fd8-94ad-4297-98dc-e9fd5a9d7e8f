'use server'

import { BudgetRevisions } from '@/types/budgets/budgets.types';
import { createClient } from '@/utils/supabase/server'

export async function submitBudgetRevisions(
    data: {
        version?: number;
        entity_id?: string;
        parent_id?: string;
        budget_code?: string;
        budgetType?: string;
        budgetFormat?: string;
        title?: string;
        description?: string;
    },
    budgetRevisionId?: string
): Promise<{ success: boolean, responseId?: string }> {
    const supabase = await createClient();

    const rawData = {
        version: data.version,
        entity_id: data.entity_id,
        parent_id: data.parent_id,
        budget_code: data.budget_code,
        entity_type: data.budgetType,
        format: data.budgetFormat,
        title: data.title,
        description: data.description,
    };

    const filteredData = Object.fromEntries(
        Object.entries(rawData).filter(([_, value]) => value !== undefined)
    );

    let result;
    let error;

    if (budgetRevisionId) {
        // Update existing record
        const response = await supabase
            .schema('budget')
            .from('budget_revisions')
            .update(filteredData)
            .eq('id', budgetRevisionId);

        error = response.error;
    } else {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user?.id) {
            error = 'cannot get authenticated user';
        }

        const insertData = {
            ...filteredData,
            created_by: user?.id,
        };

        // Insert new record
        const response = await supabase
            .schema('budget')
            .from('budget_revisions')
            .insert(insertData)
            .select('id')
            .single();

        result = response.data;
        error = response.error;
    }

    if (error) {
        return { success: false };
    }

    return { success: true, responseId: result?.id };
}

export async function submitBudgetPreparation(data: {
    budgetRevisionId: string;
    expenditureEntries?: Array<{
        id?: string;
        type?: string;
        proposedRate?: number;
        quantity?: number;
        remarks?: string;
        subTotal?: number;
    }>;
    incomeEntries?: Array<{
        id?: string;
        type?: string;
        proposedRate?: number;
        quantity?: number;
        remarks?: string;
        subTotal?: number;
    }>;
}): Promise<void> {
    const supabase = await createClient();

    // Validate required fields
    if (!data.budgetRevisionId) {
        throw new Error("Budget revision ID is required for budget preparation.");
    }

    // Handle income entries
    for (const entry of data.incomeEntries || []) {
        if (entry.id) {
            // Update existing income entry
            const { error: incomeError } = await supabase
                .schema("budget")
                .from("budgets")
                .update({
                    budget_item_id: entry.type,
                    type: 'INCOME',
                    proposed_rate: entry.proposedRate,
                    quantity: entry.quantity,
                    remarks: entry.remarks
                })
                .eq("id", entry.id);

            if (incomeError) {
                throw new Error("Failed to update income entry.");
            }
        } else {
            // Insert new income entry
            const { error: incomeError } = await supabase
                .schema("budget")
                .from("budgets")
                .insert({
                    budget_revision_id: data.budgetRevisionId,
                    budget_item_id: entry.type,
                    type: 'INCOME',
                    proposed_rate: entry.proposedRate,
                    quantity: entry.quantity,
                    remarks: entry.remarks
                });

            if (incomeError) {
                throw new Error("Failed to insert income entry.");
            }
        }
    }

    // Handle expenditure entries
    for (const entry of data.expenditureEntries || []) {
        if (entry.id) {
            // Update existing expenditure entry
            const { error: expenditureError } = await supabase
                .schema("budget")
                .from("budgets")
                .update({
                    budget_item_id: entry.type,
                    type: 'EXPENDITURE',
                    proposed_rate: entry.proposedRate,
                    quantity: entry.quantity,
                    remarks: entry.remarks
                })
                .eq("id", entry.id);

            if (expenditureError) {
                throw new Error("Failed to update expenditure entry.");
            }
        } else {
            // Insert new expenditure entry
            const { error: expenditureError } = await supabase
                .schema("budget")
                .from("budgets")
                .insert({
                    budget_revision_id: data.budgetRevisionId,
                    budget_item_id: entry.type,
                    type: 'EXPENDITURE',
                    proposed_rate: entry.proposedRate,
                    quantity: entry.quantity,
                    remarks: entry.remarks
                });

            if (expenditureError) {
                throw new Error("Failed to insert expenditure entry.");
            }
        }
    }
}

export async function deleteBudgetPreparation(itemsToDelete: { id: string }[]): Promise<void> {
    // Initialize the Supabase client
    const supabase = await createClient();

    // Validate required fields
    if (!itemsToDelete || itemsToDelete.length === 0) {
        throw new Error("id is required for deleting budget preparation.");
    }

    const ids = itemsToDelete.map(item => item.id);

    // Perform the delete operation
    const { error } = await supabase
        .schema("budget")
        .from("budgets")
        .delete()
        .in("id", ids);

    // Handle Supabase errors
    if (error) {
        throw new Error(`Failed to delete budget preparation: ${error.message}`);
    }
}

export async function submitBudgetForReview(budgetRevisionId: string): Promise<boolean> {
    const supabase = await createClient();

    if (!budgetRevisionId) {
        return false;
    }

    const { error: budgetRevisionError } = await supabase
        .schema("budget")
        .from("budget_revisions")
        .update({ status: "PENDING_APPROVAL" })
        .eq('id', budgetRevisionId);

    if (budgetRevisionError) {
        return false;
    }

    return true;
}

export async function processReviseBudget(
    budgetRevision: BudgetRevisions
): Promise<{ success: boolean; message: string; responseId?: string }> {
    const supabase = await createClient();
    const parentId = budgetRevision.parent_id ?? budgetRevision.id;

    // Fetch the parent budget summary
    const { data, error } = await supabase
        .schema('budget')
        .from('parent_budget_revisions')
        .select('*')
        .eq('id', parentId)
        .single();

    if (error || !data) {
        return {
            success: false,
            message: 'Failed to fetch parent budget revision.',
        };
    }

    const { latest_status, latest_version, latest_id } = data;

    if (!['APPROVED', 'REJECTED'].includes(latest_status)) {
        // Already revising, return existing response ID
        return {
            success: true,
            message: 'Budget is already under revision. Redirecting to the latest draft.',
            responseId: latest_id,
        };
    }

    const isParentBudget = latest_version === 1;

    const submitData = {
        version: latest_version + 1,
        entity_id: budgetRevision.entity_id ?? undefined,
        parent_id: isParentBudget ? budgetRevision.id : budgetRevision.parent_id ?? undefined,
        budget_code: budgetRevision.budget_code ?? undefined,
        budgetType: budgetRevision.entity_type,
        budgetFormat: budgetRevision.format,
        title: budgetRevision.title ?? undefined,
        description: budgetRevision.description ?? undefined,
    };

    const response = await submitBudgetRevisions(submitData);

    if (!response.success || !response.responseId) {
        return {
            success: false,
            message: 'Failed to submit new budget revision.',
        };
    }

    const cloneResult = await cloneBudgetPreparation(latest_id, response.responseId);
    if (!cloneResult.success) {
        await deleteBudgetRevisions(response.responseId);
        return {
            success: false,
            message: cloneResult.message || 'Failed to clone budget preparation data.',
        };
    }

    return { success: true, message: 'Budget revision submitted and data cloned successfully.', responseId: response.responseId };
}

async function cloneBudgetPreparation(oldBudgetId: string, newBudgetId: string): Promise<{ success: boolean; message: string }> {
    const supabase = await createClient();

    // Step 1: Fetch budgets by old budget revision ID
    const { data: oldBudgets, error: fetchError } = await supabase
        .schema('budget')
        .from('budgets')
        .select('*')
        .eq('budget_revision_id', oldBudgetId);

    if (fetchError || !oldBudgets || oldBudgets.length === 0) {
        return { success: false, message: 'Failed to fetch old budget data' };
    }

    // Step 2: Prepare new data
    const clonedBudgets = oldBudgets.map(({ id, sub_total, created_at, ...budget }) => ({
        ...budget,
        budget_revision_id: newBudgetId,
    }));

    // Step 3: Insert cloned data
    const { error: insertError } = await supabase
        .schema('budget')
        .from('budgets')
        .insert(clonedBudgets);

    if (insertError) {
        return { success: false, message: 'Failed to insert cloned budget data' };
    }

    return { success: true, message: 'success' };
}

export async function deleteBudgetRevisions(id: string): Promise<boolean> {
    const supabase = await createClient();

    const { error } = await supabase
        .schema('budget')
        .from('budget_revisions')
        .delete()
        .eq('id', id);

    if (error) return false;

    return true;
}

export async function setBudgetArchived(parentId: string): Promise<boolean> {
    const supabase = await createClient();

    try {
        // Update the parent record
        const { error: parentError } = await supabase
            .schema('budget')
            .from('budget_revisions')
            .update({ is_archived: true })
            .eq('id', parentId);

        if (parentError) {
            console.error('Failed to archive parent budget');
            return false;
        }

        // Update child records
        const { error: childError } = await supabase
            .schema('budget')
            .from('budget_revisions')
            .update({ is_archived: true })
            .eq('parent_id', parentId);

        if (childError) {
            console.error('Failed to archive child budgets');
            return false;
        }

        return true;
    } catch (err) {
        console.error('Unexpected error archiving budget:', err);
        return false;
    }
}