import { createClient } from "@/utils/supabase/server";
import { Database } from "@/types/database.types";
import type {
  CreditNote,
  CreditNoteFilters,
  CreditNoteSummary,
  CreateCreditNoteInput,
  ApplyCreditNoteInput,
  CustomerCreditStatement,
  CreditNoteApplication,
  CreditNoteItem,
} from "@/types/credit-notes/credit-notes.types";

export async function getCreditNotesServer(filters: CreditNoteFilters) {
  const supabase = await createClient();
  
  let query = supabase
    .from("invoices")
    .select(`
      *,
      customer:customers!inner(
        id,
        customer_code,
        name,
        display_name,
        email,
        phone
      ),
      items:invoice_items(count),
      applications:credit_note_applications!credit_note_id(
        amount_applied
      )
    `, { count: "exact" })
    .eq("invoice_type", "CREDIT_NOTE");

  // Apply filters
  if (filters.search) {
    query = query.or(
      `invoice_number.ilike.%${filters.search}%,customer.name.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%`
    );
  }

  if (filters.status && filters.status.length > 0) {
    query = query.in("status", filters.status);
  }

  if (filters.customer_id) {
    query = query.eq("customer_id", filters.customer_id);
  }

  if (filters.date_from) {
    query = query.gte("invoice_date", filters.date_from);
  }

  if (filters.date_to) {
    query = query.lte("invoice_date", filters.date_to);
  }

  if (filters.amount_min) {
    query = query.gte("total_amount", filters.amount_min);
  }

  if (filters.amount_max) {
    query = query.lte("total_amount", filters.amount_max);
  }

  // Sorting
  const sortColumn = filters.sort_by || "invoice_date";
  const sortOrder = filters.sort_order || "desc";
  query = query.order(sortColumn, { ascending: sortOrder === "asc" });

  // Pagination
  const page = filters.page || 1;
  const limit = filters.limit || 10;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching credit notes:", error);
    throw error;
  }

  // Calculate unapplied amounts
  const creditNotes = (data || []).map((note) => {
    const totalApplied = note.applications?.reduce(
      (sum: number, app: CreditNoteApplication) => sum + app.amount_applied,
      0
    ) || 0;
    return {
      ...note,
      unapplied_amount: note.total_amount - totalApplied,
      items_count: note.items?.[0]?.count || 0,
    };
  });

  return {
    data: creditNotes,
    total: count || 0,
    page,
    limit,
    totalPages: Math.ceil((count || 0) / limit),
  };
}

export async function getCreditNoteByIdServer(id: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from("invoices")
    .select(`
      *,
      customer:customers!inner(
        id,
        customer_code,
        name,
        display_name,
        email,
        phone
      ),
      entity:entities!inner(
        id,
        name,
        code
      ),
      items:invoice_items(
        id,
        line_number,
        description,
        quantity,
        unit_price,
        discount_amount,
        discount_percentage,
        tax_amount,
        tax_percentage,
        line_total,
        item_code,
        gl_account_code,
        created_at,
        updated_at
      ),
      applications:credit_note_applications!credit_note_id(
        id,
        invoice_id,
        amount_applied,
        application_date,
        created_at,
        created_by,
        invoice:invoices!invoice_id(
          id,
          invoice_number,
          invoice_date,
          total_amount,
          payment_status
        )
      ),
      audit_logs:audit_log(
        id,
        action,
        changed_at,
        changed_by,
        changed_fields
      )
    `)
    .eq("id", id)
    .eq("invoice_type", "CREDIT_NOTE")
    .single();

  if (error) {
    console.error("Error fetching credit note:", error);
    throw error;
  }

  // Calculate unapplied amount
  const totalApplied = data.applications?.reduce(
    (sum: number, app: CreditNoteApplication) => sum + app.amount_applied,
    0
  ) || 0;

  return {
    ...data,
    unapplied_amount: data.total_amount - totalApplied,
  } as CreditNote;
}

export async function getCreditNotesSummaryServer(): Promise<CreditNoteSummary> {
  const supabase = await createClient();
  
  // Get total count and value
  const { data: totalData, error: totalError } = await supabase
    .from("invoices")
    .select("id, total_amount")
    .eq("invoice_type", "CREDIT_NOTE")
    .not("status", "eq", "CANCELLED");

  if (totalError) {
    console.error("Error fetching totals:", totalError);
    throw totalError;
  }

  const total_count = totalData?.length || 0;
  const total_value = totalData?.reduce((sum: number, note: { id: string; total_amount: number }) => sum + note.total_amount, 0) || 0;

  // Get unapplied credits
  const { data: unappliedData, error: unappliedError } = await supabase
    .rpc("get_unapplied_credits_total");

  if (unappliedError) {
    console.error("Error fetching unapplied credits:", unappliedError);
  }

  const unapplied_credits = unappliedData || 0;

  // Get monthly credits (current month)
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  const { data: monthlyData, error: monthlyError } = await supabase
    .from("invoices")
    .select("total_amount")
    .eq("invoice_type", "CREDIT_NOTE")
    .gte("invoice_date", startOfMonth.toISOString())
    .not("status", "eq", "CANCELLED");

  if (monthlyError) {
    console.error("Error fetching monthly credits:", monthlyError);
  }

  const monthly_credits = monthlyData?.reduce((sum: number, note: { total_amount: number }) => sum + note.total_amount, 0) || 0;

  // Get expiring soon count (next 30 days)
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

  const { count: expiringCount, error: expiringError } = await supabase
    .from("invoices")
    .select("id", { count: "exact", head: true })
    .eq("invoice_type", "CREDIT_NOTE")
    .not("status", "eq", "CANCELLED")
    .not("expiry_date", "is", null)
    .lte("expiry_date", thirtyDaysFromNow.toISOString())
    .gte("expiry_date", new Date().toISOString());

  if (expiringError) {
    console.error("Error fetching expiring credits:", expiringError);
  }

  return {
    total_count,
    total_value,
    unapplied_credits,
    monthly_credits,
    expiring_soon_count: expiringCount || 0,
  };
}

export async function createCreditNoteServer(input: CreateCreditNoteInput) {
  const supabase = await createClient();
  
  // Start a transaction by creating the credit note and items in sequence
  const { data: creditNote, error: creditError } = await supabase
    .from("invoices")
    .insert({
      invoice_type: "CREDIT_NOTE",
      customer_id: input.customer_id,
      entity_id: input.entity_id,
      invoice_date: input.invoice_date,
      due_date: input.due_date,
      total_amount: 0, // Will be calculated
      status: "DRAFT",
      payment_status: "UNPAID",
      currency_code: input.currency_code,
      reference_number: input.reference_number,
      notes: input.notes,
      created_at: new Date().toISOString(),
      created_by: "current_user", // Should be replaced with actual user
    })
    .select()
    .single();

  if (creditError) {
    console.error("Error creating credit note:", creditError);
    throw creditError;
  }

  // Insert items
  const itemsToInsert = input.items.map((item) => ({
    invoice_id: creditNote.id,
    line_number: item.line_number,
    description: item.description,
    quantity: item.quantity,
    unit_price: item.unit_price,
    discount_amount: item.discount_amount || 0,
    discount_percentage: item.discount_percentage || 0,
    tax_amount: (item.quantity * item.unit_price * item.tax_percentage) / 100,
    tax_percentage: item.tax_percentage,
    line_total: item.quantity * item.unit_price,
    item_code: item.item_code,
    gl_account_code: item.gl_account_code,
    created_at: new Date().toISOString(),
  }));

  const { data: items, error: itemsError } = await supabase
    .from("invoice_items")
    .insert(itemsToInsert)
    .select();

  if (itemsError) {
    console.error("Error creating credit note items:", itemsError);
    // Rollback by deleting the credit note
    await supabase.from("invoices").delete().eq("id", creditNote.id);
    throw itemsError;
  }

  // Calculate total amount
  const totalAmount = items.reduce((sum: number, item: CreditNoteItem) => sum + item.line_total + item.tax_amount, 0);

  // Update credit note with total amount
  const { data: updatedCreditNote, error: updateError } = await supabase
    .from("invoices")
    .update({ total_amount: totalAmount })
    .eq("id", creditNote.id)
    .select()
    .single();

  if (updateError) {
    console.error("Error updating credit note total:", updateError);
    throw updateError;
  }

  return updatedCreditNote;
}

export async function getCustomerCreditBalanceServer(customerId: string) {
  const supabase = await createClient();
  
  // Get all credit notes for the customer
  const { data: creditNotes, error: creditError } = await supabase
    .from("invoices")
    .select(`
      id,
      total_amount,
      applications:credit_note_applications!credit_note_id(
        amount_applied
      )
    `)
    .eq("customer_id", customerId)
    .eq("invoice_type", "CREDIT_NOTE")
    .not("status", "eq", "CANCELLED");

  if (creditError) {
    console.error("Error fetching customer credit notes:", creditError);
    throw creditError;
  }

  // Calculate total available credit
  const totalCredit = creditNotes?.reduce((total: number, note: { id: string; total_amount: number; applications?: Array<{ amount_applied: number }> }) => {
    const applied = note.applications?.reduce((sum: number, app: { amount_applied: number }) => sum + app.amount_applied, 0) || 0;
    return total + (note.total_amount - applied);
  }, 0) || 0;

  return {
    customer_id: customerId,
    total_credit_balance: totalCredit,
    credit_notes_count: creditNotes?.length || 0,
  };
}

export async function getOutstandingInvoicesServer(customerId: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from("invoices")
    .select(`
      id,
      invoice_number,
      invoice_date,
      total_amount,
      payment_status,
      payments:customer_payments!invoice_id(
        amount
      ),
      credit_applications:credit_note_applications!invoice_id(
        amount_applied
      )
    `)
    .eq("customer_id", customerId)
    .eq("invoice_type", "STANDARD")
    .in("payment_status", ["UNPAID", "PARTIAL"])
    .order("invoice_date", { ascending: true });

  if (error) {
    console.error("Error fetching outstanding invoices:", error);
    throw error;
  }

  // Calculate outstanding amounts
  const invoicesWithOutstanding = (data || []).map((invoice) => {
    const paidAmount = invoice.payments?.reduce((sum: number, payment: { amount: number }) => sum + payment.amount, 0) || 0;
    const creditApplied = invoice.credit_applications?.reduce((sum: number, app: { amount_applied: number }) => sum + app.amount_applied, 0) || 0;
    const totalPaid = paidAmount + creditApplied;
    
    return {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      total_amount: invoice.total_amount,
      payment_status: invoice.payment_status,
      outstanding_amount: invoice.total_amount - totalPaid,
    };
  });

  return invoicesWithOutstanding.filter((inv: { outstanding_amount: number }) => inv.outstanding_amount > 0);
}