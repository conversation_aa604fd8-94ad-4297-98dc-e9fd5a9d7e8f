"use client";

import { createClient } from "@/utils/supabase/Client";
import type {
  CreditNote,
  CreditNoteFilters,
  CreditNoteSummary,
  CreateCreditNoteInput,
  ApplyCreditNoteInput,
  CustomerCreditStatement,
  CreditNoteExportData,
} from "@/types/credit-notes/credit-notes.types";

export async function getCreditNotes(filters: CreditNoteFilters) {
  const supabase = await createClient();
  let query = supabase
    .from("invoices")
    .select(`
      *,
      customer:customers!inner(
        id,
        customer_code,
        name,
        display_name,
        email,
        phone
      ),
      items:invoice_items(
        id,
        line_number,
        description,
        quantity,
        unit_price,
        discount_amount,
        discount_percentage,
        tax_amount,
        tax_percentage,
        line_total,
        item_code,
        gl_account_code,
        created_at,
        updated_at
      ),
      applications:credit_note_applications!credit_note_id(
        id,
        invoice_id,
        amount_applied,
        application_date,
        created_at,
        created_by
      )
    `)
    .eq("invoice_type", "CREDIT_NOTE");

  // Apply filters
  if (filters.search) {
    query = query.or(
      `invoice_number.ilike.%${filters.search}%,customer.name.ilike.%${filters.search}%,reference_number.ilike.%${filters.search}%`
    );
  }

  if (filters.status && filters.status.length > 0) {
    query = query.in("status", filters.status);
  }

  if (filters.customer_id) {
    query = query.eq("customer_id", filters.customer_id);
  }

  if (filters.date_from) {
    query = query.gte("invoice_date", filters.date_from);
  }

  if (filters.date_to) {
    query = query.lte("invoice_date", filters.date_to);
  }

  if (filters.amount_min) {
    query = query.gte("total_amount", filters.amount_min);
  }

  if (filters.amount_max) {
    query = query.lte("total_amount", filters.amount_max);
  }

  // Sorting
  const sortColumn = filters.sort_by || "invoice_date";
  const sortOrder = filters.sort_order || "desc";
  query = query.order(sortColumn, { ascending: sortOrder === "asc" });

  // Pagination
  const page = filters.page || 1;
  const limit = filters.limit || 10;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching credit notes:", error);
    throw error;
  }

  // Calculate unapplied amounts
  const creditNotes = (data || []).map((note) => {
    const totalApplied = note.applications?.reduce(
      (sum: number, app: any) => sum + app.amount_applied,
      0
    ) || 0;
    return {
      ...note,
      unapplied_amount: note.total_amount - totalApplied,
    } as CreditNote;
  });

  return {
    data: creditNotes,
    total: count || 0,
    page,
    limit,
    totalPages: Math.ceil((count || 0) / limit),
  };
}

export async function getCreditNoteById(id: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("invoices")
    .select(`
      *,
      customer:customers!inner(
        id,
        customer_code,
        name,
        display_name,
        email,
        phone
      ),
      items:invoice_items(
        id,
        line_number,
        description,
        quantity,
        unit_price,
        discount_amount,
        discount_percentage,
        tax_amount,
        tax_percentage,
        line_total,
        item_code,
        gl_account_code,
        created_at,
        updated_at
      ),
      applications:credit_note_applications!credit_note_id(
        id,
        invoice_id,
        amount_applied,
        application_date,
        created_at,
        created_by,
        invoice:invoices!invoice_id(
          id,
          invoice_number,
          invoice_date,
          total_amount,
          payment_status
        )
      )
    `)
    .eq("id", id)
    .eq("invoice_type", "CREDIT_NOTE")
    .single();

  if (error) {
    console.error("Error fetching credit note:", error);
    throw error;
  }

  // Calculate unapplied amount
  const totalApplied = data.applications?.reduce(
    (sum: number, app: any) => sum + app.amount_applied,
    0
  ) || 0;

  return {
    ...data,
    unapplied_amount: data.total_amount - totalApplied,
  } as CreditNote;
}

export async function getCreditNotesSummary(): Promise<CreditNoteSummary> {
  const supabase = await createClient();
  const { data: countData, error: countError } = await supabase
    .rpc("get_credit_notes_summary");

  if (countError) {
    console.error("Error fetching credit notes summary:", countError);
    throw countError;
  }

  return countData || {
    total_count: 0,
    total_value: 0,
    unapplied_credits: 0,
    monthly_credits: 0,
    expiring_soon_count: 0,
  };
}

export async function createCreditNote(input: CreateCreditNoteInput) {
  const supabase = await createClient();
  const { data: creditNote, error: creditError } = await supabase
    .rpc("create_credit_note", {
      p_customer_id: input.customer_id,
      p_entity_id: input.entity_id,
      p_invoice_date: input.invoice_date,
      p_due_date: input.due_date || null,
      p_currency_code: input.currency_code,
      p_reference_number: input.reference_number || null,
      p_notes: input.notes || null,
      p_credit_reason: input.credit_reason,
      p_original_invoice_id: input.original_invoice_id || null,
      p_original_order_id: input.original_order_id || null,
      p_expiry_date: input.expiry_date || null,
      p_application_method: input.application_method,
      p_application_restrictions: input.application_restrictions || null,
      p_items: input.items,
    });

  if (creditError) {
    console.error("Error creating credit note:", creditError);
    throw creditError;
  }

  return creditNote;
}

export async function updateCreditNote(id: string, updates: Partial<CreateCreditNoteInput>) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("invoices")
    .update({
      invoice_date: updates.invoice_date,
      due_date: updates.due_date,
      reference_number: updates.reference_number,
      notes: updates.notes,
      updated_at: new Date().toISOString(),
    })
    .eq("id", id)
    .eq("invoice_type", "CREDIT_NOTE")
    .select()
    .single();

  if (error) {
    console.error("Error updating credit note:", error);
    throw error;
  }

  return data;
}

export async function applyCreditNote(input: ApplyCreditNoteInput) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("apply_credit_note", {
      p_credit_note_id: input.credit_note_id,
      p_applications: input.applications,
    });

  if (error) {
    console.error("Error applying credit note:", error);
    throw error;
  }

  return data;
}

export async function reverseCreditNote(id: string, reason: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("reverse_credit_note", {
      p_credit_note_id: id,
      p_reversal_reason: reason,
    });

  if (error) {
    console.error("Error reversing credit note:", error);
    throw error;
  }

  return data;
}

export async function getCustomerCreditBalance(customerId: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("get_customer_credit_balance", {
      p_customer_id: customerId,
    });

  if (error) {
    console.error("Error fetching customer credit balance:", error);
    throw error;
  }

  return data;
}

export async function getCustomerCreditStatement(
  customerId: string,
  fromDate: string,
  toDate: string
): Promise<CustomerCreditStatement> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .rpc("get_customer_credit_statement", {
      p_customer_id: customerId,
      p_from_date: fromDate,
      p_to_date: toDate,
    });

  if (error) {
    console.error("Error fetching customer credit statement:", error);
    throw error;
  }

  return data;
}

export async function exportCreditNotes(filters: CreditNoteFilters): Promise<CreditNoteExportData> {
  const allData = await getCreditNotes({ ...filters, limit: 1000 });
  const summary = await getCreditNotesSummary();

  return {
    credit_notes: allData.data,
    summary,
    export_date: new Date().toISOString(),
    exported_by: "current_user", // This should be replaced with actual user info
  };
}

export async function getOutstandingInvoices(customerId: string) {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("invoices")
    .select(`
      id,
      invoice_number,
      invoice_date,
      total_amount,
      payment_status
    `)
    .eq("customer_id", customerId)
    .eq("invoice_type", "STANDARD")
    .in("payment_status", ["UNPAID", "PARTIAL"])
    .order("invoice_date", { ascending: true });

  if (error) {
    console.error("Error fetching outstanding invoices:", error);
    throw error;
  }

  // Calculate outstanding amounts
  const invoicesWithOutstanding = await Promise.all(
    (data || []).map(async (invoice) => {
      const innerSupabase = await createClient();
      const { data: payments } = await innerSupabase
        .rpc("get_invoice_payment_summary", {
          p_invoice_id: invoice.id,
        });

      const paidAmount = payments?.total_paid || 0;
      return {
        ...invoice,
        outstanding_amount: invoice.total_amount - paidAmount,
      };
    })
  );

  return invoicesWithOutstanding.filter((inv) => inv.outstanding_amount > 0);
}

export async function getCreditReasons() {
  return [
    { value: "RETURN", label: "Product Return" },
    { value: "PRICING_ADJUSTMENT", label: "Pricing Adjustment" },
    { value: "SERVICE_ISSUE", label: "Service Issue" },
    { value: "PROMOTIONAL", label: "Promotional Credit" },
    { value: "OTHER", label: "Other" },
  ];
}

export async function getApplicationMethods() {
  return [
    { value: "AUTO_APPLY", label: "Auto Apply to Outstanding" },
    { value: "MANUAL", label: "Manual Application" },
    { value: "HOLD", label: "Hold for Customer Instruction" },
  ];
}