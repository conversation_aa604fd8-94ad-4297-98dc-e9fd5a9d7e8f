"use client"

import { invoiceDetails, invoiceDetailsView } from '@/types/finance/invoices.types';
import { createClient } from '@/utils/supabase/Client';
import { saveAs } from 'file-saver';

export async function getAllInvoices(
    filters: {
        subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    searchTerm?: string
): Promise<{
    invoices: invoiceDetailsView[] | null;
    success: boolean;
    total: number;
    currentPage: number;
    allInvoiceIds: string[];
}> {
    const supabase = await createClient();

    try {
        // Pagination
        const start = (page - 1) * limit;
        const end = start + limit - 1;

        // Main query
        let mainQuery = supabase
            .schema("finance")
            .from("invoice_details")
            .select('*', { count: "exact" })
            .range(start, end);

        // Reapply filters on the main query
        Object.keys(filters.subFilters).forEach((filterKey) => {
            const filterValues = filters.subFilters[filterKey];
            if (Array.isArray(filterValues) && filterValues.length > 0) {
                mainQuery = mainQuery.in(filterKey, filterValues);
            }
        });

        // Apply search term filter if present
        if (searchTerm) {
            const normalizedSearchTerm = searchTerm.trim();
            mainQuery = mainQuery.or(
                `invoice_number.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
            );
        }

        const { data: invoicesData, count: total, error: invoicesError } = await mainQuery;
        if (invoicesError) {
            throw new Error();
        }

        if (!invoicesData || invoicesData.length === 0) {
            return { invoices: [], success: true, total: 0, currentPage: page, allInvoiceIds: [] };
        }

        const allInvoiceIds = invoicesData.map((inv) => inv.invoice_id);

        return {
            invoices: invoicesData,
            success: true,
            total: total || 0,
            currentPage: page,
            allInvoiceIds,
        };
    } catch {
        return { invoices: null, success: false, total: 0, currentPage: page, allInvoiceIds: [] };
    }
}

export const exportInvoices = async (selectedIds: (string | null)[]) => {
    const supabase = await createClient();

    const { data, error } = await supabase.functions.invoke('export-invoices', {
        body: {
            selectedIds: selectedIds,
        }
    });

    if (error) {
        return null;
    }

    return data;
}

export async function getSpecificInvoices(invoiceId: string): Promise<invoiceDetails | null> {
    const supabase = await createClient()

    const { data, error } = await supabase
        .schema('finance')
        .from('invoices')
        .select('*, invoice_items(*), receipts(*)')
        .eq('id', invoiceId)
        .single()

    if (error || !data) {
        return null
    }

    const formattedData: invoiceDetails = {
        ...data,
        invoice_items: data.invoice_items || []
    }

    return formattedData
}

export async function generateInvoicePdf(invoiceId: string): Promise<boolean> {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.functions.invoke('create-invoice', {
            body: {
                invoice_id: invoiceId
            }
        });

        if (error || !data || !data.result) {
            console.error("Error generating invoice PDF:", error);
            return false;
        }

        return true;
    } catch (error) {
        console.error("Error in generateInvoicePdf:", error);
        return false;
    }
}

export async function downloadInvoicePdf(invoiceId: string, fileName?: string): Promise<boolean> {
    try {
        const supabase = await createClient();

        // Always generate a new PDF
        const generated = await generateInvoicePdf(invoiceId);
        if (!generated) {
            throw new Error("Failed to generate invoice PDF");
        }

        // Fetch the invoice to get the file_name
        const invoice = await getSpecificInvoices(invoiceId);
        if (!invoice || !invoice.file_name) {
            throw new Error("Invoice PDF was not generated properly");
        }

        fileName = invoice.file_name;

        // Now we have the fileName, so download the file
        const { data, error } = await supabase
            .storage
            .from('sales-invoices')
            .createSignedUrl(fileName, 60); // URL valid for 60 seconds

        if (error || !data) {
            console.error("Error creating signed URL:", error);
            return false;
        }

        const response = await fetch(data.signedUrl);
        const blob = await response.blob();
        saveAs(blob, fileName);

        return true;
    } catch (error) {
        console.error("Error downloading invoice PDF:", error);
        return false;
    }
}

// ----------------------------------
// fetch options data
// ----------------------------------
export async function getTaxFromConfig(): Promise<number | null> {
    const supabase = await createClient()

    const { data, error } = await supabase
        .from('sys_configs')
        .select('config_value')
        .eq('config_key', 'TAX_RATE')
        .single()

    if (error || !data) {
        return null
    }

    const parsed = parseFloat(data.config_value);
    return isNaN(parsed) ? null : parsed;
}

export async function getPaymentOptions(): Promise<{ code: string, label: string }[]> {
    const supabase = await createClient();

    const { data, error } = await supabase
        .schema('system')
        .from('options')
        .select('code, label')
        .eq('type', 'payment_options')
        .is('is_active', true);

    if (error || !data) {
        return [];
    }

    return data;
}

export async function getInvoiceEntities(): Promise<{ id: string, name: string }[]> {
    const supabase = await createClient();

    const { data, error } = await supabase
        .schema('finance')
        .from('entities')
        .select('id, name')
        .is('is_active', true);

    if (error || !data) {
        return [];
    }

    return data;
}

export async function getGLAccountsByEntity(entityId: string): Promise<{ account_code: string, account_name: string }[]> {
    const supabase = await createClient();

    // First get GL accounts that are linked to this entity through gl_account_entity_settings
    const { data: entitySettings, error: settingsError } = await supabase
        .schema('finance')
        .from('gl_account_entity_settings')
        .select('account_id')
        .eq('entity_id', entityId)
        .is('is_active', true);

    if (settingsError || !entitySettings) {
        return [];
    }

    const accountIds = entitySettings.map(setting => setting.account_id).filter(id => id !== null);

    if (accountIds.length === 0) {
        return [];
    }

    // Now get the GL accounts that match these IDs and are revenue accounts
    const { data: accounts, error: accountsError } = await supabase
        .schema('finance')
        .from('gl_accounts')
        .select('account_code, account_name')
        .in('id', accountIds)
        .eq('account_category', 'revenue')
        .is('is_active', true)
        .order('account_code');

    if (accountsError || !accounts) {
        return [];
    }

    return accounts;
}