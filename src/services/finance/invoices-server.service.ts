"use server"

import { addInvoiceFormData } from "@/hooks/finance/add-invoices"
import { EditInvoiceFormData } from "@/hooks/finance/edit-invoices"
import { createClient } from "@/utils/supabase/server"

// -----------------------------
// Create invoice
// -----------------------------
export async function createInvoiceDetails(formData: addInvoiceFormData): Promise<{ success: boolean, newId?: string }> {
    const supabase = await createClient()

    try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            throw new Error()
        }
        
        // Insert the invoice
        const { data: invoiceData, error: invoiceError } = await supabase
            .schema('finance')
            .from('invoices')
            .insert({
                entity_id: formData.entities,
                issue_date: formData.issueDate,
                due_date: formData.dueDate,
                subtotal: formData.subtotal,
                tax_amount: formData.totalTax,
                total_amount: formData.grandTotal,
                notes: formData.notes,
                bill_to: formData.billTo,
                payment_terms: formData.paymentTerms,
                metadata: {
                    attn: formData.attentionTo,
                    title: formData.title
                },
                created_by: user.id,
                updated_by: user.id
            })
            .select('id')
            .single()

        if (invoiceError || !invoiceData) throw invoiceError;

        const invoiceItems = formData.invoice_items.map((item) => ({
            invoice_id: invoiceData.id,
            gl_account_code: item.gl_account_code,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            tax_rate: item.tax_rate,
            tax_amount: item.tax_amount,
            line_total: item.line_total,
            metadata: {
                price_type: item.price_type
            }
        }))

        // Insert invoice items
        const { error: itemsError } = await supabase
            .schema('finance')
            .from('invoice_items')
            .insert(invoiceItems)

        if (itemsError) throw itemsError;

        return { success: true, newId: invoiceData.id }
    } catch (error) {
        console.log(error)
        return { success: false }
    }
}

// -----------------------------
// Edit invoice
// -----------------------------
export async function updateInvoiceDetails(formData: EditInvoiceFormData): Promise<{ success: boolean, receiptIds?: string[] }> {
    const supabase = await createClient()

    try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            throw new Error()
        }

        const { error: invoiceError } = await supabase
            .schema('finance')
            .from('invoices')
            .update({
                issue_date: formData.issueDate,
                due_date: formData.dueDate,
                status: formData.status,
                subtotal: formData.subtotal,
                tax_amount: formData.totalTax,
                total_amount: formData.grandTotal,
                notes: formData.notes,
                bill_to: formData.billTo,
                payment_terms: formData.paymentTerms,
                metadata: {
                    attn: formData.attentionTo,
                    title: formData.title
                },
                updated_by: user.id
            })
            .eq('id', formData.id)

        if (invoiceError) throw invoiceError;

        const itemsToInsert = formData.invoice_items.filter(item => !item.id).map(item => ({
            invoice_id: formData.id,
            gl_account_code: item.gl_account_code,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            tax_rate: item.tax_rate,
            tax_amount: item.tax_amount,
            line_total: item.line_total,
            metadata: {
                price_type: item.price_type
            }
        }));

        const itemsToUpdate = formData.invoice_items.filter(item => item.id).map(item => ({
            id: item.id,
            invoice_id: formData.id,
            gl_account_code: item.gl_account_code,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            tax_rate: item.tax_rate,
            tax_amount: item.tax_amount,
            line_total: item.line_total,
            metadata: {
                price_type: item.price_type
            }
        }));

        if (itemsToInsert.length > 0) {
            const { error: insertError } = await supabase
                .schema('finance')
                .from('invoice_items')
                .insert(itemsToInsert);

            if (insertError) throw insertError;
        }

        for (const item of itemsToUpdate) {
            const { error: updateError } = await supabase
                .schema('finance')
                .from('invoice_items')
                .update({
                    gl_account_code: item.gl_account_code,
                    description: item.description,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    tax_rate: item.tax_rate,
                    tax_amount: item.tax_amount,
                    line_total: item.line_total,
                    metadata: item.metadata
                })
                .eq('id', item.id);

            if (updateError) throw updateError;
        }

        const newReceipts = [];

        for (const receipt of formData.receipts.filter(r => r.canEdit)) {
            const { data: nextReceiptNo, error: receiptNoError } = await supabase.schema('finance').rpc('get_next_receipt_number');
            if (receiptNoError || !nextReceiptNo) throw receiptNoError;

            newReceipts.push({
                receipt_number: nextReceiptNo,
                invoice_id: formData.id,
                payment_date: receipt.dateOfPayment,
                payment_method: receipt.paymentMode,
                amount_paid: receipt.amountPaid,
                payment_reference: receipt.referenceNo,
                payment_status: 'SUCCESS',
                created_by: user.id
            });
        }

        let insertedReceiptIds: string[] = [];

        if (newReceipts.length > 0) {
            const { data: insertedReceipts, error: receiptsError } = await supabase
                .schema('finance')
                .from('receipts')
                .insert(newReceipts)
                .select('id');

            if (receiptsError) throw receiptsError;

            insertedReceiptIds = insertedReceipts.map(r => r.id);
        }

        return { success: true, receiptIds: insertedReceiptIds };
    } catch {
        return { success: false }
    }
}

export async function deleteInvoiceDetails(itemIds: string[], receiptIds: string[]): Promise<{ success: boolean }> {
    const supabase = await createClient();

    try {
        // Delete invoice items
        if (itemIds.length > 0) {
            const { error: itemError } = await supabase
                .schema('finance')
                .from('invoice_items')
                .delete()
                .in('id', itemIds);

            if (itemError) throw itemError;
        }

        // Delete receipts
        if (receiptIds.length > 0) {
            const { data: receiptsToDelete, error: fetchError } = await supabase
                .schema('finance')
                .from('receipts')
                .select('file_name')
                .in('id', receiptIds);

            if (fetchError) throw fetchError;

            // Delete files from storage if file_name exists
            const filesToDelete = receiptsToDelete
                .map(r => r.file_name)
                .filter((name): name is string => Boolean(name)); // filter out null/undefined

            if (filesToDelete.length > 0) {
                const { error: storageError } = await supabase.storage
                    .from('sales_receipts')
                    .remove(filesToDelete);

                if (storageError) throw storageError;
            }

            // Delete receipts from DB
            const { error: receiptError } = await supabase
                .schema('finance')
                .from('receipts')
                .delete()
                .in('id', receiptIds);

            if (receiptError) throw receiptError;
        }

        return { success: true };
    } catch {
        return { success: false };
    }
}
