import { receiptDetails, receiptsInterface } from '@/types/finance/receipts.types';
import { createClient } from '@/utils/supabase/Client';
import saveAs from 'file-saver';

export async function getAllReceipts(
    filters: {
        subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    searchTerm?: string
): Promise<{
    receipts: receiptsInterface[] | null;
    success: boolean;
    total: number;
    currentPage: number;
    allReceiptIds: string[];
}> {
    const supabase = await createClient();

    try {
        let baseQuery = supabase
            .schema("finance")
            .from("receipt_user")
            .select("receipt_id", { count: "exact" });

        // Add subFilters
        Object.keys(filters.subFilters).forEach((filterKey) => {
            const filterValues = filters.subFilters[filterKey];
            if (Array.isArray(filterValues) && filterValues.length > 0) {
                baseQuery = baseQuery.in(filterKey, filterValues);
            }
        });

        // Apply search term filter if present
        if (searchTerm) {
            const normalizedSearchTerm = searchTerm.trim();
            baseQuery = baseQuery.or(
                `receipt_number.ilike.%${normalizedSearchTerm}%,invoice_number.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
            );
        }

        const { data: allReceiptIdsData, count: total, error: totalError } = await baseQuery;
        if (totalError) {
            throw new Error();
        }

        const allReceiptIds = allReceiptIdsData?.map((rec) => rec.receipt_id) || [];

        // Handle case where no receipts match
        if (!total) {
            return { receipts: [], success: true, total: 0, currentPage: 1, allReceiptIds: [] };
        }

        // Pagination
        const maxPage = Math.ceil(total / limit);
        const adjustedPage = Math.max(1, Math.min(page, maxPage));
        const start = (adjustedPage - 1) * limit;
        const end = start + limit - 1;

        // Main query for receipts
        let mainQuery = supabase
            .schema("finance")
            .from("receipt_user")
            .select(
                `
            receipt_id,
            receipt_number,
            invoice_number,
            payment_date,
            payment_method,
            payment_status,
            payment_reference,
            full_name
            `
            )
            .range(start, end);

        // Reapply filters on the main query
        Object.keys(filters.subFilters).forEach((filterKey) => {
            const filterValues = filters.subFilters[filterKey];
            if (Array.isArray(filterValues) && filterValues.length > 0) {
                mainQuery = mainQuery.in(filterKey, filterValues);
            }
        });

        // Apply search term filter if present
        if (searchTerm) {
            const normalizedSearchTerm = searchTerm.trim();
            mainQuery = mainQuery.or(
                `receipt_number.ilike.%${normalizedSearchTerm}%,invoice_number.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
            );
        }

        const { data: receiptsData, error: receiptsError } = await mainQuery;
        if (receiptsError) {
            throw new Error();
        }

        if (!receiptsData || receiptsData.length === 0) {
            return { receipts: [], success: true, total, currentPage: adjustedPage, allReceiptIds };
        }

        const enrichedReceipts = receiptsData.map((receipt) => ({
            receipt_id: receipt.receipt_id ?? "-",
            receipt_number: receipt.receipt_number ?? "-",
            invoice_number: receipt.invoice_number ?? "-",
            payment_date: receipt.payment_date ? new Date(receipt.payment_date).toLocaleDateString() : "-",
            payment_method: receipt.payment_method ?? "-",
            payment_status: receipt.payment_status ?? "-",
            payment_reference: receipt.payment_reference ?? "-",
            payment_by: receipt.full_name ?? "-",
        }));

        return {
            receipts: enrichedReceipts,
            success: true,
            total,
            currentPage: adjustedPage,
            allReceiptIds,
        };
    } catch {
        return { receipts: null, success: false, total: 0, currentPage: page, allReceiptIds: [] };
    }
}

export const exportReceipts = async (selectedIds: (string | null)[]) => {
    const supabase = await createClient();

    const { data, error } = await supabase.functions.invoke('export-receipts', {
        body: {
            selectedIds: selectedIds,
        }
    })

    if (error) {
        return null;
    }

    return data;
}

export async function getSpecificReceipts(receiptId: string): Promise<receiptDetails | null> {
    const supabase = await createClient()

    const { data, error } = await supabase
        .schema('finance')
        .from('receipts')
        .select('*')
        .eq('id', receiptId)
        .single()

    if (error || !data) {
        return null
    }

    return data
}

export async function generateReceiptPdf(receiptId: string): Promise<boolean> {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.functions.invoke('create-receipt', {
            body: {
                receipt_id: receiptId
            }
        });

        if (error || !data || !data.result) throw error;

        return true;
    } catch {
        return false;
    }
}

export async function downloadReceiptPdf(receiptId: string): Promise<boolean> {
    try {
        const supabase = await createClient();
        let fileName = null;

        // Get the receipt to check if it has a file_name
        const receipt = await getSpecificReceipts(receiptId);

        if (!receipt || !receipt.file_name) {
            // Generate the PDF first
            const generated = await generateReceiptPdf(receiptId);
            if (!generated) {
                throw new Error("Failed to generate invoice PDF");
            }

            // Now fetch the updated invoice to get the file_name
            const updatedInvoice = await getSpecificReceipts(receiptId);
            if (!updatedInvoice || !updatedInvoice.file_name) {
                throw new Error("Invoice PDF was not generated properly");
            }

            fileName = updatedInvoice.file_name;
        } else {
            fileName = receipt.file_name;
        }

        // Now we have the fileName, so download the file
        const { data, error } = await supabase
            .storage
            .from('sales-receipts')
            .createSignedUrl(fileName, 60); // URL valid for 60 seconds

        if (error || !data) throw error;

        const response = await fetch(data.signedUrl);
        const blob = await response.blob();
        saveAs(blob, fileName);

        return true;
    } catch {
        return false;
    }
}