import { createClient } from "@/utils/supabase/Client";

export interface GLAccount {
  id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  parent_account_id?: string;
  is_active: boolean;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface GLAccountsResponse {
  data: GLAccount[];
  total: number;
  page: number;
  limit: number;
}

export interface GLAccountFilters {
  search?: string;
  account_type?: string;
  is_active?: boolean;
  page?: number;
  limit?: number;
}

/**
 * Fetch GL accounts with optional filtering and pagination
 */
export async function getGLAccounts(filters: GLAccountFilters = {}): Promise<GLAccountsResponse> {
  const supabase = await createClient();
  
  try {
    const {
      search,
      account_type,
      is_active = true,
      page = 1,
      limit = 100
    } = filters;

    let query = supabase
      .from('gl_account_codes')
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`account_code.ilike.%${search}%,account_name.ilike.%${search}%`);
    }

    if (account_type) {
      query = query.eq('account_type', account_type);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by account code
    query = query.order('account_code', { ascending: true });

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching GL accounts:', error);
      throw new Error(`Failed to fetch GL accounts: ${error.message}`);
    }

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
    };
  } catch (error) {
    console.error('Error in getGLAccounts:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching GL accounts');
  }
}

/**
 * Get GL account by ID
 */
export async function getGLAccountById(id: string): Promise<GLAccount | null> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .from('gl_account_codes')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      console.error('Error fetching GL account:', error);
      throw new Error(`Failed to fetch GL account: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in getGLAccountById:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching GL account');
  }
}

/**
 * Get GL account by account code
 */
export async function getGLAccountByCode(accountCode: string): Promise<GLAccount | null> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .from('gl_account_codes')
      .select('*')
      .eq('account_code', accountCode)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      console.error('Error fetching GL account by code:', error);
      throw new Error(`Failed to fetch GL account: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in getGLAccountByCode:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching GL account');
  }
}

/**
 * Get distinct account types for filtering
 */
export async function getGLAccountTypes(): Promise<string[]> {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .from('gl_account_codes')
      .select('account_type')
      .eq('is_active', true)
      .order('account_type');

    if (error) {
      console.error('Error fetching GL account types:', error);
      throw new Error(`Failed to fetch GL account types: ${error.message}`);
    }

    // Extract unique account types
    const types = data?.map(item => item.account_type) || [];
    const uniqueTypes = Array.from(new Set(types));
    return uniqueTypes.filter(Boolean);
  } catch (error) {
    console.error('Error in getGLAccountTypes:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while fetching GL account types');
  }
}

/**
 * Search GL accounts for dropdown/autocomplete
 */
export async function searchGLAccounts(searchTerm: string, limit: number = 50): Promise<GLAccount[]> {
  const supabase = await createClient();
  
  try {
    if (!searchTerm.trim()) {
      // If no search term, return most commonly used accounts
      const { data, error } = await supabase
        .from('gl_account_codes')
        .select('*')
        .eq('is_active', true)
        .order('account_code')
        .limit(limit);

      if (error) {
        console.error('Error fetching GL accounts:', error);
        throw new Error(`Failed to fetch GL accounts: ${error.message}`);
      }

      return data || [];
    }

    const { data, error } = await supabase
      .from('gl_account_codes')
      .select('*')
      .eq('is_active', true)
      .or(`account_code.ilike.%${searchTerm}%,account_name.ilike.%${searchTerm}%`)
      .order('account_code')
      .limit(limit);

    if (error) {
      console.error('Error searching GL accounts:', error);
      throw new Error(`Failed to search GL accounts: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchGLAccounts:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred while searching GL accounts');
  }
}