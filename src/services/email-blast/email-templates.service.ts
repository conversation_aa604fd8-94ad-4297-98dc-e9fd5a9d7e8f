import { 
  EmailTemplate, 
  TemplateFormData, 
  TemplateDuplicateOptions,
  TemplateListFilters,
  TemplateUsageStats,
  TemplatePreviewData,
  TemplateValidationResult,
  TemplateStatus,
  TemplateCategory
} from '@/types/email-blast/email-template.types';
import { 
  templateFormSchema, 
  templateDuplicateSchema,
  templatePreviewSchema,
  validateTemplateVariables,
  validateTemplateContent
} from '@/schemas/email-blast/email-template.schema';
import { createClient } from '@/utils/supabase/Client';
import { Tables } from '@/types/email_system.types';

type DbEmailTemplate = Tables<{ schema: 'email_system' }, 'email_templates'>;
type DbTemplateUsageStats = Tables<{ schema: 'email_system' }, 'template_usage_stats'>;

class EmailTemplatesService {
  private async getSupabase() {
    return createClient();
  }

  private mapDbToEmailTemplate(dbTemplate: DbEmailTemplate, stats?: DbTemplateUsageStats): EmailTemplate {
    return {
      id: dbTemplate.id,
      name: dbTemplate.name,
      description: dbTemplate.description || '',
      category: dbTemplate.category as TemplateCategory,
      status: (dbTemplate.active ? TemplateStatus.ACTIVE : TemplateStatus.DRAFT) as TemplateStatus,
      content: {
        subject: dbTemplate.subject,
        body: dbTemplate.body_text || '',
        bodyHtml: dbTemplate.body_html,
        previewText: ''
      },
      variables: (dbTemplate.variables as any[]) || [],
      thumbnail: dbTemplate.thumbnail_url || '',
      tags: [],
      isSystem: dbTemplate.is_blast_template === false,
      version: dbTemplate.version || 1,
      parentTemplateId: dbTemplate.parent_template_id || undefined,
      createdAt: new Date(dbTemplate.created_at || ''),
      updatedAt: new Date(dbTemplate.updated_at || ''),
      createdBy: 'system',
      updatedBy: 'system',
      usageStats: stats ? {
        templateId: stats.template_id,
        totalUsage: stats.total_usage || 0,
        campaignsUsed: stats.blast_usage || 0,
        successRate: 0,
        avgOpenRate: stats.avg_open_rate || 0,
        avgClickRate: stats.avg_click_rate || 0,
        popularVariables: stats.popular_variables || [],
        lastUsedAt: stats.last_used_at ? new Date(stats.last_used_at) : undefined,
        monthlyUsage: []
      } : undefined
    };
  }

  /**
   * Get all templates with optional filtering
   */
  async getAll(filters?: TemplateListFilters): Promise<EmailTemplate[]> {
    const supabase = await this.getSupabase();
    
    // Build query for blast templates
    let query = supabase
      .schema('email_system')
      .from('email_templates')
      .select(`
        *,
        template_usage_stats(*)
      `)
      .eq('template_type', 'blast');
    
    if (filters) {
      // Filter by category
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      
      // Filter by status
      if (filters.status) {
        const isActive = filters.status === TemplateStatus.ACTIVE;
        query = query.eq('active', isActive);
      }
      
      // Filter by search term
      if (filters.search) {
        query = query.or(`
          name.ilike.%${filters.search}%,
          description.ilike.%${filters.search}%,
          subject.ilike.%${filters.search}%
        `);
      }
      
      // Filter by system/custom
      if (filters.isSystem !== undefined) {
        query = query.eq('is_blast_template', !filters.isSystem);
      }
    }
    
    // Sort by updated date (most recent first)
    query = query.order('updated_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching templates:', error);
      throw new Error(`Failed to fetch templates: ${error.message}`);
    }

    return (data || []).map((item: any) => 
      this.mapDbToEmailTemplate(item, item.template_usage_stats?.[0])
    );
  }

  /**
   * Get template by ID
   */
  async getById(id: string): Promise<EmailTemplate | null> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select(`
        *,
        template_usage_stats(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching template:', error);
      throw new Error(`Failed to fetch template: ${error.message}`);
    }

    return this.mapDbToEmailTemplate(data, data.template_usage_stats?.[0]);
  }

  /**
   * Create new template
   */
  async create(data: TemplateFormData): Promise<EmailTemplate> {
    const supabase = await this.getSupabase();
    
    // Validate data
    const validatedData = templateFormSchema.parse(data);
    
    // Check for duplicate name
    const { data: existingTemplates, error: checkError } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select('id')
      .ilike('name', validatedData.name)
      .limit(1);
    
    if (checkError) {
      console.error('Error checking for duplicate template:', checkError);
      throw new Error(`Failed to check for duplicate template: ${checkError.message}`);
    }
    
    if (existingTemplates && existingTemplates.length > 0) {
      throw new Error('A template with this name already exists');
    }
    
    // Generate thumbnail based on category
    const thumbnail = this.generateThumbnailPath(validatedData.category);
    
    // Insert into database
    const { data: newTemplate, error: insertError } = await supabase
      .schema('email_system')
      .from('email_templates')
      .insert({
        name: validatedData.name,
        description: validatedData.description,
        category: validatedData.category,
        template_type: 'blast',
        is_blast_template: true,
        active: validatedData.status === TemplateStatus.ACTIVE,
        subject: validatedData.subject,
        body_text: validatedData.body,
        body_html: validatedData.bodyHtml,
        variables: validatedData.variables || [],
        thumbnail_url: thumbnail,
        version: 1,
        metadata: validatedData.metadata || {},
        usage_count: 0
      })
      .select()
      .single();
    
    if (insertError) {
      console.error('Error creating template:', insertError);
      throw new Error(`Failed to create template: ${insertError.message}`);
    }
    
    // Create initial usage stats
    await supabase
      .schema('email_system')
      .from('template_usage_stats')
      .insert({
        template_id: newTemplate.id,
        total_usage: 0,
        blast_usage: 0
      });
    
    return this.mapDbToEmailTemplate(newTemplate);
  }

  /**
   * Update existing template
   */
  async update(id: string, data: Partial<TemplateFormData>): Promise<EmailTemplate> {
    const supabase = await this.getSupabase();
    
    // Get existing template
    const existingTemplate = await this.getById(id);
    if (!existingTemplate) {
      throw new Error('Template not found');
    }
    
    // Only allow editing if not system template or user has permissions
    if (existingTemplate.isSystem && data.status !== TemplateStatus.ARCHIVED) {
      throw new Error('System templates cannot be modified');
    }

    // Create partial validation - validate only provided fields
    const validatedData: Partial<typeof data> = {};
    
    // Manually validate each provided field
    if (data.name !== undefined) validatedData.name = data.name;
    if (data.description !== undefined) validatedData.description = data.description;
    if (data.category !== undefined) validatedData.category = data.category;
    if (data.status !== undefined) validatedData.status = data.status;
    if (data.subject !== undefined) validatedData.subject = data.subject;
    if (data.body !== undefined) validatedData.body = data.body;
    if (data.bodyHtml !== undefined) validatedData.bodyHtml = data.bodyHtml;
    if (data.previewText !== undefined) validatedData.previewText = data.previewText;
    if (data.variables !== undefined) validatedData.variables = data.variables;
    if (data.tags !== undefined) validatedData.tags = data.tags;
    if (data.metadata !== undefined) validatedData.metadata = data.metadata;

    // Check for duplicate name (excluding current template)
    if (validatedData.name) {
      const { data: duplicates, error: checkError } = await supabase
        .schema('email_system')
        .from('email_templates')
        .select('id')
        .ilike('name', validatedData.name)
        .neq('id', id)
        .limit(1);
      
      if (checkError) {
        console.error('Error checking for duplicate template:', checkError);
        throw new Error(`Failed to check for duplicate template: ${checkError.message}`);
      }
      
      if (duplicates && duplicates.length > 0) {
        throw new Error('A template with this name already exists');
      }
    }

    // Build update object
    const updateData: any = {
      updated_at: new Date().toISOString(),
      version: (existingTemplate.version || 0) + 1
    };
    
    if (validatedData.name !== undefined) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.category !== undefined) updateData.category = validatedData.category;
    if (validatedData.status !== undefined) updateData.active = validatedData.status === TemplateStatus.ACTIVE;
    if (validatedData.subject !== undefined) updateData.subject = validatedData.subject;
    if (validatedData.body !== undefined) updateData.body_text = validatedData.body;
    if (validatedData.bodyHtml !== undefined) updateData.body_html = validatedData.bodyHtml;
    if (validatedData.variables !== undefined) updateData.variables = validatedData.variables;
    if (validatedData.metadata !== undefined) updateData.metadata = { ...existingTemplate.metadata, ...validatedData.metadata };

    // Update in database
    const { data: updatedTemplate, error: updateError } = await supabase
      .schema('email_system')
      .from('email_templates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (updateError) {
      console.error('Error updating template:', updateError);
      throw new Error(`Failed to update template: ${updateError.message}`);
    }
    
    return this.mapDbToEmailTemplate(updatedTemplate);
  }

  /**
   * Delete template
   */
  async delete(id: string): Promise<void> {
    const supabase = await this.getSupabase();
    
    // Get existing template
    const existingTemplate = await this.getById(id);
    if (!existingTemplate) {
      throw new Error('Template not found');
    }
    
    // Only allow deletion of custom templates or if user has admin permissions
    if (existingTemplate.isSystem) {
      throw new Error('System templates cannot be deleted');
    }
    
    // Check if template is in use
    if (existingTemplate.usageStats && existingTemplate.usageStats.totalUsage > 0) {
      // Don't actually delete, just archive
      const { error } = await supabase
        .schema('email_system')
        .from('email_templates')
        .update({
          active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        console.error('Error archiving template:', error);
        throw new Error(`Failed to archive template: ${error.message}`);
      }
      return;
    }

    // Delete template
    const { error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Error deleting template:', error);
      throw new Error(`Failed to delete template: ${error.message}`);
    }
  }

  /**
   * Duplicate template
   */
  async duplicate(id: string, options: TemplateDuplicateOptions): Promise<EmailTemplate> {
    const supabase = await this.getSupabase();
    
    // Get original template
    const originalTemplate = await this.getById(id);
    if (!originalTemplate) {
      throw new Error('Template not found');
    }

    // Validate options
    const validatedOptions = templateDuplicateSchema.parse(options);

    // Check for duplicate name
    const { data: existingTemplates, error: checkError } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select('id')
      .ilike('name', validatedOptions.name)
      .limit(1);
    
    if (checkError) {
      console.error('Error checking for duplicate template:', checkError);
      throw new Error(`Failed to check for duplicate template: ${checkError.message}`);
    }
    
    if (existingTemplates && existingTemplates.length > 0) {
      throw new Error('A template with this name already exists');
    }

    // Create duplicate
    const { data: duplicatedTemplate, error: insertError } = await supabase
      .schema('email_system')
      .from('email_templates')
      .insert({
        name: validatedOptions.name,
        description: validatedOptions.description || originalTemplate.description,
        category: originalTemplate.category,
        template_type: 'blast',
        is_blast_template: true,
        active: originalTemplate.status === TemplateStatus.ACTIVE,
        subject: originalTemplate.content.subject,
        body_text: originalTemplate.content.body,
        body_html: originalTemplate.content.bodyHtml,
        variables: validatedOptions.preserveVariables ? originalTemplate.variables : [],
        thumbnail_url: originalTemplate.thumbnail,
        version: 1,
        parent_template_id: originalTemplate.id,
        metadata: originalTemplate.metadata || {},
        usage_count: 0
      })
      .select()
      .single();
    
    if (insertError) {
      console.error('Error duplicating template:', insertError);
      throw new Error(`Failed to duplicate template: ${insertError.message}`);
    }
    
    // Create initial usage stats
    await supabase
      .schema('email_system')
      .from('template_usage_stats')
      .insert({
        template_id: duplicatedTemplate.id,
        total_usage: 0,
        blast_usage: 0
      });
    
    return this.mapDbToEmailTemplate(duplicatedTemplate);
  }

  /**
   * Get usage statistics for a template
   */
  async getUsageStats(id: string): Promise<TemplateUsageStats | null> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('template_usage_stats')
      .select('*')
      .eq('template_id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No stats found, return default stats
        return {
          templateId: id,
          totalUsage: 0,
          campaignsUsed: 0,
          successRate: 0,
          avgOpenRate: 0,
          avgClickRate: 0,
          popularVariables: [],
          monthlyUsage: []
        };
      }
      console.error('Error fetching template usage stats:', error);
      throw new Error(`Failed to fetch template usage stats: ${error.message}`);
    }

    return {
      templateId: data.template_id,
      totalUsage: data.total_usage || 0,
      campaignsUsed: data.blast_usage || 0,
      successRate: 0,
      avgOpenRate: data.avg_open_rate || 0,
      avgClickRate: data.avg_click_rate || 0,
      popularVariables: data.popular_variables || [],
      lastUsedAt: data.last_used_at ? new Date(data.last_used_at) : undefined,
      monthlyUsage: []
    };
  }

  /**
   * Preview template with variable data
   */
  async previewWithData(data: TemplatePreviewData): Promise<{
    subject: string;
    bodyHtml: string;
    bodyText: string;
  }> {
    // Validate data
    const validatedData = templatePreviewSchema.parse(data);
    
    const template = await this.getById(validatedData.templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Replace variables in content
    const subject = this.replaceVariables(template.content.subject, validatedData.variableValues);
    const bodyText = this.replaceVariables(template.content.body, validatedData.variableValues);
    const bodyHtml = template.content.bodyHtml 
      ? this.replaceVariables(template.content.bodyHtml, validatedData.variableValues)
      : this.convertTextToHtml(bodyText);

    return {
      subject,
      bodyHtml,
      bodyText
    };
  }

  /**
   * Validate template variables
   */
  async validateVariables(templateId: string, variableValues: Record<string, any>): Promise<TemplateValidationResult> {
    const template = await this.getById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Validate variable values
    const variableValidation = validateTemplateVariables(template.variables, variableValues);
    
    // Validate content consistency
    const contentValidation = validateTemplateContent(
      template.content.subject + ' ' + template.content.body + ' ' + (template.content.bodyHtml || ''),
      template.variables
    );

    return {
      isValid: variableValidation.isValid && contentValidation.isValid,
      errors: variableValidation.errors.map(error => ({
        field: 'variables',
        message: error,
        type: 'error' as const
      })),
      missingVariables: contentValidation.missingVariables,
      unusedVariables: contentValidation.unusedVariables
    };
  }

  /**
   * Generate thumbnail for template
   */
  async generateThumbnail(id: string): Promise<string> {
    const supabase = await this.getSupabase();
    
    const template = await this.getById(id);
    if (!template) {
      throw new Error('Template not found');
    }

    // In real implementation, this would generate an actual thumbnail
    const thumbnailPath = this.generateThumbnailPath(template.category);
    
    // Update template with new thumbnail
    const { error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .update({
        thumbnail_url: thumbnailPath,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);
    
    if (error) {
      console.error('Error updating template thumbnail:', error);
      throw new Error(`Failed to update template thumbnail: ${error.message}`);
    }
    
    return thumbnailPath;
  }

  /**
   * Get templates by category
   */
  async getByCategory(category: TemplateCategory): Promise<EmailTemplate[]> {
    return this.getAll({ category, status: TemplateStatus.ACTIVE });
  }

  /**
   * Get templates by status
   */
  async getByStatus(status: TemplateStatus): Promise<EmailTemplate[]> {
    return this.getAll({ status });
  }

  /**
   * Search templates
   */
  async search(query: string): Promise<EmailTemplate[]> {
    if (!query.trim()) {
      return [];
    }

    return this.getAll({ search: query });
  }

  /**
   * Get popular templates
   */
  async getPopular(limit: number = 10): Promise<EmailTemplate[]> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select(`
        *,
        template_usage_stats(*)
      `)
      .eq('template_type', 'blast')
      .eq('active', true)
      .order('usage_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular templates:', error);
      throw new Error(`Failed to fetch popular templates: ${error.message}`);
    }

    return (data || []).map((item: any) => 
      this.mapDbToEmailTemplate(item, item.template_usage_stats?.[0])
    );
  }

  /**
   * Get recently used templates
   */
  async getRecentlyUsed(limit: number = 5): Promise<EmailTemplate[]> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select(`
        *,
        template_usage_stats(*)
      `)
      .eq('template_type', 'blast')
      .eq('active', true)
      .not('last_used_at', 'is', null)
      .order('last_used_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recently used templates:', error);
      throw new Error(`Failed to fetch recently used templates: ${error.message}`);
    }

    return (data || []).map((item: any) => 
      this.mapDbToEmailTemplate(item, item.template_usage_stats?.[0])
    );
  }

  /**
   * Get category statistics
   */
  async getCategoryStats(): Promise<{ category: TemplateCategory; count: number; activeCount: number }[]> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select('category, active')
      .eq('template_type', 'blast');

    if (error) {
      console.error('Error fetching category stats:', error);
      throw new Error(`Failed to fetch category stats: ${error.message}`);
    }

    // Calculate stats
    const stats = new Map<TemplateCategory, { count: number; activeCount: number }>();
    
    // Initialize all categories
    Object.values(TemplateCategory).forEach(category => {
      stats.set(category, { count: 0, activeCount: 0 });
    });
    
    // Count templates
    (data || []).forEach((template: any) => {
      const category = template.category as TemplateCategory;
      const stat = stats.get(category) || { count: 0, activeCount: 0 };
      stat.count++;
      if (template.active) stat.activeCount++;
      stats.set(category, stat);
    });
    
    return Array.from(stats.entries()).map(([category, stat]) => ({
      category,
      count: stat.count,
      activeCount: stat.activeCount
    }));
  }

  /**
   * Get popular tags
   */
  async getPopularTags(): Promise<string[]> {
    // Since tags are not currently stored in the database,
    // return empty array. This can be implemented later if needed.
    return [];
  }

  // Private helper methods

  /**
   * Send test email for template
   */
  async sendTestEmail(previewData: TemplatePreviewData): Promise<void> {
    const template = await this.getById(previewData.templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Validate preview data
    const validatedData = templatePreviewSchema.parse(previewData);
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (validatedData.recipientEmail && !emailRegex.test(validatedData.recipientEmail)) {
      throw new Error('Invalid recipient email address');
    }

    // Get preview content
    const preview = await this.previewWithData(previewData);
    
    // In a real implementation, this would:
    // 1. Process template variables with provided values
    // 2. Render the email content
    // 3. Send via email service (SendGrid, AWS SES, etc.)
    // 4. Return delivery confirmation
    
    // Test email prepared with subject and body
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    // Replace {{variableName}} with actual values
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      result = result.replace(regex, String(value || ''));
    });
    
    // Replace any remaining variables with empty string or placeholder
    result = result.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
      return `[${varName}]`; // Show missing variables as [variableName]
    });
    
    return result;
  }

  private convertTextToHtml(text: string): string {
    return text
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.*)$/, '<p>$1</p>')
      .replace(/• /g, '<li>')
      .replace(/<li>(.*?)(<br>|<\/p>)/g, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>');
  }

  private generateThumbnailPath(category: TemplateCategory): string {
    const categoryMap: Record<TemplateCategory, string> = {
      [TemplateCategory.NEWSLETTER]: 'newsletter',
      [TemplateCategory.PROMOTIONAL]: 'promotional',
      [TemplateCategory.ANNOUNCEMENT]: 'announcement',
      [TemplateCategory.WELCOME]: 'welcome',
      [TemplateCategory.FOLLOWUP]: 'followup',
      [TemplateCategory.EVENT]: 'event',
      [TemplateCategory.SURVEY]: 'survey',
      [TemplateCategory.NOTIFICATION]: 'notification',
      [TemplateCategory.CUSTOM]: 'custom'
    };
    
    return `/templates/thumbnails/${categoryMap[category]}-${Date.now()}.png`;
  }

  /**
   * Record template usage (called when template is used in a campaign)
   */
  async recordUsage(templateId: string, campaignId: string): Promise<void> {
    const supabase = await this.getSupabase();
    
    // First get current usage count
    const { data: template } = await supabase
      .schema('email_system')
      .from('email_templates')
      .select('usage_count')
      .eq('id', templateId)
      .single();

    // Update template usage count
    const { error: updateError } = await supabase
      .schema('email_system')
      .from('email_templates')
      .update({
        usage_count: (template?.usage_count || 0) + 1,
        last_used_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId);

    if (updateError) {
      console.error('Error updating template usage:', updateError);
      // Don't throw here, as usage tracking is not critical
    }

    // Update or create usage stats
    const { data: existingStats } = await supabase
      .schema('email_system')
      .from('template_usage_stats')
      .select('*')
      .eq('template_id', templateId)
      .single();

    if (existingStats) {
      await supabase
        .schema('email_system')
        .from('template_usage_stats')
        .update({
          total_usage: (existingStats.total_usage || 0) + 1,
          blast_usage: (existingStats.blast_usage || 0) + 1,
          last_used_at: new Date().toISOString(),
          last_calculated_at: new Date().toISOString()
        })
        .eq('template_id', templateId);
    } else {
      await supabase
        .schema('email_system')
        .from('template_usage_stats')
        .insert({
          template_id: templateId,
          total_usage: 1,
          blast_usage: 1,
          last_used_at: new Date().toISOString()
        });
    }
  }

  /**
   * Get blast templates (templates designed for email campaigns)
   */
  async getBlastTemplates(filters?: TemplateListFilters): Promise<EmailTemplate[]> {
    const supabase = await this.getSupabase();
    
    let query = supabase
      .schema('email_system')
      .from('email_templates')
      .select(`
        *,
        template_usage_stats(*)
      `)
      .or('template_type.eq.blast,is_blast_template.eq.true')
      .eq('active', true);

    // Apply filters
    if (filters?.category) {
      query = query.eq('category', filters.category);
    }

    if (filters?.search) {
      query = query.or(`
        name.ilike.%${filters.search}%,
        description.ilike.%${filters.search}%,
        subject.ilike.%${filters.search}%
      `);
    }

    const { data, error } = await query.order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching blast templates:', error);
      throw new Error(`Failed to fetch blast templates: ${error.message}`);
    }

    return (data || []).map((item: any) => 
      this.mapDbToEmailTemplate(item, item.template_usage_stats?.[0])
    );
  }

  /**
   * Get shared templates (templates from the shared view)
   */
  async getSharedTemplates(filters?: TemplateListFilters): Promise<EmailTemplate[]> {
    const supabase = await this.getSupabase();
    
    let query = supabase
      .schema('email_system')
      .from('email_templates')
      .select('*');

    // Apply filters
    if (filters?.category) {
      query = query.eq('category', filters.category);
    }

    if (filters?.search) {
      query = query.or(`
        name.ilike.%${filters.search}%,
        description.ilike.%${filters.search}%,
        subject.ilike.%${filters.search}%
      `);
    }

    if (filters?.status) {
      const isActive = filters.status === TemplateStatus.ACTIVE;
      query = query.eq('active', isActive);
    }

    const { data, error } = await query.order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching shared templates:', error);
      throw new Error(`Failed to fetch shared templates: ${error.message}`);
    }

    return (data || []).map((item: any) => ({
      id: item.id,
      name: item.name,
      description: item.description || '',
      category: item.category as TemplateCategory,
      status: item.active ? TemplateStatus.ACTIVE : TemplateStatus.DRAFT,
      content: {
        subject: item.subject,
        body: item.body_text || '',
        bodyHtml: item.body_html,
        previewText: ''
      },
      variables: (item.variables as any[]) || [],
      thumbnail: item.thumbnail_url || '',
      tags: [],
      isSystem: true,
      version: item.version || 1,
      parentTemplateId: item.parent_template_id || undefined,
      createdAt: new Date(item.created_at || ''),
      updatedAt: new Date(item.updated_at || ''),
      createdBy: 'system',
      updatedBy: 'system',
      usageStats: {
        templateId: item.id,
        totalUsage: item.total_usage || 0,
        campaignsUsed: item.blast_usage || 0,
        successRate: 0,
        avgOpenRate: item.avg_open_rate || 0,
        avgClickRate: item.avg_click_rate || 0,
        popularVariables: [],
        lastUsedAt: item.last_used_at ? new Date(item.last_used_at) : undefined,
        monthlyUsage: []
      }
    }));
  }

  /**
   * Convert a regular template to a shared template
   */
  async convertToShared(templateId: string): Promise<EmailTemplate> {
    const supabase = await this.getSupabase();
    
    // Get the template
    const template = await this.getById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Update the template to mark it as a blast template
    const { data, error } = await supabase
      .schema('email_system')
      .from('email_templates')
      .update({
        is_blast_template: true,
        template_type: 'blast',
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      console.error('Error converting template to shared:', error);
      throw new Error('Failed to convert template to shared');
    }

    return this.mapDbToEmailTemplate(data);
  }
}

// Export singleton instance
export const emailTemplatesService = new EmailTemplatesService();