import { createClient } from "@/utils/supabase/server";
import { format, subDays, startOfDay, endOfDay, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";
import { Tables } from "@/types/email_system.types";

type CampaignStatistics = Tables<{ schema: 'email_system' }, 'campaign_statistics'>;
type EmailCampaign = Tables<{ schema: 'email_system' }, 'email_campaigns'>;
type CampaignRecipient = Tables<{ schema: 'email_system' }, 'campaign_recipients'>;
type EmailOpen = Tables<{ schema: 'email_system' }, 'email_opens'>;
type EmailClick = Tables<{ schema: 'email_system' }, 'email_clicks'>;

export interface CampaignMetrics {
  total_campaigns: number;
  total_sent: number;
  total_delivered: number;
  total_opened: number;
  total_clicked: number;
  avg_open_rate: number;
  avg_click_rate: number;
  avg_bounce_rate: number;
}

export interface PerformanceDataPoint {
  date: string;
  sent: number;
  opened: number;
  clicked: number;
}

export interface EngagementData {
  name: string;
  value: number;
  color: string;
}

export interface TopCampaign {
  id: string;
  name: string;
  sent: number;
  openRate: number;
  clickRate: number;
}

export interface DeviceStats {
  device: string;
  percentage: number;
}

export interface DateRange {
  start: Date;
  end: Date;
}

function getDateRange(rangeType: string): DateRange {
  const now = new Date();
  
  switch (rangeType) {
    case "last7days":
      return {
        start: startOfDay(subDays(now, 7)),
        end: endOfDay(now)
      };
    case "last30days":
      return {
        start: startOfDay(subDays(now, 30)),
        end: endOfDay(now)
      };
    case "last90days":
      return {
        start: startOfDay(subDays(now, 90)),
        end: endOfDay(now)
      };
    case "thisMonth":
      return {
        start: startOfMonth(now),
        end: endOfMonth(now)
      };
    case "lastMonth":
      const lastMonth = subDays(now, now.getDate());
      return {
        start: startOfMonth(lastMonth),
        end: endOfMonth(lastMonth)
      };
    case "thisYear":
      return {
        start: startOfYear(now),
        end: endOfYear(now)
      };
    default:
      return {
        start: startOfDay(subDays(now, 30)),
        end: endOfDay(now)
      };
  }
}

export async function getCampaignMetrics(dateRange: string): Promise<CampaignMetrics> {
  const supabase = await createClient();
  const { start, end } = getDateRange(dateRange);
  
  try {
    // Get campaigns within date range
    const { data: campaigns, error: campaignsError } = await supabase
      .schema('email_system')
      .from('email_campaigns')
      .select('*, campaign_statistics(*)')
      .gte('created_at', start.toISOString())
      .lte('created_at', end.toISOString());

    if (campaignsError) throw campaignsError;

    if (!campaigns || campaigns.length === 0) {
      return {
        total_campaigns: 0,
        total_sent: 0,
        total_delivered: 0,
        total_opened: 0,
        total_clicked: 0,
        avg_open_rate: 0,
        avg_click_rate: 0,
        avg_bounce_rate: 0,
      };
    }

    // Calculate metrics
    let totalSent = 0;
    let totalDelivered = 0;
    let totalOpened = 0;
    let totalClicked = 0;
    let totalOpenRates = 0;
    let totalClickRates = 0;
    let totalBounceRates = 0;
    let campaignsWithStats = 0;

    campaigns.forEach((campaign: any) => {
      const stats = campaign.campaign_statistics;
      if (stats) {
        totalSent += stats.sent_count || 0;
        totalDelivered += stats.delivered_count || 0;
        totalOpened += stats.unique_opens || 0;
        totalClicked += stats.unique_clicks || 0;
        
        if (stats.open_rate !== null) {
          totalOpenRates += stats.open_rate;
          campaignsWithStats++;
        }
        if (stats.click_rate !== null) {
          totalClickRates += stats.click_rate;
        }
        if (stats.bounce_rate !== null) {
          totalBounceRates += stats.bounce_rate;
        }
      }
    });

    return {
      total_campaigns: campaigns.length,
      total_sent: totalSent,
      total_delivered: totalDelivered,
      total_opened: totalOpened,
      total_clicked: totalClicked,
      avg_open_rate: campaignsWithStats > 0 ? totalOpenRates / campaignsWithStats : 0,
      avg_click_rate: campaignsWithStats > 0 ? totalClickRates / campaignsWithStats : 0,
      avg_bounce_rate: campaignsWithStats > 0 ? totalBounceRates / campaignsWithStats : 0,
    };
  } catch (error) {
    console.error('Error fetching campaign metrics:', error);
    return {
      total_campaigns: 0,
      total_sent: 0,
      total_delivered: 0,
      total_opened: 0,
      total_clicked: 0,
      avg_open_rate: 0,
      avg_click_rate: 0,
      avg_bounce_rate: 0,
    };
  }
}

export async function getPerformanceData(dateRange: string): Promise<PerformanceDataPoint[]> {
  const supabase = await createClient();
  const { start, end } = getDateRange(dateRange);
  
  try {
    // Get campaign recipients data grouped by date
    const { data: recipients, error } = await supabase
      .schema('email_system')
      .from('campaign_recipients')
      .select('created_at, status, open_count, click_count')
      .gte('created_at', start.toISOString())
      .lte('created_at', end.toISOString())
      .order('created_at', { ascending: true });

    if (error) throw error;

    if (!recipients || recipients.length === 0) {
      return [];
    }

    // Group by date
    const dataByDate = new Map<string, { sent: number; opened: number; clicked: number }>();
    
    recipients.forEach((recipient: any) => {
      const date = format(new Date(recipient.created_at || ''), 'yyyy-MM-dd');
      const existing = dataByDate.get(date) || { sent: 0, opened: 0, clicked: 0 };
      
      existing.sent += 1;
      if (recipient.open_count && recipient.open_count > 0) {
        existing.opened += 1;
      }
      if (recipient.click_count && recipient.click_count > 0) {
        existing.clicked += 1;
      }
      
      dataByDate.set(date, existing);
    });

    // Convert to array
    return Array.from(dataByDate.entries()).map(([date, data]) => ({
      date,
      sent: data.sent,
      opened: data.opened,
      clicked: data.clicked
    }));
  } catch (error) {
    console.error('Error fetching performance data:', error);
    return [];
  }
}

export async function getEngagementData(dateRange: string): Promise<EngagementData[]> {
  const metrics = await getCampaignMetrics(dateRange);
  
  if (metrics.total_sent === 0) {
    return [
      { name: "Opened", value: 0, color: "#10b981" },
      { name: "Clicked", value: 0, color: "#3b82f6" },
      { name: "Unopened", value: 100, color: "#e5e7eb" },
    ];
  }

  const openedPercentage = Math.round((metrics.total_opened / metrics.total_sent) * 100);
  const clickedPercentage = Math.round((metrics.total_clicked / metrics.total_sent) * 100);
  const unopenedPercentage = 100 - openedPercentage;

  return [
    { name: "Opened", value: openedPercentage, color: "#10b981" },
    { name: "Clicked", value: clickedPercentage, color: "#3b82f6" },
    { name: "Unopened", value: unopenedPercentage, color: "#e5e7eb" },
  ];
}

export async function getTopCampaigns(dateRange: string, limit: number = 5): Promise<TopCampaign[]> {
  const supabase = await createClient();
  const { start, end } = getDateRange(dateRange);
  
  try {
    const { data: campaigns, error } = await supabase
      .schema('email_system')
      .from('email_campaigns')
      .select(`
        id,
        name,
        campaign_statistics(
          sent_count,
          open_rate,
          click_rate
        )
      `)
      .gte('created_at', start.toISOString())
      .lte('created_at', end.toISOString())
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    if (!campaigns || campaigns.length === 0) {
      return [];
    }

    return campaigns
      .filter((campaign: any) => campaign.campaign_statistics)
      .map((campaign: any) => ({
        id: campaign.id,
        name: campaign.name,
        sent: campaign.campaign_statistics?.sent_count || 0,
        openRate: campaign.campaign_statistics?.open_rate || 0,
        clickRate: campaign.campaign_statistics?.click_rate || 0,
      }))
      .sort((a: TopCampaign, b: TopCampaign) => b.openRate - a.openRate); // Sort by open rate
  } catch (error) {
    console.error('Error fetching top campaigns:', error);
    return [];
  }
}

export async function getDeviceStats(dateRange: string): Promise<DeviceStats[]> {
  const supabase = await createClient();
  const { start, end } = getDateRange(dateRange);
  
  try {
    // Get email opens with device type
    const { data: opens, error } = await supabase
      .schema('email_system')
      .from('email_opens')
      .select('device_type')
      .gte('opened_at', start.toISOString())
      .lte('opened_at', end.toISOString())
      .not('device_type', 'is', null);

    if (error) throw error;

    if (!opens || opens.length === 0) {
      return [
        { device: "Desktop", percentage: 45 },
        { device: "Mobile", percentage: 38 },
        { device: "Tablet", percentage: 17 },
      ];
    }

    // Count by device type
    const deviceCounts = new Map<string, number>();
    opens.forEach((open: any) => {
      const device = open.device_type || 'Unknown';
      deviceCounts.set(device, (deviceCounts.get(device) || 0) + 1);
    });

    // Calculate percentages
    const total = opens.length;
    const stats = Array.from(deviceCounts.entries()).map(([device, count]) => ({
      device: device.charAt(0).toUpperCase() + device.slice(1),
      percentage: Math.round((count / total) * 100)
    }));

    return stats.sort((a, b) => b.percentage - a.percentage);
  } catch (error) {
    console.error('Error fetching device stats:', error);
    return [
      { device: "Desktop", percentage: 45 },
      { device: "Mobile", percentage: 38 },
      { device: "Tablet", percentage: 17 },
    ];
  }
}

export async function getEngagementMetrics(dateRange: string) {
  const metrics = await getCampaignMetrics(dateRange);
  
  const deliveryRate = metrics.total_sent > 0 
    ? (metrics.total_delivered / metrics.total_sent) * 100 
    : 0;

  // Calculate unsubscribe rate (placeholder until we have unsubscribe data)
  const unsubscribeRate = 0.8; // Temporary placeholder value

  return {
    deliveryRate: Math.round(deliveryRate * 10) / 10,
    openRate: Math.round(metrics.avg_open_rate * 10) / 10,
    clickRate: Math.round(metrics.avg_click_rate * 10) / 10,
    unsubscribeRate,
  };
}