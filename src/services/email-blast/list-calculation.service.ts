import { createClient } from '@/utils/supabase/Client';
import { RecipientFilters } from '@/types/email-blast/recipient-list.types';
import { recipientFilterService } from './recipient-filter.service';
import * as XLSX from 'xlsx';

interface CalculatedRecipient {
  id: string;
  email: string;
  name: string;
  memberData?: Record<string, any>;
}

class ListCalculationService {
  private async getSupabase() {
    return createClient();
  }

  /**
   * Calculate recipients for a list using the database RPC
   */
  async calculateRecipientList(listId: string): Promise<number> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .rpc('calculate_recipient_list', {
        p_list_id: listId
      });

    if (error) {
      console.error('Error calculating recipient list:', error);
      throw new Error('Failed to calculate recipient list');
    }

    return data || 0;
  }

  /**
   * Preview recipients based on filters without saving
   */
  async previewRecipients(
    filters: RecipientFilters, 
    options?: { limit?: number; offset?: number }
  ): Promise<{ recipients: CalculatedRecipient[]; total: number }> {
    const supabase = await this.getSupabase();
    const limit = options?.limit || 50;
    const offset = options?.offset || 0;

    // Build query using filter service
    const filterQuery = recipientFilterService.buildSupabaseQuery(filters);
    
    // Apply pagination
    let countQuery = supabase.from('members').select('*', { count: 'exact', head: true });
    let dataQuery = supabase.from('members').select('*').range(offset, offset + limit - 1);

    // Apply filters to both queries
    if (filterQuery) {
      // This is a simplified example - in practice, you'd parse and apply the filter query
      // The actual implementation would depend on your filter structure
      countQuery = this.applyFilterToQuery(countQuery, filters);
      dataQuery = this.applyFilterToQuery(dataQuery, filters);
    }

    // Get total count
    const { count } = await countQuery;
    
    // Get paginated data
    const { data: recipients, error } = await dataQuery;

    if (error) {
      console.error('Error previewing recipients:', error);
      throw new Error('Failed to preview recipients');
    }

    // Check for suppressions
    const emails = recipients?.map(r => r.email) || [];
    const suppressedEmails = await this.getSuppressions(emails);

    // Filter out suppressed emails and format
    const filteredRecipients = (recipients || [])
      .filter(r => !suppressedEmails.has(r.email))
      .map(r => ({
        id: r.id,
        email: r.email,
        name: r.name || `${r.first_name || ''} ${r.last_name || ''}`.trim(),
        memberData: {
          membershipType: r.membership_type,
          membershipStatus: r.membership_status,
          expiryDate: r.expiry_date,
          location: r.location,
          ...r
        }
      }));

    return {
      recipients: filteredRecipients,
      total: count || 0
    };
  }

  /**
   * Import recipients from CSV/Excel file
   */
  async importRecipients(
    file: File,
    options?: {
      hasHeaders?: boolean;
      emailColumn?: string;
      nameColumn?: string;
      mapping?: Record<string, string>;
    }
  ): Promise<{ 
    success: CalculatedRecipient[]; 
    failed: Array<{ row: number; email: string; error: string }>;
    duplicates: string[];
  }> {
    const hasHeaders = options?.hasHeaders ?? true;
    const emailColumn = options?.emailColumn || 'email';
    const nameColumn = options?.nameColumn || 'name';

    try {
      // Read file content
      const content = await this.readFileContent(file);
      let data: any[][] = [];

      if (file.name.endsWith('.csv')) {
        // Parse CSV
        data = this.parseCSV(content);
      } else if (file.name.match(/\.(xlsx?|ods)$/)) {
        // Parse Excel
        const workbook = XLSX.read(content, { type: 'binary' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        data = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
      } else {
        throw new Error('Unsupported file format. Please use CSV or Excel files.');
      }

      if (data.length === 0) {
        throw new Error('File is empty');
      }

      // Process headers
      let headers: string[] = [];
      let startRow = 0;
      
      if (hasHeaders) {
        headers = data[0].map(h => String(h).toLowerCase().trim());
        startRow = 1;
      } else {
        // Generate default headers
        headers = data[0].map((_, i) => `column${i + 1}`);
      }

      // Find email and name columns
      const emailIndex = headers.indexOf(emailColumn.toLowerCase());
      const nameIndex = headers.indexOf(nameColumn.toLowerCase());

      if (emailIndex === -1) {
        throw new Error(`Email column "${emailColumn}" not found`);
      }

      // Process rows
      const success: CalculatedRecipient[] = [];
      const failed: Array<{ row: number; email: string; error: string }> = [];
      const seenEmails = new Set<string>();
      const duplicates: string[] = [];

      for (let i = startRow; i < data.length; i++) {
        const row = data[i];
        const email = String(row[emailIndex] || '').trim().toLowerCase();
        const name = nameIndex >= 0 ? String(row[nameIndex] || '').trim() : '';

        // Validate email
        if (!email) {
          failed.push({ row: i + 1, email: '', error: 'Email is required' });
          continue;
        }

        if (!this.isValidEmail(email)) {
          failed.push({ row: i + 1, email, error: 'Invalid email format' });
          continue;
        }

        // Check for duplicates
        if (seenEmails.has(email)) {
          duplicates.push(email);
          continue;
        }

        seenEmails.add(email);

        // Build member data from mapping
        const memberData: Record<string, any> = {};
        if (options?.mapping) {
          Object.entries(options.mapping).forEach(([targetField, sourceColumn]) => {
            const sourceIndex = headers.indexOf(sourceColumn.toLowerCase());
            if (sourceIndex >= 0) {
              memberData[targetField] = row[sourceIndex];
            }
          });
        }

        success.push({
          id: `import-${Date.now()}-${i}`,
          email,
          name,
          memberData
        });
      }

      // Check for existing emails in database
      if (success.length > 0) {
        const existingEmails = await this.checkExistingEmails(success.map(r => r.email));
        existingEmails.forEach(email => {
          duplicates.push(email);
        });
        
        // Remove existing emails from success list
        const finalSuccess = success.filter(r => !existingEmails.has(r.email));
        return { success: finalSuccess, failed, duplicates };
      }

      return { success, failed, duplicates };
    } catch (error: any) {
      throw new Error(`Import failed: ${error.message}`);
    }
  }

  /**
   * Export recipients to CSV format
   */
  async exportRecipients(
    listId: string,
    format: 'csv' | 'excel' = 'csv',
    options?: {
      includeFields?: string[];
      excludeFields?: string[];
    }
  ): Promise<Blob> {
    const supabase = await this.getSupabase();
    
    // Fetch all recipients
    const allRecipients: any[] = [];
    let offset = 0;
    const batchSize = 1000;

    // Get list details
    const { data: list } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('*')
      .eq('id', listId)
      .single();

    if (!list) {
      throw new Error('List not found');
    }

    // Fetch recipients based on list type
    if (list.list_type === 'static') {
      // Fetch from recipient_list_members
      while (true) {
        const { data: batch } = await supabase
          .schema('email_system')
          .from('recipient_list_members')
          .select('*')
          .eq('list_id', listId)
          .eq('is_active', true)
          .range(offset, offset + batchSize - 1);

        if (!batch || batch.length === 0) break;
        allRecipients.push(...batch);
        offset += batchSize;
      }
    } else {
      // For dynamic lists, use filters to fetch from members table
      // This is simplified - in production, you'd apply the actual filters
      while (true) {
        const { data: batch } = await supabase
          .from('members')
          .select('*')
          .range(offset, offset + batchSize - 1);

        if (!batch || batch.length === 0) break;
        allRecipients.push(...batch);
        offset += batchSize;
      }
    }

    // Format data for export
    const exportData = allRecipients.map(r => {
      const row: Record<string, any> = {
        email: r.email,
        name: r.name || '',
      };

      // Add member data fields
      if (r.member_data) {
        Object.entries(r.member_data as Record<string, any>).forEach(([key, value]) => {
          if (!options?.excludeFields?.includes(key)) {
            row[key] = value;
          }
        });
      }

      // Filter to include only specified fields
      if (options?.includeFields) {
        const filteredRow: Record<string, any> = {};
        options.includeFields.forEach(field => {
          if (row[field] !== undefined) {
            filteredRow[field] = row[field];
          }
        });
        return filteredRow;
      }

      return row;
    });

    if (format === 'csv') {
      return this.exportToCSV(exportData);
    } else {
      return this.exportToExcel(exportData, list.name);
    }
  }

  // Helper methods
  private applyFilterToQuery(query: any, filters: RecipientFilters): any {
    // This is a simplified implementation
    // In production, you'd want to use the filter-query-builder
    
    if (filters.membershipStatus?.length) {
      query = query.in('membership_status', filters.membershipStatus);
    }

    if (filters.membershipTypes?.length) {
      query = query.in('membership_type', filters.membershipTypes);
    }

    if (filters.locations?.length) {
      query = query.in('location', filters.locations);
    }

    if (filters.expiryDateRange?.from) {
      query = query.gte('expiry_date', filters.expiryDateRange.from.toISOString());
    }

    if (filters.expiryDateRange?.to) {
      query = query.lte('expiry_date', filters.expiryDateRange.to.toISOString());
    }

    return query;
  }

  private async getSuppressions(emails: string[]): Promise<Set<string>> {
    if (emails.length === 0) return new Set();

    const supabase = await this.getSupabase();
    const { data } = await supabase
      .schema('email_system')
      .from('email_suppressions')
      .select('email_address')
      .in('email_address', emails)
      .eq('is_active', true);

    return new Set(data?.map(s => s.email_address) || []);
  }

  private async checkExistingEmails(emails: string[]): Promise<Set<string>> {
    if (emails.length === 0) return new Set();

    const supabase = await this.getSupabase();
    const { data } = await supabase
      .from('members')
      .select('email')
      .in('email', emails);

    return new Set(data?.map(m => m.email) || []);
  }

  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      
      if (file.name.match(/\.(xlsx?|ods)$/)) {
        reader.readAsBinaryString(file);
      } else {
        reader.readAsText(file);
      }
    });
  }

  private parseCSV(content: string): string[][] {
    const lines = content.split(/\r?\n/);
    return lines.map(line => {
      const values: string[] = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }

      if (current) {
        values.push(current.trim());
      }

      return values;
    }).filter(row => row.length > 0);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private exportToCSV(data: Record<string, any>[]): Blob {
    if (data.length === 0) {
      return new Blob(['No data to export'], { type: 'text/csv' });
    }

    // Get headers from first row
    const headers = Object.keys(data[0]);
    
    // Build CSV content
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape values containing commas or quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value ?? '';
        }).join(',')
      )
    ].join('\n');

    return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  }

  private exportToExcel(data: Record<string, any>[], sheetName: string): Blob {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName.slice(0, 31)); // Excel sheet names max 31 chars
    
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }
}

// Export singleton instance
export const listCalculationService = new ListCalculationService();