'use server';

import { createClient } from '@/utils/supabase/server';
import { Tables } from '@/types/email_system.types';

type CampaignStatistics = Tables<{ schema: 'email_system' }, 'campaign_statistics'>;
type CampaignRecipient = Tables<{ schema: 'email_system' }, 'campaign_recipients'>;

export interface CampaignStats {
  // Basic counts
  totalRecipients: number;
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  failed: number;
  unsubscribed: number;
  
  // Rates
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
  unsubscribeRate: number;
  
  // Additional metrics
  uniqueOpens: number;
  uniqueClicks: number;
  totalOpens: number;
  totalClicks: number;
  hardBounces: number;
  softBounces: number;
  spamReports: number;
  
  // Time-based metrics
  avgOpenTimeHours: number | null;
  avgClickTimeHours: number | null;
  
  // Status
  lastUpdated: string;
}

export interface TimeSeriesData {
  timestamp: string;
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  failed: number;
}

export interface RecipientEngagement {
  email: string;
  name: string | null;
  status: string;
  openCount: number;
  clickCount: number;
  lastOpened: string | null;
  lastClicked: string | null;
}

/**
 * Get comprehensive campaign statistics
 */
export async function getCampaignStatistics(campaignId: string): Promise<CampaignStats> {
  const supabase = await createClient();
  
  // Update statistics using RPC
  await supabase
    .schema('email_system')
    .rpc('update_campaign_statistics', {
      p_campaign_id: campaignId
    });
  
  // Fetch campaign data
  const { data: campaign, error: campaignError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('id', campaignId)
    .single();

  if (campaignError) {
    throw new Error('Failed to fetch campaign data');
  }

  // Fetch detailed statistics
  const { data: stats, error: statsError } = await supabase
    .schema('email_system')
    .from('campaign_statistics')
    .select('*')
    .eq('campaign_id', campaignId)
    .single();

  if (statsError && statsError.code !== 'PGRST116') {
    throw new Error('Failed to fetch campaign statistics');
  }

  return {
    totalRecipients: campaign.total_recipients || 0,
    sent: campaign.sent_count || 0,
    delivered: stats?.delivered_count || campaign.delivered_count || 0,
    opened: stats?.unique_opens || campaign.opened_count || 0,
    clicked: stats?.unique_clicks || campaign.clicked_count || 0,
    bounced: stats?.bounce_count || campaign.bounced_count || 0,
    failed: campaign.failed_count || 0,
    unsubscribed: stats?.unsubscribe_count || campaign.unsubscribed_count || 0,
    
    deliveryRate: stats?.delivery_rate || 0,
    openRate: stats?.open_rate || 0,
    clickRate: stats?.click_rate || 0,
    bounceRate: stats?.bounce_rate || 0,
    unsubscribeRate: stats?.unsubscribe_rate || 0,
    
    uniqueOpens: stats?.unique_opens || 0,
    uniqueClicks: stats?.unique_clicks || 0,
    totalOpens: stats?.total_opens || 0,
    totalClicks: stats?.total_clicks || 0,
    hardBounces: stats?.hard_bounce_count || 0,
    softBounces: stats?.soft_bounce_count || 0,
    spamReports: stats?.spam_report_count || 0,
    
    avgOpenTimeHours: stats?.avg_open_time_hours,
    avgClickTimeHours: stats?.avg_click_time_hours,
    
    lastUpdated: stats?.last_calculated_at || new Date().toISOString()
  };
}

/**
 * Get campaign statistics over time for charts
 */
export async function getCampaignTimeSeriesData(
  campaignId: string,
  interval: 'hour' | 'day' = 'hour'
): Promise<TimeSeriesData[]> {
  const supabase = await createClient();
  
  // Fetch campaign recipients and group by time interval
  const { data: recipients, error } = await supabase
    .schema('email_system')
    .from('campaign_recipients')
    .select('created_at, sent_at, delivered_at, opened_at, clicked_at, bounced_at, failed_at, status')
    .eq('campaign_id', campaignId)
    .order('created_at', { ascending: true });

  if (error) {
    throw new Error('Failed to fetch recipient data');
  }

  // Process data into time series
  const timeSeriesMap = new Map<string, TimeSeriesData>();
  
  recipients?.forEach(recipient => {
    // Determine the time bucket based on interval
    const getTimeBucket = (timestamp: string | null) => {
      if (!timestamp) return null;
      const date = new Date(timestamp);
      if (interval === 'hour') {
        date.setMinutes(0, 0, 0);
      } else {
        date.setHours(0, 0, 0, 0);
      }
      return date.toISOString();
    };

    // Process each event type
    const events = [
      { time: recipient.sent_at, type: 'sent' },
      { time: recipient.delivered_at, type: 'delivered' },
      { time: recipient.opened_at, type: 'opened' },
      { time: recipient.clicked_at, type: 'clicked' },
      { time: recipient.bounced_at, type: 'bounced' },
      { time: recipient.failed_at, type: 'failed' }
    ];

    events.forEach(event => {
      if (event.time) {
        const bucket = getTimeBucket(event.time);
        if (bucket) {
          if (!timeSeriesMap.has(bucket)) {
            timeSeriesMap.set(bucket, {
              timestamp: bucket,
              sent: 0,
              delivered: 0,
              opened: 0,
              clicked: 0,
              bounced: 0,
              failed: 0
            });
          }
          const data = timeSeriesMap.get(bucket)!;
          data[event.type as keyof TimeSeriesData]++;
        }
      }
    });
  });

  // Convert map to sorted array
  return Array.from(timeSeriesMap.values()).sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
}

/**
 * Get top engaged recipients
 */
export async function getTopEngagedRecipients(
  campaignId: string,
  limit: number = 10
): Promise<RecipientEngagement[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .schema('email_system')
    .from('campaign_recipients')
    .select('email, name, status, open_count, click_count, first_opened_at, first_clicked_at')
    .eq('campaign_id', campaignId)
    .or('open_count.gt.0,click_count.gt.0')
    .order('click_count', { ascending: false })
    .order('open_count', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error('Failed to fetch engaged recipients');
  }

  return data?.map(r => ({
    email: r.email,
    name: r.name,
    status: r.status,
    openCount: r.open_count || 0,
    clickCount: r.click_count || 0,
    lastOpened: r.first_opened_at,
    lastClicked: r.first_clicked_at
  })) || [];
}

/**
 * Get click statistics by URL
 */
export async function getClickStatsByUrl(campaignId: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_clicks')
    .select('url')
    .eq('campaign_id', campaignId)
    .eq('is_blast_click', true);

  if (error) {
    throw new Error('Failed to fetch click data');
  }

  // Count clicks by URL
  const urlCounts = new Map<string, number>();
  data?.forEach(click => {
    const count = urlCounts.get(click.url) || 0;
    urlCounts.set(click.url, count + 1);
  });

  // Convert to sorted array
  return Array.from(urlCounts.entries())
    .map(([url, count]) => ({ url, count }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Get device and client statistics
 */
export async function getDeviceStats(campaignId: string) {
  const supabase = await createClient();
  
  // Fetch opens with device info
  const { data: opens, error: opensError } = await supabase
    .schema('email_system')
    .from('email_opens')
    .select('device_type, user_agent')
    .eq('campaign_id', campaignId)
    .eq('is_blast_open', true);

  if (opensError) {
    throw new Error('Failed to fetch device data');
  }

  // Process device types
  const deviceCounts = new Map<string, number>();
  const clientCounts = new Map<string, number>();

  opens?.forEach(open => {
    // Count device types
    const deviceType = open.device_type || 'Unknown';
    deviceCounts.set(deviceType, (deviceCounts.get(deviceType) || 0) + 1);

    // Parse user agent for email client
    const userAgent = open.user_agent || '';
    let client = 'Unknown';
    
    if (userAgent.includes('Gmail')) client = 'Gmail';
    else if (userAgent.includes('Outlook')) client = 'Outlook';
    else if (userAgent.includes('Apple Mail')) client = 'Apple Mail';
    else if (userAgent.includes('Yahoo')) client = 'Yahoo Mail';
    else if (userAgent.includes('Thunderbird')) client = 'Thunderbird';
    
    clientCounts.set(client, (clientCounts.get(client) || 0) + 1);
  });

  return {
    devices: Array.from(deviceCounts.entries())
      .map(([device, count]) => ({ device, count }))
      .sort((a, b) => b.count - a.count),
    clients: Array.from(clientCounts.entries())
      .map(([client, count]) => ({ client, count }))
      .sort((a, b) => b.count - a.count)
  };
}

/**
 * Subscribe to real-time campaign statistics updates
 * Note: This should be called from a client component
 */
export async function subscribeToCampaignStats(
  campaignId: string,
  onUpdate: (stats: CampaignStatistics) => void
) {
  const supabase = await createClient();
  
  const channel = supabase
    .channel(`campaign-stats-${campaignId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'email_system',
        table: 'campaign_statistics',
        filter: `campaign_id=eq.${campaignId}`
      },
      (payload: any) => {
        if (payload.new && typeof payload.new === 'object' && 'campaign_id' in payload.new) {
          onUpdate(payload.new as CampaignStatistics);
        }
      }
    )
    .subscribe();

  return () => {
    supabase.removeChannel(channel);
  };
}

/**
 * Get bounce and complaint details
 */
export async function getBounceAndComplaintDetails(campaignId: string) {
  const supabase = await createClient();
  
  const { data: recipients, error } = await supabase
    .schema('email_system')
    .from('campaign_recipients')
    .select('email, bounce_type, error_message, bounced_at')
    .eq('campaign_id', campaignId)
    .not('bounce_type', 'is', null)
    .order('bounced_at', { ascending: false });

  if (error) {
    throw new Error('Failed to fetch bounce data');
  }

  // Group by bounce type
  const bouncesByType = new Map<string, typeof recipients>();
  recipients?.forEach(recipient => {
    const type = recipient.bounce_type || 'Unknown';
    if (!bouncesByType.has(type)) {
      bouncesByType.set(type, []);
    }
    bouncesByType.get(type)!.push(recipient);
  });

  return {
    total: recipients?.length || 0,
    byType: Array.from(bouncesByType.entries()).map(([type, items]) => ({
      type,
      count: items.length,
      recipients: items.slice(0, 5) // Top 5 for each type
    }))
  };
}

/**
 * Export campaign statistics report
 */
export async function exportCampaignStatsReport(campaignId: string) {
  const stats = await getCampaignStatistics(campaignId);
  const timeSeriesData = await getCampaignTimeSeriesData(campaignId, 'day');
  const clickStats = await getClickStatsByUrl(campaignId);
  const deviceStats = await getDeviceStats(campaignId);
  
  // Format report data
  const report = {
    campaignId,
    generatedAt: new Date().toISOString(),
    summary: stats,
    performanceOverTime: timeSeriesData,
    topClickedLinks: clickStats.slice(0, 10),
    deviceBreakdown: deviceStats.devices,
    emailClients: deviceStats.clients
  };

  return {
    data: report,
    filename: `campaign-${campaignId}-report-${new Date().toISOString().split('T')[0]}.json`
  };
}