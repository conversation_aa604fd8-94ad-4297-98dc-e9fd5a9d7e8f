'use server';

import { createClient } from '@/utils/supabase/server';
import { EmailCampaignStatus } from '@/types/email-blast/email-blast.types';
import { revalidatePath } from 'next/cache';

/**
 * Send a campaign immediately by queuing all emails
 */
export async function sendCampaign(campaignId: string, batchSize: number = 100) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Verify campaign exists and is in valid state
  const { data: campaign, error: fetchError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('status, total_recipients')
    .eq('id', campaignId)
    .single();

  if (fetchError || !campaign) {
    throw new Error('Campaign not found');
  }

  if (!['draft', 'scheduled'].includes(campaign.status)) {
    throw new Error(`Cannot send campaign in ${campaign.status} status`);
  }

  // Update campaign status to sending
  const { error: updateError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: 'sending',
      started_at: new Date().toISOString(),
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId);

  if (updateError) {
    throw new Error('Failed to update campaign status');
  }

  // Queue emails using RPC function
  const { data: queuedCount, error: queueError } = await supabase
    .schema('email_system')
    .rpc('queue_campaign_emails', {
      p_campaign_id: campaignId,
      p_batch_size: batchSize
    });

  if (queueError) {
    // Revert status on error
    await supabase
      .schema('email_system')
      .from('email_campaigns')
      .update({
        status: campaign.status,
        started_at: null,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', campaignId);
      
    throw new Error(`Failed to queue emails: ${queueError.message}`);
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${campaignId}`);

  return {
    success: true,
    queuedCount: queuedCount || 0,
    totalRecipients: campaign.total_recipients || 0
  };
}

/**
 * Schedule a campaign to be sent at a specific time
 */
export async function scheduleCampaign(campaignId: string, scheduledAt: Date) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Verify scheduled time is in the future
  if (scheduledAt <= new Date()) {
    throw new Error('Scheduled time must be in the future');
  }

  // Update campaign
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: 'scheduled',
      scheduled_at: scheduledAt.toISOString(),
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .eq('status', 'draft')
    .select()
    .single();

  if (error) {
    throw new Error('Failed to schedule campaign');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${campaignId}`);

  return data;
}

/**
 * Cancel a scheduled campaign
 */
export async function cancelScheduledCampaign(campaignId: string) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Update campaign back to draft
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: 'draft',
      scheduled_at: null,
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .eq('status', 'scheduled')
    .select()
    .single();

  if (error) {
    throw new Error('Failed to cancel scheduled campaign');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${campaignId}`);

  return data;
}

/**
 * Duplicate an existing campaign
 */
export async function duplicateCampaign(campaignId: string, newName?: string) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Fetch original campaign
  const { data: original, error: fetchError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('id', campaignId)
    .single();

  if (fetchError || !original) {
    throw new Error('Campaign not found');
  }

  // Create duplicate with new name
  const duplicateName = newName || `${original.name} (Copy)`;
  
  const { data: duplicate, error: createError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .insert({
      name: duplicateName,
      description: original.description,
      status: 'draft',
      subject: original.subject,
      body_html: original.body_html,
      body_text: original.body_text,
      from_email: original.from_email,
      from_name: original.from_name,
      reply_to_email: original.reply_to_email,
      recipient_list_id: original.recipient_list_id,
      total_recipients: original.total_recipients,
      template_id: original.template_id,
      tags: original.tags,
      metadata: original.metadata,
      track_opens: original.track_opens,
      track_clicks: original.track_clicks,
      include_unsubscribe: original.include_unsubscribe,
      created_by: user.id,
      updated_by: user.id
    })
    .select()
    .single();

  if (createError) {
    throw new Error('Failed to duplicate campaign');
  }

  revalidatePath('/email-blast');

  return duplicate;
}

/**
 * Pause a currently sending campaign
 */
export async function pauseCampaign(campaignId: string) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Update campaign metadata to indicate paused state
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      metadata: {
        isPaused: true,
        pausedAt: new Date().toISOString(),
        pausedBy: user.id
      },
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .eq('status', 'sending')
    .select()
    .single();

  if (error) {
    throw new Error('Failed to pause campaign');
  }

  // Update any pending emails in the queue
  await supabase
    .schema('email_system')
    .from('email_queue')
    .update({
      metadata: {
        campaignPaused: true
      }
    })
    .eq('campaign_id', campaignId)
    .eq('status', 'pending');

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${campaignId}`);

  return data;
}

/**
 * Resume a paused campaign
 */
export async function resumeCampaign(campaignId: string) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Update campaign metadata to remove paused state
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      metadata: {
        isPaused: false,
        resumedAt: new Date().toISOString(),
        resumedBy: user.id
      },
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .eq('status', 'sending')
    .select()
    .single();

  if (error) {
    throw new Error('Failed to resume campaign');
  }

  // Update any paused emails in the queue
  await supabase
    .schema('email_system')
    .from('email_queue')
    .update({
      metadata: {
        campaignPaused: false
      }
    })
    .eq('campaign_id', campaignId)
    .eq('status', 'pending')
    .not('metadata->campaignPaused', 'is', null);

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${campaignId}`);

  return data;
}

/**
 * Mark a campaign as completed
 */
export async function completeCampaign(campaignId: string) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Update campaign status
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: 'completed',
      completed_at: new Date().toISOString(),
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', campaignId)
    .eq('status', 'sending')
    .select()
    .single();

  if (error) {
    throw new Error('Failed to complete campaign');
  }

  // Update campaign statistics one final time
  await supabase
    .schema('email_system')
    .rpc('update_campaign_statistics', {
      p_campaign_id: campaignId
    });

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${campaignId}`);

  return data;
}

/**
 * Send test emails for a campaign
 */
export async function sendTestEmails(campaignId: string, testEmails: string[]) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Validate email addresses
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const invalidEmails = testEmails.filter(email => !emailRegex.test(email));
  if (invalidEmails.length > 0) {
    throw new Error(`Invalid email addresses: ${invalidEmails.join(', ')}`);
  }

  // Fetch campaign details
  const { data: campaign, error: fetchError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('id', campaignId)
    .single();

  if (fetchError || !campaign) {
    throw new Error('Campaign not found');
  }

  // Queue test emails
  const testQueuePromises = testEmails.map(email => 
    supabase
      .schema('email_system')
      .from('email_queue')
      .insert({
        to_email: [email],
        subject: `[TEST] ${campaign.subject}`,
        body_html: campaign.body_html,
        body_text: campaign.body_text,
        template_id: campaign.template_id || '00000000-0000-0000-0000-000000000000',
        campaign_id: campaignId,
        is_blast_email: true,
        priority: 1,
        metadata: {
          isTest: true,
          testRequestedBy: user.id,
          testRequestedAt: new Date().toISOString()
        }
      })
  );

  const results = await Promise.all(testQueuePromises);
  const errors = results.filter(r => r.error);
  
  if (errors.length > 0) {
    throw new Error('Failed to queue some test emails');
  }

  return {
    success: true,
    count: testEmails.length
  };
}

/**
 * Get campaign recipients with pagination
 */
export async function getCampaignRecipients(
  campaignId: string, 
  page: number = 1, 
  pageSize: number = 50,
  status?: string
) {
  const supabase = await createClient();
  
  const offset = (page - 1) * pageSize;
  
  let query = supabase
    .schema('email_system')
    .from('campaign_recipients')
    .select('*', { count: 'exact' })
    .eq('campaign_id', campaignId)
    .order('created_at', { ascending: false })
    .range(offset, offset + pageSize - 1);

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error, count } = await query;

  if (error) {
    throw new Error('Failed to fetch campaign recipients');
  }

  return {
    recipients: data || [],
    totalCount: count || 0,
    page,
    pageSize,
    totalPages: Math.ceil((count || 0) / pageSize)
  };
}

/**
 * Export campaign recipients to CSV
 */
export async function exportCampaignRecipients(campaignId: string) {
  const supabase = await createClient();
  
  // Fetch all recipients
  const { data: recipients, error } = await supabase
    .schema('email_system')
    .from('campaign_recipients')
    .select('*')
    .eq('campaign_id', campaignId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error('Failed to fetch recipients for export');
  }

  // Convert to CSV format
  const headers = [
    'Email',
    'Name',
    'Status',
    'Sent At',
    'Delivered At',
    'Opened At',
    'Clicked At',
    'Bounced At',
    'Unsubscribed At',
    'Open Count',
    'Click Count',
    'Error Message'
  ];

  const rows = recipients?.map(r => [
    r.email,
    r.name || '',
    r.status,
    r.sent_at || '',
    r.delivered_at || '',
    r.opened_at || '',
    r.clicked_at || '',
    r.bounced_at || '',
    r.unsubscribed_at || '',
    r.open_count || 0,
    r.click_count || 0,
    r.error_message || ''
  ]) || [];

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');

  return {
    content: csvContent,
    filename: `campaign-${campaignId}-recipients-${new Date().toISOString().split('T')[0]}.csv`
  };
}