import { RecipientList, RecipientFilters } from '@/types/email-blast/recipient-list.types';
import { recipientListSchema } from '@/schemas/email-blast/recipient-list.schema';
import { z } from 'zod';
import { createClient } from '@/utils/supabase/Client';
import { Tables } from '@/types/email_system.types';

export type RecipientListFormData = z.infer<typeof recipientListSchema>;

type DbRecipientList = Tables<{ schema: 'email_system' }, 'recipient_lists'>;
type DbListMember = Tables<{ schema: 'email_system' }, 'recipient_list_members'>;

class RecipientListsService {
  private async getSupabase() {
    return createClient();
  }

  private mapDbToRecipientList(dbList: DbRecipientList): RecipientList {
    return {
      id: dbList.id,
      name: dbList.name,
      description: dbList.description || '',
      filters: (dbList.filters as RecipientFilters) || {},
      recipientCount: dbList.recipient_count || 0,
      isStatic: dbList.list_type === 'static',
      staticRecipientIds: dbList.list_type === 'static' ? [] : undefined, // Will be populated separately if needed
      tags: dbList.tags || [],
      metadata: dbList.metadata as Record<string, any> || {},
      createdAt: new Date(dbList.created_at || ''),
      updatedAt: new Date(dbList.updated_at || ''),
      createdBy: dbList.created_by,
      updatedBy: dbList.updated_by || dbList.created_by
    };
  }

  async getAll(): Promise<RecipientList[]> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching recipient lists:', error);
      throw new Error('Failed to fetch recipient lists');
    }

    return (data || []).map(this.mapDbToRecipientList);
  }

  async getById(id: string): Promise<RecipientList | null> {
    const supabase = await this.getSupabase();
    
    const { data, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching recipient list:', error);
      throw new Error('Failed to fetch recipient list');
    }

    return data ? this.mapDbToRecipientList(data) : null;
  }

  async create(data: RecipientListFormData): Promise<RecipientList> {
    const supabase = await this.getSupabase();
    
    // Validate data using create schema
    const { recipientListCreateSchema } = await import('@/schemas/email-blast/recipient-list.schema');
    const validatedData = recipientListCreateSchema.parse(data);
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');
    
    // Calculate recipient count
    let recipientCount = 0;
    if (validatedData.isStatic) {
      // For static lists, count the provided static recipients
      recipientCount = validatedData.staticRecipientIds?.length || 0;
    } else if (validatedData.filters) {
      // For dynamic lists, calculate count based on filters
      recipientCount = await this.countMembersByFilters(validatedData.filters);
    }
    
    const dbData = {
      name: validatedData.name,
      description: validatedData.description || null,
      list_type: validatedData.isStatic ? 'static' : 'dynamic',
      filters: validatedData.filters || {},
      recipient_count: recipientCount,
      tags: validatedData.tags || [],
      metadata: { isActive: true, ...(validatedData.metadata || {}) },
      created_by: user.id,
      status: 'active'
    };

    const { data: newList, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .insert(dbData)
      .select()
      .single();

    if (error) {
      console.error('Error creating recipient list:', error);
      throw new Error('Failed to create recipient list');
    }

    // If static list with recipients, add them
    if (validatedData.isStatic && validatedData.staticRecipientIds?.length) {
      await this.addStaticMembers(newList.id, validatedData.staticRecipientIds);
    }

    return this.mapDbToRecipientList(newList);
  }

  async update(id: string, data: Partial<RecipientListFormData>): Promise<RecipientList> {
    const supabase = await this.getSupabase();
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Validate partial data using update schema
    const { recipientListUpdateSchema } = await import('@/schemas/email-blast/recipient-list.schema');
    const validatedData = recipientListUpdateSchema.parse(data);

    // Get existing list
    const existing = await this.getById(id);
    if (!existing) {
      throw new Error('Recipient list not found');
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString(),
      updated_by: user.id
    };

    if (validatedData.name !== undefined) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.filters !== undefined) updateData.filters = validatedData.filters;
    if (validatedData.tags !== undefined) updateData.tags = validatedData.tags;
    if (validatedData.metadata !== undefined) updateData.metadata = validatedData.metadata;

    // Recalculate recipient count if filters changed on dynamic list
    if (!existing.isStatic && validatedData.filters) {
      updateData.recipient_count = await this.countMembersByFilters(validatedData.filters);
    }

    const { data: updatedList, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating recipient list:', error);
      throw new Error('Failed to update recipient list');
    }

    return this.mapDbToRecipientList(updatedList);
  }

  async delete(id: string): Promise<void> {
    const supabase = await this.getSupabase();
    
    // First delete any static members
    await supabase
      .schema('email_system')
      .from('recipient_list_members')
      .delete()
      .eq('list_id', id);

    // Then delete the list
    const { error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting recipient list:', error);
      throw new Error('Failed to delete recipient list');
    }
  }

  async toggleActive(id: string): Promise<RecipientList> {
    const supabase = await this.getSupabase();
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Get existing list
    const existing = await this.getById(id);
    if (!existing) {
      throw new Error('Recipient list not found');
    }

    // Toggle active state
    // Default to active (true) if isActive is undefined
    const currentlyActive = existing.metadata?.isActive !== false;
    const newStatus = currentlyActive ? 'inactive' : 'active';
    const newMetadata = {
      ...existing.metadata,
      isActive: newStatus === 'active'
    };

    const { data: updatedList, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .update({
        status: newStatus,
        metadata: newMetadata,
        updated_at: new Date().toISOString(),
        updated_by: user.id
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error toggling recipient list status:', error);
      throw new Error('Failed to toggle recipient list status');
    }

    return this.mapDbToRecipientList(updatedList);
  }

  async duplicateList(id: string, newName: string): Promise<RecipientList> {
    const supabase = await this.getSupabase();
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Get original list
    const { data: originalList, error: fetchError } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !originalList) {
      throw new Error('Recipient list not found');
    }

    // Create duplicate
    const duplicateData = {
      name: newName,
      description: originalList.description,
      list_type: originalList.list_type,
      filters: originalList.filters,
      recipient_count: originalList.recipient_count,
      tags: originalList.tags,
      metadata: originalList.metadata,
      created_by: user.id,
      status: 'active'
    };

    const { data: newList, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .insert(duplicateData)
      .select()
      .single();

    if (error) {
      console.error('Error duplicating recipient list:', error);
      throw new Error('Failed to duplicate recipient list');
    }

    // If static list, also duplicate members
    if (originalList.list_type === 'static') {
      const { data: members } = await supabase
        .schema('email_system')
        .from('recipient_list_members')
        .select('*')
        .eq('list_id', id);

      if (members && members.length > 0) {
        const newMembers = members.map(m => ({
          list_id: newList.id,
          email: m.email,
          name: m.name,
          member_data: m.member_data,
          added_by: user.id
        }));

        await supabase
          .schema('email_system')
          .from('recipient_list_members')
          .insert(newMembers);
      }
    }

    return this.mapDbToRecipientList(newList);
  }

  async getMemberCount(id: string): Promise<number> {
    const supabase = await this.getSupabase();
    
    // Get list details first
    const list = await this.getById(id);
    if (!list) {
      return 0;
    }

    if (list.isStatic) {
      // For static lists, count from recipient_list_members
      const { count } = await supabase
        .schema('email_system')
        .from('recipient_list_members')
        .select('*', { count: 'exact', head: true })
        .eq('list_id', id)
        .eq('is_active', true);
      
      return count || 0;
    } else {
      // For dynamic lists, calculate based on filters
      return await this.countMembersByFilters(list.filters);
    }
  }

  async refreshMemberCount(id: string): Promise<RecipientList> {
    const supabase = await this.getSupabase();
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Calculate new count
    const newCount = await this.getMemberCount(id);

    // Update the list with new count
    const { data: updatedList, error } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .update({
        recipient_count: newCount,
        last_calculated_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        updated_by: user.id
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating recipient count:', error);
      throw new Error('Failed to update recipient count');
    }

    return this.mapDbToRecipientList(updatedList);
  }

  // Alias methods for consistency
  async duplicate(id: string, options?: { name?: string; description?: string }): Promise<RecipientList> {
    const name = options?.name || `Copy of ${id}`;
    return this.duplicateList(id, name);
  }

  async refreshCount(id: string): Promise<RecipientList> {
    return this.refreshMemberCount(id);
  }

  async getRecipients(id: string, options?: { limit?: number; offset?: number }): Promise<any[]> {
    const supabase = await this.getSupabase();
    
    // Get list details first
    const list = await this.getById(id);
    if (!list) {
      throw new Error('Recipient list not found');
    }

    const limit = options?.limit || 50;
    const offset = options?.offset || 0;

    if (list.isStatic) {
      // For static lists, fetch from recipient_list_members
      const { data, error } = await supabase
        .schema('email_system')
        .from('recipient_list_members')
        .select('*')
        .eq('list_id', id)
        .eq('is_active', true)
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching static recipients:', error);
        throw new Error('Failed to fetch recipients');
      }

      return (data || []).map(member => ({
        id: member.id,
        name: member.name || '',
        email: member.email,
        ...((member.member_data as any) || {})
      }));
    } else {
      // For dynamic lists, use the previewMembers method with pagination
      const allMembers = await this.previewMembers(list.filters);
      
      // Apply pagination to the results
      const paginatedMembers = allMembers.slice(offset, offset + limit);
      
      return paginatedMembers;
    }
  }

  async export(id: string): Promise<void> {
    const list = await this.getById(id);
    if (!list) {
      throw new Error('Recipient list not found');
    }

    // Get all recipients for export
    const allRecipients: any[] = [];
    let offset = 0;
    const batchSize = 1000;

    while (true) {
      const batch = await this.getRecipients(id, { limit: batchSize, offset });
      if (batch.length === 0) break;
      allRecipients.push(...batch);
      offset += batchSize;
    }

    // Create CSV content
    const headers = ['email', 'name', 'member_data'];
    const csvContent = [
      headers.join(','),
      ...allRecipients.map(r => 
        [r.email, r.name || '', JSON.stringify(r.member_data || {})].join(',')
      )
    ].join('\n');

    // In a real implementation, you would:
    // 1. Upload to cloud storage
    // 2. Send download link via email
    // 3. Or trigger a download in the browser
    // Successfully exported recipients
  }

  async countMembersByFilters(filters: RecipientFilters): Promise<number> {
    const supabase = await this.getSupabase();
    
    try {
      // Build query based on filters using membership_profiles view
      let query = supabase
        .from('membership_profiles')
        .select('*', { count: 'exact', head: true });

      // Apply membership status filter
      if (filters.membershipStatus && filters.membershipStatus.length > 0) {
        query = query.in('membership_status', filters.membershipStatus);
      }

      // Apply expiry date range filter
      if (filters.expiryDateRange) {
        if (filters.expiryDateRange.from) {
          query = query.gte('end_date', filters.expiryDateRange.from.toISOString());
        }
        if (filters.expiryDateRange.to) {
          query = query.lte('end_date', filters.expiryDateRange.to.toISOString());
        }
      }

      // Apply membership types filter
      if (filters.membershipTypes && filters.membershipTypes.length > 0) {
        query = query.in('membership_type_name', filters.membershipTypes);
      }

      // Apply registration date range filter
      if (filters.registrationDateRange) {
        if (filters.registrationDateRange.from) {
          query = query.gte('start_date', filters.registrationDateRange.from.toISOString());
        }
        if (filters.registrationDateRange.to) {
          query = query.lte('start_date', filters.registrationDateRange.to.toISOString());
        }
      }

      // Apply exclude filters
      let excludedEmails: string[] = [];
      
      if (filters.excludeUnsubscribed || filters.excludeBounced) {
        const suppressionTypes = [];
        if (filters.excludeUnsubscribed) suppressionTypes.push('unsubscribe');
        if (filters.excludeBounced) suppressionTypes.push('bounce');
        
        const { data: suppressedEmails } = await supabase
          .schema('email_system')
          .from('email_suppressions')
          .select('email_address')
          .in('suppression_type', suppressionTypes)
          .eq('is_active', true);

        if (suppressedEmails && suppressedEmails.length > 0) {
          excludedEmails = suppressedEmails.map(s => s.email_address);
        }
      }

      if (excludedEmails.length > 0) {
        query = query.not('email', 'in', `(${excludedEmails.join(',')})`);
      }

      const { count, error } = await query;

      if (error) {
        console.error('Error counting members:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error in countMembersByFilters:', error);
      return 0;
    }
  }

  async previewMembers(filters: RecipientFilters): Promise<any[]> {
    const supabase = await this.getSupabase();
    
    try {
      // Build query based on filters using membership_profiles view
      let query = supabase
        .from('membership_profiles')
        .select(`
          membership_id,
          full_name,
          email,
          membership_type_name,
          membership_status,
          end_date,
          start_date
        `);

      // Apply membership status filter
      if (filters.membershipStatus && filters.membershipStatus.length > 0) {
        query = query.in('membership_status', filters.membershipStatus);
      }

      // Apply expiry date range filter
      if (filters.expiryDateRange) {
        if (filters.expiryDateRange.from) {
          query = query.gte('end_date', filters.expiryDateRange.from.toISOString());
        }
        if (filters.expiryDateRange.to) {
          query = query.lte('end_date', filters.expiryDateRange.to.toISOString());
        }
      }

      // Apply membership types filter
      if (filters.membershipTypes && filters.membershipTypes.length > 0) {
        query = query.in('membership_type_name', filters.membershipTypes);
      }

      // Apply registration date range filter
      if (filters.registrationDateRange) {
        if (filters.registrationDateRange.from) {
          query = query.gte('start_date', filters.registrationDateRange.from.toISOString());
        }
        if (filters.registrationDateRange.to) {
          query = query.lte('start_date', filters.registrationDateRange.to.toISOString());
        }
      }

      // Apply exclude filters
      let excludedEmails: string[] = [];
      
      if (filters.excludeUnsubscribed || filters.excludeBounced) {
        const suppressionTypes = [];
        if (filters.excludeUnsubscribed) suppressionTypes.push('unsubscribe');
        if (filters.excludeBounced) suppressionTypes.push('bounce');
        
        const { data: suppressedEmails } = await supabase
          .schema('email_system')
          .from('email_suppressions')
          .select('email_address')
          .in('suppression_type', suppressionTypes)
          .eq('is_active', true);

        if (suppressedEmails && suppressedEmails.length > 0) {
          excludedEmails = suppressedEmails.map(s => s.email_address);
        }
      }

      if (excludedEmails.length > 0) {
        query = query.not('email', 'in', `(${excludedEmails.join(',')})`);
      }

      // Limit preview to 100 members
      query = query.limit(100);

      const { data, error } = await query;

      if (error) {
        console.error('Error previewing members:', error);
        return [];
      }

      // Transform data to match expected format
      return (data || []).map((member: any) => ({
        id: member.membership_id,
        name: member.full_name || 'N/A',
        email: member.email,
        membershipType: member.membership_type_name || 'N/A',
        status: member.membership_status || 'unknown',
        expiryDate: member.end_date ? new Date(member.end_date) : null
      }));
    } catch (error) {
      console.error('Error in previewMembers:', error);
      return [];
    }
  }

  // Helper method to apply filters in memory (for dynamic lists)
  private applyFiltersToMembers(members: any[], filters: RecipientFilters): any[] {
    let filtered = [...members];

    if (filters.membershipStatus?.length) {
      filtered = filtered.filter(m => filters.membershipStatus!.includes(m.membership_status));
    }

    if (filters.membershipTypes?.length) {
      filtered = filtered.filter(m => filters.membershipTypes!.includes(m.membership_type));
    }

    if (filters.locations?.length) {
      filtered = filtered.filter(m => filters.locations!.includes(m.location));
    }

    if (filters.excludeUnsubscribed) {
      filtered = filtered.filter(m => m.is_subscribed !== false);
    }

    return filtered;
  }

  // Helper method to add static members
  private async addStaticMembers(listId: string, memberData: any[]): Promise<void> {
    const supabase = await this.getSupabase();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const members = memberData.map(m => {
      // Handle both string emails and member objects
      if (typeof m === 'string') {
        return {
          list_id: listId,
          email: m,
          name: null,
          member_data: { email: m },
          added_by: user.id,
          is_active: true
        };
      } else {
        return {
          list_id: listId,
          email: m.email,
          name: m.name || null,
          member_data: m,
          added_by: user.id,
          is_active: true
        };
      }
    });

    const { error } = await supabase
      .schema('email_system')
      .from('recipient_list_members')
      .insert(members);

    if (error) {
      console.error('Error adding static members:', error);
    }
  }
}

// Export singleton instance
export const recipientListsService = new RecipientListsService();