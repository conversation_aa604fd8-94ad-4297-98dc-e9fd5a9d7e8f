'use server';

import { EmailBlast } from '@/types/email-blast/email-blast.types';
import { EmailBlastFormData } from '@/schemas/email-blast/email-blast.schema';
import * as serverService from './email-blast-server.service';

/**
 * Server-side adapter service that provides a consistent interface for email blast operations
 * This adapter ensures compatibility between the server service and client expectations
 */
export const emailBlastServerService = {
  async getAll(): Promise<EmailBlast[]> {
    return await serverService.getEmailBlasts();
  },

  async getById(id: string): Promise<EmailBlast | null> {
    return await serverService.getEmailBlastById(id);
  },

  async create(data: any): Promise<EmailBlast> {
    // Transform the nested form data to the flat structure expected by the server
    const transformedData = {
      name: data.name || '',
      description: data.description || '',
      subject: data.content?.subject || '',
      content_text: data.content?.body || '',
      content_html: data.content?.bodyHtml || data.content?.body || '',
      from_name: data.from_name || 'IES Admin', // Use provided or default
      from_email: data.from_email || '<EMAIL>', // Use provided or default
      reply_to_email: data.reply_to_email || '',
      recipientListId: data.recipientListId || '',
      tags: data.tags || [],
      metadata: data.metadata || {},
      templateId: data.content?.templateId || '',
    };
    
    console.log('Adapter transforming data:', {
      original: data,
      transformed: transformedData
    });
    
    return await serverService.createEmailBlast(transformedData);
  },

  async update(id: string, data: any): Promise<EmailBlast> {
    // Transform the nested form data to the flat structure expected by the server
    const transformedData: any = {};
    
    if (data.name !== undefined) transformedData.name = data.name;
    if (data.description !== undefined) transformedData.description = data.description;
    if (data.content?.subject !== undefined) transformedData.subject = data.content.subject;
    if (data.content?.body !== undefined) transformedData.content_text = data.content.body;
    if (data.content?.bodyHtml !== undefined) transformedData.content_html = data.content.bodyHtml || data.content.body;
    if (data.recipientListId !== undefined) transformedData.recipientListId = data.recipientListId;
    if (data.tags !== undefined) transformedData.tags = data.tags;
    if (data.metadata !== undefined) transformedData.metadata = data.metadata;
    if (data.content?.templateId !== undefined) transformedData.templateId = data.content.templateId;
    
    return await serverService.updateEmailBlast(id, transformedData);
  },

  async delete(id: string): Promise<void> {
    await serverService.deleteEmailBlast(id);
  },

  async scheduleCampaign(id: string, scheduledAt: string): Promise<EmailBlast> {
    return await serverService.scheduleEmailBlast(id, scheduledAt);
  },

  async sendNow(id: string): Promise<EmailBlast> {
    return await serverService.sendEmailBlastNow(id);
  },

  async duplicateCampaign(id: string, newName: string): Promise<EmailBlast> {
    return await serverService.duplicateEmailBlast(id, newName);
  },

  async testEmail(id: string, emails: string[]): Promise<void> {
    await serverService.sendTestEmail(id, emails);
  },

  async pauseCampaign(id: string): Promise<EmailBlast> {
    return await serverService.pauseEmailBlast(id);
  },

  async resumeCampaign(id: string): Promise<EmailBlast> {
    return await serverService.resumeEmailBlast(id);
  },

  async cancelScheduledCampaign(id: string): Promise<EmailBlast> {
    return await serverService.cancelEmailBlast(id);
  },

  async getStats(id: string) {
    return await serverService.getEmailBlastStats(id);
  }
};