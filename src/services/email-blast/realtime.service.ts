'use client';

import { SupabaseClient } from '@supabase/supabase-js';
import { createClient } from '@/utils/supabase/Client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { Tables } from '@/types/email_system.types';

type EmailCampaign = Tables<{ schema: 'email_system' }, 'email_campaigns'>;
type CampaignStatistics = Tables<{ schema: 'email_system' }, 'campaign_statistics'>;
type EmailQueue = Tables<{ schema: 'email_system' }, 'email_queue'>;
type CampaignRecipient = Tables<{ schema: 'email_system' }, 'campaign_recipients'>;

export interface RealtimeSubscriptionOptions {
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export class EmailBlastRealtimeService {
  private supabase: SupabaseClient | null = null;
  private channels: Map<string, RealtimeChannel> = new Map();
  
  private async getClient(): Promise<SupabaseClient> {
    if (!this.supabase) {
      this.supabase = await createClient();
    }
    return this.supabase;
  }
  
  /**
   * Subscribe to campaign status updates
   */
  async subscribeToCampaignStatus(
    campaignId: string,
    onUpdate: (campaign: EmailCampaign) => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = `campaign-status-${campaignId}`;
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'email_system',
          table: 'email_campaigns',
          filter: `id=eq.${campaignId}`
        },
        (payload: RealtimePostgresChangesPayload<EmailCampaign>) => {
          if (payload.new && typeof payload.new === 'object' && 'id' in payload.new) {
            onUpdate(payload.new as EmailCampaign);
          }
        }
      );


    // Subscribe with error handling
    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to campaign status updates
        options?.onConnect?.();
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to campaign ${campaignId}: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    // Return unsubscribe function
    return () => this.unsubscribe(channelName);
  }

  /**
   * Subscribe to campaign statistics updates
   */
  async subscribeToCampaignStatistics(
    campaignId: string,
    onUpdate: (stats: CampaignStatistics) => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = `campaign-stats-${campaignId}`;
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'email_system',
          table: 'campaign_statistics',
          filter: `campaign_id=eq.${campaignId}`
        },
        (payload: RealtimePostgresChangesPayload<CampaignStatistics>) => {
          if (payload.new && typeof payload.new === 'object' && 'campaign_id' in payload.new) {
            onUpdate(payload.new as CampaignStatistics);
          }
        }
      );

    // Subscribe with error handling
    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to campaign statistics updates
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to campaign statistics: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    return () => this.unsubscribe(channelName);
  }

  /**
   * Subscribe to email queue updates for a campaign
   */
  async subscribeToCampaignQueue(
    campaignId: string,
    onUpdate: (queueItem: EmailQueue) => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = `campaign-queue-${campaignId}`;
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'email_system',
          table: 'email_queue',
          filter: `campaign_id=eq.${campaignId}`
        },
        (payload: RealtimePostgresChangesPayload<EmailQueue>) => {
          if (payload.new && typeof payload.new === 'object' && 'id' in payload.new) {
            onUpdate(payload.new as EmailQueue);
          }
        }
      );

    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to campaign queue updates
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to campaign queue: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    return () => this.unsubscribe(channelName);
  }

  /**
   * Subscribe to recipient updates for a campaign
   */
  async subscribeToCampaignRecipients(
    campaignId: string,
    onUpdate: (recipient: CampaignRecipient, event: 'INSERT' | 'UPDATE' | 'DELETE') => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = `campaign-recipients-${campaignId}`;
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'email_system',
          table: 'campaign_recipients',
          filter: `campaign_id=eq.${campaignId}`
        },
        (payload: RealtimePostgresChangesPayload<CampaignRecipient>) => {
          if (payload.eventType && payload.new && typeof payload.new === 'object' && 'id' in payload.new) {
            onUpdate(payload.new as CampaignRecipient, payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE');
          }
        }
      );

    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to campaign recipient updates
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to campaign recipients: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    return () => this.unsubscribe(channelName);
  }

  /**
   * Subscribe to all campaigns list updates
   */
  async subscribeToAllCampaigns(
    onUpdate: (campaign: EmailCampaign, event: 'INSERT' | 'UPDATE' | 'DELETE') => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = 'all-campaigns';
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'email_system',
          table: 'email_campaigns'
        },
        (payload: RealtimePostgresChangesPayload<EmailCampaign>) => {
          if (payload.eventType && payload.new && typeof payload.new === 'object' && 'id' in payload.new) {
            onUpdate(payload.new as EmailCampaign, payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE');
          }
        }
      );

    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to all campaigns updates
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to all campaigns: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    return () => this.unsubscribe(channelName);
  }

  /**
   * Subscribe to queue progress for monitoring sending progress
   */
  async subscribeToQueueProgress(
    filters: { status?: string; campaignId?: string },
    onUpdate: (stats: { total: number; pending: number; sent: number; failed: number }) => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = `queue-progress-${JSON.stringify(filters)}`;
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    let postgresFilter = '';
    if (filters.campaignId) {
      postgresFilter = `campaign_id=eq.${filters.campaignId}`;
    }
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'email_system',
          table: 'email_queue',
          ...(postgresFilter && { filter: postgresFilter })
        },
        async (payload: any) => {
          // Fetch current queue stats
          const client = await this.getClient();
          let query = client
            .schema('email_system')
            .from('email_queue')
            .select('status', { count: 'exact', head: true });

          if (filters.campaignId) {
            query = query.eq('campaign_id', filters.campaignId);
          }

          const statuses = ['pending', 'sent', 'failed'];
          const counts = await Promise.all(
            statuses.map(async (status) => {
              const { count } = await query.eq('status', status);
              return { status, count: count || 0 };
            })
          );

          const stats = {
            total: counts.reduce((sum, { count }) => sum + count, 0),
            pending: counts.find(c => c.status === 'pending')?.count || 0,
            sent: counts.find(c => c.status === 'sent')?.count || 0,
            failed: counts.find(c => c.status === 'failed')?.count || 0
          };

          onUpdate(stats);
        }
      );

    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to queue progress updates
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to queue progress: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    return () => this.unsubscribe(channelName);
  }

  /**
   * Subscribe to email events (opens, clicks, bounces)
   */
  async subscribeToEmailEvents(
    campaignId: string,
    eventType: 'opens' | 'clicks' | 'delivery_events',
    onUpdate: (event: any) => void,
    options?: RealtimeSubscriptionOptions
  ): Promise<() => void> {
    const channelName = `email-events-${campaignId}-${eventType}`;
    
    // Remove existing channel if any
    await this.unsubscribe(channelName);
    
    const tableMap = {
      opens: 'email_opens',
      clicks: 'email_clicks',
      delivery_events: 'email_delivery_events'
    };
    
    const supabase = await this.getClient();
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'email_system',
          table: tableMap[eventType],
          filter: `campaign_id=eq.${campaignId}`
        },
        (payload: any) => {
          if (payload.new) {
            onUpdate(payload.new);
          }
        }
      );

    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        // Subscribed to campaign events
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        const error = new Error(`Failed to subscribe to ${eventType} events: ${status}`);
        console.error(error);
        options?.onError?.(error);
      }
    });

    this.channels.set(channelName, channel);

    return () => this.unsubscribe(channelName);
  }

  /**
   * Unsubscribe from a specific channel
   */
  private async unsubscribe(channelName: string): Promise<void> {
    const channel = this.channels.get(channelName);
    if (channel) {
      const supabase = await this.getClient();
      supabase.removeChannel(channel);
      this.channels.delete(channelName);
      // Unsubscribed from channel
    }
  }

  /**
   * Unsubscribe from all channels
   */
  async unsubscribeAll(): Promise<void> {
    const supabase = await this.getClient();
    this.channels.forEach((channel, name) => {
      supabase.removeChannel(channel);
      // Unsubscribed from subscription
    });
    this.channels.clear();
  }

  /**
   * Get list of active subscriptions
   */
  getActiveSubscriptions(): string[] {
    return Array.from(this.channels.keys());
  }

  /**
   * Check if subscribed to a specific channel
   */
  isSubscribed(channelName: string): boolean {
    return this.channels.has(channelName);
  }
}

// Export singleton instance
export const emailBlastRealtimeService = new EmailBlastRealtimeService();

// Export convenience hooks for React components
export async function useRealtimeCampaignStatus(
  campaignId: string,
  onUpdate: (campaign: EmailCampaign) => void,
  options?: RealtimeSubscriptionOptions
) {
  const service = emailBlastRealtimeService;
  
  // Subscribe on mount
  const unsubscribe = await service.subscribeToCampaignStatus(campaignId, onUpdate, options);
  
  // Return unsubscribe function
  return unsubscribe;
}

export async function useRealtimeCampaignStats(
  campaignId: string,
  onUpdate: (stats: CampaignStatistics) => void,
  options?: RealtimeSubscriptionOptions
) {
  const service = emailBlastRealtimeService;
  
  // Subscribe on mount
  const unsubscribe = await service.subscribeToCampaignStatistics(campaignId, onUpdate, options);
  
  // Return unsubscribe function
  return unsubscribe;
}