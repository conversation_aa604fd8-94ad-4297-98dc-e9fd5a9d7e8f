'use server';

import { EmailBlast, EmailCampaignStatus } from '@/types/email-blast/email-blast.types';
import { createClient } from '@/utils/supabase/server';
import { emailBlastFormSchema, EmailBlastFormData } from '@/schemas/email-blast/email-blast.schema';
import { revalidatePath } from 'next/cache';

// Helper function to transform database row to EmailBlast type
function transformToEmailBlast(campaign: any): EmailBlast {
  return {
    id: campaign.id,
    name: campaign.name,
    description: campaign.description || '',
    status: campaign.status as EmailCampaignStatus,
    content: {
      subject: campaign.subject || '',
      body: campaign.body_text || '',
      bodyHtml: campaign.body_html || '',
      attachments: [],
      templateId: campaign.template_id || undefined,
      variables: campaign.metadata?.variables || {}
    },
    recipientListId: campaign.recipient_list_id,
    recipientCount: campaign.total_recipients || 0,
    sentCount: campaign.sent_count || 0,
    failedCount: campaign.failed_count || 0,
    openCount: campaign.opened_count || 0,
    clickCount: campaign.clicked_count || 0,
    tags: campaign.tags || [],
    metadata: campaign.metadata || {},
    createdAt: new Date(campaign.created_at || Date.now()),
    updatedAt: new Date(campaign.updated_at || campaign.created_at || Date.now()),
    sentAt: campaign.completed_at ? new Date(campaign.completed_at) : undefined,
    scheduledAt: campaign.scheduled_at ? new Date(campaign.scheduled_at) : undefined,
    createdBy: campaign.created_by || 'Unknown',
    updatedBy: campaign.updated_by || campaign.created_by || 'Unknown'
  };
}

export async function getEmailBlasts(): Promise<EmailBlast[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching email blasts:', error);
    throw new Error('Failed to fetch email blasts');
  }

  return (data || []).map(transformToEmailBlast);
}

export async function getEmailBlastById(id: string): Promise<EmailBlast | null> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null;
    }
    console.error('Error fetching email blast:', error);
    throw new Error('Failed to fetch email blast');
  }

  return data ? transformToEmailBlast(data) : null;
}

export async function createEmailBlast(data: any): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Validate data - expect flat structure after adapter transformation
  const validatedData = emailBlastFormSchema.parse(data);
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Calculate total recipients from recipient lists
  const { data: list } = await supabase
    .schema('email_system')
    .from('recipient_lists')
    .select('recipient_count')
    .eq('id', validatedData.recipientListId)
    .single();

  const totalRecipients = list?.recipient_count || 0;

  const { data: newBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .insert({
      name: validatedData.name,
      description: validatedData.description,
      status: EmailCampaignStatus.DRAFT,
      subject: validatedData.subject,
      body_text: validatedData.content_text || '',
      body_html: validatedData.content_html,
      from_email: validatedData.from_email || '<EMAIL>',
      from_name: validatedData.from_name || 'Example Company',
      recipient_list_id: validatedData.recipientListId,
      total_recipients: totalRecipients,
      sent_count: 0,
      failed_count: 0,
      opened_count: 0,
      clicked_count: 0,
      tags: validatedData.tags,
      metadata: validatedData.metadata,
      template_id: validatedData.templateId,
      created_by: user.id,
      updated_by: user.id
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating email blast:', error);
    throw new Error('Failed to create email blast');
  }

  revalidatePath('/email-blast');
  return transformToEmailBlast(newBlast);
}

export async function updateEmailBlast(id: string, data: Partial<EmailBlastFormData>): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Check if campaign is editable
  const { data: existing } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('status')
    .eq('id', id)
    .single();

  if (!existing) {
    throw new Error('Email blast not found');
  }

  if (existing.status !== EmailCampaignStatus.DRAFT) {
    throw new Error('Can only edit draft campaigns');
  }

  // Validate partial data
  const partialSchema = emailBlastFormSchema.partial();
  const validatedData = partialSchema.parse(data);

  // Prepare update data
  let updateData: any = {
    updated_by: user.id,
    updated_at: new Date().toISOString()
  };

  // Update fields if provided
  if (validatedData.name !== undefined) updateData.name = validatedData.name;
  if (validatedData.description !== undefined) updateData.description = validatedData.description;
  if (validatedData.tags !== undefined) updateData.tags = validatedData.tags;
  if (validatedData.metadata !== undefined) updateData.metadata = validatedData.metadata;

  // Update content if provided
  if (validatedData.subject) updateData.subject = validatedData.subject;
  if (validatedData.content_html) updateData.body_html = validatedData.content_html;
  if (validatedData.content_text) updateData.body_text = validatedData.content_text;

  // Recalculate recipients if list changed
  if (validatedData.recipientListId) {
    const { data: list } = await supabase
      .schema('email_system')
      .from('recipient_lists')
      .select('recipient_count')
      .eq('id', validatedData.recipientListId)
      .single();

    updateData.recipient_list_id = validatedData.recipientListId;
    updateData.total_recipients = list?.recipient_count || 0;
  }

  const { data: updatedBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating email blast:', error);
    throw new Error('Failed to update email blast');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${id}`);
  return transformToEmailBlast(updatedBlast);
}

export async function deleteEmailBlast(id: string): Promise<void> {
  const supabase = await createClient();
  
  // Check if campaign is deletable
  const { data: existing } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('status')
    .eq('id', id)
    .single();

  if (!existing) {
    throw new Error('Email blast not found');
  }

  if (existing.status !== EmailCampaignStatus.DRAFT) {
    throw new Error('Can only delete draft campaigns');
  }

  const { error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting email blast:', error);
    throw new Error('Failed to delete email blast');
  }

  revalidatePath('/email-blast');
}

export async function scheduleEmailBlast(id: string, scheduledAt: string): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data: updatedBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: EmailCampaignStatus.SCHEDULED,
      scheduled_at: scheduledAt,
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .eq('status', EmailCampaignStatus.DRAFT)
    .select()
    .single();

  if (error) {
    console.error('Error scheduling email blast:', error);
    throw new Error('Failed to schedule email blast');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${id}`);
  return transformToEmailBlast(updatedBlast);
}

export async function sendEmailBlastNow(id: string): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // First update the campaign status
  const { data: updatedBlast, error: updateError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: EmailCampaignStatus.SENDING,
      started_at: new Date().toISOString(),
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .in('status', [EmailCampaignStatus.DRAFT, EmailCampaignStatus.SCHEDULED])
    .select()
    .single();

  if (updateError) {
    console.error('Error updating campaign status:', updateError);
    throw new Error('Failed to update campaign status');
  }

  // Queue emails for sending using the RPC function
  const { data: queuedCount, error: queueError } = await supabase
    .schema('email_system')
    .rpc('queue_campaign_emails', {
      p_campaign_id: id,
      p_batch_size: 100
    });

  if (queueError) {
    console.error('Error queueing campaign emails:', queueError);
    // Revert status on error
    await supabase
      .schema('email_system')
      .from('email_campaigns')
      .update({
        status: EmailCampaignStatus.DRAFT,
        started_at: null,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);
    throw new Error('Failed to queue campaign emails');
  }

  // Successfully queued emails for campaign

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${id}`);
  return transformToEmailBlast(updatedBlast);
}

export async function pauseEmailBlast(id: string): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data: updatedBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      metadata: {
        isPaused: true,
        pausedAt: new Date().toISOString()
      },
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .eq('status', EmailCampaignStatus.SENDING)
    .select()
    .single();

  if (error) {
    console.error('Error pausing email blast:', error);
    throw new Error('Failed to pause email blast');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${id}`);
  return transformToEmailBlast(updatedBlast);
}

export async function resumeEmailBlast(id: string): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data: updatedBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      metadata: {
        isPaused: false,
        resumedAt: new Date().toISOString()
      },
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .not('metadata->isPaused', 'is', null)
    .eq('metadata->isPaused', true)
    .select()
    .single();

  if (error) {
    console.error('Error resuming email blast:', error);
    throw new Error('Failed to resume email blast');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${id}`);
  return transformToEmailBlast(updatedBlast);
}

export async function cancelEmailBlast(id: string): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data: updatedBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .update({
      status: EmailCampaignStatus.DRAFT,
      scheduled_at: null,
      updated_by: user.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .eq('status', EmailCampaignStatus.SCHEDULED)
    .select()
    .single();

  if (error) {
    console.error('Error cancelling email blast:', error);
    throw new Error('Failed to cancel email blast');
  }

  revalidatePath('/email-blast');
  revalidatePath(`/email-blast/${id}`);
  return transformToEmailBlast(updatedBlast);
}

export async function duplicateEmailBlast(id: string, newName: string): Promise<EmailBlast> {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Get original campaign
  const { data: original, error: fetchError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('id', id)
    .single();

  if (fetchError || !original) {
    throw new Error('Email blast not found');
  }

  // Create duplicate
  const { data: duplicate, error: createError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .insert({
      name: newName,
      description: original.description,
      status: EmailCampaignStatus.DRAFT,
      subject: original.subject,
      body_html: original.body_html,
      body_text: original.body_text,
      from_email: original.from_email,
      from_name: original.from_name,
      recipient_list_id: original.recipient_list_id,
      total_recipients: original.total_recipients,
      sent_count: 0,
      failed_count: 0,
      opened_count: 0,
      clicked_count: 0,
      tags: original.tags,
      metadata: original.metadata,
      template_id: original.template_id,
      created_by: user.id,
      updated_by: user.id
    })
    .select()
    .single();

  if (createError) {
    console.error('Error duplicating email blast:', createError);
    throw new Error('Failed to duplicate email blast');
  }

  revalidatePath('/email-blast');
  return transformToEmailBlast(duplicate);
}

export async function sendTestEmail(id: string, testEmails: string[]): Promise<void> {
  const supabase = await createClient();
  
  // Get email blast details
  const { data: emailBlast, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('id', id)
    .single();

  if (error || !emailBlast) {
    throw new Error('Email blast not found');
  }

  // In real implementation, this would send test emails
  // For now, we'll just log the action
  // Test emails prepared for campaign
}

export async function getEmailBlastsByStatus(status: EmailCampaignStatus): Promise<EmailBlast[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select('*')
    .eq('status', status)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching email blasts by status:', error);
    throw new Error('Failed to fetch email blasts');
  }

  return (data || []).map(transformToEmailBlast);
}

export async function getEmailBlastStats(id: string) {
  const supabase = await createClient();
  
  // First, update the campaign statistics using the RPC
  const { error: updateError } = await supabase
    .schema('email_system')
    .rpc('update_campaign_statistics', {
      p_campaign_id: id
    });

  if (updateError) {
    console.error('Error updating campaign statistics:', updateError);
    // Continue anyway to fetch existing stats
  }
  
  // Now fetch the updated statistics
  const { data: campaign, error: campaignError } = await supabase
    .schema('email_system')
    .from('email_campaigns')
    .select(`
      sent_count,
      failed_count,
      opened_count,
      clicked_count,
      bounced_count,
      delivered_count,
      unsubscribed_count,
      metadata
    `)
    .eq('id', id)
    .single();

  if (campaignError) {
    console.error('Error fetching campaign data:', campaignError);
    throw new Error('Failed to fetch campaign data');
  }

  // Also fetch from campaign_statistics table for more detailed stats
  const { data: stats, error: statsError } = await supabase
    .schema('email_system')
    .from('campaign_statistics')
    .select('*')
    .eq('campaign_id', id)
    .single();

  if (statsError && statsError.code !== 'PGRST116') {
    console.error('Error fetching campaign statistics:', statsError);
  }

  return {
    sent: campaign.sent_count || 0,
    delivered: campaign.delivered_count || stats?.delivered_count || 0,
    opened: campaign.opened_count || stats?.unique_opens || 0,
    clicked: campaign.clicked_count || stats?.unique_clicks || 0,
    unsubscribed: campaign.unsubscribed_count || stats?.unsubscribe_count || 0,
    bounced: campaign.bounced_count || stats?.bounce_count || 0,
    failed: campaign.failed_count || 0,
    openRate: stats?.open_rate || 0,
    clickRate: stats?.click_rate || 0,
    deliveryRate: stats?.delivery_rate || 0,
    bounceRate: stats?.bounce_rate || 0,
    unsubscribeRate: stats?.unsubscribe_rate || 0,
    totalOpens: stats?.total_opens || 0,
    totalClicks: stats?.total_clicks || 0,
    uniqueOpens: stats?.unique_opens || 0,
    uniqueClicks: stats?.unique_clicks || 0
  };
}