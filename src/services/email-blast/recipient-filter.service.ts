import { RecipientFilters, RecipientPreview, MembershipStatus, FilterOperator } from '@/types/email-blast/recipient-list.types';
import { createAutoAdapter, MemberDataAdapter } from '@/lib/email-blast/member-adapter';
import { createClient } from '@/utils/supabase/Client';
import { FilterQueryBuilder } from '@/lib/email-blast/filter-query-builder';

// Define types for advanced filtering
export interface RecipientFilter {
  id: string;
  field: string;
  condition: 
    | 'equals' 
    | 'not_equals' 
    | 'contains' 
    | 'not_contains' 
    | 'starts_with' 
    | 'ends_with' 
    | 'is_empty' 
    | 'is_not_empty'
    | 'greater_than' 
    | 'less_than' 
    | 'greater_than_or_equal' 
    | 'less_than_or_equal'
    | 'in_list' 
    | 'not_in_list' 
    | 'between'
    | 'date_equals' 
    | 'date_before' 
    | 'date_after' 
    | 'date_between';
  value: any;
  group?: string;
  operator?: 'AND' | 'OR';
}

export interface FilterGroup {
  id: string;
  filters: RecipientFilter[];
  operator: 'AND' | 'OR';
}

// Use RecipientPreview as Member type
type Member = RecipientPreview;

class RecipientFilterService {
  private memberAdapter: MemberDataAdapter | null = null;
  
  private async getMemberAdapter(): Promise<MemberDataAdapter> {
    if (!this.memberAdapter) {
      this.memberAdapter = await createAutoAdapter();
    }
    return this.memberAdapter;
  }

  async getFilteredMembers(filters: RecipientFilter[]): Promise<Member[]> {
    const adapter = await this.getMemberAdapter();
    
    // Convert RecipientFilter[] to RecipientFilters format
    const recipientFilters = this.convertToRecipientFilters(filters);
    
    const { members } = await adapter.getMembersByFilters(recipientFilters);
    return members;
  }

  async getFilteredMemberIds(filters: RecipientFilter[]): Promise<string[]> {
    const filteredMembers = await this.getFilteredMembers(filters);
    return filteredMembers.map(member => member.id);
  }

  async countFilteredMembers(filters: RecipientFilter[]): Promise<number> {
    const adapter = await this.getMemberAdapter();
    const recipientFilters = this.convertToRecipientFilters(filters);
    const { total } = await adapter.getMembersByFilters(recipientFilters);
    return total;
  }

  async previewFilteredMembers(filters: RecipientFilter[], limit: number = 10): Promise<Member[]> {
    const adapter = await this.getMemberAdapter();
    const recipientFilters = this.convertToRecipientFilters(filters);
    const { members } = await adapter.getMembersByFilters(recipientFilters, { page: 1, limit });
    return members;
  }

  private convertToRecipientFilters(filters: RecipientFilter[]): RecipientFilters {
    const recipientFilters: RecipientFilters = {};
    
    // Group filters by field and convert to RecipientFilters format
    filters.forEach(filter => {
      // Handle specific field mappings
      switch (filter.field) {
        case 'status':
        case 'membership_status':
          if (!recipientFilters.membershipStatus) {
            recipientFilters.membershipStatus = [];
          }
          if (filter.value && filter.condition === 'equals') {
            recipientFilters.membershipStatus.push(filter.value as MembershipStatus);
          }
          break;
          
        case 'membership_type':
        case 'membershipType':
          if (!recipientFilters.membershipTypes) {
            recipientFilters.membershipTypes = [];
          }
          if (filter.value && filter.condition === 'equals') {
            recipientFilters.membershipTypes.push(filter.value);
          }
          break;
          
        case 'location':
          if (!recipientFilters.locations) {
            recipientFilters.locations = [];
          }
          if (filter.value && filter.condition === 'equals') {
            recipientFilters.locations.push(filter.value);
          }
          break;
          
        case 'tags':
          if (!recipientFilters.tags) {
            recipientFilters.tags = [];
          }
          if (filter.value && filter.condition === 'contains') {
            recipientFilters.tags.push(filter.value);
          }
          break;
          
        case 'expiry_date':
        case 'expiryDate':
          if (filter.condition === 'date_between' && Array.isArray(filter.value)) {
            recipientFilters.expiryDateRange = {
              from: new Date(filter.value[0]),
              to: new Date(filter.value[1])
            };
          }
          break;
          
        case 'created_at':
        case 'createdAt':
          if (filter.condition === 'date_between' && Array.isArray(filter.value)) {
            recipientFilters.registrationDateRange = {
              from: new Date(filter.value[0]),
              to: new Date(filter.value[1])
            };
          }
          break;
          
        default:
          // Add to custom fields
          if (!recipientFilters.customFields) {
            recipientFilters.customFields = [];
          }
          recipientFilters.customFields.push({
            fieldName: filter.field,
            operator: this.mapConditionToOperator(filter.condition),
            value: filter.value
          });
      }
    });
    
    return recipientFilters;
  }
  
  // The actual filtering is now done by the MemberDataAdapter
  // These methods are kept for compatibility but the logic is handled by the adapter

  // Helper methods for building filters
  createFilter(field: string, condition: RecipientFilter['condition'], value: any, group?: string): RecipientFilter {
    return {
      id: `filter-${Date.now()}-${Math.random()}`,
      field,
      condition,
      value,
      group,
      operator: 'AND'
    };
  }

  // Validation methods
  validateFilter(filter: RecipientFilter): boolean {
    // Check if field is valid
    const validFields = [
      'name', 'email', 'phone', 'status', 'membership_type',
      'created_at', 'last_login', 'tags', 'custom_fields'
    ];
    
    if (!validFields.some(f => filter.field.startsWith(f))) {
      return false;
    }

    // Check if condition is valid for field type
    // This would be more complex in a real implementation
    return true;
  }

  // Get available fields for filtering
  getAvailableFields(): Array<{ value: string; label: string; type: string }> {
    return [
      { value: 'name', label: 'Name', type: 'string' },
      { value: 'email', label: 'Email', type: 'string' },
      { value: 'phone', label: 'Phone', type: 'string' },
      { value: 'status', label: 'Status', type: 'select' },
      { value: 'membership_type', label: 'Membership Type', type: 'select' },
      { value: 'created_at', label: 'Created Date', type: 'date' },
      { value: 'last_login', label: 'Last Login', type: 'date' },
      { value: 'tags', label: 'Tags', type: 'array' },
    ];
  }

  // Get available conditions for a field type
  getAvailableConditions(fieldType: string): Array<{ value: RecipientFilter['condition']; label: string }> {
    const stringConditions = [
      { value: 'equals' as const, label: 'Equals' },
      { value: 'not_equals' as const, label: 'Not Equals' },
      { value: 'contains' as const, label: 'Contains' },
      { value: 'not_contains' as const, label: 'Does Not Contain' },
      { value: 'starts_with' as const, label: 'Starts With' },
      { value: 'ends_with' as const, label: 'Ends With' },
      { value: 'is_empty' as const, label: 'Is Empty' },
      { value: 'is_not_empty' as const, label: 'Is Not Empty' },
    ];

    const numberConditions = [
      { value: 'equals' as const, label: 'Equals' },
      { value: 'not_equals' as const, label: 'Not Equals' },
      { value: 'greater_than' as const, label: 'Greater Than' },
      { value: 'less_than' as const, label: 'Less Than' },
      { value: 'greater_than_or_equal' as const, label: 'Greater Than or Equal' },
      { value: 'less_than_or_equal' as const, label: 'Less Than or Equal' },
      { value: 'between' as const, label: 'Between' },
    ];

    const dateConditions = [
      { value: 'date_equals' as const, label: 'Equals' },
      { value: 'date_before' as const, label: 'Before' },
      { value: 'date_after' as const, label: 'After' },
      { value: 'date_between' as const, label: 'Between' },
    ];

    const selectConditions = [
      { value: 'equals' as const, label: 'Equals' },
      { value: 'not_equals' as const, label: 'Not Equals' },
      { value: 'in_list' as const, label: 'In List' },
      { value: 'not_in_list' as const, label: 'Not In List' },
    ];

    switch (fieldType) {
      case 'string':
        return stringConditions;
      case 'number':
        return numberConditions;
      case 'date':
        return dateConditions;
      case 'select':
      case 'array':
        return selectConditions;
      default:
        return stringConditions;
    }
  }

  /**
   * Build Supabase query from RecipientFilters
   * This integrates with the FilterQueryBuilder
   */
  buildSupabaseQuery(filters: RecipientFilters): any {
    // Convert RecipientFilters to the format expected by FilterQueryBuilder
    const convertedFilters: RecipientFilters = {
      ...filters,
      // Map custom fields if needed
      customFields: filters.customFields?.map((field: any) => ({
        fieldName: field.fieldName,
        operator: this.mapConditionToOperator(field.operator),
        value: field.value
      })) as any
    };

    // Use FilterQueryBuilder to create the query
    const supabase = createClient();
    const queryBuilder = new FilterQueryBuilder(supabase as any, 'members');
    return queryBuilder.buildQuery(convertedFilters);
  }

  /**
   * Map our condition types to FilterOperator enum
   */
  private mapConditionToOperator(condition: string): FilterOperator {
    const mapping: Record<string, FilterOperator> = {
      'equals': FilterOperator.EQUALS,
      'not_equals': FilterOperator.NOT_EQUALS,
      'contains': FilterOperator.CONTAINS,
      'not_contains': FilterOperator.NOT_CONTAINS,
      'starts_with': FilterOperator.STARTS_WITH,
      'ends_with': FilterOperator.ENDS_WITH,
      'is_empty': FilterOperator.IS_EMPTY,
      'is_not_empty': FilterOperator.IS_NOT_EMPTY,
      'greater_than': FilterOperator.GREATER_THAN,
      'less_than': FilterOperator.LESS_THAN,
      'greater_than_or_equal': FilterOperator.GREATER_THAN_OR_EQUAL,
      'less_than_or_equal': FilterOperator.LESS_THAN_OR_EQUAL,
      'in_list': FilterOperator.IN,
      'not_in_list': FilterOperator.NOT_IN,
    };
    
    return mapping[condition] || FilterOperator.EQUALS;
  }
}

// Export singleton instance
export const recipientFilterService = new RecipientFilterService();