import { EmailBlast, EmailCampaignStatus } from '@/types/email-blast/email-blast.types';
import { mockEmailCampaigns } from '@/data/mock/email-campaigns.mock';
import { emailBlastFormSchema, EmailBlastFormData } from '@/schemas/email-blast/email-blast.schema';
import { recipientListsService } from './recipient-lists.service';
import { recipientFilterService } from './recipient-filter.service';

class EmailBlastService {
  private campaigns: EmailBlast[] = [...mockEmailCampaigns];

  async getAll(): Promise<EmailBlast[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...this.campaigns];
  }

  async getById(id: string): Promise<EmailBlast | null> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const campaign = this.campaigns.find(c => c.id === id);
    return campaign || null;
  }

  async create(data: EmailBlastFormData): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Validate data
    const validatedData = emailBlastFormSchema.parse(data);
    
    // Calculate total recipients
    const totalRecipients = await this.calculateTotalRecipients([validatedData.recipientListId]);
    
    const newCampaign: EmailBlast = {
      id: `campaign-${Date.now()}`,
      name: validatedData.name,
      description: validatedData.description,
      status: EmailCampaignStatus.DRAFT,
      content: {
        subject: validatedData.subject,
        body: validatedData.content_text || '',
        bodyHtml: validatedData.content_html,
        attachments: validatedData.attachments,
        templateId: validatedData.templateId,
        variables: validatedData.variables
      },
      recipientListId: validatedData.recipientListId,
      recipientCount: totalRecipients,
      sentCount: 0,
      failedCount: 0,
      openCount: 0,
      clickCount: 0,
      tags: validatedData.tags,
      metadata: validatedData.metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current-user-id',
      updatedBy: 'current-user-id'
    };

    this.campaigns.push(newCampaign);
    return newCampaign;
  }

  async update(id: string, data: Partial<EmailBlastFormData>): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const index = this.campaigns.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error('Email campaign not found');
    }

    const campaign = this.campaigns[index];
    
    // Only allow updates to DRAFT campaigns
    if (campaign.status !== EmailCampaignStatus.DRAFT) {
      throw new Error('Can only edit draft campaigns');
    }

    // Validate partial data
    const partialSchema = emailBlastFormSchema.partial();
    const validatedData = partialSchema.parse(data);

    // Recalculate recipients if lists changed
    let recipientCount = campaign.recipientCount;
    if (validatedData.recipientListId && validatedData.recipientListId !== campaign.recipientListId) {
      recipientCount = await this.calculateTotalRecipients([validatedData.recipientListId]);
    }

    const updatedCampaign: EmailBlast = {
      ...campaign,
      name: validatedData.name || campaign.name,
      description: validatedData.description !== undefined ? validatedData.description : campaign.description,
      recipientListId: validatedData.recipientListId || campaign.recipientListId,
      recipientCount,
      updatedAt: new Date(),
      updatedBy: 'current-user-id'
    };

    // Update content if provided
    if (validatedData.subject || validatedData.content_html || validatedData.content_text) {
      updatedCampaign.content = {
        ...campaign.content,
        subject: validatedData.subject || campaign.content.subject,
        body: validatedData.content_text || campaign.content.body,
        bodyHtml: validatedData.content_html || campaign.content.bodyHtml
      };
    }

    this.campaigns[index] = updatedCampaign;
    return updatedCampaign;
  }

  async delete(id: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const index = this.campaigns.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error('Email campaign not found');
    }

    const campaign = this.campaigns[index];
    
    // Only allow deletion of DRAFT campaigns
    if (campaign.status !== EmailCampaignStatus.DRAFT) {
      throw new Error('Can only delete draft campaigns');
    }

    this.campaigns.splice(index, 1);
  }

  async scheduleCampaign(id: string, scheduledAt: string): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    if (campaign.status !== EmailCampaignStatus.DRAFT) {
      throw new Error('Can only schedule draft campaigns');
    }

    campaign.status = EmailCampaignStatus.SCHEDULED;
    campaign.scheduledAt = new Date(scheduledAt);
    campaign.updatedAt = new Date();
    campaign.updatedBy = 'current-user-id';

    return campaign;
  }

  async sendNow(id: string): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    if (campaign.status !== EmailCampaignStatus.DRAFT && campaign.status !== EmailCampaignStatus.SCHEDULED) {
      throw new Error('Campaign cannot be sent in current status');
    }

    campaign.status = EmailCampaignStatus.SENDING;
    campaign.sentAt = new Date();
    campaign.updatedAt = new Date();
    campaign.updatedBy = 'current-user-id';

    // Simulate sending process
    setTimeout(() => {
      this.simulateSendingProgress(id);
    }, 100);

    return campaign;
  }

  async pauseCampaign(id: string): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    if (campaign.status !== EmailCampaignStatus.SENDING) {
      throw new Error('Can only pause campaigns that are sending');
    }

    // Use metadata to track paused state
    campaign.metadata = {
      ...campaign.metadata,
      isPaused: true,
      pausedAt: new Date().toISOString()
    };
    campaign.updatedAt = new Date();
    campaign.updatedBy = 'current-user-id';

    return campaign;
  }

  async resumeCampaign(id: string): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    if (!campaign.metadata?.isPaused) {
      throw new Error('Can only resume paused campaigns');
    }

    // Remove paused state from metadata
    campaign.metadata = {
      ...campaign.metadata,
      isPaused: false,
      resumedAt: new Date().toISOString()
    };
    campaign.updatedAt = new Date();
    campaign.updatedBy = 'current-user-id';

    // Continue sending process
    setTimeout(() => {
      this.simulateSendingProgress(id);
    }, 100);

    return campaign;
  }

  async cancelCampaign(id: string): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    if (campaign.status !== EmailCampaignStatus.SCHEDULED) {
      throw new Error('Can only cancel scheduled campaigns');
    }

    campaign.status = EmailCampaignStatus.DRAFT;
    campaign.scheduledAt = undefined;
    campaign.updatedAt = new Date();
    campaign.updatedBy = 'current-user-id';

    return campaign;
  }

  async duplicateCampaign(id: string, newName: string): Promise<EmailBlast> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const originalCampaign = this.campaigns.find(c => c.id === id);
    if (!originalCampaign) {
      throw new Error('Email campaign not found');
    }

    const duplicatedCampaign: EmailBlast = {
      ...originalCampaign,
      id: `campaign-${Date.now()}`,
      name: newName,
      status: EmailCampaignStatus.DRAFT,
      sentCount: 0,
      failedCount: 0,
      openCount: 0,
      clickCount: 0,
      sentAt: undefined,
      scheduledAt: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current-user-id',
      updatedBy: 'current-user-id'
    };

    this.campaigns.push(duplicatedCampaign);
    return duplicatedCampaign;
  }

  async testEmail(id: string, testEmails: string[]): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    // In real implementation, this would send test emails
    console.log(`Sending test emails for campaign ${id} to:`, testEmails);
  }

  async getCampaignStats(id: string): Promise<{
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    unsubscribed: number;
    bounced: number;
    failed: number;
  }> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign) {
      throw new Error('Email campaign not found');
    }

    return {
      sent: campaign.sentCount || 0,
      delivered: campaign.sentCount || 0,
      opened: campaign.openCount || 0,
      clicked: campaign.clickCount || 0,
      unsubscribed: 0,
      bounced: 0,
      failed: campaign.failedCount || 0
    };
  }

  async getByStatus(status: EmailCampaignStatus): Promise<EmailBlast[]> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return this.campaigns.filter(c => c.status === status);
  }

  private async calculateTotalRecipients(listIds: string[]): Promise<number> {
    let total = 0;
    
    for (const listId of listIds) {
      const list = await recipientListsService.getById(listId);
      if (list && list.metadata?.isActive !== false) {
        total += list.recipientCount || 0;
      }
    }
    
    // In real implementation, this would deduplicate members across lists
    return total;
  }

  private async simulateSendingProgress(id: string): Promise<void> {
    const campaign = this.campaigns.find(c => c.id === id);
    if (!campaign || campaign.status !== EmailCampaignStatus.SENDING || campaign.metadata?.isPaused) {
      return;
    }

    // Simulate gradual sending
    const totalRecipients = campaign.recipientCount || 0;
    const increment = Math.floor(totalRecipients / 10);
    
    const interval = setInterval(() => {
      if (!campaign || campaign.status !== EmailCampaignStatus.SENDING || campaign.metadata?.isPaused) {
        clearInterval(interval);
        return;
      }

      campaign.sentCount = Math.min((campaign.sentCount || 0) + increment, totalRecipients);
      const deliveredCount = Math.floor(campaign.sentCount * 0.95);
      campaign.openCount = Math.floor(deliveredCount * 0.25);
      campaign.clickCount = Math.floor(campaign.openCount * 0.15);
      campaign.failedCount = Math.floor(campaign.sentCount * 0.02);
      
      // Store additional stats in metadata
      campaign.metadata = {
        ...campaign.metadata,
        deliveredCount,
        bouncedCount: Math.floor(campaign.sentCount * 0.03),
        openRate: deliveredCount > 0 ? (campaign.openCount / deliveredCount) * 100 : 0,
        clickRate: campaign.openCount > 0 ? (campaign.clickCount / campaign.openCount) * 100 : 0
      };

      if (campaign.sentCount >= totalRecipients) {
        campaign.status = EmailCampaignStatus.SENT;
        campaign.metadata = {
          ...campaign.metadata,
          completedAt: new Date().toISOString()
        };
        clearInterval(interval);
      }
    }, 1000);
  }
}

// Export singleton instance
export const emailBlastService = new EmailBlastService();