import { createClient } from '@/utils/supabase/Client'
import { Member, ProfileDetials } from "@/types/members/member-details"
import type { Membership } from '@/types/members/table.types'

export class MemberService {
  // ==========================================
  // fetch all member
  // ==========================================
  static async getAllMemberships(
    filters: {
      subFilters: { [key: string]: string[] };
    },
    page: number,
    limit: number,
    searchTerm?: string
  ): Promise<{
    memberships: Membership[] | null;
    success: boolean;
    total: number;
    currentPage: number;
  }> {
    const supabase = await createClient();

    try {
      let baseQuery = supabase
        .from("membership_profiles")
        .select("membership_id", { count: "exact" });

      // Add filters directly to the query
      Object.keys(filters.subFilters).forEach((filterKey) => {
        const filterValues = filters.subFilters[filterKey];
        if (Array.isArray(filterValues) && filterValues.length > 0) {
          baseQuery = baseQuery.in(filterKey, filterValues);
        }
      });

      // Apply search term filter if present
      if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim();
        baseQuery = baseQuery.or(
          `membership_number.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
        );
      }

      const { count: total, error: totalError } = await baseQuery;
      if (totalError) {
        throw new Error();
      }

      // Handle case where no memberships match
      if (!total) {
        return { memberships: [], success: true, total: 0, currentPage: 1 };
      }

      // Handle pagination
      const maxPage = Math.ceil(total / limit);
      const adjustedPage = Math.min(maxPage, Math.max(1, page));
      const start = (adjustedPage - 1) * limit;
      const end = start + limit - 1;

      // Main query to fetch memberships
      let mainQuery = supabase
        .from("membership_profiles")
        .select(
          `
              membership_id,
              membership_type_name,
              membership_status,
              membership_number,
              start_date,
              end_date,
              member_since,
              full_name
          `,
          { count: "exact" }
        )
        .range(start, end);

      // Reapply filters on the main query
      Object.keys(filters.subFilters).forEach((filterKey) => {
        const filterValues = filters.subFilters[filterKey];
        if (Array.isArray(filterValues) && filterValues.length > 0) {
          mainQuery = mainQuery.in(filterKey, filterValues);
        }
      });

      // Apply search term filter if present
      if (searchTerm) {
        const normalizedSearchTerm = searchTerm.trim();
        mainQuery = mainQuery.or(
          `membership_number.ilike.%${normalizedSearchTerm}%,full_name.ilike.%${normalizedSearchTerm}%`
        );
      }

      const { data: membershipsData, error: membershipsError } = await mainQuery;
      if (membershipsError) {
        throw new Error();
      }

      if (!membershipsData || membershipsData.length === 0) {
        return { memberships: [], success: true, total, currentPage: adjustedPage };
      }

      const enrichedMemberships = membershipsData.map((membership) => ({
        membership_id: membership.membership_id ?? "-",
        name: membership.full_name ?? "-",
        membership_type_name: membership.membership_type_name ?? "-",
        membership_status: membership.membership_status ?? "-",
        membership_number: membership.membership_number ?? "-",
        start_date: membership.start_date ? new Date(membership.start_date).toLocaleDateString() : "-",
        end_date: membership.end_date ? new Date(membership.end_date).toLocaleDateString() : "-",
        member_since: membership.member_since ? new Date(membership.member_since).toLocaleDateString() : "-",
      }));

      return {
        memberships: enrichedMemberships,
        success: true,
        total,
        currentPage: adjustedPage,
      };
    } catch {
      return { memberships: null, success: false, total: 0, currentPage: page };
    }
  }

  // ==========================================
  // Core Member Retrieval
  // ==========================================
  static async getMemberById(id: string): Promise<Member | null> {
    const supabase = await createClient()

    try {
      const { data, error } = await supabase
        .from("memberships")
        .select("*, membership_types(name, group), applications(*)")
        .eq("id", id)
        .single()

      if (error || !data) throw error

      const { applications, membership_types, ...membership } = data

      const profileDetails = await this.getProfileDetails(data.profile_id)
      if (!profileDetails) {
        return null
      }

      // Fetch user account information if user_id exists
      let accountInfo = undefined
      if (data.user_id) {
        const { data: userMetaData } = await supabase
          .from("user_meta")
          .select("status, last_sign_in_at, email")
          .eq("id", data.user_id)
          .single()

        accountInfo = {
          hasAccount: true,
          status: userMetaData?.status ?? undefined,
          lastSignInAt: userMetaData?.last_sign_in_at ?? undefined,
          email: userMetaData?.email ?? undefined
        }
      } else {
        accountInfo = {
          hasAccount: false
        }
      }

      // Transform the database data into the Member type
      const member: Member = {
        ...profileDetails,
        applications,
        memberships: {
          ...membership,
          membershipType: {
            ...membership_types
          }
        },
        accountInfo
      }

      return member
    } catch {
      return null
    }
  }

  static async getProfileDetails(id: string): Promise<ProfileDetials | null> {
    const supabase = await createClient()

    try {
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select(`*, addresses (*), sys_industries (*)`)
        .eq('id', id)
        .single();

      if (profileError) throw profileError

      const { data, error } = await supabase
        .from("profiles")
        .select(`
            educations (
              *,
              institute:institutes!education_institute_id_fkey (*),
              institute_course: institute_courses!education_icourse_id_fkey (*)
            ),
            working_experiences (
              *,
              company:companies (
                *,
                address:addresses!company_address_id_fkey (*),
                preferred_address:addresses!companies_preferred_address_id_fkey (*)
              )
            ),
            employments (
              *,
              company:companies (
                *,
                address:addresses!company_address_id_fkey (*),
                preferred_address:addresses!companies_preferred_address_id_fkey (*)
              )
            ),
            nominees (
              *,
              educations!educations_nominee_id_fkey (
                *,
                institute:institutes!education_institute_id_fkey (*),
                institute_course: institute_courses!education_icourse_id_fkey (*)
              ),
              awards!awards_nominee_id_fkey (*)
            ),
            projects (
              *,
              company:companies (
                *,
                address:addresses!company_address_id_fkey (*),
                preferred_address:addresses!companies_preferred_address_id_fkey (*)
              )
            ),
            awards (*),
            sponsorships (*),
            documents (*),
            criminal_records (*)
        `)
        .eq('id', id)
        .single()

      if (error) throw error

      const {
        addresses,
        sys_industries,
        ...restProfile
      } = profileData;

      // Transform the database data into the Member type
      const profile: ProfileDetials = {
        profileId: profileData.id,
        personalInfo: {
          ...restProfile,
          address: {
            line1: addresses?.street_line_1,
            line2: addresses?.street_line_2,
            line3: addresses?.street_line_3,
            city: addresses?.city,
            state: addresses?.state,
            postalCode: addresses?.postal_code,
            country: addresses?.country_code
          },
          industries: {
            name: sys_industries?.name,
            group: sys_industries?.group,
          }
        },
        employment: data.employments?.[0] ? (({ company, ...rest }) => ({
          ...rest,
          company: {
            name: company.name,
            phone: company.phone,
            email: company.email,
            isSameAddress: company?.is_same_address ?? false,
            address: {
              line1: company?.address?.street_line_1,
              line2: company?.address?.street_line_2,
              city: company?.address?.city,
              state: company?.address?.state,
              postalCode: company?.address?.postal_code,
              country: company?.address?.country_code
            },
            preferredAddress: {
              line1: company?.preferred_address?.street_line_1,
              line2: company?.preferred_address?.street_line_2,
              city: company?.preferred_address?.city,
              state: company?.preferred_address?.state,
              postalCode: company?.preferred_address?.postal_code,
              country: company?.preferred_address?.country_code
            }
          }
        }))(data.employments?.[0]) : null,
        education: data.educations?.map((edu: any) => ({
          ...edu,
          institute: {
            name: edu.institute?.name,
            country: edu.institute?.country,
          },
          instituteCourse: {
            name: edu.institute_course?.name,
            category: edu.institute_course?.category,
          }
        })) || [],
        workExperience: data.working_experiences?.map((emp: any) => ({
          ...emp,
          company: {
            name: emp.company?.name,
          },
          address: {
            line1: emp.company?.address?.street_line_1,
            line2: emp.company?.address?.street_line_2,
            city: emp.company?.address?.city,
            state: emp.company?.address?.state,
            postalCode: emp.company?.address?.postal_code,
            country: emp.company?.address?.country_code,
          }
        })) || [],
        nominees: data.nominees?.map((nominee: any) => {
          const { educations, awards, ...rest } = nominee;
          return {
            ...rest,
            education: educations?.[0] ? {
              ...educations[0],
              institute: {
                name: educations[0].institute?.name,
                country: educations[0].institute?.country,
              },
              instituteCourse: {
                name: educations[0].institute_course?.name,
                category: educations[0].institute_course?.category,
              }
            } : undefined,
            award: awards?.[0] || undefined,
          };
        }) || [],
        projects: data.projects?.map((project: any) => ({
          ...project,
          company: {
            name: project.company?.name
          },
          address: {
            line1: project.company?.address?.street_line_1,
            line2: project.company?.address?.street_line_2,
            city: project.company?.address?.city,
            state: project.company?.address?.state,
            postalCode: project.company?.address?.postal_code,
            country: project.company?.address?.country_code,
          }
        })) || [],
        criminalRecords: data.criminal_records?.[0] || null,
        awards: data.awards || [],
        sponsorship: data.sponsorships || [],
        documents: data.documents?.map((doc: any) => ({
          id: doc.id,
          name: doc.path.split('/').pop(),
          type: doc.type,
          uploadDate: doc.upload_date,
          size: '--',
          path: doc.path
        })) || []
      }


      return profile
    } catch {
      return null
    }
  }
} 