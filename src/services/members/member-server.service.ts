"use server"

import { createClient } from "@/utils/supabase/server";
import type { Address, CompanyDetails, sponsorshipDetails } from "@/types/members/member-details";
import type { WorkExperienceFormData } from "@/components/member-details/work-experience/work-experience-form";
import type { EducationFormData } from "@/components/member-details/education-details/education-form";
import type { AwardFormData } from "@/components/member-details/awards/award-form";
import type { ProjectExperienceFormData } from "@/components/member-details/projects/project-form";
import type { CriminalRecordFormData } from "@/components/member-details/criminal-records/criminal-record-form";
import type { SponsorshipFormData } from "@/components/member-details/sponsorship/sponsorship-form";
import type { Database } from "@/types/database.types";
import { CompanyNomineeFormData } from "@/components/member-details/company-nominees/nominee-form";
import { EmploymentsFormData } from "@/components/member-details/employment/employment-form";
import { PersonalInformationFormData } from "@/components/member-details/personal-information/personal-form";
import { ContactDetailsFormData } from "@/components/member-details/contact-details/contact-form";
import { CompanySchemaFormData } from "@/components/member-details/company-information/company-form";

// ==========================================
// edit Functions
// ==========================================
export async function updatePersonalInfo(
    profileId: string,
    formData: PersonalInformationFormData
): Promise<{
    success: boolean;
    error?: any;
}> {
    const supabase = await createClient()

    try {
        const { error } = await supabase
            .from('profiles')
            .update({
                full_name: formData.fullName,
                date_of_birth: formData.dob,
                place_of_birth: formData.placeOfBirth,
                title: formData.title,
                identification_type: formData.idType,
                identification_no: formData.idNumber,
                nationality: formData.nationality,
                gender: formData.gender,
                is_aces_member: formData.acesMember,
                spoken_languages: formData.languageSpoken,
                written_languages: formData.languageWritten,
                industry_id: formData.industrySector
            })
            .eq('id', profileId)

        if (error) throw error

        return { success: true }
    } catch {
        return { success: false, error: 'Failed to update personal info' }
    }
}

export async function updateContactDetails(
    profileId: string,
    formData: ContactDetailsFormData
): Promise<{
    success: boolean;
    error?: any;
    addressId?: string;
}> {
    const supabase = await createClient()
    let addressId: string = formData.address_id || ""

    try {
        if (addressId) {
            await updateAddress(supabase, addressId, formData.address)
        } else {
            addressId = await createAddress(supabase, formData.address)
        }

        const { error } = await supabase
            .from('profiles')
            .update({
                address_id: addressId,
                home_number: formData.phone,
                mobile_number: formData.mobile,
                email: formData.email,
            })
            .eq('id', profileId)

        if (error) throw error

        return { success: true, addressId }
    } catch {
        return { success: false, error: 'Failed to update contact details' }
    }
}

export async function updateEmploymentDetails(
    profileId: string,
    formData: EmploymentsFormData
): Promise<{
    success: boolean;
    employmentId?: { id: string; company_id: string };
    error?: any;
}> {
    const supabase = await createClient()
    const employmentId: { id: string; company_id: string } = {
        id: formData.id || "",
        company_id: formData.company_id || ""
    }

    try {
        if (formData.id) {
            const { error } = await supabase
                .from('employments')
                .update({
                    profile_id: profileId,
                    designation: formData.designation,
                    start_date: formData.startDate
                })

            if (error) throw error
        } else {
            const { data, error } = await supabase
                .from('employments')
                .insert({
                    profile_id: profileId,
                    designation: formData.designation,
                    start_date: formData.startDate
                })
                .select('id')
                .single()

            if (error || !data) throw error

            employmentId.id = data.id
        }

        if (employmentId.company_id) {
            await handleExistingCompanyUpdate(supabase, employmentId, formData);
        } else {
            employmentId.company_id = await handleNewCompanyCreation(supabase, formData);

            if (employmentId.company_id && employmentId.id) {
                const { error } = await supabase
                    .from('employments')
                    .update({
                        company_id: employmentId.company_id,
                    })
                    .eq('id', employmentId.id)

                if (error) throw error
            }
        }

        return { success: true, employmentId }
    } catch {
        return {
            success: false,
            error: 'Failed to update employment details'
        }
    }
}

export async function updateCompanyInfo(
    profileId: string,
    formData: CompanySchemaFormData
): Promise<{
    success: boolean;
    error?: any;
    addressId?: string;
}> {
    const supabase = await createClient()
    let addressId: string = formData.address_id || ""

    try {
        if (addressId) {
            await updateAddress(supabase, addressId, formData.address)
        } else {
            addressId = await createAddress(supabase, formData.address)
        }

        const { error } = await supabase
            .from('profiles')
            .update({
                full_name: formData.fullName,
                mobile_number: formData.mobile,
                email: formData.email,
                // Place Of Registration
                nationality: formData.nationality,
                // Date Of Registration
                date_of_birth: formData.dob,
                // Registration No
                identification_no: formData.nricPassport
            })
            .eq('id', profileId)

        if (error) throw error

        return { success: true, addressId }
    } catch {
        return { success: false, error: 'Failed to update company info' }
    }
}

export async function updateWorkExperienceDetails(
    profileId: string,
    formData: WorkExperienceFormData
): Promise<{
    success: boolean;
    newWorkExperienceId?: string;
    newCompanyId?: string;
    error?: any;
}> {
    const supabase = await createClient()
    let workData: { id: string; company_id: string } = {
        id: "",
        company_id: ""
    }

    try {
        // insert or update working experience first
        if (formData.id) {
            const { data: workExperienceData, error: workExperienceFetchError } = await supabase
                .from('working_experiences')
                .select('id, company_id')
                .eq('id', formData.id)
                .single();

            if (workExperienceFetchError) {
                // Check if it's a PGRST116 error and skip throwing it
                if (workExperienceFetchError.code !== 'PGRST116') {
                    throw workExperienceFetchError;
                }
            }

            if (workExperienceData) {
                workData = workExperienceData;
            }

            const { error } = await supabase
                .from('working_experiences')
                .update({
                    designation: formData.designation,
                    duties: formData.duties,
                    period_from: formData.periodFrom,
                    period_to: formData.periodTo,

                })
                .eq('id', formData.id)

            if (error) throw error
        } else {
            const { data, error } = await supabase
                .from('working_experiences')
                .insert({
                    profile_id: profileId,
                    designation: formData.designation,
                    duties: formData.duties,
                    period_from: formData.periodFrom,
                    period_to: formData.periodTo,
                })
                .select('id')
                .single()

            if (error || !data) {
                throw error
            } else {
                workData.id = data.id
            }
        }

        if (workData?.company_id) {
            await handleExistingCompanyUpdate(supabase, workData, formData);
        } else {
            workData.company_id = await handleNewCompanyCreation(supabase, formData);

            if (workData.company_id && workData.id) {
                const { error } = await supabase
                    .from('working_experiences')
                    .update({
                        company_id: workData.company_id,

                    })
                    .eq('id', workData.id)

                if (error) throw error
            }
        }

        const newWorkExperienceId: string = workData?.id;
        const newCompanyId: string = workData?.company_id;

        return {
            success: true,
            newWorkExperienceId,
            newCompanyId
        };
    } catch {
        return {
            success: false,
            error: 'Failed to update work experience'
        };
    }
}

export async function updateEducation(
    profileId: string,
    formData: EducationFormData
): Promise<{
    success: boolean;
    error?: any;
    newEduId?: string
}> {
    const supabase = await createClient()

    try {
        let newEduId: string

        if (formData.id) {
            const { error } = await supabase
                .from('educations')
                .update({
                    institute_id: formData.learningInstitute === 'other' ? null : formData.learningInstitute,
                    other_institute: formData.learningInstitute === 'other' ? formData.learningInstituteOthers : null,
                    icourse_id: formData.courseOfStudy === 'other' ? null : formData.courseOfStudy,
                    other_course: formData.courseOfStudy === 'other' ? formData.courseOfStudyOthers : null,
                    period_from: formData.periodFrom,
                    period_to: formData.periodTo,
                    date_of_graduation: formData.dateOfGraduation,
                    ...(formData.nomineeId ? { nominee_id: formData.nomineeId } : {})
                })
                .eq('id', formData.id)

            if (error) throw error
            newEduId = formData.id
        } else {
            const { data, error } = await supabase
                .from('educations')
                .insert({
                    profile_id: profileId,
                    institute_id: formData.learningInstitute === 'other' ? null : formData.learningInstitute,
                    other_institute: formData.learningInstitute === 'other' ? formData.learningInstituteOthers : null,
                    icourse_id: formData.courseOfStudy === 'other' ? null : formData.courseOfStudy,
                    other_course: formData.courseOfStudy === 'other' ? formData.courseOfStudyOthers : null,
                    period_from: formData.periodFrom,
                    period_to: formData.periodTo,
                    date_of_graduation: formData.dateOfGraduation,
                    ...(formData.nomineeId ? { nominee_id: formData.nomineeId } : {})
                })
                .select('id')
                .single()

            if (error) throw error
            newEduId = data.id
        }

        return { success: true, newEduId };
    } catch {
        return {
            success: false,
            error: 'Failed to update education'
        };
    }
}

export async function updateAward(
    profileId: string,
    formData: AwardFormData
): Promise<{
    success: boolean;
    error?: any;
    newAwardId?: string
}> {
    const supabase = await createClient()

    try {
        let newAwardId: string

        if (formData.id) {
            const { error } = await supabase
                .from('awards')
                .update({
                    record_type: formData.recordType,
                    record_number: formData.recordType === 'AFFILIATION' ? formData.recordNumber : null,
                    date_of_election: formData.dateOfElection,
                    detail: formData.details,
                    abbreviation: formData.abbreviation,
                    expiry_date: formData.expiryDate,
                    ...(formData.nomineeId ? { nominee_id: formData.nomineeId } : {})
                })
                .eq('id', formData.id)

            if (error) throw error
            newAwardId = formData.id
        } else {
            const { data, error } = await supabase
                .from('awards')
                .insert({
                    profile_id: profileId,
                    record_type: formData.recordType,
                    record_number: formData.recordType === 'AFFILIATION' ? formData.recordNumber : null,
                    date_of_election: formData.dateOfElection,
                    detail: formData.details,
                    abbreviation: formData.abbreviation,
                    expiry_date: formData.expiryDate,
                    ...(formData.nomineeId ? { nominee_id: formData.nomineeId } : {})
                })
                .select('id')
                .single()

            if (error) throw error
            newAwardId = data.id
        }

        return { success: true, newAwardId }
    } catch {
        return {
            success: false,
            error: 'Failed to update award'
        }
    }
}

export async function updateCriminalRecords(
    profileId: string,
    formData: CriminalRecordFormData
): Promise<{
    success: boolean;
    error?: any;
    newCriminalRecordId?: string
}> {
    const supabase = await createClient()

    try {
        let newCriminalRecordId: string

        if (formData.id) {
            const { error } = await supabase
                .from('criminal_records')
                .update({
                    declaration: formData.declaration,
                    details: formData.declaration === 'yes' ? formData.details : null,
                })
                .eq('id', formData.id)

            if (error) throw error
            newCriminalRecordId = formData.id
        } else {
            const { data, error } = await supabase
                .from('criminal_records')
                .insert({
                    profile_id: profileId,
                    declaration: formData.declaration,
                    details: formData.declaration === 'yes' ? formData.details : null,
                })
                .select('id')
                .single()

            if (error) throw error
            newCriminalRecordId = data.id
        }

        return { success: true, newCriminalRecordId }
    } catch {
        return {
            success: false,
            error: 'Failed to update criminal record'
        }
    }
}

export async function updateProjectDetails(
    profileId: string,
    formData: ProjectExperienceFormData
): Promise<{
    success: boolean;
    newProjectId?: string;
    newCompanyId?: string;
    error?: any;
}> {
    const supabase = await createClient();
    let projectData: { id: string; company_id: string } = {
        id: "",
        company_id: ""
    };

    try {
        if (formData.id) {
            const { data: projectRecord, error: projectFetchError } = await supabase
                .from('projects')
                .select('id, company_id')
                .eq('id', formData.id)
                .single();

            if (projectFetchError && projectFetchError.code !== 'PGRST116') {
                throw projectFetchError;
            }

            if (projectRecord) {
                projectData = projectRecord;
            }

            const { error } = await supabase
                .from('projects')
                .update({
                    involvement_from: formData.involvementFrom,
                    involvement_to: formData.onGoingExperience ? null : formData.involvementTo,
                    title: formData.title,
                    reference_no: formData.referenceNo,
                    position_held: formData.positionHeld,
                    details: formData.details,
                    duration: formData.duration,
                    work_type: formData.employmentType,
                    spe_name: formData.speName,
                    spe_number: formData.speNumber,
                    total_year: formData.totalYear,
                    total_month: formData.totalMonth,
                    employment_start_date: formData.employment_start_date,
                    is_ongoing: formData.onGoingExperience,
                    cost_of_project: formData.costOfProject
                })
                .eq('id', formData.id);

            if (error) throw error;
        } else {
            const { data, error } = await supabase
                .from('projects')
                .insert({
                    profile_id: profileId,
                    involvement_from: formData.involvementFrom,
                    involvement_to: formData.onGoingExperience ? null : formData.involvementTo,
                    title: formData.title,
                    reference_no: formData.referenceNo,
                    position_held: formData.positionHeld,
                    details: formData.details,
                    duration: formData.duration,
                    work_type: formData.employmentType,
                    spe_name: formData.speName,
                    spe_number: formData.speNumber,
                    total_year: formData.totalYear,
                    total_month: formData.totalMonth,
                    employment_start_date: formData.employment_start_date,
                    is_ongoing: formData.onGoingExperience,
                    cost_of_project: formData.costOfProject
                })
                .select('id')
                .single();

            if (error || !data) {
                throw error;
            }

            projectData.id = data.id;
        }

        if (projectData?.company_id) {
            await handleExistingCompanyUpdate(supabase, projectData, formData);
        } else {
            projectData.company_id = await handleNewCompanyCreation(supabase, formData);

            if (projectData.company_id && projectData.id) {
                const { error } = await supabase
                    .from('projects')
                    .update({
                        company_id: projectData.company_id
                    })
                    .eq('id', projectData.id);

                if (error) throw error;
            }
        }

        return {
            success: true,
            newProjectId: projectData.id,
            newCompanyId: projectData.company_id
        };
    } catch {
        return {
            success: false,
            error: 'Failed to update project details'
        };
    }
}

export async function updateSponsorshipDetails(
    profileId: string,
    formData: SponsorshipFormData
): Promise<{
    success: boolean;
    error?: any;
    sponsorshipData?: sponsorshipDetails[]
}> {
    try {
        // Process each sponsorship separately
        const results = await Promise.all([
            // Handle proposer
            handleSponsorshipRecord({
                id: formData.proposerId || undefined,
                profile_id: profileId,
                name: formData.proposerName,
                grade: formData.proposerGrade,
                membership_id: formData.proposerMembershipID,
                date: formData.proposerDate?.toISOString().slice(0, 10),
                type: "PROPOSER" as const
            }),

            // Handle seconder
            handleSponsorshipRecord({
                id: formData.seconderId || undefined,
                profile_id: profileId,
                name: formData.seconderName,
                grade: formData.seconderGrade,
                membership_id: formData.seconderMembershipID,
                date: formData.seconderDate?.toISOString().slice(0, 10),
                type: "SECONDER" as const
            })
        ]);

        return { success: true, sponsorshipData: results };
    } catch {
        return { success: false };
    }
}

export async function updateNomineeDetails(
    profileId: string,
    formData: CompanyNomineeFormData
): Promise<{
    success: boolean;
    error?: any;
    nomineeId?: { id: string; eduId: string; awardId: string }
}> {
    const supabase = await createClient()
    const nomineeId: { id: string; eduId: string; awardId: string } = {
        id: formData.id || "",
        eduId: formData.education_id || "",
        awardId: formData.award_id || ""
    }

    try {
        // Process nominee first
        if (formData.id) {
            const { error } = await supabase
                .from('nominees')
                .update({
                    full_name: formData.fullName,
                    title: formData.title,
                    phone_number: formData.telephone,
                    email: formData.email,
                    date_of_birth: formData.dob,
                    has_awards: formData.has_awards
                })
                .eq('id', formData.id)

            if (error) throw error
        } else {
            const { data, error } = await supabase
                .from('nominees')
                .insert({
                    profile_id: profileId,
                    full_name: formData.fullName,
                    title: formData.title,
                    phone_number: formData.telephone,
                    email: formData.email,
                    date_of_birth: formData.dob,
                    has_awards: formData.has_awards
                })
                .select('id')
                .single()

            if (error || !data) throw error

            nomineeId.id = data.id
        }

        // then process with education
        const educationData: EducationFormData = {
            id: formData.education_id,
            learningInstitute: formData.learningInstitute,
            courseOfStudy: formData.courseOfStudy,
            periodFrom: formData.periodFrom,
            periodTo: formData.periodTo,
            learningInstituteOthers: formData.learningInstituteOthers,
            courseOfStudyOthers: formData.courseOfStudyOthers,
            dateOfGraduation: formData.dateOfGraduation,
            nomineeId: nomineeId.id
        }

        const result = await updateEducation(profileId, educationData)

        if (result.success && result.newEduId) {
            nomineeId.eduId = result.newEduId
        } else {
            throw new Error('failed to update the nominee education')
        }

        // process if has awards is true
        if (formData.has_awards) {
            const awardData: AwardFormData = {
                id: formData.award_id,
                recordType: formData.recordType,
                recordNumber: formData.recordNumber,
                dateOfElection: formData.dateOfElection,
                details: formData.details,
                abbreviation: formData.abbreviation,
                expiryDate: formData.expiryDate,
                nomineeId: nomineeId.id
            }

            const result = await updateAward(profileId, awardData)

            if (result.success && result.newAwardId) {
                nomineeId.awardId = result.newAwardId
            } else {
                throw new Error('failed to update the nominee award')
            }
        }

        return { success: true, nomineeId }
    } catch (error) {
        console.log("Submit sponsorships failed:", error)
        return { success: false, error }
    }
}

// ==========================================
// Helper Functions
// ==========================================
async function handleSponsorshipRecord(record: Database['public']['Tables']['sponsorships']['Insert']) {
    const supabase = await createClient();

    if (record.id) {
        // Update existing record
        const { data, error } = await supabase
            .from("sponsorships")
            .update({
                profile_id: record.profile_id,
                name: record.name,
                grade: record.grade,
                membership_id: record.membership_id,
                date: record.date,
                type: record.type
            })
            .eq('id', record.id)
            .select('*')
            .single();

        if (error) throw error;
        return data;
    } else {
        // Insert new record
        const { data, error } = await supabase
            .from("sponsorships")
            .insert({
                profile_id: record.profile_id,
                name: record.name,
                grade: record.grade,
                membership_id: record.membership_id,
                date: record.date,
                type: record.type
            })
            .select('*')
            .single();

        if (error) throw error;
        return data;
    }
}

// ==========================================
// Helper Functions for Address and Company Management
// ==========================================
async function createAddress(supabase: any, addressData: Address, type: 'COMPANY' | 'PERSONAL' = 'COMPANY') {
    const { data: address, error } = await supabase
        .from('addresses')
        .insert({
            street_line_1: addressData.line1,
            street_line_2: addressData.line2,
            street_line_3: addressData.line3,
            city: addressData.city,
            state: addressData.state,
            postal_code: addressData.postalCode,
            country_code: addressData.country,
            type
        })
        .select()
        .single()

    if (error) throw error
    return address
}

async function updateAddress(supabase: any, addressId: string, addressData: Address) {
    const { error } = await supabase
        .from('addresses')
        .update({
            street_line_1: addressData.line1,
            street_line_2: addressData.line2,
            street_line_3: addressData.line3,
            city: addressData.city,
            state: addressData.state,
            postal_code: addressData.postalCode,
            country_code: addressData.country
        })
        .eq('id', addressId)

    if (error) throw error
}

async function createCompany(supabase: any, formData: CompanyDetails) {
    const { data: company, error } = await supabase
        .from('companies')
        .insert({
            name: formData.companyName,
            phone: formData.telephone,
            email: formData.email,
            is_same_address: formData.isSameAddress
        })
        .select()
        .single()

    if (error) throw error
    return company
}

async function updateCompany(supabase: any, companyId: string, formData: CompanyDetails) {
    const { error } = await supabase
        .from('companies')
        .update({
            name: formData.companyName,
            phone: formData.telephone,
            email: formData.email,
            is_same_address: formData.isSameAddress
        })
        .eq('id', companyId)

    if (error) throw error
}

async function updateCompanyAddresses(supabase: any, companyId: string, addressId: string, preferredAddressId: string | null) {
    const { error } = await supabase
        .from('companies')
        .update({
            address_id: addressId,
            preferred_address_id: preferredAddressId
        })
        .eq('id', companyId)

    if (error) throw error
}

async function handleNewCompanyCreation(supabase: any, formData: CompanyDetails): Promise<string> {
    // Create new company
    const newCompany = await createCompany(supabase, formData)

    // Create company address
    const companyAddress = await createAddress(supabase, formData.address)

    // Create preferred address if different
    let preferredAddressId = null
    if (!formData.isSameAddress && formData.preferredAddress) {
        const preferredAddress = await createAddress(supabase, formData.preferredAddress)
        preferredAddressId = preferredAddress.id
    }

    // Update company with address IDs
    await updateCompanyAddresses(supabase, newCompany.id, companyAddress.id, preferredAddressId)

    return newCompany.id
}

async function handleExistingCompanyUpdate(
    supabase: any,
    data: { id: string; company_id: string },
    formData: CompanyDetails
): Promise<boolean> {
    // Get company details
    const { data: company, error: companyFetchError } = await supabase
        .from('companies')
        .select('address_id, preferred_address_id')
        .eq('id', data.company_id)
        .single()
    if (companyFetchError) throw companyFetchError

    // Update company details
    await updateCompany(supabase, data.company_id, formData)

    // Update company address
    let addressId = company.address_id
    if (company.address_id) {
        await updateAddress(supabase, company.address_id, formData.address)
    } else {
        const address = await createAddress(supabase, formData.address)
        addressId = address.id
        await updateCompanyAddresses(supabase, data.company_id, addressId, company.preferred_address_id ?? null)
    }

    // Handle preferred address
    if (!formData.isSameAddress && formData.preferredAddress) {
        if (company.preferred_address_id) {
            // Update existing preferred address
            await updateAddress(supabase, company.preferred_address_id, formData.preferredAddress)
        } else {
            // Create new preferred address
            const preferredAddress = await createAddress(supabase, formData.preferredAddress)
            await updateCompanyAddresses(
                supabase,
                data.company_id,
                addressId,
                preferredAddress.id
            )
        }
    } else if (company.preferred_address_id !== company.address_id) {
        // Update company to remove preferred address
        await updateCompanyAddresses(
            supabase,
            data.company_id,
            company.address_id,
            null
        )
    }

    return true;
}

// ==========================================
// delete Functions
// ==========================================
export async function deleteWorkExperience(workExperienceId: string) {
    const supabase = await createClient()

    try {
        const { error } = await supabase
            .from('working_experiences')
            .delete()
            .eq('id', workExperienceId)

        if (error) throw error

        return { success: true }
    } catch {
        return { success: false, error: 'Failed to delete work experience' }
    }
}

export async function deleteProject(projectId: string) {
    const supabase = await createClient()

    try {
        const { error } = await supabase
            .from('projects')
            .delete()
            .eq('id', projectId)

        if (error) throw error

        return { success: true }
    } catch {
        return { success: false, error: 'Failed to delete project' }
    }
}

export async function deleteEducation(educationId: string) {
    const supabase = await createClient()

    try {
        const { error } = await supabase
            .from('educations')
            .delete()
            .eq('id', educationId)

        if (error) throw error

        return { success: true }
    } catch {
        return { success: false, error: 'Failed to delete education' }
    }
}

export async function deleteAward(awardId: string) {
    const supabase = await createClient()

    try {
        const { error } = await supabase
            .from('awards')
            .delete()
            .eq('id', awardId)

        if (error) throw error

        return { success: true }
    } catch {
        return { success: false, error: 'Failed to delete award' }
    }
}

export async function deleteNominee(nomineeId: string) {
    const supabase = await createClient();

    try {
        const { error: educationError } = await supabase
            .from('educations')
            .delete()
            .eq('nominee_id', nomineeId);

        if (educationError) throw educationError;

        const { error: awardsError } = await supabase
            .from('awards')
            .delete()
            .eq('nominee_id', nomineeId);

        if (awardsError) throw awardsError;

        const { error: nomineeError } = await supabase
            .from('nominees')
            .delete()
            .eq('id', nomineeId);

        if (nomineeError) throw nomineeError;

        return { success: true };
    } catch {
        return { success: false, error: 'Failed to delete nominee' };
    }
}
