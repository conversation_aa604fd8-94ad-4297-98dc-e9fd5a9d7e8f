import { MODULES } from '@/lib/rbac-utils'

// Mapping of sidebar menu items to their required RBAC modules
export const SIDEBAR_MODULE_MAPPING = {
  // Dashboard - No permission required (public)
  '/dashboard': null,
  
  // CRM Section
  '/applications': MODULES.APPLICATION,
  '/batch-processing': MODULES.APPLICATION,
  '/applications/reports': MODULES.APPLICATION,
  '/members': MODULES.MEMBERSHIP,
  '/email-blast': MODULES.EMAIL_BLAST,
  '/email-blast/lists': MODULES.EMAIL_BLAST,
  '/email-blast/templates': MODULES.EMAIL_BLAST,
  '/email-blast/reports': MODULES.EMAIL_BLAST,
  
  // Programme Section
  '/programmes': MODULES.PROGRAMMES,
  '/programmes/archived': MODULES.PROGRAMMES,
  
  // Finance/Controlling Section
  '/budgets': MODULES.BUDGET,
  '/budgets/archived': MODULES.BUDGET,
  '/approvals': MODULES.APPROVALS,
  
  // Procurement Section
  '/purchase-orders': MODULES.PURCHASE_ORDERS,
  '/purchase-invoices': MODULES.PURCHASE_INVOICES,
  
  // Sales & Accounts Receivable Section
  '/customers': MODULES.CUSTOMER_MANAGEMENT,
  '/invoices': MODULES.SALES_INVOICES,
  '/invoices/reports': MODULES.SALES_INVOICES,
  '/receipts': MODULES.CUSTOMER_RECEIPTS,
  '/receipts/reports': MODULES.CUSTOMER_RECEIPTS,
  '/credit-notes': MODULES.CUSTOMER_CREDIT_NOTES,
  
  // System Section
  '/user-management': MODULES.USER_MANAGEMENT,
  '/user-management/users': MODULES.USER_MANAGEMENT,
  '/user-management/roles': MODULES.USER_MANAGEMENT,
  '/user-management/audit': MODULES.USER_MANAGEMENT,
  '/settings': MODULES.SETTINGS,
  
  // Reports Section
  '/reports': MODULES.PROGRAMMES, // Attendance reports are related to programmes
  '/reports/attendance': MODULES.PROGRAMMES,
} as const

// Group-level permissions - if any item in a group is accessible, show the group
export const SIDEBAR_GROUP_MODULES = {
  'CRM': [MODULES.APPLICATION, MODULES.MEMBERSHIP, MODULES.EMAIL_BLAST],
  'Programme': [MODULES.PROGRAMMES],
  'Finance/Controlling': [MODULES.BUDGET, MODULES.APPROVALS],
  'Procurement': [MODULES.PURCHASE_ORDERS, MODULES.PURCHASE_INVOICES],
  'Sales & Accounts Receivable': [
    MODULES.CUSTOMER_MANAGEMENT,
    MODULES.SALES_INVOICES,
    MODULES.CUSTOMER_RECEIPTS,
    MODULES.CUSTOMER_CREDIT_NOTES
  ],
  'System': [MODULES.USER_MANAGEMENT, MODULES.SETTINGS],
} as const