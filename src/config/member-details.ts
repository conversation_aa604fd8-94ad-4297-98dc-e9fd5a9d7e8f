import { EditPermissionConfig, TabConfig } from "@/types/members/member-details"

export const DEFAULT_TAB_CONFIG: TabConfig = {
  summary: true,
  profile: true,
  company: true,
  workExperience: true,
  educationDetails: true,
  companyNominees: true,
  awards: true,
  projectDetails: true,
  documents: true,
  points: false,
  applicationHistory: true,
  auditLogs: false,
  activities: false,
  criminalRecords: true,
  sponsorship: true,
}

export const DEFAULT_EDIT_CONFIG: EditPermissionConfig = {
  personalInformation: true,
  companyInformation: true,
  contactDetails: true,
  employmentDetails: true,
  workExperience: true,
  educationDetails: true,
  companyNominees: true,
  awards: true,
  projectDetails: true,
  documents: true,
  criminalRecords: true,
  sponsorship: true,
}

export const MEMBER_TABS = [
  {
    id: "summary",
    label: "Summary",
  },
  {
    id: "profile",
    label: "Profile",
  },
  {
    id: "company",
    label: "Company Profile",
  },
  {
    id: "workExperience",
    label: "Work Experience",
  },
  {
    id: "educationDetails",
    label: "Education Details",
  },
  {
    id: "companyNominees",
    label: "Company Nominees",
  },
  {
    id: "awards",
    label: "Awards & Distinctions",
  },
  {
    id: "projectDetails",
    label: "Project Details",
  },
  {
    id: "criminalRecords",
    label: "Criminal Records",
  },
  {
    id: "sponsorship",
    label: "Sponsorship",
  },
  {
    id: "documents",
    label: "Documents",
  },
  {
    id: "points",
    label: "Points",
  },
  {
    id: "applicationHistory",
    label: "Application History",
  },
  {
    id: "auditLogs",
    label: "Audit Logs",
  },
  {
    id: "activities",
    label: "Activities",
  },
] as const