"use client"

import { useEffect, useState } from "react"
import { z } from "zod"
import { useFieldArray, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import type { invoiceDetails } from "@/types/finance/invoices.types"
import { toast } from "@/hooks/use-toast"
import { deleteInvoiceDetails, updateInvoiceDetails } from "@/services/finance/invoices-server.service"
import { generateInvoicePdf } from "@/services/finance/invoices.service"
import { downloadReceiptPdf, generateReceiptPdf } from "@/services/finance/receipts.service"

interface useInvoicesEditModalProps {
    invoice: invoiceDetails
    taxRate: number
    fetchInvoice: () => Promise<void>
}

const invoiceItemSchema = z.object({
    id: z.string().optional(),
    gl_account_code: z.string().optional(),
    description: z.string().min(1, 'Description is required'),
    price_type: z.string().optional(),
    quantity: z.coerce.number().min(1, 'Quantity is required'),
    unit_price: z.coerce.number().min(1, 'At least 1 unit price is required'),
    tax_rate: z.coerce.number().optional(),
    tax_amount: z.coerce.number().optional(),
    line_total: z.coerce.number().optional(),
});

const receiptSchema = z.object({
    id: z.string().optional(),
    receiptNo: z.string().optional(),
    dateOfPayment: z.date().nullable(),
    amountPaid: z.coerce.number().optional(),
    paymentMode: z.string().optional(),
    referenceNo: z.string().optional(),
    canEdit: z.boolean(),
}).superRefine((data, ctx) => {
    if (data.canEdit) {
        if (!data.dateOfPayment) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Date Of Payment is required",
                path: ["dateOfPayment"],
            });
        }

        if (!data.amountPaid) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Amount Paid is required",
                path: ["amountPaid"],
            });
        }

        if (!data.paymentMode) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Payment Mode is required",
                path: ["paymentMode"],
            });
        }
    }

});

const editInvoiceSchema = z.object({
    id: z.string().optional(),
    entities: z.string({ required_error: 'Entity is required' }),
    attentionTo: z.string().optional(),
    billTo: z.string().optional(),
    title: z.string().optional(),
    issueDate: z.date({ required_error: 'Issue Date is required' }),
    paymentTerms: z.string().optional(),
    dueDate: z.date({ required_error: 'Due Date is required' }),
    status: z.enum(['DRAFT', 'UNPAID', 'SENT', 'PARTIALLY_PAID', 'PAID', 'VOID', 'OVERDUE']).optional(),
    subtotal: z.number(),
    totalTax: z.number(),
    grandTotal: z.number(),
    notes: z.string().optional(),
    invoice_items: z.array(invoiceItemSchema).min(1),
    receipts: z.array(receiptSchema)
});

export type EditInvoiceFormData = z.infer<typeof editInvoiceSchema>

export function useInvoicesEditModal({ invoice, taxRate, fetchInvoice }: useInvoicesEditModalProps) {
    const form = useForm<EditInvoiceFormData>({
        resolver: zodResolver(editInvoiceSchema),
        mode: 'onChange',
        defaultValues: {
            id: invoice.id || "",
            entities: invoice.entity_id || "",
            attentionTo: (invoice.metadata as any)?.attn ?? "",
            billTo: invoice.bill_to ?? "",
            title: (invoice.metadata as any)?.title ?? "",
            issueDate: invoice.issue_date ? new Date(invoice.issue_date) : new Date(),
            paymentTerms: invoice.payment_terms ?? "",
            dueDate: invoice.due_date ? new Date(invoice.due_date) : new Date(),
            status: invoice.status ?? "DRAFT",
            subtotal: invoice.subtotal || undefined,
            totalTax: invoice.tax_amount || undefined,
            grandTotal: invoice.total_amount || undefined,
            notes: invoice.notes ?? "",
            invoice_items: invoice.invoice_items.map(item => ({
                id: item.id || "",
                gl_account_code: item.gl_account_code ?? "",
                description: item.description ?? undefined,
                price_type: (item.metadata as any)?.price_type ?? "",
                quantity: item.quantity ?? 0,
                unit_price: item.unit_price ?? 0,
                tax_rate: item.tax_rate ?? taxRate,
                tax_amount: item.tax_amount ?? 0,
                line_total: item.line_total ?? 0,
            })) ?? [],
            receipts: invoice.receipts.map(item => ({
                id: item.id || "",
                receiptNo: item.receipt_number || "",
                dateOfPayment: item.payment_date ? new Date(item.payment_date) : undefined,
                amountPaid: item.amount_paid || 0,
                paymentMode: item.payment_method || "",
                referenceNo: item.payment_reference || "",
                canEdit: false,
            })) ?? []
        },
    })
    const {
        fields: invoiceItemFields,
        append: appendInvoiceItem,
        remove: removeInvoiceItem,
    } = useFieldArray({
        control: form.control,
        name: 'invoice_items',
        keyName: 'field_id'
    })
    const {
        fields: receiptFields,
        append: appendReceipt,
        remove: removeReceipt,
    } = useFieldArray({
        control: form.control,
        name: 'receipts',
        keyName: 'field_id'
    })
    const [itemsToDelete, setItemsToDelete] = useState<string[]>([]);
    const [receiptsToDelete, setReceiptsToDelete] = useState<string[]>([]);
    const [isPdfLoading, setIsPdfLoading] = useState<Record<string, boolean>>({});

    const addinvoiceItems = () => {
        appendInvoiceItem({
            gl_account_code: "",
            description: "",
            price_type: "",
            quantity: 0,
            unit_price: 0,
            tax_rate: taxRate,
            tax_amount: undefined,
            line_total: undefined,
        })
    }

    const addReceipt = () => {
        appendReceipt({
            receiptNo: "",
            dateOfPayment: null,
            amountPaid: 0,
            paymentMode: "",
            referenceNo: "",
            canEdit: true,
        });
    }

    const calculateAllPricing = (index?: number) => {
        const items = form.getValues("invoice_items");

        if (typeof index === "number") {
            // Calculate line total and tax amount for the specific index
            const taxRate = Number(invoiceItemFields[index]?.tax_rate) || 0;
            const quantity = Number(form.getValues(`invoice_items.${index}.quantity`)) || 0;
            const unitPrice = Number(form.getValues(`invoice_items.${index}.unit_price`)) || 0;

            const lineTotal = quantity * unitPrice;
            const taxAmount = lineTotal * (taxRate / 100);

            form.setValue(`invoice_items.${index}.line_total`, lineTotal);
            form.setValue(`invoice_items.${index}.tax_amount`, taxAmount);
        } else {
            // Recalculate all line totals and tax amounts for all items
            items.forEach((item, idx) => {
                const taxRate = Number(invoiceItemFields[idx]?.tax_rate) || 0;
                const quantity = Number(item.quantity) || 0;
                const unitPrice = Number(item.unit_price) || 0;

                const lineTotal = quantity * unitPrice;
                const taxAmount = lineTotal * (taxRate / 100);

                form.setValue(`invoice_items.${idx}.line_total`, lineTotal);
                form.setValue(`invoice_items.${idx}.tax_amount`, taxAmount);
            });
        }

        // Recalculate totals after updating line items
        const updatedItems = form.getValues("invoice_items");

        const subtotal = updatedItems.reduce((acc, item) => acc + (Number(item.line_total) || 0), 0);
        const totalTax = updatedItems.reduce((acc, item) => acc + (Number(item.tax_amount) || 0), 0);
        const grandTotal = subtotal + totalTax;

        form.setValue("subtotal", subtotal);
        form.setValue("totalTax", totalTax);
        form.setValue("grandTotal", grandTotal);
    }

    useEffect(() => {
        calculateAllPricing()
    }, [form.watch('invoice_items')])

    const downloadPdf = async (fieldId: string, id?: string) => {
        if (!id) return;
        setIsPdfLoading(prev => ({ ...prev, [fieldId]: true }));
        try {
            const success = await downloadReceiptPdf(id);
            if (!success) {
                toast({
                    title: "Error",
                    description: "Failed to download the receipt PDF. Please try again.",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error("Error downloading PDF:", error);
            toast({
                title: "Error",
                description: "Failed to download the receipt PDF. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsPdfLoading(prev => ({ ...prev, [fieldId]: false }));
        }
    }

    const onSubmit = async () => {
        const isValid = await form.trigger()

        if (!isValid) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Please fill in all required fields",
            });
            return;
        }

        if (itemsToDelete.length || receiptsToDelete.length) {
            const deleteResult = await deleteInvoiceDetails(itemsToDelete, receiptsToDelete);
            if (!deleteResult.success) {
                toast({
                    title: "Error",
                    description: "Failed to delete some invoice items or receipts. Invoice update process has been stopped to prevent inconsistent data",
                    variant: "destructive",
                });
                return;
            }
        }

        const formData = await form.getValues()
        const result = await updateInvoiceDetails(formData)

        if (!result.success) {
            toast({
                title: "Error",
                description: "Failed to update the invoice",
                variant: "destructive",
            });
            return;
        }

        const invoicePdfResult = await generateInvoicePdf(invoice.id);
        if (invoicePdfResult) {
            toast({
                title: "Success",
                description: "Invoice updated successfully.",
            });
        } else {
            toast({
                title: "Error",
                description: "The invoice was updated successfully, but failed to generate one or more PDFs.",
                variant: "destructive",
            });
        }

        await fetchInvoice();
    }

    return {
        form,
        invoiceItemFields,
        addinvoiceItems,
        removeInvoiceItem,
        receiptFields,
        addReceipt,
        removeReceipt,
        setItemsToDelete,
        setReceiptsToDelete,
        isPdfLoading,
        calculateAllPricing,
        onSubmit,
        downloadPdf,

    }
}
