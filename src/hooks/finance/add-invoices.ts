"use client"

import { useRouter } from "next/navigation"
import { z } from "zod"
import { useFieldArray, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { toast } from "@/hooks/use-toast"
import { createInvoiceDetails } from "@/services/finance/invoices-server.service"
import { useEffect, useState } from "react"

interface useInvoicesAddModalProps {
    taxRate: number
}

const invoiceItemSchema = z.object({
    id: z.string().optional(),
    gl_account_code: z.string().optional(),
    description: z.string().min(1, 'Description is required'),
    price_type: z.string().optional(),
    quantity: z.coerce.number().min(1, 'Quantity is required'),
    unit_price: z.coerce.number().min(1, 'At least 1 unit price is required'),
    tax_rate: z.coerce.number().optional(),
    tax_amount: z.coerce.number().optional(),
    line_total: z.coerce.number().optional(),
});

const addInvoiceSchema = z.object({
    entities: z.string({ required_error: 'Entity is required' }),
    attentionTo: z.string().optional(),
    billTo: z.string().optional(),
    title: z.string().optional(),
    issueDate: z.date({ required_error: 'Issue Date is required' }),
    paymentTerms: z.string().optional(),
    dueDate: z.date({ required_error: 'Due Date is required' }),
    subtotal: z.number(),
    totalTax: z.number(),
    grandTotal: z.number(),
    notes: z.string().optional(),
    invoice_items: z.array(invoiceItemSchema).min(1)
});

export type addInvoiceFormData = z.infer<typeof addInvoiceSchema>

export function useInvoicesAddModal({ taxRate }: useInvoicesAddModalProps) {
    const form = useForm<addInvoiceFormData>({
        resolver: zodResolver(addInvoiceSchema),
        mode: 'onChange',
        defaultValues: {
            entities: undefined,
            attentionTo: "",
            billTo: "",
            title: "",
            issueDate: undefined,
            paymentTerms: "",
            dueDate: undefined,
            subtotal: undefined,
            totalTax: undefined,
            grandTotal: undefined,
            notes: "",
            invoice_items: [{
                gl_account_code: "",
                description: "",
                price_type: "",
                quantity: 0,
                unit_price: 0,
                tax_rate: taxRate,
                tax_amount: undefined,
                line_total: undefined,
            }]
        },
    })
    const {
        fields: invoiceItemFields,
        append: appendInvoiceItem,
        remove: removeInvoiceItem,
    } = useFieldArray({
        control: form.control,
        name: 'invoice_items',
    })
    const router = useRouter()
    const [isSubmitting, setIsSubmitting] = useState<boolean>()

    const addinvoiceItems = () => {
        appendInvoiceItem({
            gl_account_code: "",
            description: "",
            price_type: "",
            quantity: 0,
            unit_price: 0,
            tax_rate: taxRate,
            tax_amount: undefined,
            line_total: undefined,
        })
    }

    const calculateAllPricing = (index?: number) => {
        const items = form.getValues("invoice_items");

        if (typeof index === "number") {
            // Calculate line total and tax amount for the specific index
            const taxRate = Number(invoiceItemFields[index]?.tax_rate) || 0;
            const quantity = Number(form.getValues(`invoice_items.${index}.quantity`)) || 0;
            const unitPrice = Number(form.getValues(`invoice_items.${index}.unit_price`)) || 0;

            const lineTotal = quantity * unitPrice;
            const taxAmount = lineTotal * (taxRate / 100);

            form.setValue(`invoice_items.${index}.line_total`, lineTotal);
            form.setValue(`invoice_items.${index}.tax_amount`, taxAmount);
        } else {
            // Recalculate all line totals and tax amounts for all items
            items.forEach((item, idx) => {
                const taxRate = Number(invoiceItemFields[idx]?.tax_rate) || 0;
                const quantity = Number(item.quantity) || 0;
                const unitPrice = Number(item.unit_price) || 0;

                const lineTotal = quantity * unitPrice;
                const taxAmount = lineTotal * (taxRate / 100);

                form.setValue(`invoice_items.${idx}.line_total`, lineTotal);
                form.setValue(`invoice_items.${idx}.tax_amount`, taxAmount);
            });
        }

        // Recalculate totals after updating line items
        const updatedItems = form.getValues("invoice_items");

        const subtotal = updatedItems.reduce((acc, item) => acc + (Number(item.line_total) || 0), 0);
        const totalTax = updatedItems.reduce((acc, item) => acc + (Number(item.tax_amount) || 0), 0);
        const grandTotal = subtotal + totalTax;

        form.setValue("subtotal", subtotal);
        form.setValue("totalTax", totalTax);
        form.setValue("grandTotal", grandTotal);
    };

    useEffect(() => {
        calculateAllPricing()
    }, [form.watch('invoice_items')]);

    const onSubmit = async () => {
        const isValid = await form.trigger()

        if (!isValid) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Please fill in all required fields",
            });
            return;
        }

        setIsSubmitting(true)

        const formData = await form.getValues()
        const result = await createInvoiceDetails(formData)

        if (result.success) {
            toast({
                title: "Success",
                description: "Successfully create the invoice",
            });
            router.push(`/invoices/edit?id=${result.newId}`);
        } else {
            toast({
                title: "Error",
                description: "Failed to update the invoice",
                variant: "destructive",
            });
        }

        setIsSubmitting(false)
    }

    return {
        form,
        invoiceItemFields,
        addinvoiceItems,
        removeInvoiceItem,
        calculateAllPricing,
        onSubmit,
        isSubmitting,
    }
}
