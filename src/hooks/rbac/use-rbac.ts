'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/utils/supabase/Client'
import { User } from '@supabase/supabase-js'

interface RBACPermissions {
  [module: string]: {
    [action: string]: boolean
  }
}

interface AccessibleMembershipType {
  id: string
  name: string
  abbr: string
  group: string
}

export const useRBAC = () => {
  const [permissions, setPermissions] = useState<RBACPermissions | null>(null)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<User | null>(null)
  const [error, setError] = useState<string | null>(null)

  const loadUserPermissions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      
      const supabase = createClient()
      
      // Get current user
      const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !currentUser) {
        console.log('User authentication failed:', userError)
        setUser(null)
        setPermissions(null)
        return
      }
      
      setUser(currentUser)
      console.log('User authenticated:', currentUser.email, currentUser.id)
      
      // Get user permissions
      console.log('Calling get_user_permissions RPC...')
      const { data: permissionsData, error: permissionsError } = await supabase
        .rpc('get_user_permissions')
      
      console.log('RPC Response - Data:', permissionsData, 'Error:', permissionsError)
      
      if (permissionsError) {
        console.error('Error loading permissions:', permissionsError)
        setError(`Failed to load user permissions: ${permissionsError.message}`)
        return
      }
      
      console.log('Setting permissions data:', permissionsData)
      setPermissions(permissionsData || {})
      
    } catch (err) {
      console.error('Error in loadUserPermissions:', err)
      setError(`An unexpected error occurred: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }, [])

  const hasPermission = useCallback(async (
    module: string, 
    action: string, 
    membershipTypeId?: string
  ): Promise<boolean> => {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase.rpc('check_user_permission', {
        module_name: module,
        action: action,
        membership_type_id: membershipTypeId || null
      })
      
      if (error) {
        console.error('Permission check error:', error)
        return false
      }
      
      return Boolean(data)
    } catch (err) {
      console.error('Error checking permission:', err)
      return false
    }
  }, [])

  const getAccessibleMemberships = useCallback(async (): Promise<AccessibleMembershipType[]> => {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase.rpc('get_accessible_membership_types_simple')
      
      if (error) {
        console.error('Error getting accessible memberships:', error)
        return []
      }
      
      return data || []
    } catch (err) {
      console.error('Error in getAccessibleMemberships:', err)
      return []
    }
  }, [])

  const getAccessibleApplications = useCallback(async () => {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase.rpc('get_accessible_applications')
      
      if (error) {
        console.error('Error getting accessible applications:', error)
        return []
      }
      
      return data || []
    } catch (err) {
      console.error('Error in getAccessibleApplications:', err)
      return []
    }
  }, [])

  const getUserAccessibleMembershipTypes = useCallback(async (module: string): Promise<string[]> => {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase
        .rpc('get_user_accessible_membership_types', {
          p_user_id: user?.id,
          p_module_name: module
        })
      
      if (error) {
        console.error('Error getting accessible membership types:', error)
        return []
      }
      
      return data || []
    } catch (err) {
      console.error('Error in getUserAccessibleMembershipTypes:', err)
      return []
    }
  }, [user?.id])

  // Check if user has access to specific membership type
  const hasAccessToMembershipType = useCallback((membershipTypeId: string, module: string = 'MEMBERSHIP'): Promise<boolean> => {
    return hasPermission(module, 'READ', membershipTypeId)
  }, [hasPermission])

  // Get cached permission for quick UI updates
  const getCachedPermission = useCallback((module: string, action: string): boolean => {
    if (!permissions) return false
    
    // Handle permissions as array format (from get_user_permissions RPC)
    if (Array.isArray(permissions)) {
      const modulePermissions = permissions.find(p => p.module_name === module)
      if (!modulePermissions) {
        console.log(`No permissions found for module: ${module}`)
        return false
      }
      
      // Map action names to permission fields
      const actionMap: { [key: string]: string } = {
        'CREATE': 'can_create',
        'READ': 'can_read',
        'UPDATE': 'can_update',
        'DELETE': 'can_delete',
        'REVIEW': 'can_review',
        'APPROVE': 'can_approve',
        'EXPORT': 'can_export'
      }
      
      const permissionField = actionMap[action.toUpperCase()]
      const hasPermission = permissionField ? Boolean(modulePermissions[permissionField]) : false
      console.log(`Permission check: ${module}:${action} = ${hasPermission}`)
      return hasPermission
    }
    
    // Fallback for object format (if structure changes)
    return permissions[module]?.[action] || false
  }, [permissions])

  useEffect(() => {
    loadUserPermissions()
  }, [loadUserPermissions])

  return {
    // State
    permissions,
    loading,
    user,
    error,
    
    // Permission checking
    hasPermission,
    getCachedPermission,
    hasAccessToMembershipType,
    
    // Data access
    getAccessibleMemberships,
    getAccessibleApplications,
    getUserAccessibleMembershipTypes,
    
    // Utilities
    refreshPermissions: loadUserPermissions,
    isAuthenticated: !!user,
    isAdmin: user?.user_metadata?.user_type === 'ADMIN'
  }
}