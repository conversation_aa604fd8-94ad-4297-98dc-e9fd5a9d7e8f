"use client"

import { useEffect, useState } from "react"
import type { User } from "@supabase/supabase-js"
import { AuthService } from "@/services/auth.service"

export function useUser() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const initUser = async () => {
      try {
        const currentUser = await AuthService.getCurrentUser()
        setUser(currentUser)
        setLoading(false)

        const subscription = await AuthService.onAuthStateChange((user) => {
          setUser(user)
          setLoading(false)
        })

        return () => {
          subscription.unsubscribe()
        }
      } catch (error) {
        console.error('Error initializing user:', error)
        setLoading(false)
      }
    }

    initUser()
  }, [])

  return { user, loading }
} 