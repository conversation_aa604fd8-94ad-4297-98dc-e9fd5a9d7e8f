'use client'

import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  bulkCheckInParticipants 
} from '@/services/client/attendance-client.service';
import {
  serverBulkCheckInParticipants
} from '@/services/server/attendance-server.service';
import {
  BulkCheckInRequest,
  AttendanceRecord,
  ParticipantWithAttendance
} from '@/types/attendance/attendance.types';
import { toast } from '@/hooks/use-toast';

interface BulkCheckInResult {
  successful: AttendanceRecord[];
  failed: { participant_id: string; error: string }[];
  total: number;
  successCount: number;
  failCount: number;
}

interface UseBulkCheckInOptions {
  programmeId: string;
  onSuccess?: (result: BulkCheckInResult) => void;
  onError?: (error: Error) => void;
  useServerAction?: boolean;
}

export function useBulkCheckIn(options: UseBulkCheckInOptions) {
  const { programmeId, onSuccess, onError, useServerAction = true } = options;
  const queryClient = useQueryClient();
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]);
  const [checkInLocation, setCheckInLocation] = useState<string>('');
  const [checkInNotes, setCheckInNotes] = useState<string>('');

  // ==================== BULK CHECK-IN MUTATION ====================

  const bulkCheckInMutation = useMutation({
    mutationFn: async (request: BulkCheckInRequest) => {
      if (useServerAction) {
        // Use server action for better validation and security
        return await serverBulkCheckInParticipants(request, 'current-user-id'); // TODO: Get actual user ID
      } else {
        // Use client service for simpler operations
        const result = await bulkCheckInParticipants(request);
        // Transform to match server response format
        if (result.success && result.data) {
          return {
            success: true,
            data: {
              successful: result.data,
              failed: []
            }
          };
        }
        return result;
      }
    },
    onSuccess: (response) => {
      if (response.success && response.data) {
        let result: BulkCheckInResult;
        
        // Handle both array response (client service) and object response (server action)
        if (Array.isArray(response.data)) {
          // Client service returns array directly
          result = {
            successful: response.data,
            failed: [],
            total: response.data.length,
            successCount: response.data.length,
            failCount: 0
          };
        } else {
          // Server action returns object with successful and failed arrays
          const data = response.data as { successful: AttendanceRecord[], failed: { participant_id: string, error: string }[] };
          result = {
            successful: data.successful,
            failed: data.failed,
            total: data.successful.length + data.failed.length,
            successCount: data.successful.length,
            failCount: data.failed.length
          };
        }

        // Show detailed results
        if (result.successCount > 0 && result.failCount === 0) {
          toast({
            title: "Success",
            description: `Successfully checked in ${result.successCount} participant${result.successCount > 1 ? 's' : ''}`,
          });
        } else if (result.successCount > 0 && result.failCount > 0) {
          toast({
            title: "Partial Success",
            description: `${result.successCount} successful, ${result.failCount} failed. Check details below.`,
            variant: "default"
          });
        } else {
          toast({
            title: "Failed",
            description: `All ${result.total} check-in attempts failed`,
            variant: "destructive"
          });
        }

        // Clear selections after successful operation
        if (result.successCount > 0) {
          setSelectedParticipants([]);
          setCheckInNotes('');
        }

        // Invalidate attendance queries
        queryClient.invalidateQueries({ 
          queryKey: ['participants-attendance', programmeId] 
        });
        queryClient.invalidateQueries({ 
          queryKey: ['attendance-summary', programmeId] 
        });
        queryClient.invalidateQueries({ 
          queryKey: ['attendance-records', programmeId] 
        });

        // Call success callback
        onSuccess?.(result);
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to perform bulk check-in",
          variant: "destructive"
        });
      }
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      onError?.(error instanceof Error ? error : new Error(errorMessage));
    }
  });

  // ==================== HELPER FUNCTIONS ====================

  const performBulkCheckIn = useCallback(async (
    participantIds: string[],
    scheduleId?: string,
    customLocation?: string,
    customNotes?: string
  ) => {
    if (participantIds.length === 0) {
      toast({
        title: "Warning",
        description: "Please select at least one participant",
        variant: "destructive"
      });
      return;
    }

    const request: BulkCheckInRequest = {
      participant_ids: participantIds,
      programme_id: programmeId,
      schedule_id: scheduleId,
      check_in_method: 'manual',
      check_in_location: customLocation || checkInLocation || undefined,
      notes: customNotes || checkInNotes || undefined
    };

    return bulkCheckInMutation.mutateAsync(request);
  }, [bulkCheckInMutation, programmeId, checkInLocation, checkInNotes]);

  const performBulkCheckInSelected = useCallback(async (scheduleId?: string) => {
    return performBulkCheckIn(selectedParticipants, scheduleId);
  }, [performBulkCheckIn, selectedParticipants]);

  // ==================== SELECTION MANAGEMENT ====================

  const toggleParticipantSelection = useCallback((participantId: string) => {
    setSelectedParticipants(prev => 
      prev.includes(participantId)
        ? prev.filter(id => id !== participantId)
        : [...prev, participantId]
    );
  }, []);

  const selectAllParticipants = useCallback((participants: ParticipantWithAttendance[]) => {
    // Only select participants who can be checked in
    const eligibleIds = participants
      .filter(p => p.can_check_in)
      .map(p => p.id);
    setSelectedParticipants(eligibleIds);
  }, []);

  const selectNoneParticipants = useCallback(() => {
    setSelectedParticipants([]);
  }, []);

  const selectByStatus = useCallback((
    participants: ParticipantWithAttendance[], 
    status: 'absent' | 'can_check_in' | 'can_check_out'
  ) => {
    let eligibleIds: string[] = [];

    switch (status) {
      case 'absent':
        eligibleIds = participants
          .filter(p => p.current_attendance_status === 'absent')
          .map(p => p.id);
        break;
      case 'can_check_in':
        eligibleIds = participants
          .filter(p => p.can_check_in)
          .map(p => p.id);
        break;
      case 'can_check_out':
        eligibleIds = participants
          .filter(p => p.can_check_out)
          .map(p => p.id);
        break;
    }

    setSelectedParticipants(eligibleIds);
  }, []);

  // ==================== VALIDATION HELPERS ====================

  const getSelectionSummary = useCallback((participants: ParticipantWithAttendance[]) => {
    const selectedData = participants.filter(p => selectedParticipants.includes(p.id));
    
    return {
      total: selectedParticipants.length,
      canCheckIn: selectedData.filter(p => p.can_check_in).length,
      alreadyCheckedIn: selectedData.filter(p => !p.can_check_in).length,
      participants: selectedData
    };
  }, [selectedParticipants]);

  const validateSelection = useCallback((participants: ParticipantWithAttendance[]) => {
    const summary = getSelectionSummary(participants);
    
    if (summary.total === 0) {
      return { isValid: false, message: "No participants selected" };
    }
    
    if (summary.canCheckIn === 0) {
      return { isValid: false, message: "None of the selected participants can be checked in" };
    }
    
    if (summary.alreadyCheckedIn > 0) {
      return { 
        isValid: true, 
        message: `${summary.alreadyCheckedIn} participants are already checked in and will be skipped`,
        warning: true
      };
    }
    
    return { isValid: true, message: `Ready to check in ${summary.canCheckIn} participants` };
  }, [getSelectionSummary]);

  // ==================== BULK ACTIONS ====================

  const checkInAllAbsent = useCallback(async (
    participants: ParticipantWithAttendance[],
    scheduleId?: string
  ) => {
    const absentIds = participants
      .filter(p => p.current_attendance_status === 'absent' && p.can_check_in)
      .map(p => p.id);
    
    if (absentIds.length === 0) {
      toast({
        title: "Info",
        description: "No absent participants found to check in",
      });
      return;
    }

    return performBulkCheckIn(absentIds, scheduleId);
  }, [performBulkCheckIn]);

  const checkInAllEligible = useCallback(async (
    participants: ParticipantWithAttendance[],
    scheduleId?: string
  ) => {
    const eligibleIds = participants
      .filter(p => p.can_check_in)
      .map(p => p.id);
    
    if (eligibleIds.length === 0) {
      toast({
        title: "Info",
        description: "No eligible participants found to check in",
      });
      return;
    }

    return performBulkCheckIn(eligibleIds, scheduleId);
  }, [performBulkCheckIn]);

  // ==================== RETURN VALUES ====================

  return {
    // State
    selectedParticipants,
    checkInLocation,
    checkInNotes,
    
    // State setters
    setSelectedParticipants,
    setCheckInLocation,
    setCheckInNotes,
    
    // Selection management
    toggleParticipantSelection,
    selectAllParticipants,
    selectNoneParticipants,
    selectByStatus,
    
    // Actions
    performBulkCheckIn,
    performBulkCheckInSelected,
    checkInAllAbsent,
    checkInAllEligible,
    
    // Validation
    getSelectionSummary,
    validateSelection,
    
    // Mutation state
    isLoading: bulkCheckInMutation.isPending,
    error: bulkCheckInMutation.error,
    lastResult: bulkCheckInMutation.data,
    
    // Mutation actions
    reset: bulkCheckInMutation.reset
  };
}

// ==================== CONVENIENCE HOOKS ====================

/**
 * Simple hook for bulk check-in without selection management
 */
export function useSimpleBulkCheckIn(programmeId: string) {
  const mutation = useMutation({
    mutationFn: async (request: BulkCheckInRequest) => {
      return await serverBulkCheckInParticipants(request, 'current-user-id'); // TODO: Get actual user ID
    },
    onSuccess: (response) => {
      if (response.success) {
        toast({
          title: "Success",
          description: `Bulk check-in completed: ${response.data.successful.length} successful, ${response.data.failed.length} failed`,
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Bulk check-in failed",
        variant: "destructive"
      });
    }
  });

  const checkInParticipants = useCallback(async (
    participantIds: string[],
    scheduleId?: string,
    location?: string,
    notes?: string
  ) => {
    const request: BulkCheckInRequest = {
      participant_ids: participantIds,
      programme_id: programmeId,
      schedule_id: scheduleId,
      check_in_method: 'manual',
      check_in_location: location,
      notes
    };

    return mutation.mutateAsync(request);
  }, [mutation, programmeId]);

  return {
    checkInParticipants,
    isLoading: mutation.isPending,
    error: mutation.error,
    reset: mutation.reset
  };
}