'use client'

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/Client';
import {
  getParticipantsWithAttendance,
  getAttendanceRecords,
  getAttendanceSummary,
  checkInParticipant,
  checkOutParticipant,
  exportAttendanceData
} from '@/services/client/attendance-client.service';
import {
  serverCheckInParticipant,
  serverCheckOutParticipant,
  updateAttendanceStatus
} from '@/services/server/attendance-server.service';
import {
  ParticipantWithAttendance,
  AttendanceRecord,
  AttendanceStatusSummary,
  CheckInRequest,
  CheckOutRequest,
  AttendanceExportOptions,
  AttendanceUpdatePayload
} from '@/types/attendance/attendance.types';
import { toast } from '@/hooks/use-toast';

interface UseAttendanceOptions {
  programmeId: string;
  enableRealtime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useAttendance(options: UseAttendanceOptions) {
  const { programmeId, enableRealtime = true, autoRefresh = false, refreshInterval = 30000 } = options;
  const queryClient = useQueryClient();
  const [isSubscribed, setIsSubscribed] = useState(false);

  // ==================== QUERIES ====================

  // Get participants with attendance data
  const {
    data: participantsData,
    isLoading: participantsLoading,
    error: participantsError,
    refetch: refetchParticipants
  } = useQuery({
    queryKey: ['participants-attendance', programmeId],
    queryFn: async () => {
      const result = await getParticipantsWithAttendance(
        programmeId,
        { subFilters: {} },
        1,
        1000 // Get all participants for attendance tracking
      );
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch participants');
      }
      return result;
    },
    enabled: !!programmeId,
    refetchInterval: autoRefresh ? refreshInterval : false
  });

  // Get attendance summary
  const {
    data: summaryData,
    isLoading: summaryLoading,
    error: summaryError,
    refetch: refetchSummary
  } = useQuery({
    queryKey: ['attendance-summary', programmeId],
    queryFn: async () => {
      const result = await getAttendanceSummary(programmeId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch attendance summary');
      }
      return result.data;
    },
    enabled: !!programmeId,
    refetchInterval: autoRefresh ? refreshInterval : false
  });

  // Get attendance records
  const {
    data: recordsData,
    isLoading: recordsLoading,
    error: recordsError,
    refetch: refetchRecords
  } = useQuery({
    queryKey: ['attendance-records', programmeId],
    queryFn: async () => {
      const result = await getAttendanceRecords(programmeId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch attendance records');
      }
      return result;
    },
    enabled: !!programmeId,
    refetchInterval: autoRefresh ? refreshInterval : false
  });

  // ==================== MUTATIONS ====================

  // Check-in mutation
  const checkInMutation = useMutation({
    mutationFn: async ({ request, useServer = true }: { request: CheckInRequest; useServer?: boolean }) => {
      if (useServer) {
        // Use server action for authentication and validation
        return await serverCheckInParticipant(request, 'current-user-id'); // TODO: Get actual user ID
      } else {
        return await checkInParticipant(request);
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Participant checked in successfully",
        });
        // Invalidate and refetch related queries
        invalidateAttendanceQueries();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to check in participant",
          variant: "destructive"
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  });

  // Check-out mutation
  const checkOutMutation = useMutation({
    mutationFn: async ({ request, useServer = true }: { request: CheckOutRequest; useServer?: boolean }) => {
      if (useServer) {
        return await serverCheckOutParticipant(request, 'current-user-id'); // TODO: Get actual user ID
      } else {
        return await checkOutParticipant(request);
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Participant checked out successfully",
        });
        invalidateAttendanceQueries();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to check out participant",
          variant: "destructive"
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  });

  // Update attendance status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ 
      attendanceRecordId, 
      status, 
      notes 
    }: { 
      attendanceRecordId: string; 
      status: 'present' | 'absent' | 'late' | 'excused' | 'partial';
      notes?: string;
    }) => {
      return await updateAttendanceStatus(attendanceRecordId, status, notes);
    },
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Attendance status updated successfully",
        });
        invalidateAttendanceQueries();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update attendance status",
          variant: "destructive"
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  });

  // Export mutation
  const exportMutation = useMutation({
    mutationFn: async (options: AttendanceExportOptions) => {
      const result = await exportAttendanceData(options);
      if (!result.success) {
        throw new Error(result.error || 'Failed to export attendance data');
      }
      return result.data;
    },
    onSuccess: (blob) => {
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `attendance-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Attendance report exported successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to export attendance report",
        variant: "destructive"
      });
    }
  });

  // ==================== HELPER FUNCTIONS ====================

  const invalidateAttendanceQueries = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['participants-attendance', programmeId] });
    queryClient.invalidateQueries({ queryKey: ['attendance-summary', programmeId] });
    queryClient.invalidateQueries({ queryKey: ['attendance-records', programmeId] });
  }, [queryClient, programmeId]);

  const refreshAll = useCallback(() => {
    refetchParticipants();
    refetchSummary();
    refetchRecords();
  }, [refetchParticipants, refetchSummary, refetchRecords]);

  // ==================== REALTIME SUBSCRIPTIONS ====================

  useEffect(() => {
    if (!enableRealtime || !programmeId || isSubscribed) return;

    const supabase = createClient();
    
    const channel = supabase
      .channel(`attendance-${programmeId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'programme',
        table: 'attendance_records',
        filter: `programme_id=eq.${programmeId}`
      }, (payload: any) => {
        console.log('Attendance record update:', payload);
        // Invalidate queries to refetch data
        invalidateAttendanceQueries();
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'programme',
        table: 'session_attendance',
        filter: `programme_id=eq.${programmeId}`
      }, (payload: any) => {
        console.log('Session attendance update:', payload);
        invalidateAttendanceQueries();
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'programme',
        table: 'attendance_summary',
        filter: `programme_id=eq.${programmeId}`
      }, (payload: any) => {
        console.log('Attendance summary update:', payload);
        invalidateAttendanceQueries();
      })
      .subscribe();

    setIsSubscribed(true);

    return () => {
      supabase.removeChannel(channel);
      setIsSubscribed(false);
    };
  }, [enableRealtime, programmeId, isSubscribed, invalidateAttendanceQueries]);

  // ==================== CONVENIENCE FUNCTIONS ====================

  const quickCheckIn = useCallback(async (participantId: string, scheduleId?: string, notes?: string) => {
    const request: CheckInRequest = {
      participant_id: participantId,
      programme_id: programmeId,
      schedule_id: scheduleId,
      check_in_method: 'manual',
      notes
    };

    return checkInMutation.mutateAsync({ request });
  }, [checkInMutation, programmeId]);

  const quickCheckOut = useCallback(async (attendanceRecordId: string, notes?: string) => {
    const request: CheckOutRequest = {
      attendance_record_id: attendanceRecordId,
      notes
    };

    return checkOutMutation.mutateAsync({ request });
  }, [checkOutMutation]);

  const getParticipantStatus = useCallback((participantId: string) => {
    const participant = participantsData?.data.find(p => p.id === participantId);
    if (!participant) return null;

    return {
      current_status: participant.current_attendance_status,
      can_check_in: participant.can_check_in,
      can_check_out: participant.can_check_out,
      attendance_percentage: participant.attendance_percentage,
      certificate_eligible: participant.certificate_eligible,
      last_check_in: participant.last_check_in_time
    };
  }, [participantsData]);

  // ==================== RETURN VALUES ====================

  return {
    // Data
    participants: participantsData?.data || [],
    attendanceRecords: recordsData?.data || [],
    attendanceSummary: summaryData,
    
    // Loading states
    isLoading: participantsLoading || summaryLoading || recordsLoading,
    participantsLoading,
    summaryLoading,
    recordsLoading,
    
    // Error states
    error: participantsError || summaryError || recordsError,
    
    // Mutation states
    isCheckingIn: checkInMutation.isPending,
    isCheckingOut: checkOutMutation.isPending,
    isUpdatingStatus: updateStatusMutation.isPending,
    isExporting: exportMutation.isPending,
    
    // Actions
    checkIn: checkInMutation.mutate,
    checkOut: checkOutMutation.mutate,
    updateStatus: updateStatusMutation.mutate,
    exportData: exportMutation.mutate,
    
    // Convenience functions
    quickCheckIn,
    quickCheckOut,
    getParticipantStatus,
    refreshAll,
    
    // Utils
    invalidateQueries: invalidateAttendanceQueries,
    isRealTimeConnected: isSubscribed
  };
}

// ==================== STANDALONE HOOKS ====================

/**
 * Hook for a single participant's attendance data
 */
export function useParticipantAttendance(participantId: string, programmeId: string) {
  const { participants, getParticipantStatus, ...rest } = useAttendance({ programmeId });
  
  const participant = participants.find(p => p.id === participantId);
  const status = getParticipantStatus(participantId);
  
  return {
    participant,
    status,
    ...rest
  };
}

/**
 * Hook for attendance summary only
 */
export function useAttendanceSummary(programmeId: string) {
  return useQuery({
    queryKey: ['attendance-summary-only', programmeId],
    queryFn: async () => {
      const result = await getAttendanceSummary(programmeId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch attendance summary');
      }
      return result.data;
    },
    enabled: !!programmeId,
    refetchInterval: 30000 // Refresh every 30 seconds
  });
}