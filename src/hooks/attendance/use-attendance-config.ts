'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  getAttendanceConfig,
  updateAttendanceConfig
} from '@/services/client/attendance-client.service';
import {
  createAttendanceConfiguration,
  serverUpdateAttendanceConfiguration
} from '@/services/server/attendance-server.service';
import {
  AttendanceConfiguration,
  AttendanceConfigForm,
  UpdateAttendanceConfig
} from '@/types/attendance/attendance.types';
import {
  attendanceConfigFormSchema,
  updateAttendanceConfigSchema
} from '@/schemas/attendance/attendance-config.schema';
import { toast } from '@/hooks/use-toast';

interface UseAttendanceConfigOptions {
  programmeId: string;
  createIfMissing?: boolean;
}

export function useAttendanceConfig(options: UseAttendanceConfigOptions) {
  const { programmeId, createIfMissing = true } = options;
  const queryClient = useQueryClient();

  // ==================== QUERY ====================

  const {
    data: config,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['attendance-config', programmeId],
    queryFn: async () => {
      const result = await getAttendanceConfig(programmeId);
      if (!result.success) {
        // If config doesn't exist and we should create it
        if (createIfMissing && result.error?.includes('not found')) {
          const createResult = await createAttendanceConfiguration(programmeId, {
            programme_id: programmeId,
            allow_self_checkin: true,
            qr_code_enabled: true,
            minimum_attendance_percentage: 80,
            late_arrival_threshold_minutes: 15,
            early_departure_threshold_minutes: 15,
            certificate_minimum_attendance: 80,
            require_checkout: false,
            checkin_starts_minutes_before: 30,
            checkin_ends_minutes_after: 30,
            qr_code_refresh_interval_minutes: 60
          });
          
          if (createResult.success) {
            return createResult.data;
          }
        }
        throw new Error(result.error || 'Failed to fetch attendance configuration');
      }
      return result.data;
    },
    enabled: !!programmeId,
    retry: false // Don't retry if config doesn't exist
  });

  // ==================== FORM SETUP ====================

  const form = useForm<AttendanceConfigForm>({
    resolver: zodResolver(attendanceConfigFormSchema),
    defaultValues: {
      programme_id: programmeId,
      allow_self_checkin: true,
      qr_code_enabled: true
    },
    values: config ? {
      programme_id: config.programme_id,
      allow_self_checkin: config.allow_self_checkin,
      qr_code_enabled: config.qr_code_enabled
    } : undefined
  });

  // ==================== MUTATIONS ====================

  // Update configuration mutation
  const updateMutation = useMutation({
    mutationFn: async (updates: UpdateAttendanceConfig) => {
      // Validate the updates
      const validatedUpdates = updateAttendanceConfigSchema.parse(updates);
      
      // Use server action for secure updates
      return await serverUpdateAttendanceConfiguration(programmeId, validatedUpdates);
    },
    onSuccess: (response) => {
      if (response.success) {
        toast({
          title: "Success",
          description: "Attendance settings updated successfully",
        });
        
        // Update the query cache
        queryClient.setQueryData(['attendance-config', programmeId], response.data);
        
        // Invalidate related queries
        queryClient.invalidateQueries({ 
          queryKey: ['attendance-config', programmeId] 
        });
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to update attendance settings",
          variant: "destructive"
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  });

  // Create configuration mutation (for first-time setup)
  const createMutation = useMutation({
    mutationFn: async (configData: Partial<AttendanceConfiguration>) => {
      return await createAttendanceConfiguration(programmeId, configData);
    },
    onSuccess: (response) => {
      if (response.success) {
        toast({
          title: "Success",
          description: "Attendance configuration created successfully",
        });
        
        // Update the query cache
        queryClient.setQueryData(['attendance-config', programmeId], response.data);
        refetch();
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to create attendance configuration",
          variant: "destructive"
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  });

  // ==================== FORM HANDLERS ====================

  const onSubmit = async (data: AttendanceConfigForm) => {
    const updates: UpdateAttendanceConfig = {
      allow_self_checkin: data.allow_self_checkin,
      qr_code_enabled: data.qr_code_enabled
    };

    return updateMutation.mutateAsync(updates);
  };

  const handleFormSubmit = form.handleSubmit(onSubmit);

  // ==================== CONVENIENCE FUNCTIONS ====================

  const updateSetting = async (key: keyof UpdateAttendanceConfig, value: boolean) => {
    const updates: UpdateAttendanceConfig = { [key]: value };
    return updateMutation.mutateAsync(updates);
  };

  const toggleSelfCheckIn = async () => {
    if (!config) return;
    return updateSetting('allow_self_checkin', !config.allow_self_checkin);
  };

  const toggleQRCode = async () => {
    if (!config) return;
    return updateSetting('qr_code_enabled', !config.qr_code_enabled);
  };

  const resetToDefaults = async () => {
    const defaultConfig: UpdateAttendanceConfig = {
      allow_self_checkin: true,
      qr_code_enabled: true
    };
    
    return updateMutation.mutateAsync(defaultConfig);
  };

  // ==================== VALIDATION HELPERS ====================

  const getConfigStatus = () => {
    if (!config) {
      return {
        exists: false,
        isComplete: false,
        missingFields: ['Configuration not found']
      };
    }

    return {
      exists: true,
      isComplete: true,
      missingFields: [],
      settings: {
        selfCheckInEnabled: config.allow_self_checkin,
        qrCodeEnabled: config.qr_code_enabled,
        minimumAttendance: config.minimum_attendance_percentage,
        lateThreshold: config.late_arrival_threshold_minutes,
        certificateThreshold: config.certificate_minimum_attendance
      }
    };
  };

  const canParticipantsSelfCheckIn = () => {
    return config?.allow_self_checkin || false;
  };

  const isQRCodeEnabled = () => {
    return config?.qr_code_enabled || false;
  };

  // ==================== RETURN VALUES ====================

  return {
    // Data
    config,
    isLoading,
    error,
    
    // Form
    form,
    handleSubmit: handleFormSubmit,
    
    // Actions
    updateConfig: updateMutation.mutate,
    createConfig: createMutation.mutate,
    updateSetting,
    toggleSelfCheckIn,
    toggleQRCode,
    resetToDefaults,
    refetch,
    
    // Status
    isUpdating: updateMutation.isPending,
    isCreating: createMutation.isPending,
    isSaving: updateMutation.isPending || createMutation.isPending,
    
    // Helpers
    getConfigStatus,
    canParticipantsSelfCheckIn,
    isQRCodeEnabled,
    
    // Form state
    isDirty: form.formState.isDirty,
    isValid: form.formState.isValid,
    errors: form.formState.errors
  };
}

// ==================== READONLY HOOK ====================

/**
 * Lightweight hook for reading attendance configuration without form management
 */
export function useAttendanceConfigReadonly(programmeId: string) {
  const { data: config, isLoading, error } = useQuery({
    queryKey: ['attendance-config-readonly', programmeId],
    queryFn: async () => {
      const result = await getAttendanceConfig(programmeId);
      if (!result.success) {
        return null; // Return null instead of throwing for readonly access
      }
      return result.data;
    },
    enabled: !!programmeId
  });

  return {
    config,
    isLoading,
    error,
    exists: !!config,
    canParticipantsSelfCheckIn: config?.allow_self_checkin || false,
    isQRCodeEnabled: config?.qr_code_enabled || false,
    minimumAttendancePercentage: config?.minimum_attendance_percentage || 80,
    certificateMinimumAttendance: config?.certificate_minimum_attendance || 80
  };
}

// ==================== CONFIGURATION PRESETS ====================

export const ATTENDANCE_CONFIG_PRESETS = {
  strict: {
    allow_self_checkin: false,
    qr_code_enabled: true,
    minimum_attendance_percentage: 90,
    certificate_minimum_attendance: 90,
    late_arrival_threshold_minutes: 10
  },
  standard: {
    allow_self_checkin: true,
    qr_code_enabled: true,
    minimum_attendance_percentage: 80,
    certificate_minimum_attendance: 80,
    late_arrival_threshold_minutes: 15
  },
  flexible: {
    allow_self_checkin: true,
    qr_code_enabled: false,
    minimum_attendance_percentage: 70,
    certificate_minimum_attendance: 70,
    late_arrival_threshold_minutes: 30
  }
} as const;

/**
 * Hook for applying configuration presets
 */
export function useAttendanceConfigPresets(programmeId: string) {
  const { updateConfig } = useAttendanceConfig({ programmeId });

  const applyPreset = async (preset: keyof typeof ATTENDANCE_CONFIG_PRESETS) => {
    const presetConfig = ATTENDANCE_CONFIG_PRESETS[preset];
    return updateConfig({
      allow_self_checkin: presetConfig.allow_self_checkin,
      qr_code_enabled: presetConfig.qr_code_enabled
    });
  };

  return {
    presets: ATTENDANCE_CONFIG_PRESETS,
    applyPreset
  };
}