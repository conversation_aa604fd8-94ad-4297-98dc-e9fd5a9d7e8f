'use client'

import { useState, useCallback } from 'react';
import { 
  bulkCheckInParticipants 
} from '@/services/client/attendance-client.service';
import {
  serverBulkCheckInParticipants
} from '@/services/server/attendance-server.service';
import {
  BulkCheckInRequest,
  AttendanceRecord,
  ParticipantWithAttendance
} from '@/types/attendance/attendance.types';
import { toast } from '@/hooks/use-toast';

interface BulkCheckInResult {
  successful: AttendanceRecord[];
  failed: { participant_id: string; error: string }[];
  total: number;
  successCount: number;
  failCount: number;
}

interface UseBulkCheckInOptions {
  programmeId: string;
  onSuccess?: (result: BulkCheckInResult) => void;
  onError?: (error: Error) => void;
  useServerAction?: boolean;
}

export function useBulkCheckIn(options: UseBulkCheckInOptions) {
  const { programmeId, onSuccess, onError, useServerAction = true } = options;
  
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]);
  const [checkInLocation, setCheckInLocation] = useState<string>('');
  const [checkInNotes, setCheckInNotes] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastResult, setLastResult] = useState<BulkCheckInResult | null>(null);

  // ==================== BULK CHECK-IN ====================

  const performBulkCheckIn = useCallback(async (
    participantIds: string[],
    scheduleId?: string,
    customLocation?: string,
    customNotes?: string
  ) => {
    if (participantIds.length === 0) {
      toast({
        title: "Warning",
        description: "Please select at least one participant",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: BulkCheckInRequest = {
        participant_ids: participantIds,
        programme_id: programmeId,
        schedule_id: scheduleId,
        check_in_method: 'manual',
        check_in_location: customLocation || checkInLocation || undefined,
        notes: customNotes || checkInNotes || undefined
      };

      let response;
      if (useServerAction) {
        response = await serverBulkCheckInParticipants(request, 'current-user-id');
      } else {
        const result = await bulkCheckInParticipants(request);
        // Transform to match server response format
        if (result.success && result.data) {
          response = {
            success: true,
            data: {
              successful: result.data,
              failed: []
            }
          };
        } else {
          response = result;
        }
      }

      if (response.success && response.data) {
        let result: BulkCheckInResult;
        
        // Handle server response format (has successful/failed properties)
        if ('successful' in response.data && 'failed' in response.data) {
          result = {
            successful: response.data.successful || [],
            failed: response.data.failed || [],
            total: (response.data.successful?.length || 0) + (response.data.failed?.length || 0),
            successCount: response.data.successful?.length || 0,
            failCount: response.data.failed?.length || 0
          };
        } else {
          // Handle transformed client response format (should be array or object with successful property)
          const successfulRecords = Array.isArray(response.data) 
            ? response.data 
            : (response.data as any).successful || [];
          
          result = {
            successful: successfulRecords,
            failed: [],
            total: successfulRecords.length,
            successCount: successfulRecords.length,
            failCount: 0
          };
        }

        setLastResult(result);

        // Show detailed results
        if (result.successCount > 0 && result.failCount === 0) {
          toast({
            title: "Success",
            description: `Successfully checked in ${result.successCount} participant${result.successCount > 1 ? 's' : ''}`,
          });
        } else if (result.successCount > 0 && result.failCount > 0) {
          toast({
            title: "Partial Success",
            description: `${result.successCount} successful, ${result.failCount} failed. Check details below.`,
            variant: "default"
          });
        } else {
          toast({
            title: "Failed",
            description: `All ${result.total} check-in attempts failed`,
            variant: "destructive"
          });
        }

        // Clear selections after successful operation
        if (result.successCount > 0) {
          setSelectedParticipants([]);
          setCheckInNotes('');
        }

        onSuccess?.(result);
      } else {
        throw new Error(response.error || 'Failed to perform bulk check-in');
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  }, [programmeId, checkInLocation, checkInNotes, useServerAction, onSuccess, onError]);

  const performBulkCheckInSelected = useCallback(async (scheduleId?: string) => {
    return performBulkCheckIn(selectedParticipants, scheduleId);
  }, [performBulkCheckIn, selectedParticipants]);

  // ==================== SELECTION MANAGEMENT ====================

  const toggleParticipantSelection = useCallback((participantId: string) => {
    setSelectedParticipants(prev => 
      prev.includes(participantId)
        ? prev.filter(id => id !== participantId)
        : [...prev, participantId]
    );
  }, []);

  const selectAllParticipants = useCallback((participants: ParticipantWithAttendance[]) => {
    // Only select participants who can be checked in
    const eligibleIds = participants
      .filter(p => p.can_check_in)
      .map(p => p.id);
    setSelectedParticipants(eligibleIds);
  }, []);

  const selectNoneParticipants = useCallback(() => {
    setSelectedParticipants([]);
  }, []);

  const selectByStatus = useCallback((
    participants: ParticipantWithAttendance[], 
    status: 'absent' | 'can_check_in' | 'can_check_out'
  ) => {
    let eligibleIds: string[] = [];

    switch (status) {
      case 'absent':
        eligibleIds = participants
          .filter(p => p.current_attendance_status === 'absent')
          .map(p => p.id);
        break;
      case 'can_check_in':
        eligibleIds = participants
          .filter(p => p.can_check_in)
          .map(p => p.id);
        break;
      case 'can_check_out':
        eligibleIds = participants
          .filter(p => p.can_check_out)
          .map(p => p.id);
        break;
    }

    setSelectedParticipants(eligibleIds);
  }, []);

  // ==================== VALIDATION HELPERS ====================

  const getSelectionSummary = useCallback((participants: ParticipantWithAttendance[]) => {
    const selectedData = participants.filter(p => selectedParticipants.includes(p.id));
    
    return {
      total: selectedParticipants.length,
      canCheckIn: selectedData.filter(p => p.can_check_in).length,
      alreadyCheckedIn: selectedData.filter(p => !p.can_check_in).length,
      participants: selectedData
    };
  }, [selectedParticipants]);

  const validateSelection = useCallback((participants: ParticipantWithAttendance[]) => {
    const summary = getSelectionSummary(participants);
    
    if (summary.total === 0) {
      return { isValid: false, message: "No participants selected" };
    }
    
    if (summary.canCheckIn === 0) {
      return { isValid: false, message: "None of the selected participants can be checked in" };
    }
    
    if (summary.alreadyCheckedIn > 0) {
      return { 
        isValid: true, 
        message: `${summary.alreadyCheckedIn} participants are already checked in and will be skipped`,
        warning: true
      };
    }
    
    return { isValid: true, message: `Ready to check in ${summary.canCheckIn} participants` };
  }, [getSelectionSummary]);

  // ==================== BULK ACTIONS ====================

  const checkInAllAbsent = useCallback(async (
    participants: ParticipantWithAttendance[],
    scheduleId?: string
  ) => {
    const absentIds = participants
      .filter(p => p.current_attendance_status === 'absent' && p.can_check_in)
      .map(p => p.id);
    
    if (absentIds.length === 0) {
      toast({
        title: "Info",
        description: "No absent participants found to check in",
      });
      return;
    }

    return performBulkCheckIn(absentIds, scheduleId);
  }, [performBulkCheckIn]);

  const checkInAllEligible = useCallback(async (
    participants: ParticipantWithAttendance[],
    scheduleId?: string
  ) => {
    const eligibleIds = participants
      .filter(p => p.can_check_in)
      .map(p => p.id);
    
    if (eligibleIds.length === 0) {
      toast({
        title: "Info",
        description: "No eligible participants found to check in",
      });
      return;
    }

    return performBulkCheckIn(eligibleIds, scheduleId);
  }, [performBulkCheckIn]);

  const reset = useCallback(() => {
    setError(null);
    setLastResult(null);
  }, []);

  // ==================== RETURN VALUES ====================

  return {
    // State
    selectedParticipants,
    checkInLocation,
    checkInNotes,
    
    // State setters
    setSelectedParticipants,
    setCheckInLocation,
    setCheckInNotes,
    
    // Selection management
    toggleParticipantSelection,
    selectAllParticipants,
    selectNoneParticipants,
    selectByStatus,
    
    // Actions
    performBulkCheckIn,
    performBulkCheckInSelected,
    checkInAllAbsent,
    checkInAllEligible,
    
    // Validation
    getSelectionSummary,
    validateSelection,
    
    // Status
    isLoading,
    error,
    lastResult,
    reset
  };
}