'use client'

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/utils/supabase/Client';
import {
  getParticipantsWithAttendance,
  getAttendanceRecords,
  getAttendanceSummary,
  checkInParticipant,
  checkOutParticipant,
  exportAttendanceData,
  getAttendanceConfig
} from '@/services/client/attendance-client.service';
import {
  ParticipantWithAttendance,
  AttendanceRecord,
  AttendanceStatusSummary,
  CheckInRequest,
  CheckOutRequest,
  AttendanceExportOptions,
  AttendanceConfiguration
} from '@/types/attendance/attendance.types';
import { toast } from '@/hooks/use-toast';

interface UseAttendanceOptions {
  programmeId: string;
  enableRealtime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useAttendance(options: UseAttendanceOptions) {
  const { programmeId, enableRealtime = true, autoRefresh = false, refreshInterval = 30000 } = options;
  
  // State
  const [participants, setParticipants] = useState<ParticipantWithAttendance[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [attendanceSummary, setAttendanceSummary] = useState<AttendanceStatusSummary | null>(null);
  const [attendanceConfig, setAttendanceConfig] = useState<AttendanceConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isRealTimeConnected, setIsRealTimeConnected] = useState(false);

  // ==================== DATA FETCHING ====================

  const fetchParticipants = useCallback(async () => {
    try {
      const result = await getParticipantsWithAttendance(
        programmeId,
        { subFilters: {} },
        1,
        1000
      );
      if (result.success) {
        setParticipants(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch participants');
      }
    } catch (err) {
      setError(err as Error);
    }
  }, [programmeId]);

  const fetchSummary = useCallback(async () => {
    try {
      const result = await getAttendanceSummary(programmeId);
      if (result.success) {
        setAttendanceSummary(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch attendance summary');
      }
    } catch (err) {
      setError(err as Error);
    }
  }, [programmeId]);

  const fetchRecords = useCallback(async () => {
    try {
      const result = await getAttendanceRecords(programmeId);
      if (result.success) {
        setAttendanceRecords(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch attendance records');
      }
    } catch (err) {
      setError(err as Error);
    }
  }, [programmeId]);

  const fetchConfig = useCallback(async () => {
    try {
      const result = await getAttendanceConfig(programmeId);
      if (result.success) {
        setAttendanceConfig(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch attendance configuration');
      }
    } catch (err) {
      setError(err as Error);
    }
  }, [programmeId]);

  const refreshAll = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchParticipants(),
        fetchSummary(),
        fetchRecords(),
        fetchConfig()
      ]);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchParticipants, fetchSummary, fetchRecords, fetchConfig]);

  // ==================== ACTIONS ====================

  const quickCheckIn = useCallback(async (participantId: string, scheduleId?: string, notes?: string) => {
    setIsCheckingIn(true);
    
    try {
      const request: CheckInRequest = {
        participant_id: participantId,
        programme_id: programmeId,
        schedule_id: scheduleId,
        check_in_method: 'manual',
        check_in_location: 'Admin Panel',
        notes
      };

      // Use client-side check-in which properly records in the database
      const result = await checkInParticipant(request);
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Participant checked in successfully",
        });
        await refreshAll();
      } else {
        throw new Error(result.error || 'Failed to check in participant');
      }
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsCheckingIn(false);
    }
  }, [programmeId, refreshAll]);

  const quickCheckOut = useCallback(async (attendanceRecordId: string, notes?: string) => {
    setIsCheckingOut(true);
    
    try {
      const request: CheckOutRequest = {
        attendance_record_id: attendanceRecordId,
        notes
      };

      const result = await checkOutParticipant(request);
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Participant checked out successfully",
        });
        await refreshAll();
      } else {
        throw new Error(result.error || 'Failed to check out participant');
      }
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsCheckingOut(false);
    }
  }, [refreshAll]);

  const updateStatus = useCallback(async (
    attendanceRecordId: string, 
    status: 'present' | 'absent' | 'late' | 'excused' | 'partial',
    notes?: string
  ) => {
    setIsUpdatingStatus(true);
    
    try {
      // TODO: Implement client-side status update when needed
      toast({
        title: "Info",
        description: "Status update functionality coming soon",
      });
      
      // For now, just refresh to show latest data
      await refreshAll();
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  }, [refreshAll]);

  const exportData = useCallback(async (options: AttendanceExportOptions) => {
    setIsExporting(true);
    
    try {
      const result = await exportAttendanceData(options);
      
      if (result.success && result.data) {
        // Create download link
        const url = URL.createObjectURL(result.data);
        const link = document.createElement('a');
        link.href = url;
        link.download = `attendance-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast({
          title: "Success",
          description: "Attendance report exported successfully",
        });
      } else {
        throw new Error(result.error || 'Export failed');
      }
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to export attendance report",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  }, []);

  // ==================== HELPERS ====================

  const getParticipantStatus = useCallback((participantId: string) => {
    const participant = participants.find(p => p.id === participantId);
    if (!participant) return null;

    return {
      current_status: participant.current_attendance_status,
      can_check_in: participant.can_check_in,
      can_check_out: participant.can_check_out,
      attendance_percentage: participant.attendance_percentage,
      certificate_eligible: participant.certificate_eligible,
      last_check_in: participant.last_check_in_time
    };
  }, [participants]);

  // ==================== EFFECTS ====================

  // Initial data load
  useEffect(() => {
    if (programmeId) {
      refreshAll();
    }
  }, [programmeId, refreshAll]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh || !refreshInterval) return;

    const interval = setInterval(() => {
      refreshAll();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshAll]);

  // Realtime subscriptions
  useEffect(() => {
    if (!enableRealtime || !programmeId) return;

    const setupRealtime = async () => {
      const supabase = createClient();
      
      const channel = supabase
        .channel(`attendance-${programmeId}`)
        .on('postgres_changes', {
          event: '*',
          schema: 'programme',
          table: 'attendance_records',
          filter: `programme_id=eq.${programmeId}`
        }, () => {
          refreshAll();
        })
        .on('postgres_changes', {
          event: '*',
          schema: 'programme',
          table: 'session_attendance',
          filter: `programme_id=eq.${programmeId}`
        }, () => {
          refreshAll();
        })
        .on('postgres_changes', {
          event: '*',
          schema: 'programme',
          table: 'attendance_summary',
          filter: `programme_id=eq.${programmeId}`
        }, () => {
          refreshAll();
        })
        .subscribe();

      setIsRealTimeConnected(true);

      return () => {
        supabase.removeChannel(channel);
        setIsRealTimeConnected(false);
      };
    };

    setupRealtime();
  }, [enableRealtime, programmeId, refreshAll]);

  return {
    // Data
    participants,
    attendanceRecords,
    attendanceSummary,
    attendanceConfig,
    
    // Loading states
    isLoading,
    isCheckingIn,
    isCheckingOut,
    isUpdatingStatus,
    isExporting,
    
    // Error state
    error,
    setError,
    
    // Actions
    quickCheckIn,
    quickCheckOut,
    updateStatus,
    exportData,
    refreshAll,
    
    // Helpers
    getParticipantStatus,
    
    // Status
    isRealTimeConnected
  };
}