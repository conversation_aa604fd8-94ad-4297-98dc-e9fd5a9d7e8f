"use client";

import { getProfileData } from "@/services/account/account-client.service";
import { updateProfile } from "@/services/account/account-server.service";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { isValidPhoneNumber } from "react-phone-number-input";

const profileSchema = z.object({
    fullName: z.string().min(1, "Full name is required"),
    email: z.string().email("Invalid email address"),
    mobileNumber: z
        .string()
        .min(1, "Mobile Number is required")
        .refine(isValidPhoneNumber, { message: "Invalid phone number format" }),
    partial_nric: z.string().min(1, "Partial NRIC is required"),
    date_of_birth: z.date({
        required_error: "Date of Birth is required",
        invalid_type_error: "Invalid Date of Birth",
    }).refine((val) => val !== undefined && val !== null),
});

export type ProfileFormData = z.infer<typeof profileSchema>;

export function useProfileChange() {
    const [isLoading, setisLoading] = useState(false);
    const { toast } = useToast();
    const [currentUser, setCurrentUser] = useState<Partial<ProfileFormData> | null>(null);

    const userForm = useForm<ProfileFormData>({
        resolver: zodResolver(profileSchema),
        mode: "onChange",
        defaultValues: {
            fullName: "",
            partial_nric: "",
            date_of_birth: undefined,
            email: "",
            mobileNumber: "",
        },
    });

    useEffect(() => {
        loadProfile();
    }, []);

    const loadProfile = async () => {
        const { profile, success } = await getProfileData();

        if (success && profile) {
            setCurrentUser(profile);
            userForm.reset({
                fullName: profile.fullName || '',
                partial_nric: profile.partial_nric || '',
                date_of_birth: profile?.date_of_birth ? new Date(profile.date_of_birth) : undefined,
                email: profile.email || '',
                mobileNumber: profile.mobileNumber || '',
            });
        }
    };

    const onUserSubmit = async (data: ProfileFormData) => {
        const { fullName, email, mobileNumber, partial_nric, date_of_birth } = data;

        if (currentUser) {
            const hasChanged =
                fullName !== currentUser.fullName ||
                partial_nric !== currentUser.partial_nric ||
                date_of_birth !== currentUser.date_of_birth ||
                email !== currentUser.email ||
                mobileNumber !== currentUser.mobileNumber;

            if (!hasChanged) {
                toast({
                    title: "No Changes",
                    description: "No changes detected in your profile information.",
                });
                return;
            }
        }

        setisLoading(true);
        const { success } = await updateProfile({
            fullName,
            partial_nric,
            date_of_birth,
            email,
            mobileNumber,
        });
        setisLoading(false);

        if (success) {
            if (email !== currentUser?.email) {
                toast({
                    title: "Success",
                    description: "Profile updated successfully! Please check your email to confirm the change.",
                });
            } else {
                toast({
                    title: "Success",
                    description: "Profile updated successfully!",
                });
            }

            loadProfile();
        } else {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Profile update failed. Please try again.",
            });
        }
    };

    return {
        userForm,
        isLoading,
        onUserSubmit,
    };
}