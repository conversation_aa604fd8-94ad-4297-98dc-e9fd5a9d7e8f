"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { changeNewPassword } from "@/services/account/account-server.service";

const passwordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(6, "New password must be at least 6 characters"),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords must match",
  path: ["confirmPassword"],
});

export type PasswordFormData = z.infer<typeof passwordSchema>;

export function usePasswordChange() {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<PasswordFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(passwordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: PasswordFormData) => {
    setLoading(true);

    const response = await changeNewPassword({
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
    });

    if (response.success) {
      toast({
        title: "Success",
        description: "Password updated successfully!",
      });
      form.reset();
    } else {
      toast({
        title: "Error",
        description: `${response.error}`,
        variant: "destructive"
      });
    }
    setLoading(false);
  };

  return {
    form,
    loading,
    onSubmit,
  };
} 