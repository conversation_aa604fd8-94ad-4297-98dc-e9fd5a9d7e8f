'use client'

import { useEffect, useState } from "react";
import { deleteBudgetPreparation, submitBudgetPreparation } from "@/services/budgets/budget-server.service";
import { getBudgetItemWithSection, getComplexBudgetForm } from "@/services/budgets/budget.service";
import { budgetItemCheck, BudgetItems, budgetSectionsData } from "@/types//budgets/budgets.types";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";

interface Type {
    id: string;
    type: string;
    proposedRate: string;
    quantity: string;
    subTotal: string;
    remarks: string;
}

interface Section {
    sectionId: string;
    sectionName: string;
    types: Type[];
}

export const useComplexBudgetTable = (budgetRevisionId: string, submitForReview: () => void) => {
    const router = useRouter();
    const { toast } = useToast();
    const [tabValue, setTabValue] = useState<string>("income");
    const [incomeSections, setIncomeSections] = useState<Section[]>([]);
    const [expenditureSections, setExpenditureSections] = useState<Section[]>([]);
    const [oriIncome, setOriIncome] = useState<Section[]>([]);
    const [oriExpenditure, setOriExpenditure] = useState<Section[]>([]);
    const [itemToDelete, setItemToDelete] = useState<{ id: string }[]>([]);
    const [budgetData, setBudgetData] = useState<{
        budgetSections: budgetSectionsData[];
        budgetItems: BudgetItems[];
    }>({ budgetSections: [], budgetItems: [] });
    const [totalIncome, setTotalIncome] = useState(0);
    const [totalExpenditure, setTotalExpenditure] = useState(0);
    const [netSurplusDeficit, setNetSurplusDeficit] = useState(0);
    const [percentageSurplusDeficit, setPercentageSurplusDeficit] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        const checkId = () => {
            if (!budgetRevisionId) {
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "No budget revision ID provided. Redirecting to budgets list.",
                });
                router.push("/budgets");
                return;
            } else {
                loadBudgetItem(budgetRevisionId);
            }
        };

        checkId();
    }, [budgetRevisionId]);

    useEffect(() => {
        const budgetType = tabValue === "income" ? "INCOME" : "EXPENDITURE";
        const fetchBudgetData = async (type: "INCOME" | "EXPENDITURE") => {
            try {
                const data = await getBudgetItemWithSection(type);
                setBudgetData(data);
            } catch (error) {
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Failed to fetch budget data" + error,
                });
            }
        };

        fetchBudgetData(budgetType);
    }, [tabValue]);

    const loadBudgetItem = async (budgetRevisionId: string) => {
        if (!budgetRevisionId) {
            router.push('/budgets');
            return;
        }

        try {
            const budgetpreparationResponse = await getComplexBudgetForm(budgetRevisionId);

            if (budgetpreparationResponse.incomeEntries) {
                const mappedIncomeSections = budgetpreparationResponse.incomeEntries.map((section) => ({
                    sectionId: section.id,
                    sectionName: section.name,
                    types: section.types?.map((type) => ({
                        id: type.id,
                        type: type.budget_item_id || "",
                        proposedRate: type.proposed_rate?.toString() || "",
                        quantity: type.quantity?.toString() || "",
                        subTotal: type.sub_total?.toString() || "",
                        remarks: type.remarks || "",
                    })) || [],
                }));

                setIncomeSections(mappedIncomeSections);
                setOriIncome(JSON.parse(JSON.stringify(mappedIncomeSections)));
            }

            // Map expenditure entries to the section structure
            if (budgetpreparationResponse.expenditureEntries) {
                const mappedExpenditureSections = budgetpreparationResponse.expenditureEntries.map((section) => ({
                    sectionId: section.id,
                    sectionName: section.name,
                    types: section.types?.map((type) => ({
                        id: type.id,
                        type: type.budget_item_id || "",
                        proposedRate: type.proposed_rate?.toString() || "",
                        quantity: type.quantity?.toString() || "",
                        subTotal: type.sub_total?.toString() || "",
                        remarks: type.remarks || "",
                    })) || [],
                }));

                setExpenditureSections(mappedExpenditureSections);
                setOriExpenditure(JSON.parse(JSON.stringify(mappedExpenditureSections)));
            }

        } catch (err) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch budget data. Redirecting to budgets list." + err,
            });
            router.push('/budgets');
        }
    };

    const addSection = (selectedId: string) => {
        const selectedSection = budgetData.budgetSections.find(
            (section) => section.id === selectedId
        );
        if (selectedSection) {
            setIncomeSections([
                ...incomeSections,
                {
                    sectionId: selectedSection.id, sectionName: selectedSection.name || "",
                    types: [
                        {
                            id: "",
                            type: "",
                            proposedRate: "",
                            quantity: "",
                            subTotal: "",
                            remarks: "",
                        },
                    ],
                }
            ]);
        }
    };

    const isSectionSelected = (sectionId: string) =>
        incomeSections.some((section) => section.sectionId === sectionId);

    const removeSection = (sectionIndex: number) => {
        const idsToDelete = incomeSections[sectionIndex].types
            .filter(type => type.id)
            .map(type => type.id);

        if (idsToDelete.length > 0) {
            setItemToDelete(prevItems => [...prevItems, ...idsToDelete.map(id => ({ id }))]);
        }

        const updatedSections = incomeSections.filter(
            (_, index) => index !== sectionIndex
        );
        setIncomeSections(updatedSections);
    };

    const addExpenditureSection = (selectedId: string) => {
        const selectedSection = budgetData.budgetSections.find(
            (section) => section.id === selectedId
        );
        if (selectedSection) {
            setExpenditureSections([
                ...expenditureSections,
                {
                    sectionId: selectedSection.id, sectionName: selectedSection.name || "",
                    types: [
                        {
                            id: "",
                            type: "",
                            proposedRate: "",
                            quantity: "",
                            subTotal: "",
                            remarks: "",
                        },
                    ],
                }
            ]);
        }
    };

    const isExpenditureSectionSelected = (sectionId: string) =>
        expenditureSections.some((section) => section.sectionId === sectionId);

    const addTypeToSection = (sectionIndex: number) => {
        const updatedSections = [...incomeSections];
        updatedSections[sectionIndex].types.push({
            id: "",
            type: "",
            proposedRate: "",
            quantity: "",
            subTotal: "",
            remarks: "",
        });
        setIncomeSections(updatedSections);
    };

    const removeExpenditureSection = (sectionIndex: number) => {
        const idsToDelete = expenditureSections[sectionIndex].types
            .filter(type => type.id)
            .map(type => type.id);

        if (idsToDelete.length > 0) {
            setItemToDelete(prevItems => [...prevItems, ...idsToDelete.map(id => ({ id }))]);
        }

        const updatedSections = expenditureSections.filter(
            (_, index) => index !== sectionIndex
        );
        setExpenditureSections(updatedSections);
    };

    const addExpenditureTypeToSection = (sectionIndex: number) => {
        const updatedSections = [...expenditureSections];
        updatedSections[sectionIndex].types.push({
            id: "",
            type: "",
            proposedRate: "",
            quantity: "",
            subTotal: "",
            remarks: "",
        });
        setExpenditureSections(updatedSections);
    };

    const handleInputChange = (
        sectionIndex: number,
        typeIndex: number,
        field: keyof Type,
        value: string
    ) => {
        const updatedSections = [...incomeSections];
        updatedSections[sectionIndex].types[typeIndex][field] = value;

        if (field === "proposedRate" || field === "quantity") {
            updatedSections[sectionIndex].types[typeIndex].subTotal = (
                (parseFloat(
                    updatedSections[sectionIndex].types[typeIndex].proposedRate
                ) || 0) *
                (parseFloat(updatedSections[sectionIndex].types[typeIndex].quantity) ||
                    0)
            ).toString();
        }

        setIncomeSections(updatedSections);
    };

    const handleExpenditureInputChange = (
        sectionIndex: number,
        typeIndex: number,
        field: keyof Type,
        value: string
    ) => {
        const updatedSections = [...expenditureSections];
        updatedSections[sectionIndex].types[typeIndex][field] = value;

        if (field === "proposedRate" || field === "quantity") {
            updatedSections[sectionIndex].types[typeIndex].subTotal = (
                (parseFloat(
                    updatedSections[sectionIndex].types[typeIndex].proposedRate
                ) || 0) *
                (parseFloat(updatedSections[sectionIndex].types[typeIndex].quantity) ||
                    0)
            ).toString();
        }

        setExpenditureSections(updatedSections);
    };

    const removeIncomeTypeFromSection = (sectionIndex: number, typeIndex: number, id?: string) => {
        if (id) {
            setItemToDelete((prevItems) => [...prevItems, { id }]);
        }

        const updatedSections = [...incomeSections];
        updatedSections[sectionIndex].types.splice(typeIndex, 1);
        setIncomeSections(updatedSections);
    };

    const removeExpenditureTypeFromSection = (sectionIndex: number, typeIndex: number, id?: string) => {
        if (id) {
            setItemToDelete((prevItems) => [...prevItems, { id }]);
        }
        const updatedSections = [...expenditureSections];
        updatedSections[sectionIndex].types.splice(typeIndex, 1);
        setExpenditureSections(updatedSections);
    };

    const calculateTotals = () => {
        const income = incomeSections.reduce(
            (acc, entry) =>
                acc + entry.types.reduce(
                    (innerAcc, type) => innerAcc + (Number(type.subTotal) || 0),
                    0
                ),
            0
        );

        const expenditure = expenditureSections.reduce(
            (acc, entry) =>
                acc + entry.types.reduce(
                    (innerAcc, type) => innerAcc + (Number(type.subTotal) || 0),
                    0
                ),
            0
        );

        setTotalIncome(income);
        setTotalExpenditure(expenditure);
        setNetSurplusDeficit(income - expenditure);
        setPercentageSurplusDeficit(
            income ? ((income - expenditure) / income) * 100 : 0
        );
    };

    useEffect(() => {
        calculateTotals();
    }, [incomeSections, expenditureSections]);

    const calculateSectionTotal = (section: Section): number =>
        section.types.reduce(
            (total, type) => total + (parseFloat(type.subTotal) || 0),
            0
        );

    const calculateExpenditureSectionTotal = (section: Section): number =>
        section.types.reduce(
            (total, type) => total + (parseFloat(type.subTotal) || 0),
            0
        );

    const validateEntries = () => {
        const invalidIncomeEntries = incomeSections.some(section =>
            !section.types.length ||
            section.types.some(entry =>
                !entry.type ||
                !entry.proposedRate ||
                !entry.quantity
            )
        );

        const invalidExpenditureEntries = expenditureSections.some(section =>
            !section.types.length ||
            section.types.some(entry =>
                !entry.type ||
                !entry.proposedRate ||
                !entry.quantity
            )
        );

        if (
            !incomeSections.length ||
            !expenditureSections.length ||
            invalidIncomeEntries ||
            invalidExpenditureEntries
        ) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Please fill in all required fields for income and expenditure entries.",
            });
            return false;
        }

        return true;
    };

    const handleDelete = async () => {
        if (!itemToDelete || itemToDelete.length === 0) return;

        try {
            await deleteBudgetPreparation(itemToDelete);
            setItemToDelete([]);
            toast({
                title: "Deletion Successful",
                description: "The selected budget item were successfully deleted.",
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Deletion Error",
                description: "Failed to delete budget data." + error,
            });
        }
    };

    const handleSubmit = async (isSubmitReview?: boolean) => {
        if (!validateEntries()) {
            return;
        }

        let deleteTriggered = false;
        setIsSubmitting(true);

        if (itemToDelete.length > 0) {
            await handleDelete();
            deleteTriggered = true;
        }

        const getModifiedOrNewEntries = (
            originalEntries: budgetItemCheck[],
            currentEntries: budgetItemCheck[]
        ) => {
            return currentEntries.reduce((modifiedEntries: budgetItemCheck[], entry) => {
                const originalEntry = entry.id
                    ? originalEntries.find((origEntry) => origEntry.id === entry.id)
                    : null;

                if (!entry.id) {
                    modifiedEntries.push({ ...entry });
                }
                // If entry is existing and has changes, check for changes
                else if (originalEntry) {
                    const hasChangedFields =
                        entry.type !== originalEntry.type ||
                        entry.proposedRate !== originalEntry.proposedRate ||
                        entry.quantity !== originalEntry.quantity ||
                        entry.remarks !== originalEntry.remarks;

                    if (hasChangedFields) {
                        modifiedEntries.push({ ...entry });
                    }
                }

                return modifiedEntries;
            }, []);
        };

        const incomeEntries: budgetItemCheck[] = incomeSections.map(section =>
            section.types.map(type => ({
                id: type.id,
                type: type.type,
                proposedRate: type.proposedRate,
                quantity: type.quantity,
                remarks: type.remarks
            }))
        ).flat();

        const expenditureEntries: budgetItemCheck[] = expenditureSections.map(section =>
            section.types.map(type => ({
                id: type.id,
                type: type.type,
                proposedRate: type.proposedRate,
                quantity: type.quantity,
                remarks: type.remarks
            }))
        ).flat();

        const oriIncomeEntries: budgetItemCheck[] = oriIncome.map(section =>
            section.types.map(type => ({
                id: type.id,
                type: type.type,
                proposedRate: type.proposedRate,
                quantity: type.quantity,
                remarks: type.remarks
            }))
        ).flat();

        const oriExpenditureEntries: budgetItemCheck[] = oriExpenditure.map(section =>
            section.types.map(type => ({
                id: type.id,
                type: type.type,
                proposedRate: type.proposedRate,
                quantity: type.quantity,
                remarks: type.remarks
            }))
        ).flat();

        const modifiedIncomeEntries = getModifiedOrNewEntries(oriIncomeEntries, incomeEntries);
        const modifiedExpenditureEntries = getModifiedOrNewEntries(oriExpenditureEntries, expenditureEntries);

        if (modifiedIncomeEntries.length === 0 && modifiedExpenditureEntries.length === 0) {
            if (isSubmitReview) {
                submitForReview();
            } else {
                if (deleteTriggered) {
                    setIncomeSections([]);
                    setExpenditureSections([]);
                    setOriIncome([]);
                    setExpenditureSections([]);
                    loadBudgetItem(budgetRevisionId);
                } else {
                    toast({
                        title: "Info",
                        description: "No changes detected. No submission necessary.",
                    });
                }
            }
            setIsSubmitting(false);
            return;
        }

        const submissionData = {
            budgetRevisionId,
            incomeEntries: modifiedIncomeEntries.map((entry) => ({
                id: entry.id,
                type: entry.type,
                proposedRate: Number(entry.proposedRate),
                quantity: Number(entry.quantity),
                remarks: entry.remarks,
            })),
            expenditureEntries: modifiedExpenditureEntries.map((entry) => ({
                id: entry.id,
                type: entry.type,
                proposedRate: Number(entry.proposedRate),
                quantity: Number(entry.quantity),
                remarks: entry.remarks,
            })),
        };

        try {
            await submitBudgetPreparation(submissionData);

            if (isSubmitReview) {
                submitForReview();
            } else {
                setIncomeSections([]);
                setExpenditureSections([]);
                setOriIncome([]);
                setExpenditureSections([]);
                loadBudgetItem(budgetRevisionId);

                toast({
                    title: "Success",
                    description: "Budget data saved successfully.",
                });
            }
        } catch {
            toast({
                variant: "destructive",
                title: "Submission Error",
                description: "Failed to submit budget data. Please try again.",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return {
        incomeSections,
        expenditureSections,
        budgetData,
        totalIncome,
        totalExpenditure,
        netSurplusDeficit,
        percentageSurplusDeficit,
        isSubmitting,
        setTabValue,
        addSection,
        isSectionSelected,
        removeSection,
        addExpenditureSection,
        isExpenditureSectionSelected,
        addTypeToSection,
        removeExpenditureSection,
        addExpenditureTypeToSection,
        handleInputChange,
        handleExpenditureInputChange,
        removeIncomeTypeFromSection,
        removeExpenditureTypeFromSection,
        calculateSectionTotal,
        calculateExpenditureSectionTotal,
        handleSubmit,
    }
};
