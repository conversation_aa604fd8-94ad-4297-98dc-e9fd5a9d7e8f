"use client"

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { budgetItemCheck, BudgetItems } from "@/types/budgets/budgets.types";
import { useToast } from "@/hooks/use-toast";
import { getBudgetItemNoSection, getSimpleBudgetForm } from "@/services/budgets/budget.service";
import { deleteBudgetPreparation, submitBudgetPreparation } from "@/services/budgets/budget-server.service";

interface entries {
    id: string;
    type: string;
    proposedRate: string;
    quantity: string;
    subTotal: string;
    totalBudget: string;
    remarks: string;
}

export const useCourseEventBudgetTable = (budgetRevisionId: string, submitForReview: () => void) => {
    const router = useRouter();
    const { toast } = useToast();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [tabValue, setTabValue] = useState<string>("income");
    const [incomeEntries, setIncomeEntries] = useState<entries[]>([]);
    const [expenditureEntries, setExpenditureEntries] = useState<entries[]>([]);
    const [totalIncome, setTotalIncome] = useState(0);
    const [totalExpenditure, setTotalExpenditure] = useState(0);
    const [netSurplusDeficit, setNetSurplusDeficit] = useState(0);
    const [percentageSurplusDeficit, setPercentageSurplusDeficit] = useState(0);

    const [budgetData, setBudgetData] = useState<{
        budgetItems: BudgetItems[];
    }>({ budgetItems: [] });
    const [originalIncome, setOriginalIncome] = useState<{
        id: string;
        type: string;
        proposedRate: string;
        quantity: string;
        remarks: string;
    }[]>([]);
    const [originalExpenditure, setOriginalExpenditure] = useState<{
        id: string;
        expenseType: string;
        proposedRate: string;
        quantity: string;
        remarks: string;
    }[]>([]);
    const [itemToDelete, setItemToDelete] = useState<{ id: string }[]>([]);

    useEffect(() => {
        const fetchData = () => {
            if (!budgetRevisionId) {
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "No budget revision ID provided. Redirecting to programme list.",
                });
                router.push("/budgets");
                return;
            } else {
                loadBudgetItem(budgetRevisionId);
            }
        };

        fetchData();
    }, [budgetRevisionId]);

    const fetchBudgetData = async (type: "INCOME" | "EXPENDITURE") => {
        const data = await getBudgetItemNoSection(type);

        if (data.budgetItems.length > 0) {
            setBudgetData(data);
        }
    };

    const loadBudgetItem = async (budgetRevisionId: string) => {
        if (!budgetRevisionId) {
            router.push('/budgets');
            return;
        }

        try {
            const budgetpreparationResponse = await getSimpleBudgetForm(budgetRevisionId);

            // Prepare initial data for income entries
            const incomeEntriesData = budgetpreparationResponse.incomeEntries?.map(item => ({
                id: item.id,
                type: item.budget_item_id || "",
                proposedRate: item.proposed_rate?.toString() || "",
                quantity: item.quantity?.toString() || "",
                subTotal: item.sub_total?.toString() || "",
                totalBudget: "",
                remarks: item.remarks || "",
            })) || [];

            // Prepare initial data for expenditure entries
            const expenditureEntriesData = budgetpreparationResponse.expenditureEntries?.map(item => ({
                id: item.id,
                type: item.budget_item_id || "",
                proposedRate: item.proposed_rate?.toString() || "",
                quantity: item.quantity?.toString() || "",
                subTotal: item.sub_total?.toString() || "",
                totalBudget: "",
                remarks: item.remarks || "",
            })) || [];

            if (incomeEntriesData.length > 0) {
                setOriginalIncome(JSON.parse(JSON.stringify(incomeEntriesData)));
                setIncomeEntries(incomeEntriesData);
            } else {
                addIncomeEntry();
            }

            if (expenditureEntriesData.length > 0) {
                setOriginalExpenditure(JSON.parse(JSON.stringify(expenditureEntriesData)));
                setExpenditureEntries(expenditureEntriesData);
            } else {
                addExpenditureEntry();
            }

        } catch (err) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch programme data. Redirecting to courses and events." + err,
            });
            router.push('/programmes');
        }
    };

    // Fetch data whenever the tab changes
    useEffect(() => {
        const budgetType = tabValue === "income" ? "INCOME" : "EXPENDITURE";
        fetchBudgetData(budgetType);
    }, [tabValue]);

    const calculateTotals = () => {
        const income = incomeEntries.reduce(
            (acc, entry) => acc + (Number(entry.subTotal) || 0),
            0
        );
        const expenditure = expenditureEntries.reduce(
            (acc, entry) => acc + (Number(entry.subTotal) || 0),
            0
        );

        setTotalIncome(income);
        setTotalExpenditure(expenditure);
        setNetSurplusDeficit(income - expenditure);
        setPercentageSurplusDeficit(
            income ? ((income - expenditure) / income) * 100 : 0
        );
    };

    useEffect(() => {
        calculateTotals();
    }, [expenditureEntries, incomeEntries]);

    const addIncomeEntry = () => {
        setIncomeEntries([
            ...incomeEntries,
            {
                id: "",
                type: "",
                proposedRate: "",
                quantity: "",
                subTotal: "",
                totalBudget: "",
                remarks: "",
            },
        ]);
    };

    const removeIncomeEntry = async (index: number, id: string) => {
        if (id) {
            setItemToDelete(prevItems => [...prevItems, { id }]);
        }

        // Remove the entry from the incomeEntries list
        setIncomeEntries((prevEntries) =>
            prevEntries.filter((_, i) => i !== index)
        );
    };

    const addExpenditureEntry = () => {
        setExpenditureEntries([
            ...expenditureEntries,
            {
                id: "",
                type: "",
                proposedRate: "",
                quantity: "",
                subTotal: "",
                totalBudget: "",
                remarks: "",
            },
        ]);
    };

    const removeExpenditureEntry = async (index: number, id: string) => {
        if (id) {
            setItemToDelete(prevItems => [...prevItems, { id }]);
        }

        // Remove the entry from the expenditureEntries list
        setExpenditureEntries((prevEntries) =>
            prevEntries.filter((_, i) => i !== index)
        );
    };

    const calculateIncomeTotal = () => {
        return incomeEntries.reduce(
            (acc, entry) => acc + (Number(entry.subTotal) || 0),
            0
        );
    };

    const calculateExpenditureTotal = () => {
        return expenditureEntries.reduce(
            (acc, entry) => acc + (Number(entry.subTotal) || 0),
            0
        );
    };

    const validateEntries = () => {
        // Income validation
        const invalidIncomeEntries = incomeEntries.some(entry =>
            !entry.type ||
            !entry.proposedRate ||
            !entry.quantity
        );

        // Expenditure validation
        const invalidExpenditureEntries = expenditureEntries.some(entry =>
            !entry.type ||
            !entry.proposedRate ||
            !entry.quantity
        );

        if (
            !incomeEntries.length ||
            !expenditureEntries.length ||
            invalidIncomeEntries ||
            invalidExpenditureEntries
        ) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Please fill in all required fields for income and expenditure entries.",
            });
            return false;
        }

        return true;
    };

    const handleDelete = async () => {
        if (!itemToDelete || itemToDelete.length === 0) return;

        try {
            await deleteBudgetPreparation(itemToDelete);
            setItemToDelete([]);
            toast({
                title: "Deletion Successful",
                description: "The selected budget item were successfully deleted.",
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Deletion Error",
                description: "Failed to delete budget data." + error,
            });
        }
    };

    const handleSubmit = async (isSubmitReview?: boolean) => {
        if (!validateEntries()) {
            return;
        }

        let deleteTriggered = false;
        setIsSubmitting(true);

        if (itemToDelete.length > 0) {
            await handleDelete();
            deleteTriggered = true;
        }

        const getModifiedOrNewEntries = (
            originalEntries: budgetItemCheck[],
            currentEntries: budgetItemCheck[]
        ) => {
            return currentEntries.reduce((modifiedEntries: budgetItemCheck[], entry) => {
                const originalEntry = entry.id
                    ? originalEntries.find((origEntry) => origEntry.id === entry.id)
                    : null;

                if (!entry.id) {
                    modifiedEntries.push({ ...entry });
                }
                // If entry is existing and has changes, check for changes
                else if (originalEntry) {
                    const hasChangedFields =
                        entry.type !== originalEntry.type ||
                        entry.proposedRate !== originalEntry.proposedRate ||
                        entry.quantity !== originalEntry.quantity ||
                        entry.remarks !== originalEntry.remarks;

                    if (hasChangedFields) {
                        modifiedEntries.push({ ...entry });
                    }
                }

                return modifiedEntries;
            }, []);
        };

        const modifiedIncomeEntries = getModifiedOrNewEntries(originalIncome, incomeEntries);

        const modifiedExpenditureEntries = getModifiedOrNewEntries(originalExpenditure, expenditureEntries);

        if (modifiedIncomeEntries.length === 0 && modifiedExpenditureEntries.length === 0) {
            if (isSubmitReview) {
                submitForReview();
            } else {
                if (deleteTriggered) {
                    setOriginalIncome([]);
                    setIncomeEntries([]);
                    setOriginalExpenditure([]);
                    setExpenditureEntries([]);
                    loadBudgetItem(budgetRevisionId);
                } else {
                    toast({
                        title: "Info",
                        description: "No changes detected. No submission necessary.",
                    });
                }
            }
            setIsSubmitting(false);
            return;
        }

        const submissionData = {
            budgetRevisionId,
            incomeEntries: modifiedIncomeEntries.map((entry: budgetItemCheck) => ({
                id: entry.id,
                type: entry.type,
                proposedRate: Number(entry.proposedRate),
                quantity: Number(entry.quantity),
                remarks: entry.remarks
            })),
            expenditureEntries: modifiedExpenditureEntries.map((entry: budgetItemCheck) => ({
                id: entry.id,
                type: entry.type,
                proposedRate: Number(entry.proposedRate),
                quantity: Number(entry.quantity),
                remarks: entry.remarks
            })),
        };

        try {
            await submitBudgetPreparation(submissionData);

            if (isSubmitReview) {
                submitForReview();
            } else {
                setOriginalIncome([]);
                setIncomeEntries([]);
                setOriginalExpenditure([]);
                setExpenditureEntries([]);
                loadBudgetItem(budgetRevisionId);
                toast({
                    title: "Success",
                    description: "Budget data saved successfully.",
                });
            }
        } catch {
            toast({
                variant: "destructive",
                title: "Submission Error",
                description: "Failed to submit budget data. Please try again.",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return {
        incomeEntries,
        expenditureEntries,
        totalIncome,
        totalExpenditure,
        netSurplusDeficit,
        percentageSurplusDeficit,
        budgetData,
        isSubmitting,
        setTabValue,
        setIncomeEntries,
        setExpenditureEntries,
        addIncomeEntry,
        removeIncomeEntry,
        addExpenditureEntry,
        removeExpenditureEntry,
        calculateIncomeTotal,
        calculateExpenditureTotal,
        handleSubmit,
    };
};
