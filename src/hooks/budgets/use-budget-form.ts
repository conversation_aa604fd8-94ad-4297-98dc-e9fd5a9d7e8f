"use client"

import { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import type { BudgetRevisions } from "@/types/budgets/budgets.types";
import { getBudgetTypeNFormat } from "@/services/budgets/budget.service";
import { submitBudgetRevisions } from "@/services/budgets/budget-server.service";

const budgetSchema = z.object({
    version: z.number().optional(),
    budgetType: z.string({ required_error: 'Budget type is required' }),
    budgetFormat: z.string({ required_error: 'Budget format is required' }),
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional()
})

type BudgetFormData = z.infer<typeof budgetSchema>

export const useBudgetDetailForm = (createState: boolean, submitForReview?: () => void, budgetRevisionId?: string, budgetDetails?: BudgetRevisions) => {
    const { toast } = useToast();
    const router = useRouter();

    const [budgetType, setBudgetType] = useState<{ value: string; label: string }[]>([]);
    const [budgetFormat, setBudgetFormat] = useState<{ id: string; value: string; label: string }[]>([]);
    const [initialValue, setInitialValue] = useState<Partial<BudgetFormData>>({
        version: undefined,
        budgetType: budgetDetails?.entity_type ?? undefined,
        budgetFormat: budgetDetails?.format ?? undefined,
        title: budgetDetails?.title ?? "",
        description: budgetDetails?.description ?? "",
    });
    const form = useForm<BudgetFormData>({
        resolver: zodResolver(budgetSchema),
        mode: "onChange",
        defaultValues: {
            version: undefined,
            budgetType: budgetDetails?.entity_type ?? undefined,
            budgetFormat: budgetDetails?.format ?? undefined,
            title: budgetDetails?.title ?? "",
            description: budgetDetails?.description ?? "",
        },
    });

    useEffect(() => {
        fetchOptions();
    }, []);

    const fetchOptions = async () => {
        const { budgetType, budgetFormat } = await getBudgetTypeNFormat();

        setBudgetType(
            budgetType.map((item) => ({ value: item.code, label: item.label }))
        );
        setBudgetFormat(
            budgetFormat.map((item) => ({ id: item.id, value: item.code, label: item.label }))
        );
    };

    const getChangeValue = async (data: BudgetFormData) => {
        const changedFields: Partial<BudgetFormData> = {};

        if (data.budgetType !== initialValue?.budgetType && data.budgetType) {
            changedFields.budgetType = data.budgetType;
        }

        if (data.budgetFormat !== initialValue?.budgetFormat && data.budgetFormat) {
            changedFields.budgetFormat = data.budgetFormat;
        }

        if (data.title !== initialValue?.title && data.title.trim() !== "") {
            changedFields.title = data.title;
        }

        if (data.description !== initialValue?.description && data.description?.trim() !== "") {
            changedFields.description = data.description;
        }

        // only attact when create only
        if (createState) {
            changedFields.version = 1;
        }

        return Object.keys(changedFields).length > 0 ? changedFields : null;
    };

    const onSubmit = async (data: BudgetFormData): Promise<{ success: boolean; id?: string }> => {
        const changedValue = await getChangeValue(data);

        if (!changedValue) {
            toast({
                title: "Success",
                description: "Your changes has been saved"
            });
            return { success: true };
        }

        const response = await submitBudgetRevisions(changedValue, budgetRevisionId);

        if (!response.success && !response.responseId) {
            toast({
                title: "Error",
                description: "Failed to create budget",
                variant: "destructive"
            });
            return { success: false };
        }

        toast({
            title: "Success",
            description: createState ? "Budget created successfully!" : "Budget updated successfully!"
        });

        return { success: true, id: response.responseId };
    }

    const handleParentCall = async (callFromParent?: boolean) => {
        const isValid = await form.trigger();
        if (!isValid) {
            toast({
                title: "Error",
                description: "Please complete all required fields.",
                variant: "destructive"
            });
            return;
        }

        const formData = await form.getValues();

        const result = await onSubmit(formData);

        if (result.success) {
            if (callFromParent) {
                if (createState) {
                    router.push(`/budgets/edit-budget?id=${result.id}`);
                } else if (submitForReview) {
                    submitForReview();
                }
            } else {
                setInitialValue(formData);
            }
        }
    }

    return {
        budgetType,
        budgetFormat,
        form,
        handleParentCall,
    }
};