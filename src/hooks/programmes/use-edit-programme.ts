"use client"

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFieldArray, useForm } from "react-hook-form";
import type { ProgrammeDetails, ProfitSharingDetail, FileWithPreview } from "@/types/programmes/programme.types";
import type { BudgetReviewSimpleEventProps, BudgetReviewComplexProps } from "@/types/budgets/budgets.types";
import { useToast } from "@/hooks/use-toast";
import {
    getSpecificProgramme,
    getProfitSharingDetails,
    getPointTypes,
    getProgrammePoints,
    getVenues,
    getOrganizingDepartment,
    getCommitteesData,
    getStaffData,
    uploadProgrammeImage,
    getProgrammeCertificateAndImage,
    uploadProgrammeCertificate,
} from "@/services/programmes/events-client.service";
import { getAllBudgetVersion, getBudgetDataForProgramme, getLatestBudgetId, useVerifyPageDestinationOnBudget } from "@/services/budgets/budget.service";
import { deleteEventDetailPoint, deleteEventSchedules, deleteProgramme, setProgrammeArchived, terminateProgramme, updateApprovedEventData } from "@/services/programmes/events-server.service";
import { ApplicationService } from "@/services/applications/application.service";
import { getTaxFromConfig } from "@/services/finance/invoices.service";
import { formatFileSize } from "@/lib/utils";

const ACCEPTED_FILE_TYPES = [
    { mime: "image/jpeg", ext: ".jpg,.jpeg", label: "JPG/JPEG" },
    { mime: "image/png", ext: ".png", label: "PNG" },
];

const MAX_FILE_SIZE = 4.5 * 1024 * 1024; // 4.5MB

const programmePointItemSchema = z.object({
    id: z.string().nullable(),
    point_type_id: z.string().nullable(),
    points: z.coerce.number().nullable(),
}).superRefine((val, ctx) => {
    if (val.point_type_id || val.points) {
        if (!val.point_type_id) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Point Type is required',
                path: ['point_type_id'],
            });
        }

        if (val.points && val.points < 0) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Points must be a number 0 or higher',
                path: ['points'],
            });
        }
    }
})

const programmePricingItemSchema = z.object({
    id: z.string(),
    name: z.string().min(1, "Price type name is required"),
    description: z.string().optional(),
    requires_membership: z.boolean(),
    target_membership: z.array(z.string()).optional()
}).superRefine((val, ctx) => {
    if (val.requires_membership && (!val.target_membership || val.target_membership.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Membership Types are required when a membership price is enabled.',
            path: ['target_membership']
        })
    }
})

const editProgrammeSchema = z.object({
    id: z.string().optional(),
    name: z
        .string()
        .max(255, "Max 255 characters allowed")
        .min(1, "Programme name is required"),
    description: z.string().optional(),
    maxParticipants: z.coerce.number().min(1, "Maximum participants is required and must be a number"),
    registrationStartDate: z.date().optional(),
    registrationEndDate: z.date().optional(),
    venueId: z.string().min(1, "Venue is required"),
    departmentId: z
        .string()
        .min(1, "Organizing department is required"),
    staffInChargeId: z
        .string()
        .min(1, "Staff in charge is required"),
    committeeId: z.string().optional(),
    secondary_committeeId: z.string().optional(),
    schedules: z
        .array(
            z.object({
                id: z.string().nullable(),
                dayNumber: z.coerce.number().min(0, "day number must be non-negative"),
                date: z.date().nullable(),
                startTime: z.string().min(1, "Start time is required"),
                endTime: z.string().min(1, "End time is required"),
            })
        ),
    programmePoint: z.array(programmePointItemSchema),
    programmePricing: z.array(programmePricingItemSchema),
    image: z.custom<FileWithPreview>().optional(),
    certificate: z.custom<FileWithPreview>().optional(),
}).superRefine((val, ctx) => {
    if (!val.registrationStartDate) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Start date is required',
            path: ['registrationStartDate']
        })
    }

    if (!val.registrationEndDate) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'End date is required',
            path: ['registrationEndDate']
        })
    }

    val.schedules?.forEach((entry, index) => {
        if (!entry.date) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Date is required",
                path: ["schedules", index, "date"],
            });
        }
    });
})

const editProgrammeWrapper = z.object({
    wrapper: editProgrammeSchema
})

export type EditProgrammeFormData = z.infer<typeof editProgrammeWrapper>['wrapper']

export const useEditProgramme = (id: string | null) => {
    const router = useRouter()
    const { toast } = useToast()
    const [isLoading, setIsLoading] = useState(true)
    const [isReadOnly, setReadOnly] = useState(false)
    const [disableUpdateStatus, setDisableUpdateStatus] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false)
    const [isArchiving, setArchiving] = useState(false)
    const [terminateValue, setTerminateValue] = useState('')
    const verifyPageDestination = useVerifyPageDestinationOnBudget()

    const [initialValues, setInitialValues] = useState<EditProgrammeFormData | null>(null)
    const [programmeDetails, setProgrammeDetails] = useState<ProgrammeDetails | null>(null)
    const [profitSharingData, setProfitSharingData] = useState<ProfitSharingDetail | null>(null)
    const [pointType, setPointType] = useState<{ id: string; name: string }[]>([])
    const [simpleItemData, setSimpleItemData] = useState<BudgetReviewSimpleEventProps | null>(null)
    const [complexItemData, setComplexItemData] = useState<BudgetReviewComplexProps | null>(null)
    const form = useForm<EditProgrammeFormData>({
        resolver: zodResolver(editProgrammeSchema),
        mode: "onChange",
    })
    const { fields: scheduleFields, append: appendSchedule, remove: removeSchedule } = useFieldArray({
        control: form.control,
        name: "schedules",
        keyName: "fieldId",
    })
    const { fields: pointFields, append: appendPoint, remove: removePoint } = useFieldArray({
        control: form.control,
        name: "programmePoint",
        keyName: "fieldId",
    })
    const [pointToDelete, setPointToDelete] = useState<{ id: string }[]>([])
    const [schedulesToDelete, setSchedulesToDelete] = useState<{ id: string }[]>([])
    const [selectedBudgetVersion, setSelectedBudgetVersion] = useState<string>()
    const [budgetVersion, setBudgetVersion] = useState<{ id: string, version: number, status: string, is_archived: boolean }[]>([])
    const [venue, setVenue] = useState<{ id: string; name: string }[]>([])
    const [department, setDepartment] = useState<{ id: string; name: string }[]>([])
    const [allStaff, setAllStaff] = useState<{ id: string; name: string; department_id: string }[]>([])
    const [filteredStaff, setFilteredStaff] = useState<{ id: string; name: string }[]>([])
    const [committees, setCommittees] = useState<{ id: string; name: string }[]>([])
    const [membershipTypes, setMembershipTypes] = useState<{ value: string; label: string }[]>([])
    const [taxRate, setTaxRate] = useState<number | null>(null)

    useEffect(() => {
        loadFormData();
        loadDisplayData();
    }, [id])

    useEffect(() => {
        if (selectedBudgetVersion) {
            loadBudgetItems(selectedBudgetVersion);
        }
    }, [selectedBudgetVersion])

    useEffect(() => {
        if (allStaff) {
            handleDepartmentChange(form.getValues('departmentId'));
        }
    }, [allStaff, form.watch("departmentId")])

    const loadFormData = async (): Promise<void> => {
        if (!id) {
            toast({
                title: "Error",
                description: "Programme ID is required",
                variant: "destructive",
            });
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);

            const [
                programmeDetailsResponse,
                programmePointsResponse,
                imageAndCertResponse
            ] = await Promise.all([
                getSpecificProgramme(id),
                getProgrammePoints(id),
                getProgrammeCertificateAndImage(id)
            ]);

            if (!programmeDetailsResponse) {
                toast({
                    title: "Error",
                    description: "Programme not found",
                    variant: "destructive",
                });
                setIsLoading(false);
                return;
            }

            // Handle programme status
            if (["COMPLETED", "TERMINATED"].includes(programmeDetailsResponse.programme?.status ?? '')) {
                setReadOnly(true);
            }

            setProgrammeDetails(programmeDetailsResponse);

            // Prepare form values
            const formValues = {
                id: programmeDetailsResponse.programme?.id || '',
                name: programmeDetailsResponse.programme?.name || '',
                description: programmeDetailsResponse.programme?.description || '',
                maxParticipants: programmeDetailsResponse.programme?.max_participants || 0,
                registrationStartDate: programmeDetailsResponse.programme?.registration_start_date ? new Date(programmeDetailsResponse.programme?.registration_start_date) : undefined,
                registrationEndDate: programmeDetailsResponse.programme?.registration_end_date ? new Date(programmeDetailsResponse.programme.registration_end_date) : undefined,
                venueId: programmeDetailsResponse.programme?.venue_id || '',
                departmentId: programmeDetailsResponse.programme?.organizing_dept_id || '',
                staffInChargeId: programmeDetailsResponse.programme?.staff_in_charge_id || '',
                committeeId: programmeDetailsResponse.programme?.committee_id || '',
                secondary_committeeId: '',
                schedules: programmeDetailsResponse.programmeSchedules && programmeDetailsResponse.programmeSchedules.length > 0
                    ? programmeDetailsResponse.programmeSchedules.map(schedule => ({
                        id: schedule.id || null,
                        dayNumber: schedule.day_number,
                        date: schedule.date ? new Date(schedule.date) : null,
                        startTime: schedule.start_time || '',
                        endTime: schedule.end_time || '',
                    }))
                    : [{ id: null, dayNumber: 1, date: null, startTime: '', endTime: '' }],
                programmePoint: programmePointsResponse.length > 0
                    ? programmePointsResponse.map(point => ({
                        id: point.id || null,
                        point_type_id: point.point_type_id || null,
                        points: point.points || null,
                    }))
                    : [{ id: null, point_type_id: null, points: null }],
                programmePricing: programmeDetailsResponse.programmePricing && programmeDetailsResponse.programmePricing.length > 0
                    ? programmeDetailsResponse.programmePricing.map(pricing => ({
                        id: pricing.id,
                        name: pricing?.name || '',
                        description: pricing.description || '',
                        requires_membership: pricing?.requires_membership || false,
                        target_membership: pricing.pricing_memberships ?
                            pricing.pricing_memberships.map((item: any) => item.membership_type_id).filter((item: any) => item != null)
                            : [],
                    }))
                    : [],
                image: imageAndCertResponse.image || undefined,
                certificate: imageAndCertResponse.certificate || undefined
            };

            form.reset(formValues);
            setInitialValues(formValues);

            const isValid = await form.trigger(undefined, { shouldFocus: false });
            await form.clearErrors();

            if (isValid) {
                setDisableUpdateStatus(true);
            }

            if (programmeDetailsResponse?.programme?.approved_budget_revision_id) {
                setSelectedBudgetVersion(programmeDetailsResponse?.programme?.approved_budget_revision_id);
            }
        } catch {
            toast({
                title: "Error",
                description: "Failed to load programme form data",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    }

    const loadDisplayData = async (): Promise<void> => {
        if (!id) {
            toast({
                title: "Error",
                description: "Programme ID is required",
                variant: "destructive",
            });
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);

            // Load all data in parallel
            const [
                pointTypeResponse,
                profitSharingResponse,
                budgetRevisionsResponse,
                venueData,
                departmentData,
                committeesData,
                allStaffData,
                membershipTypeData,
                taxRateData,
            ] = await Promise.all([
                getPointTypes(),
                getProfitSharingDetails(id),
                getAllBudgetVersion(undefined, id),
                getVenues(),
                getOrganizingDepartment(),
                getCommitteesData(),
                getStaffData(),
                ApplicationService.getMembershipTypes(),
                getTaxFromConfig(),
            ]);

            setPointType(pointTypeResponse);
            setProfitSharingData(profitSharingResponse);
            setBudgetVersion(budgetRevisionsResponse);
            setVenue(venueData);
            setDepartment(departmentData);
            setCommittees(committeesData);
            setAllStaff(allStaffData);
            setMembershipTypes(
                membershipTypeData.map((item: { id: string; name: string }) => ({
                    value: item.id,
                    label: item.name,
                }))
            );
            setTaxRate(taxRateData);
        } catch {
            toast({
                title: "Error",
                description: "Failed to load programme data",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    }

    const loadBudgetItems = async (id: string) => {
        const result = await getBudgetDataForProgramme(id)

        if (result?.format === 'COMPLEX' && result.budgetData) {
            setComplexItemData(result.budgetData)
        } else if (result?.format === 'SIMPLE' && result.budgetData) {
            setSimpleItemData(result.budgetData)
        }
    }

    const addProgrammePoint = () => {
        appendPoint({ id: null, point_type_id: null, points: null })
    }

    const addDateTime = () => {
        appendSchedule({
            id: null,
            dayNumber: scheduleFields.length + 1,
            date: null,
            startTime: '',
            endTime: ''
        });
    }

    const removeDateTime = (index: number, entryId?: string) => {
        if (entryId) {
            setSchedulesToDelete((prevItems) => [...prevItems, { id: entryId }]);
        }

        removeSchedule(index);
    }

    const removeProgrammePoint = (index: number, entryId?: string) => {
        if (entryId) {
            setPointToDelete((prevItems) => [...prevItems, { id: entryId }])
        }

        removePoint(index)

        if (pointFields.length === 1) {
            form.reset({ ...form.getValues(), programmePoint: [{ id: null, point_type_id: null, points: null }] });
        }
    }

    const isPointTypeSelected: (pointTypeId: string, currentIndex: number) => boolean = (
        pointTypeId,
        currentIndex
    ) =>
        pointFields.some(
            (entry, index) => index !== currentIndex && entry.point_type_id === pointTypeId
        )

    const handleDepartmentChange = (departmentId: string) => {
        if (departmentId) {
            const staffForDepartment = allStaff.filter(staff => staff.department_id === departmentId);
            setFilteredStaff(staffForDepartment);
        }
    }

    const handleImageChange = async (file: File) => {
        // Check file type
        if (!ACCEPTED_FILE_TYPES.some(type => type.mime === file.type)) {
            toast({
                variant: "destructive",
                title: "Unsupported file type",
                description: `File ${file.name} is not a supported format.`,
            });
            return;
        }

        // Check file size
        if (file.size > MAX_FILE_SIZE) {
            toast({
                variant: "destructive",
                title: "File too large",
                description: `File ${file.name} exceeds ${formatFileSize(MAX_FILE_SIZE)} limit.`,
            });
            return;
        }

        const previewUrl = URL.createObjectURL(file);

        const fileWithPreview = Object.assign(file, {
            isExisting: false,
            preview: previewUrl
        });

        form.setValue("image", fileWithPreview, {
            shouldDirty: true,
            shouldValidate: true,
        });
    }

    const setCertificate = (updatedFile: FileWithPreview | undefined) => {
        form.setValue("certificate", updatedFile, {
            shouldDirty: true,
            shouldValidate: true,
        });
    }

    const getChangedValues = (
        data: EditProgrammeFormData,
        initialValues: EditProgrammeFormData | null
    ) => {
        if (!initialValues) return {};
        const changedValues: Record<string, unknown> = {};

        const skipFields: (keyof EditProgrammeFormData)[] = ['image', 'certificate'];

        // Compare each key, using deep comparison for complex objects
        for (const key of Object.keys(data)) {
            const typedKey = key as keyof EditProgrammeFormData;

            if (skipFields.includes(typedKey)) continue;

            if (JSON.stringify(data[typedKey]) !== JSON.stringify(initialValues[typedKey])) {
                changedValues[typedKey] = data[typedKey];
            }
        }

        // Add id if there are any changes
        if (Object.keys(changedValues).length > 0 && data.id) {
            changedValues['id'] = data.id;
        }

        return changedValues;
    }

    const onSubmit = async (data: EditProgrammeFormData) => {
        if (!id) {
            throw new Error("Programme ID is required to perform the update.");
        }

        try {
            let isDataUpdated = false;

            // Handle point deletion
            if (pointToDelete.length > 0) {
                await deleteEventDetailPoint(pointToDelete);
                setPointToDelete([]);
                isDataUpdated = true;
            }

            // Handle schedule deletion
            if (schedulesToDelete.length > 0) {
                await deleteEventSchedules(schedulesToDelete);
                setSchedulesToDelete([]);
                isDataUpdated = true;
            }

            // Handle event data update
            const changedEventData = getChangedValues(data, initialValues);
            if (Object.keys(changedEventData).length > 0) {
                await updateApprovedEventData(changedEventData);
                isDataUpdated = true;
            }

            // Handle image upload
            if (data.image && !data.image.isExisting) {
                await uploadProgrammeImage(data.image, id);
                isDataUpdated = true;
            }

            if (data.certificate && !data.certificate.isExisting) {
                await uploadProgrammeCertificate(data.certificate, id);
                isDataUpdated = true;
            }

            // Reload data if any updates occurred
            if (isDataUpdated) {
                setInitialValues(null);
                loadFormData();
                toast({
                    title: "Update Complete",
                    description: "All changes have been saved successfully.",
                });
            } else {
                toast({
                    title: "No Changes",
                    description: "No changes detected in the form or files.",
                });
            }
        } catch {
            toast({
                title: "Error",
                description: "Failed to process your request. Please try again later.",
                variant: "destructive",
            });
        }
    }

    const handleSubmit = async () => {
        const isValid = await form.trigger();

        if (!isValid) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Please fill in all required fields for Event Detail.",
            });
            return;
        }
        const values = form.getValues();
        onSubmit(values);
    }

    const handleTerminate = async () => {
        try {
            if (terminateValue === 'terminate' && id) {
                await terminateProgramme(id);

                toast({
                    title: "Success",
                    description: "Event terminated successfully.",
                });

                window.location.reload();
            }

        } catch (error) {
            toast({
                title: "Error",
                description: "An error occurred while terminating the events" + error,
            });
        }
    }

    const handleReviseBudget = async () => {
        if (!id) {
            toast({
                title: "Error",
                description: "Programme ID is required",
                variant: "destructive",
            });
            setIsLoading(false);
            return;
        }

        const result = await getLatestBudgetId(id, undefined);

        if (!result) {
            toast({
                title: "Error",
                description: "No budget Id found, please try again later.",
                variant: "destructive"
            });
            return;
        }

        verifyPageDestination(result)
    }

    const onDelete = async () => {
        if (!id) {
            toast({
                variant: 'destructive',
                title: "Error",
                description: "Programme ID is required.",
            });
            return
        }
        setIsDeleting(true)

        const response = await deleteProgramme(id)

        if (!response) {
            toast({
                variant: 'destructive',
                title: "Error",
                description: "An error occurred while deleting the programme. Please try again.",
            })
        } else {
            toast({
                title: "Success",
                description: "Programme has been successfully deleted.",
            })
            router.push('/programmes')
        }

        setIsDeleting(false)
    }

    const handleArchiveProgramme = async () => {
        if (!id) {
            toast({
                title: "Error",
                description: "Programme ID is required",
                variant: "destructive",
            })
            return;
        }

        setArchiving(true)
        const result = await setProgrammeArchived(id)
        setArchiving(false)

        if (!result) {
            toast({
                title: "Error",
                description: "Failed to archive the programme, please try again later.",
                variant: "destructive"
            })
            return
        }

        loadFormData()
    }

    return {
        form,
        programmeDetails,
        pointType,
        profitSharingData,
        scheduleFields,
        pointFields,
        isPointTypeSelected,
        handleImageChange,
        setCertificate,
        addDateTime,
        removeDateTime,
        addProgrammePoint,
        removeProgrammePoint,
        simpleItemData,
        complexItemData,
        selectedBudgetVersion,
        setSelectedBudgetVersion,
        budgetVersion,
        venue,
        department,
        filteredStaff,
        committees,
        membershipTypes,
        taxRate,
        terminateValue,
        setTerminateValue,
        isLoading,
        isReadOnly,
        disableUpdateStatus,
        isDeleting,
        isArchiving,
        handleSubmit,
        handleTerminate,
        handleReviseBudget,
        loadFormData,
        onDelete,
        handleArchiveProgramme,
    }
}
