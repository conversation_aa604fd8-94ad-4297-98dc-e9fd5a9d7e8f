"use client";

import { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { submitProgramme } from "@/services/programmes/events-server.service";
import {
    getCommitteesData,
    getOrganizingDepartment,
    getStaffData,
} from "@/services/programmes/events-client.service";
import { getVenues } from "@/services/programmes/events-client.service";
import { BudgetRevisionView } from "@/types/budgets/budgets.types";
import { searchBudgets } from "@/services/budgets/budget.service";

const programmeSchema = z.object({
    budget_revision_id: z.string().min(1, "Programme budget is required"),
    name: z
        .string()
        .max(255, "Max 255 characters allowed")
        .min(1, "Programme name is required"),
    description: z
        .string()
        .optional(),
    programmeType: z.string().min(1, "Programme type is required"),
    programmeFormat: z.string().min(1, "Programme format is required"),
    venueId: z.string().min(1, "Venue is required"),
    departmentId: z
        .string()
        .min(1, "Organizing department is required"),
    staffInChargeId: z
        .string()
        .min(1, "Staff in charge is required"),
    committeeId: z.string().optional(),
    secondary_committeeId: z.string().optional(),
    remark: z.string().optional(),
    thirdPartyCheck: z.boolean(),
    thirdPartyEntity: z.string().optional(),
    schedules: z
        .array(
            z.object({
                dayNumber: z.coerce.number().min(0, "day number must be non-negative"),
                date: z.date().optional(),
                startTime: z.string().min(1, "Start time is required"),
                endTime: z.string().min(1, "End time is required"),
            })
        ),
}).superRefine((data, ctx) => {
    if (data.thirdPartyCheck && (!data.thirdPartyEntity || data.thirdPartyEntity.trim() === "")) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Third party entity is required when third party check is true",
            path: ["thirdPartyEntity"],
        });
    }

    data.schedules?.forEach((entry, index) => {
        if (!entry.date) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: "Date is required",
                path: ["schedules", index, "date"],
            });
        }
    });
});

const wrappedProgrammeSchema = z.object({
    programme: programmeSchema,
});

export type AddProgrammeFormData = z.infer<typeof wrappedProgrammeSchema>['programme'];

export const useAddProgramme = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [venue, setVenue] = useState<{ id: string; name: string }[]>([]);
    const [department, setDepartment] = useState<{ id: string; name: string }[]>([]);
    const [allStaff, setAllStaff] = useState<{ id: string; name: string; department_id: string }[]>([]);
    const [filteredStaff, setFilteredStaff] = useState<{ id: string; name: string }[]>([]);
    const [committees, setCommittees] = useState<{ id: string; name: string }[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [page, setPage] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(0);
    const [searchResults, setSearchResults] = useState<BudgetRevisionView[]>([]);
    const [selectedBudget, setSelectedBudget] = useState<BudgetRevisionView | null>(null);

    const { toast } = useToast();
    const router = useRouter();
    const form = useForm<AddProgrammeFormData>({
        resolver: zodResolver(programmeSchema),
        mode: 'onChange',
        defaultValues: {
            budget_revision_id: '',
            name: '',
            description: '',
            programmeType: '',
            programmeFormat: '',
            venueId: '',
            departmentId: '',
            staffInChargeId: '',
            committeeId: '',
            secondary_committeeId: '',
            remark: '',
            thirdPartyCheck: false,
            thirdPartyEntity: '',
            schedules: [{ dayNumber: 0, date: undefined, startTime: "", endTime: "" }],
        },
    });
    const { fields: scheduleFields, append: appendSchedule, remove: removeSchedule } = useFieldArray({
        control: form.control,
        name: "schedules",
        keyName: "fieldId",
    });
    const isSharing = form.watch("thirdPartyCheck");

    useEffect(() => {
        const loadFormData = async () => {
            try {
                setIsLoading(true);

                const [venueData, departmentData, committeesData, allStaffData] = await Promise.all([
                    getVenues(),
                    getOrganizingDepartment(),
                    getCommitteesData(),
                    getStaffData(),
                ]);

                if (venueData) setVenue(venueData);
                if (departmentData) setDepartment(departmentData);
                if (committeesData) setCommittees(committeesData);
                if (allStaffData) setAllStaff(allStaffData);
            } catch (error) {
                toast({ variant: 'destructive', title: 'Error', description: 'Failed to load necessary data.' + error });
            } finally {
                setIsLoading(false);
            }
        };

        loadFormData();
    }, []);

    const onBudgetSearch = async (pageToLoad = page) => {
        setIsSearching(true);

        const result = await searchBudgets(searchQuery, pageToLoad, "PROGRAMME");
        if (result.success) {
            setSearchResults(result.budget);
            setPage(result.currentPage);
            setTotalPages(result.totalPages);
        }
        setIsSearching(false);
    }

    useEffect(() => {
        onBudgetSearch();
    }, [searchQuery])

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            onBudgetSearch(newPage);
        }
    };

    const handleDepartmentChange = (departmentId: string) => {
        if (departmentId) {
            const staffForDepartment = allStaff.filter(staff => staff.department_id === departmentId);
            setFilteredStaff(staffForDepartment);
        }
    };

    const addDateTime = () => {
        appendSchedule({ dayNumber: scheduleFields.length + 1, date: undefined, startTime: "", endTime: "" })
    };

    const removeDateTime = (index: number) => {
        removeSchedule(index);
    };

    const onSubmit = async (data: AddProgrammeFormData) => {
        setIsSubmitting(true);

        const result = await submitProgramme(data);

        setIsSubmitting(false);

        if (!result.success) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "An error occurred while creating the programme.",
            });
        } else {
            toast({
                title: "Success",
                description: "Programme detail successfully saved.",
            });

            router.push(`/programmes/${result.programmeId}/edit`)
        }
    };

    const handleSubmit = async () => {
        const isValid = await form.trigger();

        if (!isValid) {
            toast({
                variant: "destructive",
                title: "Validation Error",
                description: "Please fill in all required fields for Programme Detail.",
            });
            return;
        }
        const values = form.getValues();
        onSubmit(values);
    }

    return {
        form,
        isLoading,
        isSubmitting,
        venue,
        department,
        filteredStaff,
        committees,
        scheduleFields,
        isSharing,
        isSearching,
        searchQuery,
        setSearchQuery,
        page,
        totalPages,
        searchResults,
        selectedBudget,
        setSelectedBudget,
        handlePageChange,
        handleDepartmentChange,
        handleSubmit,
        addDateTime,
        removeDateTime,
    }
};