"use client";

import { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { nanoid } from "nanoid";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { purchaseOrderSchema, type PurchaseOrderFormData } from "@/schemas/purchase-orders";
import type { PurchaseOrder } from "@/types/purchase-orders/purchase-orders.types";
import type { CurrencyOption, DropdownOption, FileWithPreview, GlAccountOption, TaxCodeOption } from "@/types/general.types";
import { getEntities, getCurrencies, getUnitsOfMeasure, getTaxCodes, getGLAccounts, getExchangeRate, checkPOEditPermissions, submitPoDocuments } from "@/services/purchase-orders/purchase-orders.service";
import { getActiveSuppliers } from "@/services/purchase-orders/suppliers.service";
import { createPurchaseOrder, updatePurchaseOrder, submitPurchaseOrderForApproval } from "@/services/purchase-orders/purchase-orders-server.service";
import { getBudgetItems } from "@/services/budgets/budget.service";
import { AuthService } from "@/services/auth.service";
import { getOrganizingDepartment } from "@/services/programmes/events-client.service";

export const usePurchaseOrderForm = (
  isEditing: boolean = false,
  purchaseOrderId?: string,
  initialData?: PurchaseOrder,
  attachments?: FileWithPreview[],
  loadPurchaseOrder?: (purchaseOrderId: string) => void
) => {
  const { toast } = useToast();
  const router = useRouter();

  // Dropdown options state
  const [entities, setEntities] = useState<DropdownOption[]>([]);
  const [suppliers, setSuppliers] = useState<DropdownOption[]>([]);
  const [currencies, setCurrencies] = useState<CurrencyOption[]>([]);
  const [uoms, setUoms] = useState<DropdownOption[]>([]);
  const [taxCodes, setTaxCodes] = useState<TaxCodeOption[]>([]);
  const [budgetItems, setBudgetItems] = useState<DropdownOption[]>([]);
  const [glAccounts, setGlAccounts] = useState<GlAccountOption[]>([]);
  const [departments, setDepartments] = useState<DropdownOption[]>([]);

  // Form state
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const form = useForm<PurchaseOrderFormData>({
    resolver: zodResolver(purchaseOrderSchema),
    mode: "onChange",
    defaultValues: initialData
      ? {
        entity_id: initialData.entity_id,
        title: initialData.title,
        description: initialData.description || "",
        supplier_id: initialData.supplier_id,
        po_date: new Date(initialData.po_date),
        expected_delivery_date: initialData.expected_delivery_date
          ? new Date(initialData.expected_delivery_date)
          : undefined,
        currency_code: initialData.currency_code,
        exchange_rate: initialData.exchange_rate,
        budget_revision_id: initialData.budget_revision_id || "",
        department_code: initialData.department_code || "",
        project_reference: initialData.project_reference || "",
        delivery_address: initialData.delivery_address,
        delivery_instructions: initialData.delivery_instructions || "",
        items:
          initialData.items?.map(item => ({
            field_id: nanoid(),
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            item_code: item.item_code || "",
            uom: item.unit_of_measure,
            discount_percentage: item.discount_percentage || 0,
            tax_code: item.tax_code || undefined,
            tax_rate: item.tax_rate || 0,
            gl_account_code: item.gl_account_code || "",
            budget_item_id: item.budget_item_id || "",
          })) || [],
        files: attachments ? attachments : []
      } : {
        entity_id: "",
        title: "",
        description: "",
        supplier_id: "",
        po_date: new Date(),
        expected_delivery_date: undefined,
        currency_code: "SGD",
        exchange_rate: 1,
        budget_revision_id: "",
        department_code: "",
        project_reference: "",
        delivery_address: undefined,
        delivery_instructions: "",
        items: [
          {
            field_id: nanoid(),
            description: "",
            quantity: 1,
            unit_price: 0,
            item_code: "",
            uom: "",
            discount_percentage: 0,
            tax_code: "",
            tax_rate: 0,
            gl_account_code: "",
            budget_item_id: ""
          },
        ],
        files: []
      },
  });
  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "items",
    keyName: "field_id"
  });
  const files = form.watch("files") || [];
  const watchedCurrency = form.watch("currency_code");
  const watchedBudgetRevision = form.watch("budget_revision_id");

  useEffect(() => {
    const init = async () => {
      if (purchaseOrderId && isEditing) {
        const result = await checkPOEditPermissions(purchaseOrderId);

        if (!result) {
          router.push(`/purchase-orders/${purchaseOrderId}`);
          return; // Optional: prevent fetchFormOptions if redirecting
        }
      }

      fetchFormOptions();
    };

    init();
  }, []);

  // Update exchange rate when currency changes
  useEffect(() => {
    const updateExchangeRate = async (currency: string) => {
      const rate = await getExchangeRate(currency, "USD");
      form.setValue("exchange_rate", rate);
    };

    if (watchedCurrency) {
      updateExchangeRate(watchedCurrency);
    }
  }, [watchedCurrency]);

  // Update budget items when budget revision changes
  useEffect(() => {
    const fetchBudgetItems = async (budgetRevisionId: string) => {
      const items = await getBudgetItems(budgetRevisionId);
      setBudgetItems(items);
    };

    if (watchedBudgetRevision) {
      fetchBudgetItems(watchedBudgetRevision);
    } else {
      setBudgetItems([]);
      const currentItems = form.getValues("items");
      currentItems.forEach((item, index) => {
        if (item.budget_item_id !== "none") {
          form.setValue(`items.${index}.budget_item_id`, "");
        }
      });
    }
  }, [watchedBudgetRevision]);

  const fetchFormOptions = async () => {
    setLoading(true);
    try {
      const [
        entitiesData,
        suppliersData,
        currenciesData,
        uomsData,
        taxCodesData,
        glAccountsData,
        departmentData
      ] = await Promise.all([
        getEntities(),
        getActiveSuppliers(),
        getCurrencies(),
        getUnitsOfMeasure(),
        getTaxCodes(),
        getGLAccounts(),
        getOrganizingDepartment()
      ]);

      setEntities(entitiesData);
      setSuppliers(suppliersData);
      setCurrencies(currenciesData);
      setUoms(uomsData);
      setTaxCodes(taxCodesData);
      setGlAccounts(glAccountsData);
      setDepartments(
        departmentData.map((dept) => ({
          value: dept.id,
          label: dept.name,
        }))
      );
    } catch {
      toast({
        title: "Error",
        description: "Failed to load form options",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const setFiles = (updatedFiles: FileWithPreview[]) => {
    form.setValue("files", updatedFiles, {
      shouldDirty: true,
      shouldValidate: true,
    });
  };

  const addLineItem = () => {
    append({
      field_id: nanoid(),
      description: "",
      quantity: 1,
      unit_price: 0,
      item_code: "",
      uom: "",
      discount_percentage: 0,
      tax_code: undefined,
      tax_rate: 0,
      gl_account_code: "",
      budget_item_id: ""
    });
  };

  const removeLineItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const calculateLineTotal = (item: {
    quantity: number;
    unit_price: number;
    tax_rate: number;
    discount_percentage?: number;
  }): number => {
    if (!item) return 0;

    const subtotal = item.quantity * item.unit_price;
    const discountAmount = subtotal * ((item.discount_percentage || 0) / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (item.tax_rate / 100);

    return afterDiscount + taxAmount;
  };

  const calculateTotals = () => {
    const items = form.watch("items");
    let subtotal = 0;
    let totalTax = 0;

    items.forEach((item) => {
      const itemSubtotal = item.quantity * item.unit_price;
      const discountAmount = itemSubtotal * ((item.discount_percentage || 0) / 100);
      const afterDiscount = itemSubtotal - discountAmount;
      const taxAmount = afterDiscount * (item.tax_rate / 100);

      subtotal += afterDiscount;
      totalTax += taxAmount;
    });

    return {
      subtotal,
      totalTax,
      totalAmount: subtotal + totalTax,
    };
  };

  const handleFileSubmission = async (newFiles: FileWithPreview[], poId: string) => {
    const uploadResult = await submitPoDocuments(newFiles, poId);
    return uploadResult;
  };

  const onSaveDraft = async (data: PurchaseOrderFormData) => {
    const userId = await AuthService.getCurrentUser();

    if (!userId) {
      toast({
        title: "Error",
        description: "Logged-in user not found. Please ensure you are authenticated.",
        variant: "destructive",
      });
      return { success: false, error: "User not authenticated" };
    }

    let result: {
      success: boolean;
      newId?: string;
      error?: string;
    };

    if (isEditing && purchaseOrderId) {
      result = await updatePurchaseOrder(purchaseOrderId, data);
    } else {
      result = await createPurchaseOrder(data, userId.id);
    }

    return result;
  };

  const onSubmitForApproval = async (purchaseOrderId: string) => {
    return await submitPurchaseOrderForApproval(purchaseOrderId);
  };

  const handleSubmission = async (toApproval: boolean, data: PurchaseOrderFormData) => {
    setSubmitting(true);
    const dataToSave = {
      ...data,
      files: undefined,
    };

    const saveResult = await onSaveDraft(dataToSave);
    if (!saveResult.success) {
      toast({
        title: "Error",
        description: saveResult.error || "Failed to save purchase order",
        variant: "destructive",
      });
      setSubmitting(false);
      return;
    }

    const poId = isEditing ? purchaseOrderId : saveResult.newId;

    if (poId) {
      const newFiles = (data.files || []).filter(file => !file.isExisting);

      if (newFiles.length !== 0) {
        const fileResult = await handleFileSubmission(newFiles, poId);

        if (!fileResult.success) {
          const errorList = (fileResult.errors || [])
            .map(err => `• ${err.fileName}: ${err.message}`)
            .join("\n");

          toast({
            variant: "destructive",
            title: "Some attachments failed to upload",
            description: errorList || "Some attachments failed to upload.",
          });
          setSubmitting(false);
          return;
        }

        toast({
          title: "Success",
          description: "Attachments uploaded successfully!",
        });
      }
    }

    if (toApproval && poId) {
      const submitResult = await onSubmitForApproval(poId);
      if (submitResult.success) {
        toast({
          title: "Success",
          description: "Purchase order submitted for approval successfully",
        });
        router.push(`/purchase-orders/${poId}`);
      } else {
        toast({
          title: "Error",
          description: submitResult.error || "Failed to submit for approval",
          variant: "destructive",
        });
      }
    } else if (!toApproval) {
      toast({
        title: "Success",
        description: isEditing ? "Purchase order updated successfully" : "Purchase order created successfully",
      });
      if (!isEditing && saveResult.newId) {
        router.push(`/purchase-orders/${saveResult.newId}`);
      } else {
        if (loadPurchaseOrder && poId) {
          loadPurchaseOrder(poId);
        } else if (poId) {
          router.push(`/purchase-orders/${poId}`);
        }
      }
    }

    setSubmitting(false);
  };

  return {
    form,
    fields,
    watchedCurrency,
    loading,
    submitting,
    files,
    setFiles,
    items: {
      append,
      remove,
      update
    },
    options: {
      entities,
      suppliers,
      currencies,
      uoms,
      taxCodes,
      budgetItems,
      glAccounts,
      departments
    },
    actions: {
      addLineItem,
      removeLineItem,
      handleSubmission,
    },
    calculations: {
      calculateLineTotal,
      calculateTotals
    }
  };
};