"use client";

import * as React from "react";
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useInvoiceColumns } from "@/components/pages/invoices/columns";
import { exportInvoices, getAllInvoices } from "@/services/finance/invoices.service";
import { invoiceDetailsView } from "@/types/finance/invoices.types";
import { checkExportStatus, ExportJob } from "@/services/export";
import { useInterval } from "@/hooks/use-interval";
import { useToast } from "@/hooks/use-toast";
import { ApplicationService } from "@/services/applications/application.service";

export const useInvoicesTablePage = () => {
    const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
    const [membershipType, setMembershipType] = React.useState<{ value: string, label: string }[]>([]);
    const [searchTerm, setSearchTerm] = React.useState<string>();
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [isInitialized, setIsInitialized] = React.useState(false);
    const [loading, setLoading] = React.useState<boolean>(true);
    const [error, setError] = React.useState<string | null>(null);
    const [invoices, setInvoices] = React.useState<invoiceDetailsView[]>([]);
    const [isExporting, setIsExporting] = React.useState(false);
    const [exportJobId, setExportJobId] = React.useState<string | null>(null);
    const [exportStatus, setExportStatus] = React.useState<ExportJob | null>(null);
    const { toast } = useToast();
    const { columns, selectedRows, setSelectedRows } = useInvoiceColumns();
    const [selectAllRows, setSelectAllRows] = React.useState<Set<string>>(new Set());

    React.useEffect(() => {
        loadMembershipTypes();
    }, []);

    React.useEffect(() => {
        if (!isInitialized) return;
        loadInvoices();
    }, [pageSize, searchTerm, subFilters]);

    const loadMembershipTypes = async () => {
        setError(null);

        const membershipTypes = await ApplicationService.getMembershipTypes();
        if (membershipTypes.length > 0) {
            setMembershipType(
                membershipTypes.map(type => ({
                    value: type.id,
                    label: type.name,
                }))
            );
        } else {
            setError('No membership types found.');
        }

        setIsInitialized(true);
        loadInvoices();
    };

    const loadInvoices = async (pageToLoad = page) => {
        setLoading(true);
        setError(null);

        const { invoices, success, total, currentPage, allInvoiceIds } = await getAllInvoices(
            { subFilters },
            pageToLoad,
            pageSize,
            searchTerm
        );

        if (success && invoices) {
            setInvoices(invoices);
            setSelectAllRows(new Set(allInvoiceIds));
            setTotalCount(total);
            setPage(currentPage);

            // Calculate total pages based on the current pageSize
            const calculatedTotalPages = Math.ceil(total / pageSize);
            setTotalPages(calculatedTotalPages);

            // If the current page is out of bounds, reset to the last valid page
            if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
                setPage(calculatedTotalPages);
            }
        } else {
            setError("Failed to fetch invoices, please try again later.");
        }

        setLoading(false);
    };

    const handleStatusFilter = (selectedInvoiceStatus: string[], selectedMembershipTypes: string[]) => {
        setSubFilters((prev) => ({
            ...prev,
            status: selectedInvoiceStatus,
            membership_type: selectedMembershipTypes
        }));
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm(undefined);
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadInvoices(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
    };

    const handleExport = async () => {
        setIsExporting(true)
        setExportStatus(null)
        const result = await exportInvoices(Array.from(selectedRows))

        if (result.jobId) {
            setExportJobId(result.jobId)
            setExportStatus({ status: 'PENDING' })
        } else {
            toast({
                variant: "destructive",
                title: "Export Failed",
                description: "There was an error starting the export process."
            })
            setIsExporting(false)
        }
    };

    useInterval(
        async () => {
            if (!exportJobId) return

            try {
                const data = await checkExportStatus(exportJobId)
                setExportStatus(data)

                // Stop polling when completed or failed
                if (data.status === 'COMPLETED' || data.status === 'FAILED') {
                    setExportJobId(null)

                    // Auto download when completed
                    if (data.status === 'COMPLETED' && data.file_url) {
                        window.location.href = data.file_url
                        setIsExporting(false)
                    }
                }
            } catch (error) {
                console.error('Error checking export status:', error)
            }
        },
        exportJobId ? 2000 : null
    )

    const handleSelectAllRows = () => {
        // Set selected rows to the same Set as selectAllRows
        setSelectedRows(new Set(selectAllRows));
    };

    const table = useReactTable({
        data: invoices,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    return {
        table,
        loading,
        error,
        page,
        subFilters,
        membershipType,
        searchTerm,
        totalCount,
        totalPages,
        pageSize,
        isExporting,
        exportStatus,
        columns,
        selectedRows,
        setSearchTerm,
        handleSelectAllRows,
        setIsExporting,
        handleStatusFilter,
        resetFilters,
        handlePageChange,
        handleLimitChange,
        handleExport,
    };
};
