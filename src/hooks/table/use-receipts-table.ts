"use client";

import * as React from "react";
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    getCoreRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { exportReceipts, getAllReceipts } from "@/services/finance/receipts.service";
import { receiptsInterface } from "@/types/finance/receipts.types";
import { useReceiptColumns } from "@/components/pages/receipts/columns";
import { checkExportStatus, ExportJob } from "@/services/export";
import { useToast } from "@/hooks/use-toast";
import { useInterval } from "@/hooks/use-interval";

export const useReceiptsTablePage = () => {
    const [subFilters, setSubFilters] = React.useState<{ [key: string]: string[] }>({});
    const [searchTerm, setSearchTerm] = React.useState<string>();
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [page, setPage] = React.useState<number>(1);
    const [totalCount, setTotalCount] = React.useState<number>(0);
    const [totalPages, setTotalPages] = React.useState<number>(0);
    const [loading, setLoading] = React.useState<boolean>(true);
    const [error, setError] = React.useState<string | null>(null);
    const [receipts, setReceipts] = React.useState<receiptsInterface[]>([]);
    const [isExporting, setIsExporting] = React.useState(false);
    const [exportJobId, setExportJobId] = React.useState<string | null>(null);
    const [exportStatus, setExportStatus] = React.useState<ExportJob | null>(null);
    const { toast } = useToast();
    const { columns, selectedRows, setSelectedRows } = useReceiptColumns();
    const [selectAllRows, setSelectAllRows] = React.useState<Set<string>>(new Set());

    React.useEffect(() => {
        loadReceipts();
    }, [pageSize, searchTerm, subFilters]);

    const loadReceipts = async (pageToLoad = page) => {
        setLoading(true);
        setError(null);

        const { receipts, success, total, currentPage, allReceiptIds } = await getAllReceipts(
            { subFilters },
            pageToLoad,
            pageSize,
            searchTerm
        );

        if (success && receipts) {
            setReceipts(receipts);
            setSelectAllRows(new Set(allReceiptIds));
            setTotalCount(total);
            setPage(currentPage);

            // Calculate total pages based on the current pageSize
            const calculatedTotalPages = Math.ceil(total / pageSize);
            setTotalPages(calculatedTotalPages);

            // If the current page is out of bounds, reset to the last valid page
            if (pageToLoad > calculatedTotalPages && calculatedTotalPages > 0) {
                setPage(calculatedTotalPages);
            }
        } else {
            setError("Failed to fetch receipts, please try again later.");
        }

        setLoading(false);
    };

    const handleStatusFilter = (
        selectedPaymentMethod: string[],
        selectedPaymentStatus: string[]
    ) => {
        setSubFilters((prev) => ({
            ...prev,
            payment_method: selectedPaymentMethod,
            payment_status: selectedPaymentStatus
        }));
    };

    const resetFilters = () => {
        setSubFilters({});
        setColumnFilters([]);
        setSearchTerm(undefined);
    };

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setPage(newPage);
            loadReceipts(newPage);
        }
    };

    const handleLimitChange = (value: string) => {
        setPageSize(Number(value));
    };

    const handleExport = async () => {
        setIsExporting(true)
        setExportStatus(null)
        const result = await exportReceipts(Array.from(selectedRows))

        if (result) {
            setExportJobId(result.jobId)
            setExportStatus({ status: 'PENDING' })
        } else {
            toast({
                variant: "destructive",
                title: "Export Failed",
                description: "There was an error starting the export process."
            })
            setIsExporting(false)
        }
    };

    useInterval(
        async () => {
            if (!exportJobId) return

            try {
                const data = await checkExportStatus(exportJobId)
                setExportStatus(data)

                // Stop polling when completed or failed
                if (data.status === 'COMPLETED' || data.status === 'FAILED') {
                    setExportJobId(null)

                    // Auto download when completed
                    if (data.status === 'COMPLETED' && data.file_url) {
                        window.location.href = data.file_url
                        setIsExporting(false)
                    }
                }
            } catch (error) {
                console.error('Error checking export status:', error)
            }
        },
        exportJobId ? 2000 : null
    )

    const handleSelectAllRows = () => {
        // Set selected rows to the same Set as selectAllRows
        setSelectedRows(new Set(selectAllRows));
    };

    const table = useReactTable({
        data: receipts,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    return {
        table,
        loading,
        error,
        page,
        subFilters,
        searchTerm,
        totalCount,
        totalPages,
        pageSize,
        isExporting,
        exportStatus,
        columns,
        selectedRows,
        setSearchTerm,
        handleSelectAllRows,
        setIsExporting,
        handleStatusFilter,
        resetFilters,
        handlePageChange,
        handleLimitChange,
        handleExport,
    };
};
