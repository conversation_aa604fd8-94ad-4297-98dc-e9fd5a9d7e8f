import { useState, useEffect } from "react"
import { getPersonalInfoOptions } from "@/services/system-options.service"
import { type SystemOptions } from "@/types/members/options"

export function useSystemOptions() {
  const [options, setOptions] = useState<SystemOptions | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadOptions() {
      try {
        const data = await getPersonalInfoOptions()
        setOptions(data)
      } catch {
        setError('Failed to load system options')
      } finally {
        setIsLoading(false)
      }
    }

    loadOptions()
  }, [])

  return { options, isLoading, error }
} 