"use client";

import { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { creditNoteSchema, type CreditNoteFormData } from "@/schemas/credit-notes/credit-note.schema";
import type { 
  CreditNote, 
  DropdownOption
} from "@/types/credit-notes/credit-notes.types";
import {
  createCreditNote,
  getCreditReasons,
  getApplicationMethods
} from "@/services/credit-notes/credit-notes.service";

export const useCreditNoteForm = (
  isEditing: boolean = false,
  creditNoteId?: string,
  initialData?: CreditNote
) => {
  const { toast } = useToast();
  const router = useRouter();

  // Dropdown options state
  const [entities, setEntities] = useState<DropdownOption[]>([]);
  const [customers, setCustomers] = useState<DropdownOption[]>([]);
  const [currencies, setCurrencies] = useState<DropdownOption[]>([]);
  const [creditReasons, setCreditReasons] = useState<DropdownOption[]>([]);
  const [applicationMethods, setApplicationMethods] = useState<DropdownOption[]>([]);
  const [invoices, setInvoices] = useState<DropdownOption[]>([]);

  // Form state
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Prepare default values
  const getDefaultValues = (): CreditNoteFormData => {
    if (initialData) {
      return {
        entity_id: initialData.entity_id,
        customer_id: initialData.customer_id,
        invoice_date: initialData.invoice_date,
        due_date: initialData.due_date || undefined,
        currency_code: initialData.currency_code,
        reference_number: initialData.reference_number || undefined,
        notes: initialData.notes || undefined,
        credit_reason: initialData.credit_reason!,
        original_invoice_id: initialData.original_invoice_id || undefined,
        original_order_id: initialData.original_order_id || undefined,
        expiry_date: initialData.expiry_date || undefined,
        application_method: initialData.application_method!,
        application_restrictions: initialData.application_restrictions || undefined,
        items: initialData.items?.map(item => ({
          line_number: item.line_number,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount_amount: item.discount_amount || undefined,
          discount_percentage: item.discount_percentage || undefined,
          tax_percentage: item.tax_percentage,
          item_code: item.item_code || undefined,
          gl_account_code: item.gl_account_code || undefined,
        })) || []
      };
    }

    return {
      entity_id: "",
      customer_id: "",
      invoice_date: new Date().toISOString().split('T')[0],
      currency_code: "USD",
      credit_reason: "OTHER",
      application_method: "MANUAL",
      items: []
    };
  };

  // Initialize form
  const form = useForm<CreditNoteFormData>({
    resolver: zodResolver(creditNoteSchema),
    defaultValues: getDefaultValues(),
    mode: "onChange",
  });

  // Initialize field array for line items
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Load dropdown options
  useEffect(() => {
    const loadOptions = async () => {
      setLoading(true);
      try {
        // Load basic options
        const [reasonsData, methodsData] = await Promise.all([
          getCreditReasons(),
          getApplicationMethods()
        ]);

        setCreditReasons(reasonsData);
        setApplicationMethods(methodsData);

        // Mock entities and currencies for now
        setEntities([
          { value: "entity1", label: "Main Entity" },
          { value: "entity2", label: "Subsidiary Entity" }
        ]);

        setCurrencies([
          { value: "USD", label: "US Dollar (USD)" },
          { value: "EUR", label: "Euro (EUR)" },
          { value: "GBP", label: "British Pound (GBP)" }
        ]);

        // Mock customers for now
        setCustomers([
          { 
            value: "customer1", 
            label: "ABC Corporation",
            code: "CUST001",
            email: "<EMAIL>",
            phone: "******-0123",
            creditBalance: 500.00,
            outstandingBalance: 1250.00
          },
          { 
            value: "customer2", 
            label: "XYZ Industries",
            code: "CUST002",
            email: "<EMAIL>",
            phone: "******-0456",
            creditBalance: 0.00,
            outstandingBalance: 875.50
          }
        ]);

        // Mock invoices for now
        setInvoices([
          { value: "invoice1", label: "INV-2024-001 - $1,250.00" },
          { value: "invoice2", label: "INV-2024-002 - $875.50" }
        ]);

      } catch (error) {
        console.error("Error loading options:", error);
        toast({
          title: "Error",
          description: "Failed to load form options. Please refresh the page.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadOptions();
  }, [toast]);

  // Actions
  const addLineItem = () => {
    append({
      line_number: fields.length + 1,
      description: "",
      quantity: 1,
      unit_price: 0,
      tax_percentage: 0,
    });
  };

  const removeLineItem = (index: number) => {
    remove(index);
    // Update line numbers after removal
    setTimeout(() => {
      fields.forEach((_, idx) => {
        form.setValue(`items.${idx}.line_number`, idx + 1);
      });
    }, 0);
  };

  // Calculations
  const calculateLineTotal = (index: number): number => {
    const item = form.watch(`items.${index}`);
    if (!item) return 0;

    const { quantity = 0, unit_price = 0, discount_amount = 0, discount_percentage = 0, tax_percentage = 0 } = item;
    
    let subtotal = quantity * unit_price;
    
    // Apply discount
    if (discount_percentage > 0) {
      subtotal = subtotal * (1 - discount_percentage / 100);
    }
    if (discount_amount > 0) {
      subtotal = Math.max(0, subtotal - discount_amount);
    }
    
    // Add tax
    const taxAmount = subtotal * (tax_percentage / 100);
    
    return subtotal + taxAmount;
  };

  const calculateTotals = () => {
    const items = form.watch("items") || [];
    
    let subtotal = 0;
    let totalTax = 0;

    items.forEach((item, index) => {
      const quantity = item.quantity || 0;
      const unitPrice = item.unit_price || 0;
      const discountAmount = item.discount_amount || 0;
      const discountPercentage = item.discount_percentage || 0;
      const taxPercentage = item.tax_percentage || 0;

      let itemSubtotal = quantity * unitPrice;
      
      // Apply discount
      if (discountPercentage > 0) {
        itemSubtotal = itemSubtotal * (1 - discountPercentage / 100);
      }
      if (discountAmount > 0) {
        itemSubtotal = Math.max(0, itemSubtotal - discountAmount);
      }
      
      subtotal += itemSubtotal;
      totalTax += itemSubtotal * (taxPercentage / 100);
    });

    return {
      subtotal,
      totalTax,
      totalAmount: subtotal + totalTax,
    };
  };

  // Form submission handlers
  const onSubmit = async (data: CreditNoteFormData) => {
    setSubmitting(true);
    try {
      await createCreditNote(data);
      toast({
        title: "Success",
        description: "Credit note created successfully.",
      });
      router.push("/credit-notes");
    } catch (error) {
      console.error("Error creating credit note:", error);
      toast({
        title: "Error",
        description: "Failed to create credit note. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const onSaveDraft = async (data: CreditNoteFormData) => {
    setSubmitting(true);
    try {
      // For now, we'll treat draft the same as regular creation
      await createCreditNote(data);
      toast({
        title: "Success",
        description: "Credit note draft saved successfully.",
      });
      router.push("/credit-notes");
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return {
    form,
    fields,
    loading,
    submitting,
    options: {
      entities,
      customers,
      currencies,
      creditReasons,
      applicationMethods,
      invoices,
    },
    actions: {
      addLineItem,
      removeLineItem,
      onSubmit,
      onSaveDraft,
    },
    calculations: {
      calculateLineTotal,
      calculateTotals,
    },
  };
};