import { useState, useCallback, useEffect, useMemo } from 'react';
import { 
  EmailTemplate, 
  TemplateVariable, 
  TemplatePreviewData,
  TemplateValidationResult 
} from '@/types/email-blast/email-template.types';
import { emailTemplatesService } from '@/services/email-blast/email-templates.service';
import { useToast } from '@/hooks/use-toast';

export interface EditorState {
  subject: string;
  body: string;
  bodyHtml?: string;
  previewText?: string;
  variables: Record<string, any>;
  mode: 'visual' | 'html' | 'preview';
}

interface UseEmailEditorOptions {
  template?: EmailTemplate | null;
  initialContent?: Partial<EditorState>;
  autoSave?: boolean;
  autoSaveInterval?: number;
}

export function useEditorState(options: UseEmailEditorOptions = {}) {
  const { template, initialContent, autoSave = false, autoSaveInterval = 5000 } = options;
  const { toast } = useToast();

  // Initialize state from template or initial content
  const [editorState, setEditorState] = useState<EditorState>(() => {
    if (template) {
      return {
        subject: template.content.subject,
        body: template.content.body,
        bodyHtml: template.content.bodyHtml,
        previewText: template.content.previewText,
        variables: template.content.variables || {},
        mode: 'visual' as const
      };
    }
    
    return {
      subject: initialContent?.subject || '',
      body: initialContent?.body || '',
      bodyHtml: initialContent?.bodyHtml,
      previewText: initialContent?.previewText,
      variables: initialContent?.variables || {},
      mode: 'visual' as const
    };
  });

  const [isDirty, setIsDirty] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Update subject
  const updateSubject = useCallback((subject: string) => {
    setEditorState(prev => ({ ...prev, subject }));
    setIsDirty(true);
  }, []);

  // Update body (text version)
  const updateBody = useCallback((body: string) => {
    setEditorState(prev => ({ ...prev, body }));
    setIsDirty(true);
  }, []);

  // Update body HTML
  const updateBodyHtml = useCallback((bodyHtml: string) => {
    setEditorState(prev => ({ ...prev, bodyHtml }));
    setIsDirty(true);
  }, []);

  // Update preview text
  const updatePreviewText = useCallback((previewText: string) => {
    setEditorState(prev => ({ ...prev, previewText }));
    setIsDirty(true);
  }, []);

  // Update variables
  const updateVariables = useCallback((variables: Record<string, any>) => {
    setEditorState(prev => ({ ...prev, variables }));
    setIsDirty(true);
  }, []);

  // Update specific variable
  const updateVariable = useCallback((key: string, value: any) => {
    setEditorState(prev => ({
      ...prev,
      variables: { ...prev.variables, [key]: value }
    }));
    setIsDirty(true);
  }, []);

  // Change editor mode
  const setMode = useCallback((mode: EditorState['mode']) => {
    setEditorState(prev => ({ ...prev, mode }));
  }, []);

  // Reset editor state
  const reset = useCallback(() => {
    if (template) {
      setEditorState({
        subject: template.content.subject,
        body: template.content.body,
        bodyHtml: template.content.bodyHtml,
        previewText: template.content.previewText,
        variables: template.content.variables || {},
        mode: 'visual'
      });
    } else {
      setEditorState({
        subject: '',
        body: '',
        bodyHtml: undefined,
        previewText: undefined,
        variables: {},
        mode: 'visual'
      });
    }
    setIsDirty(false);
  }, [template]);

  // Mark as saved
  const markAsSaved = useCallback(() => {
    setIsDirty(false);
    setLastSaved(new Date());
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !isDirty) return;

    const timer = setTimeout(() => {
      // In a real app, this would save to a backend or local storage
      markAsSaved();
      toast({
        title: 'Auto-saved',
        description: 'Changes have been auto-saved',
        duration: 2000
      });
    }, autoSaveInterval);

    return () => clearTimeout(timer);
  }, [autoSave, isDirty, autoSaveInterval, markAsSaved, toast]);

  // Update state when template changes
  useEffect(() => {
    if (template) {
      setEditorState({
        subject: template.content.subject,
        body: template.content.body,
        bodyHtml: template.content.bodyHtml,
        previewText: template.content.previewText,
        variables: template.content.variables || {},
        mode: 'visual'
      });
      setIsDirty(false);
    }
  }, [template]);

  return {
    editorState,
    isDirty,
    lastSaved,
    updateSubject,
    updateBody,
    updateBodyHtml,
    updatePreviewText,
    updateVariables,
    updateVariable,
    setMode,
    reset,
    markAsSaved
  };
}

export function useVariableInsertion() {
  // Insert variable at cursor position
  const insertVariable = useCallback((
    textarea: HTMLTextAreaElement | null,
    variableKey: string,
    currentValue: string,
    onUpdate: (newValue: string) => void
  ) => {
    if (!textarea) return;

    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;
    const variableText = `{{${variableKey}}}`;
    
    const newValue = 
      currentValue.substring(0, startPos) + 
      variableText + 
      currentValue.substring(endPos);
    
    onUpdate(newValue);
    
    // Set cursor position after the inserted variable
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        startPos + variableText.length,
        startPos + variableText.length
      );
    }, 0);
  }, []);

  // Extract variables from content
  const extractVariables = useCallback((content: string): string[] => {
    const variableRegex = /\{\{(\w+)\}\}/g;
    const variables: string[] = [];
    let match;
    
    while ((match = variableRegex.exec(content)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }
    
    return variables;
  }, []);

  // Check if variable exists in content
  const hasVariable = useCallback((content: string, variableKey: string): boolean => {
    const regex = new RegExp(`\\{\\{${variableKey}\\}\\}`, 'g');
    return regex.test(content);
  }, []);

  // Get variable positions in content
  const getVariablePositions = useCallback((content: string, variableKey: string): number[] => {
    const regex = new RegExp(`\\{\\{${variableKey}\\}\\}`, 'g');
    const positions: number[] = [];
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      positions.push(match.index);
    }
    
    return positions;
  }, []);

  return {
    insertVariable,
    extractVariables,
    hasVariable,
    getVariablePositions
  };
}

export function useTemplatePreview(templateId?: string) {
  const [preview, setPreview] = useState<{
    subject: string;
    bodyHtml: string;
    bodyText: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate preview
  const generatePreview = useCallback(async (
    data: TemplatePreviewData
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      const previewData = await emailTemplatesService.previewWithData(data);
      setPreview(previewData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate preview';
      setError(errorMessage);
      setPreview(null);
    } finally {
      setLoading(false);
    }
  }, []);

  // Generate preview for current template
  const generateCurrentPreview = useCallback(async (
    variableValues: Record<string, any>
  ): Promise<void> => {
    if (!templateId) return;
    
    await generatePreview({
      templateId,
      variableValues
    });
  }, [templateId, generatePreview]);

  // Clear preview
  const clearPreview = useCallback(() => {
    setPreview(null);
    setError(null);
  }, []);

  return {
    preview,
    loading,
    error,
    generatePreview,
    generateCurrentPreview,
    clearPreview
  };
}

export function useVariableValidation(templateVariables: TemplateVariable[] = []) {
  const [validationResult, setValidationResult] = useState<TemplateValidationResult | null>(null);
  const [validating, setValidating] = useState(false);

  // Validate variables
  const validateVariables = useCallback(async (
    templateId: string,
    variableValues: Record<string, any>
  ): Promise<TemplateValidationResult> => {
    try {
      setValidating(true);
      
      const result = await emailTemplatesService.validateVariables(templateId, variableValues);
      setValidationResult(result);
      
      return result;
    } catch (err) {
      const errorResult: TemplateValidationResult = {
        isValid: false,
        errors: [{
          field: 'validation',
          message: err instanceof Error ? err.message : 'Validation failed',
          type: 'error'
        }],
        missingVariables: [],
        unusedVariables: []
      };
      
      setValidationResult(errorResult);
      return errorResult;
    } finally {
      setValidating(false);
    }
  }, []);

  // Validate specific variable
  const validateVariable = useCallback((
    variable: TemplateVariable,
    value: any
  ): { isValid: boolean; error?: string } => {
    // Check if required
    if (variable.required && (value === undefined || value === null || value === '')) {
      return { isValid: false, error: `${variable.name} is required` };
    }

    // Skip further validation if empty and not required
    if (!value && !variable.required) {
      return { isValid: true };
    }

    // Type validation
    switch (variable.type) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(String(value))) {
          return { isValid: false, error: `${variable.name} must be a valid email address` };
        }
        break;
        
      case 'url':
        try {
          new URL(String(value));
        } catch {
          return { isValid: false, error: `${variable.name} must be a valid URL` };
        }
        break;
        
      case 'number':
        if (isNaN(Number(value))) {
          return { isValid: false, error: `${variable.name} must be a valid number` };
        }
        break;
        
      case 'date':
        const dateValue = new Date(value);
        if (isNaN(dateValue.getTime())) {
          return { isValid: false, error: `${variable.name} must be a valid date` };
        }
        break;
    }

    // Additional validation rules
    if (variable.validation) {
      const stringValue = String(value);
      
      if (variable.validation.minLength && stringValue.length < variable.validation.minLength) {
        return { 
          isValid: false, 
          error: `${variable.name} must be at least ${variable.validation.minLength} characters` 
        };
      }
      
      if (variable.validation.maxLength && stringValue.length > variable.validation.maxLength) {
        return { 
          isValid: false, 
          error: `${variable.name} must be no more than ${variable.validation.maxLength} characters` 
        };
      }
      
      if (variable.validation.pattern && !new RegExp(variable.validation.pattern).test(stringValue)) {
        return { isValid: false, error: `${variable.name} format is invalid` };
      }
      
      if (variable.validation.options && !variable.validation.options.includes(stringValue)) {
        return { 
          isValid: false, 
          error: `${variable.name} must be one of: ${variable.validation.options.join(', ')}` 
        };
      }
    }

    return { isValid: true };
  }, []);

  // Get validation errors for all variables
  const getValidationErrors = useCallback((
    variableValues: Record<string, any>
  ): { [key: string]: string } => {
    const errors: { [key: string]: string } = {};
    
    templateVariables.forEach(variable => {
      const value = variableValues[variable.key];
      const validation = validateVariable(variable, value);
      
      if (!validation.isValid && validation.error) {
        errors[variable.key] = validation.error;
      }
    });
    
    return errors;
  }, [templateVariables, validateVariable]);

  // Check if all variables are valid
  const areAllVariablesValid = useCallback((
    variableValues: Record<string, any>
  ): boolean => {
    const errors = getValidationErrors(variableValues);
    return Object.keys(errors).length === 0;
  }, [getValidationErrors]);

  return {
    validationResult,
    validating,
    validateVariables,
    validateVariable,
    getValidationErrors,
    areAllVariablesValid
  };
}

export function useEmailEditor(options: UseEmailEditorOptions = {}) {
  const editorState = useEditorState(options);
  const variableInsertion = useVariableInsertion();
  const preview = useTemplatePreview(options.template?.id);
  const validation = useVariableValidation(options.template?.variables);

  // Combined state for easier access
  const state = useMemo(() => ({
    ...editorState,
    preview: preview.preview,
    previewLoading: preview.loading,
    previewError: preview.error,
    validationResult: validation.validationResult,
    validating: validation.validating
  }), [editorState, preview, validation]);

  // Combined actions
  const actions = useMemo(() => ({
    ...editorState,
    ...variableInsertion,
    generatePreview: preview.generateCurrentPreview,
    clearPreview: preview.clearPreview,
    validateVariables: validation.validateVariables,
    validateVariable: validation.validateVariable,
    getValidationErrors: validation.getValidationErrors,
    areAllVariablesValid: validation.areAllVariablesValid
  }), [editorState, variableInsertion, preview, validation]);

  return {
    state,
    actions
  };
}