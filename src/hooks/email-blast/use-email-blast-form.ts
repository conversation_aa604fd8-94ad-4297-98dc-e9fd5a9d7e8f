'use client';

import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { EmailBlast } from '@/types/email-blast/email-blast.types';
import { emailBlastFormSchema, EmailBlastFormData } from '@/schemas/email-blast/email-blast.schema';
import { emailBlastServerService } from '@/services/email-blast/email-blast-server-adapter.service';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';

export function useEmailBlastForm(campaignId?: string) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [campaign, setCampaign] = useState<EmailBlast | null>(null);
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<EmailBlastFormData>({
    resolver: zodResolver(emailBlastFormSchema),
    defaultValues: {
      name: '',
      subject: '',
      from_name: '',
      from_email: '',
      reply_to_email: '',
      content_html: '',
      content_text: '',
      recipientListId: '',
      description: '',
      tags: [],
      metadata: {},
    },
  });

  // Load existing campaign if editing
  useEffect(() => {
    if (!campaignId) return;

    const loadCampaign = async () => {
      try {
        setLoading(true);
        const data = await emailBlastServerService.getById(campaignId);
        if (data) {
          setCampaign(data);
          form.reset({
            name: data.name,
            subject: data.content.subject,
            from_name: data.metadata?.fromName || '',
            from_email: data.metadata?.fromEmail || '',
            reply_to_email: data.metadata?.replyToEmail || '',
            content_html: data.content.bodyHtml || '',
            content_text: data.content.body || '',
            recipientListId: data.recipientListId,
            description: data.description,
            tags: data.tags,
            metadata: data.metadata
          });
        }
      } catch (err) {
        toast({
          title: 'Error',
          description: 'Failed to load campaign',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadCampaign();
  }, [campaignId, form, toast]);

  const saveDraft = useCallback(async (data: EmailBlastFormData) => {
    try {
      setSaving(true);
      
      if (campaignId) {
        await emailBlastServerService.update(campaignId, data);
        toast({
          title: 'Success',
          description: 'Campaign updated successfully',
        });
      } else {
        const newCampaign = await emailBlastServerService.create(data);
        toast({
          title: 'Success',
          description: 'Campaign created successfully',
        });
        // Navigate to edit page for the new campaign
        router.push(`/email-blast/${newCampaign.id}/edit`);
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to save campaign';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setSaving(false);
    }
  }, [campaignId, router, toast]);

  const scheduleForLater = useCallback(async (data: EmailBlastFormData, scheduledAt: string) => {
    try {
      setSaving(true);
      
      let id = campaignId;
      
      // Create or update campaign first
      if (!id) {
        const newCampaign = await emailBlastServerService.create(data);
        id = newCampaign.id;
      } else {
        await emailBlastServerService.update(id, data);
      }

      // Schedule it
      await emailBlastServerService.scheduleCampaign(id, scheduledAt);
      
      toast({
        title: 'Success',
        description: 'Campaign scheduled successfully',
      });
      
      router.push('/email-blast');
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to schedule campaign';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setSaving(false);
    }
  }, [campaignId, router, toast]);

  const sendNow = useCallback(async (data: EmailBlastFormData) => {
    try {
      setSaving(true);
      
      let id = campaignId;
      
      // Create or update campaign first
      if (!id) {
        const newCampaign = await emailBlastServerService.create(data);
        id = newCampaign.id;
      } else {
        await emailBlastServerService.update(id, data);
      }

      // Send it
      await emailBlastServerService.sendNow(id);
      
      toast({
        title: 'Success',
        description: 'Campaign is being sent',
      });
      
      router.push(`/email-blast/${id}`);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to send campaign';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setSaving(false);
    }
  }, [campaignId, router, toast]);

  const sendTestEmail = useCallback(async (testEmails: string[]) => {
    if (!campaignId && !form.formState.isDirty) {
      toast({
        title: 'Error',
        description: 'Please save your campaign before sending test emails',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      
      let id = campaignId;
      
      // Save current form data if needed
      if (!id || form.formState.isDirty) {
        const formData = form.getValues();
        if (!id) {
          const newCampaign = await emailBlastServerService.create(formData);
          id = newCampaign.id;
        } else {
          await emailBlastServerService.update(id, formData);
        }
      }

      // Send test email
      await emailBlastServerService.testEmail(id, testEmails);
      
      toast({
        title: 'Success',
        description: `Test email sent to ${testEmails.join(', ')}`,
      });
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to send test email';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setSaving(false);
    }
  }, [campaignId, form, toast]);

  const duplicateCampaign = useCallback(async (newName: string) => {
    if (!campaignId) {
      toast({
        title: 'Error',
        description: 'No campaign to duplicate',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      const duplicated = await emailBlastServerService.duplicateCampaign(campaignId, newName);
      
      toast({
        title: 'Success',
        description: 'Campaign duplicated successfully',
      });
      
      router.push(`/email-blast/${duplicated.id}/edit`);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to duplicate campaign';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setSaving(false);
    }
  }, [campaignId, router, toast]);

  const deleteCampaign = useCallback(async () => {
    if (!campaignId) return;

    try {
      setSaving(true);
      await emailBlastServerService.delete(campaignId);
      
      toast({
        title: 'Success',
        description: 'Campaign deleted successfully',
      });
      
      router.push('/email-blast');
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to delete campaign';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setSaving(false);
    }
  }, [campaignId, router, toast]);

  // Calculate total recipients based on selected lists
  const calculateTotalRecipients = useCallback(() => {
    const selectedListId = form.watch('recipientListId');
    // This would normally calculate from actual list data
    return selectedListId ? 100 : 0; // Mock calculation
  }, [form]);

  return {
    form,
    campaign,
    loading,
    saving,
    saveDraft,
    scheduleForLater,
    sendNow,
    sendTestEmail,
    duplicateCampaign,
    deleteCampaign,
    calculateTotalRecipients,
  };
}