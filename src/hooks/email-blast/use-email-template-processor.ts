'use client';

import { useState, useCallback, useMemo } from 'react';
import { 
  TemplateProcessor, 
  TemplateVariables, 
  ProcessedTemplate 
} from '@/lib/email-blast/template-processor';
import { TemplateVariable } from '@/types/email-blast/email-template.types';

export interface UseEmailTemplateProcessorOptions {
  subject: string;
  body: string;
  bodyHtml?: string;
  variables?: TemplateVariable[];
  recipientData?: TemplateVariables;
}

export function useEmailTemplateProcessor(options: UseEmailTemplateProcessorOptions) {
  const { subject, body, bodyHtml, variables = [], recipientData = {} } = options;
  
  const [processedContent, setProcessedContent] = useState<ProcessedTemplate | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Extract variable keys from template content
  const extractedVariables = useMemo(() => {
    return TemplateProcessor.getEmailVariables(subject, body, bodyHtml);
  }, [subject, body, bodyHtml]);
  
  // Map template variables to template processor format
  const variableMap = useMemo(() => {
    const map: TemplateVariables = { ...recipientData };
    
    // Add predefined variables from the template
    variables.forEach(variable => {
      if (variable.defaultValue !== undefined) {
        map[variable.key] = variable.defaultValue;
      }
    });
    
    return map;
  }, [variables, recipientData]);
  
  // Process the template
  const processTemplate = useCallback(async () => {
    setIsProcessing(true);
    
    try {
      const processed = TemplateProcessor.processEmailContent(
        subject,
        body,
        bodyHtml,
        variableMap
      );
      
      setProcessedContent(processed);
      return processed;
    } catch (error) {
      console.error('Failed to process template:', error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [subject, body, bodyHtml, variableMap]);
  
  // Preview the template with sample data
  const previewTemplate = useCallback(() => {
    const previewSubject = TemplateProcessor.createPreview(subject);
    const previewBody = TemplateProcessor.createPreview(body);
    const previewBodyHtml = bodyHtml ? TemplateProcessor.createPreview(bodyHtml) : undefined;
    
    return {
      subject: previewSubject,
      body: previewBody,
      bodyHtml: previewBodyHtml,
      missingVariables: [],
    };
  }, [subject, body, bodyHtml]);
  
  // Validate variables
  const validation = useMemo(() => {
    const subjectValidation = TemplateProcessor.validateVariables(subject, variableMap);
    const bodyValidation = TemplateProcessor.validateVariables(body, variableMap);
    const htmlValidation = bodyHtml 
      ? TemplateProcessor.validateVariables(bodyHtml, variableMap)
      : { isValid: true, missing: [] };
    
    const allMissing = new Set([
      ...subjectValidation.missing,
      ...bodyValidation.missing,
      ...htmlValidation.missing,
    ]);
    
    return {
      isValid: allMissing.size === 0,
      missingVariables: Array.from(allMissing),
    };
  }, [subject, body, bodyHtml, variableMap]);
  
  // Process with specific recipient data
  const processForRecipient = useCallback((recipientData: TemplateVariables) => {
    const mergedVariables = {
      ...variableMap,
      ...recipientData,
    };
    
    return TemplateProcessor.processEmailContent(
      subject,
      body,
      bodyHtml,
      mergedVariables
    );
  }, [subject, body, bodyHtml, variableMap]);
  
  // Get formatted variable list for display
  const formattedVariableList = useMemo(() => {
    return TemplateProcessor.formatVariableList(extractedVariables);
  }, [extractedVariables]);
  
  return {
    processedContent,
    isProcessing,
    processTemplate,
    previewTemplate,
    processForRecipient,
    validation,
    extractedVariables,
    formattedVariableList,
    variableMap,
  };
}

// Hook for batch processing templates for multiple recipients
export function useBatchTemplateProcessor() {
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  
  const processBatch = useCallback(async (
    template: {
      subject: string;
      body: string;
      bodyHtml?: string;
    },
    recipients: Array<{ id: string; data: TemplateVariables }>,
    onProgress?: (progress: number) => void
  ) => {
    setProcessing(true);
    setProgress(0);
    
    const results: Array<{
      recipientId: string;
      processed: ProcessedTemplate;
    }> = [];
    
    try {
      for (let i = 0; i < recipients.length; i++) {
        const recipient = recipients[i];
        
        const processed = TemplateProcessor.processEmailContent(
          template.subject,
          template.body,
          template.bodyHtml,
          recipient.data
        );
        
        results.push({
          recipientId: recipient.id,
          processed,
        });
        
        const currentProgress = ((i + 1) / recipients.length) * 100;
        setProgress(currentProgress);
        onProgress?.(currentProgress);
        
        // Add a small delay to prevent blocking UI
        if (i % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }
      
      return results;
    } finally {
      setProcessing(false);
      setProgress(100);
    }
  }, []);
  
  return {
    processBatch,
    processing,
    progress,
  };
}