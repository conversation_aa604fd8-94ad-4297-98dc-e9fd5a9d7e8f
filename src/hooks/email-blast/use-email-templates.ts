import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  EmailTemplate, 
  TemplateListFilters, 
  TemplateDuplicateOptions,
  TemplateFormData,
  TemplateStatus,
  TemplateCategory 
} from '@/types/email-blast/email-template.types';
import { emailTemplatesService } from '@/services/email-blast/email-templates.service';
import { useToast } from '@/hooks/use-toast';

interface UseEmailTemplatesOptions {
  initialFilters?: TemplateListFilters;
  autoLoad?: boolean;
}

export function useEmailTemplates(options: UseEmailTemplatesOptions = {}) {
  const { initialFilters, autoLoad = true } = options;
  const { toast } = useToast();

  // State
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<TemplateListFilters>(initialFilters || {});

  // Load templates
  const loadTemplates = useCallback(async (appliedFilters?: TemplateListFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const filtersToUse = appliedFilters || filters;
      const data = await emailTemplatesService.getAll(filtersToUse);
      setTemplates(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load templates';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<TemplateListFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    loadTemplates(updatedFilters);
  }, [filters, loadTemplates]);

  // Clear filters
  const clearFilters = useCallback(() => {
    const clearedFilters = {};
    setFilters(clearedFilters);
    loadTemplates(clearedFilters);
  }, [loadTemplates]);

  // Refresh templates
  const refresh = useCallback(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Auto-load on mount
  useEffect(() => {
    if (autoLoad) {
      loadTemplates();
    }
  }, [autoLoad, loadTemplates]);

  // Computed values
  const filteredCount = templates.length;
  const hasFilters = Object.keys(filters).length > 0;

  return {
    templates,
    loading,
    error,
    filters,
    filteredCount,
    hasFilters,
    loadTemplates,
    updateFilters,
    clearFilters,
    refresh
  };
}

export function useEmailTemplate(templateId: string) {
  const { toast } = useToast();
  
  // State
  const [template, setTemplate] = useState<EmailTemplate | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load template
  const loadTemplate = useCallback(async () => {
    if (!templateId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const data = await emailTemplatesService.getById(templateId);
      setTemplate(data);
      
      if (!data) {
        setError('Template not found');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load template';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [templateId, toast]);

  // Create template
  const createTemplate = useCallback(async (data: TemplateFormData): Promise<EmailTemplate | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const newTemplate = await emailTemplatesService.create(data);
      toast({
        title: 'Success',
        description: 'Template created successfully'
      });
      
      return newTemplate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create template';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Update template
  const updateTemplate = useCallback(async (data: Partial<TemplateFormData>): Promise<EmailTemplate | null> => {
    if (!templateId) return null;
    
    try {
      setLoading(true);
      setError(null);
      
      const updatedTemplate = await emailTemplatesService.update(templateId, data);
      setTemplate(updatedTemplate);
      
      toast({
        title: 'Success',
        description: 'Template updated successfully'
      });
      
      return updatedTemplate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update template';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [templateId, toast]);

  // Delete template
  const deleteTemplate = useCallback(async (): Promise<boolean> => {
    if (!templateId) return false;
    
    try {
      setLoading(true);
      setError(null);
      
      await emailTemplatesService.delete(templateId);
      toast({
        title: 'Success',
        description: 'Template deleted successfully'
      });
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete template';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [templateId, toast]);

  // Duplicate template
  const duplicateTemplate = useCallback(async (options: TemplateDuplicateOptions): Promise<EmailTemplate | null> => {
    if (!templateId) return null;
    
    try {
      setLoading(true);
      setError(null);
      
      const duplicatedTemplate = await emailTemplatesService.duplicate(templateId, options);
      toast({
        title: 'Success',
        description: 'Template duplicated successfully'
      });
      
      return duplicatedTemplate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to duplicate template';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [templateId, toast]);

  // Generate thumbnail
  const generateThumbnail = useCallback(async (): Promise<string | null> => {
    if (!templateId) return null;
    
    try {
      setLoading(true);
      setError(null);
      
      const thumbnailPath = await emailTemplatesService.generateThumbnail(templateId);
      
      // Update local template state
      if (template) {
        setTemplate({ ...template, thumbnail: thumbnailPath });
      }
      
      toast({
        title: 'Success',
        description: 'Thumbnail generated successfully'
      });
      
      return thumbnailPath;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate thumbnail';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [templateId, template, toast]);

  // Load template on mount or ID change
  useEffect(() => {
    loadTemplate();
  }, [loadTemplate]);

  return {
    template,
    loading,
    error,
    loadTemplate,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    generateThumbnail
  };
}

export function useTemplateFilters() {
  const [filters, setFilters] = useState<TemplateListFilters>({});

  // Update specific filter
  const updateFilter = useCallback(<K extends keyof TemplateListFilters>(
    key: K,
    value: TemplateListFilters[K]
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // Clear specific filter
  const clearFilter = useCallback((key: keyof TemplateListFilters) => {
    setFilters(prev => {
      const updated = { ...prev };
      delete updated[key];
      return updated;
    });
  }, []);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Set search filter
  const setSearch = useCallback((search: string) => {
    updateFilter('search', search || undefined);
  }, [updateFilter]);

  // Set category filter
  const setCategory = useCallback((category: TemplateCategory | undefined) => {
    updateFilter('category', category);
  }, [updateFilter]);

  // Set status filter
  const setStatus = useCallback((status: TemplateStatus | undefined) => {
    updateFilter('status', status);
  }, [updateFilter]);

  // Set tags filter
  const setTags = useCallback((tags: string[] | undefined) => {
    updateFilter('tags', tags);
  }, [updateFilter]);

  // Set creator filter
  const setCreatedBy = useCallback((createdBy: string | undefined) => {
    updateFilter('createdBy', createdBy);
  }, [updateFilter]);

  // Set system filter
  const setIsSystem = useCallback((isSystem: boolean | undefined) => {
    updateFilter('isSystem', isSystem);
  }, [updateFilter]);

  // Computed values
  const hasFilters = useMemo(() => Object.keys(filters).length > 0, [filters]);
  const activeFilterCount = useMemo(() => Object.keys(filters).length, [filters]);

  return {
    filters,
    hasFilters,
    activeFilterCount,
    updateFilter,
    clearFilter,
    clearAllFilters,
    setSearch,
    setCategory,
    setStatus,
    setTags,
    setCreatedBy,
    setIsSystem
  };
}

export function useTemplateCategories() {
  const [categories, setCategories] = useState<{ category: TemplateCategory; count: number; activeCount: number }[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await emailTemplatesService.getCategoryStats();
      setCategories(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load categories';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  return {
    categories,
    loading,
    error,
    loadCategories
  };
}

export function usePopularTemplates(limit: number = 10) {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPopularTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await emailTemplatesService.getPopular(limit);
      setTemplates(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load popular templates';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    loadPopularTemplates();
  }, [loadPopularTemplates]);

  return {
    templates,
    loading,
    error,
    loadPopularTemplates
  };
}

export function useRecentlyUsedTemplates(limit: number = 5) {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRecentTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await emailTemplatesService.getRecentlyUsed(limit);
      setTemplates(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load recent templates';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    loadRecentTemplates();
  }, [loadRecentTemplates]);

  return {
    templates,
    loading,
    error,
    loadRecentTemplates
  };
}