'use client';

import { useState, useEffect, useCallback } from 'react';
import { EmailBlast, EmailCampaignStatus } from '@/types/email-blast/email-blast.types';
import { getEmailBlasts, getEmailBlastById, getEmailBlastsByStatus, getEmailBlastStats } from '@/services/email-blast/email-blast-server.service';
import { useToast } from '@/hooks/use-toast';

export interface CampaignFilters {
  status?: EmailCampaignStatus;
  search?: string;
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
}

export function useCampaigns(filters?: CampaignFilters) {
  const [campaigns, setCampaigns] = useState<EmailBlast[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchCampaigns = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      let allCampaigns = await getEmailBlasts();
      
      if (filters) {
        if (filters.status) {
          allCampaigns = allCampaigns.filter(c => c.status === filters.status);
        }
        
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          allCampaigns = allCampaigns.filter(c => 
            c.name.toLowerCase().includes(searchLower) ||
            c.description?.toLowerCase().includes(searchLower)
          );
        }
        
        if (filters.tags && filters.tags.length > 0) {
          allCampaigns = allCampaigns.filter(c => 
            filters.tags!.some(tag => c.tags?.includes(tag))
          );
        }
        
        if (filters.dateFrom) {
          allCampaigns = allCampaigns.filter(c => 
            new Date(c.createdAt) >= filters.dateFrom!
          );
        }
        
        if (filters.dateTo) {
          allCampaigns = allCampaigns.filter(c => 
            new Date(c.createdAt) <= filters.dateTo!
          );
        }
      }
      
      setCampaigns(allCampaigns);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch campaigns');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast]);

  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  // Auto-refresh every minute
  useEffect(() => {
    const interval = setInterval(fetchCampaigns, 60000);
    return () => clearInterval(interval);
  }, [fetchCampaigns]);

  return {
    data: campaigns,
    isLoading: loading,
    error,
    refetch: fetchCampaigns,
  };
}

export function useCampaign(id: string | undefined) {
  const [campaign, setCampaign] = useState<EmailBlast | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchCampaign = useCallback(async () => {
    if (!id) {
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      const data = await getEmailBlastById(id);
      setCampaign(data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch campaign');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    fetchCampaign();
  }, [fetchCampaign]);

  return {
    data: campaign,
    isLoading: loading,
    error,
    refetch: fetchCampaign,
  };
}

export function useCampaignsByStatus(status: EmailCampaignStatus) {
  const [campaigns, setCampaigns] = useState<EmailBlast[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchCampaigns = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getEmailBlastsByStatus(status);
      setCampaigns(data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch campaigns');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [status, toast]);

  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  return {
    data: campaigns,
    isLoading: loading,
    error,
    refetch: fetchCampaigns,
  };
}

export function useCampaignStats(id: string | undefined) {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchStats = useCallback(async () => {
    if (!id) {
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      const data = await getEmailBlastStats(id);
      setStats(data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch campaign stats');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (id) {
      const interval = setInterval(fetchStats, 30000);
      return () => clearInterval(interval);
    }
  }, [id, fetchStats]);

  return {
    data: stats,
    isLoading: loading,
    error,
    refetch: fetchStats,
  };
}

// Helper hook for real-time campaign monitoring
export function useRealtimeCampaign(id: string | undefined) {
  const campaign = useCampaign(id);
  const stats = useCampaignStats(id);
  
  // Set up more aggressive refetching for active campaigns
  useEffect(() => {
    if (campaign.data?.status === EmailCampaignStatus.SENDING) {
      const interval = setInterval(() => {
        campaign.refetch();
        stats.refetch();
      }, 5000); // Refresh every 5 seconds for sending campaigns
      
      return () => clearInterval(interval);
    }
  }, [campaign.data?.status, campaign.refetch, stats.refetch]);
  
  return {
    campaign: campaign.data,
    stats: stats.data,
    isLoading: campaign.isLoading || stats.isLoading,
    error: campaign.error || stats.error,
    refetchCampaign: campaign.refetch,
    refetchStats: stats.refetch,
  };
}