'use client';

import { useState, useCallback, useMemo } from 'react';
import { RecipientPreview } from '@/types/email-blast/recipient-list.types';
import { recipientFilterService, RecipientFilter } from '@/services/email-blast/recipient-filter.service';

// Use RecipientPreview as Member type
type Member = RecipientPreview;
import { useToast } from '@/hooks/use-toast';

export function useRecipientFilter(initialFilters: RecipientFilter[] = []) {
  const [filters, setFilters] = useState<RecipientFilter[]>(initialFilters);
  const [previewMembers, setPreviewMembers] = useState<Member[]>([]);
  const [memberCount, setMemberCount] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const availableFields = useMemo(() => 
    recipientFilterService.getAvailableFields(),
    []
  );

  const getAvailableConditions = useCallback((fieldType: string) => {
    return recipientFilterService.getAvailableConditions(fieldType);
  }, []);

  const addFilter = useCallback((field?: string, condition?: RecipientFilter['condition'], value?: any) => {
    const newFilter = recipientFilterService.createFilter(
      field || 'email',
      condition || 'contains',
      value || '',
      `group-${Date.now()}`
    );
    setFilters(prev => [...prev, newFilter]);
  }, []);

  const updateFilter = useCallback((filterId: string, updates: Partial<RecipientFilter>) => {
    setFilters(prev => prev.map(filter => 
      filter.id === filterId ? { ...filter, ...updates } : filter
    ));
  }, []);

  const removeFilter = useCallback((filterId: string) => {
    setFilters(prev => prev.filter(filter => filter.id !== filterId));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters([]);
    setPreviewMembers([]);
    setMemberCount(0);
  }, []);

  const duplicateFilter = useCallback((filterId: string) => {
    const filterToDuplicate = filters.find(f => f.id === filterId);
    if (!filterToDuplicate) return;

    const duplicatedFilter = {
      ...filterToDuplicate,
      id: `filter-${Date.now()}-${Math.random()}`,
    };
    setFilters(prev => [...prev, duplicatedFilter]);
  }, [filters]);

  const moveFilter = useCallback((filterId: string, direction: 'up' | 'down') => {
    setFilters(prev => {
      const index = prev.findIndex(f => f.id === filterId);
      if (index === -1) return prev;

      const newFilters = [...prev];
      if (direction === 'up' && index > 0) {
        [newFilters[index - 1], newFilters[index]] = [newFilters[index], newFilters[index - 1]];
      } else if (direction === 'down' && index < newFilters.length - 1) {
        [newFilters[index], newFilters[index + 1]] = [newFilters[index + 1], newFilters[index]];
      }
      return newFilters;
    });
  }, []);

  const groupFilters = useCallback((filterIds: string[], groupId: string) => {
    setFilters(prev => prev.map(filter => 
      filterIds.includes(filter.id) ? { ...filter, group: groupId } : filter
    ));
  }, []);

  const ungroupFilter = useCallback((filterId: string) => {
    setFilters(prev => prev.map(filter => 
      filter.id === filterId ? { ...filter, group: undefined } : filter
    ));
  }, []);

  const setGroupOperator = useCallback((groupId: string, operator: 'AND' | 'OR') => {
    setFilters(prev => prev.map(filter => 
      filter.group === groupId ? { ...filter, operator } : filter
    ));
  }, []);

  const fetchPreview = useCallback(async () => {
    if (filters.length === 0) {
      setPreviewMembers([]);
      setMemberCount(0);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const [preview, count] = await Promise.all([
        recipientFilterService.previewFilteredMembers(filters, 10),
        recipientFilterService.countFilteredMembers(filters)
      ]);

      setPreviewMembers(preview);
      setMemberCount(count);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch preview';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast]);

  const getFilteredMemberIds = useCallback(async (): Promise<string[]> => {
    if (filters.length === 0) return [];

    try {
      const ids = await recipientFilterService.getFilteredMemberIds(filters);
      return ids;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to get filtered member IDs';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    }
  }, [filters, toast]);

  const validateFilters = useCallback((): boolean => {
    if (filters.length === 0) return true;

    for (const filter of filters) {
      if (!recipientFilterService.validateFilter(filter)) {
        toast({
          title: 'Invalid Filter',
          description: `Filter on field "${filter.field}" has invalid configuration`,
          variant: 'destructive',
        });
        return false;
      }

      // Check for required values
      if (
        filter.condition !== 'is_empty' && 
        filter.condition !== 'is_not_empty' && 
        (!filter.value || (Array.isArray(filter.value) && filter.value.length === 0))
      ) {
        toast({
          title: 'Missing Value',
          description: `Filter on field "${filter.field}" requires a value`,
          variant: 'destructive',
        });
        return false;
      }
    }

    return true;
  }, [filters, toast]);

  // Helper to get filters grouped by their group ID
  const filterGroups = useMemo(() => {
    const groups = new Map<string | undefined, RecipientFilter[]>();
    
    filters.forEach(filter => {
      const groupId = filter.group;
      if (!groups.has(groupId)) {
        groups.set(groupId, []);
      }
      groups.get(groupId)!.push(filter);
    });

    return Array.from(groups.entries()).map(([groupId, groupFilters]) => ({
      id: groupId || 'ungrouped',
      filters: groupFilters,
      operator: groupFilters[0]?.operator || 'AND'
    }));
  }, [filters]);

  return {
    filters,
    filterGroups,
    previewMembers,
    memberCount,
    loading,
    error,
    availableFields,
    getAvailableConditions,
    addFilter,
    updateFilter,
    removeFilter,
    clearFilters,
    duplicateFilter,
    moveFilter,
    groupFilters,
    ungroupFilter,
    setGroupOperator,
    fetchPreview,
    getFilteredMemberIds,
    validateFilters,
  };
}