'use client';

import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { EmailBlastFormData } from '@/schemas/email-blast/email-blast.schema';
import {
  createEmailBlast,
  updateEmailBlast,
  deleteEmailBlast,
  sendEmailBlastNow,
  scheduleEmailBlast,
  pauseEmailBlast,
  resumeEmailBlast,
  cancelEmailBlast,
  duplicateEmailBlast,
  sendTestEmail,
} from '@/services/email-blast/email-blast-server.service';
import { useRouter } from 'next/navigation';
import { EmailBlast } from '@/types/email-blast/email-blast.types';

export function useCreateCampaign() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  
  const createCampaign = useCallback(async (data: EmailBlastFormData) => {
    setLoading(true);
    setError(null);
    
    try {
      const newCampaign = await createEmailBlast(data);
      
      toast({
        title: 'Campaign created',
        description: 'Your email campaign has been created successfully.',
      });
      
      // Navigate to the new campaign
      router.push(`/email-blast/${newCampaign.id}/edit`);
      
      return newCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create campaign');
      setError(error);
      toast({
        title: 'Failed to create campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast, router]);
  
  return {
    mutate: createCampaign,
    mutateAsync: createCampaign,
    isLoading: loading,
    error,
  };
}

export function useUpdateCampaign(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  
  const updateCampaign = useCallback(async (data: Partial<EmailBlastFormData>) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedCampaign = await updateEmailBlast(campaignId, data);
      
      toast({
        title: 'Campaign updated',
        description: 'Your changes have been saved successfully.',
      });
      
      return updatedCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update campaign');
      setError(error);
      toast({
        title: 'Failed to update campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast]);
  
  return {
    mutate: updateCampaign,
    mutateAsync: updateCampaign,
    isLoading: loading,
    error,
  };
}

export function useSendCampaign(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  
  const sendCampaign = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    toast({
      title: 'Sending campaign...',
      description: 'Your campaign is being prepared for sending.',
    });
    
    try {
      const sentCampaign = await sendEmailBlastNow(campaignId);
      
      toast({
        title: 'Campaign sent',
        description: 'Your email campaign is now being sent to recipients.',
      });
      
      // Navigate to campaign details
      router.push(`/email-blast/${campaignId}`);
      
      return sentCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to send campaign');
      setError(error);
      toast({
        title: 'Failed to send campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast, router]);
  
  return {
    mutate: sendCampaign,
    mutateAsync: sendCampaign,
    isLoading: loading,
    error,
  };
}

export function useScheduleCampaign(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  
  const scheduleCampaign = useCallback(async (scheduledAt: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const scheduledCampaign = await scheduleEmailBlast(campaignId, scheduledAt);
      
      toast({
        title: 'Campaign scheduled',
        description: 'Your campaign has been scheduled successfully.',
      });
      
      return scheduledCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to schedule campaign');
      setError(error);
      toast({
        title: 'Failed to schedule campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast]);
  
  return {
    mutate: scheduleCampaign,
    mutateAsync: scheduleCampaign,
    isLoading: loading,
    error,
  };
}

export function useDeleteCampaign() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  
  const deleteCampaign = useCallback(async (campaignId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await deleteEmailBlast(campaignId);
      
      toast({
        title: 'Campaign deleted',
        description: 'The campaign has been deleted successfully.',
      });
      
      // Navigate to campaigns list
      router.push('/email-blast');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete campaign');
      setError(error);
      toast({
        title: 'Failed to delete campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast, router]);
  
  return {
    mutate: deleteCampaign,
    mutateAsync: deleteCampaign,
    isLoading: loading,
    error,
  };
}

export function usePauseCampaign(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  
  const pauseCampaign = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const pausedCampaign = await pauseEmailBlast(campaignId);
      
      toast({
        title: 'Campaign paused',
        description: 'The campaign has been paused successfully.',
      });
      
      return pausedCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to pause campaign');
      setError(error);
      toast({
        title: 'Failed to pause campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast]);
  
  return {
    mutate: pauseCampaign,
    mutateAsync: pauseCampaign,
    isLoading: loading,
    error,
  };
}

export function useResumeCampaign(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  
  const resumeCampaign = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const resumedCampaign = await resumeEmailBlast(campaignId);
      
      toast({
        title: 'Campaign resumed',
        description: 'The campaign has been resumed successfully.',
      });
      
      return resumedCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to resume campaign');
      setError(error);
      toast({
        title: 'Failed to resume campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast]);
  
  return {
    mutate: resumeCampaign,
    mutateAsync: resumeCampaign,
    isLoading: loading,
    error,
  };
}

export function useCancelScheduledCampaign(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  
  const cancelScheduled = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const cancelledCampaign = await cancelEmailBlast(campaignId);
      
      toast({
        title: 'Schedule cancelled',
        description: 'The campaign schedule has been cancelled.',
      });
      
      return cancelledCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to cancel schedule');
      setError(error);
      toast({
        title: 'Failed to cancel schedule',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast]);
  
  return {
    mutate: cancelScheduled,
    mutateAsync: cancelScheduled,
    isLoading: loading,
    error,
  };
}

export function useDuplicateCampaign() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  
  const duplicateCampaign = useCallback(async (campaignId: string, newName: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const duplicatedCampaign = await duplicateEmailBlast(campaignId, newName);
      
      toast({
        title: 'Campaign duplicated',
        description: 'The campaign has been duplicated successfully.',
      });
      
      // Navigate to the new campaign
      router.push(`/email-blast/${duplicatedCampaign.id}/edit`);
      
      return duplicatedCampaign;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to duplicate campaign');
      setError(error);
      toast({
        title: 'Failed to duplicate campaign',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast, router]);
  
  return {
    mutate: duplicateCampaign,
    mutateAsync: duplicateCampaign,
    isLoading: loading,
    error,
  };
}

export function useSendTestEmail(campaignId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  
  const sendTest = useCallback(async (testEmails: string[]) => {
    setLoading(true);
    setError(null);
    
    toast({
      title: 'Sending test email...',
      description: 'Your test email is being sent.',
    });
    
    try {
      await sendTestEmail(campaignId, testEmails);
      
      toast({
        title: 'Test email sent',
        description: `Test email sent to ${testEmails.join(', ')}`,
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to send test email');
      setError(error);
      toast({
        title: 'Failed to send test email',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [campaignId, toast]);
  
  return {
    mutate: sendTest,
    mutateAsync: sendTest,
    isLoading: loading,
    error,
  };
}