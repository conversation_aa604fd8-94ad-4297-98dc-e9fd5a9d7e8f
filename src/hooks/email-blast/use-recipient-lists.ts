'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { RecipientList, RecipientFilters } from '@/types/email-blast/recipient-list.types';
import { recipientListsService, RecipientListFormData } from '@/services/email-blast/recipient-lists.service';
import { useToast } from '@/hooks/use-toast';

export interface RecipientListsOptions {
  search?: string;
  tags?: string[];
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}

export function useRecipientLists(options?: RecipientListsOptions) {
  const { page = 1, pageSize = 10 } = options || {};
  const [lists, setLists] = useState<RecipientList[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const { toast } = useToast();

  const fetchLists = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get all lists and apply client-side filtering and pagination
      // In a production app, this would be server-side
      let allLists = await recipientListsService.getAll();
      
      if (options) {
        // Apply search filter
        if (options.search) {
          const searchLower = options.search.toLowerCase();
          allLists = allLists.filter(list => 
            list.name.toLowerCase().includes(searchLower) ||
            list.description?.toLowerCase().includes(searchLower)
          );
        }
        
        // Apply tags filter
        if (options.tags && options.tags.length > 0) {
          allLists = allLists.filter(list => 
            options.tags!.some(tag => list.tags?.includes(tag))
          );
        }
        
        // Apply active filter
        if (options.isActive !== undefined) {
          allLists = allLists.filter(list => 
            (list.metadata?.isActive ?? true) === options.isActive
          );
        }
      }
      
      // Calculate pagination
      const total = allLists.length;
      setTotalCount(total);
      
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedLists = allLists.slice(startIndex, endIndex);
      
      setLists(paginatedLists);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch recipient lists';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [options, page, pageSize, toast]);

  useEffect(() => {
    fetchLists();
  }, [fetchLists]);

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    data: {
      lists,
      totalCount,
      totalPages,
      currentPage: page,
      pageSize
    },
    isLoading: loading,
    error,
    refetch: fetchLists,
  };
}

export function useCreateRecipientList() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const createList = useCallback(async (data: RecipientListFormData) => {
    setLoading(true);
    setError(null);
    
    try {
      const newList = await recipientListsService.create(data);
      
      toast({
        title: 'Success',
        description: 'Recipient list created successfully',
      });
      
      return newList;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create recipient list');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    mutate: createList,
    mutateAsync: createList,
    isLoading: loading,
    error,
  };
}

export function useUpdateRecipientList() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const updateList = useCallback(async (id: string, data: Partial<RecipientListFormData>) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedList = await recipientListsService.update(id, data);
      
      toast({
        title: 'Success',
        description: 'Recipient list updated successfully',
      });
      
      return updatedList;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update recipient list');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    mutate: updateList,
    mutateAsync: updateList,
    isLoading: loading,
    error,
  };
}

export function useDeleteRecipientList() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const deleteList = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await recipientListsService.delete(id);
      
      toast({
        title: 'Success',
        description: 'Recipient list deleted successfully',
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete recipient list');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    mutate: deleteList,
    mutateAsync: deleteList,
    isLoading: loading,
    error,
  };
}

export function useToggleRecipientListActive() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const toggleActive = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedList = await recipientListsService.toggleActive(id);
      
      toast({
        title: 'Success',
        description: `Recipient list ${updatedList.metadata?.isActive ? 'activated' : 'deactivated'} successfully`,
      });
      
      return updatedList;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to toggle recipient list status');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    mutate: toggleActive,
    mutateAsync: toggleActive,
    isLoading: loading,
    error,
  };
}

export function useDuplicateRecipientList() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const duplicateList = useCallback(async (id: string, newName: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const duplicatedList = await recipientListsService.duplicateList(id, newName);
      
      toast({
        title: 'Success',
        description: 'Recipient list duplicated successfully',
      });
      
      return duplicatedList;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to duplicate recipient list');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    mutate: duplicateList,
    mutateAsync: duplicateList,
    isLoading: loading,
    error,
  };
}

export function useRefreshRecipientListCount() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const refreshCount = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedList = await recipientListsService.refreshMemberCount(id);
      
      toast({
        title: 'Success',
        description: 'Member count refreshed successfully',
      });
      
      return updatedList;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to refresh member count');
      setError(error);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    mutate: refreshCount,
    mutateAsync: refreshCount,
    isLoading: loading,
    error,
  };
}

export function useRecipientList(id: string | undefined) {
  const [list, setList] = useState<RecipientList | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchList = useCallback(async () => {
    if (!id) {
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      const data = await recipientListsService.getById(id);
      setList(data);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch recipient list';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    fetchList();
  }, [fetchList]);

  const updateList = useCallback(async (data: Partial<RecipientListFormData>) => {
    if (!id) return;
    
    try {
      const updatedList = await recipientListsService.update(id, data);
      setList(updatedList);
      toast({
        title: 'Success',
        description: 'Recipient list updated successfully',
      });
      return updatedList;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to update recipient list';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    }
  }, [id, toast]);

  const refreshMemberCount = useCallback(async () => {
    if (!id) return;
    
    try {
      const updatedList = await recipientListsService.refreshMemberCount(id);
      setList(updatedList);
      toast({
        title: 'Success',
        description: 'Member count refreshed successfully',
      });
      return updatedList;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to refresh member count';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      throw err;
    }
  }, [id, toast]);

  return {
    list,
    loading,
    error,
    refetch: fetchList,
    updateList,
    refreshMemberCount,
  };
}

// Hook to get recipient list count with caching
export function useRecipientListCount(id: string | undefined) {
  const [count, setCount] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setLoading(false);
      return;
    }

    const fetchCount = async () => {
      try {
        setLoading(true);
        setError(null);
        const memberCount = await recipientListsService.getMemberCount(id);
        setCount(memberCount);
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to fetch member count';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchCount();
  }, [id]);

  return {
    data: count,
    isLoading: loading,
    error,
  };
}

// Hook to get total count of all lists (useful for dashboards)
export function useRecipientListsCount() {
  const [count, setCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCount = async () => {
      try {
        setLoading(true);
        setError(null);
        const lists = await recipientListsService.getAll();
        setCount(lists.length);
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to fetch lists count';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchCount();
  }, []);

  return {
    data: count,
    isLoading: loading,
    error,
  };
}

// Hook to preview recipients based on filters
export function usePreviewRecipients(filters: RecipientFilters) {
  const [recipients, setRecipients] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const debouncedFetch = useCallback(async () => {
    if (Object.keys(filters).length === 0) {
      setRecipients([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const data = await recipientListsService.previewMembers(filters);
      setRecipients(data);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to preview recipients';
      setError(message);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    const timeout = setTimeout(debouncedFetch, 500);
    return () => clearTimeout(timeout);
  }, [debouncedFetch]);

  return {
    data: recipients,
    isLoading: loading,
    error,
  };
}

// Hook to get recipients with pagination
export function useRecipientListMembers(listId: string | undefined, options?: {
  page?: number;
  pageSize?: number;
}) {
  const { page = 1, pageSize = 20 } = options || {};
  const [members, setMembers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!listId) {
      setLoading(false);
      return;
    }

    const fetchMembers = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await recipientListsService.getRecipients(listId, {
          limit: pageSize,
          offset: (page - 1) * pageSize
        });
        setMembers(data);
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to fetch list members';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, [listId, page, pageSize]);

  return {
    data: members,
    isLoading: loading,
    error,
  };
}