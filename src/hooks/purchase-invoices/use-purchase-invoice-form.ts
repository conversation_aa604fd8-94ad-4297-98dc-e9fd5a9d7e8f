"use client";

import { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { purchaseInvoiceSchema, type PurchaseInvoiceFormData } from "@/schemas/purchase-invoices/purchase-invoice.schema";
import type { 
  PurchaseInvoice, 
  CreatePurchaseInvoiceInput,
  SupplierInfo,
  EntityInfo,
  PurchaseOrderInfo
} from "@/types/purchase-invoices/purchase-invoices.types";
import {
  createPurchaseInvoice,
  updatePurchaseInvoice,
} from "@/services/purchase-invoices/purchase-invoices.service";
import { createClient } from "@/utils/supabase/Client";

interface DropdownOption {
  value: string;
  label: string;
  code?: string;
  email?: string;
  phone?: string;
  payment_terms?: number;
  status?: string;
}

interface PurchaseOrderOption extends DropdownOption {
  po_number?: string;
  total_amount?: number;
  items?: any[];
}

export const usePurchaseInvoiceForm = (
  isEditing: boolean = false,
  invoiceId?: string,
  initialData?: PurchaseInvoice
) => {
  const { toast } = useToast();
  const router = useRouter();

  // Dropdown options state
  const [suppliers, setSuppliers] = useState<DropdownOption[]>([]);
  const [entities, setEntities] = useState<DropdownOption[]>([]);
  const [currencies, setCurrencies] = useState<DropdownOption[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrderOption[]>([]);

  // Form state
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<string>("");

  // Prepare default values
  const getDefaultValues = (): PurchaseInvoiceFormData => {
    if (initialData) {
      return {
        supplier_id: initialData.supplier_id,
        entity_id: initialData.entity_id,
        purchase_order_id: initialData.purchase_order_id || undefined,
        invoice_number: initialData.invoice_number,
        supplier_reference: initialData.supplier_reference || undefined,
        invoice_date: initialData.invoice_date,
        due_date: initialData.due_date || undefined,
        currency_code: initialData.currency_code,
        reference_number: initialData.reference_number || undefined,
        notes: initialData.notes || undefined,
        perform_three_way_match: false,
        items: initialData.items?.map(item => ({
          po_item_id: item.po_item_id || undefined,
          line_number: item.line_number,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount_amount: item.discount_amount || undefined,
          discount_percentage: item.discount_percentage || undefined,
          tax_percentage: item.tax_percentage,
          item_code: item.item_code || undefined,
          gl_account_code: item.gl_account_code || undefined,
          cost_center: item.cost_center || undefined,
        })) || []
      };
    }

    return {
      supplier_id: "",
      entity_id: "",
      invoice_number: "",
      invoice_date: new Date().toISOString().split('T')[0],
      currency_code: "USD",
      perform_three_way_match: false,
      items: []
    };
  };

  // Initialize form
  const form = useForm<PurchaseInvoiceFormData>({
    resolver: zodResolver(purchaseInvoiceSchema),
    defaultValues: getDefaultValues(),
    mode: "onChange",
  });

  // Initialize field array for line items
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Load dropdown options
  useEffect(() => {
    const loadOptions = async () => {
      setLoading(true);
      try {
        const supabase = await createClient();

        // Load suppliers with fallback
        try {
          const { data: suppliersData, error: supplierError } = await supabase
            .schema("procurement")
            .from("suppliers")
            .select("id, name, display_name, supplier_code, email, phone, payment_terms, status")
            .eq("status", "ACTIVE")
            .order("name");

          if (supplierError) {
            console.warn("Failed to load suppliers from database:", supplierError);
            // Fallback mock data for testing
            setSuppliers([
              { value: "mock-supplier-1", label: "ACME Corp - Test Supplier", code: "ACME001", email: "<EMAIL>", phone: "******-0123", payment_terms: 30, status: "ACTIVE" },
              { value: "mock-supplier-2", label: "Global Industries - Test Supplier", code: "GLOB001", email: "<EMAIL>", phone: "******-0456", payment_terms: 45, status: "ACTIVE" },
              { value: "mock-supplier-3", label: "Premium Services - Test Supplier", code: "PREM001", email: "<EMAIL>", phone: "******-0789", payment_terms: 60, status: "ACTIVE" }
            ]);
          } else if (suppliersData && suppliersData.length > 0) {
            setSuppliers(suppliersData.map(supplier => ({
              value: supplier.id,
              label: supplier.display_name || supplier.name,
              code: supplier.supplier_code,
              email: supplier.email,
              phone: supplier.phone,
              payment_terms: supplier.payment_terms,
              status: supplier.status
            })));
          } else {
            // Empty data fallback
            setSuppliers([
              { value: "mock-supplier-1", label: "ACME Corp - Test Supplier", code: "ACME001", email: "<EMAIL>", phone: "******-0123", payment_terms: 30, status: "ACTIVE" }
            ]);
          }
        } catch (supplierError) {
          console.warn("Exception loading suppliers:", supplierError);
          setSuppliers([
            { value: "mock-supplier-1", label: "ACME Corp - Test Supplier", code: "ACME001", email: "<EMAIL>", phone: "******-0123", payment_terms: 30, status: "ACTIVE" }
          ]);
        }

        // Load entities with fallback
        try {
          const { data: entitiesData, error: entityError } = await supabase
            .schema("finance")
            .from("entities")
            .select("id, name, merchant_abbreviation")
            .order("name");

          if (entityError) {
            console.warn("Failed to load entities from database:", entityError);
            // Fallback mock data for testing
            setEntities([
              { value: "mock-entity-1", label: "IES Foundation - Main Entity", code: "IES-MAIN" },
              { value: "mock-entity-2", label: "IES Operations - Operations Entity", code: "IES-OPS" },
              { value: "mock-entity-3", label: "IES Research - Research Entity", code: "IES-RES" }
            ]);
          } else if (entitiesData && entitiesData.length > 0) {
            setEntities(entitiesData.map(entity => ({
              value: entity.id,
              label: entity.merchant_abbreviation ? `${entity.merchant_abbreviation} - ${entity.name}` : entity.name,
              code: entity.merchant_abbreviation
            })));
          } else {
            // Empty data fallback
            setEntities([
              { value: "mock-entity-1", label: "IES Foundation - Main Entity", code: "IES-MAIN" }
            ]);
          }
        } catch (entityError) {
          console.warn("Exception loading entities:", entityError);
          setEntities([
            { value: "mock-entity-1", label: "IES Foundation - Main Entity", code: "IES-MAIN" }
          ]);
        }

        // Load currencies (static data, always available)
        setCurrencies([
          { value: "USD", label: "US Dollar (USD)" },
          { value: "EUR", label: "Euro (EUR)" },
          { value: "GBP", label: "British Pound (GBP)" },
          { value: "AUD", label: "Australian Dollar (AUD)" },
          { value: "CAD", label: "Canadian Dollar (CAD)" },
        ]);

      } catch (error) {
        console.error("Error loading options:", error);
        
        // Complete fallback if everything fails
        setSuppliers([
          { value: "mock-supplier-1", label: "ACME Corp - Test Supplier", code: "ACME001", email: "<EMAIL>", phone: "******-0123", payment_terms: 30, status: "ACTIVE" }
        ]);
        setEntities([
          { value: "mock-entity-1", label: "IES Foundation - Main Entity", code: "IES-MAIN" }
        ]);
        setCurrencies([
          { value: "USD", label: "US Dollar (USD)" },
          { value: "EUR", label: "Euro (EUR)" },
          { value: "GBP", label: "British Pound (GBP)" }
        ]);
        
        toast({
          title: "Notice",
          description: "Using fallback data for dropdowns. Some database services may be temporarily unavailable.",
          variant: "default",
        });
      } finally {
        setLoading(false);
      }
    };

    loadOptions();
  }, [toast]);

  // Load purchase orders when supplier changes
  useEffect(() => {
    const loadPurchaseOrders = async () => {
      if (!selectedSupplier) {
        setPurchaseOrders([]);
        return;
      }

      try {
        const supabase = await createClient();
        const { data: poData, error: poError } = await supabase
          .schema("procurement")
          .from("purchase_orders")
          .select(`
            id,
            po_number,
            total_amount,
            status,
            po_date,
            purchase_order_items(
              id,
              line_number,
              description,
              quantity,
              unit_price,
              tax_rate,
              item_code,
              gl_account_code
            )
          `)
          .eq("supplier_id", selectedSupplier)
          .in("status", ["APPROVED", "PARTIALLY_RECEIVED", "RECEIVED", "SENT"])
          .order("po_date", { ascending: false });

        if (poError) {
          console.error("Error loading purchase orders:", poError);
        }

        if (poData && poData.length > 0) {
          setPurchaseOrders(poData.map(po => ({
            value: po.id,
            label: `${po.po_number} - ${new Intl.NumberFormat('en-US', { 
              style: 'currency', 
              currency: 'USD' 
            }).format(po.total_amount)}`,
            po_number: po.po_number,
            total_amount: po.total_amount,
            items: po.purchase_order_items
          })));
        } else {
          setPurchaseOrders([]);
          // Optionally show a message that no POs are available
          console.log("No purchase orders found for selected supplier");
        }
      } catch (error) {
        console.error("Error loading purchase orders:", error);
        toast({
          title: "Error",
          description: "Failed to load purchase orders.",
          variant: "destructive",
        });
      }
    };

    loadPurchaseOrders();
  }, [selectedSupplier, toast]);

  // Watch supplier changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "supplier_id") {
        setSelectedSupplier(value.supplier_id || "");
        // Clear PO selection when supplier changes
        form.setValue("purchase_order_id", "");
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Actions
  const addLineItem = () => {
    append({
      line_number: fields.length + 1,
      description: "",
      quantity: 1,
      unit_price: 0,
      tax_percentage: 0,
    });
  };

  const removeLineItem = (index: number) => {
    remove(index);
    // Update line numbers after removal
    setTimeout(() => {
      fields.forEach((_, idx) => {
        form.setValue(`items.${idx}.line_number`, idx + 1);
      });
    }, 0);
  };

  const populateFromPO = () => {
    const poId = form.watch("purchase_order_id");
    if (!poId) return;

    const selectedPO = purchaseOrders.find(po => po.value === poId);
    if (!selectedPO || !selectedPO.items) return;

    // Clear existing items
    while (fields.length > 0) {
      remove(0);
    }

    // Add items from PO
    selectedPO.items.forEach((item, index) => {
      append({
        po_item_id: item.id,
        line_number: index + 1,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        tax_percentage: item.tax_rate || 0,
        item_code: item.item_code,
        gl_account_code: item.gl_account_code,
      });
    });

    toast({
      title: "Success",
      description: "Items populated from purchase order.",
    });
  };

  // Calculations
  const calculateLineTotal = (index: number): number => {
    const item = form.watch(`items.${index}`);
    if (!item) return 0;

    const { quantity = 0, unit_price = 0, discount_amount = 0, discount_percentage = 0, tax_percentage = 0 } = item;
    
    let subtotal = quantity * unit_price;
    
    // Apply discount
    if (discount_percentage > 0) {
      subtotal = subtotal * (1 - discount_percentage / 100);
    }
    if (discount_amount > 0) {
      subtotal = Math.max(0, subtotal - discount_amount);
    }
    
    // Add tax
    const taxAmount = subtotal * (tax_percentage / 100);
    
    return subtotal + taxAmount;
  };

  const calculateTotals = () => {
    const items = form.watch("items") || [];
    
    let subtotal = 0;
    let totalTax = 0;

    items.forEach((item) => {
      const quantity = item.quantity || 0;
      const unitPrice = item.unit_price || 0;
      const discountAmount = item.discount_amount || 0;
      const discountPercentage = item.discount_percentage || 0;
      const taxPercentage = item.tax_percentage || 0;

      let itemSubtotal = quantity * unitPrice;
      
      // Apply discount
      if (discountPercentage > 0) {
        itemSubtotal = itemSubtotal * (1 - discountPercentage / 100);
      }
      if (discountAmount > 0) {
        itemSubtotal = Math.max(0, itemSubtotal - discountAmount);
      }
      
      subtotal += itemSubtotal;
      totalTax += itemSubtotal * (taxPercentage / 100);
    });

    return {
      subtotal,
      totalTax,
      totalAmount: subtotal + totalTax,
    };
  };

  // Form submission handlers
  const onSubmit = async (data: PurchaseInvoiceFormData) => {
    setSubmitting(true);
    try {
      // Validate required fields
      if (!data.supplier_id) {
        throw new Error("Please select a supplier");
      }
      if (!data.entity_id) {
        throw new Error("Please select an entity");
      }
      if (!data.invoice_number?.trim()) {
        throw new Error("Invoice number is required");
      }
      if (!data.items || data.items.length === 0) {
        throw new Error("At least one line item is required");
      }

      // Validate line items
      for (let i = 0; i < data.items.length; i++) {
        const item = data.items[i];
        if (!item.description?.trim()) {
          throw new Error(`Line item ${i + 1}: Description is required`);
        }
        if (!item.quantity || item.quantity <= 0) {
          throw new Error(`Line item ${i + 1}: Quantity must be greater than 0`);
        }
        if (!item.unit_price || item.unit_price < 0) {
          throw new Error(`Line item ${i + 1}: Unit price must be 0 or greater`);
        }
      }

      const input: CreatePurchaseInvoiceInput = {
        supplier_id: data.supplier_id,
        entity_id: data.entity_id,
        purchase_order_id: data.purchase_order_id,
        invoice_number: data.invoice_number,
        supplier_reference: data.supplier_reference,
        invoice_date: data.invoice_date,
        due_date: data.due_date,
        currency_code: data.currency_code,
        reference_number: data.reference_number,
        notes: data.notes,
        perform_three_way_match: data.perform_three_way_match,
        items: data.items.map(item => ({
          po_item_id: item.po_item_id,
          line_number: item.line_number,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount_amount: item.discount_amount,
          discount_percentage: item.discount_percentage,
          tax_percentage: item.tax_percentage,
          item_code: item.item_code,
          gl_account_code: item.gl_account_code,
          cost_center: item.cost_center,
        }))
      };

      if (isEditing && invoiceId) {
        await updatePurchaseInvoice(invoiceId, input);
        toast({
          title: "Success",
          description: "Purchase invoice updated successfully.",
        });
        router.push(`/purchase-invoices/${invoiceId}`);
      } else {
        await createPurchaseInvoice(input);
        toast({
          title: "Success",
          description: "Purchase invoice created successfully.",
        });
        router.push("/purchase-invoices");
      }
    } catch (error) {
      console.error("Error creating purchase invoice:", error);
      
      let errorMessage = isEditing 
        ? "Failed to update purchase invoice. Please try again."
        : "Failed to create purchase invoice. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const onSaveDraft = async (data: PurchaseInvoiceFormData) => {
    setSubmitting(true);
    try {
      // For now, we'll treat draft the same as regular creation
      await onSubmit(data);
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return {
    form,
    fields,
    loading,
    submitting,
    options: {
      suppliers,
      entities,
      currencies,
      purchaseOrders,
    },
    actions: {
      addLineItem,
      removeLineItem,
      populateFromPO,
      onSubmit,
      onSaveDraft,
    },
    calculations: {
      calculateLineTotal,
      calculateTotals,
    },
  };
};