# Membership Types and Applications Analysis

## Overview
This document analyzes how memberships and applications relate to membership types in the Supabase schema.

## 1. Application Table Structure

The `applications` table connects to membership types through:
- `membership_type` (UUID) - Foreign key to `membership_types.id`
- Contains application-specific information like status, reference number, type (NEW/RENEWAL)
- Key fields:
  ```sql
  - id (UUID)
  - status (application_statuses enum)
  - user_id (UUID)
  - profile_id (UUID) 
  - membership_type (UUID) -- FK to membership_types
  - membership_id (UUID) -- FK to memberships
  - reference_no (varchar)
  - type (application_types: NEW/RENEWAL)
  - submitted_at (timestamp)
  - approved_or_rejected_at (timestamp)
  ```

## 2. Membership Type Table Structure

The `membership_types` table contains comprehensive membership information:
```sql
- id (UUID)
- name (varchar) -- e.g., "Associate Member", "Fellow Member"
- abbr (varchar) -- e.g., "AMIES", "FIES"
- group (membership_type_group enum: MBR/PR/JR)
- admin_fee (real)
- application_fee (real) 
- subscription_fee (real)
- is_one_fee (boolean)
- required_renewal_points (boolean)
- required_physical_submission (boolean)
- required_membership (boolean)
- required_tucss_membership (boolean)
- required_additional_forms (boolean)
- required_signature (boolean)
- description (text)
- is_active (boolean)
- allow_online_registration (boolean)
- subscription_period_from/until (varchar)
- renewal_period_start_from (varchar)
- application_number_prefix/suffix/sequence
- membership_number_prefix/suffix/sequence
- entity_id (UUID) -- FK to finance.entities
- certificate_template (varchar)
```

## 3. Additional Membership Type Tables

### membership_type_rules
Stores validation rules for each membership type:
```sql
- id (UUID)
- membership_type_id (UUID) -- FK to membership_types
- rule_type (membership_rule_types enum)
- rule_value (jsonb)
- error_message (text)
```

### membership_type_additional_forms
Stores additional forms required for specific membership types:
```sql
- id (UUID)
- membership_type_id (UUID) -- FK to membership_types
- form_name (varchar)
- form_path (varchar)
- is_required (boolean)
- description (text)
```

### membership_type_notice
Stores notices/alerts for membership types:
```sql
- id (UUID)
- membership_type_id (UUID) -- FK to membership_types
- type (membership_notice_types: PRE_APPLY/DOCUMENTS)
- title (varchar)
- description (text)
```

### membership_type_prerequisites
Stores prerequisites for membership types:
```sql
- id (UUID)
- membership_type_id (UUID) -- FK to membership_types
- title (varchar)
- description (text)
```

## 4. Existing Views

### application_profiles View
Joins applications with profiles and membership types:
```sql
SELECT
    a.*, -- All application fields
    p.*, -- All profile fields
    mt.name as membership_type_name,
    mt.abbr as membership_type_abbr,
    mt.group as membership_type_group
FROM applications a
JOIN profiles p ON a.profile_id = p.id
LEFT JOIN membership_types mt ON a.membership_type = mt.id
```

### application_export_view
Comprehensive view for exporting application data with all related information including membership type details.

### invoice_details View (finance schema)
Shows membership type information for invoices:
```sql
- Includes membership_type and membership_type_name
- Joins through invoice_sources to get application/membership type data
```

## 5. Key Relationships

1. **Application → Membership Type**: Direct foreign key relationship
2. **Membership → Membership Type**: Direct foreign key relationship
3. **Membership Type → Additional Tables**: One-to-many relationships for:
   - Rules (validation/eligibility)
   - Additional forms
   - Notices
   - Prerequisites

## 6. Complete Membership Type Information Available

When querying membership types for applications, the following complete information is available:

1. **Basic Information**: name, abbreviation, group (MBR/PR/JR)
2. **Fees**: admin_fee, application_fee, subscription_fee
3. **Requirements**: Various boolean flags for requirements
4. **Configuration**: Number sequences, prefixes, periods
5. **Rules**: Validation rules with types and values
6. **Forms**: Additional required forms with paths
7. **Notices**: Pre-apply and document notices
8. **Prerequisites**: List of prerequisites with descriptions
9. **Entity**: Associated financial entity for accounting

## 7. Recommendations for Complete Views

To get complete membership type information for applications, you would need to:

1. Join with all related tables:
```sql
SELECT 
    a.*,
    mt.*,
    -- Aggregate rules
    (SELECT jsonb_agg(jsonb_build_object(
        'rule_type', rule_type,
        'rule_value', rule_value,
        'error_message', error_message
    )) FROM membership_type_rules WHERE membership_type_id = mt.id) as rules,
    -- Aggregate additional forms
    (SELECT jsonb_agg(jsonb_build_object(
        'form_name', form_name,
        'form_path', form_path,
        'is_required', is_required
    )) FROM membership_type_additional_forms WHERE membership_type_id = mt.id) as additional_forms,
    -- Aggregate notices
    (SELECT jsonb_agg(jsonb_build_object(
        'type', type,
        'title', title,
        'description', description
    )) FROM membership_type_notice WHERE membership_type_id = mt.id) as notices,
    -- Aggregate prerequisites
    (SELECT jsonb_agg(jsonb_build_object(
        'title', title,
        'description', description
    )) FROM membership_type_prerequisites WHERE membership_type_id = mt.id) as prerequisites
FROM applications a
LEFT JOIN membership_types mt ON a.membership_type = mt.id
```

This would provide a complete picture of all membership type information associated with an application.